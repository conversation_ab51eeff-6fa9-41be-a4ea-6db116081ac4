<?php

namespace common\components;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\models\Seller;
use common\models\UserToken;
use yii\caching\CacheInterface;
use yii\caching\TagDependency;

class CustomerComponent
{
    public const INTEGER_MONEY_ACCURACY_OLD = 100;
    public const INTEGER_MONEY_ACCURACY_NEW = 10000;

    public const DEFAULT_LANGUAGE_CODE = 'en';
    public const DEFAULT_TIMEZONE = 'UTC';
    public const UTC_TIMEZONE = 'UTC';

    private DbManager $dbManager;
    private CustomerConfig $customerConfig;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerConfig = \Yii::$container->get('customerConfig');
    }

    public function isTransactionsFrozen(): bool
    {
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->fastPersistentCache;

        return (bool)$cache->get("is_transactions_frozen_" . $this->dbManager->getCustomerId());
    }

    public function getMoneyAccuracy(): int
    {
        $moneyAccuracy =  $this->customerConfig->get(
            CustomerConfig::PARAMETER_INTEGER_MONEY_ACCURACY,
            self::INTEGER_MONEY_ACCURACY_OLD
        );
        return (int)$moneyAccuracy;
    }

    public function getPreferredLanguageCode(): string
    {
        return $this->getFirstStaffData()['preferred_language_code'];
    }

    public function getDefaultVATValue(string $marketplaceId, string $sellerId): ?float
    {
        $customerVatSettings = $this->dbManager->getRepricerMainDb()
            ->createCommand("
                SELECT 
                    vs.customer_id,
                    aca.sellerId as seller_id,
                    vs.marketplace_id,
                    vs.vat_value
                FROM vat_setting vs
                LEFT JOIN amazon_customer_account aca ON aca.id = vs.amazon_customer_account_id
                WHERE vs.customer_id IS NULL
                OR vs.customer_id = :customer_id
            ", [
                'customer_id' => $this->dbManager->getCustomerId()
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->dbManager->getCustomerId(), $sellerId)])
            )
            ->queryAll();

        $globalLevelValue = null;
        $customerLevelValue = null;
        $sellerLevelValue = null;

        foreach ($customerVatSettings as $vatSetting) {
            if ($vatSetting['marketplace_id'] !== $marketplaceId) {
                continue;
            }

            if (empty($vatSetting['customer_id'])) {
                $globalLevelValue = $vatSetting['vat_value'];
            } else if (empty($vatSetting['seller_id'])) {
                $customerLevelValue = $vatSetting['vat_value'];
            } else if ($vatSetting['seller_id'] === $sellerId) {
                $sellerLevelValue = $vatSetting['vat_value'];
            }
        }

        return $sellerLevelValue ?? $customerLevelValue ?? $globalLevelValue;
    }

    public function getUserTimezoneName(): string
    {
        /** @var UserToken $userToken */
        $userToken = \Yii::$app->user->identity ?? null;

        if (empty($userToken)) {
            return self::UTC_TIMEZONE;
        }

        return $userToken->getTimezone();
    }

    public function isActive(): bool
    {
        return Seller::find()
            ->where(['=', 'customer_id', $this->dbManager->getCustomerId()])
            ->andWhere(['=', 'is_active', 't'])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => Seller::getCacheTag($this->dbManager->getCustomerId())
                ])
            )
            ->count() > 0;
    }

    public function getSubscriptionInfo(): array
    {
        $sellersInfo = Seller::find()
            ->select(['id', 'is_active', 'was_active_until_date'])
            ->where([
                'customer_id' => \Yii::$app->dbManager->getCustomerId(),
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => Seller::getCacheTag($this->dbManager->getCustomerId())
                ])
            )
            ->asArray()
            ->all();
        $isSubscriptionActive = false;
        $subscriptionEndDate = null;

        foreach ($sellersInfo as $sellerInfo) {
            if ($sellerInfo['is_active']) {
                $isSubscriptionActive = true;
                break;
            }

            if (!empty($sellerInfo['was_active_until_date'])) {
                $subscriptionEndDate = max($subscriptionEndDate, strtotime($sellerInfo['was_active_until_date']));
            }
        }

        $existingSellerIds = [];

        foreach ($sellersInfo as $k => $sellerInfo) {
            if ($sellerInfo['is_active']) {
                $existingSellerIds[] = $sellerInfo['id'];
                continue;
            }

            // Determine which sellers was active in previous subscription by comparing "was_active_until_date".
            // All active sellers from previous subscription become inactive almost the same time.
            if (
                $isSubscriptionActive
                || empty($sellerInfo['was_active_until_date'])
                || abs(strtotime($sellerInfo['was_active_until_date']) - $subscriptionEndDate) > 60
            ) {
                continue;
            }
            $existingSellerIds[] = $sellerInfo['id'];
        }

        if (!empty($subscriptionEndDate)) {
            $subscriptionEndDate = (new \DateTime())
                ->setTimestamp($subscriptionEndDate)
                ->setTime(23, 59, 59)
                ->modify('-1 day')
                ->format('Y-m-d H:i:s')
            ;
        }

        return [
            'isActive' => $isSubscriptionActive,
            'sellerIds' => $existingSellerIds,
            'endDate' => $subscriptionEndDate
        ];
    }

    public function getWasActiveUntilDate(bool $isAllowNull = false): ?string
    {
        return Seller::find()
            ->select('max(was_active_until_date)')
            ->where(['=', 'customer_id', $this->dbManager->getCustomerId()])
            ->scalar() ?: ($isAllowNull ? null : '1900-01-01 00:00:00')
        ;
    }

    public function getTimezoneName(): string
    {
        return $this->getFirstStaffData()['timezone_name'];
    }

    public function getFirstStaffData(): array
    {
        $db = $this->dbManager->getRepricerMainDb();
        $params = [
            'customer_id' => $this->dbManager->getCustomerId()
        ];
        $res = $db
            ->createCommand("
                SELECT 
                    t.name as timezone_name, 
                    l.code as language_code,
                    l1.code as preferred_language_code
                FROM staff s
                LEFT JOIN customer c ON c.id = s.customer_id
                LEFT JOIN timezone t ON t.id = s.timezone_id
                LEFT JOIN language l ON l.id = s.language_id
                LEFT JOIN language l1 ON l1.id = c.preferred_language_id
                WHERE s.customer_id = :customer_id
                ORDER BY s.id
            ", $params)
            ->queryOne() ?? [];

        $res['timezone_name'] = $res['timezone_name'] ?? self::DEFAULT_TIMEZONE;
        $res['language_code'] = $res['language_code'] ?? self::DEFAULT_LANGUAGE_CODE;
        $res['preferred_language_code'] = $res['preferred_language_code'] ?? self::DEFAULT_LANGUAGE_CODE;

        return $res;
    }
}
