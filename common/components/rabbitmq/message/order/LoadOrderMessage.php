<?php

namespace common\components\rabbitmq\message\order;

use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;

class LoadOrderMessage extends MessageAbstract
{
    const AMOUNT_OF_QUEUES = 5;

    protected $delay = 2;

    protected string $amazonOrderId = '';

    protected int $errorsCount = 0;

    public function __sleep()
    {
        return [
            'sellerId',
            'region',
            'lastExecuteTime',
            'nextToken',
            'query',
            'amazonOrderId',
            'errorsCount'
        ];
    }

    /**
     * @return string
     */
    public function getAmazonOrderId(): string
    {
        return $this->amazonOrderId;
    }

    /**
     * @param string $amazonOrderId
     */
    public function setAmazonOrderId(string $amazonOrderId): void
    {
        $this->amazonOrderId = $amazonOrderId;
    }

    protected function getAmountOfQueues(): int
    {
        return self::AMOUNT_OF_QUEUES;
    }

    /**
     * @return int
     */
    public function getErrorsCount(): int
    {
        return $this->errorsCount;
    }

    /**
     * @param int $errorsCount
     */
    public function setErrorsCount(int $errorsCount): void
    {
        $this->errorsCount = $errorsCount;
    }

    public function increaseErrorsCount()
    {
        $this->errorsCount = $this->errorsCount + 1;
    }

    public function resetErrorsCount()
    {
        $this->errorsCount = 0;
    }

    public function getExchangeName(): string
    {
        return MessageInterface::EXCHANGE_NAME_LOAD_ORDER;
    }

    protected function buildNextQuery(): void
    {
    }
}
