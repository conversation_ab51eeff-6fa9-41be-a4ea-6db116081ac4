<?php

namespace common\components\rabbitmq\message\order;

use common\components\rabbitmq\message\MessageAbstract;

abstract class BaseLoadOrdersMessage extends MessageAbstract
{
    const AMOUNT_OF_QUEUES = 2;

    protected $delay = 5;

    protected string $orderPeriodId = '';

    protected bool $isFinished = false;

    protected int $errorsCount = 0;

    public function __sleep()
    {
        return [
            'sellerId',
            'region',
            'lastExecuteTime',
            'nextToken',
            'from',
            'fromQueryValue',
            'to',
            'toQueryValue',
            'query',
            'orderPeriodId',
            'isFinished',
            'errorsCount'
        ];
    }

    protected function fromDateTimeParameterName(): string
    {
        return 'LastUpdatedAfter';
    }

    protected function toDateTimeParameterName(): string
    {
        return 'LastUpdatedBefore';
    }

    public function setOrderPeriodId(string $orderPeriodId): void
    {
        $this->orderPeriodId = $orderPeriodId;
    }

    protected function getAmountOfQueues(): int
    {
        return self::AMOUNT_OF_QUEUES;
    }

    protected function buildNextQuery(): void
    {
        if ($this->nextToken) {
            $this->query['NextToken'] = $this->nextToken;

            return;
        }

        unset($this->query['NextToken']);
    }

    public function getOrderPeriodId(): string
    {
        return $this->orderPeriodId;
    }

    public function isFinished(): bool
    {
        return $this->isFinished;
    }

    /**
     * @param bool $isFinished
     */
    public function setIsFinished(bool $isFinished): void
    {
        $this->isFinished = $isFinished;
    }

    /**
     * @return int
     */
    public function getErrorsCount(): int
    {
        return $this->errorsCount;
    }

    /**
     * @param int $errorsCount
     */
    public function setErrorsCount(int $errorsCount): void
    {
        $this->errorsCount = $errorsCount;
    }

    public function increaseErrorsCount()
    {
        $this->errorsCount = $this->errorsCount + 1;
    }

    public function resetErrorsCount()
    {
        $this->errorsCount = 0;
    }

    abstract function getExchangeName(): string;
}
