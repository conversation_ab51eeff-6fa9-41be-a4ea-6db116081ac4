<?php

namespace common\components\rabbitmq\message\order;

use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;

class LoadOrderItemsMessage extends MessageAbstract
{
    const AMOUNT_OF_QUEUES = 20;

    protected $delay = 2;

    protected bool $isFinished = false;

    protected string $amazonOrderId = '';

    protected int $errorsCount = 0;

    public function __sleep()
    {
        return [
            'sellerId',
            'region',
            'lastExecuteTime',
            'nextToken',
            'query',
            'amazonOrderId',
            'isFinished',
            'errorsCount'
        ];
    }

    protected function getAmountOfQueues(): int
    {
        return self::AMOUNT_OF_QUEUES;
    }

    protected function buildNextQuery(): void
    {
        if ($this->nextToken) {
            $this->query['NextToken'] = $this->nextToken;

            return;
        }

        unset($this->query['NextToken']);
    }

    public function isFinished(): bool
    {
        return $this->isFinished;
    }

    /**
     * @param bool $isFinished
     */
    public function setIsFinished(bool $isFinished): void
    {
        $this->isFinished = $isFinished;
    }

    /**
     * @return int
     */
    public function getErrorsCount(): int
    {
        return $this->errorsCount;
    }

    /**
     * @param int $errorsCount
     */
    public function setErrorsCount(int $errorsCount): void
    {
        $this->errorsCount = $errorsCount;
    }

    public function increaseErrorsCount()
    {
        $this->errorsCount = $this->errorsCount + 1;
    }

    public function resetErrorsCount()
    {
        $this->errorsCount = 0;
    }

    /**
     * @return string
     */
    public function getAmazonOrderId(): string
    {
        return $this->amazonOrderId;
    }

    /**
     * @param string $amazonOrderId
     */
    public function setAmazonOrderId(string $amazonOrderId): void
    {
        $this->amazonOrderId = $amazonOrderId;
    }

    public function getExchangeName(): string
    {
        return MessageInterface::EXCHANGE_NAME_LOAD_ORDER_ITEMS;
    }
}
