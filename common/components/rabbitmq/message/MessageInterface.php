<?php


namespace common\components\rabbitmq\message;

interface MessageInterface
{
    const EXCHANGE_NAME_FINANCE = 'EX-FINANCE';

    const EXCHANGE_NAME_LOAD_ORDERS_INIT = 'EX-LOAD-ORDERS-INIT';
    const EXCHANGE_NAME_LOAD_ORDERS_REFRESH = 'EX-LOAD-ORDERS-REFRESH';
    const EXCHANGE_NAME_LOAD_ORDER = 'EX-LOAD-ORDER';
    const EXCHANGE_NAME_LOAD_ORDER_ITEMS = 'EX-LOAD-ORDER-ITEMS';
    const EXCHANGE_NAME_CLICKHOUSE = 'EX-CLICKHOUSE';

    /**
     * Dead letter exchange for rejected messages
     */
    const EXCHANGE_NAME_CLICKHOUSE_DLX = 'EX-CLICKHOUSE-DLX';

    const EXCHANGE_NAME_COG_CHANGES = 'EX-COG-CHANGES';
    const EXCHANGE_NAME_INDIRECT_COST_CHANGES = 'EX-INDIRECT_COST_CHANGES';
    const EXCHANGE_NAME_COG_SYNC = 'EX-COG-SYNC';
    const EXCHANGE_NAME_PRODUCT_SYNC = 'EX-PRODUCT-SYNC';
    const EXCHANGE_NAME_COG_CHECK_REFUNDS= 'EX-COG-CHECK-REFUNDS';
    const EXCHANGE_NAME_CRON = 'EX-CRON';
    const EXCHANGE_NAME_FINANCIAL_EVENT_INIT = 'EX-FINANCIAL-EVENT-INIT';
    const EXCHANGE_NAME_FINANCIAL_EVENT_REFRESH = 'EX-FINANCIAL-EVENT-REFRESH';
    const EXCHANGE_NAME_DATA_IMPORT = 'EX-DATA-IMPORT';
    const EXCHANGE_NAME_DATA_EXPORT = 'EX-DATA-EXPORT';
    const EXCHANGE_NAME_CUSTOMER_PROCESS = 'EX-CUSTOMER-PROCESS';
    const EXCHANGE_NAME_AMAZON_REPORT = 'EX-AMAZON-REPORT';
    const EXCHANGE_NAME_PPC_COSTS = 'EX-PPC-COSTS';

    public function getMessage(): string;

    public function getSellerId(): string;

    public function getQuery(): array;

    public function getRegion(): string;

    public function getRoutingKey(): string;

    public function publish(): void;

    public function wait(): bool;

    public function isFinished(): bool;
}
