<?php


namespace common\components\rabbitmq\message;

abstract class BaseFinancialEventMessage extends MessageAbstract
{
    const AMOUNT_OF_QUEUES = 2;

    protected $delay = 2;

    protected string $eventPeriodId = '';

    protected bool $isFinished = false;

    protected int $errorsCount = 0;

    protected int $maxResultsPerPage = 100;

    public function __sleep()
    {
        return [
            'sellerId',
            'region',
            'lastExecuteTime',
            'nextToken',
            'from',
            'fromQueryValue',
            'to',
            'toQueryValue',
            'query',
            'eventPeriodId',
            'isFinished',
            'errorsCount',
            'maxResultsPerPage'
        ];
    }

    public function setEventPeriodId(string $eventPeriodId): void
    {
        $this->eventPeriodId = $eventPeriodId;
    }

    protected function getAmountOfQueues(): int
    {
        return self::AMOUNT_OF_QUEUES;
    }

    protected function buildNextQuery(): void
    {
        if ($this->nextToken) {
            $this->query['NextToken'] = $this->nextToken;

            return;
        }

        unset($this->query['NextToken']);
    }

    public function getEventPeriodId(): string
    {
        return $this->eventPeriodId;
    }

    public function isFinished(): bool
    {
        return $this->isFinished;
    }

    /**
     * @param bool $isFinished
     */
    public function setIsFinished(bool $isFinished): void
    {
        $this->isFinished = $isFinished;
    }

    /**
     * @return int
     */
    public function getErrorsCount(): int
    {
        return $this->errorsCount;
    }

    /**
     * @param int $errorsCount
     */
    public function setErrorsCount(int $errorsCount): void
    {
        $this->errorsCount = $errorsCount;
    }

    public function increaseErrorsCount()
    {
        $this->errorsCount = $this->errorsCount + 1;
    }

    public function setMaxResultsPerPage(int $maxResultsPerPage)
    {
        $this->maxResultsPerPage = $maxResultsPerPage;
    }

    public function getMaxResultsPerPage(): int
    {
        return $this->maxResultsPerPage;
    }

    public function resetErrorsCount()
    {
        $this->errorsCount = 0;
    }

    abstract function getExchangeName(): string;
}
