<?php


namespace common\components\rabbitmq\message;

use mikemadisonweb\rabbitmq\Configuration;

abstract class MessageAbstract implements MessageInterface
{
    /**
     * @var string
     */
    const PRODUCER_NAME = 'PRO-DEFAULT';

    /**
     * @var string
     */
    protected $sellerId;

    /**
     * @var string
     */
    protected $region;

    /**
     * @var int
     */
    protected $queueAmount = 2;

    /**
     * @var \DateTime
     */
    protected $lastExecuteTime = null;

    /**
     * @var string
     */
    protected $nextToken = null;

    /**
     * @var int
     */
    protected $delay = 1;

    /**
     * @var \DateTime
     */
    protected $from;

    /**
     * @var \DateTime
     */
    protected $fromQueryValue;

    /**
     * @var \DateTime
     */
    protected $to;

    /**
     * @var \DateTime
     */
    protected $toQueryValue;

    /**
     * @var array
     */
    protected $query = [];

    /**
     * @var Configuration
     */
    protected $rabbitMq;


    /**
     * MessageAbstract constructor.
     * @param string $sellerId
     * @param string $region
     * @param array $query
     * @param \DateTime|null $from
     * @param \DateTime|null $to
     */
    public function __construct(
        string $sellerId,
        string $region,
        array $query,
        \DateTime $from = null,
        \DateTime $to = null
    ) {
        $this->sellerId = $sellerId;
        $this->region = $region;
        $this->query = $query;
        $this->from = $from;
        $this->to = $to ?: $this->maxToValue();

        unset($this->query[$this->fromDateTimeParameterName()], $this->query[$this->toDateTimeParameterName()]);

        $this->rabbitMq = \Yii::$app->rabbitmq;
    }

    public function __wakeup()
    {
        $this->buildNextQuery();

        $this->rabbitMq = \Yii::$app->rabbitmq;
    }

    public function __sleep()
    {
        return [
            'sellerId',
            'region',
            'lastExecuteTime',
            'nextToken',
            'from',
            'fromQueryValue',
            'to',
            'toQueryValue',
            'query',
        ];
    }

    protected function fromDateTimeParameterName(): string
    {
        return 'PostedAfter';
    }

    protected function toDateTimeParameterName(): string
    {
        return 'PostedBefore';
    }

    protected function getMaxInterval(): \DateInterval
    {
        return new \DateInterval('P180D');
    }

    protected function maxToValue(): \DateTime
    {
        $interval = new \DateInterval('PT2M');
        $interval->invert = 1;
        return (new \DateTime())->add($interval);
    }

    /**
     * @return array
     */
    public function getQuery(): array
    {
        return $this->query;
    }


    /**
     * @return string
     */
    public function getSellerId(): string
    {
        return $this->sellerId;
    }

    /**
     * @param string|null $nextToken
     */
    public function setNextToken(?string $nextToken): void
    {
        $this->nextToken = $nextToken;
    }

    /**
     * @param $lastExecuteTime
     */
    public function setLastExecuteTime($lastExecuteTime): void
    {
        $this->lastExecuteTime = $lastExecuteTime;
    }

    /**
     * @return \DateTime|null
     */
    public function getLastExecuteTime(): ?\DateTime
    {
        return $this->lastExecuteTime;
    }

    /**
     * Returns exchange name.
     * @return string
     * <AUTHOR>
     */
    abstract protected function getExchangeName(): string;

    /**
     * Returns amount of queue for the current type of messages.
     * @return \DateTime
     * <AUTHOR>
     */
    abstract protected function getAmountOfQueues(): int;

    /**
     * Builds query array for the next query to api.
     * <AUTHOR>
     */
    protected function buildNextQuery(): void
    {
        //if nextToken provided - continue to load data for the same query
        if ($this->nextToken) {
            $this->query['NextToken'] = $this->nextToken;

            return;
        }

        //reset next token for new request
        unset($this->query['NextToken']);

        //it means query was already executed but next token is not provided - all data are loaded for requested period
        if ($this->fromQueryValue) {

            //if requested "from" is equal to "from" value in last request - stop querying, all data is loaded
            if ($this->fromQueryValue->format('Y-m-d') === $this->from->format('Y-m-d')) {
                $this->query = null;
                return;
            }

            //if not min value (from) or min value from the last quest is bigger than requested
            if (!$this->from || $this->fromQueryValue->getTimestamp() - $this->from->getTimestamp() > 0) {
                $this->toQueryValue = clone $this->fromQueryValue;

                $maxInterval = clone $this->getMaxInterval();
                $maxInterval->invert = 1;
                $this->fromQueryValue->add($maxInterval);

                //if set from value is less than requested - set requested "from" value
                if ($this->from->getTimestamp() - $this->fromQueryValue->getTimestamp() > 0) {
                    $this->fromQueryValue = clone $this->from;
                }
            }
        } else {
            //init default
            $this->toQueryValue = clone $this->to;
            $this->fromQueryValue = clone $this->to;

//            self::writeData('stage 1 toQueryValue'.$this->toQueryValue->format('Y-m-d'));
//            self::writeData('stage 1 fromQueryValue'.$this->fromQueryValue->format('Y-m-d'));

            $maxInterval = clone $this->getMaxInterval();
            $maxInterval->invert = 1;
            $this->fromQueryValue->add($maxInterval);

//            self::writeData('stage 2 toQueryValue'.$this->toQueryValue->format('Y-m-d'));
//            self::writeData('stage 2 fromQueryValue'.$this->fromQueryValue->format('Y-m-d'));

            //if set from value is less than requested - set requested "from" value
            if ($this->from->getTimestamp() - $this->fromQueryValue->getTimestamp() > 0) {
                $this->fromQueryValue = clone $this->from;
            }

//            self::writeData('stage 3 toQueryValue'.$this->toQueryValue->format('Y-m-d'));
//            self::writeData('stage 3 fromQueryValue'.$this->fromQueryValue->format('Y-m-d'));
        }

        $this->query[$this->fromDateTimeParameterName()] = $this->fromQueryValue->format('c');
        $this->query[$this->toDateTimeParameterName()] = $this->toQueryValue->format('c');

//        self::writeData('stage 4 query'.print_r($this->query,true));
    }

    public static function writeData($data, $putData = false, $key = null)
    {
        if ($putData) {
            $file = fopen(\Yii::getAlias('@app/runtime/logs/'.$key.'.json'), 'a');
            fwrite($file, $data);
            fclose($file);
        } else {
            $file = fopen(\Yii::getAlias('@app/runtime/logs/finance-data.log'), 'a');
            fwrite($file, date('H:i:s').'  '.$data."\n");
            fclose($file);
        }
    }

    public static function writeErrorLog($data, $postfix = '')
    {
        $file = fopen(\Yii::getAlias('@app/runtime/logs/error' . $postfix . '.log'), 'a');
        fwrite($file, date('H:i:s') . '  ' . $data . "\n");
        fclose($file);
    }

    /**
     * @return string
     * <AUTHOR>
     */
    public function getMessage(): string
    {
        return serialize($this);
    }

    /**
     * <AUTHOR>
     * @param $message
     * @return MessageAbstract|null
     */
    public static function getFromMessage($message)
    {
        /** @var self $instance */
        $instance = unserialize($message, ['allowed_classes' => [static::class, \DateTime::class]]);

        return $instance;
    }

    /**
     * <AUTHOR>
     * @return bool
     */
    public function isFinished(): bool
    {
        return $this->query === null;
    }

    /**
     * @return string
     */
    public function getRegion(): string
    {
        return $this->region;
    }

    /**
     * Returns routing key for choosing queue.
     * @return string
     * <AUTHOR>
     */
    public function getRoutingKey(): string
    {
        $key = preg_replace('/[^\d]/', '', $this->sellerId);
        if (strlen($key) === 0) {
            return '0';
        }

        return (string)((int)$key % $this->getAmountOfQueues());
    }

    /**
     * @param int $priority
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     * <AUTHOR>
     */
    public function publish(int $priority = 1): void
    {
        $producer = $this->rabbitMq->getProducer(self::PRODUCER_NAME);
//        \Yii::info('message '.$this->getMessage(), 'load-data');
//        \Yii::info('exchangeName '.$this->getExchangeName(), 'load-data');
//        \Yii::info('routingKey '.$this->getRoutingKey(), 'load-data');

        $additionalProperties = [
            'priority' => $priority
        ];

        $producer->publish($this->getMessage(), $this->getExchangeName(), $this->getRoutingKey(), $additionalProperties);
    }

    /**
     * <AUTHOR>
     * @return bool
     */
    public function wait(): bool
    {
        if (!$this->lastExecuteTime) {
            return true;
        }

        $interval = $this->lastExecuteTime->diff(new \DateTime())->s;
        $delay = $this->errorsCount ? $this->delay * $this->errorsCount : $this->delay;
        if ($interval < $delay) {
            sleep($delay - $interval);
        }

        return true;
    }

    /**
     * @return \DateTime
     */
    public function getFromQueryValue(): \DateTime
    {
        return $this->fromQueryValue;
    }

    /**
     * @return \DateTime
     */
    public function getToQueryValue(): \DateTime
    {
        return $this->toQueryValue;
    }

    /**
     * @return \DateTime
     */
    public function getFrom(): ?\DateTime
    {
        return $this->from;
    }

    /**
     * @return \DateTime
     */
    public function getTo(): \DateTime
    {
        return $this->to;
    }

    /**
     * @return string
     */
    public function getNextToken(): ?string
    {
        return $this->nextToken;
    }
}
