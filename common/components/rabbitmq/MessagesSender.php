<?php

namespace common\components\rabbitmq;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use common\models\customer\AmazonReport;
use common\models\customer\DataImportPart;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use mikemadisonweb\rabbitmq\components\Producer;
use mikemadisonweb\rabbitmq\components\Routing;
use PhpAmqpLib\Exception\AMQPConnectionClosedException;

class MessagesSender
{
    use LogToConsoleTrait;

    /**
     * Enqueues task to reload event periods data from or cache (db) to clickhouse.
     *
     * @param EventPeriod $eventPeriod
     * @param string $sellerId
     */
    public function eventPeriodsFromCacheToClickhouse(EventPeriod $eventPeriod, string $sellerId)
    {
        $message = [
            'sellerId' => $sellerId,
            'eventPeriod' => [
                'id' => $eventPeriod->id,
            ],
        ];

        $this->info($message);

        $eventPeriod->markAsQueuedToClickhouse();
        $eventPeriod->saveOrThrowException();

        $this->publish($message, MessageInterface::EXCHANGE_NAME_CLICKHOUSE, 'from_cache_to_clickhouse');
    }

    public function processDataImportPart(DataImportPart $dataImportPart): void
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dataImportPart->status = DataImportPart::STATUS_QUEUED;
        $dataImportPart->save('false');
        $this->publish(
            json_encode([
                'data_import_part_id' => $dataImportPart->id,
                'customer_id' => $dbManager->getCustomerId(),
            ]),
            MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
            'process-part'
        );
    }

    /**
     * Enqueues task to reload event periods data from or cache (db) to clickhouse.
     *
     * @param EventPeriod $eventPeriod
     * @param string $sellerId
     */
    public function invokeCustomerProcess(CustomerProcess $customerProcess)
    {
        $message = [
            'customerProcessId' => $customerProcess->id
        ];

        $this->info($message);
        $customerProcess->setQueued();

        $this->publish($message, MessageInterface::EXCHANGE_NAME_CUSTOMER_PROCESS);
    }

    public function ppcCostApply(int $customerId, string $date)
    {
        $message = [
            'customerId' => $customerId,
            'date' => $date,
        ];
        $this->info($message);
        $routingKey = strtolower('apply');

        $this->publish($message, MessageInterface::EXCHANGE_NAME_PPC_COSTS, $routingKey, [], null, true);
    }

    public function indirectCostChanges(
        int $customerId,
        $indirectCostId,
        bool $isApplyNow = false,
        bool $isDeleted = false
    ): void
    {
        if (empty($indirectCostId)) {
            $this->error("Attempt to send indirect cost change on empty cost");
        }

        $message = [
            'customerId' => $customerId,
            'indirectCostId' => $indirectCostId,
            'isApplyNow' => $isApplyNow,
            'isDeleted' => $isDeleted
        ];
        $this->info($message);

        $this->publish($message, MessageInterface::EXCHANGE_NAME_INDIRECT_COST_CHANGES);
    }

    public function customerCOGCheckRefunds(int $customerId)
    {
        $message = [
            'customerId' => $customerId,
        ];

        $this->publish(
            $message,
            MessageInterface::EXCHANGE_NAME_COG_CHECK_REFUNDS,
            '',
            [],
            null,
            true
        );
    }

    public function customerCOGSSyncBatch(array $chunks) {
        shuffle($chunks);

        foreach ($chunks as $chunk) {
            try {
                $this->customerCOGSSync(
                    $chunk['customerId'],
                    $chunk['fromId'],
                    $chunk['toId'],
                    $chunk['isFirstSync'],
                    $chunk['shouldSendCOGChanges']
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function saveAmazonOrderItemToClickhouse(
        int $amazonOrderItemId,
        string $amazonOrderId,
        string $sellerId,
        string $amazonOrderPurchaseDate,
        bool $isAmazonOrderItemJustCreated
    ): void
    {
        $message = [
            'amazonOrderItemId' => $amazonOrderItemId,
            'amazonOrderId' => $amazonOrderId,
            'sellerId' => $sellerId,
            'isJustCreated' => $isAmazonOrderItemJustCreated
        ];
        $this->info('Sending save amazon order item to clickhouse to queue');
        $this->info($message);

        // Need to prioritize latest order items
        $priority = strtotime('-15 days') <= strtotime($amazonOrderPurchaseDate)
            ? 5
            : 1;

        $this->publish(
            $message,
            MessageInterface::EXCHANGE_NAME_CLICKHOUSE,
            'save-order-item',
            [
                'priority' => $priority
            ]
        );
    }

    public function customerCOGSSync(
        int $customerId,
        int $fromId,
        int $toId = null,
        bool $isFirstSync = false,
        bool $shouldSendCOGChanges = true
    ) {
        $message = [
            'customerId' => $customerId,
            'fromId' => $fromId,
            'toId' => $toId,
            'isFirstSync' => $isFirstSync,
            'shouldSendCOGChanges' => $shouldSendCOGChanges
        ];

        $this->publish($message, MessageInterface::EXCHANGE_NAME_COG_SYNC);
    }

    public function customerProductSync(string $sellerId, int $limit, int $offset)
    {
        $message = [
            'sellerId' => $sellerId,
            'limit' => $limit,
            'offset' => $offset
        ];
        $this->info($message);
        $this->publish($message, MessageInterface::EXCHANGE_NAME_PRODUCT_SYNC);
    }

    public function COGChangesBulk(array $COGChanges)
    {
        if (empty($COGChangesToSend)) {
            return;
        }
        $message = [
            'bulk' => []
        ];

        foreach ($COGChanges as $COGChange) {
            $message['bulk'][] = $this->COGChanges(
                $COGChange['amountPerItemOld'],
                $COGChange['currencyCodeOld'],
                $COGChange['amountPerItemNew'],
                $COGChange['currencyCodeNew'],
                $COGChange['dateStart'],
                $COGChange['dateEnd'],
                $COGChange['productCostItem'],
                $COGChange['productCostPeriod'],
                true
            );
        }

        $this->publish($message, MessageInterface::EXCHANGE_NAME_COG_CHANGES, 'bulk');
    }

    public function COGChanges(
        float $amountPerItemOld,
        string $currencyCodeOld,
        float $amountPerItemNew,
        string $currencyCodeNew,
        ?string $dateStart,
        ?string $dateEnd,
        ProductCostItem $productCostItem,
        ProductCostPeriod $productCostPeriod,
        bool $justReturnMessage = false,
        int $priority = 1
    ): ?array {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $message = [
            'amountPerItemNew' => $amountPerItemNew,
            'currencyCodeNew' => $currencyCodeNew,
            'amountPerItemOld' => $amountPerItemOld,
            'currencyCodeOld' => $currencyCodeOld,
            'productCostItemId' => $productCostItem->id,
            'customerId' => $dbManager->getCustomerId(),
            'dateStart' => $dateStart,
            'dateEnd' => $dateEnd,
            'createdAt' => date('Y-m-d H:i:s'),
            'sellerSku' => $productCostPeriod->seller_sku,
            'marketplaceId' => $productCostPeriod->marketplace_id,
            'sellerId' => $productCostPeriod->seller_id,
            'productCostCategoryId' => $productCostItem->product_cost_category_id,
            'isManual' => $priority === 5
        ];
        if ($justReturnMessage) {
            return $message;
        }
        $this->info('Sending COG changes to queue');
        $this->info($message);

        $this->publish($message, MessageInterface::EXCHANGE_NAME_COG_CHANGES, random_int(0, 4), [
            'priority' => $priority
        ]);
        return null;
    }

    public function handleCreateReportsInAmazon(string $sellerId, string $dataProviderType)
    {
        $this->info('Add seller amazon report to queue');
        $message = [
            'sellerId' => $sellerId,
            'dataProviderType' => $dataProviderType,
        ];
        $this->info($message);

        $this->publish($message, MessageInterface::EXCHANGE_NAME_AMAZON_REPORT, 'create', [], null, true);
    }

    public function handleSyncAmazonReportStatuses(string $sellerId)
    {
        $this->info("Adding queue sync statuses for seller {$sellerId}");
        $message = [
            'sellerId' => $sellerId
        ];
        $this->info($message);

        $this->publish($message, MessageInterface::EXCHANGE_NAME_AMAZON_REPORT, 'sync-statuses', [], null, true);
    }

    public function handleReadyAmazonReport(AmazonReport $amazonReport): void
    {
        $this->info('Sending ready amazon report to queue');
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $message = [
            'amazonReportId' => $amazonReport->id,
            'sellerId' => $dbManager->getSellerId()
        ];
        $this->info($message);
        $routingKey = strtolower($amazonReport->type);
        $amazonReport->setStatus(AmazonReport::STATUS_QUEUED);

        $this->publish($message, MessageInterface::EXCHANGE_NAME_AMAZON_REPORT, $routingKey);
    }

    /**
     * Sends message to sync product costs with global marketplace
     *
     * @param int $customerId
     * @param string $sku
     * @param string|null $marketplaceId Optional marketplace id to sync only specific marketplace
     */
    public function syncProductWithGlobalMarketplace(string $sellerId, string $sku, ?string $marketplaceId = null)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $message = [
            'customerId' => $dbManager->getCustomerId(),
            'sellerId' => $sellerId,
            'sku' => $sku,
            'marketplaceId' => $marketplaceId
        ];

        $this->info('Sending product sync with global marketplace to queue');
        $this->info($message);

        $this->publish(
            $message, 
            MessageInterface::EXCHANGE_NAME_COG_SYNC_GLOBAL_MARKETPLACE,
            '',
            [],
            null,
            true
        );
    }

    public function publish(
        $msgBody,
        string $exchangeName,
        string $routingKey = '',
        array $additionalProperties = [],
        array $headers = null,
        bool $shouldCheckDuplicates = false
    ) {
        /** @var Producer $producer */
        $producer = \Yii::$app->rabbitmq->getProducer(MessageAbstract::PRODUCER_NAME);

        $cacheKey = BaseConsumer::generateQueuedMessageCacheKey($exchangeName, $routingKey, $msgBody);
        $cache = \Yii::$app->fastPersistentCache;

        if ($shouldCheckDuplicates && $cache->get($cacheKey)) {
            $this->info('Message is already in queue, skipped');
            return;
        }

        try {
            if ($shouldCheckDuplicates) {
                $cache->set($cacheKey, 1, 60 * 60);
            }
            $producer->publish($msgBody, $exchangeName, $routingKey, $additionalProperties, $headers);
        } catch (AMQPConnectionClosedException $e) {
            $cache->delete($cacheKey);

            $this->info('Rabbitmq connection closed. Reconnecting and trying again');

            // Unfortunately this is the only found way to make reconnection after closing.
            // renew() function does not work for unknown reason.
            $producerReflection = new \ReflectionObject($producer);
            $connProp = $producerReflection->getProperty('conn');
            $connProp->setAccessible(true);
            $routingProp = $producerReflection->getProperty('routing');
            $routingProp->setAccessible(true);
            $chProp = $producerReflection->getProperty('ch');
            $chProp->setAccessible(true);

            // Cloning connection will invoke __clone function
            // and create new instance with the same constructor arguments
            // (see AbstractConnection).
            $connProp->setValue(
                $producer,
                clone $connProp->getValue($producer)
            );

            // Creating and setting new instance of routing with new connection.
            $routingProp->setValue(
                $producer,
                new Routing($connProp->getValue($producer))
            );

            // Need to set here null to invoke initialization in getChannel().
            $chProp->setValue($producer, null);
            $producer->getChannel();

            $producer->publish($msgBody, $exchangeName, $routingKey, $additionalProperties, $headers);
        }
    }
}
