<?php

namespace common\components\rabbitmq\consumers;

use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Selling<PERSON><PERSON>ner<PERSON>pi\ApiException;

abstract class BaseConsumer implements ConsumerInterface
{
    use LogToConsoleTrait;

    public const ZOMBIE_PROCESSES_THRESHOLD = 10000;
    public const MAX_INVOKES_BEFORE_RESTART = 1000;

    protected static int $countInvokes = 0;

    abstract function __execute(AMQPMessage $msg);

    public static function generateQueuedMessageCacheKey(string $exchangeName, string $routingKey, $messageBody): string
    {
        return 'message_queued_' . $exchangeName . '_' . md5(serialize($messageBody));
    }

    /**
     * @param AMQPMessage $msg
     * @return int|mixed
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function execute(AMQPMessage $msg)
    {
        self::$countInvokes++;
        \Yii::$app->mutex->expire = 60;
        \Yii::$app->arrayCache->flush();

        try {
            \Yii::$app->fastPersistentCache->delete($this->generateQueuedMessageCacheKey($msg->getExchange(), $msg->getRoutingKey(), $msg->body));
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $countZombieProcesses = $this->getCountZombieProcesses();
        $shouldRestartContainer = false;

        if ($countZombieProcesses > static::ZOMBIE_PROCESSES_THRESHOLD) {
            $this->info(sprintf(
                "To much zombie processes (%d from %d allowed), need to restart container",
                $countZombieProcesses,
                self::ZOMBIE_PROCESSES_THRESHOLD
            ));
            $shouldRestartContainer = true;
        }

        if (self::$countInvokes > static::MAX_INVOKES_BEFORE_RESTART) {
            $this->info(sprintf(
                "Exceeded %s invokes, need to restart container",
                self::$countInvokes - 1
            ));
            $shouldRestartContainer = true;
        }

        if ($shouldRestartContainer) {
            $countBackgroundTasks = $this->getCountBackgroundTasks();

            if ($countBackgroundTasks > 0) {
                $this->info(sprintf(
                    "Container can not be restarted due to %d background tasks. Waiting while they will be finished",
                    $countBackgroundTasks
                ));
                sleep(5);
                return self::MSG_REQUEUE;
            }

            $this->info('Exit and restarting container');
            exit;
        }

        $isProcessAware = $this instanceof ProcessAwareConsumerInterface;

        if ($isProcessAware) {
            $processKey = static::class . '_' . md5(serialize($msg->getBody()));
            /** @var ProcessManager $processManager */
            $processManager = \Yii::$app->processManager;
            $processManager->register($processKey);
        }

        try {
            $result = $this->__execute($msg);
        } catch (\Throwable $e) {
            if ($isProcessAware) {
                $processManager->release($processKey);
            }
            \Yii::$app->arrayCache->flush();
            throw $e;
        }

        if ($isProcessAware) {
            $processManager->release($processKey);
        }

        \Yii::$app->arrayCache->flush();
        return $result;

    }

    protected function getCountZombieProcesses(): int
    {
        exec("ps -ux | grep 'Z'", $res);

        // Exclude first 3 due to:
        // 1. "PID %CPU %MEM    VSZ   RSS TTY      STAT START"
        // 1. "sh -c ps -ux | grep 'Z'"
        // 2. "grep Z"
        // 3. Common docker process passed to "command"
        return count($res) - 3;
    }

    protected function getCountBackgroundTasks(): int
    {
        exec("ps -ux | grep '/yii'", $res);

        // Exclude first 3 due to:
        // 1. "sh -c ps -ux | grep '/yii'"
        // 2. "grep /yii"
        // 3. Common docker process passed to "command"
        return count($res) - 3;
    }
}
