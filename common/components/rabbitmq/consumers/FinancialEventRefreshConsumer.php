<?php

namespace common\components\rabbitmq\consumers;

use common\components\rabbitmq\message\BaseFinancialEventMessage;
use common\components\rabbitmq\message\FinancialEventRefreshMessage;
use common\models\finance\EventPeriod;

class FinancialEventRefreshConsumer extends BaseFinancialEventConsumer
{
    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, FinancialEventRefreshMessage::class);
    }

    public function createNewMessage($sellerId, $region): BaseFinancialEventMessage
    {
        return new FinancialEventRefreshMessage($sellerId, $region, []);
    }

    public function loadFromMessageBody($body): BaseFinancialEventMessage
    {
        return FinancialEventRefreshMessage::getFromMessage($body);
    }

    public function getFirstEventPeriod(): ?EventPeriod
    {
        return EventPeriod::find()
            ->where(['loading_status' => [EventPeriod::LOADING_STATUS_NEW], 'type' => EventPeriod::TYPE_REFRESH])
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();
    }
}
