<?php

namespace common\components\rabbitmq\consumers;

use common\components\rabbitmq\message\BaseFinancialEventMessage;
use common\components\rabbitmq\message\FinancialEventInitMessage;
use common\models\finance\EventPeriod;

class FinancialEventInitConsumer extends BaseFinancialEventConsumer
{
    public const MAX_INVOKES_BEFORE_RESTART = 100;

    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, FinancialEventInitMessage::class);
    }

    public function loadFromMessageBody($body): BaseFinancialEventMessage
    {
        return FinancialEventInitMessage::getFromMessage($body);
    }

    public function createNewMessage($sellerId, $region): BaseFinancialEventMessage
    {
        return new FinancialEventInitMessage($sellerId, $region, []);
    }

    public function getFirstEventPeriod(): ?EventPeriod
    {
        return EventPeriod::find()
            ->where(['loading_status' => [EventPeriod::LOADING_STATUS_NEW], 'type' => EventPeriod::TYPE_INIT])
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();
    }
}
