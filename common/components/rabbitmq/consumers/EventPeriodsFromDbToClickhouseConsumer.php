<?php

namespace common\components\rabbitmq\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\BufferFactory;
use common\components\demo\DemoDataManager;
use common\components\prometheus\Prometheus;
use common\components\rabbitmq\errors\OrderNotFoundException;
use common\components\exception\SellerNotFoundException;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\components\services\order\LoadAmazonOrderManager;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\finance\clickhouse\Transaction;
use common\models\finance\EventPeriod;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use yii\base\InvalidConfigException;
use yii\db\Query;
use yii\helpers\Inflector;
use yii\mutex\Mutex;

class EventPeriodsFromDbToClickhouseConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    /**
     * Batch size of query when extracting events from DB.
     */
    private const EVENT_EXTRACTING_BATCH_SIZE = 100;

    /**
     * @var BufferFactory
     */
    private $dataBufferFactory;

    private Prometheus $prometheus;
    private DbManager $dbManager;
    private CustomerConfig $customerConfig;
    private CustomerComponent $customerComponent;
    private DemoDataManager $demoDataManager;
    private Mutex $mutex;

    protected array $notLoadedOrderIds = [];

    protected array $refundTransactions = [];

    /**
     * EventGroupsFromDbToClickhouseConsumer constructor.
     */
    public function __construct()
    {
        ini_set('memory_limit', '256M');
        $this->dataBufferFactory = new BufferFactory();
        $this->prometheus = \Yii::$app->prometheus;
        $this->dbManager = \Yii::$app->dbManager;
        /** @var CustomerConfig $customerConfig */
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->customerComponent = new CustomerComponent();
        $this->demoDataManager = new DemoDataManager();
        $this->mutex = \Yii::$app->mutex;
    }

    /**
     * @param AMQPMessage $msg
     * @return int|mixed
     * @throws InvalidConfigException
     */
    public function __execute(AMQPMessage $msg)
    {
        $this->info("Exporting event group to clickhouse");
        $startedAt = microtime(true);
        $transactionStartedAt = null;

        $this->notLoadedOrderIds = [];
        $this->refundTransactions = [];

        $eventPeriodData = $msg->body['eventPeriod'] ?? null;
        $sellerId = $msg->body['sellerId'] ?? null;

        if (null === $eventPeriodData || null === $sellerId) {
            $this->error("Missed required parameters");
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'missed_required_parameters'
            );

            return ConsumerInterface::MSG_ACK;
        }

        $this->info([
            'eventPeriod' => $eventPeriodData,
            'sellerId' => $sellerId
        ]);

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->get('dbManager');
        try{
            $dbManager->setSellerId($sellerId);
        }catch (SellerNotFoundException $exception){
            return ConsumerInterface::MSG_ACK;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen, requeue message');
            return ConsumerInterface::MSG_REQUEUE;
        }

        if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
            return $this->__executeV2($msg);
        }

        \Yii::$app->db->enableSlaves = false;
        /** @var EventPeriod $eventPeriod */
        $eventPeriod = EventPeriod::find()
            ->andWhere($eventPeriodData)
            ->one();
        \Yii::$app->db->enableSlaves = true;

        if (null === $eventPeriod) {
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'event_period_not_found'
            );
            $this->info("There is no suitable event period found (version has been changed maybe)");

            return ConsumerInterface::MSG_ACK;
        }

        if ($eventPeriod->clickhouse_status !== EventPeriod::CLICKHOUSE_STATUS_QUEUED) {
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'irrelevant_status'
            );
            $this->info("irrelevant_status_or_version: Schema: ".$this->dbManager->getSchemaName('finance') ." Status: ".$eventPeriod->clickhouse_status);

            return ConsumerInterface::MSG_ACK;
        }

        $seller = Seller::findOneUseCache($sellerId);
        if (null === $seller) {
            $this->error("Unable to find seller $sellerId");
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'seller_not_found',
                $eventPeriod->type
            );

            return ConsumerInterface::MSG_ACK;
        }

        $wasTransactionsFound = false;
        $eventTables = EventPeriod::getEventTables();
        $dataBuffer = $this->dataBufferFactory->getTransactionsToClickhouseBuffer();
        $eventPeriod->has_transactions = false;
        $eventPeriod->clickhouse_status = EventPeriod::CLICKHOUSE_STATUS_PROCESSING;
        $eventPeriod->save(false);
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();

        try {
            if ($dbManager->isDemo()) {
                return $this->doDemo($eventPeriod, $seller);
            }

//            if ($dataBuffer->isMemoryFull()) {
//                throw new \Exception('ClickhouseTmpTableBuffer memory is full');
//            }

            foreach ($eventTables as $tableName) {
                $this->info(str_repeat('-', 30));
                $this->info("Fetching events data from table $tableName");
                $query = (new Query())->from($tableName)->where([
                    'event_period_id' => $eventPeriod->id
                ]);

                foreach ($query->batch(self::EVENT_EXTRACTING_BATCH_SIZE, $dbManager->getFinanceDb()) as $financialEvents) {
                    $financialEvents = $this->jsonDecodeEvents($financialEvents);
                    $this->info("Fetched " . count($financialEvents) . ' financial events');

                    $categoryPath = explode('.', $tableName)[1];

                    $extractedTransactions = $this->exctractTransactionsFromFinancialEvents(
                        $financialEvents,
                        $seller,
                        $eventPeriod,
                        Inflector::camelize($categoryPath)
                    );

                    if (count($extractedTransactions) > 0) {
                        $eventPeriod->has_transactions = true;
                        $wasTransactionsFound = true;
                    }

                    if (!empty($this->notLoadedOrderIds)) {
                        $dataBuffer->remove();
                        continue;
                    }

                    if (empty($transactionStartedAt)) {
                        $transactionStartedAt = microtime(true);
                    }

                    $dataBuffer->put($extractedTransactions);
                    // Leaving only refund transactions
                    foreach ($extractedTransactions as $k => $transaction) {
                        if ($this->dbManager->getCustomerId() == 700) {
                            $this->info(implode('_', ['ORDER', $transaction->AmazonOrderId, $transaction->SellerSKU]));
                        }

                        if ((int)$transaction->CategoryId !== (int)$organicRefundCategoryId) {
                            unset($extractedTransactions[$k]);
                        }
                    }
                    $this->delayRefundTransactionsCOGApplying($extractedTransactions);
                }
            }

            if (!empty($this->notLoadedOrderIds)) {
                $count = count($this->notLoadedOrderIds);
                $ids = implode(', ', array_slice($this->notLoadedOrderIds, 0, 10));
                throw new OrderNotFoundException("There are {$count} unknowns orders. Ids: {$ids}");
            }

            if (!empty($this->refundTransactions)) {
                $this->dbManager
                    ->getCustomerDb()
                    ->createCommand()
                    ->batchInsert(
                        RefundTransactionWithoutProductCost::tableName(),
                        array_keys(array_values($this->refundTransactions)[0]),
                        $this->refundTransactions
                    )
                    ->execute();
            }

            if (!$wasTransactionsFound) {
                $date = date('Y-m-d H:i:s');
                $message = "[{$date}] Transactions are not found";
                $eventPeriod->clickhouse_exception = empty($eventPeriod->clickhouse_exception) ? $message : $eventPeriod->clickhouse_exception . PHP_EOL . $message;
            }

            $eventPeriod->markAsMovedToClickhouse();
            $eventPeriod->saveOrThrowException();
            $dataBuffer->flush();
            unset($dataBuffer);

            $this->info(json_encode([
                'FINISHED_NO_ERROR' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) -  $transactionStartedAt
                        : null
                ]
            ]));

            $this->info("Finished successfully");
        } catch (OrderNotFoundException $e) {
            $this->info(json_encode([
                'FINISHED_ERROR_ORDER_NOT_FOUND' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) - $transactionStartedAt
                        : null
                ]
            ]));
            $dataBuffer->remove();
            unset($dataBuffer);

            $date = date('Y-m-d H:i:s');
            $message = "[{$date}] {$e->getMessage()}";
            $eventPeriod->markAsClickhouseOrderNotFoundException($message);
            $eventPeriod->saveOrThrowException();
            $seller->last_waiting_date = date('Y-m-d H:i:s');
            $seller->saveOrThrowException();

            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'waiting',
                'order_not_found',
                $eventPeriod->type
            );
            $this->info($e);

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->info(json_encode([
                'FINISHED_ERROR' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) - $transactionStartedAt
                        : null
                ]
            ]));
            $this->error($e);
            $dataBuffer->remove();
            unset($dataBuffer);

            $eventPeriod->markAsClickhouseException('[' . $e->getMessage() . "]\n" . $e->getTraceAsString());
            $eventPeriod->saveOrThrowException();
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'exception',
                $eventPeriod->type
            );

            return ConsumerInterface::MSG_ACK;
        }

        return ConsumerInterface::MSG_ACK;
    }

    public function __executeV2(AMQPMessage $msg)
    {
        $this->info("Exporting event group to clickhouse");
        $startedAt = microtime(true);
        $transactionStartedAt = null;

        $this->notLoadedOrderIds = [];
        $this->refundTransactions = [];

        $eventPeriodData = $msg->body['eventPeriod'] ?? null;
        $sellerId = $msg->body['sellerId'] ?? null;

        if (null === $eventPeriodData || null === $sellerId) {
            $this->error("Missed required parameters");
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'missed_required_parameters'
            );

            return ConsumerInterface::MSG_ACK;
        }

        $this->info([
            'eventPeriod' => $eventPeriodData,
            'sellerId' => $sellerId
        ]);

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->get('dbManager');
        try{
            $dbManager->setSellerId($sellerId);
        }catch (SellerNotFoundException $exception){
            return ConsumerInterface::MSG_ACK;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen, requeue message');
            return ConsumerInterface::MSG_REQUEUE;
        }

        $lockKey = "mutex_from_db_to_clickhouse_consumer_{$sellerId}_{$eventPeriodData['id']}";
        $timeStart = time();
        $this->info('Acquiring lock started');
        $this->mutex->acquire($lockKey, 5);
        $this->info('Acquiring lock finished');

        if (time() - $timeStart > 1) {
            $this->error(new \Exception("Event period is already moved to clickhouse"));
        }

        \Yii::$app->db->enableSlaves = false;
        /** @var EventPeriod $eventPeriod */
        $eventPeriod = EventPeriod::find()
            ->andWhere($eventPeriodData)
            ->noCache()
            ->one();
        \Yii::$app->db->enableSlaves = true;

        if (null === $eventPeriod) {
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'event_period_not_found'
            );
            $this->info("There is no suitable event period found (version has been changed maybe)");
            $this->mutex->release($lockKey);

            return ConsumerInterface::MSG_ACK;
        }

        if ($eventPeriod->clickhouse_status !== EventPeriod::CLICKHOUSE_STATUS_QUEUED) {
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'irrelevant_status'
            );
            $this->info("irrelevant_status_or_version: Schema: ".$this->dbManager->getSchemaName('finance') ." Status: ".$eventPeriod->clickhouse_status);
            $this->mutex->release($lockKey);

            return ConsumerInterface::MSG_ACK;
        }

        $seller = Seller::find()->where(['id' => strtoupper($sellerId)])->one(\Yii::$app->db);
        if (null === $seller) {
            $this->error("Unable to find seller $sellerId");
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'seller_not_found',
                $eventPeriod->type
            );
            $this->mutex->release($lockKey);

            return ConsumerInterface::MSG_ACK;
        }

        $wasTransactionsFound = false;
        $eventTables = EventPeriod::getEventTables();
        $dataBuffer = $this->dataBufferFactory->getTransactionsToClickhouseBuffer();
        $eventPeriod->has_transactions = false;
        $eventPeriod->clickhouse_status = EventPeriod::CLICKHOUSE_STATUS_PROCESSING;
        $eventPeriod->save(false);
        $hasNotLoadedOrders = false;

        try {
            if ($dbManager->isDemo()) {
                $this->mutex->release($lockKey);
                return $this->doDemo($eventPeriod, $seller);
            }

//            if ($dataBuffer->isMemoryFull()) {
//                throw new \Exception('ClickhouseTmpTableBuffer memory is full');
//            }

            foreach ($eventTables as $tableName) {
                $this->info(str_repeat('-', 30));
                $this->info("Fetching events data from table $tableName");
                $query = (new Query())->from($tableName)->where([
                    'event_period_id' => $eventPeriod->id
                ]);

                foreach ($query->batch(self::EVENT_EXTRACTING_BATCH_SIZE, $dbManager->getFinanceDb()) as $financialEvents) {
                    $financialEvents = $this->jsonDecodeEvents($financialEvents);
                    $this->info("Fetched " . count($financialEvents) . ' financial events');

                    $this->info('Extracting transactions from financial events started');
                    $chTransactionsExtractor = new ClickhouseTransactionExtractor();
                    $chTransactionsExtractor->process(
                        $financialEvents,
                        Inflector::camelize(explode('.', $tableName)[1]),
                        [
                            ClickhouseTransactionExtractor::FACT_SELLER_ID => $seller->id,
                            ClickhouseTransactionExtractor::FACT_POSTED_DATE => $eventPeriod->finish_date
                        ]
                    );

                    try {
                        /** @var Transaction[] $extractedTransactions */
                        $extractedTransactions = $chTransactionsExtractor->getAndFlushTransactions($seller);
                    } catch (OrderNotFoundException $e) {
                        $hasNotLoadedOrders = true;
                        $dataBuffer->remove();
                        continue;
                    }
                    $this->info('Extracting transactions from financial events finished');

                    if (count($extractedTransactions) > 0) {
                        $eventPeriod->has_transactions = true;
                        $wasTransactionsFound = true;
                    }

                    if (empty($transactionStartedAt)) {
                        $transactionStartedAt = microtime(true);
                    }

                    $dataBuffer->put($extractedTransactions);
                }
            }

            if ($hasNotLoadedOrders) {
                throw new OrderNotFoundException('Some orders are not found');
            }

            if (!$wasTransactionsFound) {
                $date = date('Y-m-d H:i:s');
                $message = "[{$date}] Transactions are not found";
                $eventPeriod->clickhouse_exception = empty($eventPeriod->clickhouse_exception) ? $message : $eventPeriod->clickhouse_exception . PHP_EOL . $message;
            }

            $eventPeriod->markAsMovedToClickhouse();
            $eventPeriod->saveOrThrowException();
            $dataBuffer->flush();
            unset($dataBuffer);

            $this->info(json_encode([
                'FINISHED_NO_ERROR' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) -  $transactionStartedAt
                        : null
                ]
            ]));

            $this->info("Finished successfully");
            $this->mutex->release($lockKey);
        } catch (OrderNotFoundException $e) {
            $this->mutex->release($lockKey);
            $this->info(json_encode([
                'FINISHED_ERROR_ORDER_NOT_FOUND' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) - $transactionStartedAt
                        : null
                ]
            ]));
            $dataBuffer->remove();
            unset($dataBuffer);

            $date = date('Y-m-d H:i:s');
            $message = "[{$date}] {$e->getMessage()}";
            $eventPeriod->markAsClickhouseOrderNotFoundException($message);
            $eventPeriod->saveOrThrowException();
            $seller->last_waiting_date = date('Y-m-d H:i:s');
            $seller->saveOrThrowException();

            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'waiting',
                'order_not_found',
                $eventPeriod->type
            );
            $this->info($e);

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->mutex->release($lockKey);
            $this->info(json_encode([
                'FINISHED_ERROR' => [
                    'totalTime' => microtime(true) - $startedAt,
                    'transactionTime' => null !== $transactionStartedAt
                        ? microtime(true) - $transactionStartedAt
                        : null
                ]
            ]));
            $this->error($e);
            $dataBuffer->remove();
            unset($dataBuffer);

            $eventPeriod->markAsClickhouseException('[' . $e->getMessage() . "]\n" . $e->getTraceAsString());
            $eventPeriod->saveOrThrowException();
            $this->prometheus->incConsumerState(
                'event_period_from_db_to_clickhouse',
                'error',
                'exception',
                $eventPeriod->type
            );

            return ConsumerInterface::MSG_ACK;
        }

        return ConsumerInterface::MSG_ACK;
    }

    /**
     * @param Transaction[] $refundTransactions
     * @return void
     */
    private function delayRefundTransactionsCOGApplying(array $refundTransactions): void
    {
        foreach ($refundTransactions as $refundTransaction) {
            if (empty($refundTransaction->MarketplaceId)
                || empty($refundTransaction->SellerId)
                || empty($refundTransaction->SellerSKU)
                || empty($refundTransaction->AmazonOrderId)
            ) {
                continue;
            }

            $this->refundTransactions[] = [
                'posted_date' => $refundTransaction->PostedDate,
                'marketplace_id' => $refundTransaction->MarketplaceId,
                'seller_id' => $refundTransaction->SellerId,
                'seller_sku' => $refundTransaction->SellerSKU,
                'asin' => $refundTransaction->ASIN,
                'category_id' => $refundTransaction->CategoryId,
                'amount' => $refundTransaction->Amount,
                'currency' => $refundTransaction->Currency,
                'quantity' => $refundTransaction->Quantity,
                'amazon_order_id' => $refundTransaction->AmazonOrderId,
                'seller_order_id' => $refundTransaction->SellerOrderId,
                'event_period_id' => $refundTransaction->EventPeriodId,
            ];
        }

    }

    /**
     * Extracts clickhouse transactions from financial events tree.
     *
     * @param $financialEvents
     * @param Seller $seller
     * @param string $categoryPath
     * @return array
     */
    private function exctractTransactionsFromFinancialEvents(
        $financialEvents,
        Seller $seller,
        EventPeriod $eventPeriod,
        string $categoryPath
    ) {
        $chTransactionsExtractor = new ClickhouseTransactionExtractor();
        $chTransactionsExtractor->process(
            $financialEvents,
            $categoryPath,
            [
                ClickhouseTransactionExtractor::FACT_SELLER_ID => $seller->id,
                ClickhouseTransactionExtractor::FACT_POSTED_DATE => $eventPeriod->finish_date
            ]
        );

        /** @var Transaction[] $extractedTransactions */
        $extractedTransactions = $chTransactionsExtractor->getAndFlushTransactions();

        if (!empty($orderIds = $chTransactionsExtractor->getOrderIdsForLoading())) {
            $orderIds = array_values(array_unique($orderIds));
            foreach ($orderIds as $amazonOrderId) {
                $this->notLoadedOrderIds[] = $amazonOrderId;

                (new LoadAmazonOrderManager())->addOrderIdToLoadingList($seller, $amazonOrderId);
            }
        }

        $orderIdsForUpdateHasTransactions = $chTransactionsExtractor->getOrderIdsForUpdateHasTransactions();

        if (!empty($orderIdsForUpdateHasTransactions)) {
            AmazonOrder::updateAll([
                'has_transactions' => true
            ], [
                'id' => $orderIdsForUpdateHasTransactions
            ]);
        }

        $this->info("Extracted " . count($extractedTransactions) . ' clickhouse transactions');

        return $extractedTransactions;
    }

    /**
     * Walks through financial events and decodes json encoded properties.
     *
     * @param array $financialEvents
     * @return array
     */
    private function jsonDecodeEvents(array $financialEvents)
    {
        foreach ($financialEvents as $eventKey => $financialEvent) {
            foreach ($financialEvent as $propertyName => $propertyValue) {
                if (is_array($propertyValue)) {
                    continue;
                }
                $propertyValue = stripslashes($propertyValue);
                $propertyValue = trim($propertyValue, '""');
                $jsonValue = json_decode($propertyValue, true);

                if (!empty($jsonValue)) {
                    $financialEvents[$eventKey][$propertyName] = $jsonValue;
                }
            }
        }

        return $financialEvents;
    }

    private function doDemo(EventPeriod $eventPeriod, Seller $seller): int
    {
        $this->demoDataManager->generateOrdersAndTransactions(
            new \DateTime($eventPeriod->start_date),
            new \DateTime($eventPeriod->finish_date),
            $seller->id,
            $eventPeriod->id
        );

        $eventPeriod->has_transactions = true;
        $eventPeriod->markAsMovedToClickhouse();
        $eventPeriod->saveOrThrowException();

        return ConsumerInterface::MSG_ACK;
    }
}
