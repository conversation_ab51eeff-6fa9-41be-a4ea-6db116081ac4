<?php

namespace common\components\rabbitmq\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\message\BaseFinancialEventMessage;
use common\components\rabbitmq\message\FinancialEventInitMessage;
use common\components\rabbitmq\MessagesSender;
use common\components\sellingApi\apiProxy\FinancesApi;
use common\components\sellingApi\exception\AccessDeniedException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\DateRangeIsNotValidException;
use common\components\sellingApi\exception\InvalidInputException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use common\components\services\financialEvent\LoadingEventsService;
use common\components\services\financialEvent\StoreEventsService;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\models\finance\EventPeriod;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Model\FinancesV0\ListFinancialEventsPayload;
use yii\helpers\Json;

abstract class BaseFinancialEventConsumer extends BaseConsumer
{
    protected Prometheus $prometheus;

    const MAX_ERRORS_COUNT = 10;

    abstract public function isMessageClassCorrect($message): bool;

    abstract public function createNewMessage($sellerId, $region): BaseFinancialEventMessage;

    abstract public function getFirstEventPeriod(): ?EventPeriod;

    abstract public function loadFromMessageBody($body): BaseFinancialEventMessage;

    public function __construct()
    {
        $this->prometheus = \Yii::$app->prometheus;
    }

    /**
     * @param AMQPMessage $msg
     * @return int|mixed
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function __execute(AMQPMessage $msg)
    {
        ini_set('memory_limit', '256M');
        $eventPeriod = null;
        try {
            $this->info(str_repeat('-', 50));
            $this->info($this->getName() . ': Started');

            $message = $this->loadFromMessageBody($msg->body);
            /** @var Seller $seller */
            $seller = Seller::find()->where(['id' => $message->getSellerId()])->one();

            if ($this->isMessageClassCorrect($message)) {
                $this->info([
                    'customerId' => $seller->customer_id,
                    'sellerId' => $message->getSellerId(),
                    'eventPeriodId' => $message->getEventPeriodId(),
                    'isMessageFinished' => $message->isFinished()
                ]);

                /** @var DbManager $sellerDbManager */
                $sellerDbManager = \Yii::$app->get('dbManager');
                try{
                    $sellerDbManager->setSellerId($message->getSellerId());
                }catch (SellerNotFoundException $exception){
                    $this->info('EXCEPTION: ' . $exception->getMessage());
                    return ConsumerInterface::MSG_ACK;
                }
                /** @var EventPeriod $eventPeriod */
                $eventPeriod = EventPeriod::findOne($message->getEventPeriodId());

                if (empty($eventPeriod)) {
                    $this->info('ERROR: Unable to find event period');
                    return ConsumerInterface::MSG_ACK;
                }

                $eventPeriod->updated_at = date('Y-m-d H:i:s');
                $eventPeriod->save(false);

                $startDate = new \DateTime($eventPeriod->start_date);
                $minStartDate = (new \DateTime())->modify('-730 days');

                if ($seller->is_demo) {
                    $minStartDate = (new \DateTime())->modify('-6 month');
                }

                // Prevention of API error "Requested date range cannot end before ... UTC ..."
                if ($startDate < $minStartDate) {
                    $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_SKIPPED;
                    $eventPeriod->saveOrThrowException();
                    return ConsumerInterface::MSG_ACK;
                }

                if ($eventPeriod->isLoadingTerminated()) {
                    $this->info('Loading has been terminated');
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'loading_terminated',
                        $eventPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                if ($seller->is_demo) {
                    return $this->doDemo($eventPeriod, $seller);
                }

                if (!$seller->canMakeRequestToAmazon()) {
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->markAsTerminated('Seller can not make requests to amazon');
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'terminated_seller_with_invalid_token',
                        $eventPeriod->type
                    );
                    return ConsumerInterface::MSG_ACK;
                }

                $maxResultsPerPage = $message->getMaxResultsPerPage();
                $errorsCount = $message->getErrorsCount();

                if ($errorsCount > self::MAX_ERRORS_COUNT || $maxResultsPerPage === 0) {
                    $this->info("PERIOD {$eventPeriod->id} {$eventPeriod->start_date} - {$eventPeriod->finish_date} has been terminated doe to high amount of errors");
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->markAsTerminated('High amount of errors');
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'terminated_too_much_errors',
                        $eventPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                // Prevention of double processing (first call && wrong status = skip).
                if (empty($message->getNextToken()) && $eventPeriod->loading_status !== EventPeriod::LOADING_STATUS_QUEUED) {
                    $this->info("Prevention of double processing. Status is not " . EventPeriod::LOADING_STATUS_QUEUED);
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'skipped_already_processing',
                        $eventPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_PROCESSING;
                $eventPeriod->save(false);

                $message->wait();

                try {
                    $this->info('Loading data started');
                    $payload = $this->loadData($message, $maxResultsPerPage);
                    $this->info('Loading data finished');
                } catch (NextTokenExpiredException | InvalidInputException $e) {
                    $this->info('EXCEPTION 1: ' . $e->getMessage());

                    $eventStoreService = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $eventStoreService->rollbackCurrentVersionToInitialState();

                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'next_token_expired',
                        $eventPeriod->type
                    );
                    $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
                    $eventPeriod->save(false);

                    // Try to decrease number of results per page because amazon API says and does:
                    // If the response exceeds the maximum number of transactions or 10 MB, the API responds with 'InvalidInput'.
                    if ($e instanceof InvalidInputException) {
                        $maxResultsPerPage = floor($message->getMaxResultsPerPage() / 2);
                        $message->setMaxResultsPerPage($maxResultsPerPage);
                    }

                    $message->setNextToken(null);
                    $message->publish(2);

                    return ConsumerInterface::MSG_ACK;
                }
                catch (AccessDeniedException | AccessTokenInvalidException | NoAccessTokenException $e) {
                    $this->info('EXCEPTION 2: ' . $e->getMessage());
                    $seller->setIsTokenReceived(false);
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->markAsTerminated($e->getMessage());
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'terminated_token_is_not_valid_anymore',
                        $eventPeriod->type
                    );
                    $this->error($e);
                    return ConsumerInterface::MSG_ACK;
                }
                catch (DateRangeIsNotValidException $e) {
                    $this->info('EXCEPTION 3: ' . $e->getMessage());
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->markAsTerminated($e->getMessage());
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'terminated_wrong_date_range',
                        $eventPeriod->type
                    );
                    return ConsumerInterface::MSG_ACK;
                }
                catch (ApiException $e) {
                    $this->error($e);
                    $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
                    $eventPeriod->save(false);

                    $message->increaseErrorsCount();
                    $message->publish();

                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'api_exception',
                        $eventPeriod->type
                    );

                    $this->info("Message down with ApiException: ". $message->getMessage());

                    return ConsumerInterface::MSG_ACK;
                }
                catch (\Throwable $e) {
                    $this->error($e);
                    $eventStoreService = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $eventStoreService->markAsTerminated($e->getMessage());

                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'error',
                        'exception',
                        $eventPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                if (!$payload) {
                    $this->info("Empty payload");

                    if ($message->getNextToken()) {
                        $this->info("Next token is {$message->getNextToken()}");
                        $this->prometheus->incConsumerState(
                            'base_financial_event',
                            'error',
                            'empty_payload',
                            $eventPeriod->type
                        );
                    } else {
                        $this->error("Next token is empty");
                        $this->prometheus->incConsumerState(
                            'base_financial_event',
                            'error',
                            'empty_next_token',
                            $eventPeriod->type
                        );
                    }
                    $eventPeriod->save(false);

                    return ConsumerInterface::MSG_REQUEUE;
                }

                if ($payload->getFinancialEvents()) {
                    $this->info('Saving financial events from payload');
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->saveData($payload->getFinancialEvents());
                }

                $message->setNextToken($payload->getNextToken() ? $payload->getNextToken() : null);
                $message->setLastExecuteTime(new \DateTime());
                $message->resetErrorsCount();

                if (!$payload->getNextToken()) {
                    $this->info($this->getName() . ': Loading event data is finished for ' . json_encode([
                            'SellerId' => $message->getSellerId(),
                            'eventPeriodID' => $message->getEventPeriodId()
                        ]));
                    $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                    $manager->markAsFinished();
                    $this->prometheus->incConsumerState(
                        'base_financial_event',
                        'success',
                        'finished',
                        $eventPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $this->info(
                    $this->getName() . ': Data for eventPeriodID ' . $message->getEventPeriodId()
                    . ' loaded. Next token '
                    . ($payload->getNextToken()
                        ? $payload->getNextToken()
                        : 'is empty')
                );

                $message->publish();
                $this->prometheus->incConsumerState(
                    'base_financial_event',
                    'success',
                    'published_with_next_token',
                    $eventPeriod->type
                );
            }
            $eventPeriod->save(false);

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->prometheus->incConsumerState(
                'base_financial_event',
                'error',
                'exception_common_wrapper',
                $eventPeriod->type ?? null
            );
            $this->error($e);
            if (!empty($eventPeriod)) {
                $manager = new StoreEventsService($eventPeriod, $message->getSellerId());
                $manager->markAsTerminated($e->getMessage());
                $eventPeriod->save(false);
            }

            return ConsumerInterface::MSG_ACK;
        }
    }

    /**
     * @param BaseFinancialEventMessage $message
     * @param int $maxResultsPerPage
     * @return ListFinancialEventsPayload|null
     * @throws ApiException
     */
    private function  loadData(BaseFinancialEventMessage $message, int $maxResultsPerPage = 100)
    {
        $apiInstance = new FinancesApi($message->getSellerId(), $message->getRegion());

        if (!$eventPeriod = EventPeriod::findOne($message->getEventPeriodId())) {
            throw new \Exception('Event period is not found');
        }

        $result = $apiInstance->listFinancialEvents(
            $maxResultsPerPage,
            (new \DateTime($eventPeriod->start_date))->format('c'),
            (new \DateTime($eventPeriod->finish_date))->format('c'),
            $message->getNextToken());

        if (!empty($result->getErrors())) {
            $errors = Json::encode($result->getErrors());
            $this->error("PERIOD {$eventPeriod->id}. Response has errors: {$errors}");
        }
        return $result->getPayload();
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        $path = explode('\\', __CLASS__);
        return array_pop($path);
    }

    /**
     * test for memory leak
     */
    private function closeConnectionRandomly(){
        if ($this->counter % 100 === 99)
            \Yii::$app->db->close();
    }

    private function doDemo(EventPeriod $eventPeriod, Seller $seller): int
    {
        $this->info('Demo financial event loading, just marking as finished');

        $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_FINISHED;
        $eventPeriod->saveOrThrowException();

        $messagesSender = new MessagesSender();
        $messagesSender->eventPeriodsFromCacheToClickhouse($eventPeriod, $seller->id);

        $loadingService = new LoadingEventsService($seller);

        if ($eventPeriod->type === EventPeriod::TYPE_INIT) {
            $loadingService->loadInit();
        } else {
            $loadingService->loadRefresh();
        }

        return ConsumerInterface::MSG_ACK;
    }
}
