<?php

namespace common\components\rabbitmq\consumers;

use common\components\cron\GeneralCronComponent;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use yii\helpers\Json;

/**
 * Consumes messages from cron queue, executes cron commands
 * @package common\components\rabbitmq\consumers
 */
class CronConsumer extends BaseConsumer
{
    public const MAX_INVOKES_BEFORE_RESTART = 15000;
    public const ZOMBIE_PROCESSES_THRESHOLD = 15000;

    public function __execute(AMQPMessage $msg)
    {
        try {
            $message = Json::decode($msg->getBody());
        } catch (\Throwable $e) {
            return ConsumerInterface::MSG_ACK;
        }

        $command = $message['command'];
        $isAllowMultiple = $message['isAllowMultiple'] ?? false;

        if (empty($command)) {
            return ConsumerInterface::MSG_ACK;
        }

        /** @var GeneralCronComponent $generalCron */
        $generalCron = \Yii::$app->get('cronComponent');
        $generalCron->executeCommand($command, $isAllowMultiple);
        \Yii::getLogger()->flush();

        return ConsumerInterface::MSG_ACK;
    }
}
