<?php

namespace common\components\rabbitmq\consumers\order;

use common\components\rabbitmq\message\order\BaseLoadOrdersMessage;
use common\components\rabbitmq\message\order\LoadLoadOrdersInitMessage;
use common\models\order\OrderPeriod;

class LoadOrdersInitConsumer extends BaseLoadOrdersConsumer
{
    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, LoadLoadOrdersInitMessage::class);
    }

    public function loadFromMessageBody($body): BaseLoadOrdersMessage
    {
        return LoadLoadOrdersInitMessage::getFromMessage($body);
    }

    public function createNewMessage($sellerId, $region): BaseLoadOrdersMessage
    {
        return new LoadLoadOrdersInitMessage($sellerId, $region, []);
    }

    public function getFirstOrderPeriod(): ?OrderPeriod
    {
        return OrderPeriod::find()
            ->where(['loading_status' => [OrderPeriod::LOADING_STATUS_NEW], 'type' => OrderPeriod::TYPE_INIT])
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();
    }
}
