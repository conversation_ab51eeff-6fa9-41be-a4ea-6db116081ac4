<?php

namespace common\components\rabbitmq\consumers\order;


use common\components\rabbitmq\message\order\BaseLoadOrdersMessage;
use common\components\rabbitmq\message\order\LoadLoadOrdersRefreshMessage;
use common\models\order\OrderPeriod;

class LoadOrdersRefreshConsumer extends BaseLoadOrdersConsumer
{
    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, LoadLoadOrdersRefreshMessage::class);
    }

    public function createNewMessage($sellerId, $region): BaseLoadOrdersMessage
    {
        return new LoadLoadOrdersRefreshMessage($sellerId, $region, []);
    }

    public function loadFromMessageBody($body): BaseLoadOrdersMessage
    {
        return LoadLoadOrdersRefreshMessage::getFromMessage($body);
    }

    public function getFirstOrderPeriod(): ?OrderPeriod
    {
        return OrderPeriod::find()
            ->where(['loading_status' => [OrderPeriod::LOADING_STATUS_NEW], 'type' => OrderPeriod::TYPE_REFRESH])
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();
    }
}
