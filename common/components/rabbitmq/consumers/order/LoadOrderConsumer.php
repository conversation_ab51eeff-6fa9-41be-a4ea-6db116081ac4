<?php

namespace common\components\rabbitmq\consumers\order;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\exception\EmptyAmazonOrderIdException;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\message\order\LoadOrderMessage;
use common\components\sellingApi\apiProxy\OrdersApi;
use common\components\sellingApi\exception\AccessDeniedException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\DateRangeIsNotValidException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\sellingApi\helpers\DateHelper;
use common\components\services\order\dataConverter\OrderFromAmazonAPIToOurOrderConverter;
use common\components\services\order\LoadAmazonOrderManager;
use common\components\services\order\StoreOrderService;
use common\components\services\order\StoreOrdersServiceV2;
use common\components\services\order\UnknownOrderService;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\models\AmazonMarketplace;
use common\models\order\AmazonOrder;
use common\models\order\OrderPeriod;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Model\OrdersV0\GetOrderResponse;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\helpers\Json;

class LoadOrderConsumer extends BaseConsumer
{
    protected Prometheus $prometheus;
    protected LoadAmazonOrderManager $loadAmazonOrderManager;
    protected CustomerConfig $customerConfig;

    const MAX_ERRORS_COUNT = 10;

    const API_ERROR_TIMEOUT_SECONDS = 2;


    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, LoadOrderMessage::class);
    }

    public function loadFromMessageBody($body): LoadOrderMessage
    {
        return LoadOrderMessage::getFromMessage($body);
    }

    public function createNewMessage($sellerId, $region): LoadOrderMessage
    {
        return new LoadOrderMessage($sellerId, $region, []);
    }

    public function __construct()
    {
        $this->prometheus = \Yii::$app->prometheus;
        $this->loadAmazonOrderManager = new LoadAmazonOrderManager();
        $this->customerConfig = \Yii::$container->get("customerConfig");
    }

    public function __execute(AMQPMessage $msg): int
    {
        try {
            $this->info($this->getName() . ': Started');
            $message = $this->loadFromMessageBody($msg->body);
            /** @var Seller $seller */
            $seller = Seller::find()->where(['id' => $message->getSellerId()])->cache(5 * 60)->one();

            if ($this->isMessageClassCorrect($message)) {
                $amazonOrderId = $message->getAmazonOrderId();
                $this->info($message->getSellerId() . ' ' . $message->getAmazonOrderId());

                /** @var DbManager $sellerDbManager */
                $sellerDbManager = \Yii::$app->get('dbManager');
                $sellerDbManager->setSellerId($message->getSellerId());

                $this->loadAmazonOrderManager->removeOrderIdFromLoadingList($message->getSellerId(), $amazonOrderId);

                if (AmazonOrder::find()->where(['amazon_order_id' => $message->getAmazonOrderId()])->exists()) {
                    $this->info("Order {$message->getAmazonOrderId()} has already been loaded");
                    return ConsumerInterface::MSG_ACK;
                }

                if (!$seller->canMakeRequestToAmazon()) {
                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'terminated_seller_with_invalid_token',
                    );
                    $this->info("Terminated with invalid token");
                    return ConsumerInterface::MSG_ACK;
                }

                if ($message->getErrorsCount() > self::MAX_ERRORS_COUNT) {
                    $this->info("Loading order {$amazonOrderId} has been terminated due to high amount of errors");
                    return ConsumerInterface::MSG_ACK;
                }


                try {
                    $this->wait($message->getSellerId());
                    $payload = $this->loadData($message);
                }
                catch (AccessDeniedException | AccessTokenInvalidException | NoAccessTokenException $e) {
                    $seller->setIsTokenReceived(false);
                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'terminated_token_is_not_valid_anymore',
                    );
                    return ConsumerInterface::MSG_ACK;
                }
                catch (QuotaExceededException $e) {
                    $message->increaseErrorsCount();
                    $message->publish();

                    //generally there is one request per minute, if next message if for the same seller, better to sleep for one minute
                    sleep(60);

                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'quota_exception',
                    );

                    return ConsumerInterface::MSG_ACK;
                }
                catch (ApiException $e) {
                    $this->error($e);
                    $message->increaseErrorsCount();
                    $message->publish();

                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'api_exception',
                    );

                    return ConsumerInterface::MSG_ACK;
                }
                catch (\Throwable $e) {
                    $this->error($e);
                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'exception'
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                if (!$payload) {
                    $this->info("Order {$amazonOrderId}. Empty payload");
                    $this->prometheus->incConsumerState(
                        'load_order',
                        'error',
                        'empty_payload'
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $useRealtimeOrderDataUpdate = $this->customerConfig
                    ->get(CustomerConfig::PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES);

                try {
                    if ($useRealtimeOrderDataUpdate) {
                        if (empty($payload->getAmazonOrderId())) {
                            throw new EmptyAmazonOrderIdException();
                        }

                        $dataConverter = new OrderFromAmazonAPIToOurOrderConverter();
                        $amazonOrder = $dataConverter->convert($payload, $seller->id);

                        $storeOrdersService = new StoreOrdersServiceV2($seller->id);
                        $storeOrdersService->save([$amazonOrder]);
                    } else {
                        $manager = new StoreOrderService($message->getSellerId());
                        $manager->saveData($payload);
                    }
                } catch (EmptyAmazonOrderIdException $e) {
                    (new UnknownOrderService($message->getSellerId()))->increaseErrors($message->getAmazonOrderId());
                }

                $this->info("{$this->getName()} : Order $amazonOrderId loaded");

                $this->prometheus->incConsumerState(
                    'load_order',
                    'success',
                    'loaded'
                );
            }

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->prometheus->incConsumerState(
                'load_order',
                'error',
                'exception_common_wrapper',
            );
            $this->error($e);

            return ConsumerInterface::MSG_ACK;
        }
    }

    /**
     * @param LoadOrderMessage $message
     * @return Order|null
     * @throws ApiException
     */
    private function loadData(LoadOrderMessage $message): ?Order
    {
        $apiInstance = new OrdersApi($message->getSellerId(), $message->getRegion());

        $this->loadAmazonOrderManager->setLastAmazonRequestDate($message->getSellerId(), (new \DateTime()));
        $result = $apiInstance->getOrder($message->getAmazonOrderId());

        $this->storeRateLimit($message->getSellerId(), $result);

        if (!empty($result->getErrors())) {
            $errors = Json::encode($result->getErrors());
            $this->error("Order {$message->getAmazonOrderId()}. Response has errors: {$errors}");
        }

        return $result->getPayload();
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        $path = explode('\\', __CLASS__);
        return array_pop($path);
    }

    protected function storeRateLimit(string $sellerId, GetOrderResponse $result)
    {
        if (is_null($this->loadAmazonOrderManager->getCurrentRateLimit($sellerId))) {
            $limit = isset($result->getHeaders()['x-amzn-RateLimit-Limit'][0]) ? $result->getHeaders()['x-amzn-RateLimit-Limit'][0] : null;
            if (!is_null($limit)) {
                $this->info('Set rate limit: ' . $limit);
                $this->loadAmazonOrderManager->setCurrentRateLimit($sellerId, floatval($limit));
            }
        }

        $this->info('Loaded rate limit: ' . $this->loadAmazonOrderManager->getCurrentRateLimit($sellerId));
    }

    public function wait(string $sellerId)
    {
        sleep(2);

        $lastRequestDate = $this->loadAmazonOrderManager->getLastAmazonRequestDate($sellerId);
        $rateLimit = $this->loadAmazonOrderManager->getCurrentRateLimit($sellerId);

        $rateLimit = is_null($rateLimit) ? LoadAmazonOrderManager::DEFAULT_RATE_LIMIT : $rateLimit;

        $sleepSecondsBetweenRequest = intval(1 / $rateLimit) + 1;

        $this->info("sleepSecondsBetweenRequest: $sleepSecondsBetweenRequest");

        if (is_null($lastRequestDate)) {
            $this->info("lastRequest is null");
            sleep($sleepSecondsBetweenRequest);
            return;
        }

        $this->info("lastRequest: {$lastRequestDate->getTimestamp()}");
        $nextRequestDate = $lastRequestDate->getTimestamp() + $sleepSecondsBetweenRequest;
        $diff = $nextRequestDate - time();

        $this->info("diff: $diff");

        if ($diff > 0) {
            sleep($diff);
        }
    }
}
