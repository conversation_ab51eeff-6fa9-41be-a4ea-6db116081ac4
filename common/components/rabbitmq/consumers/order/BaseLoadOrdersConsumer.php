<?php

namespace common\components\rabbitmq\consumers\order;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\message\order\BaseLoadOrdersMessage;
use common\components\sellingApi\apiProxy\OrdersApi;
use common\components\sellingApi\exception\AccessDeniedException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\DateRangeIsNotValidException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use common\components\sellingApi\helpers\DateHelper;
use common\components\services\order\dataConverter\OrderFromAmazonAPIToOurOrderConverter;
use common\components\services\order\StoreOrdersService;
use common\components\services\order\StoreOrdersServiceV2;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\models\AmazonMarketplace;
use common\models\order\OrderPeriod;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Model\OrdersV0\OrdersList;
use yii\helpers\Json;

abstract class BaseLoadOrdersConsumer extends BaseConsumer
{
    use LogToConsoleTrait;

    protected Prometheus $prometheus;
    protected CustomerConfig $customerConfig;

    const MAX_ERRORS_COUNT = 10;

    abstract public function isMessageClassCorrect($message): bool;

    abstract public function createNewMessage($sellerId, $region): BaseLoadOrdersMessage;

    abstract public function getFirstOrderPeriod(): ?OrderPeriod;

    abstract public function loadFromMessageBody($body): BaseLoadOrdersMessage;

    public function __construct()
    {
        $this->prometheus = \Yii::$app->prometheus;
        $this->customerConfig = \Yii::$container->get("customerConfig");
    }

    public function __execute(AMQPMessage $msg): int
    {
        $orderPeriod = null;
        $message = null;
        try {
            $this->info($this->getName() . ': Started');

            $message = $this->loadFromMessageBody($msg->body);
            /** @var Seller $seller */
            $seller = Seller::find()->where(['id' => $message->getSellerId()])->one();

            if ($this->isMessageClassCorrect($message)) {
                $this->info($message->getSellerId() . ' ' . $message->getOrderPeriodId() . ' ' . $message->isFinished());

                /** @var DbManager $sellerDbManager */
                $sellerDbManager = \Yii::$app->get('dbManager');
                try{
                    $sellerDbManager->setSellerId($message->getSellerId());
                }catch (SellerNotFoundException $exception){
                    return ConsumerInterface::MSG_ACK;
                }
                /** @var OrderPeriod $orderPeriod */
                $orderPeriod = OrderPeriod::findOne($message->getOrderPeriodId());

                if (empty($orderPeriod)) {
                    $this->info("Order period ID {$message->getOrderPeriodId()} is not found");
                    return ConsumerInterface::MSG_ACK;
                }

                $orderPeriod->updated_at = date('Y-m-d H:i:s');
                $orderPeriod->save(false);

                if ($orderPeriod->isLoadingTerminated()) {
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'loading_terminated',
                        $orderPeriod->type
                    );

                    $this->info("Loading terminated");
                    return ConsumerInterface::MSG_ACK;
                }

                if (!$seller->canMakeRequestToAmazon()) {
                    $orderPeriod->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'terminated_seller_with_invalid_token',
                        $orderPeriod->type
                    );
                    $this->info("Terminated with invalid token");
                    return ConsumerInterface::MSG_ACK;
                }

                if ($message->getErrorsCount() > self::MAX_ERRORS_COUNT) {
                    $this->info("PERIOD {$orderPeriod->id} {$orderPeriod->start_date} - {$orderPeriod->finish_date} has been terminated doe to high amount of errors");
                    $orderPeriod->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'terminated_too_much_errors',
                        $orderPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_PROCESSING;
                $orderPeriod->saveOrThrowException();

                if ($message->getLastExecuteTime())
                    $this->info('Last execution time: ' . $message->getLastExecuteTime()->format('Y-m-d H:i:s'));
                $this->info($message->getSellerId() . ' ' . $message->getOrderPeriodId() . ' before wait');
                $message->wait();
                $this->info($message->getSellerId() . ' ' . $message->getOrderPeriodId() . ' after wait');

                try {
                    $message->setLastExecuteTime(new \DateTime());
                    $payload = $this->loadData($message);
                } catch (NextTokenExpiredException $e) {
                    $this->error($e);

                    $message->setNextToken(null);
                    $message->publish(2);
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'next_token_expired',
                        $orderPeriod->type
                    );
                    $orderPeriod->save(false);

                    return ConsumerInterface::MSG_ACK;
                }
                catch (AccessDeniedException | AccessTokenInvalidException | NoAccessTokenException $e) {
                    $this->error($e);
                    $seller->setIsTokenReceived(false);
                    $orderPeriod->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'terminated_token_is_not_valid_anymore',
                        $orderPeriod->type
                    );
                    return ConsumerInterface::MSG_ACK;
                }
                catch (DateRangeIsNotValidException $e) {
                    $this->error($e);
                    $orderPeriod->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'terminated_wrong_date_range',
                        $orderPeriod->type
                    );
                    return ConsumerInterface::MSG_ACK;
                }
                catch (ApiException $e) {
                    $this->error($e);
                    $message->increaseErrorsCount();
                    $message->publish();

                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'api_exception',
                        $orderPeriod->type
                    );
                    $orderPeriod->save(false);

                    return ConsumerInterface::MSG_ACK;
                }
                catch (\Throwable $e) {
                    $this->error($e);
                    $orderPeriod->markAsTerminated();

                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'error',
                        'exception',
                        $orderPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                if (!$payload) {
                    $this->info("PERIOD {$orderPeriod->id}. PERIOD {$orderPeriod->start_date} - {$orderPeriod->finish_date}. Empty payload");

                    if ($message->getNextToken()) {
                        $this->info("Next token is {$message->getNextToken()}");
                        $this->prometheus->incConsumerState(
                            'base_load_order',
                            'error',
                            'empty_payload',
                            $orderPeriod->type
                        );
                    } else {
                        $this->error("Next token is empty");
                        $this->prometheus->incConsumerState(
                            'base_load_order',
                            'error',
                            'empty_next_token',
                            $orderPeriod->type
                        );
                    }
                    $orderPeriod->save(false);

                    return ConsumerInterface::MSG_REQUEUE;
                }

                $useRealtimeOrderDataUpdate = $this->customerConfig
                    ->get(CustomerConfig::PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES);

                if ($useRealtimeOrderDataUpdate) {
                    $amazonOrders = [];
                    $converter = new OrderFromAmazonAPIToOurOrderConverter();

                    foreach ($payload->getOrders() as $orderFromAmazonAPI) {
                        $amazonOrders[] = $converter->convert($orderFromAmazonAPI, $seller->id, $orderPeriod->id);
                    }

                    $storeOrdersService = new StoreOrdersServiceV2($seller->id);
                    $storeOrdersService->save($amazonOrders);
                    unset($storeOrdersService);
                } else {
                    $manager = new StoreOrdersService($orderPeriod, $message->getSellerId());
                    $manager->saveData($payload);
                }

                $message->setNextToken($payload->getNextToken() ? $payload->getNextToken() : null);
                $message->resetErrorsCount();

                if (!$payload->getNextToken()) {
                    $this->info($this->getName() . ': Loading order data is finished for ' . json_encode([
                            'SellerId' => $message->getSellerId(),
                            'orderPeriodID' => $message->getOrderPeriodId()
                        ]));
                    $orderPeriod->markAsFinished();
                    $this->prometheus->incConsumerState(
                        'base_load_order',
                        'success',
                        'finished',
                        $orderPeriod->type
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $this->info(
                    $this->getName() . ': Data for orderPeriodID ' . $message->getOrderPeriodId()
                    . ' loaded. Next token '
                    . $payload->getNextToken()
                        ? $payload->getNextToken()
                        : 'is empty'
                );

                $message->publish();
                $this->prometheus->incConsumerState(
                    'base_load_order',
                    'success',
                    'published_with_next_token',
                    $orderPeriod->type
                );
            }
            $orderPeriod->save(false);

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->prometheus->incConsumerState(
                'base_load_order',
                'error',
                'exception_common_wrapper',
                $orderPeriod->type ?? null
            );
            $this->error($e);
            if (!empty($orderPeriod) && !is_null($message)) {
                $orderPeriod->markAsTerminated();
                $orderPeriod->save(false);
            }

            return ConsumerInterface::MSG_ACK;
        }
    }

    private function loadData(BaseLoadOrdersMessage $message): ?OrdersList
    {
        $apiInstance = new OrdersApi($message->getSellerId(), $message->getRegion());

        if (!$orderPeriod = OrderPeriod::findOne($message->getOrderPeriodId())) {
            throw new \Exception('Order period is not found');
        }

        $marketplaceIds = AmazonMarketplace::find()->where(['is_active' => true])->select(['id'])->column();

        $result = $apiInstance->getOrders(
            $marketplaceIds,
            null,
            null,
            DateHelper::getDateTimeInISO8601($orderPeriod->start_date),
            DateHelper::getDateTimeInISO8601($orderPeriod->finish_date),
            null,
            null,
            null,
            null,
            null,
            100,
            null,
            null,
            $message->getNextToken()

        );

        if (!empty($result->getErrors())) {
            $errors = Json::encode($result->getErrors());
            $this->error("PERIOD {$orderPeriod->id}. Response has errors: {$errors}");
        }
        return $result->getPayload();
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        $path = explode('\\', __CLASS__);
        return array_pop($path);
    }
}
