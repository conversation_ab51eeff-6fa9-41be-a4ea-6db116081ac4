<?php

namespace common\components\rabbitmq\consumers\order;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\message\order\LoadOrderItemsMessage;
use common\components\sellingApi\apiProxy\OrdersApi;
use common\components\sellingApi\exception\AccessDeniedException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\services\order\dataConverter\OrderItemFromAmazonAPIToOurOrderItemConverter;
use common\components\services\order\StoreOrderItemsService;
use common\components\services\order\StoreOrderItemsServiceV2;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\models\order\AmazonOrder;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Model\OrdersV0\OrderItemsList;
use yii\helpers\Json;

class LoadOrderItemsConsumer extends BaseConsumer
{
    public const MAX_INVOKES_BEFORE_RESTART = 100;

    protected Prometheus $prometheus;
    protected CustomerConfig $customerConfig;

    const MAX_ERRORS_COUNT = 10;

    public function isMessageClassCorrect($message): bool
    {
        return is_a($message, LoadOrderItemsMessage::class);
    }

    public function loadFromMessageBody($body): LoadOrderItemsMessage
    {
        return LoadOrderItemsMessage::getFromMessage($body);
    }

    public function createNewMessage($sellerId, $region): LoadOrderItemsMessage
    {
        return new LoadOrderItemsMessage($sellerId, $region, []);
    }

    public function __construct()
    {
        $this->prometheus = \Yii::$app->prometheus;
        $this->customerConfig = \Yii::$container->get("customerConfig");
    }

    public function __execute(AMQPMessage $msg): int
    {
        $message = null;

        try {
            $this->info($this->getName() . ': Started');

            $message = $this->loadFromMessageBody($msg->body);
            /** @var Seller $seller */
            $seller = Seller::find()->where(['id' => $message->getSellerId()])->cache(5 * 60)->one();

            if ($this->isMessageClassCorrect($message)) {
                $this->info($message->getSellerId() . ' ' . $message->getAmazonOrderId(). ' ' . $message->isFinished());

                /** @var DbManager $sellerDbManager */
                $sellerDbManager = \Yii::$app->get('dbManager');
                $sellerDbManager->setSellerId($message->getSellerId());
                /** @var AmazonOrder $amazonOrder */
                $amazonOrder = AmazonOrder::findOne(['amazon_order_id' => $message->getAmazonOrderId()]);

                if (empty($amazonOrder)) {
                    $this->info("Amazon order ID {$message->getAmazonOrderId()} is not found");
                    return ConsumerInterface::MSG_ACK;
                }

                if ($amazonOrder->isLoadingTerminated()) {
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'loading_terminated',
                    );

                    $this->info("Loading terminated");
                    return ConsumerInterface::MSG_ACK;
                }

                if (!$seller->canMakeRequestToAmazon()) {
                    $amazonOrder->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'terminated_seller_with_invalid_token',
                    );
                    $this->info("Terminated with invalid token");
                    return ConsumerInterface::MSG_ACK;
                }

                if ($message->getErrorsCount() > self::MAX_ERRORS_COUNT) {
                    $this->info("Loading items for Amazon order ID {$amazonOrder->id} has been terminated doe to high amount of errors");
                    $amazonOrder->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'terminated_too_much_errors',
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $amazonOrder->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_PROCESSING;
                $amazonOrder->saveOrThrowException();

                $message->wait();

                try {
                    $payload = $this->loadData($message);
                } catch (NextTokenExpiredException $e) {
                    $this->error($e);

                    $message->setNextToken(null);
                    $message->publish(2);
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'next_token_expired',
                    );
                    $amazonOrder->save(false);

                    return ConsumerInterface::MSG_ACK;
                }
                catch (AccessDeniedException | AccessTokenInvalidException | NoAccessTokenException $e) {
                    $seller->setIsTokenReceived(false);
                    $amazonOrder->markAsTerminated();
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'terminated_token_is_not_valid_anymore',
                    );
                    return ConsumerInterface::MSG_ACK;
                }
                catch (QuotaExceededException $e) {
                    $message->increaseErrorsCount();
                    $message->publish();

                    //generally there is one request per minute, if next message if for the same seller, better to sleep for one minute
                    sleep(60);

                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'quota_exception',
                    );

                    return ConsumerInterface::MSG_ACK;
                }
                catch (ApiException $e) {
                    $this->error($e);
                    $message->increaseErrorsCount();
                    $message->publish();

                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'api_exception',
                    );
                    $amazonOrder->save(false);

                    return ConsumerInterface::MSG_ACK;
                }
                catch (\Throwable $e) {
                    $this->error($e);
                    $amazonOrder->markAsTerminated();

                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'error',
                        'exception',
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                if (!$payload) {
                    $this->info("Amazon order ID {$amazonOrder->id}. Empty payload");

                    if ($message->getNextToken()) {
                        $this->info("Next token is {$message->getNextToken()}");
                        $this->prometheus->incConsumerState(
                            'load_order_items',
                            'error',
                            'empty_payload',
                        );
                    } else {
                        $this->error("Next token is empty");
                        $this->prometheus->incConsumerState(
                            'load_order_items',
                            'error',
                            'empty_next_token',
                        );
                    }
                    $amazonOrder->save(false);

                    return ConsumerInterface::MSG_REQUEUE;
                }

                $useRealtimeOrderDataUpdate = $this->customerConfig
                    ->get(CustomerConfig::PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES);

                if ($useRealtimeOrderDataUpdate) {
                    $orderItemsTheir = $payload->getOrderItems();
                    $amazonOrderAsArray = $amazonOrder->toArray();

                    $amazonOrderItems = [];
                    $dataConverter = new OrderItemFromAmazonAPIToOurOrderItemConverter();
                    foreach ($orderItemsTheir as $orderItem) {
                        $amazonOrderItems[] = $dataConverter->convert($orderItem, $amazonOrderAsArray);
                    }

                    $storeOrderItemsService = new StoreOrderItemsServiceV2($seller->id);
                    $storeOrderItemsService->save($amazonOrderItems, [$amazonOrderAsArray]);
                    unset($storeOrderItemsService);
                } else {
                    $manager = new StoreOrderItemsService($amazonOrder, $message->getSellerId());
                    $manager->saveData($payload);
                }

                $message->setNextToken($payload->getNextToken() ? $payload->getNextToken() : null);
                $message->setLastExecuteTime(new \DateTime());
                $message->resetErrorsCount();

                if (!$payload->getNextToken()) {
                    $this->info($this->getName() . ': Loading order items is finished for ' . json_encode([
                        'SellerId' => $message->getSellerId(),
                        'amazonOrderId' => $message->getAmazonOrderId()
                    ]));

                    $amazonOrder->markAsFinished();
                    $this->prometheus->incConsumerState(
                        'load_order_items',
                        'success',
                        'finished',
                    );

                    return ConsumerInterface::MSG_ACK;
                }

                $this->info(
                    $this->getName() . ': Items for amazonOrderID ' . $message->getAmazonOrderId()
                    . ' loaded. Next token '
                    . $payload->getNextToken()
                        ? $payload->getNextToken()
                        : 'is empty'
                );

                $message->publish();
                $this->prometheus->incConsumerState(
                    'load_order_items',
                    'success',
                    'published_with_next_token',
                );
            }

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->prometheus->incConsumerState(
                'load_order_items',
                'error',
                'exception_common_wrapper',
            );
            $this->error($e);
            if (!empty($amazonOrder) && !is_null($message)) {
                $amazonOrder->markAsTerminated();
                $amazonOrder->save(false);
            }

            return ConsumerInterface::MSG_ACK;
        }
    }

    private function loadData(LoadOrderItemsMessage $message): ?OrderItemsList
    {
        $apiInstance = new OrdersApi($message->getSellerId(), $message->getRegion());

        if (!$amazonOrder = AmazonOrder::findOne(['amazon_order_id' => $message->getAmazonOrderId()])) {
            throw new \Exception('Order is not found');
        }

        $result = $apiInstance->getOrderItems($message->getAmazonOrderId(), $message->getNextToken());

        if (!empty($result->getErrors())) {
            $errors = Json::encode($result->getErrors());
            $this->error("Amazon order ID {$amazonOrder->id}. Response has errors: {$errors}. Order Id {$message->getAmazonOrderId()}. Next token: {$message->getNextToken()}");
        }

        return $result->getPayload();
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        $path = explode('\\', __CLASS__);
        return array_pop($path);
    }
}
