<?php

namespace common\components\rabbitmq\consumers;

use common\components\amazonAds\CostsApplier;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class PpcCostConsumer extends BaseConsumer
{
    use LogToConsoleTrait;

    protected CostsApplier $costsApplier;

    protected MessagesSender $messagesSender;

    public function __construct()
    {
        $this->costsApplier = new CostsApplier();
    }

    /**
     * @param AMQPMessage $msg
     * @return int
     */
    public function __execute(AMQPMessage $msg): int
    {
        $this->info("Exporting ppc cost to clickhouse");
        $this->info($msg->body);
        $customerId = $msg->body['customerId'];
        $date = $msg->body['date'];

        if (empty($customerId)) {
            return ConsumerInterface::MSG_ACK;
        }

        $this->info(str_repeat('-', 50));
        $this->info('CustomerId: ' . $customerId . ' started');
        $this->info('Date: ' . $date);
        try {
            $this->costsApplier->apply($customerId, $date);
        } catch (\Throwable $e) {
            $this->error($e);
        }
        return ConsumerInterface::MSG_ACK;
    }
}
