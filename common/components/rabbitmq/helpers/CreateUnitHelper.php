<?php

namespace common\components\rabbitmq\helpers;

use <PERSON><PERSON>;
use yii\base\BaseObject;

class CreateUnitHelper extends BaseObject
{
    /** @var string */
    public $units_dir;
    /** @var string */
    public $user;
    /** @var string */
    public $group;
    /** @var string */
    public $work_dir;

    /** @var string */
    public $example = '[Unit]
Description=%description%
After=syslog.target
After=network.target

[Service]
Type=simple
WorkingDirectory=%work_dir%

User=%user%
Group=%group%

ExecStart=php /app/yii rabbitmq/consume %name_consumer% %memory_limit%
ExecReload=php /app/yii rabbitmq/restart-consume %name_consumer% %memory_limit%
TimeoutSec=10
RestartSec=5
Restart=always

[Install]
WantedBy=multi-user.target';

    /** @var string */
    public $bash = '#!/usr/bin/env bash
MASK=consumer*.service
if [ "$1" = "copy" ]; then
  find . -type f -name "$MASK" -exec cp {} /etc/systemd/system \\;
  systemctl daemon-reload
  find . -type f -name "$MASK" -exec sh -c \'systemctl enable "$(basename {})"\' \\;
  find . -type f -name "$MASK" -exec sh -c \'systemctl start "$(basename {})"\' \\;
fi
if [ "$1" = "start" ]; then
  find . -type f -name "$MASK" -exec sh -c \'systemctl start "$(basename {})"\' \\;
fi
if [ "$1" = "restart" ]; then
  find . -type f -name "$MASK" -exec sh -c \'systemctl restart "$(basename {})"\' \\;
fi
if [ "$1" = "status" ]; then
  find . -type f -name "$MASK" -exec sh -c \'systemctl status "$(basename {})"\' \\;
fi
if [ "$1" = "delete" ]; then
  find . -type f -name "$MASK" -exec sh -c \'systemctl disable "$(basename {})"\' \\;
  find /etc/systemd/system/ -type f -name "$MASK" -exec rm {} \\;
  find . -type f -name "$MASK" -exec rm {} \\;
  systemctl daemon-reload
fi';

    private $categorizedConsumers = [];

    public function init()
    {
        if ($this->units_dir && !is_dir($this->units_dir)) {
            mkdir($this->units_dir, 0777, true);
        }

        $consumers = Yii::$app->rabbitmq->consumers;
        foreach ($consumers as $consumer) {
            $category = $consumer['systemd']['category'] ?? 'default';
            $this->categorizedConsumers[$category][] = $consumer;
            if (!is_dir($this->units_dir . "/$category")) {
                mkdir($this->units_dir . "/$category");
            }
        }
    }

    public function create()
    {
        foreach ($this->categorizedConsumers as $category => $consumers) {
            $configs = $this->prepareConfigs($consumers);

            if (empty($configs)) {
                continue;
            }

            $unitsDir = $this->units_dir . "/$category";

            foreach ($configs as $item) {
                for ($i = 1; $i <= $item['workers']; $i++) {
                    $file = $unitsDir . '/consumer_' . $item['consumer'] . '_' . $i . '.service';
                    file_put_contents($file, $item['unit']);
                }
            }

            $bashFile = $unitsDir . '/exec.sh';
            file_put_contents($bashFile, $this->bash);
            chmod($bashFile, 0700);
        }
    }

    private function prepareConfigs(array $consumers): array
    {
        $result = [];
        foreach ($consumers as $consumer) {
            $description = 'Consumer ' . $consumer['name'];
            $memory_limit = $consumer['systemd']['memory_limit'] == 0 ? '' : '-l ' . $consumer['systemd']['memory_limit'];
            $workers = $consumer['systemd']['workers'];
            $unit = str_replace(
                [
                    '%description%',
                    '%work_dir%',
                    '%user%',
                    '%group%',
                    '%yii_path%',
                    '%name_consumer%',
                    '%memory_limit%'
                ],
                [
                    $description,
                    $this->work_dir,
                    $this->user,
                    $this->group,
                    $this->work_dir . '/yii',
                    $consumer['name'],
                    $memory_limit
                ],
                $this->example
            );
            $result[] = [
                'unit' => $unit,
                'workers' => $workers,
                'consumer' => $consumer['name']
            ];
        }
        return $result;
    }
}
