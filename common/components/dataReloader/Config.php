<?php

namespace common\components\dataReloader;

class Config
{
    public ?string $customerId = null;
    public ?string $sellerId = null;
    public bool $isResetPostgress = false;
    public bool $forceLoadToClickhouse = false;
    public bool $isRecreateClickhouseDatabases = false;
    public bool $isResetClickhouse = false;
    public ?string $eventPeriodExceptionMessageLike = null;
    public bool $isResetCOGData = false;
    public bool $isResetEmptyOrderPeriods = false;
    public bool $fixDuplicatesInEventPeriods = false;
    public bool $fixDuplicatesInOrdersPeriods = false;
    public bool $isRecreateClickhouseOrdersTables = false;
}