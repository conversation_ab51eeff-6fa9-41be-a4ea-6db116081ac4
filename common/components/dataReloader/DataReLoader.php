<?php

namespace common\components\dataReloader;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\core\db\DbPostfixManager;
use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\services\financialEvent\EventPeriodService;
use common\components\services\order\OrderPeriodService;
use common\components\services\order\TransferOrderService;
use common\models\Command;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\DbStructure;
use common\models\finance\EventPeriod;
use common\models\order\OrderPeriod;
use common\models\Seller;
use yii\caching\CacheInterface;
use yii\helpers\Console;

class DataReLoader
{
    use LogToConsoleTrait;

    private string $clickhouseClusterName;
    private DbManager $dbManager;
    private MessagesSender $messagesSender;
    private CacheInterface $cache;

    public function __construct(
        DbManager $dbManager,
        MessagesSender $messagesSender,
        CacheInterface $cache,
        string $clickhouseClusterName
    ) {
        $this->dbManager = $dbManager;
        $this->messagesSender = $messagesSender;
        $this->clickhouseClusterName = $clickhouseClusterName;
        $this->cache = $cache;
    }

    public function reload(Config $config): void
    {
        $sellersToReset = Seller::find()->orderBy('customer_id ASC');

        if (!empty($config->customerId)) {
            if (false !== strpos($config->customerId, '-')) {
                $fromIdToId = explode('-', $config->customerId);
                sort($fromIdToId);

                $sellersToReset->where([
                    'AND',
                    ['>=', 'customer_id', $fromIdToId[0]],
                    ['<=', 'customer_id', $fromIdToId[1]]
                ]);
            } else {
                $sellersToReset->where([
                    'customer_id' => explode(',', $config->customerId)
                ]);
            }
        } else if (!empty($config->sellerId)) {
            $sellersToReset->andWhere([
                'id' => explode(',', strtoupper($config->sellerId))
            ]);
        }

        $sellersToReset = $sellersToReset->all();
        $countAllSellers = count($sellersToReset);
        $alreadyResetCustomers = [];
        $alreadyRecreatedOrderCustomers = [];

        /** @var Seller $sellerToReset */
        foreach ($sellersToReset as $k => $sellerToReset) {
            try {
                $this->info(str_repeat('-', 50));
                $this->info(sprintf(
                    "Resetting seller %s of customer %s (%s from %s sellers in total)",
                    $sellerToReset->id,
                    $sellerToReset->customer_id,
                    $k + 1,
                    $countAllSellers,
                ));

                $this->dbManager->setSellerId($sellerToReset->id);

                if ($config->isResetPostgress) {
                    $this->truncatePostgresData();
                    $sellerToReset->is_init_periods_created = false;
                    $sellerToReset->is_init_periods_loaded = false;
                    $sellerToReset->save(false);
                }else{
                    if ($config->fixDuplicatesInEventPeriods) {
                        $this->fixDuplicatesInEventPeriods($sellerToReset);
                    }
                    if ($config->fixDuplicatesInOrdersPeriods) {
                        $this->fixDuplicatesInOrdersPeriods($sellerToReset);
                    }
                }

                if ($config->isResetCOGData) {
                    $this->truncateCOGData();
                }

                if ($config->isRecreateClickhouseDatabases && !in_array($sellerToReset->customer_id, $alreadyResetCustomers)) {
                    $this->recreateClickhouseDatabases($sellerToReset);
                    $alreadyResetCustomers[] = $sellerToReset->customer_id;
                }

                if ($config->isRecreateClickhouseOrdersTables && !in_array($sellerToReset->customer_id, $alreadyRecreatedOrderCustomers)) {
                    $this->recreateClickhouseOrdersTables();
                    $alreadyRecreatedOrderCustomers[] = $sellerToReset->customer_id;
                }

                if ($config->isResetClickhouse) {
                    $this->truncateRefundTransactionsWithoutCOG();
                    if (!$config->isRecreateClickhouseDatabases)
                        $this->truncateClickhouse();

                    if (!$config->isResetPostgress) {
                        if ($config->forceLoadToClickhouse)
                            $this->reExportFinishedToClickhouse($sellerToReset->id, $config->eventPeriodExceptionMessageLike);
                        else
                            $this->resetClickhouseStatusForPeriods();
                    }
                }elseif ($config->forceLoadToClickhouse){
                    $this->reExportFinishedToClickhouse($sellerToReset->id, $config->eventPeriodExceptionMessageLike, true);
                }

                if ($config->isResetEmptyOrderPeriods) {
                    $this->deleteEmptyOrdersPeriods();
                    $sellerToReset->is_order_init_periods_created = false;
                    $sellerToReset->save(false);
                }

            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function recreateClickhouseDatabases(Seller $seller)
    {
        static $customersProcessed = [];
        $customerId = $this->dbManager->getCustomerId();

        if (in_array($customerId, $customersProcessed)) {
            return;
        }

        $this->info(str_repeat('-', 15));
        $this->info('Recreate clickhouse databases');

        $clickhouseDb = $this->dbManager->getDb('system', HelperFactory::TYPE_CLICKHOUSE);
        $clusterPart = "";
        if (YII_ENV !== 'local') {
            $clusterName = getenv('CLICKHOUSE_CLUSTER_NAME');
            $clusterPart = "ON CLUSTER $clusterName";
        }

        $dbPostfixManager = new DbPostfixManager($seller->id, $seller->customer_id);
        $dbName = 'customer' . '_' . $dbPostfixManager->getDbPostfixForCustomerRelatedDbs();
        $sql = "DROP DATABASE IF EXISTS {$dbName} {$clusterPart} NO DELAY";
        $this->info($sql);
        $clickhouseDb->createCommand($sql)->execute();

        $sql = "CREATE DATABASE {$dbName} {$clusterPart}";
        $this->info($sql);
        $clickhouseDb
            ->createCommand($sql)
            ->execute();
        $customersProcessed[] = $customerId;
        DbStructure::getDb()->createCommand()->delete(DbStructure::tableName(), [
            'customer_id' => $customerId
        ])->execute();

        LinuxCommander::safeExecute(
            sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
            "migrate-clickhouse-customer 0 {$customerId} --interactive=0",
            true
        );

        $this->info('Finished');
    }

    public function resetClickhouseStatusForPeriods(): void
    {
        $this->info(str_repeat('-', 15));

        $this->info('Reset clickhouse status for all periods');
        EventPeriod::updateAll([
            'clickhouse_exception' => null,
            'clickhouse_status' => EventPeriod::CLICKHOUSE_STATUS_NEW,
            'clickhouse_queued_at' => null,
            'clickhouse_moved_at' => null,
        ]);
    }

    /**
     *
     * all operations in this function will be done through cron
     * @param string $sellerId
     * @param string|null $exceptionMessageLike
     * @param bool $onlyInNewStatus
     */
    public function reExportFinishedToClickhouse(string $sellerId, string $exceptionMessageLike = null, $onlyInNewStatus = false): void
    {
        $this->info(str_repeat('-', 15));
        $this->info('Re export finished event periods to clickhouse started');
        $eventPeriodQuery = EventPeriod::find();
        $eventPeriodQuery
            ->andWhere(['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])
//            ->andWhere(['=', 'has_transactions', 't'])
        ;

        if ($onlyInNewStatus){
            $eventPeriodQuery->andWhere(['clickhouse_status' => EventPeriod::CLICKHOUSE_STATUS_NEW]);
        }

        if (!empty($exceptionMessageLike)) {
            $eventPeriodQuery->andWhere(['like', 'clickhouse_exception', "{$exceptionMessageLike}"]);
            $this->info("Using LIKE pattern '%$exceptionMessageLike%'");
        }

        $eventPeriodQuery->orderBy('finish_date DESC');

        $this->info('Processing events');
        $countAllEventPeriods = $eventPeriodQuery->count();
        $countProcessedEventPeriods = 0;
        Console::startProgress(0, $countAllEventPeriods, 'Event periods');

        foreach ($eventPeriodQuery->batch() as $eventPeriods) {
            foreach ($eventPeriods as $eventPeriod) {
                $this->messagesSender->eventPeriodsFromCacheToClickhouse($eventPeriod, $sellerId);
                $countProcessedEventPeriods++;
                Console::updateProgress($countProcessedEventPeriods, $countAllEventPeriods, 'Event periods');
            }
        }
        $this->info('Re export finished event periods to clickhouse finished');
    }

    protected function truncateCOGData()
    {
        static $customersProcessed = [];
        $customerId = $this->dbManager->getCustomerId();

        if (in_array($customerId, $customersProcessed)) {
            return;
        }

        $this->info(str_repeat('-', 15));
        $this->info('Truncate COG data started');
        $customerDb = $this->dbManager->getDb('customer');

        $tablesToTruncate = [
            ProductCostPeriod::tableName(),
            ProductCostItem::tableName(),
        ];

        foreach ($tablesToTruncate as $tableName) {
            $this->info("Truncate customer COG table $tableName");
            $customerDb->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
        }

        Product::updateAll([
            'repricer_id' => null,
            'vat' => null,
            'shipping_cost' => null,
            'other_fees' => null,
            'buying_price' => null,
            'source' => ProductCostCategory::SOURCE_MANUAL
        ]);

        ProductCostItem::invalidateCachedItemsForCustomer();
        $this->info('Truncate COG data finished');

        $this->info('Sending re-sync COG data from repricer into queue');
        Command::create("product-cost/sync {$customerId} --isFirstSync=1 --shouldSendCOGChanges=0");
        $customersProcessed[] = $customerId;
    }

    protected function truncatePostgresData()
    {
        $this->info(str_repeat('-', 15));
        $this->info('Truncate postgres data started');
        $financeDb = $this->dbManager->getDb('finance');

        $financeTablesToTruncate = EventPeriod::getEventTables();
        $financeTablesToTruncate[] = EventPeriod::tableName();

        foreach ($financeTablesToTruncate as $tableName) {
            $this->info("Truncate finance table $tableName");
            $financeDb->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
        }
        $this->info('Truncate postgres data finished');
    }

    protected function deleteEmptyOrdersPeriods()
    {
        $this->info(str_repeat('-', 15));
        $this->info('Delete empty order periods started');
        $tableName = OrderPeriod::tableName();

        $startDate = OrderPeriod::getDb()->createCommand("select start_date from {$tableName} where type = 'INIT' and loading_status = 'NEW' order by start_date desc limit 1")->queryScalar();

        if ($startDate) {
            OrderPeriod::getDb()->createCommand("DELETE FROM {$tableName} where start_date <= :startDate",
                [':startDate' => $startDate])->execute();
        }
        $this->info('Delete empty order periods finished');
    }

    protected function truncateClickhouse()
    {
        static $customersProcessed = [];

        $customerId = $this->dbManager->getCustomerId();
        if (in_array($customerId, $customersProcessed)) {
            return;
        }

        $this->info(str_repeat('-', 15));
        $this->info('Truncate clickhouse data started');

        $this->waitForBufferFlush($customerId);

        $clickhouseDb = $this->dbManager->getClickhouseCustomerDb();
        $clickhouseTablesToTruncate = [Transaction::tableName()];
        $clusterName = $this->clickhouseClusterName;
        $clusterPart = "";

        if (YII_ENV !== 'local') {
            $clusterPart = "ON CLUSTER $clusterName";
        }

        foreach ($clickhouseTablesToTruncate as $tableName) {
            $clickhouseDb->createCommand("TRUNCATE TABLE {$tableName} {$clusterPart}")->execute();
        }
        ProductCostItem::invalidateCachedItemsForCustomer();
        $clickhouseDb->close();
        $customersProcessed[] = $customerId;
        $this->info('Truncate clickhouse data finished');
    }

    protected function truncateRefundTransactionsWithoutCOG()
    {
        static $customersProcessed = [];
        $customerId = $this->dbManager->getCustomerId();

        if (in_array($customerId, $customersProcessed)) {
            return;
        }

        $this->info(str_repeat('-', 15));
        $tableName = RefundTransactionWithoutProductCost::tableName();
        $this->info("Truncate postgres customer table {$tableName}");
        $customerDb = $this->dbManager->getCustomerDb();
        $customerDb->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
        $this->info('Finished');

        $customersProcessed[] = $customerId;
    }

    protected function waitForBufferFlush(int $customerId): void
    {
        for ($i = 0; $i < 20; $i++) {
            if (!$this->cache->get("has_data_in_buffer_{$customerId}")) {
                $this->info('No buffered data left, can proceed forward');
                break;
            }
            $this->info('There are some data left in buffer, waiting while it will be flushed');
            sleep(1);
        }
    }

    protected function fixDuplicatesInEventPeriods(Seller $seller)
    {
        $this->info(str_repeat('-', 15));
        $tableName = EventPeriod::tableName();

        $this->info('Fix duplicates on event periods');

        EventPeriod::getDb()->createCommand(
            "delete from {$tableName} where id in (select t1.id
            from {$tableName} t1
            join {$tableName} t2 on t1.id <> t2.id
            where t1.start_date <= t2.finish_date and t1.finish_date >= t2.start_date );")
        ->execute();

        $emptyPeriods = EventPeriod::getDb()->createCommand(
            "select t1.finish_date as date_from, (select min(start_date) from {$tableName} t2 where t2.start_date>t1.finish_date) as date_to
                from {$tableName} t1
                where EXTRACT(EPOCH FROM ((select min(start_date) from {$tableName} t2 where t2.start_date>t1.finish_date ) - t1.finish_date))>1
                order by t1.finish_date")
        ->queryAll();

        $this->info(sprintf('Found %s periods', count($emptyPeriods)));

        foreach ($emptyPeriods as $emptyPeriod) {
            $from = new \DateTime($emptyPeriod['date_from']);
            $from->add(new \DateInterval('PT1S'));
            (new EventPeriodService($seller))->generateCustomPeriods($from, new \DateTime($emptyPeriod['date_to']));
        }
    }

    protected function fixDuplicatesInOrdersPeriods(Seller $seller)
    {
        $this->info(str_repeat('-', 15));
        $tableName = OrderPeriod::tableName();

        $this->info('Fix duplicates on order periods');
        OrderPeriod::getDb()->createCommand(
            "delete from {$tableName} where id in (select t1.id
            from {$tableName} t1
            join {$tableName} t2 on t1.id <> t2.id
            where t1.start_date <= t2.finish_date and t1.finish_date >= t2.start_date );")
        ->execute();

        $emptyPeriods = OrderPeriod::getDb()->createCommand(
            "select t1.finish_date as date_from, (select min(start_date) from {$tableName} t2 where t2.start_date>t1.finish_date) as date_to
                from {$tableName} t1
                where EXTRACT(EPOCH FROM ((select min(start_date) from {$tableName} t2 where t2.start_date>t1.finish_date ) - t1.finish_date))>1
                order by t1.finish_date")
        ->queryAll();

        $this->info(sprintf('Found %s periods', count($emptyPeriods)));

        foreach ($emptyPeriods as $emptyPeriod) {
            $from = new \DateTime($emptyPeriod['date_from']);
            $from->add(new \DateInterval('PT1S'));
            (new OrderPeriodService($seller))->generateCustomPeriods($from, new \DateTime($emptyPeriod['date_to']));
        }
    }

    private function recreateClickhouseOrdersTables()
    {
        static $recreatedForCustomer = [];
        $customerId = $this->dbManager->getCustomerId();

        if (in_array($customerId, $recreatedForCustomer)) {
            return;
        }

        (new TransferOrderService($customerId))->reinit();

        $recreatedForCustomer[] = $customerId;
    }
}
