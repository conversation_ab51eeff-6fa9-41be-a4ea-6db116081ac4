<?php

namespace common\components\transactionBuffer;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\TransactionBuffer;
use yii\caching\CacheInterface;
use yii\db\Expression;
use yii\mutex\Mutex;

class TransactionBufferFlusher
{
    use LogToConsoleTrait;
    protected DbManager $dbManager;
    protected ProcessManager $processManager;
    protected Mutex $mutex;
    protected CacheInterface $cache;

    protected const MAX_RECORDS_PER_INSERT = 500000;

    /**
     * Prevent too long flushing process on single customer.
     */
    protected const MAX_FLUSHES_PER_CALL = 1;

    public function __construct()
    {
        $this->mutex = \Yii::$app->mutex;
        $this->dbManager = \Yii::$app->dbManager;
        $this->processManager = \Yii::$app->processManager;
        $this->cache = \Yii::$app->cache;
    }

    public function flush(int $customerId)
    {
        $this->info("Started flushing process for customer $customerId");
        $this->dbManager->setCustomerId($customerId);

        if (!$this->dbManager->isActive() && rand(0, 100) >= 30) {
            return;
        }

        $cacheKey = 'flushing_transaction_buffer_' . $customerId;

        if ($this->cache->get($cacheKey)) {
            $this->info("Another flushing process is already running for customer $customerId");
            return;
        }

        $this->processManager->register($cacheKey);
        $this->cache->set($cacheKey, true, 60 * 10);
        $prevVersion = null;
        try {
            for ($i = 0; $i < self::MAX_FLUSHES_PER_CALL; $i++) {
                if (!$this->hasDataToMove()) {
                    $this->info("Finished, no data left for moving to clickhouse");
                    break;
                }
                $this->info("Flushing iteration $i started");

                if (null === $prevVersion) {
                    $prevVersion = TransactionBuffer::find()
                        ->select('max(xmin_copy)')
                        ->scalar() ?? time();
                }
                $nextVersion = max(time(), $prevVersion + 1);
                $this->info([
                    'prevMovedVersion' => $prevVersion,
                    'nextVersionWillBeMoved' => $nextVersion
                ]);

                $this->fillVersionForDataWillBeMoved($nextVersion);
                $this->moveBufferedDataToClickhouse($nextVersion);
                $prevVersion = $nextVersion;
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->cache->delete($cacheKey);
        $this->processManager->release($cacheKey);
    }

    protected function fillVersionForDataWillBeMoved(int $version): void
    {
        $this->info("Filling version $version for data which will be moved");
        $tableName = TransactionBuffer::tableName();
        $transaction = TransactionBuffer::getDb()->beginTransaction();
        TransactionBuffer::getDb()->createCommand("
                UPDATE $tableName
                SET xmin_copy = $version
                WHERE id IN (
                    SELECT id
                    FROM $tableName
                    WHERE xmin_copy IS NULL
                    ORDER BY \"PostedDate\" DESC
                    LIMIT " . self::MAX_RECORDS_PER_INSERT . "
                )
            ")->execute();
        $transaction->commit();
        $this->info("Sleeping for 5 seconds to complete update");
        sleep(5);
        $this->info("Version $version filled");
    }

    protected function hasDataToMove(): bool
    {
        $this->info("Checking if there are data to move");
        $dataToMove = TransactionBuffer::find()->where([
            'is', 'xmin_copy', new Expression('NULL')
        ])->limit(1)->one();
        return !empty($dataToMove);
    }

    protected function moveBufferedDataToClickhouse(int $nextVersion)
    {
        $this->info("Moving data with version $nextVersion to clickhouse");
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();
        list($schemaName, $bufferTableName) =  explode('.', TransactionBuffer::tableName());
        $toTableName = Transaction::tableName();

        $config = $this->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbPort'];
        $dbHost = $config['dbHost'];
        $dbUsername = $config['dbUser'];
        $dbPassword = $config['dbPassword'];
        $profitDashDbName = $config['profitDashDbName'];

        $sql = sprintf("
                INSERT INTO $toTableName
                SELECT
                    PostedDate,
                    COALESCE(SellerId, ''),
                    COALESCE(MarketplaceId, ''),
                    COALESCE(CategoryId, 0),
                    COALESCE(EventPeriodId, 0),
                    COALESCE(SellerSKU, ''),
                    COALESCE(ASIN, ''),
                    COALESCE(SellerOrderId, ''),
                    COALESCE(AmazonOrderId, ''),
                    COALESCE(Quantity, 0),
                    COALESCE(Amount, 0),
                    COALESCE(Currency, ''),
                    COALESCE(AmountEUR, 0),
                    COALESCE(MergeCounter, 1),
                    COALESCE(COGCategoryId, 0),
                    CreatedAt,
                    TransactionDate,
                    COALESCE(IndirectCostId, 0),
                    COALESCE(IndirectCostTypeId, 0)
                FROM postgresql(
                    '%s:%s',
                    '%s',
                    '%s',
                    '%s',
                    '%s',
                    '%s'
                )
                WHERE xmin_copy = '%d'
                SETTINGS max_partitions_per_insert_block = 10000
            ",
            $dbHost,
            $dbPort,
            $profitDashDbName,
            $bufferTableName,
            $dbUsername,
            $dbPassword,
            $schemaName,
            $nextVersion
        );

        $clickhouseCustomerDb
            ->createCommand($sql)
            ->execute();
        $this->info("Data with version $nextVersion moved to clickhouse successfully");
    }

    protected function fixIdSeqIfNeed(): void
    {
        $seqTableNam = TransactionBuffer::tableName() . '_id_seq';

        try {
            $lastSeqVal = TransactionBuffer::getDb()
                ->createCommand("SELECT last_value FROM $seqTableNam")
                ->queryScalar();
        } catch (\Throwable $e) {
            if (false !== strpos($e->getMessage(), 'does not exist')) {
                $lastSeqVal = 0;
            } else {
                throw $e;
            }
        }

        if ($lastSeqVal > 500000000) {
            TransactionBuffer::getDb()->createCommand("SELECT setval('$seqTableNam', 1)")->execute();
        }
    }
}
