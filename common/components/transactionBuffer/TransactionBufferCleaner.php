<?php

namespace common\components\transactionBuffer;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\models\customer\TransactionBuffer;

class TransactionBufferCleaner
{
    use LogToConsoleTrait;
    protected DbManager $dbManager;
    protected CustomerConfig $customerConfig;
    protected CustomerComponent $customerComponent;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->customerComponent = new CustomerComponent();
    }

    public function clean(int $customerId)
    {
        $this->info("Started cleaning process for customer $customerId");
        $this->dbManager->setCustomerId($customerId);

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen');
            return;
        }

        try {
            $maxVersion = TransactionBuffer::find()
                ->select('max(xmin_copy)')
                ->scalar() ?? 0;

            if (empty($maxVersion)) {
                $this->info("All data cleaned");
                return;
            }

            $versionsToClean = strtotime('-1 hour');
            $this->info([
                'maxVersion' => $maxVersion,
                'versionsToClean' => $versionsToClean
            ]);

            $this->info("Clearing version <= $versionsToClean after exception");
            $transaction = TransactionBuffer::getDb()->beginTransaction();
            TransactionBuffer::deleteAll(['<=', 'xmin_copy', (string)$versionsToClean]);
            $transaction->commit();
            $this->info("Versions <= $versionsToClean cleared");
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}