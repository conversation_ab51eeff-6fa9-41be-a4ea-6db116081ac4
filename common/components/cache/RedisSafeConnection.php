<?php

namespace common\components\cache;

use common\components\LogToConsoleTrait;
use yii\redis\SocketException;

class RedisSafeConnection extends \yii\redis\Connection
{
    use LogToConsoleTrait;

    public function executeCommand($name, $params = [])
    {
        try {
            return parent::executeCommand($name, $params);
        } catch (SocketException|\Exception $e) {
            if ("CLUSTER INFO" === $name) {
                throw $e;
            }

            $this->error($e);

            return null;
        }
    }
}
