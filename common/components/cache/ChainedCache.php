<?php

namespace common\components\cache;

use yii\caching\Cache;

class ChainedCache extends Cache
{
    /** @var Cache[] $caches */
    public array $caches = [];

    public function init()
    {
        parent::init();
        foreach ($this->caches as $key => $config) {
            if (is_array($config)) {
                $this->caches[$key] = \Yii::createObject($config);
            } else {
                $this->caches[$key] = \Yii::$app->{$config};
            }
        }
    }

    public function get($key)
    {
        $value = false;
        foreach ($this->caches as $i => $cache) {
            $value = $cache->get($key);
            if (false === $value) {
                continue;
            }

            // Setting found value for all top level caches
            for ($j = 0; $j < $i; $j++) {
                $this->caches[$j]->set($key, $value);
            }
            break;
        }
        return $value;
    }

    public function getValue($key)
    {
        $value = false;
        foreach ($this->caches as $i => $cache) {
            $value = $cache->getValue($key);
            if (false !== $value) {
                break;
            }
        }
        return $value;
    }

    public function flushValues()
    {
        foreach ($this->caches as $cache) {
            $cache->flushValues();
        }
    }

    public function addValue($key, $value, $duration)
    {
        foreach ($this->caches as $cache) {
            $cache->addValue($key, $value, $duration);
        }
    }

    public function setValue($key, $value, $duration = 0, $dependency = null)
    {
        foreach ($this->caches as $cache) {
            $cache->setValue($key, $value, $duration, $dependency);
        }
    }

    public function deleteValue($key)
    {
        foreach ($this->caches as $cache) {
            $cache->deleteValue($key);
        }
    }
}