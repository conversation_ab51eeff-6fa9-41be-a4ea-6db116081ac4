<?php

namespace common\components\treeStructureHelper\breakdown;

class BreakdownBuilder
{
    public function build(array $structure, BreakdownConfig $config)
    {
        $structure = $this->walkRecursivelyAndCorrect(
            $structure,
            $config
        );

        return $structure;
    }

    protected function walkRecursivelyAndCorrect(array $structure, BreakdownConfig $config): array
    {
        $result = [];

        foreach ($structure as $element) {
            if (in_array($element['id'], $config->skipIds)) {
                continue;
            }

            if ($config->isExpensive && $element['amount'] > 0) {
                continue;
            }

            $tags = $element['tags'] ?? [];

            if (!empty($config->onlyTagIds) && empty(array_intersect($tags, $config->onlyTagIds))) {
                continue;
            }

            if (!empty($config->skipTagIds) && !empty(array_intersect($tags, $config->skipTagIds))) {
                continue;
            }

            if ($element['depth'] <= $config->maxDepth) {
                $children = $this->walkRecursivelyAndCorrect($element['children'], $config);
                $element['children'] = $children;

                if (count($children) === 0) {
                    $element['hasChildren'] = false;
                } else {
                    $element['hasChildren'] = true;
                    $element['amount'] = $element['amount_own'] ?? 0;

                    foreach ($children as $child) {
                        $element['amount'] += $child['amount'];
                    }
                }
                $element['amount'] = round_half_even($element['amount'], 2);
                $result[] = $element;
                continue;
            }

            if (!$config->onlyLastElements) {
                $element['children'] = [];
                $element['hasChildren'] = false;
                $result[] = $element;
                continue;
            }
            $result = array_merge($result, $this->findDeepestElements([$element]));
        }

        if ($config->skipEmptyAmounts) {
            foreach ($result as $k => $element) {
                if ($element['count_transactions'] != 0) {
                    continue;
                }
                if ($element['depth'] <= $config->allowEmptyAmountsOnDepth) {
                    continue;
                }

                unset($result[$k]);
            }
        }

        return array_values($result);
    }

    protected function findDeepestElements(array $treeStructure): array
    {
        if (empty($treeStructure)) {
            return [];
        }

        $result = [];
        foreach ($treeStructure as $element) {
            if (count($element['children']) === 0 && $element['count_transactions'] > 0) {
                $result[] = $element;
            } else {
                $result = array_merge(
                    $result,
                    $this->findDeepestElements($element['children'])
                );
            }
        }

        if (count($result) === 0 && count($treeStructure) === 1) {
            $element = $treeStructure[array_keys($treeStructure)[0]];
            $element['children'] = [];
            $element['hasChildren'] = false;

            if ($element['count_transactions'] > 0) {
                $result[] = $element;
            }
        }

        if (count($treeStructure) === 1) {
            $element = $treeStructure[array_keys($treeStructure)[0]];
            $resultAmount = 0;
            foreach ($result as $k => $v) {
                $resultAmount += $v['amount'];
            }

            $amountDiff = $element['amount'] - $resultAmount;

            if ($amountDiff != 0) {
                $element['children'] = [];
                $element['hasChildren'] = false;
                $element['amount'] = $amountDiff;
                array_unshift($result, $element);
            }
        }

        return $result;
    }
}
