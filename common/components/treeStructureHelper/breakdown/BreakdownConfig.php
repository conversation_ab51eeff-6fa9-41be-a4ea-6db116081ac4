<?php

namespace common\components\treeStructureHelper\breakdown;

class BreakdownConfig
{
    public int $maxDepth = 1;
    public int $allowEmptyAmountsOnDepth = 1;
    public bool $skipEmptyAmounts = false;
    public bool $onlyLastElements = false;
    public array $skipIds = [];
    public array $skipTagIds = [];
    public bool $isExpensive = false;
    public array $onlyTagIds = [];
    public bool $includeTotal = false;
    public bool $includeEstimatedProfit = false;
    public bool $includeMargin = false;
    public bool $includeROI= false;
}
