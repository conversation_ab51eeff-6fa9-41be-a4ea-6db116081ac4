<?php

namespace common\components\treeStructureHelper;

class TreeStructureHelper
{
    /**
     * Used as a separator for path components.
     */
    public const PATH_SEPARATOR = '|';

    /**
     * Convert tree structure into flat format which can be saved somewhere.
     * @example
     * [
     *  'element_id_1' => [
     *    'parentId' => null,
     *    'depth' => 0,
     *    'sort_order' => 1,
     *    'path' => 'element_id_1'
     *  ]
     *  'element_id2' => [
     *    'parentId' => 'element_id_1',
     *    'depth' => 1,
     *    'sort_order' => 2,
     *    'path' => 'element_id_1|element_id_2'
     *  ]
     *  ... => ...
     * ]
     *
     * @param array $categoriesTree
     * @param string $parentId
     * @param string $depth
     * @param string $path
     *
     * @return array
     */
    public function convertTreeToFlatFormat(array $treeStructure, string $parentId = null, $pathParts = []): array
    {
        $mapping = [];

        foreach ($treeStructure as $k => $v) {
            $itemId = is_string($v) ? $v : $k;
            $children = is_array($v) ? $v : [];
            $thisPathParts = array_merge($pathParts, [$itemId]);
            $mapping[$itemId]['parent_id'] = $parentId;
            $mapping[$itemId]['path'] = implode(self::PATH_SEPARATOR, $thisPathParts);
            $mapping[$itemId]['depth'] = count($thisPathParts) - 1;

            if (count($children) > 0) {
                $mapping = array_merge($mapping, $this->convertTreeToFlatFormat($children, $itemId, $thisPathParts));
            }
        }

        return $mapping;
    }

    /**
     * Converts flat tree into tree structure.
     *
     * @param array $flatTree
     * @return array
     */
    public function convertFlatFormatToTree(array $flatTree, int $maxDepth = null): array
    {
        $treeStructure = [];

        foreach ($flatTree as $k => $v) {
            if (is_string($k)) {
                continue;
            }

            $flatTree[$v['id']] = $v;
            unset($flatTree[$k]);
        }

        foreach ($flatTree as $treeRoot) {

            $pathParts = explode("|", $treeRoot['path']);

            $isCollapsed = false;
            if (null !== $maxDepth && count($pathParts) - 1 > $maxDepth) {
                $isCollapsed = true;
                $pathParts = array_slice($pathParts, 0, $maxDepth + 1);
            }

            $treeLink = &$treeStructure;
            $lastKey = count($pathParts) - 1;

            foreach ($pathParts as $k => $pathPart) {
                if (!isset($treeLink[$pathPart])) {
                    $treeLink[$pathPart] = $flatTree[$pathPart];
                    $treeLink[$pathPart]['children'] = [];
                    unset($treeLink[$pathPart]['path']);
                    unset($treeLink[$pathPart]['parent_id']);
                }
                $treeLink[$pathPart]['hasChildren'] = $k === $lastKey ? $isCollapsed : true;
                $treeLink = &$treeLink[$pathPart]['children'];
            }
        }

        $this->sortStructure($treeStructure);

        return $treeStructure;
    }

    private function sortStructure(array &$treeStructure)
    {
        uasort($treeStructure, function ($a, $b) {
            return ($a['sort_order'] ?? 999) <=> ($b['sort_order'] ?? 999);
        });

        foreach ($treeStructure as &$item) {
            if (!empty($item['children'])) {
               $this->sortStructure($item['children']);
            }
            unset($item['sort_order']);
        }
    }
}
