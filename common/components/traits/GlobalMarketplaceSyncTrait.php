<?php

namespace common\components\traits;

use common\models\customer\Product;
use common\models\Seller;

/**
 * Trait for handling global marketplace synchronization logic
 */
trait GlobalMarketplaceSyncTrait
{
    /**
     * Check if product can be managed when global marketplace sync is enabled
     * 
     * @param Product|null $product
     * @throws \Exception
     */
    protected function checkIsAllowManageProductOrThrowException(Product $product = null): void
    {
        if (empty($product)) {
            return;
        }

        $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);

        if (!empty($globalMarketplaceId)
            && $product->marketplace_id !== $globalMarketplaceId
            && $product->is_enabled_sync_with_global_marketplace
        ) {
            throw new \Exception(
                \Yii::t('admin', "Only periods for global marketplace can be modified when synchronization with global marketplace enabled.")
            );
        }
    }
    
    /**
     * Check if product should be skipped due to global marketplace sync settings
     * 
     * @param Product|null $product
     * @return bool
     */
    protected function shouldSkipDueToGlobalMarketplaceSync(Product $product = null): bool
    {
        if (empty($product)) {
            return false;
        }
        
        $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);
        
        return !empty($globalMarketplaceId) 
            && $product->marketplace_id !== $globalMarketplaceId
            && $product->is_enabled_sync_with_global_marketplace;
    }
    
    /**
     * Get product by cost period ID
     * 
     * @param int $productCostPeriodId
     * @return Product|null
     */
    protected function getProductByProductCostPeriodId(int $productCostPeriodId): ?Product
    {
        $productCostPeriod = \common\models\customer\ProductCostPeriod::findOne($productCostPeriodId);
        
        if (empty($productCostPeriod)) {
            return null;
        }
        
        return Product::find()->where([
            'marketplace_id' => $productCostPeriod->marketplace_id,
            'seller_id' => $productCostPeriod->seller_id,
            'sku' => $productCostPeriod->seller_sku
        ])->one();
    }
    
    /**
     * Get product by marketplace, seller and SKU
     * 
     * @param string $marketplaceId
     * @param string $sellerId
     * @param string $sku
     * @return Product|null
     */
    protected function getProductByIdentifiers(string $marketplaceId, string $sellerId, string $sku): ?Product
    {
        return Product::find()->where([
            'marketplace_id' => $marketplaceId,
            'seller_id' => $sellerId,
            'sku' => $sku
        ])->one();
    }
    
    /**
     * Filter array of product data by global marketplace sync settings
     * 
     * @param array $productInfos
     * @return array
     */
    protected function filterProductsByGlobalMarketplaceSync(array $productInfos): array
    {
        $filtered = [];
        
        foreach ($productInfos as $productInfo) {
            $product = $this->getProductByIdentifiers(
                $productInfo['marketplace_id'] ?? $productInfo['marketplace'] ?? '',
                $productInfo['seller_id'] ?? '',
                $productInfo['sku'] ?? $productInfo['item_sku'] ?? ''
            );
            
            if (!$this->shouldSkipDueToGlobalMarketplaceSync($product)) {
                $filtered[] = $productInfo;
            }
        }
        
        return $filtered;
    }
    
    /**
     * Log info about skipped product due to global marketplace sync
     * 
     * @param Product $product
     * @param string $operation
     */
    protected function logSkippedProduct(Product $product, string $operation = 'operation'): void
    {
        if (method_exists($this, 'info')) {
            $this->info("Skipped {$operation} for product {$product->sku} (marketplace: {$product->marketplace_id}) due to global marketplace sync settings");
        }
    }
}
