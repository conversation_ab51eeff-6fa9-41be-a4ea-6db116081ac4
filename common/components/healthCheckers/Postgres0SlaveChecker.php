<?php

namespace common\components\healthCheckers;

use common\components\core\db\dbManager\DbManager;

class Postgres0SlaveChecker implements HealthCheckerInterface
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function check(): string
    {
        $result = self::STATUS_OK;
        try {
            \Yii::$app->slaveDb->createCommand('SELECT 1')->execute();
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return $result;
    }

    public function getName(): string
    {
        return 'postgres0Slave';
    }
}
