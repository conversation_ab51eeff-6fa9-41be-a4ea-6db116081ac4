<?php

namespace common\components\healthCheckers;

use common\components\core\db\dbManager\DbManager;
use mikemadisonweb\rabbitmq\Configuration;
use PhpAmqpLib\Message\AMQPMessage;

class Rabbit<PERSON>Q<PERSON>hecker implements HealthCheckerInterface
{
    protected DbManager $dbManager;
    private Configuration $rabbitMq;

    public function __construct()
    {
        $this->rabbitMq = \Yii::$app->rabbitmq;
    }

    public function check(): string
    {
        $result = self::STATUS_OK;
        try {
            $connection = $this->rabbitMq->getConnection('default');
            $channel = $connection->channel();
            $channel->close();
            $connection->close();
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return $result;
    }

    public function getName(): string
    {
        return 'rabbitMQ';
    }
}
