<?php

namespace common\components\healthCheckers;

use common\components\core\db\dbManager\DbManager;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitMQChecker implements HealthCheckerInterface
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->rabbitMq = \Yii::$app->rabbitmq;
    }

    public function check(): string
    {
        $result = self::STATUS_OK;
        try {
            $connection = \Yii::$app->rabbitmq->getConnection('default');
            $channel = $connection->channel();
            $channel->close();
            $connection->close();
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return $result;
    }

    public function getName(): string
    {
        return 'rabbitMQ';
    }
}
