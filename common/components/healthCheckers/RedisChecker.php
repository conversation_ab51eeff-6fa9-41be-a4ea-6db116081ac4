<?php

namespace common\components\healthCheckers;

use common\components\core\db\dbManager\DbManager;

class RedisChecker implements HealthCheckerInterface
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function check(): string
    {
        $result = self::STATUS_OK;
        try {
            $i = 777;
            $cacheKey = 'test';
            \Yii::$app->cache->set($cacheKey, $i);

            $iCached = (int)\Yii::$app->cache->get($cacheKey);
            if ($i !== $iCached) {
                return "FAILED";
            }
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return $result;
    }

    public function getName(): string
    {
        return 'redis';
    }
}
