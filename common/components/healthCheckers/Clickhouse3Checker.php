<?php

namespace common\components\healthCheckers;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;

class Clickhouse3Checker implements HealthCheckerInterface
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function check(): string
    {
        $result = self::STATUS_OK;

        try {
            $nodes = \Yii::$app->clickhouse->masters;
            $sql = "SELECT 1";
            $this->dbManager->getDb('system', 'clickhouse', $nodes[2])
                ->createCommand($sql)
                ->execute();
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return $result;
    }

    public function getName(): string
    {
        return 'clickhouse3';
    }
}
