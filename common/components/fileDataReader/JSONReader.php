<?php

namespace common\components\fileDataReader;

use JsonMachine\Items;
use JsonMachine\JsonDecoder\ExtJsonDecoder;

/**
 * Responsible for reading CSV files.
 */
class J<PERSON>NReader implements DataReaderInterface, IterateItemsInterface
{
    /**
     * {@inheritdoc}
     */
    public function getCountItems(string $filePath, array $expectedHeaders = []): int
    {
        return 1;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $filePath, int $limit, int $offset, array $expectedHeaders = []): array
    {
        $data = [];
        foreach ($this->iterateItems($filePath) as $item) {
            $data[] = $item;
        }
        return $data;
    }

    public function iterateItems(string $filePath): \Iterator
    {
        $items = Items::fromFile(
            $filePath,
            [
                'decoder' => new ExtJsonDecoder(true)
            ]
        );

        foreach ($items as $item) {
            yield $item;
        }
    }

    public function getStartDataLineNumber(string $filePath, array $expectedHeaders = []): int
    {
        return 0;
    }
}
