<?php

namespace common\components\fileDataReader;

/**
 * Data accessor is using to read and write data in any format.
 *
 * @package common\components\dataImportExport\dataAccessor
 */
interface DataReaderInterface
{
    /**
     * Retrieves and returns data from file.
     *
     * @param  string $filePath
     * @param  int    $limit
     * @param  int    $offset
     * @param  array  $expectedHeaders
     * @return array
     */
    public function getData(string $filePath, int $limit, int $offset, array $expectedHeaders = []): array;

    /**
     * Returns count of items (elements) containing in file.
     *
     * @param  string $filePath
     * @param  array  $expectedHeaders
     * @return int
     */
    public function getCountItems(string $filePath, array $expectedHeaders = []): int;

    /**
     * Returns line number of fisrst record with data.
     *
     * @param  string $filePath
     * @param  array  $expectedHeaders
     * @return int
     */
    public function getStartDataLineNumber(string $filePath, array $expectedHeaders = []): int;
}
