<?php

namespace common\components\fileDataReader;

/**
 * Responsible for reading CSV files.
 */
class CsvReader implements DataReaderInterface
{
    protected static $fileObjects;

    private const SUPPORTED_DELIMITERS = [
        ',', ';', '|', "\t",
    ];

    /**
     * {@inheritdoc}
     */
    public function getExtension(): string
    {
        return 'csv';
    }

    /**
     * {@inheritdoc}
     */
    public function getCountItems(string $filePath, array $expectedHeaders = []): int
    {
        $detectLineEndingsPrev = ini_get('auto_detect_line_endings');
        ini_set('auto_detect_line_endings', true);
        $inputFile = $this->openFileAndFillInfo($filePath, $expectedHeaders);
        $inputFile->seek($inputFile->getSize());
        $countItems = $inputFile->key() - $this->getFileInfoByKey($filePath, 'headersLineNumber');
        ini_set('auto_detect_line_endings', $detectLineEndingsPrev);

        return $countItems;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $filePath, int $limit, int $offset, array $expectedHeaders = []): array
    {
        $detectLineEndingsPrev = ini_get('auto_detect_line_endings');
        ini_set('auto_detect_line_endings', true);

        $inputFile = $this->openFileAndFillInfo($filePath, $expectedHeaders);
        $inputFile->seek(PHP_INT_MAX);
        $lastKey = $inputFile->key();

        if ($offset > $lastKey) {
            ini_set('auto_detect_line_endings', $detectLineEndingsPrev);
            return [];
        }

        $inputFile->seek($offset + $this->getFileInfoByKey($filePath, 'headersLineNumber') + 1);
        $headers = $this->getFileInfoByKey($filePath, 'headers');
        $data = [];
        for ($i = 0; $i < $limit; $i++) {
            $row = str_getcsv($inputFile->current(), $this->getFileInfoByKey($filePath, 'delimiter'));
//            $row = mb_convert_encoding($row, 'UTF-8', "ISO-8859-1");

            if (empty($row) || $inputFile->key() > $lastKey || $row === [null]) {
                break;
            }

            $countHeaders = count($headers);
            $countValues = count($row);
            $row = array_slice($row, 0, $countHeaders);

            if ($countHeaders > $countValues) {
                for ($i = $countValues; $i < $countHeaders; $i++) {
                    $row[$i] = null;
                }
            }

            $data[] = array_combine($headers, $row);
            $inputFile->next();
        }
        ini_set('auto_detect_line_endings', $detectLineEndingsPrev);

        return $data;
    }

    public function getStartDataLineNumber(string $filePath, array $expectedHeaders = []): int
    {
        $detectLineEndingsPrev = ini_get('auto_detect_line_endings');
        ini_set('auto_detect_line_endings', true);
        $this->openFileAndFillInfo($filePath, $expectedHeaders);
        ini_set('auto_detect_line_endings', $detectLineEndingsPrev);

        return $this->getFileInfoByKey($filePath, 'headersLineNumber') + 2;
    }

    /**
     * Opens file and fills information about it (caches to static variable).
     *
     * @param  string         $filePath
     * @param  array          $expectedHeaders
     * @return \SplFileObject
     */
    private function openFileAndFillInfo(string $filePath, array $expectedHeaders = []): \SplFileObject
    {
        $inputFile = new \SplFileObject($filePath);
        $headersLineNumber =  $this->detectHeadersLineNumber($inputFile, $expectedHeaders);
        $delimiter = $this->detectDelimiter($inputFile, $headersLineNumber);
        $headers = $this->detectHeaders($inputFile, $headersLineNumber, $delimiter);

        self::$fileObjects[$filePath] = [
            'fileObject' => $inputFile,
            'headers' => $headers,
            'headersLineNumber' => $headersLineNumber,
            'delimiter' => $delimiter,
        ];

        return $inputFile;
    }

    /**
     * Detects and returns used delimiter.
     *
     * @param  \SplFileObject $inputFile
     * @param  int            $headersLineNumber
     * @return string
     */
    private function detectDelimiter(\SplFileObject $inputFile, int $headersLineNumber): string
    {
        $inputFile->seek($headersLineNumber);
        $headersStr = $inputFile->current();
        $delimiters = [];

        foreach (self::SUPPORTED_DELIMITERS as $delimiterToCheck) {
            $delimiters[$delimiterToCheck] = count(str_getcsv($headersStr, $delimiterToCheck));
        }

        return array_search(max($delimiters), $delimiters, true);
    }

    /**
     * Detects and returns list of headers.
     *
     * @param  \SplFileObject $inputFile
     * @param  int            $headersLineNumber
     * @param  string         $delimiter
     * @return array
     */
    private function detectHeaders(\SplFileObject $inputFile, int $headersLineNumber, string $delimiter): array
    {
        $inputFile->seek($headersLineNumber);
        $headersStr = $inputFile->current();

        // Removing \ufeff character if it exists
        $headersStr = trim($headersStr, "\xEF\xBB\xBF");
        $headers = str_getcsv($headersStr, $delimiter);
        return array_map('trim', $headers);
    }

    /**
     * Detects and returns headers line number.
     *
     * @param  \SplFileObject $inputFile
     * @param  array          $expectedHeaders
     * @return int
     */
    private function detectHeadersLineNumber(\SplFileObject $inputFile, array $expectedHeaders = []): int
    {
        if (empty($expectedHeaders)) {
            return 0;
        }

        for ($i = 0; $i < 10; $i++) {
            $inputFile->seek($i);
            $lineStr = $inputFile->current();

            foreach ($expectedHeaders as $expectedHeader) {
                if (false === strpos($lineStr, $expectedHeader)) {
                    continue 2;
                }
            }

            return $i;
        }

        return 0;
    }

    /**
     * Returns file information by key (parameter name).
     *
     * @param string $filePath
     * @param string $fileInfoKey
     * @return |null
     */
    private function getFileInfoByKey(string $filePath, string $fileInfoKey)
    {
        return self::$fileObjects[$filePath][$fileInfoKey] ?? null;
    }
}
