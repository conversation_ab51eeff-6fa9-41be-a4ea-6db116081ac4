<?php

namespace common\components\fileDataReader;

use yii\helpers\FileHelper;

/**
 * Responsible for creating needed fiel data reader.
 *
 * @package common\components\dataImportExport\dataAccessor
 */
class DataReaderFactory
{
    public const DATA_FORMAT_CSV = 'csv';

    /**
     * Creates and returns data reader by file path.
     */
    public function getDataReaderByPath(string $filePath): DataReaderInterface
    {
        $mimeType = FileHelper::getMimeType($filePath);

        $fileHandler = fopen($filePath, 'rb');
        $firstBytes = fread($fileHandler, 3);
        fclose($fileHandler);

        if (false !== strpos($firstBytes, "[") || false !== strpos($firstBytes, "{")) {
            return new JSONReader();
        }

        switch ($mimeType) {
            case 'application/json':
                return new JSONReader();
            default:
                return new CsvReader();
        }
    }
}
