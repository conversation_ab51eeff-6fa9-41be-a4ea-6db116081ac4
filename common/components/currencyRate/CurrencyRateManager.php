<?php

namespace common\components\currencyRate;

use common\models\CurrencyRateHistory;
use SellerLogic\InternalApi\MainApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;
use yii\caching\CacheInterface;
use yii\db\Connection;
use bash<PERSON>ev\clickhouse\Connection as ClickhouseConnection;

class CurrencyRateManager
{
    /**
     * Universal currency using for storing internal currency amounts (like UTC for date and times).
     */
    public const BASE_CURRENCY = 'EUR';

    private MainApiClient $internalApiClient;
    private Connection $db;
    private CacheInterface $cache;

    public function __construct()
    {
        $this->internalApiClient = \Yii::$container->get('internalApiClient');
        $this->db = \Yii::$app->db;
        $this->cache = \Yii::$app->cache;
    }

    public function convert(float $amount, string $fromCurrencyCode, string $toCurrencyCode, \DateTime $date): ?float
    {
        try {
            $amountInBaseCurrency = $this->toBaseCurrency($amount, $fromCurrencyCode, $date);

            if (null === $amountInBaseCurrency) {
                return null;
            }

            $rateValue = $this->getRateValue($toCurrencyCode, $date);

            if (null !== $rateValue) {
                return $amountInBaseCurrency * $rateValue;
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return null;
    }

    public function toBaseCurrency(float $amount, string $fromCurrencyCode, \DateTime $date): ?float
    {
        if ($fromCurrencyCode === self::BASE_CURRENCY) {
            return $amount;
        }

        try {
            $rateValue = $this->getRateValue($fromCurrencyCode, $date);

            if (null !== $rateValue) {
                return $amount / $rateValue;
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return null;
    }

    public function getRateValue(string $currencyCode, \DateTime $date): ?float
    {
        $cacheKey = $this->getCacheKey($currencyCode, $date->format('Y-m-d'));
        $rateValue = $this->cache->get($cacheKey);

        if (false !== $rateValue) {
            return $rateValue;
        }

        /** @var CurrencyRateHistory $currencyRate */
        $currencyRate = CurrencyRateHistory::find()
            ->where([
                'currency_id' => $currencyCode,
                'date' => $date->format('Y-m-d')]
            )
            ->one();

        if (empty($currencyRate)) {
            $this->cache->set($cacheKey, null, 60 * 5);
            return null;
        }

        $this->cache->set($cacheKey, $currencyRate->value, 60 * 60 * 24);
        return $currencyRate->value;
    }

    public function loadRatesFromApi(\DateTime $from, \DateTime $to)
    {
        $current = new \DateTime();
        if ($to->format('Y-m-d') > $current->format('Y-m-d')) {
            $to = $current;
        }

        $response = $this->internalApiClient
            ->currencyRateHistory()
            ->index([
                'date' => $from->format('Y-m-d') . ' - ' . $to->format('Y-m-d'),
                'all' => 1
            ]);

        $response = (new ResponseTransformer())->transform($response);
        $filledRates = $this->fillMissingRates($response, $from, $to);

        $this->saveRates($filledRates);
    }

    private function saveRates(array $ratesFromApi): void
    {
        if (empty($ratesFromApi)) {
            return;
        }

        foreach ($ratesFromApi as $rateFromApi) {
            $cacheKey = $this->getCacheKey($rateFromApi['currency_id'], $rateFromApi['date']);
            $this->cache->set($cacheKey, $rateFromApi['value'], 60 * 60 * 24);
        }

        $sql = $this->db
            ->createCommand()
            ->batchInsert('currency_rate_history', ['currency_id', 'date', 'value'], $ratesFromApi)
            ->getRawSql()
            . ' ON CONFLICT (currency_id, date) DO UPDATE SET value = EXCLUDED.value';
        $this->db->createCommand($sql)->execute();
    }

    private function fillMissingRates(array $ratesFromApi, \DateTime $firstDate, \DateTime $lastDate): array
    {
        $currencies = [];
        $lastDate = clone $lastDate;

        // If current date, we fill two days forward to prevent missing rates (covering weekends without rates)
        if ($lastDate->format('Y-m-d') === date('Y-m-d')) {
            $lastDate->modify('+2 days');
        }

        if (count($ratesFromApi) === 1) {
            $currencies = [$ratesFromApi[0]['currency_id']];
        } else {
            usort($ratesFromApi, function ($item1, $item2) use (&$currencies) {
                $currencies[] = $item1['currency_id'];
                $currencies[] = $item2['currency_id'];

                return strtotime($item1['date']) <=> strtotime($item2['date']);
            });
        }
        $currencies = array_unique($currencies);

        foreach ($ratesFromApi as $k => $rateFromApi) {
            $key = implode('_', [$rateFromApi['date'], $rateFromApi['currency_id']]);
            unset($rateFromApi['id']);
            $ratesFromApi[$key] = $rateFromApi;
            unset($ratesFromApi[$k]);
        }

        $latestRatesByCurrency = [];
        $ratesFromApiFilled = [];

        while (true) {
            foreach ($currencies as $currency) {
                $key = implode('_', [$firstDate->format('Y-m-d'), $currency]);

                if (isset($ratesFromApi[$key])) {
                    $latestRatesByCurrency[$currency] = $ratesFromApi[$key];
                    $ratesFromApiFilled[] = $ratesFromApi[$key];
                    unset($ratesFromApi[$key]);
                } elseif (isset($latestRatesByCurrency[$currency])) {
                    $ratesFromApiFilled[] = [
                        'currency_id' => $currency,
                        'date' => $firstDate->format('Y-m-d'),
                        'value' => $latestRatesByCurrency[$currency]['value']
                    ];
                }
            }
            $firstDate->modify('+1 day');

            if ($firstDate > $lastDate) {
                break;
            }
        }

        return $ratesFromApiFilled;
    }

    private function getCacheKey(string $currencyCode, string $dateYMD): string
    {
        return strtolower('currency_' . $currencyCode . '_' . $dateYMD);
    }
}
