<?php

namespace common\components;

use yii\db\BatchQueryResult;
use yii\db\DataReader;
use yii\db\Exception;

/**
 * {@inheritDoc}
 */
class BatchQueryResultWithLimitOffset extends BatchQueryResult
{
    /**
     * @var DataReader the data reader associated with this batch query.
     */
    private $_dataReader;

    /**
     * @var int MSSQL error code for exception that is thrown when last batch is size less than specified batch size
     * @see https://github.com/yiisoft/yii2/issues/10023
     */
    private $mssqlNoMoreRowsErrorCode = -13;

    protected $_offset = 0;

    /**
     * Resets the batch query.
     * This method will clean up the existing batch query so that a new batch query can be performed.
     */
    public function reset()
    {
        parent::reset();
        $this->_offset = 0;
    }

    /**
     * Fetches the next batch of data.
     * @return array the data fetched
     * @throws Exception
     */
    protected function fetchData()
    {
        $batchSize = $this->batchSize;
        $this->_dataReader = $this->query
            ->limit($batchSize)
            ->offset($this->_offset)
            ->createCommand($this->db)
            ->query();

        $this->_offset += $batchSize;
        $rows = $this->getRows();

        return $this->query->populate($rows);
    }

    /**
     * Reads and collects rows for batch
     * @return array
     * @since 2.0.23
     */
    protected function getRows()
    {
        $rows = [];
        $count = 0;

        if (is_array($this->_dataReader)) {
            return $this->_dataReader;
        }

        try {
            while ($count++ < $this->batchSize && ($row = $this->_dataReader->read())) {
                $rows[] = $row;
            }
        } catch (\PDOException $e) {
            $errorCode = isset($e->errorInfo[1]) ? $e->errorInfo[1] : null;
            if ($this->getDbDriverName() !== 'sqlsrv' || $errorCode !== $this->mssqlNoMoreRowsErrorCode) {
                throw $e;
            }
        }

        return $rows;
    }
}