<?php

namespace common\components\sellingApi\apiProxy;

use common\components\sellingApi\exception\DateRangeIsNotValidException;
use common\components\sellingApi\exception\InvalidInputException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use Selling<PERSON><PERSON>nerApi\Api\FinancesV0Api;
use SellingPartnerApi\ApiException;
use Selling<PERSON>artnerApi\Configuration;

/**
 * Proxy for \SellingPartnerApi\Api\FinancesApi.
 *
 * @mixin FinancesV0Api
 * @package common\components\sellingApi\apiClientProxy
 */
class FinancesApi extends BaseApiProxy
{
    /**
     * Indicates next token expired exception.
     *
     * @example
     * "Token is not valid, token duration is 312 minutes."
     */
    protected const NEXT_TOKEN_EXPIRED_INDICATOR = 'token duration is';

    protected const INVALID_DATE_RANGE_INDICATOR  = 'Date range is not valid';

    protected const INVALID_INPUT_INDICATOR = 'InvalidInput';

    /**
     * @inheritDoc
     */
    protected function getApiClient(Configuration $configuration)
    {
        return new FinancesV0Api($configuration);
    }

    public function __call($name, $arguments)
    {
        try {
            return parent::__call($name, $arguments);
        } catch (ApiException $e) {
            if ($this->isNextTokenExpiredException($e)) {
                throw new NextTokenExpiredException();
            }

            if ($this->isDateRangeInvalidException($e)) {
                throw new DateRangeIsNotValidException($e->getMessage());
            }

            if ($this->isInvalidInputException($e)) {
                throw new InvalidInputException($e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * Checks whether given exception occurs due to expired next token.
     *
     * @param ApiException $e
     * @return bool
     */
    private function isNextTokenExpiredException(ApiException $e): bool
    {
        return false !== strpos($e->getMessage(), self::NEXT_TOKEN_EXPIRED_INDICATOR);
    }

    /**
     * Checks whether exception has occurred due to expired access token.
     *
     * @param \Throwable $e
     * @return bool
     */
    private function isDateRangeInvalidException(\Throwable $e): bool
    {
        return false !== strpos($e->getMessage(), self::INVALID_DATE_RANGE_INDICATOR);
    }

    private function isInvalidInputException(\Throwable $e): bool
    {
        return false !== strpos($e->getMessage(), self::INVALID_INPUT_INDICATOR);
    }
}
