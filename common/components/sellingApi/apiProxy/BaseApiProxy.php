<?php

namespace common\components\sellingApi\apiProxy;

use common\components\sellingApi\ConfiguratorBuilder;
use common\components\sellingApi\exception\AccessDeniedException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\QuotaExceededException;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Configuration;

/**
 * Contains base functional for all kind of api proxies (design pattern).
 * Calling api through this proxy helps to handle api exceptions.
 *
 * @package common\components\sellingApi\apiProxy
 */
abstract class BaseApiProxy
{
    protected string $sellerId;
    protected string $region;

    private ConfiguratorBuilder $configuratorBuilder;

    /**
     * Exception with this text occurs when amazon recognized token as expired.
     */
    private const ACCESS_TOKEN_EXPIRED_INDICATOR = 'access token you provided has expired';

    private const ACCESS_TOKEN_INVALID_INDICATOR = 'revoked, malformed or invalid';
    private const ACCESS_DENIED_INDICATOR = 'requested resource is denied';

    private const QUOTA_EXCEEDED_INDICATOR  = 'QuotaExceeded';

    /**
     * Exception with this text occurs when access token will expire soon (in about 30 seconds),
     * and selling api core code is trying to recreate it using refresh token (which is empty).
     */
    private const MISSING_REFRESH_TOKEN_INDICATOR = 'is missing a required parameter : refresh_token';

    /**
     * Particular selling api client instance (FinancesApi, CatalogApi, MessagingApi, etc.)
     *
     * @var mixed
     */
    private $apiClientInstance;

    /**
     * Should create and return instance of some kind of selling api
     * (which will be used further of all api calls through proxy instance).
     *
     * @param Configuration $configuration
     * @return mixed
     */
    abstract protected function getApiClient(Configuration $configuration);

    /**
     * BaseApiProxy constructor.
     * @param string $sellerId
     * @param string $region
     */
    public function __construct(string $sellerId, string $region)
    {
        $this->sellerId = $sellerId;
        $this->region = $region;
        $this->configuratorBuilder = new ConfiguratorBuilder();
    }

    /**
     * @param $name
     * @param $arguments
     * @return mixed
     * @throws \Throwable
     */
    public function __call($name, $arguments)
    {
        if (empty($this->apiClientInstance)) {
            $this->initializeApiClient();
        }
        static $attempt = 0;
        try {
            $result = call_user_func_array([$this->apiClientInstance, $name], $arguments);
            $attempt = 0;
            return $result;
        } catch (\Throwable $e) {
            // BAS-1707 tmp debug
            if ($name === 'listFinancialEvents' && $e->getCode() == 403) {
                \Yii::error(new \Exception($e->getMessage()));
            }

            /**
             * Recreating api client with fresh configuration and access token and trying to call method again.
             */
            if ($this->isAccessTokenExpiredException($e)) {
                if ($attempt >= 1) {
                    throw new AccessTokenInvalidException("Unable to receive valid access token after second attempt");
                }
                $this->initializeApiClient(true);
                $attempt++;
                $result = $this->__call($name, $arguments);
                $attempt = 0;
                return $result;
            }
            $attempt = 0;

            if ($this->isAccessTokenInvalidException($e)) {
                throw new AccessTokenInvalidException($e->getMessage());
            }

            if ($this->isQuotaExceededException($e)) {
                throw new QuotaExceededException($e->getMessage());
            }

            if ($this->isAccessDeniedException($e)) {
                throw new AccessDeniedException($e->getMessage());
            }

            if ($e instanceof ApiException) {
                throw new ApiException(
                    $e->getMessage() . ' ' . json_encode([
                        $this->sellerId,
                        $this->region
                    ]),
                    $e->getCode(),
                    $e->getResponseHeaders(),
                    $e->getResponseBody()
                );
            }
            throw $e;
        }
    }

    /**
     * Checks whether exception has occurred due to expired access token.
     *
     * @param \Throwable $e
     * @return bool
     */
    private function isAccessTokenExpiredException(\Throwable $e): bool
    {
        if (false !== strpos($e->getMessage(), self::ACCESS_TOKEN_EXPIRED_INDICATOR)) {
            return true;
        }

        if (false !== strpos($e->getMessage(), self::MISSING_REFRESH_TOKEN_INDICATOR)) {
            return true;
        }

        return false;
    }

    private function isAccessDeniedException(\Throwable $e): bool
    {
        if (false !== strpos($e->getMessage(), self::ACCESS_DENIED_INDICATOR)) {
            return true;
        }
        return false;
    }

    /**
     * Checks whether exception has occurred due to expired access token.
     *
     * @param \Throwable $e
     * @return bool
     */
    private function isAccessTokenInvalidException(\Throwable $e): bool
    {
        if (false !== strpos($e->getMessage(), self::ACCESS_TOKEN_INVALID_INDICATOR)) {
            return true;
        }

        return false;
    }

    private function isQuotaExceededException(\Throwable $e): bool
    {
        if (false !== strpos($e->getMessage(), self::QUOTA_EXCEEDED_INDICATOR)) {
            return true;
        }

        return false;
    }

    private function initializeApiClient(bool $isRecreateConfig = false): void
    {
        try {
            $configuration = $this->configuratorBuilder->getConfiguration($this->sellerId, $this->region, $isRecreateConfig);
            $this->apiClientInstance = $this->getApiClient($configuration);
        } catch (\Throwable $e) {
            if ($e->getCode() === 404) {
                throw new AccessTokenInvalidException();
            }
            throw $e;
        }
    }
}