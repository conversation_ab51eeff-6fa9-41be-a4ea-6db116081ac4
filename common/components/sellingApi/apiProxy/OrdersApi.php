<?php

namespace common\components\sellingApi\apiProxy;

use SellingPartnerApi\Api\OrdersV0Api;
use SellingPartnerApi\Configuration;

/**
 * Proxy for \SellingPartnerApi\Api\FinancesApi.
 *
 * @mixin OrdersV0Api
 * @package common\components\sellingApi\apiClientProxy
 */
class OrdersApi extends BaseApiProxy
{
    /**
     * @inheritDoc
     */
    protected function getApiClient(Configuration $configuration)
    {
        return new OrdersV0Api($configuration);
    }
}
