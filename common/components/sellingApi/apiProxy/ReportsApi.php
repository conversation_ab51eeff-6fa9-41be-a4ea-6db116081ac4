<?php

namespace common\components\sellingApi\apiProxy;

use SellingPartnerApi\Api\ReportsV20210630Api;
use SellingPartnerApi\Configuration;

/**
 * Proxy for \SellingPartnerApi\Api\ReportsApi.
 *
 * @mixin ReportsV20210630Api
 */
class ReportsApi extends BaseApiProxy
{
    /**
     * @inheritDoc
     */
    protected function getApiClient(Configuration $configuration)
    {
        return new ReportsV20210630Api($configuration);
    }
}
