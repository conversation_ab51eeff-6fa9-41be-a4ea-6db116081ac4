<?php

namespace common\components\sellingApi;

use common\components\tokenService\TokenService;
use SellingPartnerApi\Configuration;

/**
 * Responsible for building configuration of api clients for particular seller and region.
 *
 * @package common\components\sellingApi
 */
class ConfiguratorBuilder
{
    /**
     * Used for receiving authentication tokens from token api service.
     *
     * @var TokenService|mixed
     */
    private TokenService $tokenService;

    /**
     * ConfiguratorBuilder constructor.
     */
    public function __construct()
    {
        $this->tokenService = \Yii::$app->tokenService;
    }

    public function getConfiguration(string $sellerId, string $region, bool $isRecreate = false): Configuration
    {
        $token = $this->tokenService->getToken($sellerId, $isRecreate);

        $configurator = new \SellerLogic\SellerApi\Configurator(
            new \SellerLogic\SellerApi\Config\ArrayConfig([
                'access_token' => $token->accessToken,
                'access_token_expiration' => $token->expiresAt->getTimestamp(),
                'role_arn' => getenv('SELLING_API_ROLE_ARN'),
                'access_key' => getenv('SELLING_API_ACCESS_KEY'),
                'secret_key' => getenv('SELLING_API_ACCESS_SECRET'),
            ]),
            \SellerLogic\SellerApi\Configurator::TOKEN_TYPE_ACCESS
        );
        $configurator->setRegion($region);

        return $configurator->getConfig();
    }
}
