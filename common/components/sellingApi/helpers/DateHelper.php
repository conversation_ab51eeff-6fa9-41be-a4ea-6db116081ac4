<?php

namespace common\components\sellingApi\helpers;

use DateTime;
use DateTimeZone;
use Throwable;

class DateHelper
{
    const FORMAT_ISO_8601 = 'Y-m-d\TH:i:s';

    public static function getDateTimeInISO8601($str): string
    {
        return self::getDateTime($str, self::FORMAT_ISO_8601);
    }

    public static function getDateTimeInISO($str): string
    {
        return self::getDateTime($str, 'c');
    }

    public static function getDateTime($str, $format): string
    {
        try {
            $date = new DateTime($str, new DateTimeZone('UTC'));
        } catch (Throwable $e) {
            return '';
        }
        return $date->format($format);
    }

    /**
     * @param mixed|null $money
     */
    public static function getPriceFromMoney($money = null, $default = 0)
    {
        return $money ? $money->getAmount() : $default;
    }

    public static function getDateInDefaultFormat(?int $time = null): string
    {
        return $time ? date('Y-m-d', $time) : date('Y-m-d');
    }

    public static function getDateTimeInDefaultFormat(?int $time = null): string
    {
        return $time ? date('Y-m-d H:i:s', $time) : date('Y-m-d H:i:s');
    }
}
