## Selling API component

We uses fork of amazon's selling-partner-api to get customer's data from amazon.
[Please, see more details in Selling Partner Api docs](https://github.com/amzn/selling-partner-api-docs/blob/main/guides/developer-guide/SellingPartnerApiDeveloperGuide.md#migrating-authorization-from-amazon-marketplace-web-service)

Every api resources have their own realisations of API without any kind of common interface:
- SellingPartnerApi\Api\FinancesApi
- SellingPartnerApi\Api\FeesApi
- SellingPartnerApi\Api\OrdersApi
- etc.

To initialize certain api client, we should configure it using <PERSON>llerLogic\SellerApi\Configurator.
This configurator creates authentication configuration including access token and access token expiration (for particular seller and region).

We decided to receive access tokens from our internal token service. So we accepted to handle token expiration exceptions on our side.
This handling is doing by api client proxy classes.

Every api resources we use should have their own representations of proxy and should extend abstract api proxy class common\components\sellingApi\apiProxy\BaseAiProxy (see example such implementation for FinancesApi in common\components\sellingApi\apiProxy\FinancesApi).

Proxy classes allows ass to interact with end api client like it was without proxy (calling the same methods), but also it extends functionality and adds automatic generation of configuration for particular seller and region (receiving access token from token storage, creating proper configuration and initializing end client with that configuration).
In case of token expiration, proxy class handles exception an trying to reinitialize end client with fresh access token and invoke again failed api call (see realisation of this mechanism in __call function of common\components\sellingApi\apiProxy\BaseApiProxy).

#### How to create proxy class for some api resource
Create realization in common\components\sellingApi\apiProxy namespace.
Extends it from common\components\sellingApi\apiProxy\BaseApiProxy, realize abstract method getApiClient which creates and returns real end api client. 

#### How to use proxy class
Just create it with passing seller id and region to constructor, and call methods from end client api class.
For example:
````
$financesApi = new FinancesApi('GE79IU324', 'EU');
$financialEvents = $financesApi->listFinancialEvents(100, '2021-01-02', '2021-01-03');


