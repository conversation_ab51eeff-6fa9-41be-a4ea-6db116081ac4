<?php

namespace common\components\dbCleaner;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\core\db\DbPostfixManager;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\DbStructure;
use common\models\Seller;

class Cleaner
{
    use LogToConsoleTrait;
    private DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function removeSeller(string $sellerId, int $customerId): void
    {
        $this->dbManager->setCustomerId($customerId, false);
        $dbPostfixManager = new DbPostfixManager($sellerId, $customerId);
        $this->clearSellerRelatedTablesInPostgresDatabase($sellerId);

        foreach (DbManager::getSellerRelatedDbs() as $name) {
            $schemaName = $name . '_' . $dbPostfixManager->getDbPostfixForSellerRelatedDbs();
            $this->dropPostgresSchema($schemaName);
        }

        $seller = Seller::find()->where([
            'customer_id' => $customerId,
            'seller.id' => $sellerId
        ])->one();

        if (!empty($seller)) {
            $this->info("Deleting seller {$sellerId}");
            $seller->is_active = false;
            $seller->is_analytic_active = false;
            $seller->save(false);
        }
        $this->removeCustomerIfNeed($customerId, $dbPostfixManager);
    }

    private function removeCustomerIfNeed(int $customerId, DbPostfixManager $dbPostfixManager): void
    {
        try {
            $countSellers = Seller::find()->where([
                'customer_id' => $customerId,
                'is_analytic_active' => true
            ])
                ->noCache()
                ->count();

            if ($countSellers > 0) {
                return;
            }
            $this->info("Fully removing data of customer {$customerId}");

            foreach (DbManager::getCustomerRelatedDbs() as $name) {
                $schemaName = $name . '_' . $dbPostfixManager->getDbPostfixForCustomerRelatedDbs();
                $this->dropPostgresSchema($schemaName);
            }

            $this->dropClickhouseDatabase(DbManager::DB_PREFIX_CUSTOMER . '_' . $dbPostfixManager->getDbPostfixForCustomerRelatedDbs());
            $this->clearCustomerRelatedTablesInPostgresDatabase($customerId);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    public function dropPostgresSchema(string $schemaName): void
    {
        $this->info("Dropping postgres schema {$schemaName}");

        try {
            $this->dbManager->getDb('db')
                ->createCommand("DROP SCHEMA IF EXISTS {$schemaName} CASCADE")
                ->execute();
            // We do now which shard to use, so removing on both
            $this->dbManager->getDb('db1')
                ->createCommand("DROP SCHEMA IF EXISTS {$schemaName} CASCADE")
                ->execute();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    private function dropClickhouseDatabase(string $dbName)
    {
        $this->info("Dropping clickhouse database {$dbName}, but keeping repricer_event…");

        try {
            /** @var \yii\db\Connection $clickhouseDb */
            $clickhouseDb = $this->dbManager->getDb('system', HelperFactory::TYPE_CLICKHOUSE);
            $clusterPart = '';
            if (YII_ENV !== 'local') {
                $clusterName = getenv('CLICKHOUSE_CLUSTER_NAME');
                $clusterPart = "ON CLUSTER {$clusterName}";
            }
            $tables = $clickhouseDb
                ->createCommand(
                    "SELECT name, engine
                 FROM system.tables
                 WHERE database = :db
                   AND name != 'repricer_event'"
                )
                ->bindValue(':db', $dbName)
                ->queryAll();

            foreach ($tables as $table) {
                $this->info("Dropping table {$dbName}.{$table['name']}");
                $tableName = "`{$dbName}`.`{$table['name']}`";
                if ($table['engine'] === 'Dictionary') {
                    $clickhouseDb
                        ->createCommand("DROP DICTIONARY IF EXISTS {$tableName} {$clusterPart}")
                        ->execute();
                } else {
                    $clickhouseDb
                        ->createCommand("DROP TABLE IF EXISTS {$tableName} {$clusterPart}")
                        ->execute();
                }
            }

        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    private function clearCustomerRelatedTablesInPostgresDatabase(int $customerId): void
    {
        $this->info("Deleting data from customer related postgres tables");
        try {
            DbStructure::getDb()->createCommand()->delete(DbStructure::tableName(), [
                'customer_id' => $customerId
            ])->execute();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    private function clearSellerRelatedTablesInPostgresDatabase(string $sellerId): void
    {
        $this->info("Deleting data from seller related postgres tables");

        try {
            $this->info("Deleting from product table");
            Product::getDb()->createCommand()->delete(Product::tableName(), [
                'seller_id' => $sellerId
            ])->execute();

            $productCostPeriodTableName = ProductCostPeriod::tableName();
            $productCostItemTableName = ProductCostItem::tableName();

            $this->info("Deleting data from table {$productCostItemTableName}");
            ProductCostItem::getDb()->createCommand("
                DELETE 
                FROM {$productCostItemTableName} 
                WHERE product_cost_period_id IN(
                    SELECT id 
                    FROM {$productCostPeriodTableName}
                    WHERE seller_id = '{$sellerId}'
                )
            ")->execute();

            $this->info("Deleting data from table {$productCostPeriodTableName}");
            ProductCostPeriod::getDb()->createCommand("
                DELETE 
                FROM {$productCostPeriodTableName} 
                WHERE seller_id = '{$sellerId}'
            ")->execute();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}
