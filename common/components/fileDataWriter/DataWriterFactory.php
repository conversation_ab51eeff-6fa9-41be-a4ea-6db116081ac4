<?php

namespace common\components\fileDataWriter;

use yii\helpers\FileHelper;

/**
 * Creates and returns data writer.
 */
class DataWriterFactory
{
    public const DATA_FORMAT_CSV = 'csv';
    public const DATA_FORMAT_XLS = 'xls';
    public const DATA_FORMAT_TXT = 'txt';
    public const DATA_FORMAT_EXCEL = 'excel';

    public const SUPPOERTED_FORMATS = [
        self::DATA_FORMAT_CSV,
        self::DATA_FORMAT_XLS,
        self::DATA_FORMAT_EXCEL,
        self::DATA_FORMAT_TXT,
    ];

    public function getDataWriter(string $dataFormat): DataWriterInterface
    {
        if ($dataFormat === self::DATA_FORMAT_CSV) {
            return new CsvWriter();
        }

        if ($dataFormat === self::DATA_FORMAT_XLS || $dataFormat === self::DATA_FORMAT_EXCEL) {
            return new XlsWriter();
        }

        if ($dataFormat === self::DATA_FORMAT_TXT) {
            return new TxtWriter();
        }

        throw new \Exception("Unable to determine data writer using data format '$dataFormat'");
    }

    public function getDataWriterByPath(string $filePath): DataWriterInterface
    {
        $mimeType = FileHelper::getMimeType($filePath);

        if ($mimeType === 'text/plain') {
            return $this->getDataWriter(self::DATA_FORMAT_TXT);
        }

        if ($mimeType === 'text/csv') {
            return $this->getDataWriter(self::DATA_FORMAT_CSV);
        }

        return $this->getDataWriter(self::DATA_FORMAT_CSV);
    }
}
