<?php

namespace common\components\fileDataWriter;

/**
 * Writes data into file in CSV format.
 */
class CsvWriter implements DataWriterInterface
{
    public const DELIMITER = ';';

    /**
     * {@inheritdoc}
     */
    public function getExtension(): string
    {
        return 'csv';
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(): string
    {
        return 'text/csv';
    }

    /**
     * {@inheritdoc}
     */
    public function saveData(array $dataToSave, string $filePath): void
    {
        if (empty($dataToSave)) {
            return;
        }

        $isNewFile = !file_exists($filePath);
        $outputFile = new \SplFileObject($filePath, 'a');

        if ($isNewFile) {
            $headers = array_keys($dataToSave[0]);
            $outputFile->fputcsv($headers, static::DELIMITER);
        }

        foreach ($dataToSave as $data) {
            $outputFile->fputcsv($data, static::DELIMITER);
        }
    }
}
