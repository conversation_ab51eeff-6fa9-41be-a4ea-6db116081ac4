<?php

namespace common\components\fileDataWriter;

/**
 * Writes data into file in CSV format (with a tab separator).
 */
class TxtWriter extends CsvWriter
{
    public const DELIMITER = "\t";

    /**
     * {@inheritdoc}
     */
    public function getExtension(): string
    {
        return 'txt';
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(): string
    {
        return 'text/plain';
    }
}
