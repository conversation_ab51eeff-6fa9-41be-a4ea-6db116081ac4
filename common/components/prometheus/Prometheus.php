<?php

namespace common\components\prometheus;

use Prometheus\RegistryInterface;

class Prometheus
{
    public string $namespace;
    public array $defaultOptions = [];

    protected ?RegistryInterface $registry = null;

    public function getRegistry(): RegistryInterface
    {
        if (null === $this->registry) {
            \Prometheus\Storage\Redis::setDefaultOptions($this->defaultOptions);
            $this->registry = \Prometheus\CollectorRegistry::getDefault();
        }

        return $this->registry;
    }

    public function inc(string $metricName, array $labels, int $incBy = 1): void
    {
        $registry = $this->getRegistry();
        $counter = $registry->getOrRegisterCounter(
            $this->namespace,
            $metricName,
            $metricName,
            array_keys($labels)
        );
        $counter->incBy($incBy, $labels);
    }

    public function customCounter(string $itemName, float $incBy = 1)
    {
        $registry = $this->getRegistry();
        $counter = $registry->getOrRegisterCounter(
            $this->namespace,
            'count_items',
            'Amount of any kind of items',
            [
                'item_name',
            ]
        );
        $counter->incBy($incBy, [
            'item_name' => $itemName
        ]);
    }

    public function performancePerSecond(string $metricName, float $amountPerSecond): void
    {
        $registry = $this->getRegistry();
        $histogram = $registry->getOrRegisterHistogram(
            $this->namespace,
            'performance_per_second',
            'Amount of items processed per second',
            ['metric_name']
        );

        $histogram->observe($amountPerSecond, [
            'metric_name' => $metricName
        ]);
    }

    public function gauge(string $metricName, string $gaugeName, float $metricValue): void
    {
        $registry = $this->getRegistry();
        $gauge = $registry->getOrRegisterGauge(
            $this->namespace,
            $gaugeName,
            'Custom gauge',
            ['metric_name']
        );

        $gauge->set($metricValue, [
            'metric_name' => $metricName
        ]);
    }

    public function incConsumerState(
        string $consumerName,
        string $status,
        string $status2,
        ?string $eventPeriodType = null
    ): void {
        $registry = $this->getRegistry();
        $counter = $registry->getOrRegisterCounter(
            $this->namespace,
            'consumer_state',
            'State of consumers (statuses)',
            [
                'consumer',
                'status',
                'status2',
                'event_period_type'
            ]
        );
        $counter->inc([
            'consumer' => $consumerName,
            'status' => $status,
            'status2' => $status2,
            'event_period_type' => $eventPeriodType
        ]);
    }
}