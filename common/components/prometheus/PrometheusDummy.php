<?php

namespace common\components\prometheus;

class PrometheusDummy extends Prometheus
{

    public function inc(string $metricName, array $labels, int $incBy = 1): void
    {

    }

    public function customCounter(string $itemName, float $incBy = 1)
    {

    }

    public function performancePerSecond(string $metricName, float $amountPerSecond): void
    {
    }

    public function gauge(string $metricName, string $gaugeName, float $metricValue): void
    {

    }

    public function incConsumerState(
        string $consumerName,
        string $status,
        string $status2,
        ?string $eventPeriodType = null
    ): void {

    }
}