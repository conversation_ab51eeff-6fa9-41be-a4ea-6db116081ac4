<?php
declare(strict_types=1);

namespace common\components\log;

use Throwable;
use yii\helpers\BaseConsole;
use yii\helpers\Console;
use yii\helpers\VarDumper;
use yii\log\Logger;
use yii\log\Target;

use const STDOUT;

/**
 * ConsoleTarget writes logs to console (useful for debugging console applications).
 * Drop-in replacement for pahanini\log\ConsoleTarget with PHP 8.3 fixes.
 */
final class ConsoleTarget extends Target
{
    /**
     * Backward-compatibility flag (old typo from pahanini): if true, context message is appended.
     * @var bool
     */
    public bool $enableContextMassage = false;

    /**
     * Properly spelled flag (new): if true, context message is appended.
     * Either this or enableContextMassage enables context output.
     * @var bool
     */
    public bool $enableContextMessage = false;

    /**
     * Show category in label.
     * @var bool
     */
    public bool $displayCategory = false;

    /**
     * Show date/time in label.
     * @var bool
     */
    public bool $displayDate = false;

    /**
     * Date format used when $displayDate = true.
     * @var string
     */
    public string $dateFormat = 'Y-m-d H:i:s';

    /**
     * Padding width for label column.
     * @var int
     */
    public int $padSize = 30;

    /**
     * Color scheme for message level labels.
     * Keys are level names from Logger::getLevelName().
     * @var array<string,int>
     */
    public array $color = [
        'error' => BaseConsole::BG_RED,
    ];

    /**
     * Append context only when explicitly enabled (keeps BC with pahanini).
     */
    protected function getContextMessage(): string
    {
        if ($this->enableContextMessage || $this->enableContextMassage) {
            return parent::getContextMessage();
        }
        return '';
    }

    /**
     * Export collected log messages to console.
     */
    public function export(): void
    {
        foreach ($this->messages as $message) {
            $line = $this->formatMessageSafe($message);

            // Keep original behavior: errors to stderr-like output, others to stdout
            if (($message[1] ?? Logger::LEVEL_INFO) === Logger::LEVEL_ERROR) {
                Console::error($line);
            } else {
                Console::output($line);
            }
        }
    }

    /**
     * Formats message safely (defensive against missing indexes).
     *
     * @param array{0:mixed,1?:int,2?:string,3?:float|int} $message
     */
    private function formatMessageSafe(array $message): string
    {
        $label = $this->generateLabel($message);
        $text  = $this->generateText($message);

        return str_pad($label, $this->padSize, ' ') . ' ' . $text . $this->getContextMessage();
    }

    /**
     * Builds label (date, category, level) with safe timestamp casting for PHP 8.1+.
     *
     * @param array{0:mixed,1?:int,2?:string,3?:float|int} $message
     */
    private function generateLabel(array $message): string
    {
        $label = '';

        // Date
        if ($this->displayDate) {
            $ts = isset($message[3]) ? (int)$message[3] : time();
            $label .= '[' . date($this->dateFormat, $ts) . ']';
        }

        // Category
        if ($this->displayCategory) {
            $category = $message[2] ?? 'application';
            $label .= '[' . $category . ']';
        }

        // Level (with coloring)
        $levelInt  = isset($message[1]) ? (int)$message[1] : Logger::LEVEL_INFO;
        $levelName = Logger::getLevelName($levelInt);
        $levelTag  = '[' . $levelName . ']';

        if (Console::streamSupportsAnsiColors(STDOUT)) {
            if (isset($this->color[$levelName])) {
                $levelTag = Console::ansiFormat($levelTag, [$this->color[$levelName]]);
            } else {
                $levelTag = Console::ansiFormat($levelTag, [BaseConsole::BOLD]);
            }
        }

        $label .= $levelTag;

        return $label;
    }

    /**
     * Converts message body to string.
     *
     * @param array{0:mixed} $message
     */
    private function generateText(array $message): string
    {
        $text = $message[0] ?? '';

        if (!is_string($text)) {
            // Exceptions may not be serializable if in the call stack somewhere is a Closure
            if ($text instanceof Throwable) {
                return (string)$text;
            }
            return VarDumper::export($text);
        }

        return $text;
    }
}
