<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\Seller;

class WrongMovedAmazonOrdersToClickhouse implements CaseInterface
{
    use HelperTrait;
    public function getDescription(): string
    {
        return 'checking how correct amazon orders moved to clickhouse';
    }

    public function getOccurrencesThreshold(): int
    {
        return 100;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var Seller[] $sellers */
        $sellers = Seller::find()->where([
            'customer_id' => $dbManager->getCustomerId(),
            'is_analytic_active' => true
        ])->all();

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbPort'];
        $dbHost = $config['dbHost'];
        $dbUsername = $config['dbUser'];
        $dbPassword = $config['dbPassword'];
        $amazonOrderTableName = AmazonOrder::tableName();
        $amazonOrderInProgressTableName = AmazonOrderInProgress::tableName();
        $timeOffset = date('Y-m-d H:i:s', strtotime('-3 hours'));

        $minCheckDate = strtotime('-1 day');

        foreach ($sellers as $seller) {
            $dbManager->setSellerId($seller->id);

            foreach ($this->iterateDates() as $dates) {
                $fromDateTime = $dates['fromDateTime'];
                $toDateTime = $dates['toDateTime'];
                $schemaName = $dbManager->getSchemaName('order');

                if (strtotime($fromDateTime) > $minCheckDate) {
                    continue;
                }

                $sql = "
                    SELECT 
                        ch.date,
                        pg.orders as orders_postgres,
                        ch.orders as orders_clickhouse,
                        pg.units as units_postgres,
                        ch.units as units_clickhouse,
                        '{$seller->id}' as seller_id
                    FROM (
                        SELECT 
                            toDate(order_purchase_date) as date,
                            count(distinct order_id) as orders,
                            sum(quantity) as units
                        FROM (
                            SELECT order_purchase_date, order_id, quantity
                            FROM {$amazonOrderTableName}
                            WHERE toDate(order_purchase_date) BETWEEN toDate('{$fromDateTime}') AND toDate('{$toDateTime}')
                            AND toDate(order_purchase_date) <= toDate('{$timeOffset}')
                            AND seller_id = '{$seller->id}'
                            UNION ALL
                            SELECT order_purchase_date, order_id, quantity
                            FROM {$amazonOrderInProgressTableName}
                            WHERE toDate(order_purchase_date) BETWEEN toDate('{$fromDateTime}') AND toDate('{$toDateTime}')
                            AND toDate(order_purchase_date) <= toDate('{$timeOffset}')
                            AND seller_id = '{$seller->id}'
                            AND version = (SELECT max(version) FROM {$amazonOrderInProgressTableName})
                        )
                        GROUP BY toDate(order_purchase_date)
                    ) as ch
                    LEFT JOIN (
                        SELECT 
                            toDate(order_purchase_date) as date,
                            count(distinct order_id) as orders,
                            sum(quantity) as units
                        FROM
                        postgresql(
                            '$dbHost:$dbPort', 
                            'profit_dash_db', 
                            'amazon_order_view', 
                            '$dbUsername', 
                            '$dbPassword', 
                            '$schemaName'
                        )
                        WHERE toDate(order_purchase_date) BETWEEN toDate('{$fromDateTime}') AND toDate('{$toDateTime}')
                        AND toDate(order_purchase_date) <= toDate('{$timeOffset}')
                        AND seller_id = '{$seller->id}'
                        GROUP BY date
                    ) as pg ON (pg.date = ch.date)
                    HAVING ch.orders != pg.orders OR ch.units != pg.units
                    LIMIT 5000
                ";

                $result = $dbManager->getClickhouseCustomerDb()->createCommand($sql)->queryAll();

                if (empty($result)) {
                    continue;
                }

                $issue->countOccurrences += count($result);
                $issue->examples = array_merge(
                    $issue->examples,
                    array_slice($result, 0, 5)
                );

                if ($issue->countOccurrences >= $this->getOccurrencesThreshold() * 3) {
                    break;
                }
            }
        }

        return $issue;
    }
}
