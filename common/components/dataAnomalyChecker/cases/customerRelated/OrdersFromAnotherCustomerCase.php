<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\Product;
use common\models\finance\Shipment;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use yii\db\Expression;

class OrdersFromAnotherCustomerCase implements CustomerRelatedCaseInterface
{
    use HelperTrait;

    public function getDescription(): string
    {
        return 'checking that there are no orders from another customer';
    }

    public function getOccurrencesThreshold(): int
    {
        return 50;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;

        $sellers = Seller::find()
            ->select('id, customer_id')
            ->where(['customer_id' => $dbManger->getCustomerId()])
            ->asArray()
            ->all();

        foreach ($sellers as $seller) {
            $dbManger->setSellerId($seller['id']);

            foreach ($this->iterateDates() as $dates) {
                $fromDateTime = $dates['fromDateTime'];
                $toDateTime = $dates['toDateTime'];
                $orderItems = AmazonOrderItem::find()
                    ->select([
                        'order_id',
                        'order_item_id',
                        'amazon_order_item.sku',
                    ])
                    ->leftJoin(Product::tableName() . ' p', 'p.sku = amazon_order_item.sku')
                    ->leftJoin(Shipment::tableName() . ' s', 's."AmazonOrderId" = amazon_order_item.order_id')
                    ->where([
                        'AND',
                        ['>=', 'amazon_order_item.order_purchase_date', $fromDateTime],
                        ['<=', 'amazon_order_item.order_purchase_date', $toDateTime],
                        ['<=', 'amazon_order_item.order_purchase_date', date('Y-m-d', strtotime('-3 days'))],
                        ['is', 'p.sku', new Expression('null')],
                        ['is', 's.id', new Expression('null')],
                    ])
                    ->asArray()
                    ->groupBy('order_id, order_item_id, amazon_order_item.sku')
                    ->limit(500)
                    ->all();

                if (count($orderItems) === 0) {
                    continue;
                }

                $issue->countOccurrences += count($orderItems);
                $issue->examples[] = [
                    'seller_id' => $seller['id'],
                    'order_id' => $orderItems[0]['order_id'],
                    'order_item_id' => $orderItems[0]['order_item_id'],
                    'sku' => $orderItems[0]['sku'],
                ];

                if (count($issue->examples) >= $this->getOccurrencesThreshold() * 3) {
                    break;
                }
            }
        }
        return $issue;
    }
}
