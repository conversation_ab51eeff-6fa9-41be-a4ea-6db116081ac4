<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\ads\base\AbstractAdsRecord;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\ads\SpAdvertisedProduct;
use common\models\customer\clickhouse\PpcCostsLastFewDaysTransaction;
use common\models\customer\clickhouse\Transaction;
use common\models\FinanceEventCategory;

class WrongAppliedAmazonAdCosts implements CustomerRelatedCaseInterface
{
    use HelperTrait;

    public function getDescription(): string
    {
        return 'checking that amazon ads costs correctly moved to clickhouse from postgres (rough comparison without taking into account currency rates)';
    }

    public function getOccurrencesThreshold(): int
    {
        return 50;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManager = \Yii::$app->dbManager;
        $customerComponent = \Yii::$app->customerComponent;
        $moneyAccuracy = $customerComponent->getMoneyAccuracy();

        $dateStart = min(
            SpAdvertisedProduct::find()->select('MIN(date)')->scalar() ?? null,
            SdAdvertisedProduct::find()->select('MIN(date)')->scalar() ?? null,
            SbAdGroupStatistic::find()->select('MIN(date)')->scalar() ?? null,
        );

        if (empty($dateStart)) {
            return $issue;
        }
        $transactionTableName = Transaction::tableName();
        $ppcCostsLastFewDaysTable = PpcCostsLastFewDaysTransaction::tableName();
        $amazonAdsIds = FinanceEventCategory::getAmazonAdIds();

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUsername = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $dateStart = new \DateTime($dateStart);

        foreach ($this->iterateDates($dateStart) as $dates) {
            $fromDateTime = $dates['fromDateTime'];
            $toDateTime = $dates['toDateTime'];
            $postgresUnionParts = [];
            $postgresTables = [
                SpAdvertisedProduct::tableName(),
                SdAdvertisedProduct::tableName(),
                SbAdGroupStatistic::tableName(),
            ];

            $statusMovedToClickhouse = AbstractAdsRecord::STATUS_MOVED_TO_CLICHOUSE;

            foreach ($postgresTables as $postgresTable) {
                [$schemaName, $tableName] = explode('.', $postgresTable);
                $postgresUnionParts[] = "
                    SELECT 
                        toDate(date) as date,
                        sum(cost) as amount
                    FROM
                    postgresql(
                        '$dbHost:$dbPort', 
                        'profit_dash_db', 
                        '$tableName', 
                        '$dbUsername', 
                        '$dbPassword', 
                        '$schemaName'
                    )
                    WHERE toDate(date) BETWEEN toDate('{$fromDateTime}') AND toDate('{$toDateTime}')
                    GROUP BY date";
            }
            $postgresUnionParts = implode(' UNION ALL ', $postgresUnionParts);

            $sql = "
                SELECT 
                    ch_costs.date,
                    ch_costs.amount as amount_in_clickhouse,
                    pg_costs.amount as amount_in_postgres
                FROM (
                    SELECT 
                        toDate(t.PostedDate) as date,
                        abs(sum(t.Amount)) / $moneyAccuracy as amount
                    FROM (
                        SELECT PostedDate, Amount
                        FROM $transactionTableName
                        WHERE PostedDate BETWEEN '{$fromDateTime}' AND '{$toDateTime}'
                        AND CategoryId IN (" . implode(',', $amazonAdsIds) . ")
                        AND PostedDate < (SELECT min(PostedDate) FROM $ppcCostsLastFewDaysTable)
                        UNION ALL
                        SELECT PostedDate, Amount
                        FROM $ppcCostsLastFewDaysTable
                        WHERE PostedDate BETWEEN '{$fromDateTime}' AND '{$toDateTime}'                        
                    ) as t
                    GROUP BY toDate(t.PostedDate)
                ) as ch_costs
                LEFT JOIN (
                    SELECT 
                        pg_costs_0.date,
                        sum(pg_costs_0.amount) as amount
                    FROM (
                        $postgresUnionParts
                    ) as pg_costs_0
                    GROUP BY pg_costs_0.date
                ) as pg_costs ON (pg_costs.date = ch_costs.date)
                WHERE abs(ch_costs.amount - pg_costs.amount) > 5
                LIMIT 1000
            ";

            $result = $dbManager->getClickhouseCustomerDb()->createCommand($sql)->queryAll();

            if (empty($result)) {
                continue;
            }

            $issue->countOccurrences += count($result);
            $issue->examples = array_merge(
                $issue->examples,
                array_slice($result, 0, 5)
            );
        }

        return $issue;
    }
}
