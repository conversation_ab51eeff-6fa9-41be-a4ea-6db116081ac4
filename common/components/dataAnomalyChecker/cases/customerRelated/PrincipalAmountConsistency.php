<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\TransactionBuffer;
use common\models\FinanceEventCategory;
use SellingPartnerApi\Model\OrdersV0\Order;

class PrincipalAmountConsistency implements CustomerRelatedCaseInterface
{
    use HelperTrait;

    public function getDescription(): string
    {
        return 'checking that principal amount is consistent in clickhouse and postgres';
    }

    public function getOccurrencesThreshold(): int
    {
        return 50;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return false;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        $customerComponent = \Yii::$app->customerComponent;
        $transactionTableName = TransactionBuffer::tableName();
        $amazonOrderTableName = AmazonOrder::tableName();
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $moneyAccuracy = $customerComponent->getMoneyAccuracy();

        $allowedDiffThreshold = 1.5;

        foreach ($this->iterateDates(null, '+6 month') as $dates) {
            $fromDateTime = $dates['fromDateTime'];
            $toDateTime = $dates['toDateTime'];

            $sql = "
                SELECT 
                    pg.order_id,
                    pg.sku,
                    ch.principal_amount as principal_amount_clickhouse,
                    pg.principal_amount as principal_amount_postgres
                FROM (
                    SELECT 
                        order_id,
                        sku,
                        sum(quantity) as units,
                        sum(item_price) / 100 as principal_amount
                    FROM {$amazonOrderTableName}
                    WHERE toDate(order_purchase_date) BETWEEN toDate('{$fromDateTime}') AND toDate('{$toDateTime}')
                    AND item_price > 0
                    AND order_status = '" . Order::ORDER_STATUS_SHIPPED . "'
                    GROUP BY order_id, sku
                ) as pg
                INNER JOIN (
                    SELECT 
                        AmazonOrderId as order_id,
                        SellerSKU as sku,
                        sum(Quantity) as units,
                        sum(Amount) / {$moneyAccuracy}  as principal_amount
                    FROM {$transactionTableName}
                    WHERE (
                        toDate(PostedDate) 
                        BETWEEN toDate('{$fromDateTime}') - INTERVAL '1 MINUTE'
                        AND toDate('{$toDateTime}') + INTERVAL '1 MINUTE'
                    )
                    AND CategoryId = $organicSalesCategoryId
                    AND COGCategoryId = 0
                    GROUP BY AmazonOrderId, SellerSKU
                ) as ch ON (ch.order_id = pg.order_id and ch.sku = pg.sku)
                WHERE abs(ch.principal_amount / pg.principal_amount) > {$allowedDiffThreshold}
            ";

            $result = $dbManager->getClickhouseCustomerDb()->createCommand($sql)->queryAll();

            if (count($result) === 0) {
                continue;
            }

            $result = array_values($result);

            $issue->countOccurrences += count($result);
            $issue->examples = array_merge(
                $issue->examples,
                array_slice($result, 0, 5)
            );
        }

        return $issue;
    }
}
