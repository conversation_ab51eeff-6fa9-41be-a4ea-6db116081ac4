<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\Transaction;

class TooBigAmountCase implements CustomerRelatedCaseInterface
{
    protected CONST AMOUNT_EUR_THRESHOLD = 1000000;

    public function getDescription(): string
    {
        return 'too big amount in transaction';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }
    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;
        $tableName = Transaction::tableName();
        $threshold = self::AMOUNT_EUR_THRESHOLD;
        $customerComponent = \Yii::$app->customerComponent;
        $moneyAccuracy = $customerComponent->getMoneyAccuracy();

        $bigAmounts = $dbManger->getClickhouseCustomerDb()->createCommand("
            SELECT AmountEUR / $moneyAccuracy
            FROM {$tableName} 
            WHERE (
                AmountEUR / $moneyAccuracy <= -{$threshold}
                OR 
                AmountEUR / $moneyAccuracy >= {$threshold}
            )
            AND AmazonOrderId IS NOT NULL
            AND COGCategoryId = 1
            LIMIT 100
        ")->queryColumn();
        $issue->countOccurrences = count($bigAmounts);
        $issue->examples = $bigAmounts;

        return $issue;
    }
}