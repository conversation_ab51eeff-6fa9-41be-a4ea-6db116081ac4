<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\FbaReturn;
use common\models\customer\ProductCostCategory;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;

class IsCustomCostsAppliedCorrectly implements CustomerRelatedCaseInterface
{
    use HelperTrait;

    public function getDescription(): string
    {
        return 'checking that custom costs applied correctly';
    }

    public function getOccurrencesThreshold(): int
    {
        return 100;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDb = $dbManager->getClickhouseCustomerDb();
        $transactionTableName = TransactionBuffer::tableName();
        $orderTableName = AmazonOrder::tableName();
        $orderInProgressTableName = AmazonOrderInProgress::tableName();

        $VATId = ProductCostCategory::find()
            ->select('id')
            ->where([
                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES
            ])->column();
        $orderPriceIds = FinanceEventCategory::getOrderPriceIds();
        $refundOrderPriceIds = FinanceEventCategory::getRefundOrderPriceIds();

        $costOfGoodsIds = ProductCostCategory::find()
            ->select('id')
            ->where([
                'sales_category_id' => 'cost_of_goods'
            ])->column();

        $shippingCostsIds = ProductCostCategory::find()
            ->select('id')
            ->where([
                'sales_category_id' => 'shipping_costs'
            ])->column();

        $otherFeesIds = ProductCostCategory::find()
            ->select('id')
            ->where([
                'sales_category_id' => 'other_fees'
            ])->column();
        $facilitatorCategoryIds = FinanceEventCategory::getVatIds();
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $refundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $customerComponent = \Yii::$app->customerComponent;
        $moneyAccuracy = $customerComponent->getMoneyAccuracy();

        $allowedAmountDiff = 100;

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbPort'];
        $dbHost = $config['dbHost'];
        $dbUser = $config['dbUser'];
        $dbPassword = $config['dbPassword'];

        foreach ($this->iterateDates() as $dates) {
            $fromDateTime = $dates['fromDateTime'];
            $toDateTime = $dates['toDateTime'];
            $toDateTimeForOrders = (new \DateTime($toDateTime))->modify('+60 seconds')->format('Y-m-d H:i:s');

            $sql = sprintf("
                SELECT 
                    max(t1.marketplace_id) as marketplace_id,
                    max(t1.seller_id) as seller_id,
                    max(t1.seller_sku) as seller_sku,
                    max(t1.amazon_order_id) as amazon_order_id,
                    max(t1.cog_actual) as cog_actual,
                    max(t1.cog_actual_from_eur) as cog_actual_from_eur,
                    sum(
                        CASE
                            WHEN (cog.sales_category_id = 'cost_of_goods')
                            THEN abs(cog.amount_total * t1.quantity) * -1
                            ELSE 0
                        END
                        +
                        CASE
                            WHEN cog.sales_category_id = 'cost_of_goods' AND fba_return.order_id != '' AND t1.quantity_refund > 0
                            THEN cog.amount_total * t1.quantity_refund
                            ELSE 0
                        END
                    ) as cog_expected,
                    min(t1.shipping_costs_actual) as shipping_costs_actual,
                    min(CASE
                        WHEN (
                            cog.sales_category_id = 'shipping_costs' 
                            AND (o.fulfillment_channel = 'MFN')
                        )
                        THEN (cog.amount_total * t1.count_items) * -1
                        ELSE 0
                    END) as shipping_costs_expected,
                    max(t1.other_fees_actual) as other_fees_actual,
                    min(CASE
                        WHEN cog.sales_category_id = 'other_fees'
                        THEN (cog.amount_total * t1.quantity) * -1
                        ELSE 0
                    END) as other_fees_expected,
                    max(t1.vat_actual) as vat_actual,
                    toInt32(sum(
                        CASE
                            WHEN (
                                cog.sales_category_id = 'expenses_taxes' 
                                AND t1.facilitator_quantity = 0 
                                AND (o.is_business_order = 0 OR s.is_vcs_enabled = 0)
                            )
                            THEN (t1.order_price_actual - t1.order_price_actual / ((cog.amount_total / $moneyAccuracy) * 0.01 + 1)) * -1
                            ELSE 0
                        END
                        +
                        CASE
                            WHEN (
                                cog.sales_category_id = 'expenses_taxes'
                                AND t1.facilitator_quantity = 0 
                                AND (o.is_business_order = 0 OR s.is_vcs_enabled = 0)
                                AND abs(t1.order_price_actual_refund) > 0
                            )
                            THEN abs(t1.order_price_actual_refund - t1.order_price_actual_refund / ((cog.amount_total / $moneyAccuracy) * 0.01 + 1))
                            ELSE 0
                        END
                    )) as vat_expected,
                    max(abs(t1.order_price_actual_refund)) as order_price_actual_refund,
                    max(t1.order_price_actual) as order_price_actual,
                    sum(t1.facilitator_quantity) as facilitator_quantity,
                    max(o.is_business_order) as is_business_order,
                    max(s.is_vcs_enabled) as is_vcs_enabled,
                    max(t1.count_items) as count_items,
                    max(t1.count_items_refund) as count_items_refund
                FROM (
                    SELECT 
                        max(CASE
                            WHEN COGCategoryId = 0 AND CategoryId = $organicSalesCategoryId
                            THEN PostedDate
                            ELSE null
                        END) as posted_date,
                        max(MarketplaceId) as marketplace_id,
                        max(SellerId) as seller_id,
                        max(SellerSKU) as seller_sku,
                        max(AmazonOrderId) as amazon_order_id,
                        max(CreatedAt) as created_at,
                        sum(CASE
                            WHEN (COGCategoryId = 0 AND CategoryId = $organicSalesCategoryId)
                            THEN Quantity
                            ELSE 0
                        END) as quantity,
                        sum(
                            CASE 
                            WHEN (COGCategoryId = 0 AND CategoryId = $refundCategoryId)
                            THEN Quantity
                            ELSE 0
                        END) as quantity_refund,
                        sum(CASE
                            WHEN (COGCategoryId = 0 AND CategoryId = $organicSalesCategoryId)
                            THEN 1 * MergeCounter
                            ELSE 0
                        END) as count_items,
                        sum(
                            CASE 
                            WHEN (COGCategoryId = 0 AND CategoryId = $refundCategoryId)
                            THEN 1 * MergeCounter
                            ELSE 0
                        END) as count_items_refund,
                        sum(CASE
                            WHEN COGCategoryId IN (" . implode(',', $costOfGoodsIds) . ") AND CategoryId = $organicSalesCategoryId
                            THEN Amount
                            ELSE 0
                        END) as cog_actual,
                        sum(CASE
                            WHEN COGCategoryId IN (" . implode(',', $costOfGoodsIds) . ") AND CategoryId = $organicSalesCategoryId
                            THEN AmountEUR * dictGetOrNull(
                                default.currency_rate_dict, 
                                'value', 
                                tuple(toDate(PostedDate), Currency)
                            )
                            ELSE 0
                        END) as cog_actual_from_eur,
                        sum(CASE
                            WHEN COGCategoryId IN (" . implode(',', $shippingCostsIds) . ") AND CategoryId = $organicSalesCategoryId
                            THEN Amount
                            ELSE 0
                        END) as shipping_costs_actual,
                        sum(CASE
                            WHEN COGCategoryId IN (" . implode(',', $otherFeesIds) . ") AND CategoryId = $organicSalesCategoryId
                            THEN Amount
                            ELSE 0
                        END) as other_fees_actual,
                        sum(CASE
                            WHEN COGCategoryId = 0 AND CategoryId IN (" . implode(',', $orderPriceIds) . ") 
                            THEN Amount
                            ELSE 0
                        END) as order_price_actual,
                        sum(CASE
                            WHEN COGCategoryId = 0 AND CategoryId IN (" . implode(',', $refundOrderPriceIds) . ") 
                            THEN Amount
                            ELSE 0
                        END) as order_price_actual_refund,
                        sum(
                            CASE 
                            WHEN COGCategoryId IN (" . implode(',', $VATId) . ") AND CategoryId = $organicSalesCategoryId
                            THEN Amount
                            ELSE 0
                        END) as vat_actual,
                        sum(CASE
                            WHEN CategoryId IN (" . implode(',', $facilitatorCategoryIds) . ")
                            THEN 1
                            ELSE 0
                        END) as facilitator_quantity
                    FROM {$transactionTableName}
                    WHERE PostedDate BETWEEN toDateTime('$fromDateTime') - INTERVAL '1 MINUTE'
                    AND toDateTime('$toDateTime') + INTERVAL '1 MINUTE'
                    GROUP BY AmazonOrderId, SellerSKU
                ) as t1
                LEFT JOIN (
                    SELECT 
                        max(created_at) as created_at,
                        order_id,
                        max(COALESCE(NULLIF(fulfillment_channel, ''), 'MFN')) as fulfillment_channel,
                        max(COALESCE(is_business_order, 0)) as is_business_order
                    FROM {$orderTableName}
                    WHERE order_purchase_date BETWEEN toDateTime('$fromDateTime') AND toDateTime('$toDateTimeForOrders')
                    GROUP BY order_id
                    UNION ALL
                    SELECT 
                        max(created_at) as created_at,
                        order_id,
                        max(COALESCE(NULLIF(fulfillment_channel, ''), 'MFN')) as fulfillment_channel,
                        max(COALESCE(is_business_order, 0)) as is_business_order
                    FROM {$orderInProgressTableName}
                    WHERE order_purchase_date BETWEEN toDateTime('$fromDateTime') AND toDateTime('$toDateTimeForOrders')
                    AND version = (SELECT max(version) FROM {$orderInProgressTableName})
                    GROUP BY order_id
                ) as o ON t1.amazon_order_id = o.order_id
                LEFT JOIN (
                    SELECT 
                        pcp.id,
                        max(pcp.sales_category_id) as sales_category_id,
                        sum(pci.marketplace_amount_per_unit) * $moneyAccuracy as amount_total, 
                        max(pcp.marketplace_id) as marketplace_id, 
                        max(pcp.seller_id) as seller_id,
                        max(pcp.seller_sku) as seller_sku, 
                        max(pcp.date_start) as date_start, 
                        max(pcp.date_end) as date_end
                    FROM postgresql(
                        '$dbHost:$dbPort', 
                        'profit_dash_db', 
                        'product_cost_period', 
                        '$dbUser', 
                        '$dbPassword',
                        '%s' 
                    ) as pcp
                    INNER JOIN postgresql(
                        '$dbHost:$dbPort', 
                        'profit_dash_db', 
                        'product_cost_item', 
                        '$dbUser', 
                        '$dbPassword',
                        '%s' 
                    ) as pci ON (
                        pci.product_cost_period_id = pcp.id
                    )
                    GROUP BY pcp.id
                ) as cog ON (
                    cog.marketplace_id = t1.marketplace_id 
                    AND 
                    cog.seller_sku = t1.seller_sku 
                    AND 
                    cog.seller_id = t1.seller_id 
                )
                LEFT JOIN (
                    SELECT 
                        order_id,
                        sku
                    FROM
                    postgresql(
                        '$dbHost:$dbPort', 
                        'profit_dash_db', 
                        'fba_return', 
                        '$dbUser', 
                        '$dbPassword',
                        '%s' 
                    )
                    WHERE detailed_disposition = '" . FbaReturn::DETAILS_DISPOSITION_SELLABLE . "'
                    OR status IN ('" . FbaReturn::STATUS_REIMBURSED . "', '" . FbaReturn::STATUS_REPACKAGED_SUCCESSFULLY . "')
                ) as fba_return ON (fba_return.sku = t1.seller_sku AND fba_return.order_id = t1.amazon_order_id)
                LEFT JOIN (
                    SELECT 
                        id,
                        COALESCE(is_vcs_enabled, 0) as is_vcs_enabled
                    FROM
                    postgresql(
                        '$dbHost:$dbPort', 
                        'profit_dash_db', 
                        'seller', 
                        '$dbUser', 
                        '$dbPassword',
                        'public'
                    )
                ) as s ON (t1.seller_id = s.id)
                WHERE (cog.date_start IS NULL OR cog.date_start <= t1.posted_date)
                AND (cog.date_end IS NULL OR cog.date_end > t1.posted_date)
                AND quantity > 0
                AND NULLIF(o.order_id, '') IS NOT NULL
                AND o.created_at <= now() - interval 3 HOUR
                AND t1.created_at <= now() - interval 30 MINUTE
                GROUP BY t1.amazon_order_id, t1.seller_sku, t1.posted_date
                HAVING abs(cog_actual - cog_expected) > {$allowedAmountDiff}
                OR abs(shipping_costs_actual - shipping_costs_expected) > {$allowedAmountDiff}
                OR abs(other_fees_actual - other_fees_expected) > {$allowedAmountDiff}
                OR abs(vat_actual - vat_expected) > {$allowedAmountDiff}
                OR abs(cog_actual - cog_actual_from_eur) > {$allowedAmountDiff}
                LIMIT 1000
            ",
                $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER),
                $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER),
                $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER)
            );

            $data = $customerDb->createCommand($sql)->queryAll();
            $occurrences = [];

            foreach ($data as $datum) {
                $issues = [];
                if (abs($datum['vat_actual'] - $datum['vat_expected']) > $allowedAmountDiff) {
                    $issues['VAT'] = [
                        'expected' => $datum['vat_expected'],
                        'actual' => $datum['vat_actual'],
                    ];
                }

                if (abs($datum['cog_actual'] - $datum['cog_expected']) > $allowedAmountDiff) {
                    $issues['COG'] = [
                        'expected' => $datum['cog_expected'],
                        'actual' => $datum['cog_actual'],
                    ];
                }

                if (abs($datum['cog_actual'] - $datum['cog_actual_from_eur']) > $allowedAmountDiff) {
                    $issues['COG wrong conversion to EUR'] = [
                        'cog_actual' => $datum['cog_actual'],
                        'cog_actual_from_eur' =>  $datum['cog_actual_from_eur'],
                    ];
                }

                if (abs($datum['shipping_costs_actual'] - $datum['shipping_costs_expected']) > $allowedAmountDiff) {
                    $issues['Shipping costs'] = [
                        'expected' => $datum['shipping_costs_expected'],
                        'actual' => $datum['shipping_costs_actual'],
                    ];
                }

                if (abs($datum['other_fees_actual'] - $datum['other_fees_expected']) > $allowedAmountDiff) {
                    $issues['Other fees'] = [
                        'expected' => $datum['other_fees_expected'],
                        'actual' => $datum['other_fees_actual'],
                    ];
                }

                $occurrences[] = [
                    'marketplace_id' => $datum['marketplace_id'],
                    'seller_id' => $datum['seller_id'],
                    'seller_sku' => $datum['seller_sku'],
                    'amazon_order_id' => $datum['amazon_order_id'],
                    'order_price_actual_refund' => $datum['order_price_actual_refund'],
                    'order_price_actual' => $datum['order_price_actual'],
                    'facilitator_quantity' => $datum['facilitator_quantity'],
                    'is_business_order' => $datum['is_business_order'],
                    'is_vcs_enabled' => $datum['is_vcs_enabled'],
                    'issues' => $issues,
                ];
            }

            $issue->countOccurrences += count($occurrences);
            $issue->examples = array_merge(
                $issue->examples,
                array_slice($occurrences, 0, 10)
            );
        }

        return $issue;
    }
}
