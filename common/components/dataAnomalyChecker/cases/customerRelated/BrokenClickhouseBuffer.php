<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBuffer;

class BrokenClickhouseBuffer implements CustomerRelatedCaseInterface
{
    use HelperTrait;
    use ExtraFiltersTrait;

    protected const DIFF_PCT_THRESHOLD = 10;

    public function getDescription(): string
    {
        return 'checking that clickhouse buffer tables correct flushes data to target tables';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return false;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        $countInBuffer = TransactionBuffer::find()->count();
        $countInTargetTable = Transaction::find()->count();

        if ($countInBuffer == 0 && $countInTargetTable == 0) {
            $diffPct = 0;
        } else if ($countInBuffer == 0 && $countInTargetTable > 0) {
            $diffPct = 100;
        } else {
            $diffPct = abs(($countInTargetTable * 100) / $countInBuffer - 100);
        }

        if ($diffPct <= self::DIFF_PCT_THRESHOLD) {
            return $issue;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $clickhouseCustomerDb = $dbManager->getClickhouseCustomerDb();

        // Try to flush buffer - we receive exception if it is broken
        try {
            $clickhouseCustomerDb->createCommand("OPTIMIZE1 TABLE " . TransactionBuffer::tableName())->execute();
        } catch (\Throwable $e) {
            $issue->countOccurrences++;
            $issue->examples[] = [
                'countOrdersInClickhouse' => $countInBuffer,
                'countInTargetTable' => $countInTargetTable,
                'diffPct' => round($diffPct, 2),
            ];
        }

        return $issue;
    }
}
