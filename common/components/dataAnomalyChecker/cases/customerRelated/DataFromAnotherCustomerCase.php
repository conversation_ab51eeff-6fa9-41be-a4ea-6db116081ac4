<?php

namespace common\components\dataAnomalyChecker\cases\customerRelated;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\Transaction;
use common\models\Seller;

class DataFromAnotherCustomerCase implements CustomerRelatedCaseInterface
{
    public function getDescription(): string
    {
        return 'checking that there are no data from another customer sellers';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return false;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;
        $tableName = Transaction::tableName();

        $sellerIdsFromTransactions = $dbManger->getClickhouseCustomerDb()->createCommand("
            SELECT distinct SellerId
            FROM {$tableName} 
        ")->queryColumn() ?? [];

        $sellerIdsFromAnotherCustomers = Seller::find()
            ->select('id, customer_id')
            ->where([
                'AND',
                ['NOT IN', 'customer_id', $dbManger->getCustomerId()],
                ['IN', 'id', $sellerIdsFromTransactions]
            ])
            ->asArray()
            ->all();

        if (!empty($sellerIdsFromAnotherCustomers)) {
            $issue->countOccurrences += count($sellerIdsFromAnotherCustomers);
            $issue->examples = $sellerIdsFromAnotherCustomers;
        }

        return $issue;
    }
}