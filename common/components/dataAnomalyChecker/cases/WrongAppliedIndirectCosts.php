<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\IndirectCost;
use Cron\CronExpression;

class WrongAppliedIndirectCosts implements CaseInterface
{
    use HelperTrait;
    public function getDescription(): string
    {
        return 'checking how correct indirect costs have been applied';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var IndirectCost[] $indirectCosts */
        $indirectCosts = IndirectCost::find()->all();

        $costsFromPostgres = [];
        $startDate = new \DateTime('-2 year');

        foreach ($this->iterateDates($startDate, '+1 day', false) as $dates) {
            foreach ($indirectCosts as $indirectCost) {
                $currDate = date('Y-m-d 00:00:00', strtotime($dates['toDateTime']));
                $dateKey = date('Y-m-d', strtotime($currDate));
                if (strtotime($currDate) > time()) {
                     continue;
                }

                if (!empty($indirectCost->date_start) && strtotime($indirectCost->date_start) > strtotime($currDate)) {
                    continue;
                }

                if (!empty($indirectCost->date_end) && strtotime($indirectCost->date_end) < strtotime($currDate)) {
                    continue;
                }

                if (empty($indirectCost->cron_expr)) {
                    $costsFromPostgres[$indirectCost->id][$dateKey] += $indirectCost->amount;
                    continue;
                }

                $cronExpr = str_replace('?', '*', $indirectCost->cron_expr);
                $expression = new CronExpression($cronExpr);

                if (!$expression->isDue($currDate)) {
                    continue;
                }
                $costsFromPostgres[$indirectCost->id][$dateKey] += $indirectCost->amount;
            }
        }

        if (empty($costsFromPostgres)) {
            return $issue;
        }

        $customerComponent = \Yii::$app->customerComponent;
        $moneyAccuracy = $customerComponent->getMoneyAccuracy();
        $query = Transaction::find()
            ->select([
                'IndirectCostId as indirect_cost_id',
                "sum(Amount * -1) / $moneyAccuracy as amount",
                'toDate(PostedDate) as date',
            ])
            ->where(['>', 'IndirectCostId', 0])
            ->andWhere(['!=', 'Amount', 0])
            ->groupBy('IndirectCostId, toDate(PostedDate)')
            ->orderBy('toDate(PostedDate)')
            ->asArray()
        ;
        $results = $query->all();
        $costsFromClickhouse = [];

        foreach ($results as $result) {
            $costsFromClickhouse[$result['indirect_cost_id']][$result['date']] += $result['amount'];
        }

        $diffAll = [];

        foreach ($costsFromPostgres as $indirectCostId => $costFromPostgres) {
            $costFromClickhouse = $costsFromClickhouse[$indirectCostId] ?? [];

            foreach ($costFromPostgres as $date => $expectedAmount) {
                $actualAmount = $costFromClickhouse[$date] ?? 0;

                if ((float)$actualAmount !== (float)$expectedAmount) {
                    $diffAll[] = [
                        'indirect_cost_id' => $indirectCostId,
                        'date' => $date,
                        'expected' => $expectedAmount,
                        'actual' => $actualAmount,
                    ];
                }
            }
        }

        foreach ($costsFromClickhouse as $indirectCostId => $costFromClickhouse) {
            $costFromPostgres = $costsFromPostgres[$indirectCostId] ?? [];

            foreach ($costFromClickhouse as $date => $actualAmount) {
                $expectedAmount = $costFromPostgres[$date] ?? 0;

                if ($expectedAmount === 0) {
                    $diffAll[] = [
                        'indirect_cost_id' => $indirectCostId,
                        'date' => $date,
                        'expected' => $expectedAmount,
                        'actual' => $actualAmount,
                    ];
                }
            }
        }

        $issue->countOccurrences = count($diffAll);
        $issue->examples = $diffAll;

        return $issue;
    }
}