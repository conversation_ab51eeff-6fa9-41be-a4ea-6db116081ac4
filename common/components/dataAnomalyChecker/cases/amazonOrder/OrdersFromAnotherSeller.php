<?php

namespace common\components\dataAnomalyChecker\cases\amazonOrder;

use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\cases\HowToFixAwareInterface;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\components\services\order\TransferOrderService;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;

class OrdersFromAnotherSeller implements CaseInterface, HowToFixAwareInterface
{
    use HelperTrait;
    public function getDescription(): string
    {
        return 'checking for orders from report assigned to wrong customer';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var Seller[] $sellers */
        $sellers = Seller::find()->where([
            'customer_id' => $dbManager->getCustomerId(),
            'is_analytic_active' => true
        ])->all();

        if (count($sellers) <= 1) {
            return $issue;
        }

        $alreadyCheckedPairs = [];

        foreach ($sellers as $seller1) {
            $dbManager->setSellerId($seller1->id);
            $s1TableName = $dbManager->getSchemaName('order') . '.amazon_order_view';

            foreach ($sellers as $seller2) {
                if ($seller1->id === $seller2->id) {
                    continue;
                }
                if (in_array([$seller1->id, $seller2->id], $alreadyCheckedPairs)) {
                    continue;
                }
                $alreadyCheckedPairs[] = [$seller1->id, $seller2->id];
                $alreadyCheckedPairs[] = [$seller2->id, $seller1->id];

                $this->info("Comparing seller $seller1->id and $seller2->id");
                foreach ($this->iterateDates(null, '+3 months') as $dates) {
                    $fromDateTime = $dates['fromDateTime'];
                    $toDateTime = $dates['toDateTime'];

                    $dbManager->setSellerId($seller2->id);
                    $s2TableName = $dbManager->getSchemaName('order') . '.amazon_order_view';

                    $sql = "
                        SELECT 
                            aoi.order_id, 
                            aoi.order_status as order_status_1, 
                            aoi2.order_status as order_status_2, 
                            aoi.order_item_id as order_item_id_1, 
                            aoi2.order_item_id as order_item_id_2,
                            aoi.created_at as created_at_1, 
                            aoi2.created_at as created_at_2,
                            aoi.order_purchase_date as order_purchase_date_1,
                            aoi2.order_purchase_date as order_purchase_date_2
                        FROM {$s1TableName} aoi
                        INNER JOIN {$s2TableName} aoi2 ON (aoi.order_id = aoi2.order_id)
                        WHERE aoi.order_purchase_date >= '$fromDateTime' 
                        AND aoi.order_purchase_date < '{$toDateTime}'
                        LIMIT 10000
                    ";

                    $results = \Yii::$app->slaveDb->createCommand($sql)->queryAll();

                    if (empty($results)) {
                        continue;
                    }

                    $examples = [];

                    foreach ($results as $result) {
                        $examples[] = [
                            $seller1->id => [
                                'order_id' => $result['order_id'],
                                'order_status' => $result['order_status_1'],
                                'order_item_id' => $result['order_item_id_1'],
                                'created_at' => $result['created_at_1'],
                                'order_purchase_date' => $result['order_purchase_date_1'],
                            ],
                            $seller2->id => [
                                'order_id' => $result['order_id'],
                                'order_status' => $result['order_status_2'],
                                'order_item_id' => $result['order_item_id_2'],
                                'created_at' => $result['created_at_2'],
                                'order_purchase_date' => $result['order_purchase_date_2']
                            ],
                        ];
                    }
                    $issue->countOccurrences += count($results);
                    $issue->examples = array_merge(
                        $issue->examples,
                        array_slice($examples, 0, 5)
                    );
                }
            }
        }

        return $issue;
    }

    public function fix(Anomaly $anomaly): void
    {
        $this->info("Fixing wrong assigned report orders");
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $sellers = Seller::find()->where([
            'customer_id' => $dbManager->getCustomerId(),
            'is_analytic_active' => true
        ])->all();

        foreach ($sellers as $seller1) {
            $dbManager->setSellerId($seller1->id);
            $s1TableName = $dbManager->getSchemaName('order') . '.amazon_order_view';

            foreach ($sellers as $seller2) {
                if ($seller1->id === $seller2->id) {
                    continue;
                }

                $this->info("Comparing seller $seller1->id and $seller2->id");
                foreach ($this->iterateDates(null, '+3 months') as $dates) {
                    $fromDateTime = $dates['fromDateTime'];
                    $toDateTime = $dates['toDateTime'];

                    $dbManager->setSellerId($seller2->id);
                    $s2TableName = $dbManager->getSchemaName('order') . '.amazon_order_view';

                    $sql = "
                        SELECT DISTINCT aoi.order_id
                        FROM {$s1TableName} aoi
                        INNER JOIN {$s2TableName} aoi2 ON (aoi.order_id = aoi2.order_id)
                        WHERE aoi.order_purchase_date >= '$fromDateTime'
                        AND aoi.order_purchase_date < '{$toDateTime}'
                        AND aoi2.order_item_id LIKE 'REPORT_%'
                        AND aoi2.order_period_id = 0
                        LIMIT 10000
                    ";

                    $orderIds = \Yii::$app->slaveDb->createCommand($sql)->queryColumn();

                    if (empty($orderIds)) {
                        continue;
                    }

                    $this->info(sprintf("Removing %d orders and their items", count($orderIds)));
                    $transaction = AmazonOrderItem::getDb()->beginTransaction();
                    try {
                        AmazonOrderItem::deleteAll([
                            'AND',
                            ['IN', 'order_id', $orderIds],
                            ['LIKE', 'order_item_id', 'REPORT_%'],
                        ]);
                        AmazonOrder::deleteAll([
                            'AND',
                            ['IN', 'amazon_order_id', $orderIds]
                        ]);
                        $transaction->commit();
                        $anomaly->countFixed += count($orderIds);
                    } catch (\Throwable $e) {
                        $transaction->rollBack();
                        $this->error($e);
                    }
                }
            }
        }

        (new TransferOrderService($dbManager->getCustomerId()))->reinit();
    }
}