<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\Transaction;
use common\models\finance\Shipment;
use common\models\Seller;

class MissedEventPeriodInClickhouse implements CaseInterface
{
    use HelperTrait;

    public function getDescription(): string
    {
        return 'checking that all event periods have been moved to clickhouse';
    }

    public function getOccurrencesThreshold(): int
    {
        return 10;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;

        /** @var Seller[] $sellers */
        $sellers = Seller::find()->where([
            'customer_id' => $dbManger->getCustomerId(),
            'is_analytic_active' => true
        ])->all();

        $config = $dbManger->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUsername = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        foreach ($sellers as $seller) {
            $dbManger->setSellerId($seller->id);

            foreach ($this->iterateDates() as $dates) {
                $fromDateTime = $dates['fromDateTime'];
                $toDateTime = $dates['toDateTime'];

                $transactionTableName = Transaction::tableName();

                list($schemaName) = explode('.', Shipment::tableName());

                $sql = "
                    SELECT *
                    FROM (
                        SELECT id
                        FROM postgresql(
                            '$dbHost:$dbPort',
                            'profit_dash_db',
                            'event_period',
                            '$dbUsername',
                            '$dbPassword',
                            '$schemaName'
                        )
                        WHERE clickhouse_status = 'moved'
                        AND finish_date < '$toDateTime'
                        AND start_date >= '$fromDateTime'
                        AND has_transactions = true
                    )
                    WHERE id NOT IN (
                        SELECT DISTINCT EventPeriodId
                        FROM $transactionTableName
                        WHERE TransactionDate >= '$fromDateTime'
                        AND TransactionDate <= '$toDateTime'
                    )
                    LIMIT 10000
                ";

                $result = $dbManger->getClickhouseCustomerDb()->createCommand($sql)->queryAll();

                if (empty($result)) {
                    continue;
                }

                $issue->countOccurrences += count($result);
                $issue->examples[] = [
                    'seller_id' => $seller->id,
                    'count_missed' => count($result),
                    'events' => [
                        array_slice($result, 0, 5)
                    ]
                ];

                if ($issue->countOccurrences >= $this->getOccurrencesThreshold() * 3) {
                    break;
                }
            }
        }

        return $issue;
    }
}
