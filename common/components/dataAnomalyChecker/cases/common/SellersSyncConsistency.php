<?php

namespace common\components\dataAnomalyChecker\cases\common;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CaseInterface;
use common\components\dataAnomalyChecker\cases\HelperTrait;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\ads\AmazonAdsAccount;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\Seller;
use yii\db\Expression;
use yii\db\Query;

class SellersSyncConsistency implements CaseInterface
{
    use HelperTrait;
    use ExtraFiltersTrait;

    public function getDescription(): string
    {
        return 'checking that all sellers correctly synced with common project';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $repricerSellers = $this->getRepriceerSellers();
        $basSellers = $this->getBASSellers();

        $analyticActiveCustomerIds = [];
        $problemCustomerIds = [];

        foreach ($repricerSellers as $repricerSeller) {
            $basSeller = $basSellers[$repricerSeller['id']] ?? null;
            $basIsActive = $basSeller['is_active'] ?? null;
            $basIsAnalyticActive = $basSeller['is_analytic_active'] ?? null;
            $repricerIsActive = (int)$repricerSeller['is_active'];
            $repricerIsAnalyticActive = (int)$repricerSeller['is_analytic_active'];
            $analyticActiveCustomerIds[] = $repricerSeller['customer_id'];

            if ($repricerIsActive === $basIsActive && $repricerIsAnalyticActive === $basIsAnalyticActive) {
                continue;
            }

            $issue->countOccurrences++;
            $issue->examples[] = [
                'customer_id' => $repricerSeller['customer_id'],
                'seller_id' => $repricerSeller['id'],
                'is_active' => $repricerIsActive,
                'is_analytic_active' => $repricerIsAnalyticActive,
                'bas_is_active' => $basIsActive,
                'bas_is_analytic_active' => $basIsAnalyticActive,
            ];
            $problemCustomerIds[] = $repricerSeller['customer_id'];
        }
        $analyticActiveCustomerIds = array_unique($analyticActiveCustomerIds);

        $repricerAmazonAdsAccounts = $this->getRepricerAmazonAdsAccounts($analyticActiveCustomerIds);

        foreach ($repricerAmazonAdsAccounts as $repricerAmazonAdsAccount) {
            try {
                $dbManager->setCustomerId($repricerAmazonAdsAccount['customer_id']);
            } catch (\Throwable $e) {
                continue;
            }
            $this->info(sprintf('Comparison customer %d amazon ads account %d',
                $dbManager->getCustomerId(),
                $repricerAmazonAdsAccount['id'],
            ));

            try {
                $basAmazonAdsAccount = AmazonAdsAccount::find()->where([
                    'id' => $repricerAmazonAdsAccount['id']
                ])->asArray()->one();
            } catch (\Throwable $e) {
                $this->error($e);
                continue;
            }

            $basIsActive = (int)($basAmazonAdsAccount['is_active'] ?? 0);
            $repricerIsActive = (int)$repricerAmazonAdsAccount['is_active'];
            $basIsDeleted = empty($basAmazonAdsAccount) ? 1 : 0;
            $repricerIsDeleted = (int)$repricerAmazonAdsAccount['is_deleted'];

            if (
                ($basIsDeleted && $repricerIsDeleted)
                || ($basIsDeleted === $repricerIsDeleted && $basIsActive === $repricerIsActive)
            ) {
                $this->info('Ok');
                continue;
            }

            $issue->countOccurrences++;
            $issue->examples[] = [
                'customer_id' => $dbManager->getCustomerId(),
                'amazon_ads_account_id' => $repricerAmazonAdsAccount['id'],
                'is_active' => $repricerIsActive,
                'bas_is_active' => $basIsActive,
                'id_deleted' => $repricerIsDeleted,
                'bas_is_deleted' => $basIsDeleted,
            ];
            $problemCustomerIds[] = $dbManager->getCustomerId();
        }

        $problemCustomerIds = array_unique($problemCustomerIds);
        sort($problemCustomerIds);
        $issue->customerId = implode(',', $problemCustomerIds);

        return $issue;
    }

    protected function getRepriceerSellers(): array
    {
        return (new Query)
            ->select([
                'aca.sellerId as id',
                'ca.customer_id as customer_id',
                new Expression('IF (aca.use_bas_module, 1, 0) as is_active'),
                new Expression('IF ((aca.use_bas_module || aca.use_repricer_module), 1, 0) as is_analytic_active')
            ])
            ->from('repricer_main_db.amazon_customer_account aca')
            ->leftJoin('repricer_main_db.customer_account ca', 'ca.id = customer_account_id')
            ->leftJoin('repricer_main_db.customer c', 'c.id = ca.customer_id')
            ->where([
                'AND',
                [
                    'OR',
                    ['aca.use_bas_module' => 1],
                    ['aca.use_repricer_module' => 1]
                ],
                ['not like', 'aca.sellerId', 'DEMO'],
                ['!=', 'aca.sellerId', 'BASIC'],
                ['=', 'c.status', 'active'],
                ['!=', 'c.repricer_locked_by_empty_payment', 1]
            ])
            ->indexBy('id')
            ->all(\Yii::$app->dbManager->getRepricerMainDb());
    }

    protected function getBASSellers(): array
    {
        return Seller::find()
            ->select([
                'id',
                new Expression('is_active::int'),
                new Expression('is_analytic_active::int'),
            ])
            ->indexBy('id')
            ->asArray()
            ->all();
    }

    protected function getRepricerAmazonAdsAccounts(array $analyticActiveCustomerIds): array
    {
        return (new Query)
            ->select([
                'aac.id',
                'aac.customer_id',
                'aac.is_deleted',
                'aac.is_ba_active as is_active'
            ])
            ->from('repricer_main_db.amazon_ads_account aac')
            ->leftJoin('repricer_main_db.customer c', 'c.id = aac.customer_id')
            ->where([
                'AND',
                ['in', 'c.id', $analyticActiveCustomerIds],
            ])
            ->orderBy('aac.customer_id')
            ->indexBy('id')
            ->all(\Yii::$app->dbManager->getRepricerMainDb());
    }
}
