<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\order\AmazonOrderView;
use common\models\Seller;

class OrdersAmountInconsistency implements CaseInterface
{
    use HelperTrait;
    use ExtraFiltersTrait;

    protected const PERIOD_TO_CHECK = '-6 month';
    protected const DIFF_PCT_THRESHOLD = 5;

    public function getDescription(): string
    {
        return 'checking that orders in postgres are consistent with orders in clickhouse';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return false;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;

        $sellers = Seller::find()
            ->select('id, customer_id')
            ->where([
                'AND',
                ['customer_id' => $dbManger->getCustomerId()],
                ['is_analytic_active' => true]
            ])
            ->asArray()
            ->all();

        $countOrdersInClickhouse = AmazonOrderExtendedView::find()
            ->andWhere(['>', 'order_purchase_date', (new \DateTime(self::PERIOD_TO_CHECK))->format('Y-m-d H:i:s')])
            ->count();
        $inProgressQuery = \common\models\customer\clickhouse\AmazonOrderInProgressExtendedView::find()
            ->andWhere(['>', 'order_purchase_date', (new \DateTime(self::PERIOD_TO_CHECK))->format('Y-m-d H:i:s')]);
        $this->applyClickhouseLatestVersion($inProgressQuery);
        $countOrdersInClickhouse += $inProgressQuery->count();
        $countOrdersInPostgres = 0;

        foreach ($sellers as $seller) {
            $dbManger->setSellerId($seller['id']);
            $countOrdersInPostgres  += AmazonOrderView::find()
                ->andWhere(['>', 'order_purchase_date', (new \DateTime(self::PERIOD_TO_CHECK))->format('Y-m-d H:i:s')])
                ->count();
        }

        // Avoiding division by zero exception
        if ($countOrdersInPostgres === 0 && $countOrdersInClickhouse === 0) {
            $diffPct = 0;
        } else if ($countOrdersInPostgres === 0 && $countOrdersInClickhouse > 0) {
            $diffPct = 100;
        } else {
            $diffPct = abs(($countOrdersInClickhouse * 100) / $countOrdersInPostgres - 100);
        }

        if ($diffPct >= self::DIFF_PCT_THRESHOLD) {
            $issue->countOccurrences++;
            $issue->examples[] = [
                'countOrdersInClickhouse' => $countOrdersInClickhouse,
                'countOrdersInPostgres' => $countOrdersInPostgres,
                'diffPct' => round($diffPct, 2),
            ];
        }

        return $issue;
    }
}
