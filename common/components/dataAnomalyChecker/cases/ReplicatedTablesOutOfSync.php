<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\dto\Anomaly;

class ReplicatedTablesOutOfSync implements CaseInterface
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getDescription(): string
    {
        return 'materialized view out of sync on clickhouse nodes';
    }

    public function getOccurrencesThreshold(): int
    {
        return 1;
    }

    public function isOnlyForActiveCustomers(): bool
    {
        return true;
    }

    public function check(): Anomaly
    {
        $issue = new Anomaly();
        $dynamicTables = (new DynamicTablesManager())->getRegisteredDynamicTables();
        $masterNodes = $this->dbManager->getClickhouseCustomerMasterNodes(false);
        $foundCases = [];

        Command::$isNodeChangingEnabled = false;
        foreach ($dynamicTables as $dynamicTable) {
            if ($dynamicTable->getType() !== DynamicTablesManager::TYPE_VIEW) {
                continue;
            }

            $tableName = $dynamicTable->getName();
            $prevNodeValue = null;

            // Comparing table structure on different nodes.
            // If one of them has difference - that's a problem we are looking for.
            foreach ($masterNodes as $node) {
                $sql = "SHOW CREATE TABLE $tableName";
                $showCreateTableStr = $this->dbManager->getDb('customer', 'clickhouse', $node)
                    ->createCommand($sql)
                    ->queryScalar();

                if (!empty($prevNodeValue && $showCreateTableStr !== $prevNodeValue)) {
                    $foundCases[] = [
                        'node' => $node,
                        'table' => $tableName
                    ];
                }

                $prevNodeValue = $showCreateTableStr;
            }
        }
        Command::$isNodeChangingEnabled = true;

        $issue->countOccurrences = count($foundCases);
        $issue->examples = $foundCases;

        return $issue;
    }
}