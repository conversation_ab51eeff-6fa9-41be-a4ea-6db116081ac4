<?php

namespace common\components\dataAnomalyChecker\cases;

use common\components\LogToConsoleTrait;

trait HelperTrait
{
    use LogToConsoleTrait;

    public function iterateDates(\DateTime $dateStart = null, string $iterationStep = '+3 months', bool $writeLogs = true): \Iterator
    {
        if (empty($dateStart)) {
            $dateStart = (new \DateTime())->setTime(0,0)->modify('-1 years');
        }

        while (true) {
            $dateFinish = clone $dateStart;
            $dateFinish->modify($iterationStep)->modify('-1 second');
            $fromDateTime = $dateStart->format('Y-m-d H:i:s');
            $toDateTime = $dateFinish->format('Y-m-d H:i:s');

            if ($writeLogs) {
                $this->info("Checking dates from $fromDateTime to $toDateTime");
            }

            yield [
                'fromDateTime' => $fromDateTime,
                'toDateTime' => $toDateTime
            ];

            if ($dateFinish > new \DateTime()) {
                break;
            }

            $dateStart = $dateFinish;
            $dateStart->modify('+1 second');
        }
    }
}