<?php

namespace common\components\dataAnomalyChecker;

use common\components\core\db\dbManager\DbManager;
use common\components\dataAnomalyChecker\cases\CaseInterface;
use common\components\dataAnomalyChecker\cases\common\SellersSyncConsistency;
use common\components\dataAnomalyChecker\cases\customerRelated\BrokenClickhouseBuffer;
use common\components\dataAnomalyChecker\cases\customerRelated\DataFromAnotherCustomerCase;
use common\components\dataAnomalyChecker\cases\customerRelated\OrdersAmountInconsistency;
use common\components\dataAnomalyChecker\cases\customerRelated\PrincipalAmountConsistency;
use common\components\dataAnomalyChecker\cases\customerRelated\ReplicatedTablesOutOfSync;
use common\components\dataAnomalyChecker\cases\customerRelated\WrongAppliedAmazonAdCosts;
use common\components\dataAnomalyChecker\cases\customerRelated\WrongAppliedIndirectCosts;
use common\components\dataAnomalyChecker\cases\customerRelated\WrongMovedAmazonOrdersToClickhouse;
use common\components\dataAnomalyChecker\cases\CustomerRelatedCaseInterface;
use common\components\dataAnomalyChecker\dto\Anomaly;
use common\components\LogToConsoleTrait;
use common\components\messenger\MessagesSender;

class Checker
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected MessagesSender $messagesSender;

    protected bool $shouldSendBugs = false;

    public function __construct(bool $shouldSendBugs = false)
    {
        $this->shouldSendBugs = $shouldSendBugs;
        $this->dbManager = \Yii::$app->dbManager;
        $this->messagesSender = new MessagesSender();
    }

    public function findCommon(): void
    {
        $cases = [
            new SellersSyncConsistency()
        ];

        /** @var CaseInterface[] $cases */
        foreach ($cases as $case) {
            $this->info("Checking case (" . $case->getDescription() . ")");
            $issue = $case->check();

            if ($issue->countOccurrences === 0 || $issue->countOccurrences < $case->getOccurrencesThreshold()) {
                $this->info("Ok");
                continue;
            }

            $this->handleCheckResult($case, [$issue]);
        }
    }

    public function find(int $fromCustomerId = null, int $toCustomerId = null, bool $isOnlyActiveCustomers = false)
    {
        $cases = [
            new OrdersAmountInconsistency(),
            new ReplicatedTablesOutOfSync(),
            new DataFromAnotherCustomerCase(),
            new WrongAppliedAmazonAdCosts(),
            new WrongMovedAmazonOrdersToClickhouse(),
            new WrongAppliedIndirectCosts(),
            new PrincipalAmountConsistency(),
            new BrokenClickhouseBuffer(),
            // Must be refactored, it seems lead to OOM in some cases.
            // Do not use till be refactored.
//            new IsCustomCostsAppliedCorrectly()
        ];

        /** @var CaseInterface[] $cases */
        foreach ($cases as $case) {
            $issues = [];
            $customerIdsWithBug = [];
            $totalOccurrences = 0;

            foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $fromCustomerId, $toCustomerId) as $schemaInfo) {
                try {
                    if (!$case instanceof CustomerRelatedCaseInterface) {
                        continue;
                    }

                    if (!$this->dbManager->getCustomerId() || $this->dbManager->isDemo()) {
                        continue;
                    }

                    if ($this->dbManager->isActive() != $isOnlyActiveCustomers) {
                        continue;
                    }

                    if ($case->isOnlyForActiveCustomers() && !$this->dbManager->isActive()) {
                        continue;
                    }

                    $this->info("Customer {$this->dbManager->getCustomerId()} (" . $case->getDescription() . ")");
                    $issue = $case->check();
                    $issue->customerId = $this->dbManager->getCustomerId();

                    if ($issue->countOccurrences === 0 || $issue->countOccurrences < $case->getOccurrencesThreshold()) {
                        $this->info("Ok");
                        continue;
                    }
                    $issue->examples = array_slice($issue->examples, 0, 5);
                    $this->info("Found {$issue->countOccurrences} occurrences on customer {$this->dbManager->getCustomerId()}");
                    $this->info($issue->examples);

                    $issues[] = $issue;
                    $customerIdsWithBug[] = $this->dbManager->getCustomerId();
                } catch (\Throwable $e) {
                    $this->info($e);
                }
            }

            $this->handleCheckResult($case, $issues);
        }
    }

    /**
     * @param Anomaly[] $issues
     * @return void
     */
    protected function handleCheckResult(CaseInterface $case, array $issues): void
    {
        $customerIdsWithBug = [];
        $totalOccurrences = 0;

        foreach ($issues as $issue) {
            if ($issue->countOccurrences > 0) {
                $customerIdsWithBug = array_merge(
                    $customerIdsWithBug,
                    explode(',', $issue->customerId)
                );
                $totalOccurrences += $issue->countOccurrences;
            }
        }

        $customerIdsWithBug = array_unique($customerIdsWithBug);
        sort($customerIdsWithBug);

        if (count($customerIdsWithBug) == 0) {
            $this->info('Case checked - all OK');
            return;
        }

        $this->info("Found case occurrences on next customers:");
        $this->info(implode(',', $customerIdsWithBug));

        if ($this->shouldSendBugs) {
            try {
                $issues = array_slice($issues, 0, 100);
                $this->sendNotifications(
                    $case,
                    $issues,
                    $customerIdsWithBug,
                    $totalOccurrences
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    protected function sendNotifications(
        CaseInterface $case,
        array $issues,
        array $customerIdsWithBug,
        int $totalOccurrences
    ): void
    {
        $caseType = 'common';

        if ($case instanceof CustomerRelatedCaseInterface) {
            if ($case->isOnlyForActiveCustomers()) {
                $caseType = 'active';
            } else {
                $caseType = 'analytic_active';
            }
        }

        $this->messagesSender->sendFoundDataInconsistency(
            $case->getDescription(),
            $customerIdsWithBug,
            $totalOccurrences,
            $caseType
        );
        throw new \Exception("Found data anomaly for test case '{$case->getDescription()}'");
    }
}