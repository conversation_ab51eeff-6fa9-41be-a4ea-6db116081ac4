<?php

namespace common\components\amazonAds;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\tables\AdsStatisticsTable;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\BufferFactory;
use common\components\LogToConsoleTrait;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\base\AbstractAdsRecord;
use common\models\ads\SbAd;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\ads\SpAdvertisedProduct;
use common\models\customer\Product;
use common\models\customer\ProductTag;
use common\models\FinanceEventCategory;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

class CostsApplier
{
    use LogToConsoleTrait;

    public const DATA_STABILISATION_TIME_DAYS = 5;

    protected const BATCH_SIZE = 1000;
    public const SPONSORED_DISPLAY_PATH = 'AmazonAds.SponsoredDisplay.transactionValue';
    public const SPONSORED_PRODUCTS_PATH = 'AmazonAds.SponsoredProducts.transactionValue';
    public const SPONSORED_BRANDS_PATH = 'AmazonAds.SponsoredBrands.%s.transactionValue';

    protected DbManager $dbManager;
    protected ClickhouseTransactionExtractor $transactionExtractor;
    protected BufferInterface $transactionsBuffer;
    protected CustomerConfig $customerConfig;
    protected CustomerComponent $customerComponent;
    protected Client $clickhouseClient;
    protected bool $isOnlyStableData = true;
    protected ?string $temporaryTransactionsTableName;

    public function __construct(
        bool $isOnlyStableData = true,
        string $temporaryTransactionsTableName = null
    )
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->transactionExtractor = new ClickhouseTransactionExtractor();
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->isOnlyStableData = $isOnlyStableData;
        $this->temporaryTransactionsTableName = $temporaryTransactionsTableName;
        $this->customerComponent = new CustomerComponent();

        if (!$this->isOnlyStableData && null === $this->temporaryTransactionsTableName) {
            throw new \Exception('Temporary transactions table name should be provided');
        }
    }

    public function apply(int $customerId, string $date = null)
    {
        $this->dbManager->setCustomerId($customerId);
        $this->transactionsBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();
        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        /** @var Client $client */
        $this->clickhouseClient = $connection->getClient();

        $this->info("Applying costs for customer $customerId started");

        $this->applySpAdvertisedProductCosts($date);
        $this->applySdAdvertisedProductCosts($date);
        $this->applySponsoredBrandCosts($date);

        $this->saveToAdsStatistics($date);

        $this->info("Applying costs for customer $customerId finished");
    }

    public function getDatesNotMovedClickhouseCosts(int $customerId): array
    {
        $this->dbManager->setCustomerId($customerId);
        $subQuery1 = SpAdvertisedProduct::find()
            ->select('date')
            ->where(['status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_CREATED])
            ->andWhere(['<=', 'date', date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);

        $subQuery2 = clone $subQuery1;
        $subQuery2->from(SbAdGroupStatistic::tableName());

        $subQuery3 = clone $subQuery1;
        $subQuery3->from(SdAdvertisedProduct::tableName());

        $dates = (new Query())
            ->select('date')
            ->from(
                $subQuery1
                ->union($subQuery2)
                ->union($subQuery3)
            )
            ->column($this->dbManager->getAdsDb());
        $dates = array_unique($dates);
        rsort($dates);

        return $dates;
    }

    protected function saveToAdsStatistics(string $date = null): void
    {
        $this->info("Save to Ads Statistics clickhouse");

        if (!$date) {
            $date = date('Y-m-d');
        }

        $this->info("Processing ads statistics for date: {$date}");

        try {
            $adsStatisticsTable = new AdsStatisticsTable(
                true,
                new \DateTime($date),
                new \DateTime($date)
            );

            $tableName = $adsStatisticsTable->getName();

            $connection = $this->dbManager->getClickhouseCustomerDb();
            $connection->open();

            $this->info("Starting data population for date: {$date}");
            $adsStatisticsTable->populate($tableName);

            $this->info("Successfully saved ads statistics for date: {$date}");

        } catch (\Exception $e) {
            $this->error($e);
        }
    }

    protected function applySpAdvertisedProductCosts(string $date = null): void
    {
        $this->info("Applying Sponsored Products costs");
        try {
            $query = SpAdvertisedProduct::find()
                ->select([
                    'spap.id',
                    'spap.sku',
                    'spap.asin',
                    'spap.date',
                    'spap.cost',
                    'p.id as product_id',
                    'p.stock_type',
                    'p.sku',
                    'p.title',
                    'p.ean',
                    'p.upc',
                    'p.isbn',
                    'p.brand',
                    'p.product_type',
                    'p.manufacturer',
                    'cast(p.adult_product as integer) as adult_product',
                    'p.main_image',
                    'p.parent_asin',
                    'p.age_range',
                    'spap.currency_code',
                    'ap.seller_id',
                    'ap.marketplace_id',
                    'tags as tag_id',
                ])
                ->from(['spap' => SpAdvertisedProduct::tableName()])
                ->where(['is not', 'ap.id', new Expression('NULL')])
                ->leftJoin(AmazonAdsProfile::tableName() . ' ap', 'ap.id = spap.profile_id')
                ->leftJoin(Product::tableName() . ' p', 'p.sku = spap.sku AND p.seller_id = ap.seller_id AND p.marketplace_id = ap.marketplace_id')
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->asArray();

            $this->applyCostsByQuery($query, function ($item) {
                return self::SPONSORED_PRODUCTS_PATH;
            }, null, $date);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    protected function applySdAdvertisedProductCosts(string $date = null): void
    {
        $this->info("Applying Sponsored Display costs");
        try {
            $query = SdAdvertisedProduct::find()
                ->select([
                    'spap.id',
                    'spap.sku',
                    'spap.asin',
                    'p.id as product_id',
                    'p.stock_type',
                    'p.sku',
                    'p.title',
                    'p.ean',
                    'p.upc',
                    'p.isbn',
                    'p.brand',
                    'p.product_type',
                    'p.manufacturer',
                    'cast(p.adult_product as integer) as adult_product',
                    'p.main_image',
                    'p.parent_asin',
                    'p.age_range',
                    'spap.date',
                    'spap.cost',
                    'spap.currency_code',
                    'ap.seller_id',
                    'ap.marketplace_id',
                    'tags as tag_id',
                ])
                ->from(['spap' => SdAdvertisedProduct::tableName()])
                ->where(['is not', 'ap.id', new Expression('NULL')])
                ->leftJoin(AmazonAdsProfile::tableName() . ' ap', 'ap.id = spap.profile_id')
                ->leftJoin(Product::tableName() . ' p', 'p.sku = spap.sku AND p.seller_id = ap.seller_id AND p.marketplace_id = ap.marketplace_id')
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->asArray();

            $this->applyCostsByQuery($query, function ($item) {
                return self::SPONSORED_DISPLAY_PATH;
            }, null, $date);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    protected function applySponsoredBrandCosts(string $date = null): void
    {
        $this->info("Applying Sponsored Brands costs");

        try {
            $query = SbAdGroupStatistic::find()
                ->select([
                    'sbags.id as sb_group_statistic_id',
                    'sbags.date',
                    'sba.type',
                    new Expression('COALESCE(max(sbags.cost), 0) as cost'),
                    'coalesce(sbags.currency_code, aap.currency_code) as currency_code',
                    'aap.seller_id',
                    'aap.marketplace_id',
                    new Expression("array_to_string(array_agg(coalesce(p.id::text, 'null')), '||') as product_ids"),
                    new Expression("array_to_string(array_agg(coalesce(p.product_type, 'null')), '||') as product_types"),
                    new Expression("array_to_string(array_agg(coalesce(p.stock_type, 'null')), '||') as stock_types"),
                    new Expression("array_to_string(array_agg(coalesce(p.title, 'null')), '||') as titles"),
                    new Expression("array_to_string(array_agg(coalesce(p.ean, 'null')), '||') as eans"),
                    new Expression("array_to_string(array_agg(coalesce(p.upc, 'null')), '||') as upcs"),
                    new Expression("array_to_string(array_agg(coalesce(p.isbn, 'null')), '||') as isbns"),
                    new Expression("array_to_string(array_agg(coalesce(p.brand, 'null')), '||') as brands"),
                    new Expression("array_to_string(array_agg(coalesce(p.manufacturer, 'null')), '||') as manufacturers"),
                    new Expression("array_to_string(array_agg(CASE WHEN p.adult_product IS NULL THEN 'null' WHEN p.adult_product = true THEN '1' ELSE '0' END), '||') as adult_products"),
                    new Expression("array_to_string(array_agg(coalesce(p.main_image, 'null')), '||') as main_images"),
                    new Expression("array_to_string(array_agg(coalesce(p.parent_asin, 'null')), '||') as parent_asins"),
                    new Expression("array_to_string(array_agg(coalesce(p.age_range, 'null')), '||') as age_ranges"),
                    new Expression("array_to_string(array_agg(p.sku), '||') as skus"),
                    new Expression("array_to_string(array_agg(p.asin), '||') as asins"),
                    new Expression("array_to_string(array_agg(coalesce(pt.tags, 'null')), '||') as tags"),
                ])
                ->where(['is not', 'aap.id', new Expression('NULL')])
                ->from(['sbags' => SbAdGroupStatistic::tableName()])
                ->leftJoin(SbAd::tableName() . ' sba', 'sba.ad_group_id = sbags.ad_group_id')
                ->leftJoin(AmazonAdsProfile::tableName() . ' aap', 'aap.id = sbags.profile_id')
                ->leftJoin(
                    Product::tableName() . ' p',
                    'p.seller_id = aap.seller_id AND p.marketplace_id = aap.marketplace_id AND p.asin = sba.asin'
                )
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->groupBy('sbags.id, sbags.date, sba.type, sbags.currency_code, aap.currency_code, aap.seller_id, aap.marketplace_id')
                ->orderBy('sbags.id ASC') // need to prevent duplicates
                ->asArray();

            $this->applyCostsByQuery(
                $query,
                function ($item) {
                    return sprintf(self::SPONSORED_BRANDS_PATH, $item['type']);
                },
                function ($item) {
                    $items = [];
                    $productIds = explode('||', $item['product_ids']);
                    $skus = explode('||', $item['skus']);
                    $asins = explode('||', $item['asins']);
                    $stockTypes = explode('||', $item['stock_types']);
                    $productTypes = explode('||', $item['product_types']);
                    $titles = explode('||', $item['titles']);
                    $eans = explode('||', $item['eans']);
                    $upcs = explode('||', $item['upcs']);
                    $isbns = explode('||', $item['isbns']);
                    $brands = explode('||', $item['brands']);
                    $manufacturers = explode('||', $item['manufacturers']);
                    $adultProducts = explode('||', $item['adult_products']);
                    $mainImages = explode('||', $item['main_images']);
                    $parentAsins = explode('||', $item['parent_asins']);
                    $ageRanges = explode('||', $item['age_ranges']);
                    $costPerSku = $item['cost'] / count($skus);
                    $tags = explode('||', $item['tags']);

                    foreach ($skus as $k => $sku) {
                        $items[] = [
                            'id' => $item['sb_group_statistic_id'],
                            'date' => $item['date'],
                            'cost' => $costPerSku,
                            'type' => $item['type'],
                            'currency_code' => $item['currency_code'],
                            'seller_id' => $item['seller_id'],
                            'marketplace_id' => $item['marketplace_id'],
                            'sku' => $sku,
                            'asin' => $asins[$k] == 'nul' ? null : $asins[$k],
                            'product_id' => $productIds[$k] == 'null' ? null : $productIds[$k],
                            'title' => $titles[$k] == 'null' ? null : $titles[$k],
                            'ean' => $eans[$k] == 'null' ? null : $eans[$k],
                            'upc' => $upcs[$k] == 'null' ? null : $upcs[$k],
                            'isbn' => $isbns[$k] == 'null' ? null : $isbns[$k],
                            'brand' => $brands[$k] == 'null' ? null : $brands[$k],
                            'product_type' => $productTypes[$k] == 'null' ? null : $productTypes[$k],
                            'stock_type' => $stockTypes[$k] == 'null' ? null : $stockTypes[$k],
                            'manufacturer' => $manufacturers[$k] == 'null' ? null : $manufacturers[$k],
                            'adult_product' => $adultProducts[$k] == 'null' ? null : (int) $adultProducts[$k],
                            'main_image' => $mainImages[$k] == 'null' ? null : $mainImages[$k],
                            'parent_asin' => $parentAsins[$k] == 'null' ? null : $parentAsins[$k],
                            'age_range' => $ageRanges[$k] == 'null' ? null : $ageRanges[$k],
                            'tag_id' => $tags[$k] === 'null' ? null : $tags[$k],
                        ];
                    }

                    return $items;
                },
                $date
            );
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    protected function generateTransactionsForPPCItems(array $items, callable $generatePathFn): array
    {
        $transactions = [];
        // Need to prevent data loss when flushing buffer
        $eventPeriodId = random_int(1, 10000000);
        foreach ($items as $item) {

            $transaction = $this->transactionExtractor->convertCurrencyItemToTransaction(
                [
                    'CurrencyCode' => $item['currency_code'],
                    'CurrencyAmount' => $item['cost'] * -1,
                ],
                [
                    ClickhouseTransactionExtractor::FACT_EVENT_PERIOD_ID => $eventPeriodId,
                    ClickhouseTransactionExtractor::FACT_SELLER_ID => $item['seller_id'],
                    ClickhouseTransactionExtractor::FACT_MARKETPLACE_ID => $item['marketplace_id'],
                    ClickhouseTransactionExtractor::FACT_SELLER_SKU => $item['sku'],
                    ClickhouseTransactionExtractor::FACT_ASIN => $item['asin'],
                    ClickhouseTransactionExtractor::FACT_POSTED_DATE => $item['date'] . ' 00:00:00',
                    ClickhouseTransactionExtractor::FACT_CATEGORY_PATH => $generatePathFn($item) . FinanceEventCategory::MINUS_POSTFIX,
                ],
                false
            );

            if (!$this->isOnlyStableData) {
                $transaction->EAN = $item['ean'] ?? null;
                $transaction->ISBN = $item['isbn'] ?? null;
                $transaction->UPC = $item['upc'] ?? null;
                $transaction->Title = $item['title'] ?? null;
                $transaction->ProductId = $item['product_id'] ?? null;
                $transaction->ParentASIN = $item['parent_asin'] ?? null;
                $transaction->Brand = $item['brand'] ?? null;
                $transaction->Model = $item['model'] ?? null;
                $transaction->ProductType = $item['product_type'] ?? null;
                $transaction->Manufacturer = $item['manufacturer'] ?? null;
                $transaction->AgeRange = $item['age_range'] ?? null;
                $transaction->AdultProduct = $item['adult_product'] ?? null;
                $transaction->ProductStockType = $item['stock_type'] ?? null;
                $transaction->TagId = $item['tag_id'] ?? null;
            }

            $transactions[] = $transaction;
        }
        return $transactions;
    }

    protected function applyCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null,
        string $date = null
    )
    {
        if ($this->isOnlyStableData) {
            $this->applyStableCostsByQuery($query, $generatePathFn, $convertItemFn, $date);
        } else {
            $this->applyTemporaryCostsByQuery($query, $generatePathFn, $convertItemFn);
        }
    }

    protected function applyTemporaryCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null
    ): void
    {
        $query->andWhere(['>', 'date', date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);
        $query->noCache();

        foreach ($query->batch(500) as $itemsRaw) {
            try {
                $items = [];
                if (null === $convertItemFn) {
                    $items = $itemsRaw;
                } else {
                    foreach ($itemsRaw as $item) {
                        $items = array_merge($items, $convertItemFn($item));
                    }
                }

                if (count($items) === 0) {
                    break;
                }

                $items = $this->filterItems($items);
                $transactions = $this->generateTransactionsForPPCItems($items, $generatePathFn);

                if ($this->customerComponent->isTransactionsFrozen()) {
                    $this->info('Interaction with clickhouse is frozen');
                    $this->transactionsBuffer->remove();
                    return;
                }

                $transactions = json_decode(json_encode($transactions), true);

                if (!empty($transactions)) {
                    Command::$isNodeChangingEnabled = false;
                    $this->clickhouseClient->insertAssocBulk($this->temporaryTransactionsTableName, $transactions);
                    Command::$isNodeChangingEnabled = true;
                }
            } catch (\Throwable $e) {
                Command::$isNodeChangingEnabled = true;
                $this->error($e);
            }
        }
    }

    protected function applyStableCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null,
        string $date = null
    ): void
    {
        $query->andWhere(['=', 'status_moved_to_clickhouse', AbstractAdsRecord::STATUS_CREATED]);
        if ($date !== null) {
            $query->andWhere(['=', 'date', $date]);
        }
        $query->andWhere(['<=', 'date', date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);
        $query->noCache()->limit(self::BATCH_SIZE);

        for ($i = 0; $i < 500; $i++) {
            $itemsRaw = $query->all();

            try {
                $items = [];
                $movedIds = [];

                if (null === $convertItemFn) {
                    $items = $itemsRaw;
                } else {
                    foreach ($itemsRaw as $item) {
                        $items = array_merge($items, $convertItemFn($item));
                    }
                }

                if (count($items) === 0) {
                    break;
                }

                foreach ($items as $k => $item) {
                    $movedIds[$item['id']] = 1;
                }

                $items = $this->filterItems($items);
                $transactions = $this->generateTransactionsForPPCItems($items, $generatePathFn);

                if ($this->customerComponent->isTransactionsFrozen()) {
                    $this->info('Interaction with clickhouse is frozen');
                    $this->transactionsBuffer->remove();
                    return;
                }

                try {
                    if (!empty($transactions)) {
                        $this->transactionsBuffer->put($transactions);
                        $this->transactionsBuffer->flush();
                    }
                } catch (\Throwable $e) {
                    $this->transactionsBuffer->remove();
                    throw $e;
                }
                $this->info("Updating moved_to_clickhouse_at for " . count($movedIds) . " items");
                $this->setStatusMovedToClickhouseInDone(array_keys($movedIds), $query);
                $this->info("Flushing buffer");
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    private function setStatusMovedToClickhouseInDone(array $itemIds, $query)
    {
        $query->modelClass::updateAll(
            [
                'status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_MOVED_TO_CLICHOUSE,
                'moved_to_clickhouse_at' => date('Y-m-d H:i:s'),

            ],
            [
                'id' => $itemIds,
                'status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_CREATED,
            ]
        );
    }

    /**
     * Removes not valid or not wanted items.
     *
     * @param array $items
     * @return array
     */
    protected function filterItems(array $items): array
    {
        return array_filter($items, function ($item) {
            return (!empty($item['sku']) || !empty($item['asin']))  && $item['cost'] > 0;
        });
    }
}
