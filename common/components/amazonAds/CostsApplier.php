<?php

namespace common\components\amazonAds;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\tables\AdsStatisticsTable;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\BufferFactory;
use common\components\LogToConsoleTrait;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\SbAd;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\ads\SpAdvertisedProduct;
use common\models\customer\clickhouse\AdsStatistics;
use common\models\customer\InventoryProductHistory;
use common\models\customer\Product;
use common\models\customer\ProductTag;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

class CostsApplier
{
    use LogToConsoleTrait;

    public const DATA_STABILISATION_TIME_DAYS = 5;

    protected const BATCH_SIZE = 1000;
    public const SPONSORED_DISPLAY_PATH = 'AmazonAds.SponsoredDisplay.transactionValue';
    public const SPONSORED_PRODUCTS_PATH = 'AmazonAds.SponsoredProducts.transactionValue';
    public const SPONSORED_BRANDS_PATH = 'AmazonAds.SponsoredBrands.%s.transactionValue';

    protected DbManager $dbManager;
    protected ClickhouseTransactionExtractor $transactionExtractor;
    protected BufferInterface $transactionsBuffer;
    protected CustomerConfig $customerConfig;
    protected CustomerComponent $customerComponent;
    protected Client $clickhouseClient;
    protected bool $isOnlyStableData = true;
    protected ?string $temporaryTransactionsTableName;

    public function __construct(
        bool $isOnlyStableData = true,
        string $temporaryTransactionsTableName = null
    )
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->transactionExtractor = new ClickhouseTransactionExtractor();
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->isOnlyStableData = $isOnlyStableData;
        $this->temporaryTransactionsTableName = $temporaryTransactionsTableName;
        $this->customerComponent = new CustomerComponent();

        if (!$this->isOnlyStableData && null === $this->temporaryTransactionsTableName) {
            throw new \Exception('Temporary transactions table name should be provided');
        }
    }

    public function apply(int $customerId, string $date = null)
    {
        $this->dbManager->setCustomerId($customerId);
        $this->transactionsBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();
        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        /** @var Client $client */
        $this->clickhouseClient = $connection->getClient();

        $this->info("Applying costs for customer $customerId started");

        $countChanges = $this->applySpAdvertisedProductCosts($date);
        $countChanges += $this->applySdAdvertisedProductCosts($date);
        $countChanges += $this->applySponsoredBrandCosts($date);
        $isSomethingChanged = $countChanges > 0;

        $this->info("Applying costs for customer $customerId finished");
        $this->info([
            'countChanges' => $countChanges,
            'isSomethingChanged' => $isSomethingChanged
        ]);
        $this->saveToAdsStatistics($date, $isSomethingChanged);

        $this->info("Applying costs for customer $customerId finished");
    }

    public function getDatesNotMovedClickhouseCosts(int $customerId): array
    {
        $this->dbManager->setCustomerId($customerId);
        $actualVersion = $this->customerConfig->get(CustomerConfig::PARAMETER_PPC_COSTS_CLICKHOUSE_VERSION, 1);
        $subQuery1 = SpAdvertisedProduct::find()
            ->select('date')
            ->where(['<', 'version', $actualVersion])
            ->andWhere(['>', 'cost', 0])
            ->andWhere(['<=', 'date', date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);

        $subQuery2 = clone $subQuery1;
        $subQuery2
            ->from(SbAdGroupStatistic::tableName());

        $subQuery3 = clone $subQuery1;
        $subQuery3
            ->from(SdAdvertisedProduct::tableName());

        $dates = (new Query())
            ->select('date')
            ->from(
                $subQuery1
                ->union($subQuery2)
                ->union($subQuery3)
            )
            ->orderBy('date')
            ->column($this->dbManager->getAdsDb());

        return $dates;
    }

    protected function saveToAdsStatistics(
        string $date = null,
        bool $isSomethingChanged = false
    ): void
    {
        $this->info("Save to Ads Statistics clickhouse");

        if (!$isSomethingChanged) {
            $this->info('No changes, skipping');

            return;
        }

        $this->info("Processing ads statistics for date: {$date}");

        try {
            if ($this->isOnlyStableData) {
                $date = $date ?: date('Y-m-d');

                // Stable data are not changed - so we need to process only once.
                // Check in clickhosue if there is already data for this date.
                $isAlreadyMoved = AdsStatistics::find()
                    ->where([
                        '=', 'date', $date
                    ])
                    ->exists();

                if ($isAlreadyMoved) {
                    $this->info('Already moved, skipping');

                    return;
                }

                $dateStart = new \DateTime($date);
                $dateEnd = clone $dateStart->modify('+1 day')->modify('-1 second');
            } else {
                $dateStart = (new \DateTime())->modify('-1 ' . self::DATA_STABILISATION_TIME_DAYS . ' days');
                $dateEnd = new \DateTime();
            }

            $adsStatisticsTable = new AdsStatisticsTable(
                false
            );

            $tableName = $adsStatisticsTable->getName();

            $connection = $this->dbManager->getClickhouseCustomerDb();
            $connection->open();

            $client = $connection->getClient();

            $this->info("Starting data population for date: {$date}");

            $adsStatisticsTable->processAllDataInOneBatch($client, $tableName, $dateStart->format('Y-m-d'), $dateEnd->format('Y-m-d'));
            $adsStatisticsTable->optimizeTable();
            $this->info("Successfully saved ads statistics for date: {$date}");

        } catch (\Exception $e) {
            $this->error($e);
        }
    }

    protected function applySpAdvertisedProductCosts(string $date = null): int
    {
        $this->info("Applying Sponsored Products costs");
        try {
            $query = SpAdvertisedProduct::find()
                ->select([
                    'spap.id',
                    'spap.sku',
                    'spap.asin',
                    'spap.date',
                    'spap.cost',
                    'p.id as product_id',
                    'p.stock_type',
                    'p.sku',
                    'p.title',
                    'p.ean',
                    'p.upc',
                    'p.isbn',
                    'p.brand',
                    'p.product_type',
                    'p.manufacturer',
                    'cast(p.adult_product as integer) as adult_product',
                    'p.main_image',
                    'p.parent_asin',
                    'p.age_range',
                    'coalesce(spap.currency_code, ap.currency_code) as currency_code',
                    'ap.seller_id',
                    'ap.marketplace_id',
                    'tags as tag_id',
                    'version',
                ])
                ->from(['spap' => SpAdvertisedProduct::tableName()])
                ->where(['is not', 'ap.id', new Expression('NULL')])
                ->leftJoin(AmazonAdsProfile::tableName() . ' ap', 'ap.id = spap.profile_id')
                ->leftJoin(Product::tableName() . ' p', 'p.sku = spap.sku AND p.seller_id = ap.seller_id AND p.marketplace_id = ap.marketplace_id')
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->asArray();

            return $this->applyCostsByQuery($query, function ($item) {
                return self::SPONSORED_PRODUCTS_PATH;
            },null, $date);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        return 0;
    }

    protected function applySdAdvertisedProductCosts(string $date = null): int
    {
        $this->info("Applying Sponsored Display costs");
        try {
            $query = SdAdvertisedProduct::find()
                ->select([
                    'spap.id',
                    'spap.sku',
                    'spap.asin',
                    'p.id as product_id',
                    'p.stock_type',
                    'p.sku',
                    'p.title',
                    'p.ean',
                    'p.upc',
                    'p.isbn',
                    'p.brand',
                    'p.product_type',
                    'p.manufacturer',
                    'cast(p.adult_product as integer) as adult_product',
                    'p.main_image',
                    'p.parent_asin',
                    'p.age_range',
                    'spap.date',
                    'spap.cost',
                    'coalesce(spap.currency_code, ap.currency_code) as currency_code',
                    'ap.seller_id',
                    'ap.marketplace_id',
                    'tags as tag_id',
                    'version'
                ])
                ->from(['spap' => SdAdvertisedProduct::tableName()])
                ->where(['is not', 'ap.id', new Expression('NULL')])
                ->leftJoin(AmazonAdsProfile::tableName() . ' ap', 'ap.id = spap.profile_id')
                ->leftJoin(Product::tableName() . ' p', 'p.sku = spap.sku AND p.seller_id = ap.seller_id AND p.marketplace_id = ap.marketplace_id')
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->asArray();

            return $this->applyCostsByQuery($query, function ($item) {
                return self::SPONSORED_DISPLAY_PATH;
            }, null, $date);
        } catch (\Throwable $e) {
            $this->error($e);

            return 0;
        }
    }

    protected function applySponsoredBrandCosts(string $date = null): int
    {
        $this->info("Applying Sponsored Brands costs");

        try {
            $query = SbAdGroupStatistic::find()
                ->select([
                    'sbags.id as sb_group_statistic_id',
                    'sbags.date',
                    'sba.type',
                    new Expression('COALESCE(max(sbags.cost), 0) as cost'),
                    'coalesce(sbags.currency_code, aap.currency_code) as currency_code',
                    'aap.seller_id',
                    'aap.marketplace_id',
                    new Expression("array_to_string(array_agg(coalesce(p.id::text, 'null')), '||') as product_ids"),
                    new Expression("array_to_string(array_agg(coalesce(p.product_type, 'null')), '||') as product_types"),
                    new Expression("array_to_string(array_agg(coalesce(p.stock_type, 'null')), '||') as stock_types"),
                    new Expression("array_to_string(array_agg(coalesce(p.title, 'null')), '||') as titles"),
                    new Expression("array_to_string(array_agg(coalesce(p.ean, 'null')), '||') as eans"),
                    new Expression("array_to_string(array_agg(coalesce(p.upc, 'null')), '||') as upcs"),
                    new Expression("array_to_string(array_agg(coalesce(p.isbn, 'null')), '||') as isbns"),
                    new Expression("array_to_string(array_agg(coalesce(p.brand, 'null')), '||') as brands"),
                    new Expression("array_to_string(array_agg(coalesce(p.manufacturer, 'null')), '||') as manufacturers"),
                    new Expression("array_to_string(array_agg(CASE WHEN p.adult_product IS NULL THEN 'null' WHEN p.adult_product = true THEN '1' ELSE '0' END), '||') as adult_products"),
                    new Expression("array_to_string(array_agg(coalesce(p.main_image, 'null')), '||') as main_images"),
                    new Expression("array_to_string(array_agg(coalesce(p.parent_asin, 'null')), '||') as parent_asins"),
                    new Expression("array_to_string(array_agg(coalesce(p.age_range, 'null')), '||') as age_ranges"),
                    new Expression("array_to_string(array_agg(p.sku), '||') as skus"),
                    new Expression("array_to_string(array_agg(p.asin), '||') as asins"),
                    new Expression("array_to_string(array_agg(iph.sku), '||') as skus_inventory"),
                    'max(sba.asin) as asin',
                    new Expression("array_to_string(array_agg(coalesce(pt.tags, 'null')), '||') as tags"),
                    'version'
                ])
                ->where(['is not', 'aap.id', new Expression('NULL')])
                ->from(['sbags' => SbAdGroupStatistic::tableName()])
                ->leftJoin(SbAd::tableName() . ' sba', 'sba.ad_group_id = sbags.ad_group_id')
                ->leftJoin(AmazonAdsProfile::tableName() . ' aap', 'aap.id = sbags.profile_id')
                ->leftJoin(
                    Product::tableName() . ' p',
                    'p.seller_id = aap.seller_id AND p.marketplace_id = aap.marketplace_id AND p.asin = sba.asin'
                )
                ->leftJoin(InventoryProductHistory::tableName() . ' iph', "iph.seller_id = p.seller_id AND iph.marketplace_id = p.marketplace_id AND iph.asin = p.asin AND iph.date BETWEEN sbags.date - INTERVAL '7 days' AND sbags.date")
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags'),
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id'),
                    ],
                    'pt.product_id = p.id'
                )
                ->groupBy('sbags.id, sbags.date, sba.type, sbags.currency_code, aap.currency_code, aap.seller_id, aap.marketplace_id')
                ->orderBy('sbags.id ASC') // need to prevent duplicates
                ->asArray();

            return $this->applyCostsByQuery(
                $query,
                function ($item) {
                    return sprintf(self::SPONSORED_BRANDS_PATH, $item['type']);
                },
                function ($item) {
                    $items = [];
                    $productIds = explode('||', $item['product_ids']);
                    $skus = explode('||', $item['skus']);
                    $asins = explode('||', $item['asins']);
                    $skusInventory  = !empty($item['skus_inventory']) ? explode('||', $item['skus_inventory']) : [];

                    // Precompute a hash table for fast in_array checks
                    $inventorySet = array_flip($skusInventory);

                    // 1) Deduplicate by productId and simultaneously group by ASIN
                    $productIdsUnique = [];
                    $groups = []; // asin => [ list of indices ]

                    foreach ($productIds as $i => $pid) {
                        if (in_array($pid, $productIdsUnique, true)) {
                            // duplicate productId → remove the entire record immediately
                            unset($productIds[$i], $skus[$i], $asins[$i]);
                            continue;
                        }
                        $productIdsUnique[] = $pid;
                        $groups[$asins[$i]][] = $i;
                    }

                    // 2) Iterate over each ASIN group to select the “surviving” index
                    foreach ($groups as $asin => $indices) {
                        // if the group has only one element → nothing to change
                        if (count($indices) === 1) {
                            continue;
                        }

                        // 2a) Try to find an index whose SKU is present in inventory
                        $chosen = null;
                        foreach ($indices as $idx) {
                            if (isset($inventorySet[$skus[$idx]])) {
                                $chosen = $idx;
                                break;
                            }
                        }

                        // 2b) If none are in inventory → pick the last one
                        if ($chosen === null) {
                            $chosen = end($indices);
                        }

                        // 3) Remove all other indices in this group
                        foreach ($indices as $idx) {
                            if ($idx !== $chosen) {
                                unset($productIds[$idx], $skus[$idx], $asins[$idx]);
                            }
                        }

                        // 4) If this was a multi-item group and the chosen SKU is not in inventory → null it out
                        if (!isset($inventorySet[$skus[$chosen]])) {
                            $skus[$chosen] = null;
                        }
                    }

                    $stockTypes = explode('||', $item['stock_types']);
                    $productTypes = explode('||', $item['product_types']);
                    $titles = explode('||', $item['titles']);
                    $eans = explode('||', $item['eans']);
                    $upcs = explode('||', $item['upcs']);
                    $isbns = explode('||', $item['isbns']);
                    $brands = explode('||', $item['brands']);
                    $manufacturers = explode('||', $item['manufacturers']);
                    $adultProducts = explode('||', $item['adult_products']);
                    $mainImages = explode('||', $item['main_images']);
                    $parentAsins = explode('||', $item['parent_asins']);
                    $ageRanges = explode('||', $item['age_ranges']);
                    $costPerSku = $item['cost'] / count($skus);
                    $tags = explode('||', $item['tags']);

                    foreach ($skus as $k => $sku) {
                        $items[] = [
                            'id' => $item['sb_group_statistic_id'],
                            'date' => $item['date'],
                            'cost' => $costPerSku,
                            'type' => $item['type'],
                            'currency_code' => $item['currency_code'],
                            'seller_id' => $item['seller_id'],
                            'marketplace_id' => $item['marketplace_id'],
                            'sku' => $sku,
                            'asin' => $asins[$k] == 'null' ? null : $asins[$k],
                            'product_id' => $productIds[$k] == 'null' ? null : $productIds[$k],
                            'title' => $titles[$k] == 'null' ? null : $titles[$k],
                            'ean' => $eans[$k] == 'null' ? null : $eans[$k],
                            'upc' => $upcs[$k] == 'null' ? null : $upcs[$k],
                            'isbn' => $isbns[$k] == 'null' ? null : $isbns[$k],
                            'brand' => $brands[$k] == 'null' ? null : $brands[$k],
                            'product_type' => $productTypes[$k] == 'null' ? null : $productTypes[$k],
                            'stock_type' => $stockTypes[$k] == 'null' ? null : $stockTypes[$k],
                            'manufacturer' => $manufacturers[$k] == 'null' ? null : $manufacturers[$k],
                            'adult_product' => $adultProducts[$k] == 'null' ? null : (int) $adultProducts[$k],
                            'main_image' => $mainImages[$k] == 'null' ? null : $mainImages[$k],
                            'parent_asin' => $parentAsins[$k] == 'null' ? null : $parentAsins[$k],
                            'age_range' => $ageRanges[$k] == 'null' ? null : $ageRanges[$k],
                            'tag_id' => $tags[$k] === 'null' ? null : $tags[$k],
                        ];
                    }
                    return $items;
                },
                $date,
                'sbags.date'
            );
        } catch (\Throwable $e) {
            $this->error($e);

            return 0;
        }
    }

    protected function generateTransactionsForPPCItems(array $items, callable $generatePathFn): array
    {
        $transactions = [];
        // Need to prevent data loss when flushing buffer
        $eventPeriodId = random_int(1, 10000000);
        foreach ($items as $item) {
            $facts = [
                ClickhouseTransactionExtractor::FACT_EVENT_PERIOD_ID => $eventPeriodId,
                ClickhouseTransactionExtractor::FACT_SELLER_ID => $item['seller_id'],
                ClickhouseTransactionExtractor::FACT_MARKETPLACE_ID => $item['marketplace_id'],
                ClickhouseTransactionExtractor::FACT_SELLER_SKU => $item['sku'],
                ClickhouseTransactionExtractor::FACT_ASIN => $item['asin'],
                ClickhouseTransactionExtractor::FACT_POSTED_DATE => $item['date'] . ' 00:00:00',
                ClickhouseTransactionExtractor::FACT_CATEGORY_PATH => $generatePathFn($item) . FinanceEventCategory::MINUS_POSTFIX,
            ];

            if (!$this->isOnlyStableData) {
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_EAN] = $item['ean'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_ISBN] = $item['isbn'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_UPC] = $item['upc'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_TITLE] = $item['title'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_ID] = $item['product_id'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_PARENT_ASIN] = $item['parent_asin'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_BRAND] = $item['brand'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_MODEL] = $item['model'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_TYPE] = $item['product_type'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_MANUFACTURER] = $item['manufacturer'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_IS_ADULT_ONLY] = $item['adult_product'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_MAIN_IMAGE] = $item['main_image'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_PRODUCT_STOCK_TYPE] = $item['stock_type'] ?? null;
                $facts[ClickhouseTransactionExtractor::FACT_TAG_ID] = $item['tag_id'] ?? null;
            }

            $transaction = $this->transactionExtractor->convertCurrencyItemToTransaction(
                [
                    'CurrencyCode' => $item['currency_code'],
                    'CurrencyAmount' => $item['cost'] * -1,
                ],
                $facts,
                false
            );

            $transactions[] = $transaction;
        }
        return $transactions;
    }

    protected function applyCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null,
        string $date = null,
        string $dateColumn = 'date'
    ): int
    {
        if ($this->isOnlyStableData) {
            return $this->applyStableCostsByQuery($query, $generatePathFn, $convertItemFn, $date, $dateColumn);
        } else {
            return $this->applyTemporaryCostsByQuery($query, $generatePathFn, $convertItemFn, $dateColumn);
        }
    }

    protected function applyTemporaryCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null,
        string $dateColumn = 'date'
    ): int
    {
        $query->andWhere(['>', $dateColumn, date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);
        $query->noCache();

        $countChanges = 0;

        foreach ($query->batch(500) as $itemsRaw) {
            try {
                $items = [];
                if (null === $convertItemFn) {
                    $items = $itemsRaw;
                } else {
                    foreach ($itemsRaw as $item) {
                        $items = array_merge($items, $convertItemFn($item));
                    }
                }

                if (count($items) === 0) {
                    break;
                }

                $items = $this->filterItems($items);

                $transactions = $this->generateTransactionsForPPCItems($items, $generatePathFn);

                if ($this->customerComponent->isTransactionsFrozen()) {
                    $this->info('Interaction with clickhouse is frozen');
                    $this->transactionsBuffer->remove();

                    return 0;
                }

                if (!empty($transactions)) {
                    $transactions = Transaction::convertTransactionsToArray($transactions);

                    Command::$isNodeChangingEnabled = false;
                    $this->clickhouseClient->insertAssocBulk($this->temporaryTransactionsTableName, $transactions);
                    Command::$isNodeChangingEnabled = true;

                    $countChanges += count($transactions);
                }
            } catch (\Throwable $e) {
                Command::$isNodeChangingEnabled = true;
                $this->error($e);
            }
        }

        return $countChanges;
    }

    protected function applyStableCostsByQuery(
        ActiveQuery $query,
        callable $generatePathFn,
        callable $convertItemFn = null,
        string $date = null,
        string $dateColumn = 'date'
    ): int
    {
        $ppcCostsClickhouseVersion = $this->customerConfig->get(CustomerConfig::PARAMETER_PPC_COSTS_CLICKHOUSE_VERSION, 1);

        $query->andWhere(['<', 'version', $ppcCostsClickhouseVersion]);
        if ($date !== null) {
            $query->andWhere(['=', $dateColumn, $date]);
        }
        $query->andWhere(['<=', $dateColumn, date('Y-m-d', strtotime("-" . self::DATA_STABILISATION_TIME_DAYS . " days"))]);
        $query->noCache()->limit(self::BATCH_SIZE);

        $countChanges = 0;

        for ($i = 0; $i < 500; $i++) {
            $itemsRaw = $query->all();

            try {
                $items = [];
                $movedIds = [];

                if (null === $convertItemFn) {
                    $items = $itemsRaw;
                } else {
                    foreach ($itemsRaw as $item) {
                        $items = array_merge($items, $convertItemFn($item));
                    }
                }

                if (count($items) === 0) {
                    break;
                }

                foreach ($items as $k => $item) {
                    $movedIds[$item['id']] = 1;
                }

                $items = $this->filterItems($items);
                $transactions = $this->generateTransactionsForPPCItems($items, $generatePathFn);

                if ($this->customerComponent->isTransactionsFrozen()) {
                    $this->info('Interaction with clickhouse is frozen');
                    $this->transactionsBuffer->remove();

                    return 0;
                }

                try {
                    if (!empty($transactions)) {
                        $this->transactionsBuffer->put($transactions);
                        $this->transactionsBuffer->flush();
                        $countChanges += count($transactions);
                    }
                } catch (\Throwable $e) {
                    $this->transactionsBuffer->remove();
                    throw $e;
                }
                $this->info("Updating moved_to_clickhouse_at for " . count($movedIds) . " items");
                $this->setActualVersion(array_keys($movedIds), $query, $ppcCostsClickhouseVersion);
                $this->info("Flushing buffer");
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        return $countChanges;
    }

    private function setActualVersion(array $itemIds, $query, int $ppcCostsClickhouseVersion)
    {
        $query->modelClass::updateAll(
            [
                'version' => $ppcCostsClickhouseVersion,
                'moved_to_clickhouse_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => $itemIds
            ]
        );
    }

    /**
     * Removes not valid or not wanted items.
     *
     * @param array $items
     * @return array
     */
    protected function filterItems(array $items): array
    {
        return array_filter($items, function ($item) {
            return (!empty($item['sku']) || !empty($item['asin']))  && $item['cost'] > 0;
        });
    }
}
