<?php

namespace common\components\amazonAds\api;

use AmazonAdvertisingApi\Client as BaseClient;
use AmazonAdvertisingApi\CurlRequest;
use AmazonAdvertisingApi\SponsoredProductsRequests;
use common\components\amazonAds\api\exception\AmazonAdsApiException;
use common\components\amazonAds\api\requests\ReportingRequests;
use common\components\amazonAds\api\requests\SponsoredBrandsRequests;

/**
 * @mixin \AmazonAdvertisingApi\Client
 * @mixin SponsoredProductsRequests
 */
class Client extends BaseClient
{
    use ReportingRequests;
    use SponsoredBrandsRequests;

    /**
     * @throws AmazonAdsApiException
     * @throws \Exception
     */
    public function operationV3(string $interface, ?array $params = [], string $method = "GET", $headers = []): ?array
    {
        $headers = array_merge($headers, [
            "Authorization: Bearer {$this->config['accessToken']}",
            "Content-Type: application/json",
            "User-Agent: $this->userAgent",
            "Amazon-Advertising-API-ClientId: {$this->config['clientId']}",
        ]);

        if (!is_null($this->profileId)) {
            $headers[] = "Amazon-Advertising-API-Scope: $this->profileId";
        }

        $request = new CurlRequest();
        $url = $this->getApiUrl() . "/{$interface}";

        switch (strtolower($method)) {
            case "get":
                if (!empty($params)) {
                    $url .= "?";
                    foreach ($params as $k => $v) {
                        $url .= "{$k}=" . rawurlencode($v) . "&";
                    }
                    $url = rtrim($url, "&");
                }
                break;
            case "put":
            case "post":
            case "delete":
                if (!empty($params)) {
                    $request->setOption(CURLOPT_POST, true);
                    $request->setOption(CURLOPT_POSTFIELDS, json_encode($params));
                }
                break;
            default:
                $this->logAndThrow("Unknown verb {$method}.");
        }

        $request->setOption(CURLOPT_URL, $url);
        $request->setOption(CURLOPT_HTTPHEADER, $headers);
        $request->setOption(CURLOPT_USERAGENT, $this->userAgent);
        $request->setOption(CURLOPT_CUSTOMREQUEST, strtoupper($method));
//        $request->setOption(CURLOPT_VERBOSE, true);
        $result = $this->executeRequest($request);

        if (isset($result['code']) && $result['code'] >= 400) {
            throw new AmazonAdsApiException($result['response'], $result['code']);
        }

        return json_decode($result['response'], true);
    }

    protected function getApiUrl(): string
    {
        if (!array_key_exists(strtolower($this->config["region"]), $this->endpoints)) {
            throw new \Exception('Region is not supported');
        }
        $regionCode = strtolower($this->config["region"]);

        if ($this->config["sandbox"]) {
            return "https://{$this->endpoints[$regionCode]["sandbox"]}";
        }

        return "https://{$this->endpoints[$regionCode]["prod"]}";
    }


    public function __get($name)
    {
        $reflectionClass = new \ReflectionClass(BaseClient::class);
        $reflectionProperty = $reflectionClass->getProperty($name);
        $reflectionProperty->setAccessible(true);
        return $reflectionProperty->getValue($this);
    }

    public function __set($name, $value)
    {
        $reflectionClass = new \ReflectionClass(BaseClient::class);
        $reflectionProperty = $reflectionClass->getProperty($name);
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($this, $value);
    }

    /**
     * @throws \ReflectionException
     */
    public function __call($name, $params)
    {
        $method = new \ReflectionMethod(BaseClient::class, $name);
        $method->setAccessible(true);
        return $method->invoke($this, ...$params);
    }

    /**
     * @throws AmazonAdsApiException
     */
    protected function handleResponse(array $response)
    {
        if (isset($response['code']) && $response['code'] >= 400) {
            throw new AmazonAdsApiException($response['response'], $response['code']);
        }

        return json_decode($response['response'], true);
    }
}
