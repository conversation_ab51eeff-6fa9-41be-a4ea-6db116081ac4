<?php

namespace common\components\amazonAds\api;

use common\components\tokenService\tokenProvider\TokenProviderFactory;
use common\components\tokenService\TokenService;

class ClientBuilder
{
    protected TokenService $tokenService;

    public function __construct()
    {
        $this->tokenService = \Yii::$app->tokenService;;
    }

    public function getApiClient(int $accountId, string $regionId, int $profileId = null): Client
    {
        $token = $this->tokenService->getToken($accountId, false, TokenProviderFactory::TYPE_AMAZON_ADS_API);
        $refreshToken = $token->refreshToken;

        $config = [
            "clientId" => getenv("AMAZON_ADS_API_CLIENT_ID"),
            "clientSecret" => getenv("AMAZON_ADS_API_CLIENT_SECRET"),
            "refreshToken" => $refreshToken,
            "region" => $regionId,
            "sandbox" => false,
            'apiVersion' => 'v2'
        ];
        $client = new Client($config);
        $client->profileId = $profileId;

        return $client;
    }
}
