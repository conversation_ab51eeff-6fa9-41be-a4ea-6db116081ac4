<?php

namespace common\components\amazonAds\api\requests;

trait SponsoredBrandsRequests
{
    public function listSponsoredBrandAds(array $params): array
    {
        return $this->operationV3("sb/v4/ads/list", $params, 'POST', [
            'Accept' => 'application/vnd.sbadresource.v4+json'
        ]);
    }

    public function listSbCampaigns(array $data = []): array
    {
        $nextToken = null;
        $campaigns = [];

        do {
            $data =  $this->operationV3("sb/v4/campaigns/list", [
                'nextToken' => $nextToken
            ], 'POST', [
                'Accept' => 'application/vnd.sbadresource.v4+json'
            ]);

            $nextToken = $data['nextToken'] ?? null;
            $campaigns = array_merge($campaigns, $data['campaigns'] ?? []);
        } while ($nextToken);

        return $campaigns;
    }

    public function listSbAdGroups($data = null): array
    {
        return $this->operationV3("sb/v4/adGroups/list", $data, 'POST', [
            'Accept' => 'application/vnd.sbadresource.v4+json'
        ]);
    }
}
