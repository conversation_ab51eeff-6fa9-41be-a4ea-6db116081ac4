<?php

namespace common\components\amazonAds\api\requests;

trait SponsoredProductsRequests
{
    public function listSpCampaigns(): array
    {
        $nextToken = null;
        $campaigns = [];

        do {
            $data =  $this->operationV3("sp/campaigns/list", [
                'nextToken' => $nextToken
            ], 'POST', [
                'Accept: application/vnd.spCampaign.v3+json',
                'Content-Type: application/vnd.spCampaign.v3+json'
            ]);

            $nextToken = $data['nextToken'] ?? null;
            $campaigns = array_merge($campaigns, $data['campaigns'] ?? []);
        } while ($nextToken);

        return $campaigns;
    }

    public function listSponsoredProductAds($data = null): array
    {
        return $this->operationV3("sp/productAds/list", [ ], 'POST', [
            'Accept' => 'application/vnd.spproductAd.v3+json',
            'Content-Type' => 'application/vnd.spproductAd.v3+json'
        ]);
    }

    public function listSpAdGroups($data = null): array
    {
        return $this->operationV3("sp/adGroups/list", $data, 'POST', [
            'Accept' => 'application/vnd.spadGroup.v3+json',
            'Content-Type' => 'application/vnd.spadGroup.v3+json'
        ]);
    }
}
