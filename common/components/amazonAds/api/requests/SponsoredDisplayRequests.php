<?php

namespace common\components\amazonAds\api\requests;

trait SponsoredDisplayRequests
{
    public function listSdCampaigns(string $nextToken = null): array
    {
        return $this->operationV3("sd/campaigns", [ ], 'GET');
    }

    public function listSponsoredDisplayProductAds($data = null): array
    {
        return $this->operationV3("sd/productAds", $data);
    }

    public function listSdAdGroups($data = null): array
    {
        return $this->operationV3("sd/adGroups", $data);
    }
}
