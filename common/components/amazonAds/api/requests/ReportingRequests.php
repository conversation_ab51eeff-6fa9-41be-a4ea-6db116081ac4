<?php

namespace common\components\amazonAds\api\requests;

use common\components\LogToConsoleTrait;

/**
 * @mixin \AmazonAdvertisingApi\Client
 */
trait ReportingRequests
{
    use LogToConsoleTrait;

    public function requestReportV3(
        \DateTime $dateStart,
        \DateTime $dateEnd,
        string $adProduct,
        string $reportType,
        array $columns = [],
        array $groupBy = [],
        string $timeUnit = 'DAILY'
    ): array
    {
        $reportName = "BA report ($reportType) " . $dateStart->format('n/j') . '-' . $dateEnd->format('n/j');

        $data = [
            'name' => $reportName,
            'startDate' => $dateStart->format('Y-m-d'),
            'endDate' => $dateEnd->format('Y-m-d'),
            'configuration' => [
                'adProduct' => $adProduct,
                'groupBy' => $groupBy,
                'columns' => $columns,
                'reportTypeId' => $reportType,
                'timeUnit' => $timeUnit,
                'format' => 'GZIP_JSON',
            ],
        ];

        $this->info("Requesting report: $reportName");
        $this->info($data);

        return $this->operationV3('reporting/reports', $data, 'POST');
    }

    public function getReportV3(string $reportId): array
    {
        return $this->operationV3("reporting/reports/{$reportId}");
    }

    public function getReport($reportId)
    {
        return $this->handleResponse($this->operation("reports/{$reportId}"));
    }
}