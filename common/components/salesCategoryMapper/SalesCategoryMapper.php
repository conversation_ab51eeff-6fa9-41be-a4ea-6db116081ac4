<?php

namespace common\components\salesCategoryMapper;

use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * Maps sales categories
 *
 * @package common\components\salesCategoryMapper
 */
class SalesCategoryMapper
{
    use LogToConsoleTrait;

    private ExpressionLanguage $expressionLanguage;
    private SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct()
    {
        $this->expressionLanguage = new ExpressionLanguage();
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
    }

    public function remap(): void
    {
        $this->info('Re-map sales categories started');

        /** @var FinanceEventCategory[] $financeEventCategories */
        $financeEventCategories = FinanceEventCategory::find()->all();

        foreach ($financeEventCategories as $financeEventCategory) {
            // Trigger works inside model
            $financeEventCategory->save(false);
        }

        $this->info('Re-map sales categories finished');
    }

    /**
     * Using comparators with rules, determines and returns sales category id by event category path.
     *
     * @param string $eventCategoryPath
     * @return string
     */
    public function getSalesCategoryId(string $eventCategoryPath, SalesCategoryStrategyInterface $strategy): ?string
    {
        $categoriesToBeMapped = $this->getSalesCategoriesForMapping($strategy);
        $foundSalesCategory = null;

        if (FinanceEventCategory::isInternalCategory($eventCategoryPath)) {
            return null;
        }

        foreach ($categoriesToBeMapped as $salesCategory) {
            if (!$this->isMatchesRule($salesCategory['mapping_rule'], $eventCategoryPath)) {
                continue;
            }
            $foundSalesCategory = $salesCategory;
            break;
        }

        if (null === $foundSalesCategory
            || (
                SalesCategory::isUndefinedCategory($foundSalesCategory['id'])
                && false !== strpos($eventCategoryPath, FinanceEventCategory::PLUS_ZERO_POSTFIX)
            )
        ) {
            // Try to map ignoring PLUS and MINUS signs
            foreach ($categoriesToBeMapped as $salesCategory) {
                $mappingRule = str_replace([
                    FinanceEventCategory::PLUS_POSTFIX,
                    FinanceEventCategory::MINUS_POSTFIX
                ], '.', $salesCategory['mapping_rule']);

                $isMatchingRule = $this->isMatchesRule($mappingRule, $eventCategoryPath);

                if (!$isMatchingRule) {
                    continue;
                }

                $foundSalesCategory = $salesCategory;
                break;
            }
        }

        if (null === $foundSalesCategory) {
            throw new \Exception("Unable to determine sales category for path $eventCategoryPath");
        }

        return $foundSalesCategory['id'];
    }

    private function isMatchesRule(string $mappingRule, string $eventCategoryPath)
    {
        // Converting our rule to expression language format.
        $businessRule = $mappingRule;

        $businessRule = strtr($businessRule, ['.' => '_DOT_']);
        $eventCategoryPath = strtr($eventCategoryPath, ['.' => '_DOT_']);

        $businessRule = preg_replace('/[^a-zA-Z_()&|]+/', '', $businessRule);
        $eventCategoryPath = preg_replace('/[^a-zA-Z_]+/', '', $eventCategoryPath);

        $businessRule = strtr($businessRule, ['&&' => ' && ', '||' => ' || ', '(' => ' (', ')' => ') ']);
        $eventCategoryPath = strtr($eventCategoryPath, ['&&' => ' && ', '||' => ' || ', '(' => ' (', ')' => ') ']);

        $businessRule = preg_replace("/([\w.-]+)/", 'event_category_path matches "/$1/"', $businessRule);

        return $this
            ->expressionLanguage
            ->evaluate($businessRule, [
                'event_category_path' => $eventCategoryPath
            ]);
    }

    private function getSalesCategoriesForMapping(SalesCategoryStrategyInterface $strategy): array
    {
        return SalesCategory::find()
            ->select('mapping_rule, id, path')
            ->where([
                'AND',
                ['type' => $strategy->getType()],
                ['!=', 'mapping_rule', ""]
            ])
            ->addOrderBy("mapping_rule_order asc")
            ->createCommand()
            ->cache(60)
            ->queryAll();
    }
}
