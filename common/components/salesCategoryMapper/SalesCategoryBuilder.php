<?php

namespace common\components\salesCategoryMapper;

use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\models\SalesCategory;
use yii\helpers\Inflector;

class SalesCategoryBuilder
{
    use LogToConsoleTrait;

    private SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct()
    {
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
    }

    public function rebuild()
    {
        $this->info('Rebuild sales categories started');
        /** @var SalesCategoryStrategyInterface[] $strategies */
        $strategies = $this->salesCategoryStrategyFactory->getAllStrategies();

        foreach ($strategies as $strategy) {
            $this->info('Rebuild strategy ' . $strategy->getType());
            $salesCategories = $this->convertTreeToFlatFormat($strategy->getMapping());
            $availableSalesCategoryIds = [];

            foreach ($salesCategories as $sortOrder => $salesCategoryArr) {
                $salesCategory = SalesCategory::findOne($salesCategoryArr['id']);

                // Color should be sticky, and do not be changed every remapping.
                if (null === $salesCategory) {
                    $salesCategory = new SalesCategory();
                    $salesCategory->id = $salesCategoryArr['id'];
                    $salesCategory->color_hex = SalesCategory::generateHexColor();
                }

                if (!empty($salesCategoryArr['color_hex'])) {
                    $salesCategory->color_hex = $salesCategoryArr['color_hex'];
                }

                $salesCategory->name = $salesCategoryArr['name'];
                $salesCategory->parent_id = $salesCategoryArr['parent_id'];
                $salesCategory->depth = $salesCategoryArr['depth'];
                $salesCategory->path = $salesCategoryArr['path'];
                $salesCategory->mapping_rule = $salesCategoryArr['mapping_rule'];
                $salesCategory->is_visible = $salesCategoryArr['is_visible'];
                $salesCategory->is_exclude_from_calculation = $salesCategoryArr['is_exclude_from_calculation'];
                $salesCategory->mapping_rule_order = $salesCategoryArr['mapping_rule_order'];
                $salesCategory->sort_order = $salesCategoryArr['sort_order'];
                $salesCategory->tags = $salesCategoryArr['tags'];
                $salesCategory->type = $strategy->getType();
                $availableSalesCategoryIds[] = $salesCategoryArr['id'];
                $salesCategory->save(false);
            }

            SalesCategory::deleteAll([
                'AND',
                ['type' => $strategy->getType()],
                ['not in', 'id', $availableSalesCategoryIds]
            ]);
        }
        $this->info('Rebuild sales categories finished');
    }

    /**
     * Based on tree structure, builds and returns its flat representation.
     *
     * @param array $categoriesTree
     * @param string $parentId
     * @param string $depth
     * @param string $path
     *
     * @return array
     */
    private function convertTreeToFlatFormat(
        array $categoriesTree,
        string $parentId = null,
        $pathParts = [],
        array $mappingRules = [],
        array $tags = null
    ): array
    {
        $flatTree = [];

        // Some categories are repeating. We should add postfix with incrementer to them
        static $usedCategoryIds = [];

        foreach ($categoriesTree as $k => $v) {
            $itemName = is_string($v) ? $v : $k;
            $children = is_array($v) ? $v : [];

            if (empty($children)) {
                continue;
            }

            if (isset($children['_id'])) {
                $categoryId = $children['_id'];
            } else {
                $categoryId = Inflector::slug($itemName, '_');
            }

            preg_match('/^(.+?)(?:_(\d+))?$/', $categoryId, $matches);
            $noUniqueIndexCategoryId = $matches[1];
            $uniqueIndex = (int)($matches[2] ?? 0);

            // Building unique ID by adding numeric postfix
            if (isset($usedCategoryIds[$noUniqueIndexCategoryId][$uniqueIndex])) {
                for ($i = 0; $i < 10; $i++) {
                    if (!isset($usedCategoryIds[$noUniqueIndexCategoryId][$i])) {
                        $uniqueIndex = $i;
                        break;
                    }
                }
            }
            $usedCategoryIds[$noUniqueIndexCategoryId][$uniqueIndex] = true;

            if (0 !== $uniqueIndex) {
                $categoryId = $noUniqueIndexCategoryId . '_' . $uniqueIndex;
            }

            $salesCategory = [];
            $salesCategory['id'] = $categoryId;
            $thisPathParts = array_merge($pathParts, [$salesCategory['id']]);
            $salesCategory['name'] = $itemName;
            $salesCategory['parent_id'] = $parentId;
            $salesCategory['depth'] = count($thisPathParts);
            $salesCategory['path'] = implode('|', $thisPathParts);
            $salesCategory['mapping_rule'] = '';
            $salesCategory['is_visible'] = true;
            $salesCategory['is_exclude_from_calculation'] = false;
            $salesCategory['mapping_rule_order'] = 1;
            $salesCategory['tags'] = $tags;
            $salesCategory['sort_order'] = count($flatTree) + 1;

            $parentMappingRules = $mappingRules;

            if (isset($children['_mappingRuleOrder'])) {
                $salesCategory['mapping_rule_order'] = $children['_mappingRuleOrder'];
                $salesCategory['sort_order'] = $salesCategory['mapping_rule_order'];
                unset($children['_mappingRuleOrder']);
            }

            if (isset($children['_colorHex'])) {
                $salesCategory['color_hex'] = $children['_colorHex'];
                unset($children['_colorHex']);
            }

            $childrenTags = $tags;

            if (isset($children['_tags'])) {
                foreach ($children['_tags'] as $tagInfo) {
                    [$tagId, $shouldApplyForChildren] = explode('|', $tagInfo);

                    $shouldApplyForChildren = (bool)($shouldApplyForChildren ?? true);
                    $salesCategory['tags'][] = $tagId;

                    if ($shouldApplyForChildren) {
                        $childrenTags[] = $tagId;
                    }
                }

                $salesCategory['tags'] = array_unique($salesCategory['tags']);
                sort($salesCategory['tags']);
                unset($children['_tags']);
            }

            if (isset($children['_mappingRuleBase'])) {
                $parentMappingRules[] = "({$children['_mappingRuleBase']})";
                unset($children['_mappingRuleBase']);
            }

            if (isset($children['_isVisible'])) {
                $salesCategory['is_visible'] = $children['_isVisible'];
                unset($children['_isVisible']);
            }

            if (isset($children['_isExcludeFromCalculation'])) {
                $salesCategory['is_exclude_from_calculation'] = $children['_isExcludeFromCalculation'];
                unset($children['_isExcludeFromCalculation']);
            }

            $rules = $mappingRules;

            if (isset($children['_mappingRule'])) {
                $rules[] = "({$children['_mappingRule']})";
                $salesCategory['mapping_rule'] = implode(' && ', $rules);
                unset($children['_mappingRule']);
            }

            $flatTree[] = $salesCategory;

            if (count($children) > 0) {
                $flatTree = array_merge(
                    $flatTree,
                    $this->convertTreeToFlatFormat(
                        $children,
                        $salesCategory['id'],
                        $thisPathParts,
                        $parentMappingRules,
                        $childrenTags
                    )
                );
            }
        }

        return $flatTree;
    }
}
