<?php

namespace common\components\salesCategoryMapper\strategy;

use common\models\SalesCategory;
use yii\db\ActiveQuery;

abstract  class AbstractStrategy implements SalesCategoryStrategyInterface
{
    /**
     * {@inheritdoc }
     */
    public function getNetPurchasePriceAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_MANUAL_NET_PURCHASE_PRICE);
    }

    /**
     * {@inheritdoc }
     */
    public function getRevenueAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_REVENUE);
    }

    /**
     * {@inheritdoc}
     */
    public function getProductSalesAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_PRODUCT_SALES);
    }

    /**
     * {@inheritdoc}
     */
    public function getPPCAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_PPC_COSTS);
    }

    /**
     * {@inheritdoc}
     */
    public function getAmazonFeesAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_AMAZON_FEES);
    }

    /**
     * {@inheritdoc }
     */
    public function getExpensesAmountByFlatTreeData(array $flatTreeData): float
    {
        return $this->getAmountByFlatTreeDataByTag($flatTreeData, SalesCategory::TAG_REVENUE, false);
    }

    /**
     * Calculates and returns sum of all amounts with specific tag.
     *
     * @param array $flatTreeData
     * @param string $tag
     * @param bool $shouldIncludeTag - include passed tag or exclude
     * @return float
     */
    protected function getAmountByFlatTreeDataByTag(array $flatTreeData, string $tag, bool $shouldIncludeTag = true): float
    {
        return (float)array_reduce($flatTreeData, function ($amount, $item) use ($tag, $shouldIncludeTag) {
            if ((float)$item['amount'] == 0 || $item['has_children']) {
                return $amount;
            }

            $tags = $item['tags'] ?? [];
            $tagWasFound = in_array($tag, $tags);

            if (($tagWasFound && $shouldIncludeTag) || (!$tagWasFound && !$shouldIncludeTag)) {
                $amount += $item['amount'];
            }

            return $amount;
        });
    }

    public function getTransactionExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\TransactionExtendedView::find();
    }

    public function getAmazonOrderExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\clickhouse\AmazonOrderExtendedView::find();
    }

    public function getAmazonOrderInProgressExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\clickhouse\AmazonOrderInProgressExtendedView::find();
    }
}
