<?php

namespace common\components\salesCategoryMapper\strategy\mapping;

use common\models\FinanceEventCategory;

class MappingReader
{
    /**
     * Reads sales categories mapping from csv file and returns it as tree structure.
     *
     * @param string $csvMappingFilePath
     * @param int $pathColumnIndex
     * @param int $tagsColumnIndex
     * @param int $idColumnIndex
     * @param int $category0SortColumnIndex
     * @param int $category0ColumnIndex
     * @param int $category1SortColumnIndex
     * @param int $category1ColumnIndex
     * @return array
     */
    public function readAsTree(
        string $csvMappingFilePath,
        int $pathColumnIndex,
        int $tagsColumnIndex,
        int $category0SortColumnIndex,
        int $category0ColumnIndex,
        int $category1SortColumnIndex,
        int $category1ColumnIndex,
        int $colorHexColumnIndex
    ): array
    {
        // Read csv file
        $csvFile = fopen($csvMappingFilePath, 'r');
        $csvData = [];
        while (($row = fgetcsv($csvFile)) !== false) {
            $csvData[] = $row;
        }

        $categoriesTree = [];

        // Removing headers
        unset($csvData[0]);

        array_multisort($csvData, SORT_ASC,
            array_column($csvData, $category0ColumnIndex)
        );

        foreach ($csvData as $k => $mapping) {
            $path = $mapping[$pathColumnIndex];
            $path = preg_replace('/[()&|]+/', '', $path);

            $tags = null;

            if (!empty($mapping[$tagsColumnIndex])) {
                $tags = explode(',', $mapping[$tagsColumnIndex]);
                $tags = array_map('trim', $tags);
            }

            $categoryNames = [
                $mapping[$category0ColumnIndex],
                $mapping[$category1ColumnIndex]
            ];
            $sortOrders = [
                $mapping[$category0SortColumnIndex]
                    ? (int)$mapping[$category0SortColumnIndex]
                    : null,
                $mapping[$category1SortColumnIndex]
                    ? (int)$mapping[$category1SortColumnIndex]
                    : null,
            ];
            $categoryNames = array_filter($categoryNames);
            $sortOrders = array_filter($sortOrders);
            $lastKey = array_key_last($categoryNames);
            $currentLevel = &$categoriesTree;
            $colorHex = $mapping[$colorHexColumnIndex] ?? null;

            foreach ($categoryNames as $j => $categoryName) {
                $isLastColumn = $j === $lastKey;

                if (empty($currentLevel[$categoryName])) {
                    $currentLevel[$categoryName] = [];
                }

                $sortOrder = $sortOrders[$j];

                if ($isLastColumn && !empty($path)) {
                    $mappingRules = explode(' || ', ($currentLevel[$categoryName]['_mappingRule'] ?? ''));
                    $mappingRules[] = $path;
                    $mappingRules = array_filter($mappingRules);
                    $currentLevel[$categoryName]['_mappingRule'] = implode(' || ', $mappingRules);
                }

                if ($isLastColumn && !empty($tags)) {
                    $currentLevel[$categoryName]['_tags'] = $tags;
                }

                if ($isLastColumn && !empty($colorHex)) {
                    $currentLevel[$categoryName]['_colorHex'] = $colorHex;
                }

                if (!empty($sortOrder)) {
                    $currentLevel[$categoryName]['_mappingRuleOrder'] = $sortOrder;
                }

                $currentLevel = &$currentLevel[$categoryName];
            }
        }

        return $categoriesTree;
    }
}