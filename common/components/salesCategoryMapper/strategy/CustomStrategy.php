<?php

namespace common\components\salesCategoryMapper\strategy;

use common\components\salesCategoryMapper\strategy\mapping\MappingReader;
use common\models\SalesCategory;
use yii\db\ActiveQuery;

class CustomStrategy extends AbstractStrategy implements SalesCategoryStrategyInterface
{
    const MAPPING_FILE_PATH = __DIR__ . '/mapping/custom_mapping.csv';

    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return SalesCategoryStrategyFactory::STRATEGY_CUSTOM;
    }

    /**
     * {@inheritdoc}
     */
    public function getSalesCategoryIdColumnName(): string
    {
        return 'sales_category_id_custom';
    }

    public function getDataSeriesDepth(): int
    {
        return 0;
    }

    /**
     * {@inheritdoc}
     */
    public function getMapping(): array
    {
        $mappingReader = new MappingReader();
        $categoriesTree = $mappingReader->readAsTree(
            self::MAPPING_FILE_PATH,
            1,
            13,
            11,
            4,
            12,
            5,
            14,
            7
        );

        $categoriesTree['Ignored'] = [
            '_mappingRule' => '.PerUnitAmount || .TotalAmount || .totalAmount || .ReserveCredit || .ReserveDebit || .FailedDisbursement || .baseValue || .taxValue || DebtRecovery. || LoanServicing. || PayWithAmazon. || AdhocDisbursement. || .TCS-SGST || .TCS-CGST || TaxWithholding. || Debt Adjustment',
            '_mappingRuleOrder' => 100,
            '_isVisible' => false,
            '_isExcludeFromCalculation' => true
        ];

        $categoriesTree = json_decode(json_encode($categoriesTree), true);

        return $categoriesTree;
    }

    public function getTransactionExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\TransactionExtendedViewV1::find();
    }

    public function getAmazonOrderExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\clickhouse\AmazonOrderExtendedViewV1::find();
    }

    public function getAmazonOrderInProgressExtendedViewQuery(): ActiveQuery
    {
        return \common\models\customer\clickhouse\AmazonOrderInProgressExtendedViewV1::find();
    }

    public function isRevenueCategories(array $categoryInfo): bool
    {
        return $categoryInfo['tags'] !== null ? in_array('revenue', $categoryInfo['tags']) : false;
    }
}
