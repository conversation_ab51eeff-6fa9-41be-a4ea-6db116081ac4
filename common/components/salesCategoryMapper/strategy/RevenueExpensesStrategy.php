<?php

namespace common\components\salesCategoryMapper\strategy;

use common\components\amazonAds\CostsApplier;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;

class RevenueExpensesStrategy extends AbstractStrategy implements SalesCategoryStrategyInterface
{
    public const CATEGORY_REVENUE = 'revenue';
    public const CATEGORY_PRODUCT_SALES = 'product_sales';
    public const CATEGORY_COSTS = 'expenses';
    public const CATEGORY_EXPENSES_TAXES = 'expenses_taxes';
    public const CATEGORY_EXPENSES_COG = 'cost_of_goods';
    public const CATEGORY_EXPENSES_SHIPPING_COSTS = 'shipping_costs';
    public const CATEGORY_EXPENSES_AMAZON_FEES = 'amazon_fees';
    public const CATEGORY_PROMOTION = 'promotion';

    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return SalesCategoryStrategyFactory::STRATEGY_REVENUE_EXPENSES;
    }

    /**
     * {@inheritdoc}
     */
    public function getSalesCategoryIdColumnName(): string
    {
        return 'sales_category_id';
    }

    public function getDataSeriesDepth(): int
    {
        return 1;
    }

    /**
     * {@inheritdoc}
     */
    public function getMapping(): array
    {
        return [
            'Revenue' => [
                '_tags' => [
                    SalesCategory::TAG_REVENUE
                ],
                '_mappingRuleBase' => FinanceEventCategory::PLUS_POSTFIX,
                'Product sales' => [
                    '_tags' => [
                        SalesCategory::TAG_PRODUCT_SALES
                    ],
                    '_mappingRule' => '(Shipment. || ShipmentSettle.) && ShipmentItem. && .Principal.ChargeAmount',
                    '_colorHex' => '#e5eefa'
                ],
                'Shipping charges' => [
                    '_mappingRule' => 'ShipmentItem. && .ShippingCharge.ChargeAmount',
                    '_colorHex' => '#c6f5b2'
                ],
                'Gift wrap charges' => [
                    '_colorHex' => '#d7d7d7',
                    'Gift wrap' => ['_mappingRule' => 'ShipmentItem. && GiftWrap.ChargeAmount'],
                    'Gift wrap taxes' => ['_mappingRule' => 'Shipment.ShipmentItem. && .GiftWrapTax.ChargeAmount'],
                ],
                'Refunds and chargebacks' => [
                    '_colorHex' => '#b6e2ed',
                    '_mappingRuleBase' => 'Refund. || Chargeback. || .REMOVAL_ORDER_DAMAGED || .PREPFEE_REFUND || .MULTICHANNEL_ORDER_DAMAGED || ChargeBackRecovery || Custom.',
                    'Amazon points' => ['_mappingRule' => '.CostOfPointsReturned'],
                    'Damaged goods refund' => ['_mappingRule' => '.REMOVAL_ORDER_DAMAGED && .AdjustmentAmount'],
                    'Discount' => ['_mappingRule' => '.PromotionAmount'],
                    'COD chargeback' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemFeeAdjustment.CODChargeback.FeeAmount'],
                    'FBA inbound fee' => [
                        'Preparation for shipment refund' => ['_mappingRule' => '.PREPFEE_REFUND && .AdjustmentAmount'],
                        'FBA International Inbound freight fee' => ['_mappingRule' => '.FBAInternationalInboundFreightFee']
                    ],
                    'FBA outbound fee' => [
                        '_mappingRuleBase' => '.FeeAmount',
                        'Gift wrap chargeback' => ['_mappingRule' => '.GiftwrapChargeback'],
                        'Shipping chargeback' => ['_mappingRule' => '.ShippingChargeback'],
                        'Gift wrap commission' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemFeeAdjustment.GiftwrapCommission.FeeAmount'],
                    ],
                    'Other fee' => [
                        '_mappingRuleBase' => '.FeeAmount',
                        'Referral fee' => ['_mappingRule' => '.Commission',],
                        'Variable closing fee' => ['_mappingRule' => '.VariableClosingFee'],
                        'Sales tax collection fee' => ['_mappingRule' => '.SalesTaxCollectionFee'],
                        'Fixed closing fee' => ['_mappingRule' => '.FixedClosingFee'],
                        'Get paid faster fee' => ['_mappingRule' => '.GetPaidFasterFee']
                    ],
                    'FBA service fee' => [
                        'Restocking fee' => [
                            '_mappingRule' => '.RestockingFee && .ChargeAmount',
                        ],
                        'Shipping holdback fee' => [
                            '_mappingRule' => '.ShippingHB && .FeeAmount'
                        ],
                        'Amazon accelerator fee' => [
                            '_mappingRule' => '.AmazonAcceleratorFee && .FeeAmount',
                        ],
                        'Amazon exclusives fee' => [
                            '_mappingRule' => '.AmazonExclusivesFee',
                        ],
                        'Amazon for all fee' => [
                            '_mappingRule' => '.AmazonForAllFee && .FeeAmount',
                        ],
                        'Amazon licensing fee' => [
                            '_mappingRule' => '.AmazonLicensingFee && .FeeAmount',
                        ]
                    ],
                    'Points adjusted' => ['_mappingRule' => '.PointsAdjusted && .ChargeAmount'],
                    'Taxes' => [
                        '_mappingRule' => '.ItemChargeAdjustment && .Tax',
                        'Product taxes' => [
                            '_mappingRule' => 'Custom.ProductTaxes.transactionValue',
                        ],
                        'Shipping taxes' => [
                            '_mappingRule' => 'Custom.ShippingTaxes.transactionValue',
                        ],
                        'Gift wrap taxes' => [
                            '_mappingRule' => 'Custom.GiftWrapTaxes.transactionValue',
                        ],
                        'Facilitator tax' => ['_mappingRule' => '.ItemTaxWithheld. && Facilitator'],
                        'Low value goods tax' => ['_mappingRule' => '.ItemTaxWithheld && .LowValueGoods'],
                        'Tax Collected at Source' => ['_mappingRule' => '.ItemChargeAdjustment && TCS-IGST']
                    ],
                    'Return shipping' => ['_mappingRule' => '.ReturnShipping && .ChargeAmount'],
                    'Multichannel order damaged' => ['_mappingRule' => '.MULTICHANNEL_ORDER_DAMAGED'],
                    'Chargeback recovery' => ['_mappingRule' => 'Adjustment.ChargeBackRecovery.AdjustmentAmount'],
                ],
                'Reimbursements' => [
                    '_colorHex' => '#ffeecc',
                    '_mappingRuleBase' => '.AdjustmentAmount',
                    'Carton information correction' => ['_mappingRule' => 'CLI_PLANNED_FEE_REIMBURSEMENT'],
                    'Damaged goods refund' => ['_mappingRule' => '.WAREHOUSE_DAMAGE'],
                    'Inbound carrier damage' => ['_mappingRule' => '.INBOUND_CARRIER_DAMAGE'],
                    'Inbound FBA shipment lost' => ['_mappingRule' => '.MISSING_FROM_INBOUND.'],
                    'Incorrect fees adjustment' => ['_mappingRule' => '.INCORRECT_FEES'],
                    'Lost multichannel order' => ['_mappingRule' => '.MULTICHANNEL_ORDER_LOST'],
                    'Lost removal order' => ['_mappingRule' => '.REMOVAL_ORDER_LOST'],
                    'Replaced product not received' => ['_mappingRule' => '.FREE_REPLACEMENT_REFUND_ITEMS'],
                    'Reversal reimbursement' => ['_mappingRule' => '.REVERSAL_REIMBURSEMENT'],
                    'Service error' => ['_mappingRule' => '.CS_ERROR_ITEMS'],
                    'Warehouse lost goods' => ['_mappingRule' => '.WAREHOUSE_LOST'],
                    'Wrong adjustment' => ['_mappingRule' => 'CS_ERROR_NON_ITEMIZED'],
                    'Wrong item return' => ['_mappingRule' => '.CRETURN_WRONG_ITEM'],
                    'Late multichannel order' => ['_mappingRule' => '.MULTICHANNEL_ORDER_LATE'],
                    'Amazon shipping reimbursement' => ['_mappingRule' => '.AmazonShippingReimbursement'],
                    'Lost or damaged reimbursement' => ['_mappingRule' => '.LostOrDamagedReimbursement'],
                ],
                'Taxes' => [
                    '_colorHex' => '#d1e7b7',
                    'Product taxes' => [
                        '_mappingRule' => '(ShipmentItem. && .Tax.ChargeAmount) || Retrocharge.BaseTax',
                    ],
                    'Shipping taxes' => [
                        '_mappingRule' => '(Shipment.ShipmentItem. && .ShippingTax.ChargeAmount) || Retrocharge.ShippingTax',
                    ],
                    'Wholesale liquidation' => [
                        '_mappingRule' => 'RemovalShipment.WHOLESALE_LIQUIDATION.RemovalShipmentItem.Standard.TaxAmount'
                    ],
                    'Facilitator tax' => [
                        '_mappingRule' => '(RetrochargeTaxWithheld. || RemovalShipment. ) && (.MarketplaceFacilitatorVAT || .MarketplaceFacilitatorTax || .MarketplaceFacilitator)',
                        'Wholesale liquidation' => [
                            '_mappingRule' => '.WHOLESALE_LIQUIDATION && .MarketplaceFacilitator'
                        ]
                    ],
                    'Commingling VAT transaction' => ['_mappingRule' => '.ComminglingVAT'],
                    'Tax withholding' => ['_mappingRule' => 'TaxWithholding.WithheldAmount_PLUS']
                ],
                'Additional income' => [
                    '_colorHex' => '#fdddec',
                    'Amazon A-to-Z guarantee' => ['_mappingRule' => 'Adjustment.A2ZGuaranteeRecovery.AdjustmentAmount'],
                    'Buyer recharge' => ['_mappingRule' => 'Adjustment.BuyerRecharge.AdjustmentAmount'],
                    'Debt recovery' => ['_mappingRule' => 'DebtRecovery.DebtRecoveryItem.RecoveryAmount'],
                    'Failed disbursement' => [
                        '_isExcludeFromCalculation' => true,
                        '_mappingRule' => 'Adjustment.FailedDisbursement.AdjustmentAmount'],
                    'FBA service fee' => [
                        'Payment method fee' => ['_mappingRule' => 'ShipmentItem. && PaymentMethodFee.ChargeAmount'],
                        'Subscription fee' => ['_mappingRule'=> 'ServiceFee. && .Subscription. && .FeeAmount'],
                        'Refused delivery fee' => ['_mappingRule'=> 'ServiceFee. && .RefusedDelivery. && .FeeAmount_PLUS']
                    ],
                    'Guarantee claim' => [
                        '_mappingRuleBase' => 'GuaranteeClaim.',
                        '_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemFeeAdjustment.Commission.FeeAmount',
                        'Shipping holdback fee' => ['_mappingRule' => '.ShippingHB'],
                        'Facilitator tax' => ['_mappingRule' => '.MarketplaceFacilitatorTax || .MarketplaceFacilitatorVAT'],
                        'Discount' => ['_mappingRule' => '.PromotionAmount'],
                        'Amazon exclusives fee' => ['_mappingRule' => '.AmazonExclusivesFee'],
                        'Variable closing fee' => ['_mappingRule' => '.VariableClosingFee'],
                        'Fixed closing fee' => ['_mappingRule' => '.FixedClosingFee'],
                        'Amazon accelerator fee' => ['_mappingRule' => '.AmazonAcceleratorFee'],
                        'COD chargeback' => ['_mappingRule' => '.CODChargeback'],
                    ],
                    'Non-subscription fee adjustment' => ['_mappingRule' => 'NonSubscriptionFeeAdj. && .AdjustmentAmount'],
                    'Postage fee' => [
                        'Postage refund delivery area surcharge' => ['_mappingRule' => 'PostageRefund_DeliveryAreaSurcharge. && .AdjustmentAmount'],
                        'Postage refund delivery confirmation' => ['_mappingRule' => 'PostageRefund_DeliveryConfirmation. && .AdjustmentAmount'],
                        'Postage refund carrier pickup' => ['_mappingRule' => 'PostageRefund_CarrierPickup. && .AdjustmentAmount'],
                        'Postage refund insurance' => ['_mappingRule' => 'PostageRefund_Insurance. && .AdjustmentAmount'],
                        'Postage refund postage' => ['_mappingRule' => 'PostageRefund_Postage && .AdjustmentAmount'],
                        'Postage refund tracking' => ['_mappingRule' => 'PostageRefund_Tracking. && .AdjustmentAmount'],
                        'Return postage billing oversize surcharge' => ['_mappingRule' => 'Adjustment. && OversizeSurcharge && .AdjustmentAmount'],
                        'Postage refund signature confirmation' => ['_mappingRule' => '.PostageRefund_SignatureConfirmation'],
                        'Postage refund congestion charge' => ['_mappingRule' => '.PostageRefund_CongestionCharge'],
                        'Postage refund VAT' => ['_mappingRule' => '.PostageRefund_VAT'],
                        'Postage refund fuel surcharge' => ['_mappingRule' => 'Adjustment.PostageRefund_FuelSurcharge.AdjustmentAmount'],
                        'Postage refund adjustment' => ['_mappingRule' => 'Adjustment.PostageRefund.AdjustmentAmount_PLUS'],
                    ],
                    'Referral fee correction' => ['_mappingRule' => '.CommissionCorrection'],
                    'Reserve credit' => [
                        '_isExcludeFromCalculation' => true,
                        '_mappingRule' => 'ReserveCredit. && .AdjustmentAmount'],
                    'SAFE-T claim' => ['_mappingRule' => '(SAFETReimbursement. || SafetReimbursement.) && .ReimbursedAmount'],
                    'SAFE-T claim reimbursement' => ['_mappingRule' => '(SAFETReimbursementCharge. || SafeReimbursementCharge.) && .ChargeAmount'],
                    'Subscription fee correction' => ['_mappingRule' => 'SubscriptionFeeCorrection. && .AdjustmentAmount'],
                    'FBA outbound fee' => [
                        'FBA per unit fulfillment fee adjustment' => ['_mappingRule' => 'FBAPerUnitFulfillmentFee. && .FeeAmount'],
                        'FBA weight based fee' => ['_mappingRule' => 'FBAWeightBasedFee.FeeAmount'],
                        'FBA customer return fee' => ['_mappingRule' => 'FBACustomerReturnPerOrderFee.FeeAmount'],
                    ],
                    'FBA inbound fee' => [
                        'FBA international inbound freight tax and duty fee' => ['_mappingRule' => '.FBAInternationalInboundFreightTaxAndDuty'],
                        'FBA International Inbound freight fee' =>  ['_mappingRule' => '.FBAInternationalInboundFreightFee']
                    ],
                    'FBA multitier per unit fee' => [
                        '_mappingRule' => 'FBAMultitierPerUnitFee. && .FeeAmount'
                    ],
                    'Sales commission fee' => [
                        '_mappingRule' => 'SalesCommission. && .FeeAmount'
                    ],
                    'Re-evaluation' => ['_mappingRule' => 'RE_EVALUATION'],
                    'Other fee' => [
                        'Miscellaneous' => ['_mappingRule' => 'Adjustment.MiscAdjustment.AdjustmentAmount']
                    ],
                    'Export charge' => [
                        '_mappingRule' => '.ExportCharge'
                    ],
                    'Prime wardrobe reimbursement' => [
                        '_mappingRule' => 'PRIME_WARDROBE_REIMBURSEMENT'
                    ],
                    'Loan advance' => [
                        '_mappingRule' => 'LoanServicing.LoanAdvance.LoanAmount',
                    ],
                    'Fee adjustment'=> [
                        '_mappingRule' => '.FeeAdjustment && .AdjustmentAmount',
                    ],
                    'Pay with Amazon' => [
                        '_mappingRuleBase' => 'PayWithAmazon.',
                        '_mappingRule' => 'PayWithAmazon.Charge.Principal.ChargeAmount',
                        'EPSO cross border fee' => [
                            '_mappingRule' => 'PayWithAmazon. && .EPSOCrossBorderFee',
                        ],
                        'EPSO payment settle fee' => [
                            '_mappingRule' => 'PayWithAmazon. &&.EPSOPaymentSettleFeeOnFinalize',
                        ]
                    ],
                    'Wholesale liquidation' => [
                        '_mappingRule' => '.WHOLESALE_LIQUIDATION. && .Revenue',
                    ],
                    'Micro deposit failed' => [
                        '_mappingRule' => '.Micro Deposit && Failed',
                    ],
                    'Undefined' => [
                        '_mappingRule' => '.',
                        '_mappingRuleOrder' => 99
                    ],
                ],
            ],
            'Expenses' => [
                '_mappingRuleBase' => '_MINUS',
                'Amazon fees' => [
                    '_tags' => [
                        SalesCategory::TAG_AMAZON_FEES
                    ],
                    '_colorHex' => '#d70606',
                    'FBA inbound fee' => [
                        '_mappingRuleBase' => '.FeeAmount',
                        'FBA inbound transportation fee' => ['_mappingRule' => 'FBAInboundTransportationFee.'],
                        'FBA inbound transportation program fee' => ['_mappingRule' => 'FBAInboundTransportationProgramFee.'],
                        'FBA International Inbound freight fee' => ['_mappingRule' => 'FBAInternationalInboundFreightFee.'],
                        'FBA international inbound freight tax and duty fee' => ['_mappingRule' => 'FBAInternationalInboundFreightTaxAndDuty.'],
                        'FBA inbound defect fee' => ['_mappingRule' => 'FBAInboundDefectFee.'],
                        'Carton information correction' => ['_mappingRule' => 'FBAInboundShipmentCartonLevelInfoFee.'],
                        'FBA transportation fee' => ['_mappingRule' => 'FBATransportationFee.'],
                        'Premium placement fee' => ['_mappingRule' => 'PREMIUM_PLACEMENT'],
                        'Global inbound transportation duty fee' => ['_mappingRule' => '.GlobalInboundTransportationDuty'],
                        'Global inbound transportation freight fee' => ['_mappingRule' => '.GlobalInboundTransportationFreight'],
                        'Convenience fee' => ['_mappingRule' => 'CONVENIENCE'],
                    ],
                    'FBA outbound fee' => [
                        'FBA fulfillment fee' => ['_mappingRule' => 'FBAPerUnitFulfillmentFee.'],
                        'Gift wrap chargeback' => ['_mappingRule' => 'GiftwrapChargeback.'],
                        'Shipping chargeback' => ['_mappingRule' => 'ShippingChargeback.'],
                        'Gift wrap commission' => ['_mappingRule' => 'GiftwrapCommission.'],
                        'Shipping holdback fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.ShippingHB.FeeAmount'],
                        'FBA customer return fee' => ['_mappingRule' => 'ServiceFee. && (FBACustomerReturnPerOrderFee || .FBACustomerReturnPerUnitFee || FBACustomerReturnWeightBasedFee) && .FeeAmount'],
                        'FBA disposal fee' => ['_mappingRule' => 'ServiceFee.Fee.FBADisposalFee.FeeAmount'],
                        'FBA removal fee' => ['_mappingRule' => 'ServiceFee.Fee.FBARemovalFee.FeeAmount'],
                        'FBA per order fulfillment fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.FBAPerOrderFulfillmentFee.FeeAmount'],
                        'FBA weight based fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.FBAWeightBasedFee.FeeAmount'],
                        'Plan of Action service fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.POAServiceFee.FeeAmount'],
                        'Plan of Action fulfillment fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.PoAPerUnitFulfillmentFee.FeeAmount'],
                    ],
                    'FBA service fee' => [
                        'FBA storage fee' => ['_mappingRule' => 'FBAStorageFee || STARStorageFee'],
                        'FBA long term storage fee' => ['_mappingRule' => 'FBALongTermStorageFee'],
                        'Labeling' => ['_mappingRule' => 'LabelingFee'],
                        'Subscription fee' => ['_mappingRule' => 'Subscription'],
                        'Bubble wrap' =>  ['_mappingRule' => 'BubblewrapFee'],
                        'FBA inventory storage overage fee' => ['_mappingRule' => 'FBAOverageFee'],
                        'FBA inventory fee' => ['_mappingRule' => '.FBA Inventory Fee.'],
                        'Taping fee' => ['_mappingRule' => 'TapingFee'],
                        'VAT registration fee' => ['_mappingRule' => 'VatRegistrationFee'],
                        'VAT amazon technology fee' => ['_mappingRule' => 'VatAmazonTechnologyFee'],
                        'Polybagging fee' => ['_mappingRule' => 'PolybaggingFee'],
                        'Vine program fee' => ['_mappingRule' => 'VineFee'],
                        'Other services' => ['_mappingRule' => 'PaidServicesFee'],
                        'Amazon imaging fee' => ['_mappingRule' => 'ImagingServicesFee'],
                        'Amazon exclusives fee' => ['_mappingRule' => 'AmazonExclusivesFee'],
                        'Amazon accelerator fee' => ['_mappingRule' => 'AmazonAcceleratorFee'],
                        'Amazon for all fee' => ['_mappingRule' => 'AmazonForAllFee'],
                        'Amazon licensing fee' => ['_mappingRule' => 'AmazonLicensingFee'],
                        'Grade and resell fee' => ['_mappingRule' => 'ReCommerceGradingAndListingCharge'],
                        'Try before you buy fee' => ['_mappingRule' => 'FBATryBeforeYouBuyMultitierPerUnitFee'],
                        'Vat partner fee' => ['_mappingRule' => 'VatPartnerFee'],
                        'AWD storage fee' => ['_mappingRule' => 'STARStorageBilling'],
                        'AWD transportation fee' => ['_mappingRule' => 'AmazonUpstreamStorageTransportationFee'],
                        'AWD processing fee' => ['_mappingRule' => 'AmazonUpstreamProcessingFee'],
                        'Payment method fee' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.PaymentMethodFee.ChargeAmount'],
                        'Opaque bagging' => ['_mappingRule' => 'ServiceFee.Fee.OpaqueBaggingFee.FeeAmount_MINUS'],
                    ],
                    'Postage fee' => [
                        'Return postage billing postage' => ['_mappingRule' => 'ReturnPostageBilling_Postage. || ReturnPostageBilling_postage.'],
                        'Return postage billing tracking' => ['_mappingRule' => 'ReturnPostageBilling_Tracking'],
                        'Postage billing postage' => ['_mappingRule' => '.PostageBilling_Postage'],
                        'Postage billing carrier pickup' => ['_mappingRule' => 'PostageBilling_CarrierPickup'],
                        'Postage billing delivery confirmation' => ['_mappingRule' => 'PostageBilling_DeliveryConfirmation || ReturnPostageBilling_DeliveryConfirmation'],
                        'Postage billing delivery area surcharge' => ['_mappingRule' => '.PostageBilling_DeliveryAreaSurcharge'],
                        'Postage billing tracking' => ['_mappingRule' => 'PostageBilling_Tracking'],
                        'Postage billing signature confirmation' => ['_mappingRule' => '.PostageBilling_SignatureConfirmation'],
                        'Return postage billing commission' => ['_mappingRule' => 'ReturnPostageBilling_postage_commission'],
                        'Return postage billing delivery area surcharge' => ['_mappingRule' => 'ReturnPostageBilling_DeliveryAreaSurcharge'],
                        'Return postage billing VAT' => ['_mappingRule' => 'ReturnPostageBilling_VAT'],
                        'Return postage billing delivery confirmation' => ['_mappingRule' => 'ReturnPostageBilling_DeliveryConfirmation'],
                        'Return postage billing transaction fee' => ['_mappingRule' => 'ReturnPostageBilling_TransactionFee'],
                        'Postage billing insurance' => ['_mappingRule' => 'PostageBilling_Insurance'],
                        'Return postage billing fuel surcharge' => ['_mappingRule' => 'ReturnPostageBilling_FuelSurcharge'],
                        'Postage billing oversize surcharge' => ['_mappingRule' => '.PostageBilling_OversizeSurcharge'],
                        'Return postage billing oversize surcharge' => ['_mappingRule' => 'ReturnPostageBilling_OversizeSurcharge'],
                        'Postage refund congestion charge' => ['_mappingRule' => 'PostageRefund_CongestionCharge'],
                        'MFN postage fee' => ['_mappingRule' => 'ServiceFee.Fee.MFNPostageFee.FeeAmount'],
                        'Postage billing fuel surcharge' => ['_mappingRule' => 'Adjustment.PostageBilling_FuelSurcharge.AdjustmentAmount'],
                        'Postage billing VAT' => ['_mappingRule' => 'Adjustment.PostageBilling_VAT.AdjustmentAmount'],
                        'Postage billing congestion charge' => ['_mappingRule' => 'Adjustment.PostageBilling_CongestionCharge.AdjustmentAmount'],
                    ],
                    'Other fee' => [
                        'Referral fee' => ['_mappingRule' => 'ShipmentItem.ItemFee.Commission.FeeAmount'],
                        'Fixed closing fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.FixedClosingFee.FeeAmount'],
                        'Variable closing fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.VariableClosingFee.FeeAmount'],
                        'Renewed program fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.RenewedProgramFee.FeeAmount'],
                        'Seller deal payment' => ['_mappingRule' => 'SellerDealPayment.feeAmount'],
                        'Coupon redemption fee' => ['_mappingRule' => 'CouponPayment.FeeComponent.CouponRedemptionFee.FeeAmount'],
                        'Miscellaneous' => ['_mappingRule' => 'Adjustment.MiscAdjustment.AdjustmentAmount'],
                        'Review enrollment fee' => ['_mappingRule' => 'SellerReviewEnrollmentPayment.FeeComponent.ReviewEnrollmentFee.FeeAmount'],
                        'Sales tax collection fee' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.SalesTaxCollectionFee.FeeAmount'],
                        'COD chargeback' => ['_mappingRule' => 'Shipment.ShipmentItem.ItemFee.CODChargeback.FeeAmount'],
                        'EPSO chargeback fee' => ['_mappingRule' => 'EPSOChargebackFee && FeeAmount'],
                        'EPSO cross border fee' => ['_mappingRule' => 'EPSOCrossBorderFee && FeeAmount'],
                        'EPSO payment authorization fee' => ['_mappingRule' => 'EPSOPaymentAuthFeeOnFinalize && FeeAmount'],
                        'EPSO payment settle fee' => ['_mappingRule' => 'EPSOPaymentSettleFeeOnFinalize && FeeAmount'],
                        'FBA liquidation' => ['_mappingRule' => 'RemovalShipment.WHOLESALE_LIQUIDATION.RemovalShipmentItem.Standard.FeeAmount'],
                        'Get paid faster fee' => ['_mappingRule' => 'GetPaidFasterFee && FeeAmount'],
                        'Pay with Amazon' => ['_mappingRule' => 'PayWithAmazon. &&.ChargeAmount'],
                    ],
                    'Service fee' => [
                        'CSBA fee' => ['_mappingRule' => 'CSBAFee'],
                        'MFN delivery fee' => ['_mappingRule' => 'MFNDeliveryServiceFee']
                    ],
                    'Wholesale liquidation' => [
                        'Customer return based' => ['_mappingRule' => 'CUSTOMER_RETURN_BASED_WHOLESALE_LIQUIDATION && FeeAmount']
                    ],
                    'Technology fee' => ['_mappingRule' => 'ShipmentItem && .TechnologyFee && .FeeAmount_MINUS']
                ],
                'Taxes' => [
                    '_colorHex' => '#f89291',
                    'VAT' => [
                        '_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                        '_tags' => [
                            SalesCategory::TAG_MANUAL_VAT
                        ],
                    ],
                    'Facilitator tax' => [
                        '_mappingRule' => "(Retrocharge. || Shipment. || ShipmentSettle. || RemovalShipment. || RemovalShipmentAdjustment.) && .MarketplaceFacilitator && (.ChargeAmount || TaxAmount)",
                        'Wholesale liquidation' => [
                            '_mappingRule' => ".WHOLESALE_LIQUIDATION && .MarketplaceFacilitator",
                        ]
                    ],
                    'Coupon payment' => ['_mappingRule' => 'CouponPayment.ChargeComponent.Tax.ChargeAmount'],
                    'Commingling VAT transaction' => ['_mappingRule' => '.ComminglingVAT'],
                    'Low value goods tax' => [
                        '_mappingRule' => '.LowValueGoods && .ChargeAmount'
                    ],
                    'Seller deal payment' => ['_mappingRule' => 'SellerDealPayment.taxAmount'],
                    'Seller review enrollment payment' => ['_mappingRule' => 'SellerReviewEnrollmentPayment. && .Tax. && .ChargeAmount'],
                    'Product taxes' => [
                        '_mappingRule' => 'Custom.ProductTaxes.transactionValue',
                        'Retrocharge reversal' => ['_mappingRule' => 'Retrocharge.BaseTax'],
                    ],
                    'Shipping taxes' => [
                        '_mappingRule' => 'Custom.ShippingTaxes.transactionValue',
                        'Retrocharge reversal' => ['_mappingRule' => 'Retrocharge.ShippingTax'],
                    ],
                    'Gift wrap taxes' => [
                        '_mappingRule' => 'Custom.GiftWrapTaxes.transactionValue',
                    ],
                    'Tax amount adjustment' => [
                        'Wholesale liquidation' => [
                            '_mappingRule' => '.WHOLESALE_LIQUIDATION && .TaxAmountAdjustment'
                        ]
                    ],
                    'Wholesale liquidation' => [
                        'Customer return based' => [
                            '_mappingRule' => 'CUSTOMER_RETURN_BASED_WHOLESALE_LIQUIDATION && TaxWithheld_MINUS'
                        ]
                    ],
                    'Tax Collected at Source' => [
                        '_mappingRule' => 'Shipment.ShipmentItem.ItemCharge.TCS-IGST.ChargeAmount_MINUS'
                    ],
                    'Tax withholding' => [
                        '_mappingRule' => 'TaxWithholding.WithheldAmount_MINUS'
                    ],
                    'Tax Deducted at Source' => [
                        '_mappingRule' => 'TaxesWithheld.ItemTDS.ChargeAmount_MINUS'
                    ],
                ],
                'Refunds and Chargebacks' => [
                    '_colorHex' => '#0f3775',
                    'Discount' => ['_mappingRule' => 'Chargeback.ShipmentItemAdjustment.PromotionAdjustment.PromotionMetaDataDefinitionValue.PromotionAmount'],
                    'Export charge' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ExportCharge.ChargeAmount || Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.ExportCharge.ChargeAmount'],
                    'Generic bad debt deduction' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GenericDeduction.ChargeAmount'],
                    'Gift wrap' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrap.ChargeAmount || Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrap.ChargeAmount'],
                    'Goodwill' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Goodwill.ChargeAmount'],
                    'Product sales' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount || Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount'],
                    'Retained commission' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemFeeAdjustment.RefundCommission.FeeAmount || Chargeback.ShipmentItemAdjustment.ItemFeeAdjustment.RefundCommission.FeeAmount'],
                    'Return shipping' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ReturnShipping.ChargeAmount || Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.ReturnShipping.ChargeAmount'],
                    'Shipping charge' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingCharge.ChargeAmount'],
                    'Shipping chargeback' => ['_mappingRule' => 'Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingCharge.ChargeAmount'],
                    'Shipping holdback fee' => ['_mappingRule' => 'Chargeback.ShipmentItemAdjustment.ItemFeeAdjustment.ShippingHB.FeeAmount'],
                    'Taxes' => [
                        '_mappingRule' => 'Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount || Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount',
                        'Gift wrap tax' => ['_mappingRule' => 'Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrapTax.ChargeAmount || Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrapTax.ChargeAmount'],
                        'Shipping taxes' => ['_mappingRule' => 'Chargeback.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount || Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount'],
                        'Facilitator tax' => ['_mappingRule' => 'Refund.ShipmentItemAdjustment. && .MarketplaceFacilitatorTax-RestockingFee && .ChargeAmount']
                    ],
                    'Chargeback recovery' => ['_mappingRule' => '.ChargeBackRecovery']
                ],
                'Guarantee claim' => [
                    '_colorHex' => '#2f80ed',
                    'Goodwill' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.Goodwill.ChargeAmount'],
                    'Product sales' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount'],
                    'Retained commission' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemFeeAdjustment.RefundCommission.FeeAmount'],
                    'Shipping charge' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingCharge.ChargeAmount'],
                    'Shipping holdback fee' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemFeeAdjustment.ShippingHB.FeeAmount'],
                    'Taxes' => [
                        '_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount',
                        'Shipping taxes' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount'],
                    ],
                    'FBA service fee' => [
                        'Payment method fee' => ['_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.PaymentMethodFee.ChargeAmount']
                    ],
                    'Return shipping charge' => [
                        '_mappingRule' => 'GuaranteeClaim.ShipmentItemAdjustment.ItemChargeAdjustment.ReturnShipping.ChargeAmount'
                    ],
                ],
                'Promotion' => [
                    '_colorHex' => '#09a646',
                    'Discount' => ['_mappingRule' => 'ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount'],
                ],
                'Ads (PPC)' => [
                    '_id' => 'ppc_1',
                    '_tags' => [
                        SalesCategory::TAG_PPC_COSTS,
                    ],
                    'Sponsored Products' => [
                        '_mappingRule' => CostsApplier::SPONSORED_PRODUCTS_PATH
                    ],
                    'Sponsored Brands' => [
                        'Product collection' => [
                            '_mappingRule' => sprintf(CostsApplier::SPONSORED_BRANDS_PATH, 'PRODUCT_COLLECTION')
                        ],
                        'Store spotlight' => [
                            '_mappingRule' => sprintf(CostsApplier::SPONSORED_BRANDS_PATH, 'STORE_SPOTLIGHT')
                        ],
                        'Video' => [
                            '_mappingRule' => sprintf(CostsApplier::SPONSORED_BRANDS_PATH, 'VIDEO')
                                . ' || '
                                . sprintf(CostsApplier::SPONSORED_BRANDS_PATH, 'BRAND_VIDEO')
                        ],
                    ],
                    'Sponsored Display' => [
                        '_mappingRule' => CostsApplier::SPONSORED_DISPLAY_PATH
                    ],
                ],
                'Cost of goods' => [
                    '_tags' => [
                        SalesCategory::TAG_MANUAL_COST_OF_GOODS
                    ],
                    '_colorHex' => '#8bc34a',
                ],
                'FBM shipping costs' => [
                    '_id' => SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS,
                    '_tags' => [
                        SalesCategory::TAG_MANUAL_FBM_SHIPPING_COSTS
                    ],
                    '_colorHex' => '#ec7100',
                ],
                'Indirect costs' => [
                    '_colorHex' => '#a6a6a6',
                    '_id' => SalesCategory::CATEGORY_EXPENSES_INDIRECT_COSTS,
                    '_tags' => [
                        SalesCategory::TAG_MANUAL_INDIRECT_COSTS
                    ],
                ],
                'Other costs' => [
                    '_colorHex' => '#7437a5',
                    'Amazon points' => ['_mappingRule' => 'Shipment.ShipmentItem.CostOfPointsGranted'],
                    'Debt recovery' => ['_mappingRule' => 'DebtRecovery.DebtRecoveryItem.OriginalAmount'],
                    'Reimbursement clawback' => ['_mappingRule' => 'Adjustment.MISSING_FROM_INBOUND_CLAWBACK.AdjustmentAmount || Adjustment.COMPENSATED_CLAWBACK.AdjustmentAmount || Adjustment.ReimbursementClawback.AdjustmentAmount_MINUS'],
                    'Reserve debit' => [
                        '_isExcludeFromCalculation' => true,
                        '_mappingRule' => 'Adjustment.ReserveDebit.AdjustmentAmount'],
                    'Undefined' => [
                        '_mappingRule' => '.',
                        '_mappingRuleOrder' => 99
                    ],
                    'Non-subscription fee adjustment' => ['_mappingRule' => 'NonSubscriptionFeeAdj. && .AdjustmentAmount_MINUS'],
                    'Subscription fee correction' => ['_mappingRule' => 'SubscriptionFeeCorrection. && .AdjustmentAmount_MINUS'],
                    'Buyer recharge' => ['_mappingRule' => 'Adjustment.BuyerRecharge.AdjustmentAmount'],
                    'Amazon A-to-Z guarantee' => ['_mappingRule' => '.A2ZGuaranteeRecovery'],
                    'Referral fee correction' => ['_mappingRule' => '.CommissionCorrection'],
                    'Other fee' => [
                        'Amazon shipping chargebacks' => ['_mappingRule' => '.AmazonShippingChargebacks'],
                        'Amazon shipping charges' => ['_mappingRule' => '.AmazonShippingCharges'],
                    ],
                    'Revenue adjustment' => [
                        'Wholesale liquidation' => [
                            '_mappingRule' => '.WHOLESALE_LIQUIDATION && .RevenueAdjustment && .Standard',
                        ]
                    ],
                    'Payment retraction' => ['_mappingRule' => '.PAYMENT_RETRACTION_',],
                    'Policy violation' => ['_mappingRule' => '.Policy violation.',],
                    'Micro deposit' => ['_mappingRule' => 'AdhocDisbursement.Micro Deposit.TransactionAmount',],
                    'Loan payment' => ['_mappingRule' => 'LoanServicing.LoanPayment.LoanAmount || LoanServicing.LoanAmount',],
                    'Disbursement correction' => ['_mappingRule' => 'Adjustment.DisbursementCorrection.AdjustmentAmount',],
                ],
                'Other fees' => [
                    '_colorHex' => '#bb6bd9',
                    '_tags' => [
                        SalesCategory::TAG_MANUAL_OTHER_FEES
                    ],
                ],
            ],
            'Ignored' => [
                '_mappingRule' => '.PerUnitAmount || .TotalAmount || .totalAmount || ProductAdsPayment. || USER_DEFINED. || TCS-SGST. || TCS-CGST. || BaseAmount_PLUS',
                '_isVisible' => false,
                '_isExcludeFromCalculation' => true,
                'Additional income' => [
                    '_isVisible' => false,
                    '_isExcludeFromCalculation' => true,
                    'Debt recovery' => [
                        '_isVisible' => false,
                        '_isExcludeFromCalculation' => true,
                        '_mappingRule' => 'DebtRecovery.RecoveryAmount || DebtRecovery.ChargeInstrument.Amount']
                ]
            ],
        ];
    }

    public function isRevenueCategories(array $categoryInfo): bool
    {
        return false !== strpos($categoryInfo['path'], 'revenue');
    }
}
