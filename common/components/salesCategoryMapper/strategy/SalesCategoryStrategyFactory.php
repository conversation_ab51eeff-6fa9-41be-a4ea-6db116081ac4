<?php

namespace common\components\salesCategoryMapper\strategy;

class SalesCategoryStrategyFactory
{
    public const DEFAULT_STRATEGY = 'custom';
    public const STRATEGY_REVENUE_EXPENSES = 'revenue_expenses';
    public const STRATEGY_CUSTOM = 'custom';

    public function getSupportedStrategyTypes(): array
    {
        $supportedStrategyTypes = [];

        foreach ($this->getAllStrategies() as $strategy) {
            $supportedStrategyTypes[] = $strategy->getType();
        }

        return $supportedStrategyTypes;
    }

    /**
     * @return SalesCategoryStrategyInterface[]
     */
    public function getAllStrategies(): array
    {
        return [
            new RevenueExpensesStrategy(),
            new CustomStrategy()
        ];
    }

    public function getStrategyByType(string $type): SalesCategoryStrategyInterface
    {
        $supportedStrategies = $this->getAllStrategies();

        foreach ($supportedStrategies as $strategy) {
            if ($strategy->getType() === $type) {
                return $strategy;
            }
        }

        throw new \Exception("Unable to find appropriate sales category strategy for type '$type'");
    }
}