<?php

namespace common\components\salesCategoryMapper\strategy;

use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use yii\db\ActiveQuery;

/**
 * Used for all sales category strategies.
 */
interface SalesCategoryStrategyInterface
{
    /**
     * Builds and returns sales categories mapping data as tree structure.
     *
     * @return array
     */
    public function getMapping(): array;

    /**
     * Returns strategy type.
     *
     * @return string
     */
    public function getType(): string;

    /**
     * Returns column name used for this strategy in finance_event_category table.
     *
     * @return string
     */
    public function getSalesCategoryIdColumnName(): string;

    /**
     * Depth of data series which are displaying on the chart.
     *
     * @return int
     */
    public function getDataSeriesDepth(): int;

    /**
     * Calculates and returns product sales sum (principal transactions).
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getProductSalesAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * Calculates and returns product PPC costs sum (amazon ads API data).
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getPPCAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * Calculates and returns sum of all positive amounts.
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getRevenueAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * Calculates and returns sum of all negative amounts.
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getExpensesAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * Calculates and returns sum of net purchase price amounts.
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getNetPurchasePriceAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * Calculates and returns sum of all amazon fees amounts.
     *
     * @param array $flatTreeData
     * @return float
     */
    public function getAmazonFeesAmountByFlatTreeData(array $flatTreeData): float;

    /**
     * @return ActiveQuery
     */
    public function getTransactionExtendedViewQuery(): ActiveQuery;

    /**
     * @return ActiveQuery
     */
    public function getAmazonOrderExtendedViewQuery(): ActiveQuery;

    /**
     * @return ActiveQuery
     */
    public function getAmazonOrderInProgressExtendedViewQuery(): ActiveQuery;

    public function isRevenueCategories(array $categoryInfo): bool;
}
