<?php

namespace common\components\dataImportExport;

use common\components\fileDataReader\DataReaderFactory;
use common\components\fileDataWriter\DataWriterFactory;

/**
 * Contains list of supported handlers.
 */
class SupportedFileFormats
{
    public const EXPORT_SUPPORTED_FORMATS = [
        DataWriterFactory::DATA_FORMAT_CSV,
        DataWriterFactory::DATA_FORMAT_TXT,
    ];

    public const IMPORT_SUPPORTED_FORMATS = [
        DataReaderFactory::DATA_FORMAT_CSV,
    ];
}
