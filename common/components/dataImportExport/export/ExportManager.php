<?php

namespace common\components\dataImportExport\export;

use common\components\dataImportExport\export\exporter\ExporterFactory;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use common\models\customer\DataExport;
use common\models\customer\DataExportPart;
use common\models\customer\DataExportTemplate;
use mikemadisonweb\rabbitmq\components\Producer;

/**
 * Class ExportManager.
 */
class ExportManager
{
    /**
     * @var ExporterFactory
     */
    private $exporterFactory;

    /**
     * ExportManager constructor.
     */
    public function __construct()
    {
        $this->exporterFactory = new ExporterFactory();
    }

    /**
     * Creates export and export parts, enqueues parts processing.
     */
    public function export(
        string $handlerName,
        string $outputFormat,
        int $templateId = null,
        string $type = DataExport::TYPE_MANUAL
    ): DataExport {
        $dataExport = new DataExport();
        $dataExport->type = $type;
        $dataExport->handler_name = $handlerName;
        $dataExport->output_format = $outputFormat;
        $dataExport->status = DataExport::STATUS_NEW;
        $dataExport->language_code = substr(\Yii::$app->language, 0, 3);
        $dataExport->template_id = $templateId;
        $dataExport->save(false);

        try {
            $this->generateParts($dataExport);
        } catch (\Throwable $e) {
            $dataExport->exception = $e->getMessage();
            $dataExport->setFinished();
            $dataExport->save(false);
            throw $e;
        }

        return $dataExport;
    }

    public function renewTerminated(DataExport $dataExportOld): void
    {
        $dataExport = clone $dataExportOld;
        $dataExport->count_parts = 0;
        $dataExport->count_all_items = 0;
        $dataExport->count_exported_items = 0;
        $dataExport->count_errors = 0;
        $dataExport->exception .= date("[Y-m-d H:i:s]") . " renewed\n";
        $dataExport->status = DataExport::STATUS_NEW;
        $dataExport->started_at = null;
        $dataExport->finished_at = null;
        $dataExport->updated_at = date('Y-m-d H:i:s');
        unset($dataExport->id);
        $dataExport->isNewRecord = true;
        $dataExport->save(false);

        $dataExportOld->delete();

        try {
            $this->generateParts($dataExport);
        } catch (\Throwable $e) {
            $dataExport->exception = $e->getMessage();
            $dataExport->setFinished();
            $dataExport->save(false);
            throw $e;
        }
    }

    /**
     * Generates data export parts objects.
     *
     * @param DataExport   $dataExport
     * @param ExportConfig $exportConfig
     */
    private function generateParts(DataExport $dataExport): void
    {
        $exportConfig = new ExportConfig();
        /** @var DataExportTemplate $template */
        $template = $dataExport->getTemplate()->one();
        $exportConfig->criteria = $template->criteria ?? [];
        $exportConfig->extra_criteria = $template->extra_criteria ?? [];
        $exportConfig->fields = $template->fields ?? [];

        $exporter = $this->exporterFactory->getExporter($dataExport->handler_name);
        $countAllItems = $exporter->getCountAllItems($exportConfig);

        $limitOffsetParts = [];
        if (method_exists($exporter, 'getLimitOffsetParts')) {
            $limitOffsetParts = $exporter->getLimitOffsetParts($exportConfig);
        }

        $dataExport->count_all_items = $countAllItems;
        $dataExport->save(false);

        $limit = $exporter->getItemsPerPart();
        $offset = 0;
        $partNo = 0;

        while (true) {
            $exportPart = new DataExportPart();

            try {
                $exportPart->status = DataExportPart::STATUS_NEW;
                $exportPart->data_export_id = $dataExport->id;
                $exportPart->part_no = $partNo;
                $exportPart->offset = $offset;
                $exportPart->limit = $limit;
                if (!empty($limitOffsetParts)) {
                    $exportPart->offset = $limitOffsetParts[$partNo]['offset'] ?? $offset;
                    $exportPart->limit = $limitOffsetParts[$partNo]['limit'] ?? $limit;
                }
                $exportPart->save(false);

                $this->enqueuePartProcessing($exportPart);
            } catch (\Throwable $e) {
                $exportPart->exception = $e->getMessage();
                $exportPart->setFinished();
                $exportPart->save(false);
            }

            $offset += $limit;
            $partNo++;
            if ($offset >= $countAllItems) {
                break;
            }
        }
    }

    /**
     * @param DataExportPart $dataExportPart
     */
    private function enqueuePartProcessing(DataExportPart $dataExportPart): void
    {
        /** @var Producer $producer */
        $producer = \Yii::$app->rabbitmq->getProducer(MessageAbstract::PRODUCER_NAME);
        $message = json_encode([
            'data_export_part_id' => $dataExportPart->id,
            'customer_id' => \Yii::$app->dbManager->getCustomerId(),
        ]);
        $producer->publish($message, MessageInterface::EXCHANGE_NAME_DATA_EXPORT, 'process-part');
    }
}
