<?php

namespace common\components\dataImportExport\export\template;

use common\components\dataImportExport\export\template\filter\FilterFactory;
use common\components\LogToConsoleTrait;
use yii\db\Query;

class QueryModifier
{
    use LogToConsoleTrait;

    protected array $templateStructure;
    protected FilterFactory $filterFactory;

    public function __construct(array $templateStructure)
    {
        $this->templateStructure = $templateStructure;
        $this->filterFactory = new FilterFactory();
    }

    public function modify(Query $query, array $templateFields, array $criteria): void
    {
        if (empty($templateFields)) {
            $templateFields = array_keys($this->templateStructure['columns']);
        }
        $this->applySelect($query, $templateFields);
        $this->applyFilters($query, $criteria);
    }

    protected function applyFilters(Query $query, array $criteria): void
    {
        foreach ($criteria as $templateField => $criterion) {
            try {
                $tableField = $this->getTableField($templateField);

                if (empty($tableField)) {
                    continue;
                }

                $filterType = strtolower($criterion['select']);
                $filter = $this->filterFactory->getFilter($filterType);

                $filter->apply($query, $tableField, $criterion);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    protected function applySelect(Query $query, array $fields): void
    {
        foreach ($fields as $k => $templateField) {
            $tableField = $this->getTableField($templateField);

            if (empty($tableField)) {
                unset($fields[$k]);
                continue;
            }

            $fields[$k] = $tableField !== $templateField
                ? "{$tableField} as {$templateField}"
                : $tableField;
        }

        if (empty($fields)) {
            return;
        }

        $query->select(implode(',', $fields));
    }

    private function getTableField(string $templateField): ?string
    {
        if (empty($this->templateStructure['columns'][$templateField])) {
            return null;
        }

        return $this->templateStructure['columns'][$templateField]['table_field'] ?? $templateField;
    }
}