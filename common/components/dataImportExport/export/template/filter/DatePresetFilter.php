<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Query;

class DatePresetFilter implements FilterInterface
{
    public static function getDatePreset(): array
    {
        return [
            'today' => \Yii::t('import_export', 'Today'),
            'yesterday' => \Yii::t('import_export', 'Yesterday'),
            'current_week' => \Yii::t('import_export', 'Current Week'),
            'last_7_days' => \Yii::t('import_export', 'Last 7 Days'),
            'current_month' => \Yii::t('import_export', 'Current Month'),
            'last_month' => \Yii::t('import_export', 'Last Month'),
            'last_30_days' => \Yii::t('import_export', 'Last 30 Days'),
            'last_90_days' => \Yii::t('import_export', 'Last 90 Days'),
            'last_6_months' => \Yii::t('import_export', 'Last 6 Months'),
            'current_year' => \Yii::t('import_export', 'Current Year'),
            'last_year' => \Yii::t('import_export', 'Last Year'),
            'all_time' => \Yii::t('import_export', 'All Time'),
        ];
    }

    public function getDateRangeFromPreset(string $preset): array
    {
        switch ($preset) {
            case 'yesterday':
                $yesterday = strtotime('yesterday');
                return [date('Y-m-d 00:00:00', $yesterday), date('Y-m-d 23:59:59', $yesterday)];
            case 'current_week':
                return [date('Y-m-d 00:00:00', strtotime('monday this week')), date('Y-m-d 23:59:59', strtotime('sunday this week'))];
            case 'last_7_days':
                return [date('Y-m-d 00:00:00', strtotime('-7 days')), date('Y-m-d 23:59:59')];
            case 'current_month':
                return [date('Y-m-01 00:00:00'), date('Y-m-t 23:59:59')];
            case 'last_month':
                $lastMonth = strtotime('first day of last month');
                return [date('Y-m-d 00:00:00', $lastMonth), date('Y-m-t 23:59:59', $lastMonth)];
            case 'last_30_days':
                return [date('Y-m-d 00:00:00', strtotime('-30 days')), date('Y-m-d 23:59:59')];
            case 'last_90_days':
                return [date('Y-m-d 00:00:00', strtotime('-90 days')), date('Y-m-d 23:59:59')];
            case 'last_6_months':
                return [date('Y-m-d 00:00:00', strtotime('-6 months')), date('Y-m-d 23:59:59')];
            case 'current_year':
                return [date('Y-01-01 00:00:00'), date('Y-12-31 23:59:59')];
            case 'last_year':
                $lastYear = date('Y') - 1;
                return ["{$lastYear}-01-01 00:00:00", "{$lastYear}-12-31 23:59:59"];
            case 'all_time':
                return ['1970-01-01 00:00:00', date('Y-m-d 23:59:59')];
            default:
                return [date('Y-m-d 00:00:00'), date('Y-m-d 23:59:59')];
        }
    }
    public function apply(Query $query, string $tableField, array $criteria)
    {
        $dates = $this->getDateRangeFromPreset($criteria['select']);
        $date_criteria = [
            "select" => "RANGE",
            "from" => $dates[0],
            "to" => $dates[1],
        ];

        (new RangeFilter())->apply($query, $tableField, $date_criteria);
    }
}
