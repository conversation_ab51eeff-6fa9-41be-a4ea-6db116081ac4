<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Expression;
use yii\db\Query;

class WithoutFilter implements FilterInterface
{
    public function apply(Query $query, string $tableField, array $criteria)
    {
        $query->andWhere([
            'OR',
            ['=', new Expression($tableField . '::text'), ''],
            ['is', $tableField, new Expression('NULL')],
        ]);
    }
}