<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Query;

class SelectFilter implements FilterInterface
{
    public function apply(Query $query, string $tableField, array $criteria)
    {
        if (isset($criteria['value'])) {
            $values = array_keys($criteria['value']);
            $query->andWhere(['in', $tableField, $values]);
        } else {
            $query->andWhere(['=', $tableField, $criteria['select']]);
        }
    }
}