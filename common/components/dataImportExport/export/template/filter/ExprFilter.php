<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Query;

class ExprFilter implements FilterInterface
{
    public function apply(Query $query, string $tableField, array $criteria)
    {
        $expr = $criteria['expr'];
        $value =  null;

        if (preg_match('/^(?:\s*(<>|<=|>=|<|>|=))?(.*)$/', $expr, $matches)) {
            $value = trim($matches[2]);
            $operator = trim($matches[1]);
        }

        if (empty($operator)) {
            $operator = '=';
        }

        $query->andWhere([$operator, $tableField, $value]);
    }
}