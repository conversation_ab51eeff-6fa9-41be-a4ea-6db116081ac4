<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Expression;
use yii\db\Query;

class WithFilter implements FilterInterface
{
    public function apply(Query $query, string $tableField, array $criteria)
    {
        $query->andWhere([
            'AND',
            ['!=', new Expression($tableField . '::text'), ''],
            ['is not', $tableField, new Expression('NULL')],
        ]);
    }
}