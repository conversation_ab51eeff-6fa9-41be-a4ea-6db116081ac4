<?php

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Query;

class RangeFilter implements FilterInterface
{
    public function apply(Query $query, string $tableField, array $criteria)
    {
        $from = $criteria['from'];
        $to = $criteria['to'];

        $query->andWhere([
            'AND',
            ['>=', $tableField, $from],
            ['<=', $tableField, $to],
        ]);
    }
}