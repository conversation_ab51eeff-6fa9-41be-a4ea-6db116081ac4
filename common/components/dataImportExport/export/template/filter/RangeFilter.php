<?php

declare(strict_types=1);

namespace common\components\dataImportExport\export\template\filter;

use yii\db\Query;

class RangeFilter implements FilterInterface
{
    #[\Override]
    public function apply(Query $query, string $tableField, array $criteria): void
    {
        $from = $criteria['from'];
        $to = $criteria['to'];

        // Check if date passed (for example 2025-01-02)
        if (str_contains((string) $from, '-')) {
            $from = date('Y-m-d 00:00:00', strtotime((string) $from));
            $to = date('Y-m-d 23:59:59', strtotime((string) $to));
        }

        $query->andWhere([
            'AND',
            ['>=', $tableField, $from],
            ['<=', $tableField, $to],
        ]);
    }
}
