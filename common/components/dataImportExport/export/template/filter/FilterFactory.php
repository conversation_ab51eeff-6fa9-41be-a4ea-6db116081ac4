<?php

namespace common\components\dataImportExport\export\template\filter;

class FilterFactory
{
    public const FILTER_TYPE_ALL = 'all';
    public const FILTER_TYPE_TEXT = 'text';
    public const FILTER_TYPE_WITH = 'with';
    public const FILTER_TYPE_WITHOUT = 'without';
    public const FILTER_TYPE_EXPR = 'expr';
    public const FILTER_TYPE_SELECT = 'select';
    public const FILTER_TYPE_RANGE = 'range';

    public function getFilter(string $filterType): FilterInterface
    {
        switch ($filterType) {
            case self::FILTER_TYPE_ALL:
                return new AllFilter();
            case self::FILTER_TYPE_TEXT:
                return new TextFilter();
            case self::FILTER_TYPE_WITH:
                return new WithFilter();
            case self::FILTER_TYPE_WITHOUT:
                return new WithoutFilter();
            case self::FILTER_TYPE_EXPR:
                return new ExprFilter();
            case self::FILTER_TYPE_SELECT:
                return new SelectFilter();
            case self::FILTER_TYPE_RANGE:
                return new RangeFilter();
        }

        return new SelectFilter();
    }
}