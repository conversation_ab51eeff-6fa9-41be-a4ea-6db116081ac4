<?php

namespace common\components\dataImportExport\export\exporter;

use common\components\CustomerComponent;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\template\filter\RangeFilter;
use common\components\dataImportExport\export\template\QueryModifier;
use common\components\salesCategoryMapper\strategy\CustomStrategy;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\services\order\TransferOrderService;
use common\components\widget\TransactionQueryExecutor;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedViewV1;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\IndirectCostType;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\SalesCategory;
use common\models\Seller;
use Exception;
use SellingPartnerApi\Model\OrdersV0\Order;
use Yii;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Query;

class OrdersV1 extends Orders implements ExporterInterface
{
    /**
     * @inheritDoc
     */
    public function getTemplateStructure(): array
    {
        static $result = null;

        if (!empty($result)) {
            return $result;
        }

        $columns = [
            //Order group
            'order_id' => [
                'title' => Yii::t('import_export', 'Order number'),
                'options' => [],
                'type' => 'string',
                'group' => 'order',
                'table_field' => 'order_id'
            ],
            'status' => [
                'title' => Yii::t('import_export', 'Status'),
                'options' => [
                    Order::ORDER_STATUS_PENDING => Yii::t('import_export_const', Order::ORDER_STATUS_PENDING),
                    Order::ORDER_STATUS_UNSHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_UNSHIPPED),
                    Order::ORDER_STATUS_PARTIALLY_SHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_PARTIALLY_SHIPPED),
                    Order::ORDER_STATUS_SHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_SHIPPED),
                    Order::ORDER_STATUS_CANCELED => Yii::t('import_export_const', Order::ORDER_STATUS_CANCELED),
                    Order::ORDER_STATUS_UNFULFILLABLE => Yii::t('import_export_const', Order::ORDER_STATUS_UNFULFILLABLE),
                    Order::ORDER_STATUS_INVOICE_UNCONFIRMED => Yii::t('import_export_const', Order::ORDER_STATUS_INVOICE_UNCONFIRMED),
                    Order::ORDER_STATUS_PENDING_AVAILABILITY => Yii::t('import_export_const', Order::ORDER_STATUS_PENDING_AVAILABILITY),
                ],
                'type' => 'checkboxList',
                'group' => 'order',
                'table_field' => 'order_status'
            ],
            'quantity' => [
                'title' => Yii::t('import_export', 'Ordered units'),
                'options' => [],
                'type' => 'int',
                'group' => 'order',
                'table_field' => 'quantity'
            ],
            //Product group
            'marketplace' => [
                'title' => Yii::t('import_export', 'Marketplace'),
                'options' => $this->getMarketplaceMap(),
                'type' => 'checkboxList',
                'group' => 'product',
                'table_field' => 'marketplace_id'
            ],
            'title' => [
                'title' => Yii::t('import_export', 'Title'),
                'options' => [],
                'type' => 'string',
                'group' => 'product',
                'table_field' => 'product_title'
            ],
            'asin' => [
                'title' => Yii::t('import_export', 'ASIN'),
                'options' => [],
                'type' => 'string',
                'group' => 'product',
                'table_field' => 'product_asin'
            ],
            'seller_sku' => [
                'title' => Yii::t('import_export', 'SKU'),
                'options' => [],
                'type' => 'string',
                'group' => 'product',
                'table_field' => 'seller_sku'
            ],
            'seller_id' => [
                'title' => Yii::t('import_export', 'Seller ID'),
                'options' => $this->getAvailableSellers(),
                'type' => 'checkboxList',
                'group' => 'product',
                'table_field' => 'seller_id'
            ],
            'fulfillment_method' => [
                'title' => Yii::t('import_export', 'Fulfillment method'),
                'options' => [
                    Product::STOCK_TYPE_FBA => Yii::t('import_export_const', Product::STOCK_TYPE_FBA),
                    Product::STOCK_TYPE_FBM => Yii::t('import_export_const', Product::STOCK_TYPE_FBM),
                ],
                'type' => 'select',
                'group' => 'product',
                'table_field' => 'product_stock_type'
            ],
            'offer_type' => [
                'title' => Yii::t('import_export', 'Offer type'),
                'options' => [
                    AmazonOrder::OFFER_TYPE_B2B => Yii::t('import_export_const', AmazonOrder::OFFER_TYPE_B2B),
                    AmazonOrder::OFFER_TYPE_B2C => Yii::t('import_export_const', AmazonOrder::OFFER_TYPE_B2C),
                ],
                'type' => 'select',
                'group' => 'product',
                'table_field' => 'offer_type'
            ],
            'condition' => [
                'title' => Yii::t('import_export', 'Condition'),
                'options' => Product::CONDITIONS_MAP,
                'type' => 'select',
                'group' => 'product',
                'table_field' => 'product_condition'
            ],
            //KPI group
            'revenue_amount' => [
                'title' => Yii::t('import_export', 'Revenue'),
                'options' => [],
                'type' => 'float',
                'group' => 'kpi',
                'table_field' => 'transactions.revenue_amount'
            ],
            'estimated_margin' => [
                'title' => Yii::t('import_export', 'Estimated margin'),
                'options' => [],
                'type' => 'float',
                'group' => 'kpi',
                'table_field' => 'transactions.estimated_profit_amount'
            ],
            'roi' => [
                'title' => Yii::t('import_export', 'ROI'),
                'options' => [],
                'type' => 'float',
                'group' => 'kpi',
                'table_field' => 'transactions.roi'
            ],
            'refunded_units' => [
                'title' => Yii::t('import_export', 'Refunded units'),
                'options' => [],
                'type' => 'float',
                'group' => 'kpi',
                'table_field' => 'quantity_refunded'
            ],
            'order_purchase_date' => [
                'title' => Yii::t('import_export', 'Order purchase date'),
                'options' => [],
                'type' => 'date',
                'group' => '',
                'table_field' => 'order_purchase_date',
                'is_required' => true
            ],
            'currency_id' => [
                'title' => Yii::t('import_export', 'Order currency code'),
                'table_field' => 'currency_id',
            ]
        ];

        $salesCategoryStrategy = $this->salesCategoryStrategyFactory->getStrategyByType(SalesCategoryStrategyFactory::STRATEGY_CUSTOM);
        $salesCategoriesMap = FinanceEventCategory::getSalesCategoriesMap($salesCategoryStrategy, 0);
        $customerCostCategories = $this->getCustomerProductCostCategoriesMap($salesCategoryStrategy);
        $salesCategoriesMap = array_merge($salesCategoriesMap, $customerCostCategories);

        $categoryOrder = [
            'Product sales' => 1,
            'FBA inbound fee' => 2,
            'FBA outbound fee' => 3,
            'Service fee' => 4,
            'Technology fee' => 5,
            'Miscellaneous fees' => 6,
            'Gift wrap charges' => 7,
            'Shipping charges' => 8,
            'Refunds and chargebacks' => 9,
            'Reimbursements' => 10,
            'Taxes & VAT' => 11,
            'Guarantee claim' => 12,
            'Promotion' => 13,
            'Cost of goods' => 14,
            'FBM Shipping costs' => 15,
            'Other costs' => 16,
            'Other fees' => 17
        ];

        $tempColumns = [];

        foreach ($salesCategoriesMap as $salesCategoryId => $ids) {
            if (strpos($salesCategoryId, 'ads_ppc') !== false ||
                strpos($salesCategoryId, 'postage_fee') !== false ||
                strpos($salesCategoryId, 'wholesale_liquidation') !== false) {
                continue;
            }
            $salesCategory = SalesCategory::find()
                ->where(['id' => $salesCategoryId])
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => [
                        SalesCategory::COMMON_CACHE_TAG,
                        ProductCostCategory::COMMON_CACHE_TAG,
                        IndirectCostType::COMMON_CACHE_TAG
                    ]])
                )
                ->one();

            if (!$salesCategory) {
                continue;
            }

            $categoryName = $salesCategory->name;
            $sortOrder = $categoryOrder[$categoryName] ?? 1000;

            $tempColumns[$salesCategoryId] = [
                'data' => [
                    'title' => Yii::t('import_export', $categoryName),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'transactions',
                    'table_field' => 'transactions.' . $salesCategoryId
                ],
                'sortOrder' => $sortOrder
            ];
        }

        uasort($tempColumns, function($a, $b) {
            return $a['sortOrder'] <=> $b['sortOrder'];
        });

        foreach ($tempColumns as $salesCategoryId => $columnData) {
            $columns[$salesCategoryId] = $columnData['data'];
        }

        $result = [
            'groups' => [
                'order' => [
                    'title' => Yii::t('import_export', 'Order')
                ],
                'product' => [
                    'title' => Yii::t('import_export', 'Product')
                ],
                'kpi' => [
                    'title' => Yii::t('import_export', 'KPI')
                ],
                'transactions' => [
                    'title' => Yii::t('import_export', 'Transactions')
                ]
            ],
            'columns' => $columns
        ];

        return $result;
    }

    protected function prepareAmazonOrderItemsQuery(ExportConfig $exportConfig): Query
    {
        $salesCategoryStrategy = $this->salesCategoryStrategyFactory->getStrategyByType(SalesCategoryStrategyFactory::STRATEGY_CUSTOM);
        $salesCategoriesMap = FinanceEventCategory::getSalesCategoriesMap($salesCategoryStrategy, 0);
        $customerCostCategories = $this->getCustomerProductCostCategoriesMap($salesCategoryStrategy);
        $moneyAccuracy = (new CustomerComponent())->getMoneyAccuracy();

        $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());

        $orderQuery = AmazonOrderExtendedViewV1::find();
        $orderInProgressQuery = clone $orderQuery;
        $orderInProgressQuery
            ->from(AmazonOrderInProgressExtendedViewV1::tableName())
            ->andWhere(['=', 'version', $lastVersion]);

        $amazonOrderItemsQuery = AmazonOrderExtendedViewV1::find()->from(['o' => $orderQuery->union($orderInProgressQuery, true)]);

        $transactionQuery = $this->prepareTransactionQuery($salesCategoryStrategy, $salesCategoriesMap, $customerCostCategories, $moneyAccuracy, $exportConfig);

        $amazonOrderItemsQuery->leftJoin(
            ['transactions' => $transactionQuery], '(o.order_id = transactions.amazon_order_id)'
        );

        return $amazonOrderItemsQuery;
    }

    /**
     * @inheritDoc
     */
    public function getExportFilePrefix(): string
    {
        return 'orders_v1';
    }

    private function formatSalesCategoryTitle($salesCategory) {
        $string = preg_replace('/_\d+$/', '', $salesCategory);
        $string = str_replace('_', ' ', $string);

        return ucfirst($string);
    }


    public function getCogTaxesIds(string $categoryName, array $customerCostCategories, string $categoryVAT): ?array
    {
        $cogTaxesIds = null;
        if ($categoryName === $categoryVAT && isset($customerCostCategories[$categoryVAT])) {
            $cogTaxesIds = $customerCostCategories[$categoryVAT];
        }
        return $cogTaxesIds;
    }
}
