<?php

namespace common\components\dataImportExport\export\exporter;

use yii\helpers\Inflector;

/**
 * Class ExporterFactory.
 */
class ExporterFactory
{
    /**
     * Creates and returns needed data exporter.
     *
     * @param  string            $handlerName
     * @return ExporterInterface
     */
    public function getExporter(string $handlerName): ExporterInterface
    {
        try {
            $className = __NAMESPACE__ . '\\' . ucfirst(Inflector::camelize($handlerName));
            return new $className();
        } catch (\Throwable $e) {
            throw new \Exception("Unable to find handler '$handlerName'");
        }
    }
}
