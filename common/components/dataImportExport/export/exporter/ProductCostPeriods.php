<?php

namespace common\components\dataImportExport\export\exporter;

use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\Constants;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\template\QueryModifier;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use common\models\customer\ProductCostPeriod;
use common\models\Seller;
use yii\db\Expression;
use yii\db\Query;

/**
 * Responsible for exporting data from amazon_product_dimension_and_weight table.
 */
class ProductCostPeriods implements ExporterInterface
{
    protected QueryModifier $queryModifier;

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->queryModifier = new QueryModifier($this->getTemplateStructure());
        $this->dbManager = \Yii::$app->dbManager;
    }

    /**
     * {@inheritdoc}
     */
    public function getItemsPerPart(): int
    {
        return 5000;
    }

    public function getExportFilePrefix(): string
    {
        return 'product_costs';
    }

    /**
     * {@inheritdoc}
     */
    public function getWhiteListedFields(ExportConfig $exportConfig): array
    {
        $templateStructure = $this->getTemplateStructure();
        return array_keys($templateStructure['columns']);
    }

    public function getTemplateStructure(): array
    {
        static $results = null;

        $resultsKey = implode('_', [
            $this->dbManager->getCustomerId(),
            \Yii::$app->language,
        ]);

        if (!empty($results[$resultsKey])) {
            return $results[$resultsKey];
        }

        $availableMarketplaces = AmazonMarketplace::find()
            ->select('id, title')
            ->where([
                'in',
                'id',
                Product::find()->select('marketplace_id')->distinct()->column()
            ])
            ->distinct()
            ->asArray()->all();
        $availableSellers = Seller::find()
            ->select('id')
            ->distinct()
            ->where(['customer_id' => \Yii::$app->dbManager->getCustomerId()])
            ->column();

        $sellerIds = [];
        $marketplaceIds = [];

        foreach ($availableMarketplaces as $marketplace) {
            $marketplaceIds[$marketplace['id']] = $marketplace['title'];
        }

        foreach ($availableSellers as $sellerId) {
            $sellerIds[$sellerId] = $sellerId;
        }

        $result = [
            'groups' => [
                'product' => [
                    'title' => \Yii::t('import_export', 'Product')
                ],
            ],
            'columns' => [
                'item_sku' => [
                    'title' => \Yii::t('import_export', 'SKU (Stock Keeping Unit)'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product.sku'
                ],
                'asin' => [
                    'title' => \Yii::t('import_export', 'ASIN'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product.asin'
                ],
                'title' => [
                    'title' => \Yii::t('import_export', 'Title'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product.title'
                ],
                'marketplace' => [
                    'title' => \Yii::t('import_export', 'Marketplace'),
                    'options' => $marketplaceIds,
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'product.marketplace_id'
                ],
                'seller_id' => [
                    'title' => \Yii::t('import_export', 'Seller ID'),
                    'options' => $sellerIds,
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'product.seller_id'
                ],
                'condition' => [
                    'title' => \Yii::t('import_export', 'Condition'),
                    'options' => Product::CONDITIONS_MAP,
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'product.condition'
                ],
                'fulfillment_method' => [
                    'title' => \Yii::t('import_export', 'Fulfillment method'),
                    'options' => [
                        Product::STOCK_TYPE_FBA => \Yii::t('import_export_const', Product::STOCK_TYPE_FBA),
                        Product::STOCK_TYPE_FBM => \Yii::t('import_export_const', Product::STOCK_TYPE_FBM),
                    ],
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'product.stock_type'
                ],
                'synchronize_with_repricer' => [
                    'title' => \Yii::t('import_export', 'Synchronize with Repricer'),
                    'options' => [
                        't' => \Yii::t('import_export_const', Constants::YES),
                        'f' => \Yii::t('import_export_const', Constants::NO),
                    ],
                    'options_db_map' => [
                        Constants::YES => 't',
                        Constants::NO => 'f',
                    ],
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'product.is_enabled_sync_with_repricer'
                ],
                'cost_of_goods' => [
                    'title' => \Yii::t('import_export', 'Cost of goods'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'product.buying_price'
                ],
                'cost_of_goods_start_date' => [
                    'title' => \Yii::t('import_export', 'Cost of goods, Start date'),
                    'group' => 'product',
                    'table_field' => 'pcp.cost_of_goods_start_date'
                ],
                'other_fees' => [
                    'title' => \Yii::t('import_export', 'Other fees'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'product.other_fees'
                ],
                'other_fees_start_date' => [
                    'title' => \Yii::t('import_export', 'Other fees, Start date'),
                    'group' => 'product',
                    'table_field' => 'pcp.other_fees_start_date'
                ],
                'shipping_costs' => [
                    'title' => \Yii::t('import_export', 'FBM shipping costs'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'product.shipping_cost'
                ],
                'shipping_costs_start_date' => [
                    'title' => \Yii::t('import_export', 'FBM shipping costs, Start date'),
                    'group' => 'product',
                    'table_field' => 'pcp.shipping_costs_start_date'
                ],
                'vat' => [
                    'title' => \Yii::t('import_export', 'VAT'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'product.vat'
                ],
                'vat_start_date' => [
                    'title' => \Yii::t('import_export', 'VAT, Start date'),
                    'group' => 'product',
                    'table_field' => 'pcp.expenses_taxes_start_date'
                ],
            ]
        ];
        $results[$resultsKey] = $result;

        return $result;
    }

    /**
     * {@inheritdoc}
     */
    public function getCountAllItems(ExportConfig $exportConfig): int
    {
        $query = Product::find();
        $this->queryModifier->modify($query, $exportConfig->fields, $exportConfig->criteria);

        return $query->count() ?? 0;
    }

    public function getLimitOffsetParts(ExportConfig $exportConfig): array
    {
        $parts = [];
        $countAllItems = $this->getCountAllItems($exportConfig);
        $limit = $this->getItemsPerPart();
        $query = Product::find();
        $this->queryModifier->modify($query, $exportConfig->fields, $exportConfig->criteria);
        $query->select('product.id')->orderBy('product.id ASC');
        $offset = 0;
        $partNo = 0;
        while (true) {
            $query->limit($limit)->offset($offset);
            $data = (new Query())
                ->from(["p" => $query])
                ->select('min(p.id) as min_id, max(p.id) as max_id')
                ->all(Product::getDb());

            $parts[$partNo] = [
                'offset' => $data[0]['min_id'] ?? null,
                'limit' => $data[0]['max_id'] ?? null,
            ];

            $offset += $limit;
            $partNo++;
            if ($offset >= $countAllItems || empty($data)) {
                break;
            }
        }

        return $parts;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(int $limit, int $offset, ExportConfig $exportConfig): array
    {
        $productCostPeriodQuery = ProductCostPeriod::find()->select([
                'product_cost_period.marketplace_id',
                'product_cost_period.seller_id',
                'product_cost_period.seller_sku',
                new Expression("
                    MAX(CASE 
                        WHEN sales_category_id = 'expenses_taxes'
                        THEN TO_CHAR(date_start, 'YYYY-MM-DD')
                        ELSE null
                    END) as expenses_taxes_start_date
                "),
                new Expression("
                    MAX(CASE
                        WHEN sales_category_id = 'cost_of_goods'
                        THEN TO_CHAR(date_start, 'YYYY-MM-DD')
                        ELSE null
                    END) as cost_of_goods_start_date
                "),
                new Expression("
                    MAX(CASE
                        WHEN sales_category_id = 'other_fees'
                        THEN TO_CHAR(date_start, 'YYYY-MM-DD')
                        ELSE null
                    END) as other_fees_start_date
                "),
                new Expression("
                    MAX(CASE
                        WHEN sales_category_id = 'shipping_costs'
                        THEN TO_CHAR(date_start, 'YYYY-MM-DD')
                        ELSE null
                    END) as shipping_costs_start_date
                ")
            ])
            ->innerJoin(
                ['prod' => Product::find()
                    ->select(['marketplace_id', 'seller_id', 'sku'])
                    ->andWhere([ 'AND',
                        ['>=', 'id', $offset],
                        ['<=', 'id', $limit],
                    ])
                ],
            'product_cost_period.marketplace_id = prod.marketplace_id AND
                 product_cost_period.seller_id = prod.seller_id AND
                 product_cost_period.seller_sku = prod.sku'
            )
            ->where([
                'AND',
                [
                    'OR',
                    ['<=', 'date_start', date('Y-m-d 00:00:00')],
                    ['is', 'date_start', new Expression('NULL')],
                ],
                [
                    'OR',
                    ['>', 'date_end', date('Y-m-d 00:00:00')],
                    ['is', 'date_end', new Expression('NULL')],
                ],
            ])
            ->groupBy('product_cost_period.marketplace_id, product_cost_period.seller_id, product_cost_period.seller_sku');

        $query = Product::find()
            ->select([
                'product.sku',
                'product.asin',
                'product.title',
                'product.marketplace_id',
                'amazon_marketplace.title as marketplace',
                'product.seller_id',
                'product.condition',
                'product.stock_type',
                'product.buying_price',
                'product.other_fees',
                'product.shipping_cost',
                'product.vat',
                'pcp.cost_of_goods_start_date',
                'pcp.other_fees_start_date',
                'pcp.shipping_costs_start_date',
                'pcp.expenses_taxes_start_date',
                'product.is_enabled_sync_with_repricer AS synchronize_with_repricer',
            ])
            ->leftJoin(['pcp' => $productCostPeriodQuery], '(product.marketplace_id = pcp.marketplace_id AND product.seller_id = pcp.seller_id AND product.sku = pcp.seller_sku)')
            ->leftJoin(['amazon_marketplace' => AmazonMarketplace::tableName()], 'product.marketplace_id = amazon_marketplace.id')
            ->asArray();

        $this->queryModifier->modify($query, $exportConfig->fields, $exportConfig->criteria);
        $query->andWhere([ 'AND',
            ['>=', 'product.id', $offset],
            ['<=', 'product.id', $limit],
        ]);
        $query->addSelect('amazon_marketplace.title as marketplace_title');
        $products = $query->all();

        $translationCache = [
            'condition' => [],
            'fulfillment_method' => [],
            'boolean' => [
                true => \Yii::t('import_export_const', Constants::YES),
                false => \Yii::t('import_export_const', Constants::NO)
            ],
        ];
        foreach ($products as $k => $product) {
            $products[$k]['marketplace'] = $product['marketplace_title'];
            if (isset($product['synchronize_with_repricer'])) {
                $products[$k]['synchronize_with_repricer'] = $translationCache['boolean'][$product['synchronize_with_repricer']];
            }

            if (!empty($products[$k]['fulfillment_method'])) {
                if (!isset($translationCache[$product['fulfillment_method']])) {
                    $translationCache['fulfillment_method'][$product['fulfillment_method']] = \Yii::t('import_export_const', $product['fulfillment_method']);
                }
                $products[$k]['fulfillment_method'] = $translationCache['fulfillment_method'][$product['fulfillment_method']];
            }

            if (isset($product['condition'])) {
                if (array_key_exists($product['condition'], Product::CONDITIONS_MAP) &&
                    !isset($translationCache['condition'][$product['condition']])) {
                    $translationCache['condition'][$product['condition']] =
                        \Yii::t('import_export_const', Product::CONDITIONS_MAP[$product['condition']]);
                }
                $products[$k]['condition'] = array_key_exists($product['condition'], Product::CONDITIONS_MAP) ? $translationCache['condition'][$product['condition']] : null;
            }
        }

        return $products;
    }
}
