<?php

namespace common\components\dataImportExport\export\exporter;

use common\components\CustomerComponent;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\template\QueryModifier;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\widget\TransactionQueryExecutor;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedViewV1;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\SalesCategory;
use common\models\Seller;
use Exception;
use SellingPartnerApi\Model\OrdersV0\Order;
use Yii;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Query;

class Orders implements ExporterInterface
{
    protected QueryModifier $queryModifier;

    protected SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct()
    {
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $this->queryModifier = new QueryModifier($this->getTemplateStructure());
    }

    /**
     * @inheritDoc
     */
    public function getWhiteListedFields(ExportConfig $exportConfig): array
    {
        $result = array_keys($this->getTemplateStructure()['columns']);

        if (!empty($exportConfig->fields)) {
            $result = array_values(array_intersect($result, $exportConfig->fields));
        }

        if (!in_array('currency_id', $result, true)) {
            $result[] = 'currency_id';
        }

        return $result;
    }

    protected function getMarketplaceMap(): array
    {
        return AmazonMarketplace::find()
            ->select('title')
            ->where([
                'in',
                'id',
                Product::find()->select('marketplace_id')->distinct()->column()
            ])
            ->indexBy('id')
            ->column();
    }

    protected function getAvailableSellers(): array
    {
        return Seller::find()
            ->select('id')
            ->distinct()
            ->where(['customer_id' => Yii::$app->dbManager->getCustomerId()])
            ->column();
    }

    /**
     * @inheritDoc
     */
    public function getTemplateStructure(): array
    {
        static $result = null;

        if (!empty($result)) {
            return $result;
        }

        $result = [
            'groups' => [
                'order' => [
                    'title' => Yii::t('import_export', 'Order')
                ],
                'product' => [
                    'title' => Yii::t('import_export', 'Product')
                ],
                'revenue' => [
                    'title' => Yii::t('import_export', 'Revenue')
                ],
                'expenses' => [
                    'title' => Yii::t('import_export', 'Expenses')
                ],
                'kpi' => [
                    'title' => Yii::t('import_export', 'KPI')
                ],
            ],
            'columns' => [
                //Order group
                'order_id' => [
                    'title' => Yii::t('import_export', 'Order number'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'order',
                    'table_field' => 'order_id'
                ],
                'status' => [
                    'title' => Yii::t('import_export', 'Status'),
                    'options' => [
                        Order::ORDER_STATUS_PENDING => Yii::t('import_export_const', Order::ORDER_STATUS_PENDING),
                        Order::ORDER_STATUS_UNSHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_UNSHIPPED),
                        Order::ORDER_STATUS_PARTIALLY_SHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_PARTIALLY_SHIPPED),
                        Order::ORDER_STATUS_SHIPPED => Yii::t('import_export_const', Order::ORDER_STATUS_SHIPPED),
                        Order::ORDER_STATUS_CANCELED => Yii::t('import_export_const', Order::ORDER_STATUS_CANCELED),
                        Order::ORDER_STATUS_UNFULFILLABLE => Yii::t('import_export_const', Order::ORDER_STATUS_UNFULFILLABLE),
                        Order::ORDER_STATUS_INVOICE_UNCONFIRMED => Yii::t('import_export_const', Order::ORDER_STATUS_INVOICE_UNCONFIRMED),
                        Order::ORDER_STATUS_PENDING_AVAILABILITY => Yii::t('import_export_const', Order::ORDER_STATUS_PENDING_AVAILABILITY),
                    ],
                    'type' => 'checkboxList',
                    'group' => 'order',
                    'table_field' => 'order_status'
                ],
                'quantity' => [
                    'title' => Yii::t('import_export', 'Ordered units'),
                    'options' => [],
                    'type' => 'int',
                    'group' => 'order',
                    'table_field' => 'quantity'
                ],
                //Product group
                'marketplace' => [
                    'title' => Yii::t('import_export', 'Marketplace'),
                    'options' => $this->getMarketplaceMap(),
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'marketplace_id'
                ],
                'title' => [
                    'title' => Yii::t('import_export', 'Title'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product_title'
                ],
                'asin' => [
                    'title' => Yii::t('import_export', 'ASIN'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product_asin'
                ],
                'seller_sku' => [
                    'title' => Yii::t('import_export', 'SKU'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'seller_sku'
                ],
                'seller_id' => [
                    'title' => Yii::t('import_export', 'Seller ID'),
                    'options' => $this->getAvailableSellers(),
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'seller_id'
                ],
                'fulfillment_method' => [
                    'title' => Yii::t('import_export', 'Fulfillment method'),
                    'options' => [
                        Product::STOCK_TYPE_FBA => Yii::t('import_export_const', Product::STOCK_TYPE_FBA),
                        Product::STOCK_TYPE_FBM => Yii::t('import_export_const', Product::STOCK_TYPE_FBM),
                    ],
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'product_stock_type'
                ],
                'offer_type' => [
                    'title' => Yii::t('import_export', 'Offer type'),
                    'options' => [
                        AmazonOrder::OFFER_TYPE_B2B => Yii::t('import_export_const', AmazonOrder::OFFER_TYPE_B2B),
                        AmazonOrder::OFFER_TYPE_B2C => Yii::t('import_export_const', AmazonOrder::OFFER_TYPE_B2C),
                    ],
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'offer_type'
                ],
                'condition' => [
                    'title' => Yii::t('import_export', 'Condition'),
                    'options' => Product::CONDITIONS_MAP,
                    'type' => 'select',
                    'group' => 'product',
                    'table_field' => 'product_condition'
                ],
                //Revenue group
                'revenue_amount' => [
                    'title' => Yii::t('import_export', 'Total revenue'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.revenue_amount'
                ],
                'product_sales' => [
                    'title' => Yii::t('import_export', 'Product sales'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.product_sales'
                ],
                'revenue_refunds' => [
                    'title' => Yii::t('import_export', 'Refunds and chargebacks'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.refunds_and_chargebacks'
                ],
                'reimbursements' => [
                    'title' => Yii::t('import_export', 'Reimbursements'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.reimbursements'
                ],
                'revenue_taxes' => [
                    'title' => Yii::t('import_export', 'Taxes'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.taxes_1'
                ],
                'additional_income' => [
                    'title' => Yii::t('import_export', 'Additional income'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'revenue',
                    'table_field' => 'transactions.additional_income'
                ],
                //Expenses group
                'expenses_amount' => [
                    'title' => Yii::t('import_export', 'Total expenses'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.expenses_amount'
                ],
                'amazon_fees' => [
                    'title' => Yii::t('import_export', 'Amazon fees'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.amazon_fees'
                ],
                'expenses_taxes' => [
                    'title' => Yii::t('import_export', 'Taxes'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.taxes_2'
                ],
                'expenses_refunds' => [
                    'title' => Yii::t('import_export', 'Refunds and chargebacks'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.refunds_and_chargebacks_1'
                ],
                'guarantee_claim' => [
                    'title' => Yii::t('import_export', 'Guarantee claim'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.guarantee_claim_1'
                ],
                'promotion' => [
                    'title' => Yii::t('import_export', 'Promotion'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.promotion'
                ],
                'cost_of_goods' => [
                    'title' => Yii::t('import_export', 'Cost of goods'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.cost_of_goods'
                ],
                'fbm_shipping_cost' => [
                    'title' => Yii::t('import_export', 'FBM shipping costs'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.shipping_costs'
                ],
                'other_costs' => [
                    'title' => Yii::t('import_export', 'Other costs'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.other_costs'
                ],
                'other_fees' => [
                    'title' => Yii::t('import_export', 'Other fees'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'expenses',
                    'table_field' => 'transactions.other_fees'
                ],
                //KPI group
                'estimated_margin' => [
                    'title' => Yii::t('import_export', 'Estimated margin'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'kpi',
                    'table_field' => 'transactions.estimated_profit_amount'
                ],
                'roi' => [
                    'title' => Yii::t('import_export', 'ROI'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'kpi',
                    'table_field' => 'transactions.roi'
                ],
                'refunded_units' => [
                    'title' => Yii::t('import_export', 'Refunded units'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'kpi',
                    'table_field' => 'quantity_refunded'
                ],
                'order_purchase_date' => [
                    'title' => Yii::t('import_export', 'Order purchase date'),
                    'options' => [],
                    'type' => 'date',
                    'group' => '',
                    'table_field' => 'order_purchase_date',
                    'is_required' => true
                ],
                'currency_id' => [
                    'title' => Yii::t('import_export', 'Order currency code'),
                    'table_field' => 'currency_id',
                ]
            ]
        ];

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getCountAllItems(ExportConfig $exportConfig): int
    {
        $amazonOrderItems = $this->prepareAmazonOrderItemsQuery($exportConfig);

        $this->queryModifier->modify($amazonOrderItems, [], $exportConfig->criteria);

        return (int)$amazonOrderItems->count() ?? 0;
    }

    /**
     * @inheritDoc
     */
    public function getItemsPerPart(): int
    {
        return 5000;
    }

    /**
     * @inheritDoc
     */
    public function getData(int $limit, int $offset, ExportConfig $exportConfig): array
    {
        $amazonOrderItemsQuery = $this->prepareAmazonOrderItemsQuery($exportConfig)
            ->limit($limit)
            ->offset($offset);

        $this->queryModifier->modify($amazonOrderItemsQuery, [], $exportConfig->criteria);

        try {
            $amazonOrderItemsData = $amazonOrderItemsQuery->createCommand()->queryAll();
        } catch (Exception $e) {
            Yii::error($e);
            return [];
        }

        $marketplaceMap = $this->getMarketplaceMap();

        foreach ($amazonOrderItemsData as $i => $orderItem) {
            $amazonOrderItemsData[$i] = $this->formatOrder($orderItem, $marketplaceMap);
        }

        return $amazonOrderItemsData;
    }

    protected function getCogCategorySelect($categoryName, $customerCostCategories, $moneyAccuracy): string
    {
        if (!isset($customerCostCategories[$categoryName])) {
            return "0 as $categoryName";
        }

        $cogIds = implode(',', $customerCostCategories[$categoryName]);
        $select = "ROUND(SUM(CASE WHEN cog_category_id IN ($cogIds)
                THEN 
                (amount_eur / $moneyAccuracy) * dictGetOrNull(
                    default.currency_rate_dict, 
                    'value', 
                    tuple(toDate(posted_date), currency_code)
                )
                ELSE 0
                END
                ), 2) AS $categoryName";

        return $select;
    }

    protected function getCustomerProductCostCategoriesMap(SalesCategoryStrategyInterface $salesCategoryStrategy): array
    {
        $customCategories = ProductCostCategory::find()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        ProductCostCategory::COMMON_CACHE_TAG,
                    ]
                ])
            )
            ->asArray()
            ->all();

        $categoriesMap = [];
        foreach ($customCategories as $k => $customCategory) {
            $tagToSearchFor = null;

            switch ($customCategory['sales_category_id']) {
                case SalesCategory::CATEGORY_EXPENSES_COG:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_COST_OF_GOODS;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_TAXES:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_VAT;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_FBM_SHIPPING_COSTS;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_OTHER_FEES:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_OTHER_FEES;
            }

            /** @var SalesCategory $parentSalesCategory */
            $parentSalesCategory = SalesCategory::findByTag(
                    $tagToSearchFor,
                    $salesCategoryStrategy->getType()
                )[0] ?? null;

            if (null === $parentSalesCategory) {
                continue;
            }
            $categoriesMap[$parentSalesCategory->id][] = $customCategory['id'];
        }
        return $categoriesMap;
    }

    protected function prepareAmazonOrderItemsQuery(ExportConfig $exportConfig): Query
    {
        $salesCategoryStrategy = $this->salesCategoryStrategyFactory->getStrategyByType(SalesCategoryStrategyFactory::DEFAULT_STRATEGY);
        $salesCategoriesMap = FinanceEventCategory::getSalesCategoriesMap($salesCategoryStrategy, 1);
        $customerCostCategories = $this->getCustomerProductCostCategoriesMap($salesCategoryStrategy);
        $moneyAccuracy = (new CustomerComponent())->getMoneyAccuracy();

        $lastVersion = (new Query())
            ->select('max(version)')
            ->from(AmazonOrderInProgressExtendedViewV1::tableName())
            ->scalar(Yii::$app->dbManager->getClickhouseCustomerDb()) ?? 0;

        $orderQuery = AmazonOrderExtendedViewV1::find();
        $orderInProgressQuery = clone $orderQuery;
        $orderInProgressQuery
            ->from(AmazonOrderInProgressExtendedViewV1::tableName())
            ->andWhere(['=', 'version', $lastVersion]);

        $amazonOrderItemsQuery = AmazonOrderExtendedViewV1::find()->from(['o' => $orderQuery->union($orderInProgressQuery, true)]);

        $transactionQuery = $this->prepareTransactionQuery($salesCategoryStrategy, $salesCategoriesMap, $customerCostCategories, $moneyAccuracy, $exportConfig);

        $amazonOrderItemsQuery->leftJoin(
            ['transactions' => $transactionQuery], '(o.order_id = transactions.amazon_order_id)'
        );

        return $amazonOrderItemsQuery;
    }

    protected function prepareTransactionQuery(SalesCategoryStrategyInterface $salesCategoryStrategy, array $salesCategoriesMap, array $customerCostCategories, int $moneyAccuracy, ExportConfig $exportConfig): ActiveQuery
    {
        $transactionQuery = $salesCategoryStrategy->getTransactionExtendedViewQuery()
            ->select([
                "*",
            ]);

        $query1 = $this->prepareTransactionQueryPart($salesCategoryStrategy->getTransactionExtendedViewQuery(), 0);
        $query2 = $this->prepareTransactionQueryPart(OrderBasedTransaction::find(), 1);
        $transactionQuery->from(['t' => $query1->union($query2, true)]);

        $transactionQuery->select([
            "amazon_order_id",
            "seller_sku",
        ])->groupBy(["amazon_order_id", "seller_sku"]);

        $categoryVAT = SalesCategory::findByTag(
                SalesCategory::TAG_MANUAL_VAT,
                $salesCategoryStrategy->getType()
            )[0]->id ?? null;

        $categoryProductSales = SalesCategory::findByTag(
                SalesCategory::TAG_PRODUCT_SALES,
                $salesCategoryStrategy->getType()
            )[0]->id ?? null;

        $categoryShippingCost = SalesCategory::findByTag(
                SalesCategory::TAG_MANUAL_FBM_SHIPPING_COSTS,
                $salesCategoryStrategy->getType()
            )[0]->id ?? null;

        $categoryExpensesCog = SalesCategory::findByTag(
                SalesCategory::TAG_MANUAL_COST_OF_GOODS,
                $salesCategoryStrategy->getType()
            )[0]->id ?? null;

        $categoryOtherFees = SalesCategory::findByTag(
                SalesCategory::TAG_MANUAL_OTHER_FEES,
                $salesCategoryStrategy->getType()
            )[0]->id ?? null;

        foreach ($salesCategoriesMap as $name => $salesCategoryIds) {
            $customerCostCondition = "";

            if ($name === $categoryProductSales) {
                $customerCostCondition = "AND cog_category_id = 0";
            }

            $cogTaxesIds = $this->getCogTaxesIds($name, $customerCostCategories, $categoryVAT);
            if (!empty($cogTaxesIds)) {
                $cogTaxesIdsString = implode(',', $cogTaxesIds);
                $customerCostCondition = "OR cog_category_id IN ($cogTaxesIdsString)";
            }

            $selectQuery = "0 as $name";
            if (count($salesCategoryIds) > 0) {
                $ids = implode(',', $salesCategoryIds);
                $selectQuery = "
                ROUND(SUM(CASE WHEN category_id IN ($ids) $customerCostCondition
                    THEN 
                    (amount_eur / $moneyAccuracy) * 
                    dictGetOrNull(
                        default.currency_rate_dict, 
                        'value', 
                        tuple(toDate(posted_date), currency_code)
                    )
                    ELSE 0
                    END
                    ), 2) AS $name";
            }
            $transactionQuery->addSelect([
                $selectQuery
            ]);
        }

        $transactionQuery->addSelect([
            $this->getCogCategorySelect($categoryExpensesCog, $customerCostCategories, $moneyAccuracy),
            $this->getCogCategorySelect($categoryShippingCost, $customerCostCategories, $moneyAccuracy),
            $this->getCogCategorySelect($categoryOtherFees, $customerCostCategories, $moneyAccuracy),
            "round(SUM(
                    IF(
                        amount_eur >= 0,
                        (amount_eur / $moneyAccuracy) * 
                        dictGetOrNull(
                            default.currency_rate_dict, 
                            'value', 
                            tuple(toDate(posted_date), currency_code)
                        ),
                        0
                    )
                ), 2) as revenue_amount",
            "round(SUM(
                    IF(
                        amount_eur < 0,
                        abs((amount_eur / $moneyAccuracy)) *
                        dictGetOrNull(
                            default.currency_rate_dict, 
                            'value', 
                            tuple(toDate(posted_date), currency_code)
                        ),
                        0
                    )
                ), 2) as expenses_amount",
            "round(SUM((amount_eur / $moneyAccuracy) * 
                    dictGetOrNull(
                        default.currency_rate_dict, 
                        'value', 
                        tuple(toDate(posted_date), currency_code)
                    )), 2) as estimated_profit_amount",
            "IF (
                    expenses_amount = 0,
                    round((estimated_profit_amount / 0.1) * 100, 2),
                    round((estimated_profit_amount / abs(expenses_amount)) * 100, 2)
                ) as roi",
            "IF (
                    revenue_amount = 0,
                    null,
                    greatest(-100, least(100, round((estimated_profit_amount / abs(revenue_amount)) * 100, 2)))
                ) as margin",
        ]);

        //leftJoin query optimization
        if (isset($exportConfig->criteria['order_purchase_date'])) {
            $transactionQuery->andWhere([
                'AND',
                ['>=', 'posted_date', $exportConfig->criteria['order_purchase_date']['from']],
                ['<=', 'posted_date', $exportConfig->criteria['order_purchase_date']['to']],
            ]);
        }

        return $transactionQuery;
    }

    protected function prepareTransactionQueryPart(ActiveQuery $query, int $isCamelCase): ActiveQuery
    {
        $columns = ['amazon_order_id', 'seller_sku', 'category_id', 'cog_category_id', 'amount_eur', 'amount', 'posted_date', 'currency_code'];

        foreach ($columns as $column) {
            $query->addSelect([
                TransactionQueryExecutor::COLUMN_NAME_MAPPING[$column][$isCamelCase] . ' as ' . TransactionQueryExecutor::COLUMN_NAME_MAPPING[$column][0]
            ]);
        }

        return $query;
    }

    protected function formatOrder(array $orderItem, array $marketplaceMap): array
    {
        if (isset($orderItem['marketplace'])) {
            $orderItem['marketplace'] = $marketplaceMap[$orderItem['marketplace']] ?? 'unknown';
        }
        if (isset($orderItem['condition'])) {
            $orderItem['condition'] = Product::CONDITIONS_MAP[(int)$orderItem['condition']] ?? 'unknown';
        }

        return $orderItem;
    }

    /**
     * @inheritDoc
     */
    public function getExportFilePrefix(): string
    {
        return 'orders';
    }

    public function getCogTaxesIds(string $categoryName, array $customerCostCategories, string $categoryVAT): ?array
    {
        $cogTaxesIds = null;
        if ($categoryName === SalesCategory::CATEGORY_REVENUE_TAXES && isset($customerCostCategories[SalesCategory::CATEGORY_EXPENSES_TAXES])) {
            $cogTaxesIds = $customerCostCategories[SalesCategory::CATEGORY_EXPENSES_TAXES];
        }
        return $cogTaxesIds;
    }
}