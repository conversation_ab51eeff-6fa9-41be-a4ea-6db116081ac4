<?php

namespace common\components\dataImportExport\export\exporter;

use bashkarev\clickhouse\Query;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\template\QueryModifier;
use common\models\customer\clickhouse\ProductAggregatedSalesInfo;
use yii\db\Expression;

class AggregatedSalesInfo implements ExporterInterface
{
    protected QueryModifier $queryModifier;

    public function __construct()
    {
        $this->queryModifier = new QueryModifier($this->getTemplateStructure());
    }

    public function getWhiteListedFields(ExportConfig $exportConfig): array
    {
        $templateStructure = $this->getTemplateStructure();

        return array_keys($templateStructure['columns']);
    }

    public function getTemplateStructure(): array
    {
        $result = [
            'groups' => [
                'product' => [
                    'title' => \Yii::t('import_export', 'Product')
                ],
            ],
            'columns' => [
                'number' => [
                    'title' => \Yii::t('import_export', 'Number'),
                    'options' => [],
                    'group' => 'product',
                    'table_field' => 'rowNumberInAllBlocks() + 1',
                ],
                'title' => [
                    'title' => \Yii::t('import_export', 'Product Title'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product_title'
                ],
                'asin' => [
                    'title' => \Yii::t('import_export', 'ASIN'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'product_asin'
                ],
                'item_sku' => [
                    'title' => \Yii::t('import_export', 'SKU'),
                    'options' => [],
                    'type' => 'string',
                    'group' => 'product',
                    'table_field' => 'seller_sku'
                ],
                'marketplace' => [
                    'title' => \Yii::t('import_export', 'Marketplace'),
                    'options' => [],
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'marketplace_id'
                ],
                'brand' => [
                    'title' => \Yii::t('import_export', 'Brand'),
                    'options' => [],
                    'type' => 'checkboxList',
                    'group' => 'product',
                    'table_field' => 'product_brand'
                ],
                'revenue' => [
                    'title' => \Yii::t('import_export', 'Revenue'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'revenue_amount'
                ],
                'expenses' => [
                    'title' => \Yii::t('import_export', 'Expenses'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'expenses_amount'
                ],
                'estimated' => [
                    'title' => \Yii::t('import_export', 'Estimated'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'estimated_profit_amount'
                ],
                'order_items' => [
                    'title' => \Yii::t('import_export', 'Ordered items'),
                    'options' => [],
                    'type' => 'int',
                    'group' => 'product',
                    'table_field' => 'orders'
                ],
                'units' => [
                    'title' => \Yii::t('import_export', 'Units'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'units'
                ],
                'promotion' => [
                    'title' => \Yii::t('import_export', 'Promotion'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'promo'
                ],
                'refunds' => [
                    'title' => \Yii::t('import_export', 'Refunds'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'refunds'
                ],
                'bsr' => [
                    'title' => \Yii::t('import_export', 'BSR'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'bsr_avg_curr'
                ],
                'margin' => [
                    'title' => \Yii::t('import_export', 'Margin'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'margin'
                ],
                'roi' => [
                    'title' => \Yii::t('import_export', 'ROI'),
                    'options' => [],
                    'type' => 'float',
                    'group' => 'product',
                    'table_field' => 'roi'
                ]
            ]
        ];

        return $result;
    }

    public function getCountAllItems(ExportConfig $exportConfig): int
    {
        $query = ProductAggregatedSalesInfo::find();
        if ($exportConfig->extra_criteria) {
            $query = (new Query())->from(['o' => (new ProductAggregatedSalesInfo())->search($exportConfig->extra_criteria, ProductAggregatedSalesInfo::getDb())]);
        }

        $this->queryModifier->modify($query, $exportConfig->fields, $exportConfig->criteria);
        $count = $query->count('*', ProductAggregatedSalesInfo::getDb()) ?? 0;

        return (int) $count;
    }

    public function getItemsPerPart(): int
    {
        return 5000;
    }

    public function getData(int $limit, int $offset, ExportConfig $exportConfig): array
    {
        $query = ProductAggregatedSalesInfo::find();

        if ($exportConfig->extra_criteria) {
            $query = (new Query())->from(['o' => (new ProductAggregatedSalesInfo())->search($exportConfig->extra_criteria, ProductAggregatedSalesInfo::getDb())]);
        }

        $this->queryModifier->modify($query, $exportConfig->fields, $exportConfig->criteria);

        $query->limit($limit)
            ->offset($offset);

        try {
            $data = $query->all(ProductAggregatedSalesInfo::getDb());
        } catch (\Exception $e) {
            \Yii::error($e);
            return [];
        }

        return $data;
    }

    public function getExportFilePrefix(): string
    {
        return 'aggregated-sales-info';
    }
}
