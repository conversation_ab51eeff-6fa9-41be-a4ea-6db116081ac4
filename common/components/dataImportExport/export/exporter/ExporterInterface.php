<?php

namespace common\components\dataImportExport\export\exporter;

use common\components\dataImportExport\export\ExportConfig;

/**
 * Interface ExporterInterface.
 */
interface ExporterInterface
{
    /**
     * Returns list of fields can be exported.
     *
     * @param  ExportConfig $exportConfig
     * @return array
     */
    public function getWhiteListedFields(ExportConfig $exportConfig): array;

    /**
     * Returns export template structure
     *
     * @return array
     */
    public function getTemplateStructure(): array;

    /**
     * Returns count of all items will be exported.
     *
     * @param  ExportConfig $exportConfig
     * @return int
     */
    public function getCountAllItems(ExportConfig $exportConfig): int;

    /**
     * Returns amount of items can be exported per part.
     *
     * @return int
     */
    public function getItemsPerPart(): int;

    /**
     * Returns part of data for export.
     *
     * @param  int          $limit
     * @param  int          $offset
     * @param  ExportConfig $exportConfig
     * @return array
     */
    public function getData(int $limit, int $offset, ExportConfig $exportConfig): array;

    /**
     * Returns file name prefix of result export file.
     *
     * @return string
     */
    public function getExportFilePrefix(): string;
}
