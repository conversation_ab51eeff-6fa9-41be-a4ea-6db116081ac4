<?php

namespace common\components\dataImportExport\export\consumer;

use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\export\exporter\ExporterFactory;
use common\components\dataImportExport\export\exporter\ExporterInterface;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\fileDataReader\DataReaderFactory;
use common\components\fileDataWriter\DataWriterFactory;
use common\components\fileDataWriter\DataWriterInterface;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\models\customer\DataExport;
use common\models\customer\DataExportPart;
use common\models\customer\DataExportTemplate;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Filesystem\Filesystem;
use Yii;
use yii\helpers\FileHelper;

/**
 * Downloads prepared for export parts, merges them into single file,
 * uploads result file to remote storage.
 */
class MergePartsConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    private Filesystem $filesystem;

    private DataReaderFactory $dataReaderFactory;

    private DataWriterFactory $dataWriterFactory;

    private ExporterFactory $exporterFactory;

    private array $tmpFiles = [];

    private DbManager $dbManager;

    private int $concurrency = 10;
    
    private array $downloadedQueue = [];
    
    private int $nextPartNoToProcess = 0;

    public function __construct()
    {
        $this->filesystem = new Filesystem();
        $this->dataReaderFactory = new DataReaderFactory();
        $this->dataWriterFactory = new DataWriterFactory();
        $this->exporterFactory = new ExporterFactory();
        $this->dbManager = Yii::$app->dbManager;
    }

    public function __execute(AMQPMessage $msg): int
    {
        DataExport::getDb()->enableSlaves = false;
        $this->info(str_repeat('-', 60));
        $this->info('Merging data export parts has been started');

        $data = json_decode($msg->body, true);
        $customerId = $data['customer_id'] ?? null;
        $dataExportId = $data['data_export_id'] ?? null;

        $this->info($data);

        if (null === $customerId || null === $dataExportId) {
            return ConsumerInterface::MSG_ACK;
        }

        $this->dbManager->setCustomerId($data['customer_id']);
        /** @var DataExportPart $dataImportPart */
        $dataExport = DataExport::findOne($dataExportId);

        if (null === $dataExport) {
            return ConsumerInterface::MSG_ACK;
        }

        $languagePrev = Yii::$app->language;
        Yii::$app->translation->setIsAllowHandleMissingInConsole(true);

        try {
            $dataExport->count_errors = 0;
            $dataExport->exception = null;
            $dataExport->count_exported_items = 0;
            $dataExport->save(false);
            Yii::$app->language = $dataExport->getLanguageCode();

            /** @var DataExportPart[] $dataExportParts */
            $dataExportParts = $dataExport->getDataExportParts()->orderBy(['part_no' => SORT_ASC])->all();
            $outputTmpFilePath = $this->getTmpFilePath();
            $dataWriter = $this->dataWriterFactory->getDataWriter($dataExport->output_format);
            $exporter = $this->exporterFactory->getExporter($dataExport->handler_name);

            $this->info("Downloading data export parts and merging them into temp file $outputTmpFilePath");

            $this->downloadedQueue = [];
            $this->nextPartNoToProcess = 0;
            $isFirstPart = true;
            
            $this->streamProcessParts($dataExportParts, $outputTmpFilePath, $dataWriter, $dataExport, $isFirstPart);

            $this->info("All data export parts has been downloaded and merged into $outputTmpFilePath");
            $this->info("Uploading result file to remote storage");
            $dataExport->uploadFileToStorage(
                $outputTmpFilePath,
                $dataWriter->getExtension(),
                $dataWriter->getMimeType(),
                $this->generateExportFileName($exporter)
            );

            $dataExport->setFinished();
            $dataExport->save(false);
            $this->info("Uploading has been finished");
            $this->info([
                'file_url' => $dataExport->file_url,
            ]);
            /** @var DataExportTemplate $template */
            $template = $dataExport->getTemplate()->one();
            if ($template->remove_after_export === true) {
                $template->delete();
                $this->info("Template successfully removed");
            }
        } catch (\Throwable $e) {
            $this->error('Exception: ' . $e->getMessage());
            $dataExport->setException($e->getMessage());
            $dataExport->setFinished();
            $dataExport->save(false);
        } finally {
            Yii::$app->language = $languagePrev;
            $this->removeTmpFiles();
        }

        $this->info("Data export has been finished!");

        return ConsumerInterface::MSG_ACK;
    }
    
    /**
     * @param DataExportPart[] $dataExportParts
     * @param string $outputTmpFilePath 
     * @param DataWriterInterface $dataWriter 
     * @param DataExport $dataExport 
     * @param bool &$isFirstPart 
     * @return void 
     */
    private function streamProcessParts(
        array $dataExportParts, 
        string $outputTmpFilePath, 
        DataWriterInterface $dataWriter, 
        DataExport $dataExport,
        bool &$isFirstPart
    ): void {
        $client = new Client();
        $requests = function() use ($dataExportParts) {
            foreach ($dataExportParts as $part) {
                if (empty($part->file_url)) {
                    continue;
                }
                
                $this->info('Queuing download for part #' . $part->part_no . ' from ' . $part->file_url);
                yield $part->part_no => new Request('GET', $part->file_url);
            }
        };
        
        $pool = new Pool($client, $requests(), [
            'concurrency' => $this->concurrency,
            'fulfilled' => function(Response $response, $index) use ($dataExportParts, $outputTmpFilePath, $dataWriter, $dataExport, &$isFirstPart) {
                $part = $this->findPartByPartNo($dataExportParts, $index);
                if ($part) {
                    $this->handleDownloadedPart($part, $response, $outputTmpFilePath, $dataWriter, $dataExport, $isFirstPart);
                }
            },
            'rejected' => function(RequestException $reason, $index) use ($dataExportParts, $dataExport, $outputTmpFilePath, $dataWriter, &$isFirstPart) {
                $part = $this->findPartByPartNo($dataExportParts, $index);
                if ($part) {
                    $this->error(new Exception('Failed to download part #' . $part->part_no . ': ' . $reason->getMessage()));
                    $dataExport->count_errors += $part->count_all_items;
                    $dataExport->save(false);
                    
                    $this->downloadedQueue[$part->part_no] = [
                        'part' => $part,
                        'error' => true
                    ];
                    
                    $this->processQueue($outputTmpFilePath, $dataWriter, $dataExport, $isFirstPart);
                }
            },
        ]);
        
        $this->info('Starting streaming download and processing of ' . count($dataExportParts) . ' parts');
        $pool->promise()->wait();
        $this->info('All downloads and processing completed');
    }
    
    /**
     * @param DataExportPart[] $parts
     * @param int $partNo
     * @return DataExportPart|null
     */
    private function findPartByPartNo(array $parts, int $partNo): ?DataExportPart
    {
        foreach ($parts as $part) {
            if ($part->part_no === $partNo) {
                return $part;
            }
        }
        return null;
    }
    
    /**
     * @param DataExportPart $part
     * @param Response $response
     * @param string $outputTmpFilePath
     * @param DataWriterInterface $dataWriter
     * @param DataExport $dataExport
     * @param bool &$isFirstPart
     */
    private function handleDownloadedPart(
        DataExportPart $part, 
        Response $response, 
        string $outputTmpFilePath, 
        DataWriterInterface $dataWriter, 
        DataExport $dataExport,
        bool &$isFirstPart
    ): void {
        $this->info('Downloaded part #' . $part->part_no);
        
        $partTmpFilePath = $this->getTmpFilePath();
        file_put_contents($partTmpFilePath, $response->getBody());

        $this->downloadedQueue[$part->part_no] = [
            'part' => $part,
            'tmp_path' => $partTmpFilePath
        ];
        
        $this->processQueue($outputTmpFilePath, $dataWriter, $dataExport, $isFirstPart);
    }

    /**
     * @param string $outputTmpFilePath
     * @param DataWriterInterface $dataWriter
     * @param DataExport $dataExport
     * @param bool &$isFirstPart
     */
    private function processQueue(
        string $outputTmpFilePath, 
        DataWriterInterface $dataWriter, 
        DataExport $dataExport,
        bool &$isFirstPart
    ): void {
        while (isset($this->downloadedQueue[$this->nextPartNoToProcess])) {
            $partInfo = $this->downloadedQueue[$this->nextPartNoToProcess];
            $part = $partInfo['part'];
            
            $this->info('Processing part #' . $part->part_no . ' (next in queue)');
            
            if (isset($partInfo['error']) && $partInfo['error'] === true) {
                $this->info('Skipping part #' . $part->part_no . ' due to previous download error');
                unset($this->downloadedQueue[$this->nextPartNoToProcess]);
                $this->nextPartNoToProcess++;
                continue;
            }
            
            $partTmpFilePath = $partInfo['tmp_path'];
            
            try {
                $dataReader = $this->dataReaderFactory->getDataReaderByPath($partTmpFilePath);
                $partData = $dataReader->getData($partTmpFilePath, PHP_INT_MAX, 0);
                
                $dataWriter->saveData($partData, $outputTmpFilePath);
                $dataExport->count_exported_items += count($partData);
                $dataExport->save(false);

                if ($isFirstPart && !empty($partData)) {
                    $isFirstPart = false;
                    $this->saveExtraData($outputTmpFilePath, $dataExport, $dataWriter, array_keys($partData[0]));
                }

                unset($this->downloadedQueue[$this->nextPartNoToProcess]);
                
                $this->nextPartNoToProcess++;
                
            } catch (\Throwable $e) {
                $this->error($e);
                $dataExport->count_errors += $part->count_all_items;
                $dataExport->save(false);
                
                unset($this->downloadedQueue[$this->nextPartNoToProcess]);
                $this->nextPartNoToProcess++;
            }
        }
    }

    /**
     * Prepends extra information (fields definitions, template versions, etc.) into start of the export file.
     * Should be used only on a small files or in the beginning of export process.
     *
     * @param string              $outputTmpFilePath
     * @param DataExport          $dataExport
     * @param DataWriterInterface $dataWriter
     * @param array               $columnsToDescribe
     */
    private function saveExtraData(
        string $outputTmpFilePath,
        DataExport $dataExport,
        DataWriterInterface $dataWriter,
        array $columnsToDescribe
    ): void {
        $extraDataFile = $this->getTmpFilePath();
        file_put_contents($extraDataFile, '');

        $extraData = [['TemplateType=XXX', 'Version=2017.0001']];
        $importerFactory = new ImporterFactory();
        $importer = $importerFactory->getImporter($dataExport->handler_name);
        $exampleData = $importer->getExampleData();
        $titles = [];

        foreach ($columnsToDescribe as $columnName) {
            $titles[] = $exampleData['columns'][$columnName]['title'] ?? '';
        }

        if (count(array_filter($titles)) > 0) {
            $extraData[] = $titles;
        }

        $dataWriter->saveData($extraData, $extraDataFile);
        $outputFileContent = file_get_contents($outputTmpFilePath);

        file_put_contents($outputTmpFilePath, file_get_contents($extraDataFile));
        file_put_contents($outputTmpFilePath, $outputFileContent, FILE_APPEND);
    }

    /**
     * Removes all created tmp files.
     */
    private function removeTmpFiles()
    {
        foreach ($this->tmpFiles as $tmpFilePath) {
            if (file_exists($tmpFilePath)) {
                $this->filesystem->remove($tmpFilePath);
            }
        }
    }

    private function generateExportFileName(ExporterInterface $exporter): string
    {
        return implode('_', [
            $exporter->getExportFilePrefix(),
            'export',
            date('Y-m-d_H-i-s'),
        ]);
    }

    /**
     * Creates temporary file which can be removed after using.
     */
    private function getTmpFilePath()
    {
        $tmpDirectory = sys_get_temp_dir() . '/import-export/export_merge_parts';
        FileHelper::createDirectory($tmpDirectory);
        $tmpFilePath = $tmpDirectory . '/' . uniqid('', true);
        $this->tmpFiles[] = $tmpFilePath;

        return $tmpFilePath;
    }
}
