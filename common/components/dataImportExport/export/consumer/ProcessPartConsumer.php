<?php

namespace common\components\dataImportExport\export\consumer;

use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\exporter\ExporterFactory;
use common\components\dataImportExport\rotator\RotatorFactory;
use common\components\fileDataWriter\DataWriterFactory;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use common\models\customer\DataExport;
use common\models\customer\DataExportPart;
use common\models\customer\DataExportTemplate;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use mikemadisonweb\rabbitmq\components\Producer;
use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Filesystem\Filesystem;
use Yii;
use yii\helpers\FileHelper;

/**
 * Retrieves data from exporter and saves it into file, uploads this file to remote storage.
 */
class ProcessPartConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var ExporterFactory
     */
    private $exporterFacotry;

    /**
     * @var DataWriterFactory
     */
    private $dataWriterFactory;

    /**
     * @var RotatorFactory
     */
    private $rotatorFactory;

    /**
     * @var array
     */
    private $tmpFiles = [];

    /**
     * ProcessPartConsumer constructor.
     */
    public function __construct()
    {
        $this->filesystem = new Filesystem();
        $this->dataWriterFactory = new DataWriterFactory();
        $this->exporterFacotry = new ExporterFactory();
        $this->rotatorFactory = new RotatorFactory();
    }

    /**
     * @param  AMQPMessage $msg
     * @throws \Exception
     * @return int|mixed
     */
    public function __execute(AMQPMessage $msg)
    {
        DataExport::getDb()->enableSlaves = false;
        $this->info(str_repeat('-', 60));
        $this->info('Received new file part for processing');

        $data = json_decode($msg->body, true);
        $customerId = $data['customer_id'] ?? null;
        $dataExportPartId = $data['data_export_part_id'] ?? null;

        $this->info($data);
        Yii::$app->translation->setIsAllowHandleMissingInConsole(true);

        if (null === $customerId || null === $dataExportPartId) {
            $this->error('Some required fields was not provided');
            return ConsumerInterface::MSG_ACK;
        }

        Yii::$app->dbManager->setCustomerId($data['customer_id']);
        /** @var DataExportPart $dataImportPart */
        $dataExportPart = DataExportPart::findOne($dataExportPartId);

        if (null === $dataExportPart) {
            return ConsumerInterface::MSG_ACK;
        }

        /** @var DataExport $dataExport */
        $dataExport = $dataExportPart->getDataExport()->one();

        try {
            $this->processDataExportPart($dataExportPart);
        } catch (\Throwable $e) {
            $this->error('Exception: ' . $e->getMessage());
            $dataExportPart->setException($e->getMessage());
            $dataExportPart->setFinished();
            $dataExportPart->save(false);
        } finally {
            $this->removeTmpFiles();
        }

        $this->enqueueMergePartsIfNeed($dataExport);

        $this->info('Rotating data (removing old data imports)');
        $rotator = $this->rotatorFactory->getRotator(RotatorFactory::TYPE_EXPORT);
        $rotator->rotate();

        $this->info('Part processing has been finished!');

        return ConsumerInterface::MSG_ACK;
    }

    private function processDataExportPart(DataExportPart $dataExportPart): void
    {
        $tmpFile = $this->getTmpFilePath();

        /** @var DataExport $dataExport */
        $dataExport = $dataExportPart->getDataExport()->one();
        $dataExportPart->setInProgress();
        $dataExportPart->save(false);
        $dataExport->setInProgress();
        $dataExport->save(false);

        /** @var DataExportTemplate $exportTemplate */
        $exportTemplate = $dataExport->getTemplate()->one();
        $exportConfig = new ExportConfig();
        $exportConfig->criteria = $exportTemplate->criteria ?? [];
        $exportConfig->extra_criteria = $exportTemplate->extra_criteria ?? [];
        $exportConfig->fields = $exportTemplate->fields ?? [];

        $exporter = $this->exporterFacotry->getExporter($dataExport->handler_name);
        $dataWriter = $this->dataWriterFactory->getDataWriter(DataWriterFactory::DATA_FORMAT_CSV);

        $dataForExport = $exporter->getData($dataExportPart->limit, $dataExportPart->offset, $exportConfig);
        $dataForExport = $this->removeRestrictedFields($dataForExport, $exporter->getWhiteListedFields($exportConfig));

        $countDtaForExport = count($dataForExport);

        $this->info([
            'exporterClass' => get_class($exporter),
            'dataWriterClass' => get_class($dataWriter),
            'countDataForExport' => $countDtaForExport,
        ]);

        $dataExportPart->count_all_items = $countDtaForExport;
        $dataExportPart->save(false);

        if ($countDtaForExport > 0) {
            $dataWriter->saveData($dataForExport, $tmpFile);
            $dataExportPart->uploadFileToStorage($tmpFile, $dataWriter->getExtension());
        }

        $dataExportPart->count_exported_items = $countDtaForExport;
        $dataExportPart->setFinished();
        $dataExportPart->save(false);
    }

    /**
     * Removes from data all not whitelisted fields.
     */
    private function removeRestrictedFields(array $dataForExport, array $allowedFields): array
    {
        $dataForExportFiltered = [];

        foreach ($dataForExport as $k => $data) {
            $filtered = [];

            // Preserving sort order as it is in allowed fields
            foreach ($allowedFields as $allowedField) {
                $filtered[$allowedField] = $data[$allowedField] ?? null;
            }
            $dataForExportFiltered[] = $filtered;
            unset($dataForExport[$k]);
        }

        return $dataForExportFiltered;
    }

    /**
     * Puts task to merge parts into queue if all parts has been finished.
     */
    private function enqueueMergePartsIfNeed(DataExport $dataExport): void
    {
        $countNotFinishedParts = $dataExport
            ->getDataExportParts()
            ->where(['not', ['status' => [DataExportPart::STATUS_FINISHED, DataExportPart::STATUS_NO_ITEMS]]])
            ->count();

        if ($countNotFinishedParts > 0) {
            return;
        }

        /** @var Producer $producer */
        $producer = \Yii::$app->rabbitmq->getProducer(MessageAbstract::PRODUCER_NAME);
        $producer->publish(
            json_encode([
                'data_export_id' => $dataExport->id,
                'customer_id' => Yii::$app->dbManager->getCustomerId(),
            ]),
            MessageInterface::EXCHANGE_NAME_DATA_EXPORT,
            'merge-parts'
        );

        $this->info("All parts has been finished, enqueued task for merging parts");
    }

    /**
     * Removes temporary files.
     */
    private function removeTmpFiles()
    {
        foreach ($this->tmpFiles as $tmpFilePath) {
            if (file_exists($tmpFilePath)) {
                $this->filesystem->remove($tmpFilePath);
            }
        }
    }

    private function getTmpFilePath()
    {
        $tmpDirectory = Yii::getAlias('@runtime/import-export/part_processing');
        FileHelper::createDirectory($tmpDirectory);
        $tmpFilePath = $tmpDirectory . '/' . uniqid('', true);
        $this->tmpFiles[] = $tmpFilePath;

        return $tmpFilePath;
    }
}
