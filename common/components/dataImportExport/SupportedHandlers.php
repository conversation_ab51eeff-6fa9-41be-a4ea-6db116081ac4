<?php

namespace common\components\dataImportExport;

/**
 * Contains list of supported handlers.
 */
class SupportedHandlers
{
    public const HANDLER_AGGREGATED_SALES = 'aggregated_sales_info';
    public const HANDLER_PRODUCT_COST_PERIODS = 'product_cost_periods';
    public const HANDLER_ORDER_FBM_COST = 'order_fbm_cost';
    public const HANDLER_ORDER_ITEM_FBM_COST = 'order_item_fbm_cost';
    public const HANDLER_PRODUCT = 'product';
    public const HANDLER_ORDER = 'orders';
    public const HANDLER_ORDER_V1 = 'orders_v1';

    public const EXPORT_SUPPORTED_HANDLERS = [
        self::HANDLER_PRODUCT_COST_PERIODS,
        self::HANDLER_ORDER_ITEM_FBM_COST,
        self::HANDLER_ORDER_FBM_COST,
        self::HANDLER_ORDER,
        self::HANDLER_ORDER_V1,
    ];

    public const IMPORT_SUPPORTED_HANDLERS = [
        self::HANDLER_PRODUCT_COST_PERIODS,
        self::HANDLER_ORDER_ITEM_FBM_COST,
        self::HANDLER_ORDER_FBM_COST,
    ];

    public const BULK_EDIT_SUPPORTED_HANDLERS = [
        self::HANDLER_PRODUCT,
    ];
}
