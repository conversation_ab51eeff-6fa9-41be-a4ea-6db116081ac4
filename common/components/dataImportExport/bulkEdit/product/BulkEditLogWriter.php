<?php

namespace common\components\dataImportExport\bulkEdit\product;

use common\models\customer\DataImportBulkEditPart;
use common\models\customer\Product;
use yii\helpers\Json;

class BulkEditLogWriter
{
    /** @var DataImportBulkEditPart */
    public DataImportBulkEditPart $bulkEditPart;

    public function __construct(DataImportBulkEditPart $bulkEditPart)
    {
        $this->bulkEditPart = $bulkEditPart;
    }

    public function getValidationErrorLog(Product $product): array
    {
        return [
            'id' => $product->id,
            'errors' => $product->getErrors(),
        ];
    }

    public function writeErrorLog(string $message, int $errorCount)
    {
        $errors = is_null($this->bulkEditPart->errors) ? [] : Json::decode($this->bulkEditPart->errors);

        $decodedData = Json::decode($message);
        $errors = array_values(array_merge($errors + $decodedData));
        $this->bulkEditPart->errors = Json::encode($errors);
        $this->bulkEditPart->count_errors = $this->bulkEditPart->count_errors + $errorCount;
        $this->bulkEditPart->updated_at = date('Y-m-d H:i:s');
        $this->bulkEditPart->update(false, ['updated_at', 'count_errors', 'errors']);
    }

    public function writeSuccessfulLog()
    {
        $this->bulkEditPart->count_imported_items = $this->bulkEditPart->count_imported_items + 1;
        $this->bulkEditPart->updated_at = date('Y-m-d H:i:s');
        $this->bulkEditPart->update(false, ['updated_at', 'count_imported_items']);
    }

    public function writeProductCountLog($count)
    {
        $this->bulkEditPart->count_all_items = $count;
        $this->bulkEditPart->updated_at = date('Y-m-d H:i:s');
        $this->bulkEditPart->update(false, ['updated_at', 'count_all_items']);
    }
}
