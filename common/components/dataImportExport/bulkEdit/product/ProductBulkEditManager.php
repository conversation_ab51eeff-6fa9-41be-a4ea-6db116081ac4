<?php

namespace common\components\dataImportExport\bulkEdit\product;

use common\models\customer\DataImport;
use common\models\customer\Product;
use yii\db\ActiveQuery;
use yii\helpers\Json;

class ProductBulkEditManager
{
    private DataImport $bulkEdit;

    private $side;
    private $ids;
    private $query;

    public function __construct(DataImport $bulkEdit)
    {
        $this->bulkEdit = $bulkEdit;
        $params = Json::decode($this->bulkEdit->params);

        $this->side = $params['settings']['side'];
        $this->ids = $params['settings']['ids'];
        $this->query = $params['query'];
    }

    public function getProductsCount(): int
    {
        return $this->getInitialQuery()->count();
    }

    public function getInitialQuery(): ActiveQuery
    {
        if ($this->side === DataImport::SIDE_ALL) {
            $query = $this->getProductActiveQuery();
        } else {
            $query = Product::find()->andWhere(['product.id' => $this->ids]);
        }

        return $query;
    }

    public function getProductActiveQuery(): ActiveQuery
    {
        return (new Product())->search($this->query);
    }
}
