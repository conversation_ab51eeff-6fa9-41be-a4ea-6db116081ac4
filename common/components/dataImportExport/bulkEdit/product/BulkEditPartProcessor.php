<?php

namespace common\components\dataImportExport\bulkEdit\product;

use common\components\dataImportExport\Constants;
use common\components\dataImportExport\import\importer\ProductCostPeriods;
use common\components\dataImportExport\import\ImportError;
use common\components\LogToConsoleTrait;
use common\models\customer\DataImport;
use common\models\customer\DataImportBulkEditPart;
use common\models\customer\Product;
use yii\db\ActiveQuery;
use yii\helpers\Json;

class BulkEditPartProcessor
{
    use LogToConsoleTrait;

    const ITEM_LIMIT = 20;

    /** @var DataImport */
    public DataImport $bulkEdit;

    /** @var DataImportBulkEditPart */
    public DataImportBulkEditPart $bulkEditPart;

    private $side;
    private $ids;

    private $query;
    private $fields;

    /** @var BulkEditLogWriter */
    private BulkEditLogWriter $logWriter;

    public function __construct(DataImportBulkEditPart $bulkEditPart)
    {
        $this->bulkEditPart = $bulkEditPart;
        $this->bulkEdit = $bulkEditPart->dataImport;
        $params = Json::decode($this->bulkEdit->params);
        $this->logWriter = new BulkEditLogWriter($bulkEditPart);

        /** @todo add additional check if array exists and maybe use DTO */
        $this->side = $params['settings']['side'];
        $this->ids = $params['settings']['ids'];
        $this->query = $params['query'];
        $this->fields = $params['fields'];
    }

    private function setStatusDone()
    {
        $this->bulkEditPart->setFinished();

        $processedPartsCount = DataImportBulkEditPart::find()
            ->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])
            ->andWhere(['data_import_bulk_part.status' => [DataImport::STATUS_FINISHED]])->count();

        $totalPartsCount = DataImportBulkEditPart::find()->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])->count();

        if ($processedPartsCount === $totalPartsCount) {
            $this->bulkEdit->setFinished();
            $this->bulkEdit->count_all_items = DataImportBulkEditPart::find()
                ->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])->sum('count_all_items');
            $this->bulkEdit->count_errors = DataImportBulkEditPart::find()
                ->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])->sum('count_errors');
            $this->bulkEdit->count_imported_items = DataImportBulkEditPart::find()
                ->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])->sum('count_imported_items');

            $errors = DataImportBulkEditPart::find()->select('errors')
                ->andWhere(['data_import_bulk_part.data_import_id' => $this->bulkEditPart->data_import_id])
                ->andWhere(['IS NOT', 'errors', null])
                ->asArray()->column();

            $mergedErrors = [];

            foreach ($errors as $error) {
                $decodedError = Json::decode(Json::decode($error));
                $mergedErrors = array_merge($mergedErrors, $decodedError);
            }

            $this->bulkEdit->errors = $mergedErrors;

            $this->bulkEdit->update(false, ['errors', 'status', 'finished_at']);
        }
    }


    public function setStatusTerminated($error = null)
    {
        $this->bulkEditPart->setTerminated();
        if ($error) {
            $this->bulkEditPart->errors = $error;
        }

        $this->bulkEditPart->finished_at = date('Y-m-d H:i:s');
        $this->bulkEditPart->update(false, ['status', 'errors', 'finished_at']);

        $this->bulkEdit->refresh();
        if ($this->bulkEdit->status !== DataImportBulkEditPart::STATUS_TERMINATED) {
            $this->bulkEdit->status = DataImportBulkEditPart::STATUS_TERMINATED;
            $this->bulkEdit->finished_at = date('Y-m-d H:i:s');
            if ($error) {
                $this->bulkEdit->errors = $error;
            }

            $this->bulkEdit->update(false, ['status', 'finished_at', 'errors']);
        }
    }

    public function writeCounters(int $errorCount, int $successfulCount)
    {
        DataImport::getDb()->createCommand('
            update ' . DataImport::tableName() . '
            set count_errors = count_errors + :errorCount, count_imported_items = count_imported_items + :successfulCount, updated_at = :dateUpdated
            where id = :bulkEditId
        ', [
            ':errorCount' => $errorCount,
            ':successfulCount' => $successfulCount,
            ':bulkEditId' => $this->bulkEdit->id,
            ':dateUpdated' => date('Y-m-d H:i:s'),
        ])->execute();
    }

    protected function getProductsCount(): int
    {
        return $this->getInitialQuery()->count();
    }

    protected function getInitialQuery(): ActiveQuery
    {
        if ($this->side === DataImport::SIDE_ALL) {
            $query = $this->getProductActiveQuery();
        } else {
            $query = Product::find()->andWhere(['product.id' => $this->ids]);
        }

        $query->andWhere(['>=', 'product.id', $this->bulkEditPart->start_id])->andWhere(['<=', 'product.id', $this->bulkEditPart->finish_id]);

        return $query;
    }

    public function process()
    {
        $this->bulkEditPart->setInProgress();
        /** @var DataImport $dataImport */
        $dataImport = $this->bulkEditPart->getDataImport()->one();
        $dataImport->setInProgress();

        if ($this->side === DataImport::SIDE_ALL) {
            $this->ids = [];
        }

        $query = $this->getInitialQuery();

        $this->logWriter->writeProductCountLog($this->getProductsCount());

        $importer = new ProductCostPeriods();

        while ($items = $query->andWhere(['>', 'product.id', $this->bulkEditPart->last_processed_id])->limit(self::ITEM_LIMIT)->orderBy('product.id ASC')->all()) {
            $this->bulkEdit->refresh();
            if ($this->bulkEdit->isStatusTerminated()) {
                $this->setStatusTerminated();
                return;
            }
            $errors = [];
            $successfulCount = 0;
            $errorsCount = 0;

            /** @var Product $product */
            foreach ($items as $product) {
                try {
                    $dataForImport = [
                        'marketplace' => $product->marketplace_id,
                        'seller_id' => $product->seller_id,
                        'item_sku' => $product->sku,
                        'cost_of_goods' => $this->fields['buying_price'] ?? null,
                        'shipping_costs' => $this->fields['shipping_cost'] ?? null,
                        'other_fees' => $this->fields['other_fees'] ?? null,
                        'vat' => $this->fields['vat'] ?? null,
                        'synchronize_with_repricer' => $this->fields['is_enabled_sync_with_repricer'] ?? null,
                        'tags_add' => $this->fields['tags_add'] ?? null,
                        'tags_remove' => $this->fields['tags_remove'] ?? null,
                    ];

                    if ($dataForImport['synchronize_with_repricer'] === 1) {
                        $dataForImport['synchronize_with_repricer'] = Constants::YES;
                    } else if ($dataForImport['synchronize_with_repricer'] === 0) {
                        $dataForImport['synchronize_with_repricer'] = Constants::NO;
                    }

                    $importResult = $importer->import([$dataForImport], random_int(1, 1000000));

                    if ($importResult->countLinesWithErrors > 0) {
                        $productErrors = [
                            'id' => $product->id,
                            'errors' => [],
                        ];

                        /** @var ImportError $error */
                        foreach ($importResult->errors as $error) {
                            $productErrors['errors'][$error->field][] = $error->errorUntranslated;
                            $errorsCount++;
                        }

                        $errors[] = $productErrors;
                    } else {
                        $this->logWriter->writeSuccessfulLog();
                        $successfulCount++;
                    }
                } catch (\Throwable $e) {
                    $this->error($e);
                    $this->logWriter->writeErrorLog(Json::encode(['id' => $product->id, 'errors' => ['id' => [YII_ENV === 'prod' ? $e->getMessage() : $e->__toString()]]]), 1);
                    $errorsCount++;
                } finally {
                    $this->updateLastProcessedId($product->id);
                }
            }
            if (!empty($errors)) {
                $this->logWriter->writeErrorLog(Json::encode($errors), $errorsCount);
            }

            $this->writeCounters($errorsCount, $successfulCount);
        }

        $this->setStatusDone();
    }

    /**
     * @param  Product $product
     * @return Product
     */
    protected function populateAttributes(Product $product): Product
    {

        $this->setProductAttribute('is_enabled_sync_with_repricer', $product);

        return $product;
    }

    /**
     * @param string        $name
     * @param Product $product
     */
    private function setProductAttribute(string $name, Product $product)
    {
        if ($this->isProductAttributeExists($name)) {
            $product->$name = $this->fields[$name];
        }
    }

    /**
     * @param  string $name
     * @return bool
     */
    private function isProductAttributeExists(string $name): bool
    {
        return array_key_exists($name, $this->fields);
    }

    /**
     * @param $lastId
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    private function updateLastProcessedId($lastId)
    {
        $this->bulkEditPart->last_processed_id = $lastId;
        $this->bulkEditPart->updated_at = date('Y-m-d H:i:s');
        $this->bulkEditPart->update(false, ['updated_at', 'last_processed_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProductActiveQuery()
    {
        return (new Product())->search($this->query);
    }
}
