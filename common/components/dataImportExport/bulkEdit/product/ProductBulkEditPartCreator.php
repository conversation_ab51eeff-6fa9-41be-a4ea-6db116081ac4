<?php
namespace common\components\dataImportExport\bulkEdit\product;

use common\components\LogToConsoleTrait;
use common\models\customer\DataImportBulkEditPart;
use common\models\customer\DataImport;

class ProductBulkEditPartCreator
{
    use LogToConsoleTrait;

    private const ITEMS_PER_PART = 500;

    /** @var DataImport */
    private DataImport $bulkEdit;

    /**
     * @param DataImport $bulkEdit
     */
    public function __construct(DataImport $bulkEdit)
    {
        $this->bulkEdit = $bulkEdit;
    }

    /**
     * @throws \yii\db\Exception
     * @return DataImportBulkEditPart[]
     */
    public function createParts(): void
    {
        $this->info('Started creating bulk edit parts');
        $initialQuery = (new ProductBulkEditManager($this->bulkEdit))->getInitialQuery();
        $initialQuery->orderBy('product.id ASC');
        $countProducts = (clone $initialQuery)->count();

        $this->bulkEdit->count_all_items = $countProducts;
        $this->bulkEdit->save(false);

        $finishId = null;
        $partNumber = 0;

        while (true) {
            $query = clone $initialQuery;
            $query->limit(self::ITEMS_PER_PART);

            if (!is_null($finishId)) {
                $query->andWhere(['>', 'product.id', $finishId]);
            }

            $ids = $query->select(['product.id'])->column();

            if (empty($ids)) {
                break;
            }

            $partNumber++;
            $bulkEditPart = new DataImportBulkEditPart();
            $bulkEditPart->data_import_id = $this->bulkEdit->id;
            $bulkEditPart->part_no = $partNumber;

            $startId = $ids[0];
            $finishId = $ids[count($ids) - 1];

            $bulkEditPart->start_id = $startId;
            $bulkEditPart->finish_id = $finishId;
            $bulkEditPart->count_all_items = count($ids);
            $bulkEditPart->status = DataImport::STATUS_NEW;
            $bulkEditPart->created_at = date('Y-m-d H:i:s');
            $bulkEditPart->updated_at = date('Y-m-d H:i:s');
            $bulkEditPart->log('Created');
            $bulkEditPart->save(false);

            if ($partNumber % 100 === 0) {
                $this->info("Created $partNumber parts");
            }
        }

        $this->bulkEdit->count_parts = $partNumber;
        $this->bulkEdit->save(false);

        $this->info('Finished creating bulk edit parts');
    }
}
