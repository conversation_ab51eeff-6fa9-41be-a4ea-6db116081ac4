<?php

namespace common\components\dataImportExport\import\importer;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\dataImportExport\import\ImportError;
use common\components\dataImportExport\import\ImportResult;
use common\components\dataImportExport\SupportedHandlers;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use yii\caching\CacheInterface;
use yii\db\Query;

/**
 * An adapter of old logic to a new one.
 */
class OrderFbmCost extends OrderItemFbmCost implements ImporterInterface
{
    protected DbManager $dbManager;
    protected CacheInterface $cache;


    public function getImportFilePrefix(): string
    {
        return SupportedHandlers::HANDLER_ORDER_FBM_COST;
    }

    /**
     * {@inheritdoc}
     */
    public function getItemsPerPart(): int
    {
        return 100;
    }

    /**
     * {@inheritdoc}
     */
    public function getRequiredFields(): array
    {
        return [
            'order_number'
        ];
    }

    /**
     * {@inheritdoc}
     * @throws \Exception
     */
    public function import(array $dataToImport, int $dataImportId): ImportResult
    {
        $importResult = new ImportResult();
        $shippingCostForItems = [];

        $amazonOrderIdsBySeller = [];
        $orderIds = [];
        foreach ($dataToImport as $data) {
            $orderIds[] = $data['order_number'];
        }

        $amazonOrderItems = $this->findOrderItems($orderIds);

        $amazonOrderItemByOrderIdAndSku = [];

        foreach ($amazonOrderItems as $item) {
            $orderId = $item['order_id'];
            $amazonOrderItemByOrderIdAndSku[$orderId][] = $item;
        }

        foreach ($dataToImport as $k => $data) {
            if (empty($data['order_number'])) {
                unset($dataToImport[$k]);
                continue;
            }

            if (!isset($amazonOrderItemByOrderIdAndSku[$data['order_number']])) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'order_number';
                $importError->errorUntranslated = \Yii::t('import_export', "Unable to find such order. Please check the order_number");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            $amazonOrderId = $data['order_number'];
            $sellerId = $amazonOrderItemByOrderIdAndSku[$data['order_number']][0]['seller_id'];
            $this->dbManager->setSellerId($sellerId);

            $model = new AmazonOrder();
            $model->setScenario(AmazonOrder::SCENARIO_DATA_IMPORT);

            if (!$this->validateModel([
                'amazon_order_id' => $data['order_number'],
                'order_shipping_costs' => $data['order_shipping_costs']
            ], $model, $importResult, $k)) {
                unset($dataToImport[$k]);
                continue;
            }

            $orderShippingCost = $data['order_shipping_costs'];

            if ($this->isAlreadyProcessed($dataImportId, $amazonOrderId)) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'order_number';
                $importError->errorUntranslated = \Yii::t('import_export', "A duplicate order was found. Please check the order_number.");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            $orderItems = $amazonOrderItemByOrderIdAndSku[$data['order_number']];

            $newShippingCost = $orderShippingCost / count($orderItems);

            $currencyId = CurrencyRateManager::BASE_CURRENCY;;
            foreach ($orderItems as $orderItem) {
                $shippingCostForItems[$orderItem->order_id][] = [
                    'order_item' => $orderItem,
                    'shipping_cost' => $newShippingCost
                ];
                $currencyId = $orderItem->currency_id;
            }

            AmazonOrderItem::updateAll([
                'manual_shipping_cost' => $newShippingCost,
                'manual_shipping_cost_currency' => $currencyId,
            ], [
                'order_item_id' => array_column($orderItems, 'order_item_id')
            ]);
            $amazonOrderIdsBySeller[$sellerId][] = $amazonOrderId;

            $this->markAsAlreadyProcessed($dataImportId, $amazonOrderId);

        }

        foreach ($amazonOrderIdsBySeller as $selleId => $amazonOrderIds) {
            $this->dbManager->setSellerId($selleId);

            AmazonOrder::updateAll([
                'has_manual_shipping_cost' => true,
            ], [
                'amazon_order_id' => $amazonOrderIds
            ]);
        }

        return $this->handleShippingCosts($shippingCostForItems, $importResult, false);
    }

    public function getExampleData(): array
    {
        $columns = [
            'order_number' => [
                'title' => \Yii::t('import_export', 'Order number'),
                'description' => \Yii::t('import_export', 'A unique order number assigned by Amazon.'),
                'format' => \Yii::t('import_export', 'An alphanumeric string.'),
                'isRequired' => true,
                'group' => 'common',
            ],
            'order_shipping_costs' => [
                'title' => \Yii::t('import_export', 'Order FBM shipping costs'),
                'description' => \Yii::t('import_export', 'FBM shipping costs applied to the total order. These shipping costs can only be applied to FBM orders.'),
                'format' => 'An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.',
                'isRequired' => false,
                'group' => 'uneditable',
            ]
        ];

        $dataColumns = [
            'order_number' => '301-5138521-7069139',
            'order_shipping_costs' => 40.95
        ];

        $data = [$dataColumns];

        return [
            'groups' => [
                'common' => [
                    'title' => \Yii::t('import_export', 'Basic order and order item information'),
                    'colorRgb' => 'ff0611',
                ],
                'uneditable' => [
                    'title' => \Yii::t('import_export', 'Advanced order item information'),
                    'colorRgb' => '92d05b',
                ],
            ],
            'columns' => $columns,
            'data' => $data,
        ];
    }
}
