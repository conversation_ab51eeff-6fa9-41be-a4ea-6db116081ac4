<?php

namespace common\components\dataImportExport\import\importer;

use common\components\dataImportExport\import\ImportResult;

/**
 * Interface ImporterInterface.
 */
interface ImporterInterface
{
    /**
     * Returns amount of items can be saved per part.
     *
     * @return int
     */
    public function getItemsPerPart(): int;

    /**
     * Returns array of required fields needed for "pre" validation.
     *
     * @return array
     */
    public function getRequiredFields(): array;

    /**
     * Returns example structure of import file.
     *
     * @return array
     */
    public function getExampleData(): array;

    /**
     * Makes data import.
     *
     * @param  array $dataToImport
     * @param  int $dataImportId
     * @return ImportResult
     */
    public function import(array $dataToImport, int $dataImportId): ImportResult;

    /**
     * Returns prefix of import file name.
     *
     * @return string
     */
    public function getImportFilePrefix(): string;

    /**
     * Update views clickhouse and optimize table after import
     *
     * @return void
     */
    public function onAfterImport(): void;
}
