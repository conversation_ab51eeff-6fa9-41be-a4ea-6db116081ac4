<?php

namespace common\components\dataImportExport\import\importer;

use yii\helpers\Inflector;

/**
 * Class ImporterFactory.
 */
class ImporterFactory
{
    /**
     * Creates and returns needed data importer.
     *
     * @param  string                                                                        $handlerName
     * @return \common\components\dataImportExport\import\importer\ImporterInterface
     */
    public function getImporter(string $handlerName): ImporterInterface
    {
        $className = __NAMESPACE__ . '\\' . ucfirst(Inflector::camelize($handlerName));
        return new $className();
    }
}
