<?php

namespace common\components\dataImportExport\import\importer;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\clickhouse\ShippingCostAmount;
use common\components\services\order\TransferOrderService;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\order\AmazonOrder;
use common\models\order\base\AbstractOrderRecord;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Order;
use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\import\ImportError;
use common\components\dataImportExport\import\ImportResult;
use common\components\dataImportExport\SupportedHandlers;
use common\models\order\AmazonOrderItem;
use common\models\SalesCategory;
use DateTime;
use Yii;
use yii\caching\CacheInterface;
use yii\db\Exception;
use yii\db\Query;


class OrderItemFbmCost extends BaseImporter implements ImporterInterface
{
    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->cache = \Yii::$app->fastPersistentCache;
    }

    public function getImportFilePrefix(): string
    {
        return SupportedHandlers::HANDLER_ORDER_ITEM_FBM_COST;
    }

    /**
     * {@inheritdoc}
     */
    public function getItemsPerPart(): int
    {
        return 100;
    }

    /**
     * {@inheritdoc}
     */
    public function getRequiredFields(): array
    {
        return [
            'item_sku',
            'order_number'
        ];
    }

    public function import(array $dataToImport, int $dataImportId): ImportResult
    {
        $importResult = new ImportResult();
        $shippingCostForItems = [];
        $amazonOrderIdsBySeller = [];
        $orderIds = [];
        foreach ($dataToImport as $data) {
            $orderIds[] = $data['order_number'];
        }

        $amazonOrderItems = $this->findOrderItems($orderIds);

        $amazonOrderItemByOrderIdAndSku = [];

        foreach ($amazonOrderItems as $item) {
            $orderId = $item['order_id'];
            $sku = $item['seller_sku'];

            $amazonOrderItemByOrderIdAndSku[$orderId][$sku] = $item;
        }

        foreach ($dataToImport as $k => $data) {

            if (empty($data['order_number']) || empty($data['item_sku'])) {
                unset($dataToImport[$k]);
                continue;
            }

            if (!isset($amazonOrderItemByOrderIdAndSku[$data['order_number']][$data['item_sku']])) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'item_sku';
                $importError->errorUntranslated = \Yii::t('import_export', "Unable to find such orders. Please check the item_sku");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            $orderItem = $amazonOrderItemByOrderIdAndSku[$data['order_number']][$data['item_sku']];

            $this->dbManager->setSellerId($orderItem['seller_id']);

            $model = new AmazonOrderItem();
            $model->setScenario(AmazonOrderItem::SCENARIO_DATA_IMPORT);

            if (!$this->validateModel([
                'order_id' => $data['order_number'],
                'sku' => $data['item_sku'],
                'order_shipping_costs' => $data['order_shipping_costs']
            ], $model, $importResult, $k)) {
                unset($dataToImport[$k]);
                continue;
            }

            if ($this->isAlreadyProcessed($dataImportId, $data['order_number'], $data['item_sku'])) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'order_id';
                $importError->errorUntranslated = \Yii::t('import_export', "A duplicate order was found. Please check the order_number, item_sku.");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }
            $this->markAsAlreadyProcessed($dataImportId, $data['order_number'], $data['item_sku']);

            $shippingCostForItems[$orderItem->order_id][] = [
                'order_item' => $orderItem,
                'shipping_cost' => $data['order_shipping_costs']
            ];

            AmazonOrderItem::updateAll([
                'manual_shipping_cost' => $data['order_shipping_costs'],
                'manual_shipping_cost_currency' => $orderItem->currency_id,
            ], [
                'order_item_id' => $orderItem->order_item_id,
                'sku' => $data['item_sku']
            ]);

            $amazonOrderIdsBySeller[$orderItem->seller_id][] = $orderItem->order_id;
        }

        foreach ($amazonOrderIdsBySeller as $selleId => $amazonOrderIds) {
            $this->dbManager->setSellerId($selleId);

            AmazonOrder::updateAll([
                'has_manual_shipping_cost' => true,
            ], [
                'amazon_order_id' => $amazonOrderIds
            ]);
        }

        return $this->handleShippingCosts($shippingCostForItems, $importResult, false);
    }

    public function getExampleData(): array
    {
        $columns = [
            'order_number' => [
                'title' => \Yii::t('import_export', 'Order number'),
                'description' => \Yii::t('import_export', 'A unique order number assigned by Amazon.'),
                'format' => \Yii::t('import_export', 'An alphanumeric string.'),
                'isRequired' => true,
                'group' => 'common',
            ],
            'item_sku' => [
                'title' => \Yii::t('import_export', 'SKU (Stock Keeping Unit)'),
                'description' => \Yii::t('import_export', 'An unambiguous knowledge of the product assigned by the retailer. The SKU must be unambiguous for each product listed.'),
                'format' => \Yii::t('import_export', 'An alphanumeric string with a min. length of one and a max. length of 16 characters.'),
                'isRequired' => true,
                'group' => 'common',
            ],
            'order_shipping_costs' => [
                'title' => \Yii::t('import_export', 'Order FBM shipping costs'),
                'description' => \Yii::t('import_export', 'FBM shipping costs applied to the total order. These shipping costs can only be applied to FBM orders.'),
                'format' => 'An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.',
                'isRequired' => false,
                'group' => 'uneditable',
            ]
        ];

        $dataColumns = [
            'order_number' => '301-5138521-7069139',
            'item_sku' => '123456-XYZ',
            'order_shipping_costs' => 5.9
        ];

        $data = [$dataColumns];

        return [
            'groups' => [
                'common' => [
                    'title' => \Yii::t('import_export', 'Basic order and order item information'),
                    'colorRgb' => 'ff0611',
                ],
                'uneditable' => [
                    'title' => \Yii::t('import_export', 'Advanced order item information'),
                    'colorRgb' => '92d05b',
                ],
            ],
            'columns' => $columns,
            'data' => $data,
        ];
    }

    protected function toFloatString(string $value): string
    {
        $value = preg_replace('/\s/', '', $value);
        return str_replace(',', '.', $value);
    }

    protected function isAlreadyProcessed(
        int $dataImportId,
        string $orderId,
        string $sellerSku = ''
    ): bool
    {
        $cacheKey = $this->getIsAlreadyProcessedCacheKey($dataImportId, $orderId, $sellerSku);
        return !empty($this->cache->get($cacheKey));
    }

    protected function markAsAlreadyProcessed(
        int $dataImportId,
        string $orderId,
        string $sellerSku = ''
    ): void
    {
        $cacheKey = $this->getIsAlreadyProcessedCacheKey($dataImportId, $orderId, $sellerSku);
        $this->cache->set($cacheKey, 1, 60 * 5);
    }

    protected function getIsAlreadyProcessedCacheKey(
        int $dataImportId,
        string $orderId,
        string $sellerSku = ''
    ): string
    {
        return implode('_', [
            'already_processed_rows',
            $this->dbManager->getCustomerId(),
            $dataImportId,
            $orderId,
            $sellerSku
        ]);
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function onAfterImport(): void
    {
        try {
            $manager = new DynamicTablesManager();
            $manager->rebuildDynamicTable(new \common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView());
            $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
            $manager->rebuildDynamicTable(new \common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView());
            $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedView());
            $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedViewV1());
            $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());

            $this
                ->dbManager->getClickhouseCustomerDb()
                ->createCommand("OPTIMIZE TABLE " . \common\models\customer\TransactionExtendedView::tableName())
                ->execute();

            $this
                ->dbManager->getClickhouseCustomerDb()
                ->createCommand("OPTIMIZE TABLE " . \common\models\customer\TransactionExtendedViewV1::tableName())
                ->execute();
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    protected function validateModel(array $data, AbstractOrderRecord $model, ImportResult $importResult, $lineNumber): bool
    {
        $model->load($data, '');
        if (!$model->validate()) {
            foreach ($model->getErrors() as $attribute => $errors) {
                foreach ($errors as $error) {
                    $importError = new ImportError();
                    $importError->lines = [$lineNumber];
                    $importError->field = $attribute;
                    $importError->errorUntranslated = $error;
                    $importResult->errors[] = $importError;
                }
            }
            $importResult->countLinesWithErrors++;
            return false;
        }
        return true;
    }

    protected function findOrderItems(array $orderIds): array
    {
        $orderItems = AmazonOrderExtendedView::find()
            ->where(['order_id' => $orderIds])
            ->andWhere(['!=', 'order_status', Order::ORDER_STATUS_CANCELED])
            ->all();

        if (empty($orderItems)) {
            $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());
            $orderItems = AmazonOrderInProgressExtendedView::find()
                ->where(['order_id' => $orderIds])
                ->andWhere(['version' => $lastVersion])
                ->andWhere(['!=', 'order_status', Order::ORDER_STATUS_CANCELED])
                ->all();
        }

        return $orderItems;
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    protected function handleShippingCosts(array $shippingCostForItems, ImportResult $importResult, bool $isOrderImport = false): ImportResult
    {
        $costAmount = new ShippingCostAmount(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);

        $minDateOrderPurchase = null;
        $maxDateOrderPurchase = null;

        foreach ($shippingCostForItems as $amazonOrderId => $orderItemsData) {
            [$transactions, $transactionOneBySKU] = $costAmount->generateOppositeTransactions($amazonOrderId);

            foreach ($orderItemsData as $orderItemData) {
                $newAmount = $orderItemData['shipping_cost'];
                $orderItem = $orderItemData['order_item'];
                $transactionForCopy = $transactionOneBySKU[$orderItem['seller_sku']] ?? null;
                $transactions[] = $costAmount->generateNewCostTransactions($orderItem, $orderItem->currency_id, $newAmount, $transactionForCopy);

                $orderPurchaseDate = new DateTime($orderItem->order_purchase_date);

                if ($minDateOrderPurchase === null || $orderPurchaseDate < $minDateOrderPurchase) {
                    $minDateOrderPurchase = $orderPurchaseDate;
                }
                if ($maxDateOrderPurchase === null || $orderPurchaseDate > $maxDateOrderPurchase) {
                    $maxDateOrderPurchase = $orderPurchaseDate;
                }
            }

            $costAmount->apply($transactions);

            $importResult->countImported += $isOrderImport ? 1 : count($orderItemsData);
        }

        return $importResult;
    }
}
