<?php

namespace common\components\dataImportExport\import\importer;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\COGSync\COGSynchronizer;
use common\components\COGSync\PeriodsMerge;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\dataImportExport\Constants;
use common\components\dataImportExport\import\ImportError;
use common\components\dataImportExport\import\ImportResult;
use common\components\dataImportExport\SupportedHandlers;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductTag;
use common\models\customer\Tag;
use common\models\SalesCategory;
use common\models\Seller;
use yii\caching\CacheInterface;
use yii\db\Exception;
use yii\helpers\Inflector;

/**
 * An adapter of old logic to a new one.
 */
class ProductCostPeriods extends BaseImporter implements ImporterInterface
{
    use LogToConsoleTrait;

    protected COGSynchronizer $COGSynchronizer;
    protected DbManager $dbManager;
    protected CacheInterface $cache;
    protected CustomerComponent $customerComponent;
    public const VALUE_IGNORED = 'exclude';

    public function __construct()
    {
        /** @var COGSynchronizer COGSynchronizer */
        $this->COGSynchronizer = \Yii::$container->get('COGSynchronizer');
        $this->dbManager = \Yii::$app->dbManager;
        $this->cache = \Yii::$app->fastPersistentCache;
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function getImportFilePrefix(): string
    {
        return SupportedHandlers::HANDLER_PRODUCT_COST_PERIODS;
    }

    /**
     * {@inheritdoc}
     */
    public function getItemsPerPart(): int
    {
        return 100;
    }

    /**
     * {@inheritdoc}
     */
    public function getRequiredFields(): array
    {
        return [
            'item_sku',
            'seller_id',
            'marketplace',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function import(array $dataToImport, int $dataImportId): ImportResult
    {
        $importResult = new ImportResult();

        foreach ($dataToImport as $k => $data) {
            $model = new Product();
            $model->setScenario(Product::SCENARIO_DATA_IMPORT);
            $model->load([
                'marketplace' => $data['marketplace'],
                'seller_id' => $data['seller_id'],
                'item_sku' => $data['item_sku']
            ], '');

            if (!$model->validate()) {
                foreach ($model->getErrors() as $attribute => $errors) {
                    foreach ($errors as $error) {
                        $importError = new ImportError();
                        $importError->lines = [$k];
                        $importError->field = $attribute;
                        $importError->errorUntranslated = $error;
                        $importResult->errors[] = $importError;
                    }
                }
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }
            unset($model);

            $marketplaceTitle = $data['marketplace'] ?? null;
            $marketplaceId = AmazonMarketplace::find()
                ->where([
                    'OR',
                    ['title' => $marketplaceTitle],
                    ['id' => $marketplaceTitle]
                ])
                ->cache(60)
                ->scalar();

            if (empty($marketplaceId)) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'marketplace';
                $importError->errorUntranslated = \Yii::t('import_export', "{attribute} is invalid.", ['attribute' => 'marketplace']);
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            /** @var Product $model */
            $model = Product::find()->where([
                'marketplace_id' => $marketplaceId,
                'seller_id' => $data['seller_id'],
                'sku' => $data['item_sku']
            ])->one();

            if (empty($model)) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'item_sku';
                $importError->errorUntranslated = \Yii::t('import_export', "Unable to find such product. Please check the marketplace, seller_id, item_sku");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            $activeSellerIds = Seller::find()->select('id')->where([
                'customer_id' => $this->dbManager->getCustomerId(),
                'is_active' => true
            ])->cache(10)->column();

            if (!in_array($model->seller_id, $activeSellerIds)) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'seller_id';
                $importError->errorUntranslated = \Yii::t('import_export', "That account have not been included in active subscription.");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            if ($this->isAlreadyProcessed($dataImportId, $model->marketplace_id, $model->seller_id, $model->sku)) {
                $importError = new ImportError();
                $importError->lines = [$k];
                $importError->field = 'item_sku';
                $importError->errorUntranslated = \Yii::t('import_export', "A duplicate product was found. Please check the marketplace_id, seller_id, and item_sku.");
                $importResult->errors[] = $importError;
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }
            $this->markAsAlreadyProcessed($dataImportId, $model->marketplace_id, $model->seller_id, $model->sku);

            $model->setScenario(Product::SCENARIO_DATA_IMPORT);

            $model->marketplace = $model->marketplace_id;
            $model->item_sku = $model->sku;
            $model->cost_of_goods = $model->buying_price;

            $periodPrototype =  [
                'marketplace_id' => $model->marketplace_id,
                'seller_id' => $model->seller_id,
                'seller_sku' => $model->sku,
                'is_first' => false,
                'date_start' => null,
                'has_transactions_in_clickhouse' => false,
                'productCostItems' => []
            ];
            $productCostItemsToCreate = [];

            if (isset($data['vat']) && trim($data['vat']) !== '') {
                $value = strtolower($data['vat']) == self::VALUE_IGNORED ? null : (float)$this->toFloatString($data['vat']);
                $dateStart = $data['vat_start_date'] ?? null;
                $model->vat = $value;
                $model->vat_start_date = $dateStart;
                $productCostItemsToCreate[SalesCategory::CATEGORY_EXPENSES_TAXES] = array_merge($periodPrototype, [
                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                    'is_first' => null === $model->vat,
                    'date_start' => $dateStart,
                    'productCostItems' => [[
                        'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                        'amount_per_unit' => $value,
                        'marketplace_amount_per_unit' => $value,
                        'currency_id' => $model->currency_code,
                        'marketplace_currency_id' => $model->currency_code,
                        'marketplace_currency_rate' => 1,
                    ]]
                ]);
            }

            if (!empty($data['synchronize_with_repricer'])) {
                $phrase = $this->toOriginalLanguage($data['synchronize_with_repricer']);
                $phrase = trim($phrase);

                if (!$this->dbManager->isRepricerSync() === false) {
                    if (strtolower($phrase) === strtolower(Constants::YES)) {
                        $model->is_enabled_sync_with_repricer = true;
                    } else if (strtolower($phrase) === strtolower(Constants::NO)) {
                        $model->is_enabled_sync_with_repricer = false;
                    }
                }
            }

            foreach ($data as $keyCostCustomCategory => $valueCostCustomCategory) {
                if (
                    is_array($valueCostCustomCategory) ||
                    strpos($keyCostCustomCategory, '_start_date') !== false ||
                    trim($valueCostCustomCategory) === ''
                ) {
                    continue;
                }
                if (preg_match('/^(other_fees|cost_of_goods|shipping_costs)(_.*)?$/', $keyCostCustomCategory, $matches)) {
                    switch ($matches[1]) {
                        case 'other_fees':
                            $categoryType = SalesCategory::CATEGORY_EXPENSES_OTHER_FEES;
                            $startDateKey = 'other_fees_start_date';
                            $productCostCategories = ProductCostCategory::getAllBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_OTHER_FEES);
                            break;
                        case 'shipping_costs':
                            $categoryType = SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS;
                            $startDateKey = 'shipping_costs_start_date';
                            $productCostCategories = ProductCostCategory::getAllBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
                            break;
                        default:
                            $categoryType = SalesCategory::CATEGORY_EXPENSES_COG;
                            $startDateKey = 'cost_of_goods_start_date';
                            $productCostCategories = ProductCostCategory::getAllBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_COG);
                            break;
                    }
                    $customCategory = ltrim($matches[2], '_');

                    if (empty($customCategory)) {
                        $customCategory = $categoryType === SalesCategory::CATEGORY_EXPENSES_COG
                            ? 'net_purchase_price'
                            : 'default';
                    }

                    $categoryId = null;

                    foreach ($productCostCategories as $productCostCategory) {
                        if (Inflector::slug($productCostCategory['name'], '_') == $customCategory) {
                            $categoryId = $productCostCategory['id'];
                            $dateStart = $data[$startDateKey] ?? null;
                            $value = strtolower($valueCostCustomCategory) == self::VALUE_IGNORED ? null : (float)$this->toFloatString($valueCostCustomCategory);
                            $model->buying_price = $value;
                            $model->cost_of_goods = $value;
                            $model->cost_of_goods_start_date = $dateStart;
                            if (!isset($productCostItemsToCreate[$categoryType])) {
                                $productCostItemsToCreate[$categoryType] = array_merge($periodPrototype, [
                                    'sales_category_id' => $categoryType,
                                    'is_first' => null === $model->buying_price,
                                    'date_start' => $dateStart,
                                    'productCostItems' => []
                                ]);
                            }
                            $productCostItemsToCreate[$categoryType]['productCostItems'][] = [
                                'sales_category_id' => $categoryType,
                                'product_cost_category_id' => $categoryId,
                                'amount_per_unit' => $value,
                                'marketplace_amount_per_unit' => $value,
                                'currency_id' => $model->currency_code,
                                'marketplace_currency_id' => $model->currency_code,
                                'marketplace_currency_rate' => 1,
                            ];
                            break;
                        }
                    }
                }
            }

            if (!$model->validate()) {
                foreach ($model->getErrors() as $attribute => $errors) {
                    foreach ($errors as $error) {
                        $importError = new ImportError();
                        $importError->lines = [$k];
                        $importError->field = $attribute;
                        $importError->errorUntranslated = $error;
                        $importResult->errors[] = $importError;
                    }
                }
                $importResult->countLinesWithErrors++;
                unset($dataToImport[$k]);
                continue;
            }

            // Other values will be auto calculated during saving of product cost periods
            $model->save(false, ['is_enabled_sync_with_repricer']);
            if ($data['tags_add'] !== null) {

                $existingTags = ProductTag::find()
                        ->select('tag_id')
                        ->where(['product_id' => $model->id])
                        ->column() ?? [];

                $validTags = Tag::find()
                    ->select('id')
                    ->where(['id' => $data['tags_add']])
                    ->column();

                $uniqueTags = array_unique(array_merge($existingTags, $validTags));

                if (count($uniqueTags) > 30) {
                    $importError = new ImportError();
                    $importError->lines = [$k];
                    $importError->field = 'tags_add';
                    $importError->errorUntranslated = \Yii::t('import_export', "Tags limit exceeded");
                    $importResult->errors[] = $importError;
                    $importResult->countLinesWithErrors++;
                    unset($dataToImport[$k]);
                    continue;
                }

                $this->saveTags($validTags, $model->id, $existingTags);
            }
            if ($data['tags_remove'] !== null) {
                $this->removeTags($data['tags_remove'], $model->id);
            }
            $this->COGSynchronizer->createProductCostPeriods($productCostItemsToCreate, ProductCostCategory::SOURCE_MANUAL);
            $importResult->countImported++;
        }

        return $importResult;
    }

    public function getExampleData(): array
    {
        $availableMarketplaceIds = Product::find()->select('marketplace_id')->distinct()->column();
        $availableMarketplaceTitles = AmazonMarketplace::find()->select('title')
            ->where(['in', 'id', $availableMarketplaceIds])
            ->distinct()
            ->column();

        $availableSellerIds = Product::find()->select('seller_id')->distinct()->column();

        $columns = [
            'item_sku' => [
                'title' => \Yii::t('import_export', 'SKU (Stock Keeping Unit)'),
                'description' => \Yii::t('import_export', 'An unambiguous knowledge of the product assigned by the retailer. The SKU must be unambiguous for each product listed.'),
                'format' => \Yii::t('import_export', 'An alphanumeric string with a min. length of one and a max. length of 16 characters.'),
                'isRequired' => true,
                'group' => 'common',
            ],
            'marketplace' => [
                'title' => \Yii::t('import_export', 'Marketplace'),
                'description' => \Yii::t('import_export', 'Unique marketplace ID. If only one Amazon marketplace is used, the value will already be pre-filled. If several marketplaces are used, a selection is available.'),
                'isRequired' => true,
                'possibleValues' => $availableMarketplaceTitles,
                'group' => 'common',
            ],
            'seller_id' => [
                'title' => \Yii::t('import_export', 'Seller ID'),
                'description' => \Yii::t('import_export', 'Unique seller ID. If only one Amazon account is used, the value will already be pre-filled. If multiple accounts are used, a selection is available.'),
                'isRequired' => true,
                'possibleValues' => $availableSellerIds,
                'group' => 'common',
            ],
            'condition' => [
                'title' => \Yii::t('import_export', 'Condition'),
                'description' => \Yii::t('import_export', 'Condition of the product.'),
                'isRequired' => false,
                'possibleValues' => [
                    \Yii::t('import_export_const', 'New'),
                    \Yii::t('import_export_const', 'Used; Like New'),
                    \Yii::t('import_export_const', 'Used; Very Good'),
                    \Yii::t('import_export_const', 'Used; Good'),
                    \Yii::t('import_export_const', 'Used; Acceptable'),
                    \Yii::t('import_export_const', 'Collectible; Like New'),
                    \Yii::t('import_export_const', 'Collectible; Very Good'),
                    \Yii::t('import_export_const', 'Collectible; Good'),
                    \Yii::t('import_export_const', 'Collectible; Acceptable'),
                    \Yii::t('import_export_const', 'Used; Refurbished'),
                    \Yii::t('import_export_const', 'Refurbished'),
                ],
                'group' => 'uneditable',
            ],
            'fulfillment_method' => [
                'title' => \Yii::t('import_export', 'Fulfillment method'),
                'description' => \Yii::t('import_export', 'Fulfillment method of the product. Can be Fulfilled by Amazon or Fulfilled by Merchant.'),
                'isRequired' => false,
                'possibleValues' => [
                    \Yii::t('import_export_const', 'FBA'),
                    \Yii::t('import_export_const', 'FBM')
                ],
                'group' => 'uneditable',
            ],
            'asin' => [
                'title' => \Yii::t('import_export', 'ASIN'),
                'description' => \Yii::t('import_export', 'Amazon Standart Identification Number. A unique product identifier assigned by Amazon.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'An alphanumeric string with a length of 10 characters.'),
                'group' => 'uneditable',
            ],
            'title' => [
                'title' => \Yii::t('import_export', 'Title'),
                'description' => \Yii::t('import_export', 'The title of the product.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'An alphanumeric string.'),
                'group' => 'uneditable',
            ],
            'synchronize_with_repricer' => [
                'title' => \Yii::t('import_export', 'Synchronize with Repricer'),
                'description' => \Yii::t('import_export', "Transfer costs data from Repricer to Business Analytics. Once the account is connected in Repricer, the costs data will be synchronized. Repricer subscription is required for the synchronization.\n\nIf you wish the current costs to be updated manually only, disable synchronization with Repricer."),
                'isRequired' => false,
                'possibleValues' => [
                    \Yii::t('import_export_const', Constants::YES),
                    \Yii::t('import_export_const', Constants::NO)
                ],
                'group' => 'advanced',
            ]
        ];

        $dataColumns = [
            'item_sku' => '123456-XYZ',
            'marketplace' => 'Amazon DE',
            'seller_id' => 'A11B07LXF9Q06B',
            'condition' => \Yii::t('import_export_const', 'Used; Very Good'),
            'fulfillment_method' => \Yii::t('import_export_const', 'FBM'),
            'asin' => 'B07N1TKW9B',
            'title' => 'Formuler Z8 Ultimate 4K UHD Android OTT 5G',
            'synchronize_with_repricer' => \Yii::t('import_export_const', Constants::YES)
        ];

        [$columnsCostOfGoods, $dataCostOfGoods] = $this->generateColumnsCostOfGoods();
        [$columnsOtherFees, $dataOtherFees] = $this->generateColumnsOtherFees();
        [$columnsShippingCosts, $dataShippingCosts] = $this->generateColumnsShippingCosts();

        $columnsVat = [
            'vat_start_date' => [
                'title' => \Yii::t('import_export', 'VAT, Start date'),
                'description' => \Yii::t('import_export', 'The start date of the “VAT” period. Please note that if no date is set, the cost amount will be assigned to the current date - the date when the import file is uploaded.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'A date in the following format: YYYY-MM-DD'),
                'group' => 'advanced',
            ],
            'vat' => [
                'title' => \Yii::t('import_export', 'VAT'),
                'description' => \Yii::t('import_export', 'Valid VAT rate related to the product.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ]
        ];

        $dataVat = [
            'vat_start_date' => '2023-11-23',
            'vat' => 19.5
        ];

        $columns = array_merge($columns, $columnsCostOfGoods, $columnsOtherFees, $columnsShippingCosts, $columnsVat);

        $dataColumns = array_merge($dataColumns, $dataCostOfGoods, $dataOtherFees, $dataShippingCosts, $dataVat);

        $data = [$dataColumns];

        return [
            'groups' => [
                'common' => [
                    'title' => \Yii::t('import_export', 'Basic product information'),
                    'colorRgb' => 'ff0611',
                ],
                'uneditable' => [
                    'title' => \Yii::t('import_export', 'Uneditable product information. Export only'),
                    'colorRgb' => 'ffff3c',
                ],
                'advanced' => [
                    'title' => \Yii::t('import_export', 'Advanced product information'),
                    'colorRgb' => '92d05b',
                ],
            ],
            'columns' => $columns,
            'data' => $data,
        ];
    }

    protected function toFloatString(string $value): string
    {
        $value = preg_replace('/\s/', '', $value);
        return str_replace(',', '.', $value);
    }

    protected function isAlreadyProcessed(
        int $dataImportId,
        string $marketplaceId,
        string $sellerId,
        string $sellerSku
    ): bool
    {
        $cacheKey = $this->getIsAlreadyProcessedCacheKey($dataImportId, $marketplaceId, $sellerId, $sellerSku);
        return !empty($this->cache->get($cacheKey));
    }

    protected function markAsAlreadyProcessed(
        int $dataImportId,
        string $marketplaceId,
        string $sellerId,
        string $sellerSku
    ): void
    {
        $cacheKey = $this->getIsAlreadyProcessedCacheKey($dataImportId, $marketplaceId, $sellerId, $sellerSku);
        $this->cache->set($cacheKey, 1, 60 * 5);
    }

    protected function getIsAlreadyProcessedCacheKey(
        int $dataImportId,
        string $marketplaceId,
        string $sellerId,
        string $sellerSku
    ): string
    {
        return implode('_', [
            'already_processed_rows',
            $this->dbManager->getCustomerId(),
            $dataImportId,
            $marketplaceId,
            $sellerId,
            $sellerSku
        ]);
    }

    private function generateColumnsCostOfGoods(): array
    {
        $columnsCostOfGoods = [
            'cost_of_goods_start_date' => [
                'title' => \Yii::t('import_export', 'Cost of Goods, Start date'),
                'description' => \Yii::t('import_export', 'The start date of the “Cost of goods” period that applies to all "Cost of goods" subcategories. Only one cost period can be created per unique SKU. Please note that if no date is set, the cost amount will be assigned to the current date - the date when the import file is uploaded.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'A date in the following format: YYYY-MM-DD'),
                'group' => 'advanced',
            ],
            'cost_of_goods_net_purchase_price' => [
                'title' => \Yii::t('import_export', 'Net purchase price'),
                'description' => \Yii::t('import_export', 'Net purchase price applied to a product as a part of its cost of goods.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ],
            'cost_of_goods_customs_duty' => [
                'title' => \Yii::t('import_export', 'Customs duty'),
                'description' => \Yii::t('import_export', 'Customs duty applied to a product as a part of its cost of goods.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ],
            'cost_of_goods_inbound_shipment' => [
                'title' => \Yii::t('import_export', 'Inbound shipment'),
                'description' => \Yii::t('import_export', 'Inbound shipment applied to a product as a part of its cost of goods.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ],
        ];

        $dataCostOfGoods = [
            'cost_of_goods_start_date' => '2023-12-25',
            'cost_of_goods_net_purchase_price' => 4.95,
            'cost_of_goods_customs_duty' => 5.25,
            'cost_of_goods_inbound_shipment' => 4.95,
        ];

        return $this->generateColumns('cost_of_goods', $columnsCostOfGoods, $dataCostOfGoods, 6.5, '2023-09-20');
    }

    private function generateColumnsOtherFees(): array
    {
        $columnsOtherFees = [
            'other_fees_start_date' => [
                'title' => \Yii::t('import_export', 'Other fees, Start date'),
                'description' => \Yii::t('import_export', 'The start date of the “Other fees” period that applies to all "Other fees" subcategories. Only one cost period can be created per unique SKU. Please note that if no date is set, the fee amount will be assigned to the current date - the date when the import file is uploaded.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'A date in the following format: YYYY-MM-DD'),
                'group' => 'advanced',
            ],
            'other_fees' => [
                'title' => \Yii::t('import_export', 'Other fees'),
                'description' => \Yii::t('import_export', 'Other charges such as packaging material etc. can be accommodated here..'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ]
        ];

        $dataOtherFees = [
            'other_fees_start_date' => '2023-05-28',
            'other_fees' => 12.5
        ];

        return $this->generateColumns('other_fees', $columnsOtherFees, $dataOtherFees, 12.5, '2023-05-28');
    }

    private function generateColumnsShippingCosts(): array
    {
        $columnsShippingCosts = [
            'shipping_costs_start_date' => [
                'title' => \Yii::t('import_export', 'FBM shipping costs, Start date'),
                'description' => \Yii::t('import_export', 'The start date of the “FBM shipping costs” period that applies to all "FBM shipping costs" subcategories. Only one cost period can be created per unique SKU. Please note that if no date is set, the cost amount will be assigned to the current date - the date when the import file is uploaded.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', 'A date in the following format: YYYY-MM-DD'),
                'group' => 'advanced',
            ],
            'shipping_costs' => [
                'title' => \Yii::t('import_export', 'FBM shipping costs'),
                'description' => \Yii::t('import_export', 'Net shipping costs are necessary shipping the goods.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ]
        ];
        $dataShippingCosts = [
            'shipping_costs_start_date' => '2023-04-17',
            'shipping_costs' => 3.55,
        ];

        return $this->generateColumns('shipping_costs', $columnsShippingCosts, $dataShippingCosts, 7.25, '2023-05-19');
    }

    private function generateColumns(string $salesCategoryId, array $columns, array $data, float $defaultValue, string $defaultDate): array
    {
        $customCosts = ProductCostCategory::find()
            ->select(['name'])
            ->where([
                'sales_category_id' => $salesCategoryId,
                'source' => ProductCostCategory::SOURCE_MANUAL,
                'is_editable' => true
            ])->column();

        foreach ($customCosts as $customNameSubCategory) {
            $keyColumn = $salesCategoryId . '_' . Inflector::slug($customNameSubCategory, '_');
            $columns[$keyColumn] = [
                'title' => $customNameSubCategory,
                'description' => \Yii::t('import_export', 'The custom-created subcategory of Cost of goods applied to the product.'),
                'isRequired' => false,
                'format' => \Yii::t('import_export', "An integer or number with a maximum of 10 digits and 2 decimal places. Use a comma to separate the decimal places.\n / Exclude.\nExcluding the cost subcategory removes it from the total cost period."),
                'group' => 'advanced',
            ];

            $data[$keyColumn] = $defaultValue;
        }

        return [$columns, $data];
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function onAfterImport(): void
    {
        try {
            $manager = new DynamicTablesManager();
            $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
            $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());

            $this
                ->dbManager->getClickhouseCustomerDb()
                ->createCommand("OPTIMIZE TABLE " . \common\models\customer\TransactionExtendedViewV1::tableName())
                ->execute();
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    private function saveTags(array $tags, $id, array $existingTags)
    {
        foreach ($tags as $tagId) {
            if (!in_array($tagId, $existingTags)) {
                $productTag = new ProductTag();
                $productTag->product_id = $id;
                $productTag->tag_id = $tagId;
                $productTag->save();
            }
        }
    }

    private function removeTags($tags, $id)
    {
        ProductTag::deleteAll(['product_id' => $id, 'tag_id' => $tags]);
    }
}
