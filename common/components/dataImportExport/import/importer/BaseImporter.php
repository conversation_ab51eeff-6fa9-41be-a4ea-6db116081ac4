<?php

namespace common\components\dataImportExport\import\importer;

use common\models\Message;
use common\models\MessageTranslation;
use yii\db\Query;

abstract class BaseImporter
{
    public function toOriginalLanguage(string $phrase): string
    {
        $originalPhrase = (new Query())
            ->select('m.message')
            ->from(Message::tableName() . ' m')
            ->leftJoin(MessageTranslation::tableName() . ' mt', 'm.id = mt.id')
            ->where([
                'AND',
                ['=', 'm.category', 'import_export_const'],
                ['=', 'LOWER(mt.translation)', strtolower($phrase)]
            ])
            ->scalar();

        return $originalPhrase ?: $phrase;
    }
}
