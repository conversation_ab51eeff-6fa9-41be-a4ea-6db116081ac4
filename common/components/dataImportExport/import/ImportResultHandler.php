<?php


namespace common\components\dataImportExport\import;

use common\components\dataImportExport\import\importer\ImporterFactory;
use common\models\customer\DataImport;
use common\models\customer\DataImportPart;

/**
 * Handles import result, any kind of post processing.
 */
class ImportResultHandler
{
    /**
     * @var ImportErrorCollapser
     */
    private $importErrorCollapser;

    private ImporterFactory $importerFactory;

    /**
     * ImportResultHandler constructor.
     */
    public function __construct()
    {
        $this->importErrorCollapser = new ImportErrorCollapser();
        $this->importerFactory = new ImporterFactory();
    }

    /**
     * @param ImportResult   $importResult
     * @param DataImportPart $dataImportPart
     */
    public function handle(ImportResult $importResult, DataImportPart $dataImportPart): void
    {
        /** @var DataImport $dataImport */
        $dataImport = $dataImportPart->getDataImport()->one();

        $dataImportPart->count_imported_items = $importResult->countImported;
        $dataImportPart->count_errors = $importResult->countLinesWithErrors;
        $dataImportPart->errors = $this
            ->importErrorCollapser
            ->collapse(
                $importResult->errors,
                $dataImport->data_start_line_number + $dataImportPart->offset
            );

        $dataImportPart->setFinished();
        $dataImportPart->save(false);

        $dataImport->count_imported_items += $importResult->countImported;
        $dataImport->count_errors += $importResult->countLinesWithErrors;
        $dataImport->errors = $this
            ->importErrorCollapser
            ->collapse(
                array_merge(
                    $dataImport->errors,
                    $dataImportPart->errors
                )
            );
        $dataImport->save(false);

        $this->finishDataImportIfNeed($dataImport);
    }

    private function finishDataImportIfNeed(DataImport $dataImport): void
    {
        $countNotFinishedParts = $dataImport
            ->getDataImportParts()
            ->where(['not', ['status' => DataImportPart::STATUS_FINISHED]])
            ->count();

        if ($countNotFinishedParts > 0) {
            return;
        }
        $importer = $this->importerFactory->getImporter($dataImport->handler_name);
        $importer->onAfterImport();

        $dataImport->refresh();
        $dataImport->setFinished();
        $dataImport->save(false);
        $dataImport->recalculateStats();
    }
}
