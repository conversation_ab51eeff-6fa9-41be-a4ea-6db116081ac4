<?php

namespace common\components\dataImportExport\import;

/**
 * Groups (collapses) array of errors by lines of occurrence.
 */
class ImportErrorCollapser
{
    /**
     * Max number of lines can be saved for single error.
     * Necessary to prevent data overflowing in case when errors too many.
     */
    public const MAX_LINES_TO_PRESERVE = 100;

    /**
     * @param ImportError[] $dataImportErrors
     * @param int           $lineNumberOffset Line number offset which should be taken into account
     *
     * @return ImportError[]
     */
    public function collapse(array $dataImportErrors, int $lineNumberOffset = 0): array
    {
        /** @var ImportError[] $collapsed */
        $collapsed = [];

        foreach ($dataImportErrors as $importError) {
            if (is_array($importError)) {
                $importError = (object)$importError;
            }

            $errorFieldName = $importError->errorUntranslated ?? $importError->error;
            $groupingKey = md5(json_encode([$importError->field, $errorFieldName]));

            if (empty($collapsed[$groupingKey])) {
                $collapsed[$groupingKey] = new ImportError();
                $collapsed[$groupingKey]->count = 0;
                $collapsed[$groupingKey]->lines = [];
                $collapsed[$groupingKey]->error = $importError->error;
                $collapsed[$groupingKey]->errorUntranslated = $importError->errorUntranslated ?? null;
                $collapsed[$groupingKey]->field = $importError->field;
            }

            $collapsed[$groupingKey]->lines = array_merge($collapsed[$groupingKey]->lines, $importError->lines);
            sort($collapsed[$groupingKey]->lines);
            $collapsed[$groupingKey]->count += $importError->count;
        }

        // Cutting off lines to allowed limit
        foreach ($collapsed as $importError) {
            $importError->lines = array_slice(
                $importError->lines,
                0,
                self::MAX_LINES_TO_PRESERVE
            );

            array_walk($importError->lines, function (&$lineNumber) use ($lineNumberOffset) {
                $lineNumber += $lineNumberOffset;
            });
        }

        return array_values($collapsed);
    }
}
