<?php

namespace common\components\dataImportExport\import\processor;

use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\bulkEdit\product\BulkEditPartProcessor;
use common\components\dataImportExport\bulkEdit\product\ProductBulkEditPartCreator;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\MessageInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\DataImport;
use common\models\customer\DataImportBulkEditPart;
use common\models\customer\DataImportPart;

class BulkEditProcessor implements DataImportProcessorInterface
{
    use LogToConsoleTrait;

    protected MessagesSender $messagesSender;
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->messagesSender = new MessagesSender();
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getMaxInProgressParts(): int
    {
        return 20;
    }

    public function getCountInProgressParts(): int
    {
        return DataImportBulkEditPart::find()
            ->where([
                'AND',
                ['status' => [
                    DataImport::STATUS_IN_PROGRESS,
                    DataImport::STATUS_QUEUED
                ]]
            ])->count();
    }

    public function splitIntoParts(DataImport $dataImport): void
    {
        (new ProductBulkEditPartCreator($dataImport))->createParts();
    }

    public function processPart(int $dataImportPartId): void
    {
        /** @var DataImportBulkEditPart $bulkEditPart */
        $bulkEditPart = DataImportBulkEditPart::find()
            ->where(['id' => $dataImportPartId])
            ->andWhere(['status' => DataImport::STATUS_QUEUED])
            ->one();

        if (empty($bulkEditPart)) {
            return;
        }

        $partProcessor = (new BulkEditPartProcessor($bulkEditPart));
        try {
            $partProcessor->process();
        } catch (\Throwable $e) {
            $bulkEditPart->exception($e->getMessage());
            $bulkEditPart->setFinished();
            $this->error($e);
        }
    }

    public function enqueuePartProcessing(int $limit = 1): void
    {
        /** @var DataImportBulkEditPart[] $readyDataImportParts */
        $readyDataImportParts = DataImportBulkEditPart::find()
            ->where([
                'status' => DataImport::STATUS_NEW
            ])
            ->orderBy('id ASC')
            ->limit($limit)
            ->all();

        if (empty($readyDataImportParts)) {
            $this->info("No data import parts to start");
            return;
        }

        foreach ($readyDataImportParts as $dataImportPart) {
            try {
                $dataImportPart->setQueued();

                $message = [
                    'data_import_part_id' => $dataImportPart->id,
                    'customer_id' => $this->dbManager->getCustomerId(),
                    'data_import_processor_type' => DataImportProcessorFactory::PROCESSOR_TYPE_BULK_EDIT
                ];
                $this->info($message);

                $this->messagesSender->publish(
                    $message,
                    MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
                    'process-part-bulk-edit'
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function renewStuckParts(): void
    {
        /** @var DataImportBulkEditPart[] $stuckParts */
        $stuckParts = DataImportBulkEditPart::find()->where([
            'AND',
            ['in', 'status', [DataImport::STATUS_IN_PROGRESS, DataImport::STATUS_QUEUED]],
            ['<=', 'updated_at', (new \DateTime())->modify('-30 minutes')->format('Y-m-d H:i:s')]
        ])->all();

        foreach ($stuckParts as $stuckPart) {
            try {
                $bulkEditPart = clone $stuckPart;
                unset($bulkEditPart->id);
                $bulkEditPart->last_processed_id = 0;
                $bulkEditPart->count_all_items = 0;
                $bulkEditPart->count_imported_items = 0;
                $bulkEditPart->count_errors = 0;
                $bulkEditPart->errors = null;
                $bulkEditPart->status = DataImport::STATUS_NEW;
                $bulkEditPart->started_at = date('Y-m-d H:i:s');
                $bulkEditPart->finished_at = null;
                $bulkEditPart->updated_at = date('Y-m-d H:i:s');
                $bulkEditPart->isNewRecord = true;
                $bulkEditPart->log('Renewed terminated');
                $bulkEditPart->save(false);

                $stuckPart->delete();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }
}