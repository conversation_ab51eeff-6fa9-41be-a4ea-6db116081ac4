<?php

namespace common\components\dataImportExport\import\processor;

use common\models\customer\DataImport;

interface DataImportProcessorInterface
{
    public function getMaxInProgressParts(): int;

    public function getCountInProgressParts(): int;

    public function splitIntoParts(DataImport $dataImport): void;

    public function processPart(int $dataImportPartId): void;

    public function enqueuePartProcessing(int $limit = 1): void;

    public function renewStuckParts(): void;
}