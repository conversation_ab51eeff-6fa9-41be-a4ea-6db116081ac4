<?php

namespace common\components\dataImportExport\import\processor;

use common\models\customer\DataImport;

class DataImportProcessorFactory
{
    public const PROCESSOR_TYPE_BULK_EDIT = 'bulk_edit';
    public const PROCESSOR_TYPE_IMPORT_THROUGH_FILE = 'import_through_file';

    /**
     * @return DataImportProcessorInterface[]
     * @throws \Exception
     */
    public function getAllProcessors(): array
    {
        return [
            $this->getProcessor(self::PROCESSOR_TYPE_IMPORT_THROUGH_FILE),
            $this->getProcessor(self::PROCESSOR_TYPE_BULK_EDIT),
        ];
    }

    public function getProcessor(string $type): DataImportProcessorInterface
    {
        switch ($type) {
            case self::PROCESSOR_TYPE_BULK_EDIT:
                return new BulkEditProcessor();
            case self::PROCESSOR_TYPE_IMPORT_THROUGH_FILE:
                return new ImportThroughFileProcessor();
        }

        throw new \Exception("Unable to determine data import processor by type '$type'");
    }

    public function getProcessorByImportType(string $importType): DataImportProcessorInterface
    {
        switch ($importType) {
            case DataImport::TYPE_BULK_EDIT:
                return new BulkEditProcessor();
            case DataImport::TYPE_AUTO:
            case DataImport::TYPE_MANUAL:
                return new ImportThroughFileProcessor();
        }

        throw new \Exception("Unable to determine data import processor by import type '$importType'");
    }
}