<?php

namespace common\components\dataImportExport\import\processor;

use common\components\core\db\dbManager\DbManager;
use common\components\core\i18n\I18N;
use common\components\dataImportExport\fileSplitter\FileSplitter;
use common\components\dataImportExport\fileSplitter\SplittedFile;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\dataImportExport\import\ImportManager;
use common\components\dataImportExport\import\ImportResult;
use common\components\dataImportExport\import\ImportResultHandler;
use common\components\dataImportExport\rotator\RotatorFactory;
use common\components\fileDataReader\DataReaderFactory;
use common\components\fileDataWriter\DataWriterFactory;
use common\components\fileDataWriter\DataWriterInterface;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\MessageInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\DataImport;
use common\models\customer\DataImportPart;
use Symfony\Component\Filesystem\Filesystem;
use yii\db\Expression;

class ImportThroughFileProcessor implements DataImportProcessorInterface
{
    use LogToConsoleTrait;

    private Filesystem $filesystem;
    private DataReaderFactory $dataReaderFactory;
    private DataWriterFactory $dataWriterFactory;
    private ImporterFactory $importerFactory;
    private RotatorFactory $rotatorFactory;
    private DbManager $dbManager;
    private ImportManager $importManager;
    private DataImportProcessorFactory $dataImportProcessorFactory;
    private ImportResultHandler $importResultHandler;
    private MessagesSender $messagesSender;

    /**
     * SplitIntoPartsConsumer constructor.
     */
    public function __construct()
    {
        $this->filesystem = new Filesystem();
        $this->importerFactory = new ImporterFactory();
        $this->dataReaderFactory = new DataReaderFactory();
        $this->dataWriterFactory = new DataWriterFactory();
        $this->dbManager = \Yii::$app->dbManager;
        $this->rotatorFactory = new RotatorFactory();
        $this->importManager = new ImportManager();
        $this->dataImportProcessorFactory = new DataImportProcessorFactory();
        $this->importResultHandler = new ImportResultHandler();
        $this->messagesSender = new MessagesSender();
    }

    public function getMaxInProgressParts(): int
    {
        return 20;
    }

    public function getCountInProgressParts(): int
    {
        return DataImportPart::find()
            ->where([
                'AND',
                ['status' => [
                    DataImport::STATUS_IN_PROGRESS,
                    DataImport::STATUS_QUEUED
                ]]
            ])->count();
    }

    public function enqueuePartProcessing(int $limit = 1): void
    {
        /** @var DataImportPart[] $readyDataImportParts */
        $readyDataImportParts = DataImportPart::find()
            ->where([
                'status' => DataImport::STATUS_NEW
            ])
            ->orderBy('id ASC')
            ->limit($limit)
            ->all();

        if (empty($readyDataImportParts)) {
            $this->info("No data import parts to start");
            return;
        }

        foreach ($readyDataImportParts as $dataImportPart) {
            try {
                $dataImportPart->setQueued();

                $message = [
                    'data_import_part_id' => $dataImportPart->id,
                    'customer_id' => $this->dbManager->getCustomerId(),
                    'data_import_processor_type' => DataImportProcessorFactory::PROCESSOR_TYPE_IMPORT_THROUGH_FILE
                ];
                $this->info($message);

                $this->messagesSender->publish(
                    $message,
                    MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
                    'process-part'
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function processPart(int $dataImportPartId): void
    {
        /** @var DataImportPart $dataImportPart */
        $dataImportPart = DataImportPart::findOne($dataImportPartId);

        if (null === $dataImportPart) {
            return;
        }

        if ($dataImportPart->status !== DataImportPart::STATUS_QUEUED) {
            return;
        }

        $importResult = new ImportResult();

        if (empty($dataImportPart->file_url)) {
            throw new \Exception("Empty file url in data import part");
        }

        $tmpFilePath = \Yii::getAlias('@runtime/import-export/part_processing') . '/' . uniqid('', true);
        $languagePrev = \Yii::$app->language;

        try {
            /** @var DataImport $dataImport */
            $dataImport = $dataImportPart->getDataImport()->one();
            \Yii::$app->language = 'en';

            $dataImport->setInProgress();
            $dataImportPart->setInProgress();

            $this->info('Started downloading of source file ' . $dataImportPart->file_url);
            $this->downloadFileUsingFileClient($dataImportPart->file_url, $tmpFilePath);
            exec("iconv -c -f UTF-8 -t UTF-8 $tmpFilePath > $tmpFilePath.utf8");
            unlink($tmpFilePath);
            $tmpFilePath = "$tmpFilePath.utf8";

            $this->info('Source file has been downloaded into ' . $tmpFilePath);

            $importer = $this->importerFactory->getImporter($dataImport->handler_name);
            $dataReader = $this->dataReaderFactory->getDataReaderByPath($tmpFilePath);

            $this->info('Reading data from file');
            $dataToImport = $dataReader->getData($tmpFilePath, PHP_INT_MAX, 0, $importer->getRequiredFields());

            $this->info([
                'handlerName' => $dataImport->handler_name,
                'dataReaderClass' => get_class($dataReader),
                'importerClass' => get_class($importer),
                'countDataToImport' => count($dataToImport),
            ]);

            $this->info('Importing data');

            /** @var I18N $i18n */
            $i18n = \Yii::$app->i18n;
            $i18n->isFutureTranslationMode = true;
            $importResult = $importer->import($dataToImport, $dataImport->id);
            $i18n->isFutureTranslationMode = false;
        } catch (\Throwable $e) {
            $this->error($e);
            $dataImportPart->setException($e->getMessage());
            $dataImportPart->setFinished();
            $dataImportPart->save(false);
        } finally {
            $this->filesystem->remove($tmpFilePath);
        }

        $this->info('Handling import result');
        $this->importResultHandler->handle($importResult, $dataImportPart);
        \Yii::$app->language = $languagePrev;

        $this->info('Part processing has been finished!');
    }

    public function splitIntoParts(DataImport $dataImport): void
    {
        $dataWriter = null;
        $importer = null;
        $fileSplitter = null;

        $tmpFilePath = \Yii::getAlias('@runtime/import-export/downloaded_source') . '/' . md5($dataImport->file_url);

        try {
            $this->info('Started downloading of source file');
            $this->downloadFileUsingFileClient($dataImport->file_url, $tmpFilePath);
            $this->info('Source file has been downloaded into ' . $tmpFilePath);

            $dataReader = $this->dataReaderFactory->getDataReaderByPath($tmpFilePath);
            $dataWriter = $this->dataWriterFactory->getDataWriter(DataWriterFactory::DATA_FORMAT_CSV);
            $fileSplitter = new FileSplitter($dataReader, $dataWriter);
            $importer = $this->importerFactory->getImporter($dataImport->handler_name);

            $this->info('Started splitting file');
            $splittedFile = $fileSplitter->split(
                $tmpFilePath,
                $importer->getItemsPerPart(),
                $importer->getRequiredFields()
            );

            $dataImport->count_all_items = $splittedFile->countItems;
            $dataImport->count_parts = count($splittedFile->parts);
            $dataImport->data_start_line_number = $dataReader
                ->getStartDataLineNumber($tmpFilePath, $importer->getRequiredFields());
            $dataImport->save(false);

            $this->info('Finished spitting file');
            $this->info([
                'count_all_items' => $dataImport->count_all_items,
                'count_parts' => $dataImport->count_parts,
            ]);

            $this->saveSplittedParts($splittedFile, $dataWriter, $dataImport);
        } catch (\Throwable $e) {
            $this->filesystem->remove($tmpFilePath);
            unset($dataWriter, $importer, $fileSplitter);

            throw $e;
        }

        $this->info('Rotating data (removing old data imports)');
        $rotator = $this->rotatorFactory->getRotator(RotatorFactory::TYPE_IMPORT);
        $rotator->rotate();
        $this->info('Splitting has been finished!');
    }

    /**
     * Download file using FileClient component instead of direct filesystem copy.
     * This provides unified access to HTTP, FTP, SFTP, and S3-backed SFTP files.
     *
     * @param string $fileUrl Source file URL
     * @param string $localPath Local destination path
     * @throws \Exception If download fails
     */
    private function downloadFileUsingFileClient(string $fileUrl, string $localPath): void
    {
        // Create file client from URL
        $client = \Yii::$app->fileClient->createFromUrl($fileUrl);

        // Check if file exists
        if (!$client->exists()) {
            throw new \Exception("File not found: $fileUrl");
        }

        // Download file directly to local path
        $success = $client->downloadToFile($localPath);

        if (!$success) {
            throw new \Exception("Failed to download file from: $fileUrl");
        }

        $this->info("Successfully downloaded file from $fileUrl to $localPath");
    }

    public function renewStuckParts(): void
    {
        /** @var DataImportPart[] $stuckParts */
        $stuckParts = DataImportPart::find()->where([
            'AND',
            ['in', 'status', [DataImport::STATUS_IN_PROGRESS, DataImport::STATUS_QUEUED]],
            ['<=', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
        ])->all();

        foreach ($stuckParts as $stuckPart) {
            try {
                $dataImportPart = clone $stuckPart;
                unset($dataImportPart->id);
                $dataImportPart->data_start_line_number = 0;
                $dataImportPart->count_all_items = 0;
                $dataImportPart->count_imported_items = 0;
                $dataImportPart->count_errors = 0;
                $dataImportPart->errors = null;
                $dataImportPart->status = DataImport::STATUS_NEW;
                $dataImportPart->started_at = date('Y-m-d H:i:s');
                $dataImportPart->finished_at = null;
                $dataImportPart->updated_at = date('Y-m-d H:i:s');
                $dataImportPart->isNewRecord = true;
                $dataImportPart->log('Renewed terminated');
                $dataImportPart->save(false);

                $stuckPart->delete();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function renewStuckImport(): void
    {
        /** @var DataImport[] $stuckImports */
        $stuckImports = DataImport::find()
            ->alias('di')
            ->where([
                'AND',
                ['in', 'di.status', [DataImport::STATUS_IN_PROGRESS]],
            ])
            ->leftJoin(DataImportPart::tableName() . ' dip', 'di.id = dip.data_import_id')
            ->groupBy('di.id')
            ->having([
                '=',
                new Expression("SUM(
                    CASE
                        WHEN dip.status IN ('" . DataImport::STATUS_FINISHED . "', '" . DataImport::STATUS_NO_ITEMS . "')
                        THEN 1
                        ELSE 0
                    END
                )"),
                new Expression("di.count_parts")
            ])
            ->all()
        ;

        foreach ($stuckImports as $stuckImport) {
            try {
                $stuckImport->log("Attempt to finish stack import (all parts was done)");
                $stuckImport->save(false);

                $this->importResultHandler->finishDataImportIfNeed($stuckImport);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    /**
     * Saves spitted parts into database and remote file storage.
     *
     * @param  SplittedFile                 $splittedFile
     * @param  DataWriterInterface          $dataWriter
     * @param  DataImport                   $dataImport
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    private function saveSplittedParts(
        SplittedFile $splittedFile,
        DataWriterInterface $dataWriter,
        DataImport $dataImport
    ): void {
        $this->info('Started saving file parts');

        foreach ($splittedFile->parts as $splittedFilePart) {
            $dataImportPart = new DataImportPart();
            $dataImportPart->status = DataImportPart::STATUS_NEW;
            $dataImportPart->log('Created');
            $dataImportPart->data_import_id = $dataImport->id;
            $dataImportPart->count_all_items = $splittedFilePart->countItems;
            $dataImportPart->part_no = $splittedFilePart->partNumber;
            $dataImportPart->offset = $splittedFilePart->offset;
            $dataImportPart->save(false);

            try {
                $dataImportPart->uploadFileToStorage($splittedFilePart->filePath, $dataWriter->getExtension());
                $dataImportPart->save(false);
            } catch (\Throwable $e) {
                $this->error($e);
                $dataImportPart->exception = (string)$e;
                $dataImportPart->setFinished();
                $dataImportPart->save(false);
            }
        }
        $this->info('Finished saving file parts');
    }
}
