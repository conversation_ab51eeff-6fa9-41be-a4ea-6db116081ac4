<?php

namespace common\components\dataImportExport\import\templateGenerator;

/**
 * PHP Excel doesn't allow set auto height for rows (at least I haven't found working solution).
 * So, this helper allows to adjust size of all rows on the sheet.
 */
class RowsHeightAdjuster
{
    /**
     * Hand calculated parameter of char average height.
     */
    private const CHAR_AVG_HEIGHT = 1.2;

    /**
     * Hand calculated parameter of char average length.
     */
    private const CHAR_AVG_LENGTH = 15;

    /**
     * Hand calculated parameter of row minimum height which doesn't disappear characters.
     */
    private const MIN_ROW_HEIGHT = 15;

    /**
     * @var \PHPExcel_Worksheet
     */
    private $sheet;

    /**
     * @param \PHPExcel_Worksheet $sheet
     */
    public function __construct(\PHPExcel_Worksheet $sheet)
    {
        $this->sheet = $sheet;
    }

    /**
     * See description of class.
     */
    public function adjust(): void
    {
        $totalRows = $this->sheet->getHighestRow();
        $totalColumns = \PHPExcel_Cell::columnIndexFromString($this->sheet->getHighestColumn());

        for ($rowNumber = 1; $rowNumber <= $totalRows; $rowNumber++) {
            // Max number of text rows in this row (taking into account all columns).
            $maxNumberOfRows = 1;

            for ($columnNumber = 0; $columnNumber < $totalColumns; $columnNumber++) {
                $value = $this->sheet
                    ->getCellByColumnAndRow($columnNumber, $rowNumber)
                    ->getValue();

                $width = $this->sheet->getColumnDimensionByColumn($columnNumber)->getWidth();
                $numberOfChars = strlen($value);

                $textLength = $numberOfChars * self::CHAR_AVG_LENGTH;
                $numberOfRows = ($textLength / $width);
                $maxNumberOfRows = max($maxNumberOfRows, $numberOfRows);
            }

            $rowHeight = max(self::MIN_ROW_HEIGHT, $maxNumberOfRows * self::CHAR_AVG_HEIGHT);
            $this->sheet->getRowDimension($rowNumber)->setRowHeight($rowHeight);
        }
    }
}
