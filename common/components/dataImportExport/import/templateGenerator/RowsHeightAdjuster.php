<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator;

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

/**
 * PhpSpreadsheet doesn't auto-fit row height reliably for wrapped text,
 * so this helper adjusts row heights heuristically.
 */
class RowsHeightAdjuster
{
    /** Hand-calculated average char height (heuristic). */
    private const float CHAR_AVG_HEIGHT = 1.2;

    /** Hand-calculated average char width in "column width" units (heuristic). */
    private const int CHAR_AVG_LENGTH = 15;

    /** Minimal safe row height so text doesn't get clipped. */
    private const int MIN_ROW_HEIGHT = 15;

    public function __construct(private readonly Worksheet $worksheet)
    {
    }

    public function adjust(): void
    {
        $totalRows    = $this->worksheet->getHighestRow();
        $totalColumns = Coordinate::columnIndexFromString($this->worksheet->getHighestColumn());

        for ($row = 1; $row <= $totalRows; ++$row) {
            $maxLines = 1.0;

            for ($colIndex = 1; $colIndex <= $totalColumns; ++$colIndex) {
                $cell   = $this->worksheet->getCellByColumnAndRow($colIndex, $row);
                $value  = (string) $cell->getValue();

                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $width     = (float) $this->worksheet->getColumnDimension($colLetter)->getWidth();

                if ($width <= 0.0) {
                    $width = 1.0;
                }

                $numChars    = mb_strlen($value);
                $textLength  = $numChars * self::CHAR_AVG_LENGTH;
                $linesNeeded = $textLength / $width;

                if ($linesNeeded > $maxLines) {
                    $maxLines = $linesNeeded;
                }
            }

            $rowHeight = max(self::MIN_ROW_HEIGHT, $maxLines * self::CHAR_AVG_HEIGHT);
            $this->worksheet->getRowDimension($row)->setRowHeight($rowHeight);
        }
    }
}
