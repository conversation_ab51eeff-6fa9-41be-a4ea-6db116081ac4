<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

abstract class AbstractSheet implements SheetFillerInterface
{
    protected const GREY_COLOR = 'c0c0c0';
    protected const RED_COLOR = 'ff0000';
    protected const DEFAULT_BG_COLOR = 'ff0000';
    protected const DEFAULT_GROUP = 'default';
    protected const DEFAULT_CELL_WIDTH = 20;

    protected $currentRow = 1;
    protected $sheet;

    protected $columnsInfo = [];
    protected $exampleValues = [];
    protected $groupsInfo = [];

    public function __construct(\PHPExcel_Worksheet $sheet, array $columnsInfo, array $exampleValues, array $groupsInfo = [])
    {
        $this->sheet = $sheet;
        $this->columnsInfo = $columnsInfo;
        $this->groupsInfo = $groupsInfo;
        $this->exampleValues = $exampleValues;

        if (empty($this->groupsInfo)) {
            $this->groupsInfo = [
                self::DEFAULT_GROUP => [
                    'colorRgb' => self::RED_COLOR,
                ],
            ];
        }
    }

    protected function isAnyGroupSpecified(): bool
    {
        return count($this->groupsInfo) > 1 || array_keys($this->groupsInfo)[0] !== self::DEFAULT_GROUP;
    }
}
