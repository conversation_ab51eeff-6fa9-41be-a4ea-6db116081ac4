<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

abstract class AbstractSheet implements SheetFillerInterface
{
    protected const GREY_COLOR = 'c0c0c0';

    protected const RED_COLOR = 'ff0000';

    protected const DEFAULT_BG_COLOR = 'ff0000';

    protected const DEFAULT_GROUP = 'default';

    protected const DEFAULT_CELL_WIDTH = 20;

    protected $currentRow = 1;

    protected $groupsInfo = [];

    public function __construct(
        protected Worksheet $sheet,
        protected array $columnsInfo,
        protected array $exampleValues,
        array $groupsInfo = []
    ) {
        $this->groupsInfo = $groupsInfo ?: [
            self::DEFAULT_GROUP => [
                'colorRgb' => self::RED_COLOR,
            ],
        ];
    }

    protected function isAnyGroupSpecified(): bool
    {
        return \count($this->groupsInfo) > 1 || \array_key_first($this->groupsInfo) !== self::DEFAULT_GROUP;
    }
}
