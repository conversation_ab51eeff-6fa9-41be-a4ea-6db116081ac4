<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use common\components\dataImportExport\import\templateGenerator\RowsHeightAdjuster;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class DataDefinitionsSheet extends AbstractSheet
{
    #[\Override]
    public function fill(): void
    {
        $this->fillTitle();
        $this->fillInfoColumnTitles();
        $this->fillInfoDividedByGroups();

        $rowsHeightAdjuster = new RowsHeightAdjuster($this->sheet);
        $rowsHeightAdjuster->adjust();
    }

    private function fillTitle(): void
    {
        // Set worksheet title and header
        $this->sheet->setTitle(\Yii::t('import_export', 'Data definitions'));

        // First column is 1-based
        $this->sheet
            ->getCellByColumnAndRow(1, $this->currentRow)
            ->setValue(\Yii::t('import_export', 'Instruction for filling out'))
            ->getStyle()
            ->applyFromArray([
                'font' => [
                    'size' => 20,
                ],
            ]);

        $this->sheet->getRowDimension($this->currentRow)->setRowHeight(25);
        ++$this->currentRow;
    }

    private function fillInfoColumnTitles(): void
    {
        // Hardcoded structure for 100% design similarity
        $columnsData = [
            [
                'title' => $this->isAnyGroupSpecified()
                    ? \Yii::t('import_export', 'Group Name')
                    : '',
                'width' => 15,
            ], [
                'title' => \Yii::t('import_export', 'Parameter'),
                'width' => 20,
            ], [
                'title' => \Yii::t('import_export', 'Designation'),
                'width' => 40,
            ], [
                'title' => \Yii::t('import_export', "Definition and application"),
                'width' => 60,
            ], [
                'title' => \Yii::t('import_export', 'Valid values'),
                'width' => 60,
            ], [
                'title' => \Yii::t('import_export', 'Example'),
                'width' => 20,
            ], [
                'title' => \Yii::t('import_export', 'Required?'),
                'width' => 10,
            ],
        ];

        foreach ($columnsData as $col0 => $columnData) {
            $col1   = $col0 + 1; // 1-based index
            $letter = Coordinate::stringFromColumnIndex($col1);

            // Set column width
            $this->sheet->getColumnDimension($letter)->setWidth($columnData['width']);

            // Set alignment
            $this->sheet
                ->getStyle($letter . $this->currentRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setWrapText(true);

            // Set cell value and style
            $this->sheet
                ->getCellByColumnAndRow($col1, $this->currentRow)
                ->setValue($columnData['title']);

            $this->sheet
                ->getStyle($letter . $this->currentRow)
                ->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                    'fill' => [
                        'type'  => Fill::FILL_SOLID,
                        'color' => ['rgb' => self::GREY_COLOR],
                    ],
                    'borders' => [
                        'outline' => [
                            'style' => Border::BORDER_THIN,
                        ],
                    ],
                ]);
        }

        ++$this->currentRow;
    }

    public function fillInfoDividedByGroups(): void
    {
        foreach ($this->groupsInfo as $groupId => $groupInfo) {
            $groupBgColor = $groupInfo['colorRgb'] ?? self::DEFAULT_BG_COLOR;

            if (!$this->isAnyGroupSpecified()) {
                $this->fillGroupInfo($groupId, $groupBgColor);
                continue;
            }

            // Group title row
            for ($col0 = 0; $col0 < 7; ++$col0) {
                $col1   = $col0 + 1;
                $letter = Coordinate::stringFromColumnIndex($col1);

                $value = $col0 === 0 ? ($groupInfo['title'] ?? '') : '';

                $this->sheet
                    ->getCellByColumnAndRow($col1, $this->currentRow)
                    ->setValue($value);

                $this->sheet
                    ->getStyle($letter . $this->currentRow)
                    ->applyFromArray([
                        'font' => [
                            'bold' => true,
                        ],
                        'fill' => [
                            'type'  => Fill::FILL_SOLID,
                            'color' => ['rgb' => $groupBgColor],
                        ],
                        'borders' => [
                            'outline' => [
                                'style' => Border::BORDER_THIN,
                            ],
                        ],
                    ]);
            }

            ++$this->currentRow;

            $this->fillGroupInfo($groupId, $groupBgColor);
        }
    }

    private function fillGroupInfo(string $groupId, string $groupBgColor): void
    {
        foreach ($this->columnsInfo as $columnName => $columnInfo) {
            $columnGroupId = $columnInfo['group'] ?? self::DEFAULT_GROUP;

            // Skip columns belonging to another group
            if ($columnGroupId !== $groupId) {
                continue;
            }

            $format = $columnInfo['format'] ?? null;

            // If no format specified, try to build from possible values
            if (empty($format) && !empty($columnInfo['possibleValues'])) {
                $columnInfo['possibleValues'] = \array_slice($columnInfo['possibleValues'], 0, 100);
                $format = \implode(' / ', $columnInfo['possibleValues']);
            }

            $isRequired = isset($columnInfo['isRequired']) ? (bool) $columnInfo['isRequired'] : true;

            $rowValues = [
                $columnName,
                $columnInfo['title'] ?? '',
                $columnInfo['description'] ?? '',
                $format,
                $columnInfo['example'] ?? ($this->exampleValues[0][$columnName] ?? null),
                $isRequired ? \Yii::t('import_export', 'Required') : \Yii::t('import_export', 'Optional'),
            ];

            // Colorize first column if group is not default
            if ($columnGroupId !== self::DEFAULT_GROUP) {
                $this->sheet
                    ->getCellByColumnAndRow(1, $this->currentRow)
                    ->getStyle()
                    ->applyFromArray([
                        'fill' => [
                            'type'  => Fill::FILL_SOLID,
                            'color' => ['rgb' => $groupBgColor],
                        ],
                    ]);
            }

            // Fill row values
            foreach ($rowValues as $col0 => $value) {
                $col1   = $col0 + ($this->isAnyGroupSpecified() ? 2 : 1); // shift if group column exists
                $letter = Coordinate::stringFromColumnIndex($col1);

                $formatStyle = [
                    'borders' => [
                        'outline' => [
                            'style' => Border::BORDER_THIN,
                        ],
                    ],
                ];

                // Mark required with red font
                if ($isRequired && $col0 === \count($rowValues) - 1) {
                    $formatStyle['font'] = [
                        'color' => ['rgb' => self::RED_COLOR],
                    ];
                }

                $this->sheet
                    ->getCellByColumnAndRow($col1, $this->currentRow)
                    ->setValue($value);

                $this->sheet
                    ->getStyle($letter . $this->currentRow)
                    ->applyFromArray($formatStyle)
                    ->getAlignment()
                    ->setWrapText(true)
                    ->setVertical(Alignment::VERTICAL_TOP)
                    ->setHorizontal(Alignment::HORIZONTAL_LEFT);
            }

            ++$this->currentRow;
        }
    }
}
