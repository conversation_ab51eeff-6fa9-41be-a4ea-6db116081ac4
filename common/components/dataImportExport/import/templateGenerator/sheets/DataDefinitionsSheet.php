<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use common\components\dataImportExport\import\templateGenerator\RowsHeightAdjuster;

class DataDefinitionsSheet extends AbstractSheet
{
    public function fill(): void
    {
        $this->fillTitle();
        $this->fillInfoColumnTitles();
        $this->fillInfoDividedByGroups();
        $rowsHeightAdjuster = new RowsHeightAdjuster($this->sheet);
        $rowsHeightAdjuster->adjust();
    }

    private function fillTitle(): void
    {
        $this->sheet->setTitle(\Yii::t('import_export', 'Data definitions'));
        $this->sheet
            ->getCellByColumnAndRow(0, $this->currentRow)
            ->setValue(\Yii::t('import_export', 'Instruction for filling out'))
            ->getStyle()
            ->applyFromArray([
                'font' => [
                    'size' => 20,
                ],
            ]);
        $this->sheet->getRowDimension(1)->setRowHeight(25);
        $this->currentRow++;
    }

    private function fillInfoColumnTitles(): void
    {
        // Hardcode.. Yes, because we need 100% similarity with design, but unfortunately excel is not a css.
        $columnsData = [
            [
                'title' => $this->isAnyGroupSpecified()
                    ? \Yii::t('import_export', 'Group Name')
                    : '',
                'width' => 15,
            ], [
                'title' => \Yii::t('import_export', 'Parameter'),
                'width' => 20,
            ], [
                'title' => \Yii::t('import_export', 'Designation'),
                'width' => 40,
            ], [
                'title' => \Yii::t('import_export', "Definition and application"),
                'width' => 60,
            ], [
                'title' => \Yii::t('import_export', 'Valid values'),
                'width' => 60,
            ], [
                'title' => \Yii::t('import_export', 'Example'),
                'width' => 20,
            ], [
                'title' => \Yii::t('import_export', 'Required?'),
                'width' => 10,
            ],
        ];

        foreach ($columnsData as $columnNumber => $columnData) {
            $this->sheet->getColumnDimensionByColumn($columnNumber)->setWidth($columnData['width']);
            $this->sheet
                ->getCellByColumnAndRow($columnNumber, $this->currentRow)
                ->getStyle()
                ->getAlignment()
                ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT)
                ->setWrapText(true)
            ;

            $this->sheet
                ->getCellByColumnAndRow($columnNumber, $this->currentRow)
                ->setValue($columnData['title'])
                ->getStyle()
                ->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                    'fill' => [
                        'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                        'color' => ['rgb' => self::GREY_COLOR],
                    ],
                    'borders' => [
                        'outline' => [
                            'style' => \PHPExcel_Style_Border::BORDER_THIN,
                        ],
                    ],
                ]);
        }

        $this->currentRow++;
    }

    public function fillInfoDividedByGroups(): void
    {
        foreach ($this->groupsInfo as $groupId => $groupInfo) {
            $groupBgColor = $groupInfo['colorRgb'] ?? self::DEFAULT_BG_COLOR;

            if (!$this->isAnyGroupSpecified()) {
                $this->fillGroupInfo($groupId, $groupBgColor);
                continue;
            }

            for ($columnNumber = 0; $columnNumber < 7; $columnNumber++) {
                $value = $columnNumber === 0
                    ? $groupInfo['title']
                    : '';

                $this->sheet
                    ->getCellByColumnAndRow($columnNumber, $this->currentRow)
                    ->setValue($value)
                    ->getStyle()
                    ->applyFromArray([
                        'font' => [
                            'bold' => true,
                        ],
                        'fill' => [
                            'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                            'color' => ['rgb' => $groupBgColor],
                        ],
                        'borders' => [
                            'outline' => [
                                'style' => \PHPExcel_Style_Border::BORDER_THIN,
                            ],
                        ],
                    ]);
            }
            $this->currentRow++;

            $this->fillGroupInfo($groupId, $groupBgColor);
        }
    }

    private function fillGroupInfo(string $groupId, string $groupBgColor)
    {
        foreach ($this->columnsInfo as $columnName => $columnInfo) {
            $columnGroupId = $columnInfo['group'] ?? self::DEFAULT_GROUP;

            // This column relates to another group and will be show later
            if ($columnGroupId !== $groupId) {
                continue;
            }

            $format = $columnInfo['format'] ?? null;

            if (empty($format) && !empty($columnInfo['possibleValues'])) {
                $columnInfo['possibleValues'] = array_slice($columnInfo['possibleValues'], 0, 100);
                $format = implode(' / ', $columnInfo['possibleValues']);
            }

            $isRequired = true;
            if (isset($columnInfo['isRequired'])) {
                $isRequired = (bool)$columnInfo['isRequired'];
            }

            $rowValues = [
                $columnName,
                $columnInfo['title'] ?? '',
                $columnInfo['description'] ?? '',
                $format,
                $columnInfo['example'] ?? $this->exampleValues[0][$columnName] ?? null,
                $isRequired ? \Yii::t('import_export', 'Required') : \Yii::t('import_export', 'Optional'),
            ];

            if ($columnGroupId !== self::DEFAULT_GROUP) {
                $this->sheet
                    ->getCellByColumnAndRow(0, $this->currentRow)
                    ->getStyle()
                    ->applyFromArray([
                        'fill' => [
                            'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                            'color' => ['rgb' => $groupBgColor],
                        ],
                    ]);
            }

            foreach ($rowValues as $columnNumber => $value) {
                $format = [
                    'borders' => [
                        'outline' => [
                            'style' => \PHPExcel_Style_Border::BORDER_THIN,
                        ],
                    ],
                ];

                if ($isRequired && $columnNumber === count($rowValues) - 1) {
                    $format['font'] = [
                        'color' => ['rgb' => self::RED_COLOR],
                    ];
                }

                $this->sheet
                    ->getCellByColumnAndRow($columnNumber + ($this->isAnyGroupSpecified() ? 1 : 0), $this->currentRow)
                    ->setValue($value)
                    ->getStyle()
                    ->applyFromArray($format)
                    ->getAlignment()
                    ->setWrapText(true)
                    ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_TOP)
                    ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT)
                ;
            }
            $this->currentRow++;
        }
    }
}
