<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

class ValidValuesSheet extends AbstractTemplateSheet
{
    public function fill(): void
    {
        $this->sheet->setTitle(\Yii::t('import_export', 'Valid values'));

        $this->drawTemplateInfo();
        $this->drawColumnNames();
        $this->drawValidValues();
    }

    private function drawValidValues(): void
    {
        $columns = array_keys($this->exampleValues[0]);
        foreach ($columns as $columnNumber => $columnName) {
            $possibleValues = $this->columnsInfo[$columnName]['possibleValues'] ?? [];
            if (empty($possibleValues)) {
                continue;
            }

            foreach ($possibleValues as $k => $possibleValue) {
                $this
                    ->sheet
                    ->getCellByColumnAndRow($columnNumber, $this->currentRow + $k)
                    ->setValue($possibleValue);
            }
        }
    }
}
