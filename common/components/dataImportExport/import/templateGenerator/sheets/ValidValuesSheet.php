<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

class ValidValuesSheet extends AbstractTemplateSheet
{
    public function fill(): void
    {
        $this->sheet->setTitle(\Yii::t('import_export', 'Valid values'));

        // Draw header/meta rows and column names
        $this->drawTemplateInfo();
        $this->drawColumnNames();
        $this->drawValidValues();
    }

    private function drawValidValues(): void
    {
        // Use the first example row to determine column order
        $columns = \array_keys($this->exampleValues[0] ?? []);

        foreach ($columns as $col0 => $columnName) {
            $possibleValues = $this->columnsInfo[$columnName]['possibleValues'] ?? [];
            if ($possibleValues === []) {
                continue;
            }

            // PhpSpreadsheet columns are 1-based
            $col1 = $col0 + 1;

            foreach (\array_values($possibleValues) as $k => $value) {
                $row = $this->currentRow + $k;

                $this->sheet
                    ->getCellByColumnAndRow($col1, $row)
                    ->setValue($value);
            }
        }
    }
}
