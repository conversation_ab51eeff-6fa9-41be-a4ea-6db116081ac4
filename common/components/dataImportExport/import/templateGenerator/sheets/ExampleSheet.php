<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

class ExampleSheet extends AbstractTemplateSheet
{
    #[\Override]
    public function fill(): void
    {
        // Set sheet title
        $this->sheet->setTitle(\Yii::t('import_export', 'Example'));

        // Draw header/meta rows and column names
        $this->drawTemplateInfo();
        $this->drawColumnNames();

        // Fill example data
        $this->drawExampleData();
    }

    private function drawExampleData(): void
    {
        // Use the first example row
        $exampleValues = \array_values($this->exampleValues[0] ?? []);

        foreach ($exampleValues as $col0 => $exampleValue) {
            // PhpSpreadsheet columns are 1-based
            $col1 = $col0 + 1;

            $this->sheet
                ->getCellByColumnAndRow($col1, $this->currentRow)
                ->setValue($exampleValue);
        }

        // Optionally advance to the next row after writing examples
        ++$this->currentRow;
    }
}
