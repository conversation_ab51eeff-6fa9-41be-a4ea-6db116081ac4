<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

class ExampleSheet extends AbstractTemplateSheet
{
    public function fill(): void
    {
        $this->sheet->setTitle(\Yii::t('import_export', 'Example'));

        $this->drawTemplateInfo();
        $this->drawColumnNames();
        $this->drawExampleData();
    }

    private function drawExampleData(): void
    {
        $exampleValues = array_values($this->exampleValues[0]);

        foreach ($exampleValues as $columnNumber => $exampleValue) {
            $this->sheet
                ->getCellByColumnAndRow($columnNumber, $this->currentRow)
                ->setValue($exampleValue)
            ;
        }
    }
}
