<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

class TemplateSheet extends AbstractTemplateSheet
{
    public const MAX_ROWS_WITH_DROPDOWN_FORMULAS = 500;

    public function fill(): void
    {
        $this->sheet->setTitle(\Yii::t('import_export', 'Template'));

        $this->drawTemplateInfo();
        $this->drawColumnNames();
        $this->drawPossibleValues();
    }

    private function drawPossibleValues(): void
    {
        $columns = array_keys($this->exampleValues[0]);

        foreach ($columns as $columnNumber => $columnName) {
            $possibleValues = $this->columnsInfo[$columnName]['possibleValues'] ?? [];
            if (empty($possibleValues)) {
                continue;
            }

            for ($i = 0; $i < self::MAX_ROWS_WITH_DROPDOWN_FORMULAS; $i++) {
                $this->sheet
                    ->getCellByColumnAndRow($columnNumber, $this->currentRow + $i)
                    ->getDataValidation()
                    ->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST)
                    ->setErrorStyle(\PHPExcel_Cell_DataValidation::STYLE_INFORMATION)
                    ->setShowDropDown(true)
                    ->setAllowBlank(true)
                    ->setShowInputMessage(true)
                    ->setShowErrorMessage(true)
                    ->setErrorTitle(\Yii::t('import_export', 'Input error'))
                    ->setError(\Yii::t('import_export', 'Value is not in list.'))
                    ->setPromptTitle(\Yii::t('import_export', 'Pick from list'))
                    ->setPrompt('')
                    ->setFormula1('"' . implode(',', $possibleValues) . '"');
            }
        }
    }
}
