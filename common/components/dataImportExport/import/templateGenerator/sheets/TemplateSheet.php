<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use PhpOffice\PhpSpreadsheet\Cell\DataValidation;

class TemplateSheet extends AbstractTemplateSheet
{
    public const MAX_ROWS_WITH_DROPDOWN_FORMULAS = 500;

    #[\Override]
    public function fill(): void
    {
        // Set worksheet title
        $this->sheet->setTitle(\Yii::t('import_export', 'Template'));

        // Draw header/meta rows and column names
        $this->drawTemplateInfo();
        $this->drawColumnNames();

        // Add dropdown validations for columns with possible values
        $this->drawPossibleValues();
    }

    /**
     * Adds data validation (dropdown list) for columns that define "possibleValues".
     * NOTE: Inline list breaks if values contain commas or quotes.
     *       For robust behavior, consider writing values to a hidden sheet and using a range formula instead.
     */
    private function drawPossibleValues(): void
    {
        $columns = \array_keys($this->exampleValues[0] ?? []);

        foreach ($columns as $col0 => $columnName) {
            $possibleValues = $this->columnsInfo[$columnName]['possibleValues'] ?? [];
            if ($possibleValues === []){
                continue;
            }

            // Build an inline list formula: "A,B,C"
            // Escape embedded quotes by doubling them
            $escaped = \array_map(
                static fn(string $v): string|array => \str_replace('"', '""', $v),
                \array_map('strval', $possibleValues)
            );
            $inlineList = '"' . \implode(',', $escaped) . '"';

            // PhpSpreadsheet uses 1-based column index
            $col1 = $col0 + 1;

            for ($i = 0; $i < self::MAX_ROWS_WITH_DROPDOWN_FORMULAS; ++$i) {
                $row = $this->currentRow + $i;

                $validation = $this->sheet
                    ->getCellByColumnAndRow($col1, $row)
                    ->getDataValidation();

                // Configure dropdown list validation
                $validation->setType(DataValidation::TYPE_LIST);
                $validation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                $validation->setAllowBlank(true);
                $validation->setShowDropDown(true);
                $validation->setShowInputMessage(true);
                $validation->setShowErrorMessage(true);
                $validation->setErrorTitle(\Yii::t('import_export', 'Input error'));
                $validation->setError(\Yii::t('import_export', 'Value is not in list.'));
                $validation->setPromptTitle(\Yii::t('import_export', 'Pick from list'));
                $validation->setPrompt('');
                $validation->setFormula1($inlineList);
            }
        }
    }
}
