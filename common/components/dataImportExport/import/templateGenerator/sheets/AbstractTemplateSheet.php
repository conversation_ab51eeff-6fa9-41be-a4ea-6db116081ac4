<?php

namespace common\components\dataImportExport\import\templateGenerator\sheets;

abstract class AbstractTemplateSheet extends AbstractSheet
{
    protected function getFirstGroupColor(): string
    {
        return array_values($this->groupsInfo)[0]['colorRgb'] ?? self::DEFAULT_BG_COLOR;
    }

    protected function drawTemplateInfo(): void
    {
        $columnsToFill = array_fill(0, count($this->exampleValues[0]), '');
        // This is not working yet, but we assume that we could use this features in future.
        $columnsToFill[0] = 'TemplateType=XXX';
        $columnsToFill[1] = 'Version=2017.0001';

        foreach ($columnsToFill as $columnNumber => $columnValue) {
            $this->sheet
                ->getColumnDimensionByColumn($columnNumber)
                ->setAutoSize(true)
            ;

            $this->sheet
                ->getCellByColumnAndRow($columnNumber, $this->currentRow)
                ->setValue($columnValue)
                ->getStyle()
                ->applyFromArray([
                    'fill' => [
                        'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                        'color' => ['rgb' => $this->getColorByColumnNumber($columnNumber)],
                    ],
                    'borders' => [
                        'outline' => [
                            'style' => \PHPExcel_Style_Border::BORDER_THIN,
                        ],
                    ],
                ])
            ;
        }

        $this->currentRow++;
    }

    protected function drawColumnNames(): void
    {
        $columns = array_keys($this->exampleValues[0]);

        foreach ($columns as $columnNumber => $columnName) {
            $columnTitle = $this->columnsInfo[$columnName]['title'] ?? \Yii::t('import_export', $columnName);
            $columnDimension = $this->sheet->getColumnDimensionByColumn($columnNumber);
            $columnDimension->setWidth(self::DEFAULT_CELL_WIDTH);

            foreach ([$columnTitle, $columnName] as $k => $columnValue) {
                $this
                    ->sheet
                    ->getCellByColumnAndRow($columnNumber, $this->currentRow + $k)
                    ->setValue($columnValue)
                    ->getStyle()
                    ->applyFromArray([
                        'fill' => [
                            'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                            'color' => ['rgb' => $this->getColorByColumnNumber($columnNumber)],
                        ],
                        'borders' => [
                            'outline' => [
                                'style' => \PHPExcel_Style_Border::BORDER_THIN,
                            ],
                        ],
                    ])
                    ->getAlignment()
                    ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)
                ;
            }
        }

        $this->currentRow += 2;
    }

    protected function getColorByColumnNumber(int $columnNumber): string
    {
        $groupName = array_values($this->columnsInfo)[$columnNumber]['group']
            ?? array_values($this->groupsInfo)[0];

        return $this->groupsInfo[$groupName]['colorRgb'] ?? self::GREY_COLOR;
    }
}
