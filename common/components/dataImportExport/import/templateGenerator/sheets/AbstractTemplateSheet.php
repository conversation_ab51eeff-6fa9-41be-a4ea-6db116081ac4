<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator\sheets;

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

abstract class AbstractTemplateSheet extends AbstractSheet
{
    protected function getFirstGroupColor(): string
    {
        $firstGroup = \array_key_first($this->groupsInfo) ?? self::DEFAULT_GROUP;
        return $this->groupsInfo[$firstGroup]['colorRgb'] ?? self::DEFAULT_BG_COLOR;
    }

    protected function drawTemplateInfo(): void
    {
        $firstRow = $this->exampleValues[0] ?? [];
        $columnsToFill = \array_fill(0, \count($firstRow), '');

        if ($columnsToFill !== []) {
            $columnsToFill[0] = 'TemplateType=XXX';
        }

        if (\count($columnsToFill) > 1) {
            $columnsToFill[1] = 'Version=2017.0001';
        }

        foreach ($columnsToFill as $col0 => $value) {
            $col1 = $col0 + 1; // 1-based PhpSpreadsheet
            $letter = Coordinate::stringFromColumnIndex($col1);

            $this->sheet
                ->getColumnDimension($letter)
                ->setAutoSize(true);

            $this->sheet
                ->getCellByColumnAndRow($col1, $this->currentRow)
                ->setValue($value);

            $this->sheet
                ->getStyle($letter . $this->currentRow)
                ->applyFromArray([
                    'fill' => [
                        'type'  => Fill::FILL_SOLID,
                        'color' => ['rgb' => $this->getColorByColumnNumber($col0)],
                    ],
                    'borders' => [
                        'outline' => [
                            'style' => Border::BORDER_THIN,
                        ],
                    ],
                ]);
        }

        ++$this->currentRow;
    }

    protected function drawColumnNames(): void
    {
        $firstRow = $this->exampleValues[0] ?? [];
        $columns  = \array_keys($firstRow);

        foreach ($columns as $col0 => $columnName) {
            $col1   = $col0 + 1; // 1-based
            $letter = Coordinate::stringFromColumnIndex($col1);

            $columnTitle = $this->columnsInfo[$columnName]['title'] ?? \Yii::t('import_export', $columnName);

            $this->sheet->getColumnDimension($letter)->setWidth(self::DEFAULT_CELL_WIDTH);

            foreach ([$columnTitle, $columnName] as $k => $value) {
                $row = $this->currentRow + $k;

                $this->sheet
                    ->getCellByColumnAndRow($col1, $row)
                    ->setValue($value);

                $this->sheet
                    ->getStyle($letter . $row)
                    ->applyFromArray([
                        'fill' => [
                            'type'  => Fill::FILL_SOLID,
                            'color' => ['rgb' => $this->getColorByColumnNumber($col0)],
                        ],
                        'borders' => [
                            'outline' => [
                                'style' => Border::BORDER_THIN,
                            ],
                        ],
                    ])
                    ->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);
            }
        }

        $this->currentRow += 2;
    }

    protected function getColorByColumnNumber(int $columnNumber): string
    {
        $groupName = $this->columnsInfo[\array_keys($this->columnsInfo)[$columnNumber] ?? '']['group']
            ?? (\array_key_first($this->groupsInfo) ?? self::DEFAULT_GROUP);

        return $this->groupsInfo[$groupName]['colorRgb'] ?? self::GREY_COLOR;
    }
}
