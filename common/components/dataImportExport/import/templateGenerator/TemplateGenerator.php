<?php

namespace common\components\dataImportExport\import\templateGenerator;

use common\components\dataImportExport\import\templateGenerator\sheets\DataDefinitionsSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\ExampleSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\TemplateSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\ValidValuesSheet;

class TemplateGenerator
{
    private $columnsInfo;
    private $exampleData;
    private $groupsInfo;

    /**
     * @param array $columnsInfo
     * @example
     * [
     *      'asin' => [
     *          'title' => 'ASIN',
     *          'description' => 'Amazon standard identification number',
     *          'format' => '10-character alphanumeric unique identifier',
     *          'isRequired' => false,
     *          'group' => 'firstGroupId'
     *      ],
     *      'sku' => [
     *          'title' => 'SKU',
     *          'description' => 'Stock keeping unit',
     *          'format' => 'An alphanumeric string with a min. length of one and a max. length of 16 characters.',
     *          'isRequired' => true,
     *          'group' => 'secondGroupId'
     *      ],
     * ]
     *
     * @param array $exampleData
     * @example
     * [
     *      [
     *          'asin' => 'B00PQY7TJA',
     *          'sku' => '123456-XYZ',
     *      ],
     *      [
     *          'asin' => 'G00PQY7THC',
     *          'sku' => '654321-ZYX'
     *      ],
     * ]
     *
     * @param array $groupsInfo
     * @example
     * [
     *      'firstGroupId' => [
     *          'title' => 'First group title'
     *          'colorRgb' => 'fff6e9',
     *      ],
     *      'secondGroupId' => [
     *          'title' => 'Second group title'
     *          'colorRgb' => 'ddeedd',
     *      ],
     * ]
     */
    public function __construct(array $columnsInfo, array $exampleData, array $groupsInfo = [])
    {
        $this->exampleData = $exampleData;
        $this->columnsInfo = $columnsInfo;
        $this->groupsInfo = $groupsInfo;
    }

    /**
     * Generates and returns xls object.
     *
     * @throws \PHPExcel_Exception
     * @return \PHPExcel
     */
    public function generate()
    {
        // There are a lot of notices in vendor library, we can not do anything with them
        $errorReportingTmp = error_reporting();
        error_reporting(E_ALL & ~E_NOTICE);

        $xls = new \PHPExcel();
        $xls->setActiveSheetIndex(0);

        $sheet = $xls->getActiveSheet();
        $dataDefinitionSheet = new DataDefinitionsSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $dataDefinitionSheet->fill();

        $sheet = $xls->createSheet();
        $templateSheet = new TemplateSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $templateSheet->fill();

        $sheet = $xls->createSheet();
        $templateSheet = new ExampleSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $templateSheet->fill();

        $sheet = $xls->createSheet();
        $validValuesSheet = new ValidValuesSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $validValuesSheet->fill();
        $xls->setActiveSheetIndex(1);
        error_reporting($errorReportingTmp);

        return $xls;
    }

    public function saveAsXlsx(\PHPExcel $xls, string $pathToSave): void
    {
        $objWriter = new \PHPExcel_Writer_Excel2007($xls);
        $objWriter->save($pathToSave);
    }
}
