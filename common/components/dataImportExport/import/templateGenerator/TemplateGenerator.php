<?php

declare(strict_types=1);

namespace common\components\dataImportExport\import\templateGenerator;

use PhpOffice\PhpSpreadsheet\Exception as SpreadsheetException;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use common\components\dataImportExport\import\templateGenerator\sheets\DataDefinitionsSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\ExampleSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\TemplateSheet;
use common\components\dataImportExport\import\templateGenerator\sheets\ValidValuesSheet;


class TemplateGenerator
{
    /**
     * @example
     * [
     *      'asin' => [
     *          'title' => 'ASIN',
     *          'description' => 'Amazon standard identification number',
     *          'format' => '10-character alphanumeric unique identifier',
     *          'isRequired' => false,
     *          'group' => 'firstGroupId'
     *      ],
     *      'sku' => [
     *          'title' => 'SKU',
     *          'description' => 'Stock keeping unit',
     *          'format' => 'An alphanumeric string with a min. length of one and a max. length of 16 characters.',
     *          'isRequired' => true,
     *          'group' => 'secondGroupId'
     *      ],
     * ]
     *
     * @example
     * [
     *      [
     *          'asin' => 'B00PQY7TJA',
     *          'sku' => '123456-XYZ',
     *      ],
     *      [
     *          'asin' => 'G00PQY7THC',
     *          'sku' => '654321-ZYX'
     *      ],
     * ]
     *
     * @example
     * [
     *      'firstGroupId' => [
     *          'title' => 'First group title'
     *          'colorRgb' => 'fff6e9',
     *      ],
     *      'secondGroupId' => [
     *          'title' => 'Second group title'
     *          'colorRgb' => 'ddeedd',
     *      ],
     * ]
     */
    public function __construct(
        private readonly array $columnsInfo,
        private readonly array $exampleData,
        private readonly array $groupsInfo = []
    ) {}

    /**
     * Generates and returns Spreadsheet object.
     *
     * @throws SpreadsheetException
     */
    public function generate(): Spreadsheet
    {
        $prevLevel = error_reporting();
        error_reporting(E_ALL & ~E_NOTICE);

        $spreadsheet = new Spreadsheet();
        $spreadsheet->setActiveSheetIndex(0);

        // Sheet 1: Data Definitions
        $sheet = $spreadsheet->getActiveSheet();
        $dataDefinitionsSheet = new DataDefinitionsSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $dataDefinitionsSheet->fill();

        // Sheet 2: Template
        $sheet = $spreadsheet->createSheet();
        $templateSheet = new TemplateSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $templateSheet->fill();

        // Sheet 3: Example
        $sheet = $spreadsheet->createSheet();
        $exampleSheet = new ExampleSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $exampleSheet->fill();

        // Sheet 4: Valid Values
        $sheet = $spreadsheet->createSheet();
        $validValuesSheet = new ValidValuesSheet(
            $sheet,
            $this->columnsInfo,
            $this->exampleData,
            $this->groupsInfo
        );
        $validValuesSheet->fill();

        $spreadsheet->setActiveSheetIndex(1);

        error_reporting($prevLevel);
        return $spreadsheet;
    }

    public function saveAsXlsx(Spreadsheet $spreadsheet, string $pathToSave): void
    {
        $xlsx = new Xlsx($spreadsheet);
        $xlsx->save($pathToSave);
    }
}
