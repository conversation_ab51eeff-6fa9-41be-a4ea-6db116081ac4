<?php

namespace common\components\dataImportExport\import\consumer;

use common\components\core\db\dbManager\DbManager;
use common\components\core\i18n\I18N;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\dataImportExport\import\ImportResult;
use common\components\dataImportExport\import\ImportResultHandler;
use common\components\dataImportExport\import\processor\DataImportProcessorFactory;
use common\components\fileDataReader\DataReaderFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\models\customer\DataImport;
use common\models\customer\DataImportPart;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Filesystem\Filesystem;
use Yii;
use yii\debug\models\search\Db;

/**
 * Downloads file part from remote storage and imports data from this part it into our storage.
 */
class ProcessPartConsumer extends BaseConsumer
{
    /**
     * @var Filesystem
     */
    private Filesystem $filesystem;

    /**
     * @var ImporterFactory
     */
    private ImporterFactory $importerFactory;

    /**
     * @var ImportResultHandler
     */
    private ImportResultHandler $importResultHandler;

    /**
     * @var DataReaderFactory
     */
    private DataReaderFactory $dataReaderFactory;

    private DbManager $dbManager;
    private DataImportProcessorFactory $dataImportProcessorFactory;

    /**
     * ProcessPartConsumer constructor.
     */
    public function __construct()
    {
        $this->filesystem = new Filesystem();
        $this->importResultHandler = new ImportResultHandler();
        $this->dataReaderFactory = new DataReaderFactory();
        $this->importerFactory = new ImporterFactory();
        $this->dataImportProcessorFactory = new DataImportProcessorFactory();
        /** @var DbManager dbManger */
        $this->dbManager = Yii::$app->dbManager;
    }

    /**
     * @param  AMQPMessage $msg
     * @return bool
     */
    public function __execute(AMQPMessage $msg)
    {
        DataImport::getDb()->enableSlaves = false;
        $this->info(str_repeat('-', 60));
        $this->info('Process part consumer');

        $this->info($msg->body);

        $customerId = $msg->body['customer_id'] ?? null;
        $dataImportPartId = $msg->body['data_import_part_id'] ?? null;
        $dataImportProcessorType = $msg->body['data_import_processor_type'] ?? null;

        if (null === $customerId || null === $dataImportPartId || empty($dataImportProcessorType)) {
            $this->error(new \Exception('Some required fields was not provided'));
            return ConsumerInterface::MSG_ACK;
        }

        $this->dbManager->setCustomerId($customerId);

        try {
            $dataImportProcessor = $this->dataImportProcessorFactory->getProcessor($dataImportProcessorType);
            $dataImportProcessor->processPart($dataImportPartId);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->info('Process part has been finished!');

        return self::MSG_ACK;
    }
}
