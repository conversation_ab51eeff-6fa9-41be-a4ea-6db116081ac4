<?php

namespace common\components\dataImportExport\import\consumer;

use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\import\ImportManager;
use common\components\dataImportExport\import\processor\DataImportProcessorFactory;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\models\customer\DataImport;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Filesystem\Filesystem;
use Yii;

/**
 * Downloads file with data from remote storage and splits it into smallest parts.
 * Uploads parts back to remote storage, enqueues tasks to process parts.
 */
class SplitIntoPartsConsumer extends BaseConsumer
{
    private DbManager $dbManager;
    private ImportManager $importManager;
    private DataImportProcessorFactory $dataImportProcessorFactory;

    /**
     * SplitIntoPartsConsumer constructor.
     */
    public function __construct()
    {
        $this->filesystem = new Filesystem();
        /** @var DbManager dbManger */
        $this->dbManager = Yii::$app->dbManager;
        $this->importManager = new ImportManager();
        $this->dataImportProcessorFactory = new DataImportProcessorFactory();
    }

    /**
     * @param  AMQPMessage $msg
     * @throws \Exception
     * @return int|mixed
     */
    public function __execute(AMQPMessage $msg)
    {
        DataImport::getDb()->enableSlaves = false;
        $this->info(str_repeat('-', 60));
        $this->info('Splitting into parts has been started');

        $this->info($msg->body);

        $customerId = $msg->body['customer_id'] ?? null;
        $dataImportId = $msg->body['data_import_id'] ?? null;

        if (null === $customerId || null === $dataImportId) {
            $this->error('Some required fields was not provided');
            return ConsumerInterface::MSG_REJECT;
        }

        $this->dbManager->setCustomerId($customerId);
        $dataImport = DataImport::findOne($dataImportId);
        Yii::$app->translation->setIsAllowHandleMissingInConsole(true);

        if (null === $dataImport) {
            return ConsumerInterface::MSG_ACK;
        }

        $countParts = $dataImport->getDataImportParts()->count();

        // Parts have neem already created
        if ($countParts > 0) {
            return ConsumerInterface::MSG_ACK;
        }

        $importProcessor = $this->dataImportProcessorFactory->getProcessorByImportType($dataImport->type);

        try {
            $importProcessor->splitIntoParts($dataImport);
            $this->importManager->enqueuePartProcessing();
        } catch (\Throwable $e) {
            $this->error($e);
            $dataImport->setException($e->getMessage());
            $dataImport->setFinished();
            $dataImport->save(false);
        }

        $this->info('Splitting has been finished!');

        return ConsumerInterface::MSG_ACK;
    }
}
