<?php

namespace common\components\dataImportExport\import;

use common\components\core\db\dbManager\DbManager;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\dataImportExport\import\processor\DataImportProcessorFactory;
use common\components\fileDataWriter\DataWriterFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\DataImport;
use mikemadisonweb\rabbitmq\components\Producer;

/**
 * Class ImportManager.
 */
class ImportManager
{
    use LogToConsoleTrait;

    private DataWriterFactory $dataWriterFactory;
    private ImporterFactory $importerFactory;
    private DbManager $dbManager;
    private MessagesSender $messagesSender;
    private DataImportProcessorFactory $dataImportProcessorFactory;

    /**
     * ImportManager constructor.
     */
    public function __construct()
    {
        $this->dataWriterFactory = new DataWriterFactory();
        $this->importerFactory = new ImporterFactory();
        $this->dbManager = \Yii::$app->dbManager;
        $this->messagesSender = new MessagesSender();
        $this->dataImportProcessorFactory = new DataImportProcessorFactory();
    }

    /**
     * Uploads file with import data to remote storage, enqueues tasks for splitting file into parts.
     */
    public function upload(string $filePath, string $handlerName, string $type = DataImport::TYPE_MANUAL): DataImport
    {
        $dataImport = new DataImport();

        try {
            $dataImport->type = $type;
            $dataImport->handler_name = $handlerName;
            $dataImport->status = DataImport::STATUS_NEW;
            $dataImport->log('Created');
            $dataImport->language_code = \Yii::$app->language;
            $dataImport->save(false);

            $importer = $this->importerFactory->getImporter($handlerName);
            try {
                $dataWriter = $this->dataWriterFactory->getDataWriterByPath($filePath);
            } catch (\Throwable $e) {
                $dataWriter = $this->dataWriterFactory->getDataWriter(DataWriterFactory::DATA_FORMAT_CSV);
            }
            $importFileName = implode('_', [
                $importer->getImportFilePrefix(),
                'import',
                date('Y-m-d_H-i-s'),
            ]);
            $dataImport->uploadFileToStorage(
                $filePath,
                $dataWriter->getExtension(),
                $dataWriter->getMimeType(),
                $importFileName
            );

            $dataImport->save(false);

            $this->enqueueSplittingIntoParts($dataImport);
        } catch (\Throwable $e) {
            $dataImport->delete();
            throw $e;
        }

        return $dataImport;
    }

    public function save(string $fileUrl, string $handlerName, string $type = DataImport::TYPE_MANUAL): DataImport
    {
        $dataImport = new DataImport();
        try {
            $dataImport->type = $type;
            $dataImport->handler_name = $handlerName;
            $dataImport->file_url = $fileUrl;
            $dataImport->status = DataImport::STATUS_NEW;
            $dataImport->log('Created');
            $dataImport->language_code = \Yii::$app->language;
            $dataImport->save(false);
            $this->enqueueSplittingIntoParts($dataImport);
        } catch (\Throwable $e) {
            $dataImport->delete();
            throw $e;
        }

        return $dataImport;
    }

    public function enqueuePartProcessing(): void
    {
        $this->info('Enqueue import part processing started ' . $this->dbManager->getCustomerId());

        foreach ($this->dataImportProcessorFactory->getAllProcessors() as $importProcessor) {
            $maxInProgressParts = $importProcessor->getMaxInProgressParts();
            $countInProgressParts = $importProcessor->getCountInprogressParts();
            $countPartsToBeQueued = $maxInProgressParts - $countInProgressParts;

            $this->info([
                'importProcessor' => get_class($importProcessor),
                'maxInProgressParts' => $maxInProgressParts,
                'countInProgressParts' => $countInProgressParts,
                'countPartsToBeQueued' => $countPartsToBeQueued,
            ]);

            if ($countPartsToBeQueued <= 0) {
                continue;
            }

            $importProcessor->enqueuePartProcessing($countPartsToBeQueued);
        }
    }

    public function enqueueSplittingIntoParts(DataImport $dataImport): void
    {
        /** @var Producer $producer */
        $producer = \Yii::$app->rabbitmq->getProducer(MessageAbstract::PRODUCER_NAME);
        $producer->publish([
                'data_import_id' => $dataImport->id,
                'customer_id' => $this->dbManager->getCustomerId(),
            ],
            MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
            'split-into-parts'
        );
    }
}
