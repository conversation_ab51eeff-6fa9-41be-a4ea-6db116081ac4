<?php


namespace common\components\dataImportExport\import\validator;

use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\fileDataReader\DataReaderFactory;
use yii\validators\Validator;
use yii\web\UploadedFile;

/**
 * Superficial validation of import file provided by user.
 */
class ImportFileStructureValidator extends Validator
{
    /**
     * {@inheritdoc}
     */
    public function validateAttribute($model, $attribute) : bool
    {
        /** @var UploadedFile $uploadedFile */
        $uploadedFile = $model->file ?? null;
        $handlerName =  $model->handler_name ?? null;
        if (null === $uploadedFile || !$uploadedFile instanceof UploadedFile) {
            return true;
        }

        $importerFactory = new ImporterFactory();
        $dataReaderFactory = new DataReaderFactory();

        $filePath = $uploadedFile->tempName;

        try {
            $importer = $importerFactory->getImporter($handlerName);
        } catch (\Throwable $e) {
            return false;
        }

        try {
            $dataReader = $dataReaderFactory->getDataReaderByPath($filePath);
        } catch (\Throwable $e) {
            $this->addError(
                $model,
                $attribute,
                \Yii::t(
                    'admin',
                    'Can not read such type of file'
                )
            );

            return false;
        }

        $requiredFields = $importer->getRequiredFields();
        $firstItem = $dataReader->getData($filePath, 1, 0, $requiredFields)[0] ?? [];

        if (empty($firstItem)) {
            $this->addError(
                $model,
                $attribute,
                \Yii::t(
                    'admin',
                    'Import file should contain at least one item for import.'
                )
            );

            return false;
        }

        $providedFields = array_keys($firstItem);
        $missingFields = array_diff($requiredFields, $providedFields);

        if (count($missingFields) === 0) {
            return true;
        }

        $this->addError(
            $model,
            $attribute,
            \Yii::t(
                'admin',
                'Unable to find required fields "{missing_fields}" in data structure of the file',
                [
                    'missing_fields' => implode(', ', $missingFields),
                ]
            )
        );

        return false;
    }
}
