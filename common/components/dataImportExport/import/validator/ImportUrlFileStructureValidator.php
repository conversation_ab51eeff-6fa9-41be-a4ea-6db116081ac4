<?php

namespace common\components\dataImportExport\import\validator;

use yii\base\DynamicModel;
use yii\validators\Validator;
use yii\web\UploadedFile;

/**
 * Reads first block of file from url and checks its structure,required fields.
 */
class ImportUrlFileStructureValidator extends Validator
{
    /**
     * {@inheritdoc}
     */
    public function validateAttribute($model, $attribute) : bool
    {
        $firstLinesOfData = [];
        $contextOpts = [
            "http" => [
                'method' => 'GET',
                'max_redirects' => 3,
            ],
        ];
        $urlFieldName = isset($model->url) ? 'url' : 'file_url';

        $authLogin = $model->auth_login ?? null;
        $authPassword = $model->auth_password ?? null;

        if (!empty($authLogin) && !empty($authPassword)) {
            $auth = base64_encode("{$authLogin}:{$authPassword}");
            $contextOpts['http']['header'] = "Authorization: Basic $auth";
        }

        $context = stream_context_create($contextOpts);

        try {
            $stream = fopen($model->{$urlFieldName}, 'r', false, $context);
        } catch (\Throwable $e) {
            if (false !== strpos($e->getMessage(), 'Unauthorized')) {
                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Basic authorization, access denied. Please, recheck specified access credentials')
                );
            } else {
                $reason = '';

                if (false !== strpos($e->getMessage(), 'Not Found')) {
                    $reason = '(404 Not Found)';
                } elseif (false !== strpos($e->getMessage(), 'Forbidden')) {
                    $reason = '(403 Forbidden)';
                }

                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Unable to open specified URL {0}', [$reason])
                );
            }
            return false;
        }

        if ($stream) {
            $firstLinesOfData = stream_get_contents($stream, 20000);
            fclose($stream);
        }

        $tmpFilePath = sys_get_temp_dir() . '/' . md5(random_bytes(10)) . '.csv';
        file_put_contents($tmpFilePath, $firstLinesOfData);

        $uploadedFile = new UploadedFile([
            'tempName' => $tmpFilePath,
            'type' => 'text/csv',
        ]);

        $dynamicModel = new DynamicModel([
            'file' => $uploadedFile,
            'handler_name' => $model->handler_name,
        ]);
        $dynamicModel->addRule(['file'], ImportFileStructureValidator::class);
        $dynamicModel->validate();
        unlink($tmpFilePath);

        $dynamicModelErrors = $dynamicModel->getErrors();

        if (count($dynamicModelErrors) === 0) {
            return false;
        }

        foreach ($dynamicModelErrors as $field => $errors) {
            if ($field === 'file') {
                $field = $urlFieldName;
            }

            foreach ($errors as $error) {
                $this->addError($model, $field, $error);
            }
        }

        return true;
    }
}
