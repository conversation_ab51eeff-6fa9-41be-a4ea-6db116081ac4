<?php

namespace common\components\dataImportExport\import\validator;

use common\models\validators\SftpUrlValidator;
use yii\base\DynamicModel;
use yii\validators\Validator;
use yii\web\UploadedFile;

/**
 * Reads first block of file from url and checks its structure,required fields.
 */
class ImportUrlFileStructureValidator extends Validator
{
    /**
     * {@inheritdoc}
     */
    public function validateAttribute($model, $attribute) : bool
    {
        $urlFieldName = isset($model->url) ? 'url' : 'file_url';
        $url = $model->{$urlFieldName};

        // Validate URL with our custom validator
        $urlValidator = new SftpUrlValidator(['validSchemes' => ['http', 'https', 'ftp', 'sftp']]);
        if (!$urlValidator->validate($url)) {
            $this->addError($model, $attribute, 'URL is not valid.');
            return false;
        }

        $authLogin = $model->auth_login ?? null;
        $authPassword = $model->auth_password ?? null;
        $method = $model->method ?? 'GET';
        $maxRedirects = $model->max_redirects ?? 5;

        // Determine which credentials to use
        $username = null;
        $password = null;

        if (!empty($authLogin)) {
            // If explicit auth login is provided, use it (password can be empty)
            $username = $authLogin;
            $password = $authPassword; // Can be empty string
        }
        // If no explicit auth login, let FileClient use embedded credentials from URL (if any)

        try {
            // Use FileClient component for unified file access
            $client = \Yii::$app->fileClient->createFromUrl($url, $username, $password, [
                'method' => $method,
                'max_redirects' => $maxRedirects,
            ]);

            // Check if file exists
            if (!$client->exists()) {
                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Unable to open specified URL (File not found)')
                );
                return false;
            }

            // Get file stream
            $stream = $client->getStream();
            if ($stream === null) {
                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Unable to read file content')
                );
                return false;
            }

            // Take only first part of the file for validation
            $firstLinesOfData = stream_get_contents($stream, 20000);

            // Close the stream
            fclose($stream);

        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Unauthorized') !== false || strpos($e->getMessage(), '401') !== false) {
                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Basic authorization, access denied. Please, recheck specified access credentials')
                );
            } else {
                $reason = '';

                if (strpos($e->getMessage(), 'Not Found') !== false || strpos($e->getMessage(), '404') !== false) {
                    $reason = '(404 Not Found)';
                } elseif (strpos($e->getMessage(), 'Forbidden') !== false || strpos($e->getMessage(), '403') !== false) {
                    $reason = '(403 Forbidden)';
                }

                $this->addError(
                    $model,
                    $urlFieldName,
                    \Yii::t('admin', 'Unable to open specified URL {0}', [$reason])
                );
            }
            return false;
        }

        // Create temporary file for validation
        $tmpFilePath = sys_get_temp_dir() . '/' . md5(random_bytes(10)) . '.csv';
        file_put_contents($tmpFilePath, $firstLinesOfData);

        $uploadedFile = new UploadedFile([
            'tempName' => $tmpFilePath,
            'type' => 'text/csv',
        ]);

        $dynamicModel = new DynamicModel([
            'file' => $uploadedFile,
            'handler_name' => $model->handler_name,
        ]);
        $dynamicModel->addRule(['file'], ImportFileStructureValidator::class);
        $dynamicModel->validate();
        unlink($tmpFilePath);

        $dynamicModelErrors = $dynamicModel->getErrors();

        if (count($dynamicModelErrors) === 0) {
            return false;
        }

        foreach ($dynamicModelErrors as $field => $errors) {
            if ($field === 'file') {
                $field = $urlFieldName;
            }

            foreach ($errors as $error) {
                $this->addError($model, $field, $error);
            }
        }

        return true;
    }
}
