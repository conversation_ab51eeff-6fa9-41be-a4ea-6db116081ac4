<?php

namespace common\components\dataImportExport\fileSplitter;

use common\components\fileDataReader\DataReaderInterface;
use common\components\fileDataWriter\DataWriterInterface;
use Yii;
use yii\helpers\FileHelper;

/**
 * Splits source file into separated parts.
 */
class FileSplitter
{
    /**
     * Maximum number of parts can be created from source file.
     * Should prevent problems when too big file will be uploaded.
     */
    private const MAX_NUMBER_OF_PARTS = 1000;

    /**
     * @var DataReaderInterface
     */
    private $dataReader;

    /**
     * @var DataWriterInterface
     */
    private $dataWriter;

    /**
     * Used as a temporary storage for splitted parts.
     * @var
     */
    protected $tmpDirectory;

    /**
     * FileSplitter constructor.
     * @param DataReaderInterface $dataReader
     * @param DataWriterInterface $dataWriter
     */
    public function __construct(DataReaderInterface $dataReader, DataWriterInterface $dataWriter)
    {
        $this->dataReader = $dataReader;
        $this->dataWriter = $dataWriter;
    }

    /**
     * @throws \yii\base\ErrorException
     */
    public function __destruct()
    {
        if (null !== $this->tmpDirectory) {
            FileHelper::removeDirectory($this->tmpDirectory);
        }
    }

    /**
     * @param  string              $filePath
     * @param  int                 $limit
     * @throws \yii\base\Exception
     * @return SplittedFile
     */
    public function split(string $filePath, int $limit, array $expectedHeaders = []): SplittedFile
    {
        $splittedFile = new SplittedFile();
        $tmpPartsDirectory = $this->getTmpDirPath();
        $offset = 0;

        for ($i = 0;; $i++) {
            $partFilepath = $tmpPartsDirectory . '/part_' . $limit . '_' . $offset;
            $partData = $this->dataReader->getData($filePath, $limit, $offset, $expectedHeaders);

            if (count($partData) === 0) {
                break;
            }

            if ($i >= self::MAX_NUMBER_OF_PARTS) {
                break;
            }

            $this->dataWriter->saveData($partData, $partFilepath);

            $splittedFilePart = new SplittedFilePart();
            $splittedFilePart->offset = $offset;
            $splittedFilePart->partNumber = $i;
            $splittedFilePart->filePath = $partFilepath;
            $splittedFilePart->countItems = count($partData);

            $splittedFile->parts[] = $splittedFilePart;
            $splittedFile->countItems += $splittedFilePart->countItems;
            $offset += $limit;
        }

        return $splittedFile;
    }

    /**
     * @throws \yii\base\Exception
     * @return bool|string
     */
    public function getTmpDirPath()
    {
        if (null !== $this->tmpDirectory) {
            return $this->tmpDirectory;
        }

        $this->tmpDirectory = Yii::getAlias('@runtime/import-export/splitted_files/' . uniqid('', true));
        FileHelper::createDirectory($this->tmpDirectory);

        return $this->tmpDirectory;
    }
}
