<?php

namespace common\components\dataImportExport\rotator;


use common\models\customer\DataExport;

/**
 * Class ExportRotator.
 */
class ExportRotator implements RotatorInterface
{
    /**
     * Number of records to preserve after rotating.
     */
    const PRESERVE_COUNT = 10;

    /**
     * {@inheritdoc}
     */
    public function rotate()
    {
        $lastId = (new DataExport())->getLastId(self::PRESERVE_COUNT);
        /** @var DataExport[] $dataExports */
        $dataExports = DataExport::find()->where(['<', 'id', $lastId])->all();

        foreach ($dataExports as $dataExport) {
            $dataExport->delete();
        }
    }
}
