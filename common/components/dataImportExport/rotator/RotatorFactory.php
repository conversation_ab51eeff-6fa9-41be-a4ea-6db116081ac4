<?php

namespace common\components\dataImportExport\rotator;

/**
 * Class RotatorFactory.
 */
class RotatorFactory
{
    public const TYPE_IMPORT = 'import';
    public const TYPE_EXPORT = 'export';

    /**
     * Creates and returns needed rotator by given type.
     *
     * @param  string           $type
     * @throws \Exception
     * @return RotatorInterface
     */
    public function getRotator(string $type): RotatorInterface
    {
        if ($type === self::TYPE_IMPORT) {
            return new ImportRotator();
        }

        if ($type === self::TYPE_EXPORT) {
            return new ExportRotator();
        }

        throw new \Exception("Unable to determine data rotator for type '$type'");
    }
}
