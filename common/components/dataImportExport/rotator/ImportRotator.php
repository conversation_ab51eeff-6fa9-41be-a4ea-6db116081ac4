<?php

namespace common\components\dataImportExport\rotator;

use common\models\customer\DataImport;

/**
 * Class ImportRotator.
 */
class ImportRotator implements RotatorInterface
{
    /**
     * Number of records to preserve after rotating.
     */
    const PRESERVE_COUNT = 10;

    /**
     * {@inheritdoc}
     */
    public function rotate()
    {
        $lastId = (new DataImport())->getLastId(self::PRESERVE_COUNT);
        /** @var DataImport[] $dataImports */
        $dataImports = DataImport::find()->where(['<', 'id', $lastId])->all();

        foreach ($dataImports as $dataImport) {
            $dataImport->delete();
        }
    }
}
