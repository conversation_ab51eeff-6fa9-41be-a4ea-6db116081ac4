<?php

namespace common\components\db;

use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;

class ClickhouseDbHelper
{
    use LogToConsoleTrait;

    /**
     * Identifier that detects errors like:
     * "HttpCode:404 ;  ;Code: 81. DB::Exception: Database customer_00001 does not exist. (UNKNOWN_DATABASE)"
     */
    public const UNKNOWN_DATABASE_ERROR_IDENTIFIER = 'UNKNOWN_DATABASE';

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getClusterPart(): string
    {
        $clusterPart = "";
        if (YII_ENV !== 'local') {
            $clusterName = getenv('CLICKHOUSE_CLUSTER_NAME');
            $clusterPart = "ON CLUSTER $clusterName";
        }

        return $clusterPart;
    }

    public function getAmazonOrderTableName(): string
    {
        return AmazonOrder::tableName();
    }

    public function getAmazonOrderInProgressTableName(): string
    {
        return AmazonOrderInProgress::tableName();
    }

    public function getCustomerPrefix(): string
    {
        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER);
    }

    public function getAmazonOrderInProgressTmpTableName(): string
    {
        return AmazonOrderInProgress::tableName() . '_tmp';
    }

    public function generateReplicationPath(string $tableName): string
    {
        [$dbName, $tableName] = explode('.', $tableName, 2);
        return "/clickhouse/tables/{shard}/$dbName/{$tableName}_" . date('Y-m-d_H-i-s');
    }

    public function getAmazonOrderBaseStructure(): string
    {
        return "
                `id` Nullable(Int64),
                `order_id` String,
                `asin` String,
                `sku` String,
                `order_item_id` String,
                `title` String,
                `quantity` Int32,
                `quantity_shipped` Int32,
                `item_price` Int64,
                `shipping_price` Int64,
                `item_tax` Int64,
                `shipping_tax` Int64,
                `shipping_discount` Int64,
                `promotion_discount` Int64,
                `cod_fee` Int64,
                `cod_fee_discount` Int64,
                `promotion_id` String,
                `condition_id` String,
                `condition_subtype_id` String,
                `condition_note` String,
                `scheduled_delivery_start_date` DateTime,
                `scheduled_delivery_end_date` DateTime,
                `order_purchase_date` DateTime,
                `order_marketplace_id` String,
                `date` DateTime,
                `profit` Int64,
                `order_period_id` String,
                `seller_id` String,
                `seller_order_id` String,
                `last_update_date` DateTime,
                `order_status` String,
                `order_type` String,
                `created_at` DateTime,
                `updated_at` DateTime,
                `fulfillment_channel` String,
                `is_business_order` Nullable(Bool),
                `currency_code` String
                ";
    }

    public function getAmazonOrderBasePrimaryKey(): string
    {
        return "PRIMARY KEY (order_purchase_date, seller_id, order_marketplace_id, asin, sku, order_id, seller_order_id, order_item_id)";
    }

    public function getAmazonOrderBaseOrder(): string
    {
        return "ORDER BY (order_purchase_date, seller_id, order_marketplace_id, asin, sku, order_id, seller_order_id, order_item_id)";
    }

    public function getPostgresqlFunction(array $config, string $table, string $schema)
    {
        $host = $config['dbHost'];
        $port = $config['dbPort'];
        $username = $config['dbUser'];
        $password = $config['dbPassword'];
        $dbName = $config['profitDashDbName'];

        return "postgresql('{$host}:{$port}', {$dbName}, {$table}, '{$username}', '{$password}', {$schema})";
    }

    public function executeOnMasterNodes(string $sql, int $maxAttempts = 1)
    {
        $masterNodes = $this->dbManager->getClickhouseCustomerMasterNodes(false);
        $clusterPart = $this->getClusterPart();
        $sql = str_replace($clusterPart, '', $sql);

        // Need to create database also on base node, because db manager use it when walking through databases.
        if (false !== strpos($sql, 'CREATE DATABASE')) {
            $masterNodes[] = getenv('CLICKHOUSE_HOST');
        }
        $masterNodes = array_unique($masterNodes);

        $this->info("Executing sql on master nodes: $sql");
        $this->info($sql);

        foreach ($masterNodes as $node) {
            for ($i = $maxAttempts; $i > 0; $i--) {
                Command::$isNodeChangingEnabled = false;
                try {
                    $this->info("Executing sql on $node, attempt " . ($maxAttempts - $i + 1));
                    $this->dbManager->getDb('customer', 'clickhouse', $node)
                        ->createCommand($sql)
                        ->execute();
                    continue 2;
                } catch (\Throwable $e) {
                    $this->error($e);
                    sleep(1);
                }
                Command::$isNodeChangingEnabled = true;
            }
            throw new \Exception("Failed to execute sql on $node after $maxAttempts attempts");
        }
    }
}
