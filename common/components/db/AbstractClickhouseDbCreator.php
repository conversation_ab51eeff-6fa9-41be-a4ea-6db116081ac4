<?php

namespace common\components\db;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use yii\db\Connection;

abstract class AbstractClickhouseDbCreator implements InitDbInterface
{
    use LogToConsoleTrait;

    protected string $templateDb;
    protected string $dbName;
    protected Connection $connection;
    protected string $postfix;
    protected int $customerId;
    protected DbManager $dbManager;

    public function __construct(string $postfix, int $customerId)
    {
        $this->postfix = $postfix;
        $this->dbName = static::DB_NAME . '_' . $postfix;
        $this->connection =  \Yii::$app->clickhouse;
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerId = $customerId;
    }

    public function create(): bool
    {
        $nodes = $this->dbManager->getAllNodesForConnection($this->connection);

        foreach ($nodes as $nodeIp) {
            $this->connection = $this->dbManager->getDb('system', HelperFactory::TYPE_CLICKHOUSE, $nodeIp);

            if (!$this->isDbExists()) {
                $this->info("Creating database '$this->dbName' on host $nodeIp");
                $this->connection
                    ->createCommand("CREATE DATABASE {$this->dbName}")
                    ->execute();
                $this->afterCreate();
            }
            $this->connection->close();
        }

        return true;
    }

    public function isDbExists(): bool
    {
        $command = $this
            ->connection
            ->createCommand('SELECT exists (SELECT name FROM system.databases WHERE lower(name) = lower(:dbname))')
            ->bindValues([':dbname' => $this->dbName]);
        return $command->queryScalar();
    }

    protected function afterCreate()
    {
    }

    private function getTemplateName(): string
    {
        return static::TEMPLATE_DB_NAME;
    }
}
