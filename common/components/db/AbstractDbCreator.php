<?php

namespace common\components\db;

use yii\db\Connection;

abstract class AbstractDbCreator implements InitDbInterface
{
    protected string $templateDb;
    protected string $dbName;
    protected Connection $connection;
    protected string $postfix;

    public function __construct(string $postfix)
    {
        $this->postfix = $postfix;
        $this->dbName = static::DB_NAME . '_' . $postfix;
        $this->templateDb = static::TEMPLATE_DB_NAME;
        $this->connection = \Yii::$app->db;
    }

    public function create(): bool
    {
        $templateName = $this->getTemplateName();

        if (!$this->isDbExists()) {
            // Disconnecting all opened connections before database drop
            $this
                ->connection
                ->createCommand("
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE datname = '{$templateName}'
                    AND pid <> pg_backend_pid();
                ")
                ->execute();

            $this->connection
                ->createCommand("CREATE DATABASE {$this->dbName} WITH TEMPLATE {$templateName}")
                ->execute();
            $this->connection->close();
            $this->afterCreate();

            return true;
        }

        return false;
    }

    public function isDbExists(): bool
    {
        $command = $this->connection->createCommand('SELECT exists (SELECT datname FROM pg_catalog.pg_database WHERE lower(datname) = lower(:dbname))')->bindValues([':dbname' => $this->dbName]);

        return $command->queryScalar();
    }

    protected function afterCreate()
    {
    }

    private function getTemplateName(): string
    {
        return static::TEMPLATE_DB_NAME;
    }
}
