<?php

namespace common\components\db;

use common\components\core\db\DbPostfixManager;
use common\models\Seller;
use yii\mutex\Mutex;

class DbCreator
{
    protected Seller $seller;
    protected Mutex $mutex;

    /** @var InitDbInterface[]  */
    protected array $initDb = [];

    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        $this->mutex = \Yii::$app->mutex;

        $dbPostfixManager = new DbPostfixManager($seller->id, $seller->customer_id);
        $sellerRelatedDbPostfix = $dbPostfixManager->getDbPostfixForSellerRelatedDbs();
        $customerRelatedDbPostfix = $dbPostfixManager->getDbPostfixForCustomerRelatedDbs();

        $processes = [
            new InitFinanceDb($sellerRelatedDbPostfix, $seller->customer_id),
            new InitCustomerDb($customerRelatedDbPostfix, $seller->customer_id),
            new InitAdsDb($customerRelatedDbPostfix, $seller->customer_id),
            new InitOrderDb($sellerRelatedDbPostfix, $seller->customer_id),

            // Clickhouse db initialization should start after postgres
            new InitClickhouseCustomerDb($customerRelatedDbPostfix, $seller->customer_id),
            new InitRepricerEventDb($customerRelatedDbPostfix, $seller->customer_id),

            new InitDbStructure($seller->customer_id),
        ];

        foreach ($processes as $process) {
            if ($process instanceof InitDbInterface) {
                $this->initDb[] = $process;
            }
        }
    }

    public function createDatabases() : bool
    {
        $errors = [];

        foreach ($this->initDb as $process) {
            try {
                $process->create();
            } catch (\Throwable $e) {
                $errors[] = $e->getMessage();
                \Yii::error($e);
            }
        }

        if (!empty($errors)) {
            throw new \Exception(implode("\n", $errors));
        }

        return true;
    }
}
