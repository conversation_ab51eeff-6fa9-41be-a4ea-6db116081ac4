<?php

namespace common\components\db;

use common\components\core\db\dbManager\helper\PostgressShard;
use yii\db\Connection;

abstract class AbstractSchemaCreator implements InitDbInterface
{
    protected string $templateDb;
    protected string $schemaName;
    protected Connection $connection;
    protected string $postfix;
    protected int $customerId;

    public function __construct(string $postfix, int $customerId, ?Connection $connection = null)
    {
        $this->postfix = $postfix;
        $this->schemaName = static::SCHEMA_NAME . '_' . $postfix;
        $helper = new PostgressShard();
        $this->connection = $connection ?? $helper->getBaseConnection();
        $this->customerId = $customerId;
    }

    public function create(): bool
    {
        if (!$this->isSchemaExists()) {

            $this->connection
                ->createCommand("CREATE SCHEMA {$this->schemaName}")
                ->execute();
            $this->afterCreate();

            return true;
        }

        return false;
    }

    public function isSchemaExists(): bool
    {
        $command = $this->connection->createCommand('SELECT exists (SELECT nspname FROM pg_catalog.pg_namespace WHERE lower(nspname) = lower(:schemaName))')->bindValues([':schemaName' => $this->schemaName]);

        return $command->queryScalar();
    }

    protected function afterCreate()
    {
    }
}
