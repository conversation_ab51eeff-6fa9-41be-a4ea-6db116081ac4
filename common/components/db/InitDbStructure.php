<?php

namespace common\components\db;

use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\LinuxCommander;
use common\models\Seller;

class InitDbStructure implements InitDbInterface
{
    protected ?string $customerId = null;

    public function __construct(string $customerId)
    {
        $this->customerId = $customerId;
    }

    public function create(): bool
    {
        $errors = [];

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-finance 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-customer 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-order 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-ads 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-clickhouse-customer 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        try {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf("migrate-clickhouse-customer-related 0 %d %d --interactive=0", $this->customerId, $this->customerId + 1),
                true
            );
        } catch (\Throwable $e) {
            $errors[] = $e->getMessage();
        }

        $isFirstInitialization = Seller::find()->where([
            'customer_id' => $this->customerId,
            'is_db_created' => true
        ])->noCache()->count() === 0;

        if ($isFirstInitialization) {
            try {
                /** @var CustomerConfig $customerConfig */
                $customerConfig = \Yii::$container->get('customerConfig');
                $customerConfig->set(
                    CustomerConfig::PARAMETER_INTEGER_MONEY_ACCURACY,
                    CustomerComponent::INTEGER_MONEY_ACCURACY_NEW
                );
                $customerConfig->set(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2, 1);
            } catch (\Throwable $e) {
                $errors[] = $e->getMessage();
            }
        }

        if (!empty($errors)) {
            throw new \Exception(implode("\n", $errors));
        }

        return true;
    }
}
