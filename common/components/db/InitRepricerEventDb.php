<?php

namespace common\components\db;

use yii\db\Connection;

class InitRepricerEventDb extends AbstractSchemaCreator
{
    public const SCHEMA_NAME = 'repricer_event';

    public function __construct(string $postfix, int $customerId, ?Connection $connection = null)
    {
        $connection = $connection ?? \Yii::$app->dbManager->getRepricerEventDb();
        parent::__construct($postfix, $customerId, $connection);
    }
}
