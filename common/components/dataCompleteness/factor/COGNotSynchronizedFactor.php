<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use yii\db\ActiveQuery;

class COGNotSynchronizedFactor extends AbstractProductRelatedFactor
{
    use ExtraFiltersTrait;

    public function getCountAll(): int
    {
        return $this
            ->getProductBaseQuery()
            ->andWhere([
                'AND',
                ['>', 'repricer_id', 0],
                ['=', 'repricer_is_deleted', 'f']
            ])
            ->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query->andWhere([
            'AND',
            ['=', 'is_enabled_sync_with_repricer', 'f'],
            ['>', 'repricer_id', 0],
            ['=', 'repricer_is_deleted', 'f']
        ]);
    }
}
