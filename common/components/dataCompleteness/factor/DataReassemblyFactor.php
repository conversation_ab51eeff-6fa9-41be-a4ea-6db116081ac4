<?php

namespace common\components\dataCompleteness\factor;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\ads\SpAdvertisedProduct;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\Seller;
use yii\db\ActiveQuery;
use yii\db\Expression;

class DataReassemblyFactor implements FactorInterface
{
    protected CustomerConfig $customerConfig;
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->customerConfig = \Yii::$container->get("customerConfig");
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getCountAll(): int
    {
        $isRebuilding = \Yii::$app
            ->fastPersistentCache
            ->get("is_re_export_in_progress_" . $this->dbManager->getCustomerId());

        if (!$isRebuilding) {
            return 0;
        }

        return CustomerProcess::find()
            ->select('sum(count_all)')
            ->where([
                'parent_process_id' => CustomerProcess::find()
                    ->select('id')
                    ->where([
                    'AND',
                    ['=', 'customer_id', $this->dbManager->getCustomerId()],
                    ['in', 'name', ['recreate_clickhouse_and_re_export', 'from_db_to_clickhouse_process']],
                    ['=', 'status', CustomerProcess::STATUS_IN_PROGRESS]
                ])
            ])
            ->groupBy('parent_process_id')
            ->scalar() ?? 0;
    }

    public function getCountUnfilled(): int
    {
        $isRebuilding = \Yii::$app
            ->fastPersistentCache
            ->get("is_re_export_in_progress_" . $this->dbManager->getCustomerId());

        if (!$isRebuilding) {
            return 0;
        }

        return CustomerProcess::find()
            ->select('sum(count_all) - sum(count_success)')
            ->where([
                'parent_process_id' => CustomerProcess::find()
                    ->select('id')
                    ->where([
                        'AND',
                        ['=', 'customer_id', $this->dbManager->getCustomerId()],
                        ['in', 'name', ['recreate_clickhouse_and_re_export', 'from_db_to_clickhouse_process']],
                        ['=', 'status', CustomerProcess::STATUS_IN_PROGRESS]
                    ])
            ])
            ->groupBy('parent_process_id')
            ->scalar() ?? 0;
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }
}
