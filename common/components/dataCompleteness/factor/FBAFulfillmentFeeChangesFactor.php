<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\FbaFeeFactorView;
use yii\db\ActiveQuery;

class FBAFulfillmentFeeChangesFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return AmazonOrderExtendedViewV1::find()->cache(5 * 60)->count();
    }

    public function getCountUnfilled(): int
    {
        return FbaFeeFactorView::find()
            ->andWhere('fba_fee_eur != estimated_fba_fee_per_item_eur')
            ->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }
}
