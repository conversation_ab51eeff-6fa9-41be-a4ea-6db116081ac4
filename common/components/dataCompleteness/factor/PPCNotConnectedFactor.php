<?php

namespace common\components\dataCompleteness\factor;

use common\models\ads\AmazonAdsAccount;
use yii\db\ActiveQuery;

class PPCNotConnectedFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return 1;
    }

    public function getCountUnfilled(): int
    {
        return AmazonAdsAccount::find()->count() > 0 ? 0 : 1;
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }
}
