<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\TransactionExtendedView;
use yii\db\ActiveQuery;

class AdjustmentsFeeFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return AmazonOrderExtendedView::find()->cache(5 * 60)->count();
    }

    public function getCountUnfilled(): int
    {
        $query = TransactionExtendedView::find();
        return self::setFilterByQuery($query)
            ->cache(5 * 60)->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query->andWhere(['=', 'transaction_type', 'adjustment'])
            ->andWhere(['!=', 'sales_category_depth_1', 'ignored']);
    }
}
