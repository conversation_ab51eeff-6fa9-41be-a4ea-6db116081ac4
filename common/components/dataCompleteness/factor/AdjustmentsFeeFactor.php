<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\TransactionExtendedViewV1;
use yii\db\ActiveQuery;

class AdjustmentsFeeFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return AmazonOrderExtendedViewV1::find()->cache(5 * 60)->count();
    }

    public function getCountUnfilled(): int
    {
        $query = TransactionExtendedViewV1::find();
        return self::setFilterByQuery($query)
            ->cache(5 * 60)->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query->andWhere(['=', 'transaction_type', 'adjustment'])
            ->andWhere(['!=', 'sales_category_depth_1', 'ignored']);
    }
}
