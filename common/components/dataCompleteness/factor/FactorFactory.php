<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\Product;
use common\models\customer\TransactionExtendedView;

class FactorFactory
{
    public const FACTOR_NO_COST_OF_GOODS = 'no_cost_of_goods';
    public const FACTOR_NO_VAT = 'no_vat';
    public const FACTOR_NO_FBM_SHIPPING_COST = 'no_fbm_shipping_costs';
    public const FACTOR_NO_OTHER_FEES = 'no_other_fees';
    public const FACTOR_DATA_REASSEMBLY = 'data_reassembly';
    public const FACTOR_HISTORICAL_DATA_UPLOAD = 'historical_data_upload';
    public const FACTOR_PPC_NOT_CONNECTED = 'ppc_not_connected';
    public const FACTOR_COG_NOT_SYNCHRONIZED = 'cog_not_synchronized';
    public const FACTOR_MISSING_PRODUCTS_IN_REPRICER = 'missing_products_in_repricer';
    public const FACTOR_NO_INDIRECT_COSTS = 'no_indirect_costs';
    public const FACTOR_ADJUSTMENTS_FEE = 'adjustments_fee';
    public const FACTOR_REFERRAL_FEE_CHANGES = 'referral_fee_changes';
    public const FACTOR_FBA_FULFILLMENT_FEE_CHANGES = 'fba_fulfillment_fee_changes';

    public function getFactor(string $factorId): FactorInterface
    {
        switch ($factorId) {
            case self::FACTOR_NO_COST_OF_GOODS:
                return new NoCostOfGoodsFactor();
            case self::FACTOR_NO_VAT:
                return new NoVATFactor();
            case self::FACTOR_NO_FBM_SHIPPING_COST;
                return new NoFBMShippingCostsFactor();
            case self::FACTOR_NO_OTHER_FEES;
                return new NoOtherFeesFactor();
            case self::FACTOR_DATA_REASSEMBLY;
                return new DataReassemblyFactor();
            case self::FACTOR_PPC_NOT_CONNECTED;
                return new PPCNotConnectedFactor();
            case self::FACTOR_COG_NOT_SYNCHRONIZED:
                return new COGNotSynchronizedFactor();
            case self::FACTOR_MISSING_PRODUCTS_IN_REPRICER:
                return new MissingProductsInRepricerFactor();
            case self::FACTOR_NO_INDIRECT_COSTS:
                return new NoIndirectCostsFactor();
            case self::FACTOR_HISTORICAL_DATA_UPLOAD:
                return new HistoricalDataUploadFactor();
            case self::FACTOR_ADJUSTMENTS_FEE:
                return new AdjustmentsFeeFactor();
            case self::FACTOR_REFERRAL_FEE_CHANGES:
                return new ReferralFeeChangesFactor();
            case self::FACTOR_FBA_FULFILLMENT_FEE_CHANGES:
                return new FBAFulfillmentFeeChangesFactor();
        }

        throw new \Exception("Unable to find factor instance for factor $factorId");
    }

    public function getSupportedFactorsForTable(string $tableName): array
    {
        $map = [
            TransactionExtendedView::tableName() => [
                self::FACTOR_ADJUSTMENTS_FEE
            ],
            Product::tableName() => [
                self::FACTOR_NO_COST_OF_GOODS,
                self::FACTOR_NO_VAT,
                self::FACTOR_NO_FBM_SHIPPING_COST,
                self::FACTOR_NO_OTHER_FEES,
                self::FACTOR_COG_NOT_SYNCHRONIZED,
                self::FACTOR_MISSING_PRODUCTS_IN_REPRICER,
            ],
        ];

        return $map[$tableName] ?? [];
    }
}
