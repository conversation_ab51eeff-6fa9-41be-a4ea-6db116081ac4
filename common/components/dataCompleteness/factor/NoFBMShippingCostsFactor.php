<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\Product;
use yii\db\ActiveQuery;
use yii\db\Expression;

class NoFBMShippingCostsFactor extends AbstractProductRelatedFactor
{
    public function getCountAll(): int
    {
        return $this
            ->getProductBaseQuery()
            ->where([
                'OR',
                ['=', 'stock_type', Product::STOCK_TYPE_FBM],
                ['=', 'is_multiple_stock_type', 't']
            ])
            ->cache(5 * 60)
            ->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query->andWhere([
            'AND',
            ['is', 'shipping_cost', new Expression('NULL')],
            [
                'OR',
                ['=', 'stock_type', Product::STOCK_TYPE_FBM],
                ['=', 'is_multiple_stock_type', 't']
            ]
        ]);
    }
}
