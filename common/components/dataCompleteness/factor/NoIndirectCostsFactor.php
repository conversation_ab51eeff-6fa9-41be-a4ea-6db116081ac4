<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\IndirectCost;
use yii\db\ActiveQuery;

class NoIndirectCostsFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return 1;
    }

    public function getCountUnfilled(): int
    {
        return IndirectCost::find()->count() > 0 ? 0 : 1;
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }
}
