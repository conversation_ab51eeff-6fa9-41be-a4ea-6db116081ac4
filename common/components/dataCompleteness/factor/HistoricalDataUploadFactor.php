<?php

namespace common\components\dataCompleteness\factor;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\AmazonReport;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\finance\EventPeriod;
use common\models\Seller;
use yii\db\ActiveQuery;
use yii\db\Query;

class HistoricalDataUploadFactor implements FactorInterface
{
    use ExtraFiltersTrait;

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getCountAll(): int
    {
        $sellers = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ])->all();

        $countAll = 0;
        $query = AmazonReport::find();
        $this->takeIntoAccountCreatedAt($query);
        $this->applyExpiredOrInactiveSubscriptionLogic($query);
        $countAll += $query->count();

        foreach ($sellers as $seller) {
            if (!$seller->is_active) {
                continue;
            }
            $this->dbManager->setSellerId($seller->id);
            $query = EventPeriod::find()->andWhere([
                'AND',
                ['!=', 'loading_status', EventPeriod::LOADING_STATUS_SKIPPED],
            ]);
            $this->takeIntoAccountCreatedAt($query);

            $countAll += $query->count();
        }

        return $countAll;
    }

    public function getCountUnfilled(): int
    {
        /** @var Seller[] $sellers */
        $sellers = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId(),
        ])->all();

        $countAll = 0;
        $query = AmazonReport::find()->where(['in', 'status', [
                AmazonReport::PROCESSING_STATUS_UNKNOWN,
                AmazonReport::PROCESSING_STATUS_IN_PROGRESS,
                AmazonReport::PROCESSING_STATUS_QUEUED,
                AmazonReport::PROCESSING_STATUS_IN_QUEUE
            ]]);
        $this->takeIntoAccountCreatedAt($query);
        $this->applyExpiredOrInactiveSubscriptionLogic($query);

        foreach ($sellers as $seller) {
            if (!$seller->is_active) {
                continue;
            }
            $this->dbManager->setSellerId($seller->id);
            $query = EventPeriod::find()->where(['in', 'loading_status', [
                    EventPeriod::LOADING_STATUS_NEW,
                    EventPeriod::LOADING_STATUS_PROCESSING,
                    EventPeriod::LOADING_STATUS_QUEUED
                ]]);
            $this->takeIntoAccountCreatedAt($query);

            $countAll += $query->count();
        }

        return $countAll;
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }

    protected function takeIntoAccountCreatedAt(Query $query): void
    {
        $query
            ->andWhere(['<=', 'created_at', date('Y-m-d', strtotime('-30 minutes'))])
            ->andWhere(['>=', 'created_at', date('Y-m-d', strtotime('-1 months'))]);
    }
}
