<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use yii\db\ActiveQuery;

abstract class AbstractProductRelatedFactor implements FactorInterface
{
    use ExtraFiltersTrait;

    public function getCountAll(): int
    {
        return $this
            ->getProductBaseQuery()
            ->cache(5 * 60)
            ->count();
    }

    public function getCountUnfilled(): int
    {
        return static::setFilterByQuery($this->getProductBaseQuery())->count();
    }

    protected function getProductBaseQuery(): ActiveQuery
    {
        $query = (new Product())->search([]);

        return $query;
    }
}
