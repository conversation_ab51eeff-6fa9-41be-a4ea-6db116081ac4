<?php

namespace common\components\dataCompleteness\factor;

use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\ReferralFeeFactorView;
use common\models\customer\TransactionExtendedView;
use yii\db\ActiveQuery;

class ReferralFeeChangesFactor implements FactorInterface
{
    public function getCountAll(): int
    {
        return AmazonOrderExtendedView::find()->cache(5 * 60)->count();
    }

    public function getCountUnfilled(): int
    {
        return ReferralFeeFactorView::find()
            ->andWhere('referral_fee_eur != estimated_referral_fee_per_item_eur')
            ->count();
    }

    public function setFilterByQuery(ActiveQuery $query): ActiveQuery
    {
        return $query;
    }
}
