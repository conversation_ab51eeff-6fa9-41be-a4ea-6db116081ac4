<?php

namespace common\components\dataCompleteness;

use common\components\core\db\dbManager\DbManager;
use common\components\dataCompleteness\factor\FactorFactory;
use common\components\LogToConsoleTrait;
use common\models\customer\DataCompleteness;

class Checker
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected FactorFactory $factorFactory;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->factorFactory = new FactorFactory();
    }

    /**
     * Re-checks the completeness of the data for the specified factor.
     *
     * @param string $factorId
     * @return void
     * @throws \Exception
     */
    public function check(string $factorId): void
    {
        $this->info("Calculating factor {$factorId} started");
        $factorHandler = $this->factorFactory->getFactor($factorId);

        /** @var DataCompleteness $dataCompleteness */
        $dataCompleteness = DataCompleteness::find()->where(['factor_id' => $factorId])->one();

        if (!empty($dataCompleteness) && $dataCompleteness->is_ignored) {
            $countAll = null;
            $countUnfilled = null;
        } else {
            $countAll = $factorHandler->getCountAll();
            $countUnfilled = $factorHandler->getCountUnfilled();
        }

        $this->info([
            'countAll' => $countAll,
            'countUnfilled' => $countUnfilled
        ]);


        if (empty($dataCompleteness)) {
            $dataCompleteness = new DataCompleteness();
            $dataCompleteness->factor_id = $factorId;
        }

        $dataCompleteness->count_all = $countAll;
        $dataCompleteness->count_unfilled = $countUnfilled;
        $dataCompleteness->checked_at = date('Y-m-d H:i:s');
        $dataCompleteness->save(false);

        $this->info("Calculating factor finished");
    }
}
