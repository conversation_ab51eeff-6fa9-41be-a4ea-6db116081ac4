<?php

namespace common\components\dataCompleteness;

use common\models\customer\DataCompleteness;
use common\models\DataCompletenessFactor;
use yii\db\Expression;

class Calculator
{
    /**
     * Calculate completeness percents based on factor weights and fill percentage.
     * For example:
     * Factor 1: weight 1, 100% filled
     * Factor 2: weight 2, 50% filled
     * Completeness = (1 * 100 + 2 * 50) / (1 + 2) = 66.66%
     *
     * @return float
     */
    public function getCompletenessPercents(): float
    {
        /** @var DataCompleteness[] $dataCompleteness */
        $dataCompleteness = DataCompletenessFactor::find()
            ->alias('dcf')
            ->select([
                'dcf.weight',
                'count_all',
                'count_unfilled'
            ])
            ->leftJoin(DataCompleteness::tableName() . ' dc', 'dcf.id = dc.factor_id')
            ->where([
                'AND',
                ['=', 'is_ignored', 'f'],
                ['>', 'weight', 0]
            ])
            ->asArray()
            ->all(DataCompleteness::getDb());

        if (count($dataCompleteness) === 0) {
            return 100;
        }

        $totalWeight = 0;
        $totalWeightReal = 0;

        foreach ($dataCompleteness as $item) {
            if ($item['count_all'] === 0) {
                continue;
            }

            $totalWeight += $item['weight'];
            $percentsFilled = 100 - ($item['count_unfilled'] / $item['count_all'] * 100);
            $totalWeightReal += $item['weight'] * $percentsFilled;
        }

        return round($totalWeightReal / $totalWeight,2);
    }
}
