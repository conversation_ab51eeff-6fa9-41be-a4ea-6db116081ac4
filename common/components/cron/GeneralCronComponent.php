<?php

namespace common\components\cron;

use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use common\models\Customer;
use Cron\CronExpression;
use yii\base\Component;
use yii\helpers\Console;
use yii\helpers\Json;

/**
 * This components process cron-like commands.
 */
class GeneralCronComponent extends Component
{
    use LogToConsoleTrait;

    public $commands = [];

    /**
     * This function processes $commands and run them taking in account their configuration.
     * @throws \Exception
     */
    public function scheduleTasks(): void
    {
        $commands = $this->commands;
        $dateTime = new \DateTime();
        $this->info(count($commands) . ' commands will be processed');

        foreach ($commands as $cron) {
            $cron['expression'] = trim($cron['expression']);

            try {
                $expression = new CronExpression($cron['expression']);
                $command = $cron['command'];

                if (!$expression->isDue($dateTime)) {
                    continue;
                }

                if (isset($cron['customerIdStep'])) {
                    $commands = $this->generateCustomerIdChunkedCommands($command, $cron['customerIdStep']);
                } else {
                    $commands = [$command];
                }

                foreach ($commands as $command) {
                    $this->addCommand($command);
                }
            } catch (\Exception $e) {
                $this->error('Cron Exception: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }
        }
    }

    public function generateCustomerIdChunkedCommands(string $baseCommand, int $step = 10000): array
    {
        $commands = [];
        $fromCustomerId = 0;

        $maxCustomerId = Customer::find()->noCache()->max('id');

        while (true) {
            $toCustomerId = $fromCustomerId + $step;
            $command = "{$baseCommand} {$fromCustomerId}";

            if ($toCustomerId <= $maxCustomerId) {
                $command .= " {$toCustomerId}";
            }

            $commands[] = $command;

            if ($toCustomerId > $maxCustomerId) {
                break;
            }

            $fromCustomerId = $toCustomerId;
        }

        return $commands;
    }

    /**
     * Puts commands into cron queue
     *
     * @param $command
     */
    public function addCommand($command): void
    {
        $message = Json::encode(['command' => $command]);
        $this->info("Putting command '$command' into queue");
        \Yii::$app
            ->rabbitmq
            ->getProducer(MessageAbstract::PRODUCER_NAME)
            ->publish($message, MessageInterface::EXCHANGE_NAME_CRON);
    }

    /**
     * Executes passed command
     *
     * @param string $command
     * @param bool $isAllowMultiple Is allow multiple execution? If no - only one execution will be done per time
     * @return false|int|null
     */
    public function executeCommand(string $command, bool $isAllowMultiple = false)
    {
        $this->info("Processing command '$command'");

        if (!$isAllowMultiple) {
            $res = [];
            exec("ps -ux | grep '{$command}'", $res);

            if (!empty($res) && false !== strpos($res[0], 'yii')) {
                $this->info("Command has been already ran ($res[0])");
                return null;
            }
        }

        $path = \Yii::getAlias('@app') . '/..';
        $pid = LinuxCommander::safeExecute("php $path/yii ", $command);

        $this->info("Executed successfully, pid = {$pid}");
        return $pid;
    }
}
