<?php

namespace common\components\IndirectCost\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\BufferFactory;
use common\components\dataCompleteness\Checker;
use common\components\dataCompleteness\factor\FactorFactory;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\models\Command;
use common\models\customer\IndirectCost;
use common\models\customer\Product;
use common\models\customer\TransactionBuffer;
use common\models\finance\clickhouse\Transaction;
use Cron\CronExpression;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class IndirectCostChangesConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    protected BufferInterface $transactionsBuffer;
    protected DbManager $dbManager;
    protected CurrencyRateManager $currencyRateManager;
    protected CustomerComponent $customerComponent;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->currencyRateManager = new CurrencyRateManager();
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 30));
        $this->info($msg->body);
        $message = $msg->body;

        $isApplyNow = $message['isApplyNow'] ?? null;
        $isDeleted = $message['isDeleted'] ?? null;
        $customerId = $message['customerId'] ?? null;
        $indirectCostIds = $message['indirectCostId'] ?? null;
        $this->dbManager->setCustomerId($customerId);
        $this->transactionsBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();

        $indirectCostIds = explode(',', $indirectCostIds);

        foreach ($indirectCostIds as $indirectCostId) {
            try {
                $this->applySingleChange($indirectCostId, $isDeleted, $isApplyNow);
                (new Checker())->check(FactorFactory::FACTOR_NO_INDIRECT_COSTS);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        Command::create("transaction-buffer/force-flush $customerId", 1, true);

        return ConsumerInterface::MSG_ACK;
    }

    public function applySingleChange(int $indirectCostId, bool $isDeleted, bool $isApplyNow): void
    {
        $indirectCost = IndirectCost::findOne($indirectCostId);

        if (empty($indirectCost) && !$isDeleted) {
            throw new \Exception("Unable to find indirect cost with id $indirectCostId");
        }

        try {
            if ($isApplyNow) {
                $this->revertAll($indirectCostId, new \DateTime());
                $this->applyNow($indirectCost);
            } else {
                $this->revertAll($indirectCostId);

                if (!$isDeleted) {
                    if (empty($indirectCost->cron_expr)) {
                        $this->applyOneTime($indirectCost);
                    } else {
                        $this->applyRecurrent($indirectCost);
                    }
                }
            }
            $this->transactionsBuffer->flush();
        } catch (\Throwable $e) {
            $this->transactionsBuffer->remove();
            $this->error($e);
        }
    }

    protected function revertAll(int $indirectCostId, \DateTime $postedDate = null): void
    {
        if (empty($indirectCostId)) {
            throw new \Exception("Attempt to revert not existing indirect cost");
        }

        $this->info('Revert all started');

        $query = \common\models\customer\clickhouse\Transaction::find()
            ->select([
                'PostedDate',
                'max(TransactionDate) as TransactionDate',
                'SellerId',
                'MarketplaceId',
                'SellerSKU',
                'any(ASIN) as ASIN',
                'Currency',
                'IndirectCostId',
                'IndirectCostTypeId',
                'sum(AmountEUR) as AmountEUR',
                'sum(Amount) as Amount'
            ])
            ->where([
                'AND',
                ['=', 'IndirectCostId', $indirectCostId]
            ])
            ->groupBy('SellerId, MarketplaceId, SellerSKU, Currency, IndirectCostId, IndirectCostTypeId, PostedDate')
            ->having('Amount != 0')
            ->asArray();

        if (!empty($postedDate)) {
            TransactionBuffer::deleteAll([
                'AND',
                ['=', 'IndirectCostId', $indirectCostId],
                ['>=', 'PostedDate', $postedDate->format('Y-m-d 00:00:00')],
                ['<=', 'PostedDate', $postedDate->format('Y-m-d 23:59:59')]
            ]);
            $query->andWhere([
                'AND',
                ['>=', 'PostedDate', $postedDate->format('Y-m-d 00:00:00')],
                ['<=', 'PostedDate', $postedDate->format('Y-m-d 23:59:59')]
            ]);
        } else {
            TransactionBuffer::deleteAll(['=', 'IndirectCostId', $indirectCostId]);
        }

        foreach ($query->batch(100, $this->dbManager->getClickhouseCustomerDb()) as $transactions) {
            $reverseTransactions = [];

            foreach ($transactions as $transaction) {
                $reverseTransaction = new Transaction($transaction);
                $reverseTransaction->AmountEUR *= -1;
                $reverseTransaction->Amount *= -1;
                $reverseTransaction->MergeCounter = 1;
                $reverseTransaction->SellerOrderId = '';
                $reverseTransaction->AmazonOrderId = '';
                $reverseTransaction->CategoryId = 0;
                $reverseTransaction->COGCategoryId = 0;
                $reverseTransaction->Quantity = 0;
                $reverseTransaction->EventPeriodId = 0;
                $reverseTransaction->CreatedAt = date('Y-m-d H:i:s');
                $reverseTransactions[] = $reverseTransaction;
            }

            $this->info($reverseTransactions);

            $this->transactionsBuffer->put($reverseTransactions);
        }

        $this->info('Revert all finished');
    }

    protected function applyNow(IndirectCost $indirectCost): void
    {
        $this->info('Apply now started');
        $transactions = $this->generateTransactions($indirectCost, new \DateTime());
        $this->transactionsBuffer->put($transactions);
        $this->info($transactions);
        $indirectCost->last_apply_date = (new \DateTime())->format('Y-m-d H:i:s');
        $indirectCost->save(false, ['last_apply_date']);
        $this->info('Apply now finished');
    }

    protected function applyOneTime(IndirectCost $indirectCost): void
    {
        $this->info('Apply one time stated');
        $currDateTime = (new \DateTime())->setTime(0,0);
        $dateStart = (new \DateTime($indirectCost->date_start))->setTime(0,0);

        if ($dateStart > $currDateTime) {
            $this->info('Future cost, skipped');
            $this->info([
                'dateStart' => $dateStart,
                'curDate' => $currDateTime
            ]);
            return;
        }
        $transactions = $this->generateTransactions($indirectCost, new \DateTime($indirectCost->date_start));
        $this->transactionsBuffer->put($transactions);
        $this->info($transactions);
        $indirectCost->last_apply_date = (new \DateTime($indirectCost->date_start))->format('Y-m-d H:i:s');
        $indirectCost->save(false, ['last_apply_date']);
        $this->info('Apply one time finished');
    }

    protected function applyRecurrent(IndirectCost $indirectCost): void
    {
        $this->info('Apply recurring started');
        $dateStart = new \DateTime($indirectCost->date_start);
        $dateStart->setTime(0,0);
        $currDateTime = (new \DateTime())->setTime(0,0);
        $dateEnd = $currDateTime;

        if (!empty($indirectCost->date_end)) {
            $dateEnd = new \DateTime($indirectCost->date_end);
            if ($dateEnd > $currDateTime) {
                $dateEnd = clone $currDateTime;
            }
        }
        $dateEnd->setTime(0,0);
        $nextRun = $dateStart;
        $cronExpr = str_replace('?', '*', $indirectCost->cron_expr);
        $expression = new CronExpression($cronExpr);

        // 5000 here just prevents too long loop
        for ($i = 0; $i < 5000; $i++) {
            $this->info('New iteration');
            $nextRun = $expression->getNextRunDate($nextRun, 0, true);

            if ($nextRun > $dateEnd) {
                break;
            }

            $transactions = $this->generateTransactions($indirectCost, $nextRun);
            $this->info($transactions);
            $this->transactionsBuffer->put($transactions);
            $indirectCost->last_apply_date = $nextRun->format('Y-m-d H:i:s');

            // Hack to prevent infinity loop on next iteration
            $nextRun->setTime(23,59);
        }

        $indirectCost->save(false, ['last_apply_date']);
        $this->info('Apply recurring finished');
    }

    /**
     * @param IndirectCost $indirectCost
     * @param \DateTime $date
     * @return Transaction[]
     */
    protected function generateTransactions(IndirectCost $indirectCost, \DateTime $date): array
    {
        $transactions = [];

        $sellerId = $indirectCost->seller_id ?: '';
        $marketplaceId = $indirectCost->marketplace_id ?: '';
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        $transaction = new Transaction();
        $transaction->IndirectCostId = $indirectCost->id;
        $transaction->IndirectCostTypeId = $indirectCost->indirect_cost_type_id;
        $transaction->Currency = $indirectCost->currency_id;
        $transaction->Amount = (int)round($indirectCost->amount * -1 * $moneyAccuracy);
        $transaction->MarketplaceId = $marketplaceId;
        $transaction->SellerId = strtoupper($sellerId);
        $transaction->PostedDate = $date->format('Y-m-d 00:00:00');
        $transaction->MergeCounter = 1;
        $transaction->SellerSKU = '';
        $transaction->SellerOrderId = '';
        $transaction->AmazonOrderId = '';
        $transaction->CategoryId = 0;
        $transaction->COGCategoryId = 0;
        $transaction->Quantity = 0;
        $transaction->EventPeriodId = 0;
        $transaction->TransactionDate = $date->format('Y-m-d 00:00:00');
        $transaction->AmountEUR = $this
            ->currencyRateManager
            ->toBaseCurrency(
                $indirectCost->amount  * -1,
                $indirectCost->currency_id,
                $date
            ) * $moneyAccuracy;
        $transaction->AmountEUR = (int)round($transaction->AmountEUR);

        if (!empty($indirectCost->product_id)) {
            $product = Product::findOne($indirectCost->product_id);
            $transaction->SellerSKU = $product->sku;
            $transaction->ASIN = $product->asin;
            $transaction->SellerId = $product->seller_id;
            $transaction->MarketplaceId = $product->marketplace_id;
        }

        $transactions[] = $transaction;

        return $transactions;
    }
}
