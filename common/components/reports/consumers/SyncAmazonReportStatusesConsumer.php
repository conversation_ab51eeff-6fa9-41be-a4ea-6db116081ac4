<?php

namespace common\components\reports\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\ReadyReportsProcessor;
use common\components\reports\ReportsService;
use common\components\reports\ReportsSynchronizer;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\tokenService\Exception\NoTokenEntity;
use common\models\customer\AmazonReport;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use yii\db\Exception;

class SyncAmazonReportStatusesConsumer extends BaseConsumer
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected ReportsSynchronizer $reportsSynchronizer;

    protected MessagesSender $messagesSender;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->reportsSynchronizer = new ReportsSynchronizer();
        $this->messagesSender = new MessagesSender();
    }

    /**
     * @throws SellerNotFoundException
     * @throws Exception
     */
    public function __execute(AMQPMessage $msg)
    {
        $this->info($msg->body);

        try {
            $sellerId = $msg->body['sellerId'];
            $this->dbManager->setSellerId($sellerId);

            if (empty($sellerId)) {
                return ConsumerInterface::MSG_ACK;
            }

            $this->reportsSynchronizer->sync($sellerId);
            return ConsumerInterface::MSG_ACK;
        }catch (\Throwable $e) {
            $this->error($e);
        }

        return ConsumerInterface::MSG_ACK;
    }
}
