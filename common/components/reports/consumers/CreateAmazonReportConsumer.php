<?php

namespace common\components\reports\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\ReportsService;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use yii\db\Exception;

class CreateAmazonReportConsumer extends BaseConsumer
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected ReportsService $reportsService;

    protected MessagesSender $messagesSender;

    public function __construct()
    {

        $this->dbManager = \Yii::$app->dbManager;
        $this->reportsService = new ReportsService();
        $this->messagesSender = new MessagesSender();
    }

    /**
     * @throws SellerNotFoundException
     * @throws Exception
     */
    public function __execute(AMQPMessage $msg)
    {
        $this->info($msg->body);

        try {
            $dataProviderType = $msg->body['dataProviderType'];
            $sellerId = $msg->body['sellerId'];
            $this->dbManager->setSellerId($sellerId);

            if (empty($sellerId)) {
                return ConsumerInterface::MSG_ACK;
            }

            $processingResult = $this->reportsService->createReportsInAmazon($sellerId, $dataProviderType);
            $this->info("create reports in amazon {$processingResult->processedCount} items");
            return ConsumerInterface::MSG_ACK;
        }catch (\Throwable $e) {
            $this->error($e);
        }

        return ConsumerInterface::MSG_ACK;
    }
}
