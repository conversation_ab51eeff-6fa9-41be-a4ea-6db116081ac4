<?php

namespace common\components\reports\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\exception\ConfigurationNotFoundException;
use common\components\reports\ReadyReportsProcessor;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\tokenService\Exception\NoTokenEntity;
use common\models\customer\AmazonReport;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class HandleReadyReportConsumer extends BaseConsumer
{
    public const MAX_INVOKES_BEFORE_RESTART = 200;

    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected ReadyReportsProcessor $readyReportsProcessor;

    protected MessagesSender $messagesSender;

    public function __construct()
    {
        ini_set('memory_limit', '2048M');
        $this->dbManager = \Yii::$app->dbManager;
        $this->readyReportsProcessor = new ReadyReportsProcessor();
        $this->messagesSender = new MessagesSender();
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info($msg->body);

        try {
            $reportId = $msg->body['amazonReportId'];
            $this->dbManager->setSellerId($msg->body['sellerId']);
        } catch (\Throwable $e) {
            $this->error($e);
            return ConsumerInterface::MSG_ACK;
        }

        /** @var AmazonReport $amazonReport */
        $amazonReport = AmazonReport::findOne($reportId);

        if (empty($amazonReport)) {
            return ConsumerInterface::MSG_ACK;
        }

        try {
            if ($amazonReport->status !== AmazonReport::STATUS_QUEUED) {
                $this->info("Inconsistent report status $amazonReport->status");
                return ConsumerInterface::MSG_ACK;
            }

            if ($amazonReport->seller_id != $this->dbManager->getSellerId()) {
                throw new \Exception('Seller ID mismatch');
            }

            $amazonReport->setStatus(AmazonReport::STATUS_IN_PROGRESS);
            $processingResult = $this->readyReportsProcessor->process($amazonReport);
            $amazonReport->setStatus(
                AmazonReport::STATUS_DONE,
                "processed {$processingResult->processedCount} items"
            );
            return ConsumerInterface::MSG_ACK;
        } catch (NoTokenEntity $e) {
            $this->info($e->getMessage());
            $amazonReport->delete();
            return ConsumerInterface::MSG_ACK;
        } catch (QuotaExceededException $e) {
            $this->error($e);
            $amazonReport->setStatus(AmazonReport::STATUS_WAITING, 'Renew due to quota exception');
            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $amazonReport->log('Error while processing report: ' . $e->getMessage());
            $amazonReport->setStatus(AmazonReport::STATUS_ERROR);

            if ($e instanceof ConfigurationNotFoundException && !empty($amazonReport->stop_repeating_at)) {
                $amazonReport->stop_repeating_at = date('Y-m-d H:i:s');
            }

            // Undefined bug in amazon - a lot of errors in rollbar
            if (empty($e->getMessage()) || false === strpos('Invalid reportDocumentId', $e->getMessage())) {
                $this->error($e);
            }
        }

        return ConsumerInterface::MSG_ACK;
    }
}
