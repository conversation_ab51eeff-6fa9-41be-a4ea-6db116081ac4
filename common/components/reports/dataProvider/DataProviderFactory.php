<?php

namespace common\components\reports\dataProvider;

use common\components\reports\Configurator;

class DataProviderFactory
{
    public const PROVIDER_SELLING_API = 'selling_api';

    protected Configurator $configurator;

    public function __construct()
    {
        $this->configurator = new Configurator();
    }

    public function getDataProvider(?string $providerType): DataProviderInterface
    {
        foreach ($this->configurator->getRegisteredDataProviders() as $dataProvider) {
            if ($dataProvider->getType() === $providerType) {
                return $dataProvider;
            }
        }

        return new SellingApiProvider();
    }
}