<?php

namespace common\components\reports\dataProvider\traits;

use common\components\core\db\dbManager\DbManager;
use common\components\reports\dto\Configuration;
use common\models\ads\AmazonAdsProfile;

trait AmazonAdsMultiProfileConfigurationTrait
{
    public function buildConfigurationsForProfiles(array $configurationPrototypes, string $sellerId): array
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $adsProfiles = AmazonAdsProfile::find()
            ->joinWith(['amazonAdsAccount'])
            ->where(['seller_id' => $sellerId])
            ->cache(20)
            ->all();
        $configurations = [];
        $isActive = $dbManager->isSellerActive();
        /** @var AmazonAdsProfile[] $adsProfiles */
        foreach ($adsProfiles as $adsProfile) {
            /**
             * @var Configuration[] $configurationPrototypes
             */
            foreach ($configurationPrototypes as $configurationPrototype) {
                $profileConfiguration = clone $configurationPrototype;

                if ($isActive) {
                    $isActive = $adsProfile->getAmazonAdsAccount()->one()->canMakeRequestToAmazon();
                }

                $profileConfiguration->setIsActive($isActive);
                $profileConfiguration->setUniqueType(implode('_', [
                    $profileConfiguration->getType(),
                    $adsProfile->country_code,
                    $adsProfile->id
                ]));
                $profileConfiguration->setExtraIfo(
                    array_merge(
                        $profileConfiguration->getExtraIfo(),
                        [
                            'profile_id' => $adsProfile->id,
                            'region' => $adsProfile->region,
                            'account_id' => $adsProfile->account_id,
                        ]
                    )
                );
                $configurations[] = $profileConfiguration;
            }
        }

        return $configurations;
    }
}