<?php

namespace common\components\reports\dataProvider;

use common\components\reports\dto\ExternalReport;
use common\models\customer\AmazonReport;

interface RemoteFileDataProviderInterface extends DataProviderInterface
{
    public function getMaxReportsToCreatePerIteration(): int;
    public function getMaxReportsToSyncPerIteration(): int;
    public function createReportInAmazon(AmazonReport $amazonReport, array $config = []): string;
    public function getExternalReport(AmazonReport $amazonReport): ExternalReport;
    public function mapStatus(string $statusToMap): string;
    public function getUrl(AmazonReport $amazonReport): string;
}