<?php

namespace common\components\reports\dataProvider;

use common\components\amazonAds\api\Client;
use common\components\amazonAds\api\ClientBuilder;
use common\components\amazonAds\CostsApplier;
use common\components\core\db\dbManager\DbManager;
use common\components\reports\dataProvider\traits\AmazonAdsMultiProfileConfigurationTrait;
use common\components\reports\dto\Configuration;
use common\components\reports\dto\ExternalReport;
use common\components\reports\exception\CancelOrderException;
use common\components\reports\handler\SbAdGroupsHandler;
use common\models\customer\AmazonReport;
use yii\caching\CacheInterface;

class AmazonAdsReportsV2Provider implements RemoteFileDataProviderInterface
{
    use AmazonAdsMultiProfileConfigurationTrait;

    public const STATUS_PENDING = 'PENDING';
    public const STATUS_PROCESSING = 'PROCESSING';
    public const STATUS_IN_PROGRESS = 'IN_PROGRESS';
    public const STATUS_COMPLETED = 'COMPLETED';
    public const STATUS_SUCCESS = 'SUCCESS';
    public const STATUS_FAILURE = 'FAILURE';
    public const STATUS_FAILED = 'FAILED';

    public CacheInterface $cache;
    public DbManager $dbManager;
    public ClientBuilder $clientBuilder;

    public function __construct()
    {
        $this->cache = \Yii::$app->cache;
        $this->dbManager = \Yii::$app->dbManager;
        $this->clientBuilder = new ClientBuilder();
    }

    public function getType(): string
    {
        return 'amazon_ads_reports_v2';
    }

    public function getConfigurations(): array
    {
        return $this->buildConfigurationsForProfiles([
            (new Configuration())
                ->setType('AMAZON_ADS_SB_GROUPS')
                ->setHandlerClasses([
                    new SbAdGroupsHandler()
                ])
                ->setDailyStrategy()
                ->setPeriodMaxSizeMinutes(60 * 24)
                ->setPeriodicalUpdateStrategy(
                    60 * 24 * (CostsApplier::DATA_STABILISATION_TIME_DAYS - 1),
                    60
                )
                ->setMinDateOffsetMinutes(60 * 24 * 60)
                ->setExtraIfo([
                    'reportType' => Client::CAMPAIGN_TYPE_SPONSORED_BRANDS,
                    'recordType' => 'adGroups',
                    'creativeType' => 'all',
                    'metrics' => [
                        'adGroupId',
                        'campaignId',
                        'clicks',
                        'cost',
                        'attributedSales14dSameSKU',
                        'impressions',
                    ],
                ])
        ], $this->dbManager->getSellerId());
    }

    public function getMaxReportsToCreatePerIteration(): int
    {
        return 10;
    }

    public function getMaxReportsToSyncPerIteration(): int
    {
        return 10;
    }

    public function getUrl(AmazonReport $amazonReport): string
    {
        $extraInfo = $amazonReport->extra_info;
        $client = $this->clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);
        $response = $client->getReport($amazonReport->amazon_id);
        $downloaded = $client->download($response["location"]);
        return $downloaded['responseInfo']['url'];
    }

    /**
     * @throws CancelOrderException
     */
    public function createReportInAmazon(AmazonReport $amazonReport, array $config = []): string
    {
        $metrics = $config['metrics'];
        $reportType = $config['reportType'];
        $recordType = $config['recordType'];
        $creativeType = $config['creativeType'];

        $extraInfo = $amazonReport->extra_info;
        $clientBuilder = new ClientBuilder();

        $client = $clientBuilder->getApiClient(
            $extraInfo['account_id'],
            $extraInfo['region'],
            $extraInfo['profile_id']
        );
        $response = $client->requestReport(
            $recordType,
            [
                'reportDate' => (new \DateTime($amazonReport->start_date))->format('Ymd'),
                'metrics' => implode(',', $metrics),
                'creativeType' => $creativeType,
                'reportType' => $reportType,
            ]
        );

        $decodedResponse = json_decode($response['response'], true);

        if ($response['code'] >= 400) {
            if ($decodedResponse['code'] === 'UNAUTHORIZED') {
                throw new CancelOrderException($response['response']);
            }
            throw new \Exception($response['response']);
        }

        return $decodedResponse['reportId'];

    }

    public function getExternalReport(AmazonReport $amazonReport): ExternalReport
    {
        $externalReport = new ExternalReport();
        $extraInfo = $amazonReport->extra_info;

        $client = $this->clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);
        $response = $client->getReport($amazonReport->amazon_id);
        $externalReport->status = $response['status'];

        return $externalReport;
    }

    public function mapStatus(string $statusToMap): string
    {
        switch ($statusToMap) {
            case self::STATUS_PENDING:
                return AmazonReport::STATUS_WAITING;
            case self::STATUS_PROCESSING:
            case self::STATUS_IN_PROGRESS:
                return AmazonReport::STATUS_IN_PROGRESS;
            case self::STATUS_COMPLETED:
            case self::STATUS_SUCCESS:
                return AmazonReport::STATUS_DONE;
            case self::STATUS_FAILURE:
            case self::STATUS_FAILED:
                return AmazonReport::STATUS_ERROR;
        }

        throw new \Exception("Unknown amazon ads status $statusToMap");
    }
}
