<?php

namespace common\components\reports\dataProvider;

use common\components\amazonAds\api\ClientBuilder;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\reports\dto\Configuration;
use common\components\reports\dataProvider\traits\AmazonAdsMultiProfileConfigurationTrait;
use common\components\reports\handler\SbAdsHandler;
use common\models\customer\AmazonReport;
use yii\caching\CacheInterface;

class AmazonAdsApiCallProvider implements AlreadyPreparedDataProviderInterface
{
    use LogToConsoleTrait;
    use AmazonAdsMultiProfileConfigurationTrait;

    public CacheInterface $cache;
    public DbManager $dbManager;
    public ClientBuilder $clientBuilder;

    public function __construct()
    {
        $this->cache = \Yii::$app->cache;
        $this->dbManager = \Yii::$app->dbManager;
        $this->clientBuilder = new ClientBuilder();
    }

    public function getType(): string
    {
        return 'amazon_ads_api_call';
    }

    public function getConfigurations(): array
    {
        return $this->buildConfigurationsForProfiles([
            (new Configuration())
                ->setType('AMAZON_ADS_SB_ADS')
                ->setHandlerClasses([
                    new SbAdsHandler()
                ])
                ->setPeriodMinSizeMinutes(60 * 12)
                ->setExtraIfo([
                    'function' => 'listSponsoredBrandAds',
                    'params' => [
                        'stateFilter' => [
                            'include' => [
                                "PAUSED",
                                "ARCHIVED",
                                "ENABLED"
                            ]
                        ]
                    ]
                ])
            ,
        ], $this->dbManager->getSellerId());
    }

    public function getData(AmazonReport $amazonReport): array
    {
        $extraInfo = $amazonReport->extra_info;
        $client = $this->clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);

        $data = [];
        $nextToken = null;
        $i = 0;
        $functionName = $amazonReport->extra_info['function'] ?? null;
        $params = $amazonReport->extra_info['params'] ?? [];

        do {
            $this->info("Getting data for {$amazonReport->amazon_type} with nextToken {$nextToken}");
            $response = $client->{$functionName}(
                array_merge($params, [
                    'nextToken' => $nextToken
                ])
            );
            $data = array_merge($data, $response['ads']);
            $nextToken = $response['nextToken'] ?? null;
            $i++;
        } while ($nextToken && $i < 50);

        return $data;
    }
}