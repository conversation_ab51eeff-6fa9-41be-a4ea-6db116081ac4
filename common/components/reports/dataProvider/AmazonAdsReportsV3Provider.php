<?php

namespace common\components\reports\dataProvider;

use common\components\amazonAds\api\ClientBuilder;
use common\components\amazonAds\CostsApplier;
use common\components\reports\dataProvider\traits\AmazonAdsMultiProfileConfigurationTrait;
use common\components\reports\dto\Configuration;
use common\components\reports\dto\ExternalReport;
use common\components\reports\handler\SdAdvertisedProductHandler;
use common\components\reports\handler\SpAdvertisedProductHandler;
use common\components\tokenService\Exception\NoTokenEntity;
use common\models\ads\AmazonAdsProfile;
use common\models\customer\AmazonReport;

class AmazonAdsReportsV3Provider extends AmazonAdsReportsV2Provider
{
    use AmazonAdsMultiProfileConfigurationTrait;

    public function getType(): string
    {
        return 'amazon_ads_reports_v3';
    }

    public function getConfigurations(): array
    {
        return $this->buildConfigurationsForProfiles([
            (new Configuration())
                ->setType('AMAZON_ADS_SP_ADVERTISED_PRODUCT')
                ->setHandlerClasses([
                    new SpAdvertisedProductHandler()
                ])
                ->setDailyStrategy()
                ->setPeriodicalUpdateStrategy(
                    60 * 24 * (CostsApplier::DATA_STABILISATION_TIME_DAYS - 1),
                    60
                )
                ->setPeriodMaxSizeMinutes(60 * 24 * 30)
                ->setMinDateOffsetMinutes(60 * 24 * 95)
                ->setExtraIfo([
                    'adProduct' => 'SPONSORED_PRODUCTS',
                    'reportType' => 'spAdvertisedProduct',
                    'columns' => [
                        'date',
                        'campaignId',
                        'adId',
                        'impressions',
                        'purchases1d',
                        'purchasesSameSku1d',
                        'unitsSoldClicks1d',
                        'unitsSoldSameSku1d',
                        'sales1d',
                        'attributedSalesSameSku1d',
                        'attributedSalesSameSku14d',
                        'clicks',
                        'cost',
                        'campaignBudgetCurrencyCode',
                        'spend',
                        'advertisedAsin',
                        'advertisedSku',
                    ],
                    'groupBy' => [
                        'advertiser'
                    ],
                    'timeUnit' => 'DAILY'
                ]),
            (new Configuration())
                ->setType('AMAZON_ADS_SD_ADVERTISED_PRODUCT')
                ->setHandlerClasses([
                    new SdAdvertisedProductHandler()
                ])
                ->setDailyStrategy()
                ->setPeriodicalUpdateStrategy(
                    60 * 24 * (CostsApplier::DATA_STABILISATION_TIME_DAYS - 1),
                    60
                )
                ->setPeriodMaxSizeMinutes(60 * 24 * 30)
                ->setMinDateOffsetMinutes(60 * 24 * 65)
                ->setExtraIfo([
                    'adProduct' => 'SPONSORED_DISPLAY',
                    'reportType' => 'sdAdvertisedProduct',
                    'columns' => [
                        'date',
                        'campaignId',
                        'adId',
                        'impressions',
                        'purchases',
                        'purchasesClicks',
                        'purchasesPromotedClicks',
                        'sales',
                        'salesClicks',
                        'salesPromotedClicks',
                        'unitsSold',
                        'unitsSoldClicks',
                        'clicks',
                        'cost',
                        'campaignBudgetCurrencyCode',
                        'promotedAsin',
                        'promotedSku',
                    ],
                    'groupBy' => [
                        'advertiser'
                    ],
                    'timeUnit' => 'DAILY'
                ]),
        ], $this->dbManager->getSellerId());
    }

    public function getUrl(AmazonReport $amazonReport): string
    {
        $extraInfo = $amazonReport->extra_info;
        $client = $this->clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);
        $report = $client->getReportV3($amazonReport->amazon_id);
        return $report['url'];
    }

    public function createReportInAmazon(AmazonReport $amazonReport, array $config = []): string
    {
        $columns = $config['columns'];
        $groupBy = $config['groupBy'];
        $reportType = $config['reportType'];
        $adProduct = $config['adProduct'];
        $timeUnit = $config['timeUnit'];
        $extraInfo = $amazonReport->extra_info;
        $clientBuilder = new ClientBuilder();
        $client = $clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);
        $report = $client->requestReportV3(
            new \DateTime($amazonReport->start_date),
            new \DateTime($amazonReport->end_date),
            $adProduct,
            $reportType,
            $columns,
            $groupBy,
            $timeUnit
        );

        return $report['reportId'];
    }

    public function getExternalReport(AmazonReport $amazonReport): ExternalReport
    {
        try {
            $externalReport = new ExternalReport();
            $extraInfo = $amazonReport->extra_info;
            $client = $this->clientBuilder->getApiClient($extraInfo['account_id'], $extraInfo['region'], $extraInfo['profile_id']);
            $report = $client->getReportV3($amazonReport->amazon_id);
            $externalReport->status = $report['status'];
        } catch (\Throwable $e) {
            $profileId = $amazonReport->extra_info['profile_id'] ?? null;
            if (!empty($profileId)) {
                $profile = AmazonAdsProfile::findOne($profileId);
                if (empty($profile)) {
                    throw new NoTokenEntity();
                }
            }
            throw $e;
        }

        return $externalReport;
    }
}
