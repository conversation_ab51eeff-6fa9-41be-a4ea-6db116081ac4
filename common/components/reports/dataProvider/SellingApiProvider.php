<?php

namespace common\components\reports\dataProvider;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\reports\dto\Configuration;
use common\components\reports\dto\ExternalReport;
use common\components\reports\handler\EstimatedFBAFeesHandler;
use common\components\reports\handler\FBAInventoryPlanningDataHandler;
use common\components\reports\handler\FBAMyiUnsuppressedInventoryDataHandler;
use common\components\reports\handler\FBAReturnHandler;
use common\components\reports\handler\FBMReturnHandler;
use common\components\reports\handler\MerchantListingsDataLiterHandler;
use common\components\reports\handler\OrderItemsHandler;
use common\components\reports\handler\ReferralFeePreviewHandler;
use common\components\reports\handler\FBAStorageFeeChargesHandler;
use common\components\reports\handler\FBALongTermStorageFeeChargesHandler;
use common\components\sellingApi\apiProxy\ReportsApi;
use common\components\sellingApi\apiProxy\SellersApi;
use common\components\tokenService\Exception\IgnoreReport;
use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\Product;
use common\models\Seller;
use SellingPartnerApi\ApiException;
use SellingPartnerApi\Model\ReportsV20210630\Report;
use SellingPartnerApi\Model\SellersV1\MarketplaceParticipation;
use SellingPartnerApi\ReportType;
use yii\caching\CacheInterface;

class SellingApiProvider implements RemoteFileDataProviderInterface, BatchSyncInterface
{
    use LogToConsoleTrait;

    protected const GET_REPORTS_RATE = 0.0222;

    protected CacheInterface $cache;
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->cache = \Yii::$app->cache;
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getType(): string
    {
        return 'selling_api_reports';
    }

    /**
     * @return Configuration[]
     */
    public function getConfigurations(): array
    {
        $marketplaceIds = $this->getApplicableMarketplaces($this->dbManager->getSellerId());
        sort($marketplaceIds);

        $configurations = [
            (new Configuration())
                ->setType(ReportType::GET_FLAT_FILE_ALL_ORDERS_DATA_BY_LAST_UPDATE_GENERAL['name'])
                ->setHandlerClasses([
                    new OrderItemsHandler()
                ])
                ->setPeriodMaxSizeMinutes(60 * 24 * 29)
                ->setPeriodMinSizeMinutes(YII_ENV_PROD ? 10 : 30)
                ->setPeriodOffsetMinutes(0)
                ->setMinDateOffsetMinutes(60 * 24 * 365 * 2),

            (new Configuration())
                ->setType(ReportType::GET_FBA_STORAGE_FEE_CHARGES_DATA['name'])
                ->setHandlerClasses([
                    new FBAStorageFeeChargesHandler()
                ])
                ->setMonthlyStrategy(60 * 24)
                ->setPeriodicalUpdateStrategy(60 * 24 * 5, 60 * 6)
                ->setMinDateOffsetMinutes(60 * 24 * 365 * 2),

            (new Configuration())
                ->setType(ReportType::GET_FBA_FULFILLMENT_LONGTERM_STORAGE_FEE_CHARGES_DATA['name'])
                ->setHandlerClasses([
                    new FBALongTermStorageFeeChargesHandler()
                ])
                ->setMonthlyStrategy(60 * 24)
                ->setPeriodicalUpdateStrategy(60 * 24 * 5, 60 * 6)
                ->setMinDateOffsetMinutes(60 * 24 * 365 * 2),
        ];

        foreach ($marketplaceIds as $marketplaceId) {
            $configurations[] = (new Configuration())
                ->setType(implode('_', [ReportType::GET_REFERRAL_FEE_PREVIEW_REPORT['name'], $marketplaceId]))
                ->setHandlerClasses([
                    new ReferralFeePreviewHandler()
                ])
                ->setDailyStrategy(60 * 24)
                ->setPeriodMaxSizeMinutes(60 * 24)
                ->setPeriodicalUpdateStrategy(0, 60 * 6)
                ->setExtraIfo([
                    'reportType' => ReportType::GET_REFERRAL_FEE_PREVIEW_REPORT['name'],
                    'marketplaceIds' => [$marketplaceId]
                ]);
            $configurations[] = (new Configuration())
                ->setType(implode('_', [ReportType::GET_MERCHANT_LISTINGS_DATA_LITER['name'], $marketplaceId]))
                ->setHandlerClasses([
                    new MerchantListingsDataLiterHandler()
                ])
                ->setDailyStrategy(60 * 24)
                ->setPeriodMaxSizeMinutes(60 * 24)
                ->setPeriodicalUpdateStrategy(0, 60 * 6)
                ->setExtraIfo([
                    'reportType' => ReportType::GET_MERCHANT_LISTINGS_DATA_LITER['name'],
                    'marketplaceIds' => [$marketplaceId]
                ]);
            $configurations[] = (new Configuration())
                ->setType(implode('_', [ReportType::GET_FBA_INVENTORY_PLANNING_DATA['name'], $marketplaceId]))
                ->setHandlerClasses([
                    new FBAInventoryPlanningDataHandler()
                ])
                ->setDailyStrategy(60 * 24)
                ->setPeriodMaxSizeMinutes(60 * 24)
                ->setPeriodicalUpdateStrategy(0, 60 * 6)
                ->setExtraIfo([
                    'reportType' => ReportType::GET_FBA_INVENTORY_PLANNING_DATA['name'],
                    'marketplaceIds' => [$marketplaceId]
                ]);
        }

        if (empty($marketplaceIds)) {
            return $configurations;
        }

        $uniqId = md5(implode('_', $marketplaceIds));
        $configurations[] = (new Configuration())
            ->setType(ReportType::GET_FBA_ESTIMATED_FBA_FEES_TXT_DATA['name'] . '_' . $uniqId)
            ->setHandlerClasses([
                new EstimatedFBAFeesHandler()
            ])
            ->setDailyStrategy(60 * 12)
            ->setPeriodMaxSizeMinutes(60 * 24)
            ->setPeriodicalUpdateStrategy(0, 60 * 6)
            ->setExtraIfo([
                'reportType' => ReportType::GET_FBA_ESTIMATED_FBA_FEES_TXT_DATA['name'],
                'marketplaceIds' => $marketplaceIds
            ]);
        $configurations[] = (new Configuration())
            ->setType(implode('_', [ReportType::GET_FBA_MYI_UNSUPPRESSED_INVENTORY_DATA['name'], $uniqId]))
            ->setHandlerClasses([
                new FBAMyiUnsuppressedInventoryDataHandler()
            ])
            ->setDailyStrategy(60 * 24)
            ->setPeriodMaxSizeMinutes(60 * 24)
            ->setPeriodicalUpdateStrategy(0, 60 * 6)
            ->setExtraIfo([
                'reportType' => ReportType::GET_FBA_MYI_UNSUPPRESSED_INVENTORY_DATA['name'],
                'marketplaceIds' => $marketplaceIds
            ]);
        $configurations[] = (new Configuration())
            ->setType(implode('_', [ReportType::GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA['name'], $uniqId]))
            ->setHandlerClasses([
                new FBAReturnHandler()
            ])
            ->setPeriodMaxSizeMinutes(60 * 24 * 29)
            ->setPeriodMinSizeMinutes(60)
            ->setPeriodOffsetMinutes(30)
            ->setMinDateOffsetMinutes(60 * 24 * 365 * 2)
            ->setExtraIfo([
                'reportType' => ReportType::GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA['name'],
                'marketplaceIds' => $marketplaceIds
            ]);
        $configurations[] = (new Configuration())
            ->setType(implode('_', [ReportType::GET_FLAT_FILE_RETURNS_DATA_BY_RETURN_DATE['name'], $uniqId]))
            ->setHandlerClasses([
                new FBMReturnHandler()
            ])
            ->setPeriodMaxSizeMinutes(60 * 24 * 29)
            ->setPeriodMinSizeMinutes(60)
            ->setPeriodOffsetMinutes(30)
            ->setMinDateOffsetMinutes(60 * 24 * 365 * 2)
            ->setExtraIfo([
                'reportType' => ReportType::GET_FLAT_FILE_RETURNS_DATA_BY_RETURN_DATE['name'],
                'marketplaceIds' => $marketplaceIds
            ]);

        return $configurations;
    }

    public function getMaxReportsToCreatePerIteration(): int
    {
        return 1;
    }

    public function getMaxReportsToSyncPerIteration(): int
    {
        return 500;
    }

    public function getUrl(AmazonReport $amazonReport): string
    {
        $extraInfo = $amazonReport->extra_info;
        $amazonDocumentId = $amazonReport->amazon_document_id ?? $extraInfo['amazonDocumentId'];
        $seller = Seller::find()->where(['id' => $amazonReport->seller_id])->one(\Yii::$app->db);
        $reportsApi = new ReportsApi($seller->id, $seller->region);
        $document = $reportsApi->getReportDocument($amazonDocumentId, $amazonReport->amazon_type);

        return $document->getUrl();
    }

    /**
     * @throws \DateMalformedStringException
     * @throws ApiException
     */
    public function createReportInAmazon(AmazonReport $amazonReport, array $config = []): string
    {
        $seller = Seller::find()->where(['id' => $amazonReport->seller_id])->one(\Yii::$app->db);

        $marketplaceIds = $config['marketplaceIds'] ?? $this->getApplicableMarketplaces($seller->id);

        // Validate that all marketplaceIds are applicable for this seller's region
        if (!empty($config['marketplaceIds'])) {
            $applicableMarketplaces = $this->getApplicableMarketplaces($seller->id);
            $marketplaceIds = array_intersect($config['marketplaceIds'], $applicableMarketplaces);

            if (empty($marketplaceIds)) {
                throw new \InvalidArgumentException(
                    "No valid marketplaces found for seller {$seller->id} in region {$seller->region}"
                );
            }
        }

        $reportType = $config['reportType'] ?? $amazonReport->amazon_type;

        $startDate = new \DateTime($amazonReport->start_date);
        $endDate = new \DateTime($amazonReport->end_date);

        $reportsApi = new ReportsApi($seller->id, $seller->region);
        $result = $reportsApi->createReport([
            'reportType' => $reportType,
            'dataStartTime' => $startDate->format('c'),
            'dataEndTime' => $endDate->format('c'),
            'marketplaceIds' => $marketplaceIds
        ]);

        return $result->getReportId();
    }

    public function getExternalReport(AmazonReport $amazonReport): ExternalReport
    {
        throw new \Exception('Use getStatusesBatch method instead');
    }

    public function mapStatus(string $statusToMap): string
    {
        return $statusToMap;
    }

    /**
     * @throws ApiException
     */
    public function getSellerMarketplaces(string $sellerId): array
    {
        // Using cache here because this method is called many times
        $cacheKey = "seller_marketplaces_{$sellerId}";
        $marketplaceIds = $this->cache->get($cacheKey);

        if ($marketplaceIds !== false) {
            return $marketplaceIds;
        }

        $seller = Seller::findOne($sellerId);
        $sellersApi = new SellersApi($seller->id, $seller->region);
        $result = $sellersApi->getMarketplaceParticipations();

        $marketplaceIds = [];
        /** @var MarketplaceParticipation $marketplace */
        foreach ($result->getPayload() as $marketplace) {
            $marketplaceIds[] = $marketplace->getMarketplace()->getId();
        }

        $this->cache->set($cacheKey, $marketplaceIds, 60 * 20);

        return $marketplaceIds;
    }

    public function getLatestExternalReports(string $sellerId, array $reportTypes = null): array
    {
        $externalReports = [];
        $this->info('Getting external reports for seller ' . $sellerId);

        if (null === $reportTypes) {
            $reportTypes = [];
            foreach ($this->getConfigurations() as $configuration) {
                $reportTypes[] = $configuration->getExtraIfo()['reportType'] ?? $configuration->getType();
            }
            $reportTypes = array_unique($reportTypes);
        }

        /** @var Seller $seller */
        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $reportsApi = new ReportsApi($seller->id, $seller->region);
        $nextToken = null;

        while (true) {
            if (!empty($nextToken)) {
                $response = $reportsApi->getReports(
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    $nextToken
                );
            } else {
                $response = $reportsApi->getReports(
                    $reportTypes,
                    null,
                    null,
                    100,
                    (new \DateTime())->modify('-3 days')->format('c'),
                    null,
                    $nextToken
                );
            }

            /** @var Report[] $reports */
            $reports = $response->getReports();

            $nextToken = $response->getNextToken();
            $this->info([
                'countReports' => count($reports),
                'nextToken' => $nextToken,
            ]);

            foreach ($reports as $report) {
                try {
                    $externalReport = new ExternalReport();
                    $externalReport->id = $report->getReportId();
                    $externalReport->status = $report->getProcessingStatus();
                    $externalReport->dateStart = new \DateTime($report->getDataStartTime());
                    $externalReport->dateEnd = new \DateTime($report->getDataEndTime());
                    $externalReport->createdAt = new \DateTime($report->getCreatedTime());
                    $externalReport->extraInfo = [
                        'marketplaceIds' => $report->getMarketplaceIds(),
                        'reportType' => $report->getReportType(),
                        'amazonDocumentId' => $report->getReportDocumentId(),
                    ];
                    $externalReports[] = $externalReport;
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }

            // Temporary only one page, to prevent 60 seconds sleep between next page loading
            break;
            if (empty($nextToken)) {
                break;
            }

            if (count($reports) === 0) {
                break;
            }

            $this->sleep(self::GET_REPORTS_RATE);
        }

        return $externalReports;
    }

    public function generateUniqueIdBasedOnExtraInfo(array $extraInfo): string
    {
        $uniqueId = $extraInfo['reportType'] ?? '';
        $marketplaceIds = $extraInfo['marketplaceIds'] ?? [];
        sort($marketplaceIds);

        if (count($marketplaceIds) === 1) {
            return $uniqueId . '_' . $marketplaceIds[0];
        }

        return $uniqueId . '_' . md5(implode('_', $marketplaceIds));
    }

    protected function sleep(float $rate): void
    {
        $sleepSeconds = 1 / $rate;
        $sleepSeconds = ceil($sleepSeconds);
        $sleepSeconds += $sleepSeconds * 0.1;

        $this->info("Sleeping for {$sleepSeconds} seconds to prevent quota limit");
        sleep($sleepSeconds);
    }

    /**
     * @throws ApiException
     */
    public function getApplicableMarketplaces(string $sellerId): array
    {
        $seller = Seller::findOne($sellerId);
        $allMarketplaces = $this->getSellerMarketplaces($sellerId);

        $this->info("All seller marketplaces: " . implode(', ', $allMarketplaces));

        $regionCompatibleMarketplaces = $this->filterMarketplacesByRegion($allMarketplaces, $seller->region);

        $this->info("Region compatible marketplaces for {$seller->region}: " . implode(', ', $regionCompatibleMarketplaces));

        return $regionCompatibleMarketplaces;
    }

    /**
     * Filter marketplaces by seller region using database
     */
    private function filterMarketplacesByRegion(array $marketplaceIds, string $region): array
    {
        $cacheKey = "region_marketplaces_{$region}";
        $regionMarketplaceIds = $this->cache->get($cacheKey);

        if ($regionMarketplaceIds !== false) {
            return array_intersect($marketplaceIds, $regionMarketplaceIds);
        }

        $regionMarketplaceIds = AmazonMarketplace::find()
            ->select('id')
            ->where([
                'is_active' => true,
                'region' => $region
            ])
            ->column();

        $this->cache->set($cacheKey, $regionMarketplaceIds, 60 * 60);

        return array_intersect($marketplaceIds, $regionMarketplaceIds);
    }
}
