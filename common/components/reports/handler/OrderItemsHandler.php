<?php

namespace common\components\reports\handler;

use common\components\COGSync\ProductsSaver;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\services\order\dataConverter\OrderFromAmazonReportToOurOrderConverter;
use common\components\services\order\dataConverter\OrderFromAmazonReportToOurOrderItemConverter;
use common\components\services\order\StoreOrderItemsServiceV2;
use common\components\services\order\StoreOrdersServiceV2;
use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\caching\CacheInterface;
use yii\helpers\StringHelper;

class OrderItemsHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    public const FULFILMENT_CHANNEL_MAP = [
        'Amazon' => AmazonOrder::FULFILMENT_CHANNEL_AFN,
        'Merchant' => AmazonOrder::FULFILMENT_CHANNEL_MFN,
    ];

    public const PRODUCT_STOCK_TYPE_MAP = [
        'Amazon' => Product::STOCK_TYPE_FBA,
        'Merchant' => Product::STOCK_TYPE_FBM,
    ];

    protected DbManager $dbManager;
    protected CacheInterface $cache;
    protected CustomerConfig $customerConfig;
    protected ProductsSaver $productsSaver;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->cache = \Yii::$app->get('cache');
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->productsSaver = new ProductsSaver();
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $useRealtimeOrderDataUpdate = $this->customerConfig
            ->get(CustomerConfig::PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES);

        // Old logic
        if (!$useRealtimeOrderDataUpdate) {
            $this->saveOrders($reportData);
            $this->saveOrderItems($reportData, $amazonReport);
            $this->saveProducts($reportData);
            return;
        }

        $amazonOrderToOurOrderConverter = new OrderFromAmazonReportToOurOrderConverter();
        $amazonOrderToOurOrderItemConverter = new OrderFromAmazonReportToOurOrderItemConverter();

        $amazonOrders = [];
        $amazonOrderItems = [];

        foreach ($reportData as $reportDatum) {
            $marketplace = AmazonMarketplace::getBySalesChannel(strtolower($reportDatum['sales-channel']));

            if (empty($marketplace)
                || $marketplace->id == AmazonMarketplace::NON_AMAZON
                || empty($reportDatum['sku'])
            ) {
                continue;
            }

            $amazonOrders[] = $amazonOrderToOurOrderConverter
                ->convert(
                    $reportDatum,
                    $amazonReport->seller_id,
                    $marketplace->id
                );
            $amazonOrderItems[] = $amazonOrderToOurOrderItemConverter
                ->convert(
                    $reportDatum,
                    $amazonReport->id,
                    $marketplace->id
                );
        }

        $storeOrdersService = new StoreOrdersServiceV2($amazonReport->seller_id);
        $storeOrdersService->save($amazonOrders);

        $storeOrderItemsService = new StoreOrderItemsServiceV2($amazonReport->seller_id);
        $storeOrderItemsService->save($amazonOrderItems, $amazonOrders);
    }

    protected function saveOrderItems(array $reportData, AmazonReport $amazonReport): void
    {
        $this->info('Saving order items');

        $orderIds = [];
        foreach ($reportData as $reportDatum) {
            $orderIds[] = $reportDatum['amazon-order-id'];
        }

        $allExistingOrderItems = AmazonOrderItem::find()
            ->select([
                'ao.last_update_date',
                'order_id',
                'order_item_id',
                'sku',
            ])
            ->where(['order_id' => $orderIds])
            ->leftJoin(AmazonOrder::tableName() . ' as ao', 'amazon_order_id = order_id')
            ->asArray()
            ->all();

        foreach ($allExistingOrderItems as $k => $orderItem) {
            $allExistingOrderItems[$orderItem['order_id'] . '_'. $orderItem['sku']][] = $orderItem;
            unset($allExistingOrderItems[$k]);
        }

        $orderItemsToSave = [];

        foreach ($reportData as $reportDatum) {
            try {
                $marketplace = AmazonMarketplace::getBySalesChannel(strtolower($reportDatum['sales-channel']));
                if (empty($marketplace)
                    || $marketplace->id == AmazonMarketplace::NON_AMAZON
                    || empty($reportDatum['sku'])
                ) {
                    continue;
                }

                $hasExistingOrderItems = !empty($allExistingOrderItems[$reportDatum['amazon-order-id'] . '_' . $reportDatum['sku']]);

                if (!$hasExistingOrderItems) {
                    $orderItemId = 'REPORT_' . $amazonReport->id . '_' . uniqid('');
                } else {
                    $existingOrderItem = array_shift($allExistingOrderItems[$reportDatum['amazon-order-id'] . '_' . $reportDatum['sku']]);

                    if (strtotime($existingOrderItem['last_update_date']) > strtotime($reportDatum['last-updated-date'])) {
                        continue;
                    }
                    $orderItemId = $existingOrderItem['order_item_id'];
                }

                $orderItemsToSave[$orderItemId] = [
                    'order_item_id' => $orderItemId,
                    'order_id' => $reportDatum['amazon-order-id'],
                    'sku' => $reportDatum['sku'],
                    'order_marketplace_id' => $marketplace->id,
                    'asin' => $reportDatum['asin'] ?: '',
                    'title' => StringHelper::truncate($reportDatum['product-name'] ?: '', 200, ''),
                    'quantity' => $reportDatum['quantity'] ?: 0,
                    'item_price' => $reportDatum['item-price'] ?: 0,
                    'item_tax' => $reportDatum['item-tax'] ?: 0,
                    'shipping_price' => $reportDatum['shipping-price'] ?: 0,
                    'shipping_tax' => $reportDatum['shipping-tax'] ?: 0,
                    'shipping_discount' => $reportDatum['ship-promotion-discount'] ?: 0,
                    'promotion_discount' => $reportDatum['item-promotion-discount'] ?: 0,
                    'order_purchase_date' => $reportDatum['purchase-date'],
                    'date' => date('Y-m-d H:i:s'),
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($orderItemsToSave)) {
            return;
        }

        $orderItemsToSave = array_values($orderItemsToSave);

        $insertUpdateSql = AmazonOrderItem::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrderItem::tableName(),
                array_keys(array_values($orderItemsToSave)[0]),
                $orderItemsToSave
            )
            ->getRawSql();

        $insertUpdateSql .= ' ON CONFLICT (order_item_id) DO UPDATE SET
            quantity = EXCLUDED.quantity,
            item_price = EXCLUDED.item_price,
            item_tax = EXCLUDED.item_tax,
            shipping_price = EXCLUDED.shipping_price,
            shipping_tax = EXCLUDED.shipping_tax,
            shipping_discount = EXCLUDED.shipping_discount,
            promotion_discount = EXCLUDED.promotion_discount,
            date = EXCLUDED.date
        ';

        AmazonOrderItem::getDb()->createCommand($insertUpdateSql)->execute();
        $this->info('Order items saved');
    }

    protected function saveOrders(array $reportData): void
    {
        $this->info('Saving orders');

        $orderIds = [];
        foreach ($reportData as $reportDatum) {
            $orderIds[] = $reportDatum['amazon-order-id'];
        }

        $exitingOrders = AmazonOrder::find()
            ->select('amazon_order_id, last_update_date, order_status, updated_at, order_status_from_report')
            ->where(['amazon_order_id' => $orderIds])
            ->asArray()
            ->all()
        ;

        foreach ($exitingOrders as $K => $v) {
            $exitingOrders[$v['amazon_order_id']] = $v;
            unset($exitingOrders[$K]);
        }

        $ordersToSave = [];

        foreach ($reportData as $reportDatum) {
            $exitingOrder = $exitingOrders[$reportDatum['amazon-order-id']] ?? null;

            if ($exitingOrder && strtotime($exitingOrder['last_update_date']) > strtotime($reportDatum['last-updated-date'])) {
                continue;
            }

            $orderStatus = $reportDatum['order-status'];
            $orderStatusFromReport = $reportDatum['order-status'];
            try {
                $orderStatus = $this->mapOrderStatus($reportDatum['order-status']);
            } catch (\Throwable $e) {
                $this->error($e);
            }
            $marketplace = AmazonMarketplace::getBySalesChannel(strtolower($reportDatum['sales-channel']));

            if (empty($marketplace)
                || $marketplace->id == AmazonMarketplace::NON_AMAZON
                || empty($reportDatum['sku'])
            ) {
                continue;
            }

            $updatedAt = date('Y-m-d H:i:s');

            if ($exitingOrder) {
                // Do not allow to change order status for orders with final status
                if (in_array($exitingOrder['order_status'], [
                    Order::ORDER_STATUS_SHIPPED,
                    Order::ORDER_STATUS_CANCELED,
                ])) {
                    $orderStatus = $exitingOrder['order_status'];
                    $orderStatusFromReport = $exitingOrder['order_status_from_report'];
                }

                if ($orderStatus == $exitingOrder['order_status']) {
                    $updatedAt = $exitingOrder['updated_at'];
                }
            }

            $ordersToSave[$reportDatum['amazon-order-id']] = [
                'seller_id' => $this->dbManager->getSellerId(),
                'amazon_order_id' => $reportDatum['amazon-order-id'],
                'seller_order_id' => $reportDatum['merchant-order-id'],
                'purchase_date' => $reportDatum['purchase-date'],
                'last_update_date' => $reportDatum['last-updated-date'],
                'order_status' => $orderStatus,
                'order_status_from_report' => $orderStatusFromReport,
                'marketplace_id' => $marketplace->id,
                'fulfillment_channel' => self::FULFILMENT_CHANNEL_MAP[$reportDatum['fulfillment-channel']],
                'items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_FINISHED,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => $updatedAt,
            ];
        }

        $ordersToSave = array_values($ordersToSave);

        if (empty($ordersToSave)) {
            return;
        }

        $insertUpdateSql = AmazonOrder::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrder::tableName(),
                array_keys(array_values($ordersToSave)[0]),
                $ordersToSave
            )
            ->getRawSql()
        ;

        $insertUpdateSql .= ' ON CONFLICT (amazon_order_id) DO UPDATE SET
            last_update_date = EXCLUDED.last_update_date,
            order_status = EXCLUDED.order_status,
            order_status_from_report = EXCLUDED.order_status_from_report,
            updated_at = EXCLUDED.updated_at
        ';

        AmazonOrder::getDb()->createCommand($insertUpdateSql)->execute();
        $this->info('Orders saved');
    }

    protected function saveProducts(array $reportData): void
    {
        if (empty($reportData)) {
            return;
        }

        $skus = array_column($reportData, 'sku');
        $existingProducts = Product::find()
            ->select('marketplace_id, seller_id, sku, stock_type, is_multiple_stock_type')
            ->where(['sku' => $skus])
            ->asArray()
            ->all();

        foreach ($existingProducts as $k => $existingProduct) {
            $key = implode('_', [$existingProduct['marketplace_id'], $existingProduct['seller_id'], $existingProduct['sku']]);
            $existingProducts[$key] = $existingProduct;
            unset($existingProducts[$k]);
        }

        $productsToSave = [];

        foreach ($reportData as $reportDatum) {
            $marketplace = AmazonMarketplace::getBySalesChannel(strtolower($reportDatum['sales-channel']));
            if (empty($marketplace)
                || $marketplace->id == AmazonMarketplace::NON_AMAZON
                || empty($reportDatum['sku'])
                || strpos($reportDatum['product-name'], "ï¿½") !== false
                || strpos($reportDatum['sku'], "ï¿½") !== false
            ) {
                continue;
            }

            $key = implode('_', [
                $marketplace->id,
                $this->dbManager->getSellerId(),
                $reportDatum['sku'],
            ]);

            $stockType = self::PRODUCT_STOCK_TYPE_MAP[$reportDatum['fulfillment-channel']];
            if (empty($existingProducts[$key])) {
                $productsToSave[$key] = [
                    'marketplace_id' => $marketplace->id,
                    'seller_id' => $this->dbManager->getSellerId(),
                    'currency_code' => $marketplace->currency_code,
                    'sku' => $reportDatum['sku'],
                    'asin' => $reportDatum['asin'],
                    'title' => $reportDatum['product-name'],
                    'is_multiple_stock_type' => false,
                    'source' => Product::SOURCE_REPORT,
                    'stock_type' => $stockType
                ];
            } elseif ($stockType != $existingProducts[$key]['stock_type']
                && !$existingProducts[$key]['is_multiple_stock_type']
            ) {
                $productsToSave[$key] = array_merge($existingProducts[$key], [
                    'is_multiple_stock_type' => true,
                    'stock_type' => $stockType,
                    'source' => Product::SOURCE_REPORT,
                ]);
            }
        }

        $productsToSave = array_values($productsToSave);

        if (empty($productsToSave)) {
            return;
        }

        // Temporary fix for bug with empty currency code (Not null violation: 7 ERROR:  null value in column "currency_code")
        foreach ($productsToSave as $k => $product) {
            if (empty($product['currency_code'])) {
                unset($productsToSave[$k]);
            }
        }

        if (empty($productsToSave)) {
            return;
        }

        $productsToSave = $this->productsSaver->fillIsEnabledSyncWithRepricerDefaultValue($productsToSave);

        $insertUpdateSql = Product::getDb()
            ->createCommand()
            ->batchInsert(
                Product::tableName(),
                array_keys(array_values($productsToSave)[0]),
                $productsToSave
            )
            ->getRawSql();
        $insertUpdateSql .= ' ON CONFLICT (marketplace_id, seller_id, sku) DO UPDATE SET
            stock_type = EXCLUDED.stock_type,
            is_multiple_stock_type = EXCLUDED.is_multiple_stock_type
        ';
        Product::getDb()->createCommand($insertUpdateSql)->execute();
    }

    protected function mapOrderStatus(?string $orderStatus): string
    {
        if ($orderStatus === AmazonOrder::ORDER_STATUS_WRONG_CANCELED) {
            $orderStatus = Order::ORDER_STATUS_CANCELED;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_ON_TRIAL) {
            $orderStatus = Order::ORDER_STATUS_PENDING;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_SHIPPING) {
            $orderStatus = Order::ORDER_STATUS_UNSHIPPED;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_COMPLETE) {
            $orderStatus = Order::ORDER_STATUS_SHIPPED;
        }

        if (false !== strpos($orderStatus, Order::ORDER_STATUS_SHIPPED)) {
            $orderStatus = Order::ORDER_STATUS_SHIPPED;
        }
        if (false !== strpos($orderStatus, Order::ORDER_STATUS_PENDING)) {
            $orderStatus = Order::ORDER_STATUS_PENDING;
        }

        $allowedStatuses = [
            Order::ORDER_STATUS_PENDING,
            Order::ORDER_STATUS_UNSHIPPED,
            Order::ORDER_STATUS_PARTIALLY_SHIPPED,
            Order::ORDER_STATUS_SHIPPED,
            Order::ORDER_STATUS_CANCELED,
            Order::ORDER_STATUS_UNFULFILLABLE,
            Order::ORDER_STATUS_INVOICE_UNCONFIRMED,
            Order::ORDER_STATUS_PENDING_AVAILABILITY,
        ];

        if (in_array($orderStatus, $allowedStatuses)) {
            return $orderStatus;
        }

        throw new \Exception("Unable to map order status from amazon report");
    }
}
