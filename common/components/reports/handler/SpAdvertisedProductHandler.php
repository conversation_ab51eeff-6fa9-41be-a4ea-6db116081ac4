<?php

namespace common\components\reports\handler;

use common\components\amazonAds\CostsApplier;
use common\components\LogToConsoleTrait;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\SpAdvertisedProduct;
use common\models\customer\AmazonReport;

class SpAdvertisedProductHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $this->info('SpAdvertisedProductHandler processing ' . count($reportData) . ' items');

        if (empty($reportData)) {
            return;
        }

        $dataToSave = [];

        foreach ($reportData as $reportDatum) {
            $dataToSave[] = [
                'profile_id' => $amazonReport->extra_info['profile_id'],
                'date' => $reportDatum['date'],
                'campaign_id' => $reportDatum['campaignId'],
                'clicks' => $reportDatum['clicks'],
                'impressions' => $reportDatum['impressions'],
                'purchases_1d' => $reportDatum['purchases1d'],
                'purchases_same_sku_1d' => $reportDatum['purchasesSameSku1d'],
                'units_sold_clicks_1d' => $reportDatum['unitsSoldClicks1d'],
                'attributed_sales_same_sku_1d' => $reportDatum['attributedSalesSameSku1d'],
                'attributed_sales_same_sku_14d' => $reportDatum['attributedSalesSameSku14d'],
                'units_sold_same_sku_1d' => $reportDatum['unitsSoldSameSku1d'],
                'sales_1d' => $reportDatum['sales1d'],
                'spend' => $reportDatum['spend'],
                'ad_id' => $reportDatum['adId'],
                'cost' => $reportDatum['cost'],
                'sku' => $reportDatum['advertisedSku'] ?? '',
                'asin' => $reportDatum['advertisedAsin'],
                'currency_code' => $reportDatum['campaignBudgetCurrencyCode'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        $sql = SpAdvertisedProduct::getDb()
            ->createCommand()
            ->batchInsert(SpAdvertisedProduct::tableName(), array_keys(array_values($dataToSave)[0]), $dataToSave)
            ->getRawSql();
        $sql .= ' ON CONFLICT (profile_id, date, ad_id, sku) DO UPDATE SET 
            clicks = EXCLUDED.clicks,
            impressions = EXCLUDED.impressions,
            purchases_1d = EXCLUDED.purchases_1d,
            purchases_same_sku_1d = EXCLUDED.purchases_same_sku_1d,
            units_sold_clicks_1d = EXCLUDED.units_sold_clicks_1d,
            attributed_sales_same_sku_1d = EXCLUDED.attributed_sales_same_sku_1d,
            units_sold_same_sku_1d = EXCLUDED.units_sold_same_sku_1d,
            sales_1d = EXCLUDED.sales_1d,
            spend = EXCLUDED.spend,
            cost = EXCLUDED.cost,
            updated_at = EXCLUDED.updated_at
        ';
        SpAdvertisedProduct::getDb()->createCommand($sql)->execute();
    }
}
