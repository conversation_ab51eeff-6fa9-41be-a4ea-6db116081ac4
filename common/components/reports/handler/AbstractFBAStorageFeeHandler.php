<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\dataImportExport\import\processor\DataImportProcessorFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\MessageInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\AmazonReport;
use common\models\customer\FbaStorageFeeHistory;
use common\models\customer\Product;

abstract class AbstractFBAStorageFeeHandler implements DataHandlerInterface
{

    use LogToConsoleTrait;

    protected DbManager $dbManager;
    private CustomerComponent $customerComponent;
    protected MessagesSender $messagesSender;

    abstract protected function getReportType(): string;

    abstract public function handle(array $reportData, AmazonReport $amazonReport): void;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->messagesSender = new MessagesSender();
    }

    protected function prepareHistoryData(array $dataToSave): array
    {
        $historyData = [];
        foreach ($dataToSave as $data) {
            $currentRecord = FbaStorageFeeHistory::find()
                ->where([
                    'marketplace_id' => $data['marketplace_id'],
                    'seller_id' => $data['seller_id'],
                    'asin' => $data['asin'],
                    'report_type' => $data['report_type'],
                    'date' => $data['date'],
                ])
                ->one();

            if ($currentRecord &&
                isset($data['amount'])) {
                $data['diff_amount'] = (float)$data['amount'] - (float)$currentRecord->amount;
            } else {
                $data['diff_amount'] = null;
            }

            $historyData[] = $data;
        }

        return $historyData;
    }

    protected function sendToTransactionQueue(array $historyData): void
    {
        if (empty($historyData)) {
            return;
        }

        try {
            foreach ($historyData as $data) {
                if (empty($data['amount']) && empty($data['diff_amount'])) {
                    continue;
                }

                $message = [
                    'seller_id' =>  $data['seller_id'],
                    'marketplace_id' => $data['marketplace_id'],
                    'sku' => $data['sku'] ?? null,
                    'fnsku' => $data['fnsku'] ?? null,
                    'asin' => $data['asin'],
                    'report_type' => $data['report_type'],
                    'date' => $data['date'],
                    'amount' => $data['amount'],
                    'diff_amount' => $data['diff_amount'],
                    'currency_code' => $data['currency_code'],
                    'customer_id' => $data['customer_id']
                ];

                $this->info($message);

                $routingKey = strtolower('apply');

                $this->messagesSender->publish($message, MessageInterface::EXCHANGE_NAME_FBA_FEE_STORAGE_SYNC, $routingKey);
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}
