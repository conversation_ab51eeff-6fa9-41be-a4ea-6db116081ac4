<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\FbaEstimatedFee;
use common\models\customer\FbaEstimatedFeeHistory;
use yii\caching\CacheInterface;

class EstimatedFBAFeesHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->cache = \Yii::$app->get('cache');
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        foreach ($reportData as $reportDatum) {
            try {
                if ($reportDatum['amazon-store'] === 'GB') {
                    $reportDatum['amazon-store'] = 'UK';
                }

                /** @var AmazonMarketplace $marketplace */
                $marketplace = AmazonMarketplace::find()->where([
                    'country_code' => $reportDatum['amazon-store']
                ])->cache(60)->one();

                if (empty($marketplace)) {
                    throw new \Exception("Unable to determine marketplace for {$reportDatum['amazon-store']}");
                }

                $dataToSave[] = [
                    'marketplace_id' => $marketplace->id,
                    'currency' => $reportDatum['currency'],
                    'seller_id' => $this->dbManager->getSellerId(),
                    'sku' => html_entity_decode($reportDatum['sku'] ?? null),
                    'sales_price' => is_numeric($reportDatum['sales-price'] ?? null)
                        ? $reportDatum['sales-price'] : null,
                    'estimated_referral_fee_per_unit' => is_numeric($reportDatum['estimated-referral-fee-per-unit'] ?? null)
                        ? $reportDatum['estimated-referral-fee-per-unit'] : null,
                    'estimated_variable_closing_fee' => is_numeric($reportDatum['estimated-variable-closing-fee'] ?? null)
                        ? $reportDatum['estimated-variable-closing-fee'] : null,
                    'estimated_fixed_closing_fee' => is_numeric($reportDatum['estimated-fixed-closing-fee'] ?? null)
                        ? $reportDatum['estimated-fixed-closing-fee'] : null,
                    'expected_domestic_fulfilment_fee_per_unit' => is_numeric($reportDatum['expected-domestic-fulfilment-fee-per-unit'] ?? null)
                        ? $reportDatum['expected-domestic-fulfilment-fee-per-unit'] : null,
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbaEstimatedFee::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;
        $sql .= ' ON CONFLICT (marketplace_id, seller_id, sku) DO UPDATE SET
            currency = EXCLUDED.currency,
            sales_price = EXCLUDED.sales_price,
            estimated_referral_fee_per_unit = EXCLUDED.estimated_referral_fee_per_unit,
            estimated_variable_closing_fee = EXCLUDED.estimated_variable_closing_fee,
            estimated_fixed_closing_fee = EXCLUDED.estimated_fixed_closing_fee,
            expected_domestic_fulfilment_fee_per_unit = EXCLUDED.expected_domestic_fulfilment_fee_per_unit,
            updated_at = NOW()
        ';
        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();

        $sqlHistory = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbaEstimatedFeeHistory::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;

        $sqlHistory .= ' ON CONFLICT (marketplace_id, seller_id, sku, date) DO UPDATE SET
            currency = EXCLUDED.currency,
            sales_price = EXCLUDED.sales_price,
            estimated_referral_fee_per_unit = EXCLUDED.estimated_referral_fee_per_unit,
            estimated_variable_closing_fee = EXCLUDED.estimated_variable_closing_fee,
            estimated_fixed_closing_fee = EXCLUDED.estimated_fixed_closing_fee,
            expected_domestic_fulfilment_fee_per_unit = EXCLUDED.expected_domestic_fulfilment_fee_per_unit
        ';

        $this->dbManager->getCustomerDb()->createCommand($sqlHistory)->execute();
    }
}
