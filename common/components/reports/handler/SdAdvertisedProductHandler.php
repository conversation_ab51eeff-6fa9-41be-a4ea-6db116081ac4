<?php

namespace common\components\reports\handler;

use common\components\amazonAds\CostsApplier;
use common\components\LogToConsoleTrait;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\SdAdvertisedProduct;
use common\models\customer\AmazonReport;

class SdAdvertisedProductHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $this->info('SdAdvertisedProductHandler processing ' . count($reportData) . ' items');

        if (empty($reportData)) {
            return;
        }

        $dataToSave = [];

        foreach ($reportData as $reportDatum) {
            $dataToSave[] = [
                'profile_id' => $amazonReport->extra_info['profile_id'],
                'date' => $reportDatum['date'],
                'campaign_id' => $reportDatum['campaignId'],
                'clicks' => $reportDatum['clicks'],
                'impressions' => $reportDatum['impressions'],
                'purchases' => $reportDatum['purchases'],
                'purchases_clicks' => $reportDatum['purchasesClicks'],
                'purchases_promoted_clicks' => $reportDatum['purchasesPromotedClicks'],
                'sales' => $reportDatum['sales'],
                'sales_clicks' => $reportDatum['salesClicks'],
                'sales_promoted_clicks' => $reportDatum['salesPromotedClicks'],
                'units_sold' => $reportDatum['unitsSold'],
                'units_sold_clicks' => $reportDatum['unitsSoldClicks'],
                'spend' => $reportDatum['spend'],
                'ad_id' => $reportDatum['adId'],
                'cost' => $reportDatum['cost'],
                'sku' => $reportDatum['promotedSku'] ?? '',
                'asin' => $reportDatum['promotedAsin'],
                'currency_code' => $reportDatum['campaignBudgetCurrencyCode'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        $sql = SdAdvertisedProduct::getDb()
            ->createCommand()
            ->batchInsert(SdAdvertisedProduct::tableName(), array_keys(array_values($dataToSave)[0]), $dataToSave)
            ->getRawSql();
        $sql .= ' ON CONFLICT (profile_id, date, ad_id, sku) DO UPDATE SET
            clicks = EXCLUDED.clicks,
            impressions = EXCLUDED.impressions,
            purchases = EXCLUDED.purchases,
            purchases_clicks = EXCLUDED.purchases_clicks,
            purchases_promoted_clicks = EXCLUDED.purchases_promoted_clicks,
            sales = EXCLUDED.sales,
            sales_clicks = EXCLUDED.sales_clicks,
            sales_promoted_clicks = EXCLUDED.sales_promoted_clicks,
            units_sold = EXCLUDED.units_sold,
            units_sold_clicks = EXCLUDED.units_sold_clicks,
            spend = EXCLUDED.spend,
            cost = EXCLUDED.cost,
            updated_at = EXCLUDED.updated_at
        ';

        SdAdvertisedProduct::getDb()->createCommand($sql)->execute();
    }
}