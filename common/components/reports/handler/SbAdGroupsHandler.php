<?php

namespace common\components\reports\handler;

use common\components\amazonAds\CostsApplier;
use common\components\LogToConsoleTrait;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\customer\AmazonReport;

class SbAdGroupsHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected CostsApplier $costsApplier;

    public function __construct()
    {
        $this->costsApplier = new CostsApplier();
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $this->info('AdGroupsHandler processing ' . count($reportData) . ' items');

        if (empty($reportData)) {
            return;
        }

        /** @var AmazonAdsProfile $adsProfile */
        $adsProfile = AmazonAdsProfile::find()
            ->where(['id' => $amazonReport->extra_info['profile_id']])
            ->cache(10)
            ->one();

        $dataToSave = [];

        foreach ($reportData as $reportDatum) {
            $dataToSave[] = [
                'date' => $amazonReport->start_date,
                'profile_id' => $amazonReport->extra_info['profile_id'],
                'campaign_id' => $reportDatum['campaignId'],
                'clicks' => $reportDatum['clicks'],
                'impressions' => $reportDatum['impressions'],
                'ad_group_id' => $reportDatum['adGroupId'],
                'cost' => $reportDatum['cost'],
                'currency_code' => $adsProfile->currency_code,
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        $sql = SbAdGroupStatistic::getDb()
            ->createCommand()
            ->batchInsert(SbAdGroupStatistic::tableName(), array_keys(array_values($dataToSave)[0]), $dataToSave)
            ->getRawSql();
        $sql .= ' ON CONFLICT (profile_id, date, ad_group_id) DO UPDATE SET
            clicks = EXCLUDED.clicks,
            impressions = EXCLUDED.impressions,
            cost = EXCLUDED.cost,
            currency_code = EXCLUDED.currency_code,
            updated_at = EXCLUDED.updated_at
        ';
        SdAdvertisedProduct::getDb()->createCommand($sql)->execute();
    }
}