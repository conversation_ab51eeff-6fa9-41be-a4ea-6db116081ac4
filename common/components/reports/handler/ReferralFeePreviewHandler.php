<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\ReferralFeePreview;
use common\models\customer\ReferralFeePreviewHistory;

class ReferralFeePreviewHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        /** @var AmazonMarketplace $marketplace */
        $marketplace = AmazonMarketplace::findOne($amazonReport->extra_info['marketplaceIds'][0]);

        foreach ($reportData as $reportDatum) {
            try {
                $dataToSave[] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplace->id,
                    'seller_sku' => $reportDatum['seller-sku'],
                    'asin' => $reportDatum['asin'],
                    'price' => is_numeric($reportDatum['price'])
                        ? $reportDatum['price']
                        : null,
                    'estimated_referral_fee_per_item' => is_numeric($reportDatum['estimated-referral-fee-per-item'])
                        ? $reportDatum['estimated-referral-fee-per-item']
                        : null,
                    'currency' => $marketplace->currency_code,
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                ReferralFeePreview::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;
        $sql .= ' ON CONFLICT (marketplace_id, seller_id, seller_sku) DO UPDATE SET
            price = EXCLUDED.price,
            estimated_referral_fee_per_item = EXCLUDED.estimated_referral_fee_per_item,
            updated_at = NOW()
        ';
        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();

        $sqlHistory = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                ReferralFeePreviewHistory::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;
        $sqlHistory .= ' ON CONFLICT (marketplace_id, seller_id, seller_sku, date) DO UPDATE SET
            price = EXCLUDED.price,
            estimated_referral_fee_per_item = EXCLUDED.estimated_referral_fee_per_item';

        $this->dbManager->getCustomerDb()->createCommand($sqlHistory)->execute();
    }
}
