<?php

namespace common\components\reports\handler;

use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;

class FBAStorageFeeChargesHandler extends AbstractFBAStorageFeeHandler
{
    protected function getReportType(): string
    {
        return FbaStorageFee::REPORT_TYPE_STORAGE;
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['month_of_charge'])) {
                    continue;
                }

                $marketplace = AmazonMarketplace::find()->where([
                    'country_code' => $reportDatum['country_code'] ?? null
                ])->cache(60)->one();

                if (empty($marketplace)) {
                    throw new \Exception("Unable to determine marketplace for country code: {$reportDatum['country_code']}");
                }

                $dataToSave[] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplace->id,
                    'asin' => $reportDatum['asin'] ?? null,
                    'fnsku' => html_entity_decode($reportDatum['fnsku']) ?? null,
                    'date' => $reportDatum['month_of_charge'] . '-01',
                    'sku' => '',
                    'amount' => $reportDatum['estimated_monthly_storage_fee'] ?? null,
                    'currency_code' => $reportDatum['currency'] ?? null,
                    'report_type' => $this->getReportType(),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $this->saveToMainTable($dataToSave);

        $historyData = $this->prepareHistoryData($dataToSave);

        $this->saveToHistoryTable($historyData);

        $this->sendToTransactionQueue($historyData);
    }
}
