<?php

namespace common\components\reports\handler;

use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;

class FBAStorageFeeChargesHandler extends AbstractFBAStorageFeeHandler
{
    protected function getReportType(): string
    {
        return FbaStorageFee::REPORT_TYPE_STORAGE;
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['month_of_charge'])) {
                    continue;
                }

                // Convert GB to UK for marketplace lookup
                $countryCode = $reportDatum['country_code'] ?? null;
                if ($countryCode === 'GB') {
                    $countryCode = 'UK';
                }

                $marketplace = AmazonMarketplace::find()->where([
                    'country_code' => $countryCode
                ])->cache(60)->one();

                if (empty($marketplace)) {
                    throw new \Exception("Unable to determine marketplace for country code: {$countryCode}");
                }

                $date = $reportDatum['month_of_charge'] . '-01';

                $keyParts = [
                    $marketplace->id,
                    $amazonReport->seller_id,
                    $reportDatum['asin'],
                    $date,
                    $this->getReportType(),
                ];
                $key = implode('|', $keyParts);

                if ($reportDatum['estimated_monthly_storage_fee'] > 0.00) {
                    if (!isset($dataToSave[$key])) {
                        $dataToSave[$key] = [
                            'seller_id' => $amazonReport->seller_id,
                            'marketplace_id' => $marketplace->id,
                            'asin' => $reportDatum['asin'],
                            'fnsku' => html_entity_decode($reportDatum['fnsku']),
                            'date' => $date,
                            'sku' => '',
                            'amount' => (float) $reportDatum['estimated_monthly_storage_fee'],
                            'currency_code' => $reportDatum['currency'],
                            'report_type' => $this->getReportType(),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                    } else {
                        $dataToSave[$key]['amount'] += (float) $reportDatum['estimated_monthly_storage_fee'];
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $this->saveToMainTable($dataToSave);

        $historyData = $this->prepareHistoryData($dataToSave);

        $this->saveToHistoryTable($historyData);

        $this->sendToTransactionQueue($historyData);
    }
}
