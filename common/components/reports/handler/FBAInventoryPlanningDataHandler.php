<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\ProductBsr;

class FBAInventoryPlanningDataHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        /** @var AmazonMarketplace $marketplace */
        $marketplace = AmazonMarketplace::findOne($amazonReport->extra_info['marketplaceIds'][0]);

        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['sku']) || empty($reportDatum['sales-rank'])) {
                    continue;
                }

                $dataToSave[] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplace->id,
                    'sku' => $reportDatum['sku'],
                    'bsr' => $reportDatum['sales-rank'],
                    'date' => date('Y-m-d H:i:s'),
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                ProductBsr::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;
        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
    }
}