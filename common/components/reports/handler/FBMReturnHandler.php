<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\customer\AmazonReport;
use common\models\customer\FbmReturn;
use yii\caching\CacheInterface;

class FBMReturnHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->cache = \Yii::$app->get('cache');
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];

        $marketplaceId = $amazonReport->extra_info['marketplaceIds'][0];

        foreach ($reportData as $reportDatum) {
            try {
                $reportDatum['Return request date'] = date('Y-m-d H:i:s', strtotime($reportDatum['Return request date']));
                $reportDatum['Order date'] = date('Y-m-d H:i:s', strtotime($reportDatum['Order date']));

                if (empty($reportDatum['Order ID'])) {
                    continue;
                }

                $uniqueKey = implode('_', [
                    $reportDatum['Order ID'],
                    $reportDatum['Return request date'],
                    $reportDatum['Merchant SKU'],
                    $reportDatum['Amazon RMA ID']
                ]);

                if (isset($dataToSave[$uniqueKey])) {
                    $dataToSave[$uniqueKey]['return_quantity'] += $reportDatum['return_quantity'];
                    $dataToSave[$uniqueKey]['refund_amount'] += $reportDatum['refund_amount'];
                    continue;
                }

                $dataToSave[$uniqueKey] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplaceId,
                    'order_id' => $reportDatum['Order ID'],
                    'order_date' => $reportDatum['Order date'],
                    'return_request_date' => $reportDatum['Return request date'],
                    'return_request_status' => $reportDatum['Return request status'],
                    'amazon_rma_id' => $reportDatum['Amazon RMA ID'],
                    'label_type' => $reportDatum['Label type'],
                    'label_cost' => (float)$reportDatum['Label cost'],
                    'currency_code' => $reportDatum['Currency code'],
                    'return_carrier' => $reportDatum['Return carrier'],
                    'tracking_id' => $reportDatum['Tracking ID'],
                    'label_to_paid_by' => $reportDatum['Label to be paid by'],
                    'a_to_z_claim' => $reportDatum['A-to-Z Claim'] === 'Y',
                    'is_prime' => $reportDatum['Is prime'] === 'Y',
                    'asin' => $reportDatum['ASIN'],
                    'merchant_sku' => html_entity_decode($reportDatum['Merchant SKU']),
                    'item_name' => $reportDatum['Item Name'],
                    'return_quantity' => (int)$reportDatum['Return quantity'],
                    'return_reason' => $reportDatum['Return Reason'],
                    'in_policy' => $reportDatum['In policy'] === 'Y',
                    'return_type' => $reportDatum['Return type'],
                    'resolution' => $reportDatum['Resolution'],
                    'order_amount' => (float)$reportDatum['Order Amount'],
                    'order_quantity' => (int)$reportDatum['Order quantity'],
                    'refund_amount' => (float)$reportDatum['Refunded Amount'],
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbmReturn::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;

        $sql .= ' ON CONFLICT (order_id, return_request_date, merchant_sku, amazon_rma_id) DO UPDATE SET
            amazon_rma_id = EXCLUDED.amazon_rma_id,
            label_type = EXCLUDED.label_type,
            label_cost = EXCLUDED.label_cost,
            currency_code = EXCLUDED.currency_code,
            return_carrier = EXCLUDED.return_carrier,
            tracking_id = EXCLUDED.tracking_id,
            label_to_paid_by = EXCLUDED.label_to_paid_by,
            a_to_z_claim = EXCLUDED.a_to_z_claim,
            is_prime = EXCLUDED.is_prime,
            asin = EXCLUDED.asin,
            item_name = EXCLUDED.item_name,
            return_quantity = EXCLUDED.return_quantity,
            return_reason = EXCLUDED.return_reason,
            in_policy = EXCLUDED.in_policy,
            return_type = EXCLUDED.return_type,
            resolution = EXCLUDED.resolution,
            order_amount = EXCLUDED.order_amount,
            order_quantity = EXCLUDED.order_quantity,
            refund_amount = EXCLUDED.refund_amount,
            updated_at = CURRENT_TIMESTAMP
        ';
        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
    }
}