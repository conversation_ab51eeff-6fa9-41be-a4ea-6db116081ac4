<?php

namespace common\components\reports\handler;

use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;

class FBALongTermStorageFeeChargesHandler extends AbstractFBAStorageFeeHandler
{
    protected function getReportType(): string
    {
        return FbaStorageFee::REPORT_TYPE_LONGTERM;
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['snapshot-date'])) {
                    continue;
                }

                $marketplace = AmazonMarketplace::find()->where([
                    'country_code' => $reportDatum['country'] ?? null
                ])->cache(60)->one();

                if (empty($marketplace)) {
                    throw new \Exception("Unable to determine marketplace for country code: {$reportDatum['country']}");
                }

                $dataToSave[] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplace->id,
                    'sku' => html_entity_decode($reportDatum['sku']),
                    'asin' => $reportDatum['asin'] ?? null,
                    'fnsku' => html_entity_decode($reportDatum['fnsku']) ?? null,
                    'date' => $reportDatum['snapshot-date'],
                    'amount' => $reportDatum['amount-charged'] ?? null,
                    'currency_code' => $reportDatum['currency'] ?? null,
                    'report_type' => $this->getReportType(),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'customer_id' => \Yii::$app->dbManager->getCustomerId()
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $this->saveToMainTable($dataToSave);

        $historyData = $this->prepareHistoryData($dataToSave);

        $this->saveToHistoryTable($historyData);

        $this->sendToTransactionQueue($historyData);
    }

    protected function saveToMainTable(array $dataToSave): void
    {
        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbaStorageFee::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;

        $sql .= ' ON CONFLICT (marketplace_id, seller_id, asin, date, report_type) DO UPDATE SET
            fnsku = EXCLUDED.fnsku,
            sku = EXCLUDED.sku,
            amount = EXCLUDED.amount,
            currency_code = EXCLUDED.currency_code,
            updated_at = NOW()
        ';

        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
    }

    protected function saveToHistoryTable(array $historyData): void
    {
        $sqlHistory = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbaStorageFeeHistory::tableName(),
                array_keys(array_values($historyData)[0]),
                $historyData
            )
            ->getRawSql()
        ;

        $sqlHistory .= ' ON CONFLICT (marketplace_id, seller_id, asin, date, report_type) DO UPDATE SET
            fnsku = EXCLUDED.fnsku,
            sku = EXCLUDED.sku,
            amount = EXCLUDED.amount,
            diff_amount = EXCLUDED.diff_amount,
            currency_code = EXCLUDED.currency_code,
            updated_at = NOW()';

        $this->dbManager->getCustomerDb()->createCommand($sqlHistory)->execute();
    }
}
