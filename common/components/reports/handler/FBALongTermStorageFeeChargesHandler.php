<?php

namespace common\components\reports\handler;

use common\models\AmazonMarketplace;
use common\models\customer\AmazonReport;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;

class FBALongTermStorageFeeChargesHandler extends AbstractFBAStorageFeeHandler
{
    protected function getReportType(): string
    {
        return FbaStorageFee::REPORT_TYPE_LONGTERM;
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];
        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['snapshot-date'])) {
                    continue;
                }

                // Convert GB to UK for marketplace lookup
                $countryCode = $reportDatum['country'] ?? null;
                if ($countryCode === 'GB') {
                    $countryCode = 'UK';
                }

                $marketplace = AmazonMarketplace::find()->where([
                    'country_code' => $countryCode
                ])->cache(60)->one();

                if (empty($marketplace)) {
                    throw new \Exception("Unable to determine marketplace for country code: {$countryCode}");
                }

                $date = (new \DateTime($reportDatum['snapshot-date']))->format('Y-m-d');

                $keyParts = [
                    $marketplace->id,
                    $amazonReport->seller_id,
                    $reportDatum['asin'],
                    $date,
                    $this->getReportType(),
                ];
                $key = implode('|', $keyParts);

                if ((float)$reportDatum['amount-charged'] > 0.00) {
                    if (!isset($dataToSave[$key])) {
                        $dataToSave[$key] = [
                            'seller_id' => $amazonReport->seller_id,
                            'marketplace_id' => $marketplace->id,
                            'sku' => html_entity_decode($reportDatum['sku']),
                            'asin' => $reportDatum['asin'],
                            'fnsku' => html_entity_decode($reportDatum['fnsku']),
                            'date' => $date,
                            'amount' => (float)$reportDatum['amount-charged'],
                            'currency_code' => $reportDatum['currency'],
                            'report_type' => $this->getReportType(),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                    } else {
                        $dataToSave[$key]['amount'] += (float)$reportDatum['amount-charged'];
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $this->saveToMainTable($dataToSave);

        $historyData = $this->prepareHistoryData($dataToSave);

        $this->saveToHistoryTable($historyData);

        $this->sendToTransactionQueue($historyData);
    }
}
