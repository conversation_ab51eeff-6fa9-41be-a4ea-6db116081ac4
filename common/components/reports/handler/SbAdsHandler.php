<?php

namespace common\components\reports\handler;

use common\components\LogToConsoleTrait;
use common\models\ads\SbAd;
use common\models\customer\AmazonReport;

class <PERSON>b<PERSON>dsHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $this->info('AdGroupsHandler processing ' . count($reportData) . ' items');

        $dataToSave = [];

        foreach ($reportData as $reportDatum) {
            $asins = [];
            if (isset($reportDatum['creative']['subpages'])) {
                foreach ($reportDatum['creative']['subpages'] as $subpage) {
                    $asins[] = $subpage['asin'];
                }
            } else {
                $asins = $reportDatum['creative']['asins'];
            }

            if (empty($asins)) {
                continue;
            }

            foreach ($asins as $asin) {
                $dataToSave[] = [
                    'profile_id' => $amazonReport->extra_info['profile_id'],
                    'campaign_id' => $reportDatum['campaignId'],
                    'ad_group_id' => $reportDatum['adGroupId'],
                    'brand_name' => $reportDatum['creative']['brandName'],
                    'type' => $reportDatum['creative']['type'],
                    'asin' => $asin,
                ];
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = SbAd::getDb()
            ->createCommand()
            ->batchInsert(SbAd::tableName(), array_keys(array_values($dataToSave)[0]), $dataToSave)
            ->getRawSql();
        $sql .= ' ON CONFLICT (profile_id, ad_group_id, asin) DO NOTHING';

        SbAd::getDb()->createCommand($sql)->execute();
    }
}