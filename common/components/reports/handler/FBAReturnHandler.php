<?php

namespace common\components\reports\handler;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\customer\AmazonReport;
use common\models\customer\FbaReturn;
use yii\caching\CacheInterface;

class FBAReturnHandler implements DataHandlerInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->cache = \Yii::$app->get('cache');
    }

    public function handle(array $reportData, AmazonReport $amazonReport): void
    {
        $dataToSave = [];

        $marketplaceId = $amazonReport->extra_info['marketplaceIds'][0];

        foreach ($reportData as $reportDatum) {
            try {
                if (empty($reportDatum['order-id'])) {
                    continue;
                }

                $reportDatum['return-date'] = date('Y-m-d H:i:s', strtotime($reportDatum['return-date']));
                $uniqueKey = implode('_', [
                    $reportDatum['order-id'],
                    $reportDatum['sku'],
                    $reportDatum['return-date']
                ]);

                if (isset($dataToSave[$uniqueKey])) {
                    $dataToSave[$uniqueKey]['quantity'] += $reportDatum['quantity'];
                    continue;
                }

                $dataToSave[$uniqueKey] = [
                    'seller_id' => $amazonReport->seller_id,
                    'marketplace_id' => $marketplaceId,
                    'order_id' => $reportDatum['order-id'],
                    'return_date' => $reportDatum['return-date'],
                    'sku' => html_entity_decode($reportDatum['sku']),
                    'asin' => $reportDatum['asin'],
                    'fnsku' => html_entity_decode($reportDatum['fnsku']),
                    'quantity' => $reportDatum['quantity'],
                    'product_name' => $reportDatum['product-name'],
                    'fulfillment_center_id' => $reportDatum['fulfillment-center-id'],
                    'detailed_disposition' => $reportDatum['detailed-disposition'],
                    'reason' => $reportDatum['reason'],
                    'status' => $reportDatum['status'],
                    'license_plate_number' => $reportDatum['license-plate-number'],
                    'customer_comments' => $reportDatum['customer-comments']
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        if (empty($dataToSave)) {
            return;
        }

        $sql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                FbaReturn::tableName(),
                array_keys(array_values($dataToSave)[0]),
                $dataToSave
            )
            ->getRawSql()
        ;

        $sql .= ' ON CONFLICT (order_id, sku, return_date) DO UPDATE SET
            quantity = EXCLUDED.quantity,
            status = EXCLUDED.status,
            updated_at = NOW()
        ';
        $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
    }
}