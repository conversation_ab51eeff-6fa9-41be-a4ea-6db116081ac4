<?php

namespace common\components\reports;

use common\components\fileDataReader\DataReaderFactory;
use common\components\fileDataReader\DataReaderInterface;
use common\components\fileDataReader\IterateItemsInterface;
use common\components\LogToConsoleTrait;
use common\components\reports\dataProvider\AlreadyPreparedDataProviderInterface;
use common\components\reports\dataProvider\DataProviderFactory;
use common\components\reports\dataProvider\RemoteFileDataProviderInterface;
use common\models\customer\AmazonReport;
use Symfony\Component\Filesystem\Filesystem;

class ReadyReportsProcessor
{
    use LogToConsoleTrait;

    protected const READ_BATCH_SIZE = 500;

    protected Filesystem $filesystem;
    protected DataReaderFactory $dataReaderFactory;
    protected Configurator $configurator;

    protected DataProviderFactory $dataProviderFactory;

    public function __construct()
    {
        $this->filesystem = new Filesystem();
        $this->dataReaderFactory = new DataReaderFactory();
        $this->configurator = new Configurator();
        $this->dataProviderFactory = new DataProviderFactory();
    }

    public function process(AmazonReport $amazonReport): ProcessingResult
    {
        $dataProvider = $this->dataProviderFactory->getDataProvider($amazonReport->data_provider);

        if ($dataProvider instanceof RemoteFileDataProviderInterface) {
            return $this->processRemoteFileProvider($amazonReport, $dataProvider);
        }

        if ($dataProvider instanceof AlreadyPreparedDataProviderInterface) {
            return $this->processAlreadyPreparedDataProvider($amazonReport, $dataProvider);
        }

        throw new \Exception("Unknown processing mechanism for data provider {$amazonReport->data_provider}");
    }

    protected function processAlreadyPreparedDataProvider(
        AmazonReport $amazonReport,
        AlreadyPreparedDataProviderInterface $dataProvider
    ): ProcessingResult
    {
        $processingResult = new ProcessingResult();

        $items = $dataProvider->getData($amazonReport);
        $config = $this->configurator->getConfig($amazonReport->amazon_type);
        $handlers = $config->getHandlerClasses();

        foreach ($handlers as $handler) {
            try {
                $this->info("Handling data by " . get_class($handler) . ' started');
                $handler->handle($items, $amazonReport);
                $this->info("Handling data by " . get_class($handler) . ' finished');
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
        $processingResult->processedCount += count($items);
        $processingResult->isSuccess = true;

        return $processingResult;
    }

    protected function processRemoteFileProvider(
        AmazonReport $amazonReport,
        RemoteFileDataProviderInterface $dataProvider
    ): ProcessingResult
    {
        $processingResult = new ProcessingResult();
        $reportUrl = $dataProvider->getUrl($amazonReport);

        $this->info([
            'url' => $reportUrl,
        ]);

        $headers = get_headers($reportUrl, 1 );
        $charset = explode('charset=', ($headers['Content-Type'] ?? ''))[1];
        $charset = $charset ?: 'UTF-8';
        $tmpName = tempnam(sys_get_temp_dir(), uniqid('amazon_report'));

        try {
            $this->info("Downloading report to $tmpName started");
            $this->filesystem->copy($reportUrl, $tmpName);
            $this->info("Downloading report to $tmpName finished");

            if ($this->isFileCompressed($tmpName)) {
                $this->info("Unzipping report $tmpName if need");
                exec("cat $tmpName | gunzip > $tmpName.unzipped");
                $this->info("Unzipping report $tmpName.unzipped finished");
                unlink($tmpName);
                $tmpName = $tmpName . '.unzipped';
            }

            exec("iconv -c -f $charset -t UTF-8 $tmpName > $tmpName.utf8");
            unlink($tmpName);
            $tmpName = "$tmpName.utf8";

            $dataReader = $this->dataReaderFactory->getDataReaderByPath($tmpName);
            $config = $this->configurator->getConfig($amazonReport->amazon_type);
            $handlers = $config->getHandlerClasses();

            if (empty($handlers)) {
                throw new \Exception("No handlers found for amazon report type {$amazonReport->amazon_type}");
            }

            foreach ($this->iterateItems($dataReader, $tmpName) as $items) {
                foreach ($handlers as $handler) {
                    try {
                        $this->info("Handling data by " . get_class($handler) . ' started');
                        $handler->handle($items, $amazonReport);
                        $this->info("Handling data by " . get_class($handler) . ' finished');
                    } catch (\Throwable $e) {
                        $this->error($e);
                    }
                }
                $processingResult->processedCount += count($items);
            }
            $processingResult->isSuccess = true;
        } catch (\Throwable $e) {
            $this->error($e);
            unlink($tmpName);
            throw $e;
        }

        unlink($tmpName);
        return $processingResult;
    }

    protected function iterateItems(DataReaderInterface $dataReader, string $tmpName): \Iterator
    {
        if ($dataReader instanceof IterateItemsInterface) {
            $iterator = $dataReader->iterateItems($tmpName);
            $items = [];

            foreach ($iterator as $item) {
                $items[] = $item;

                if (count($items) >= self::READ_BATCH_SIZE) {
                    yield $items;
                    $items = [];
                }
            }

            if (count($items) > 0) {
                yield $items;
            }
            return;
        }

        $offset = 0;

        while (true) {
            try {
                $reportData = $dataReader->getData($tmpName, self::READ_BATCH_SIZE, $offset);
                $offset += self::READ_BATCH_SIZE;
                $this->info("Read " . count($reportData) . " rows");

                if (empty($reportData)) {
                    break;
                }

                yield $reportData;
            } catch (\Throwable $e) {
                $this->error($e);
                break;
            }
        }
    }

    protected function isFileCompressed(string $filePath)
    {
        $fileHandle = fopen($filePath, 'rb');
        $firstBytes = fread($fileHandle, 3); // Gzip magic bytes
        return $firstBytes === "\x1F\x8B\x08";
    }
}