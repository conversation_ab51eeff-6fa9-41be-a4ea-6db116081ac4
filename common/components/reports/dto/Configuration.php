<?php

namespace common\components\reports\dto;

use common\components\reports\dataProvider\DataProviderInterface;
use common\components\reports\handler\DataHandlerInterface;

class Configuration
{
    /**
     * @var DataProviderInterface
     */
    protected DataProviderInterface $dataProvider;
    protected string $type;
    protected string $uniqueType;

    /**
     * List of handlers that will be used to process the report data.
     *
     * @var DataHandlerInterface[]
     */
    protected array $handlerClasses = [];

    /**
     * Maximum size of the  period in minutes.
     *
     * @var int|float
     */
    protected int $periodMaxSizeMinutes = 60 * 24 * 365;

    /**
     * Minimum size of the period in minutes.
     *
     * @var int|float
     */
    protected int $periodMinSizeMinutes = 5;

    /**
     * Number of minutes to shift the period end time.
     *
     * @var int|float
     */
    protected int $periodOffsetMinutes = 0;

    /**
     * Number of minutes to shift the period data available since.
     *
     * @var int|float
     */
    protected int $minDateOffsetMinutes = 0;
    protected bool $isActive = true;

    /**
     * Number of minutes to ait while we can use the same report for another time.
     * For example if expiration time 24hours, we can create 20 reports  during the day and all of them
     * will be linked to singe report which is not expired yet.
     *
     * @var int
     */
    protected int $expirationTimeMinutes = 0;

    /**
     * Is the report daily (all day without time specifying).
     *
     * @var bool
     */
    protected bool $isDaily = false;

    protected bool $isMonthly = false;

    /**
     * Number of minutes between attempts to reload report and get more fresh results.
     *
     * @var int
     */
    protected int $timeBetweenAttemptsMinutes = 0;

    /**
     * Number of minutes to wait before considering the data stable and will never be changed again
     *
     * @var int
     */
    protected int $dataStabilizationPeriodMinutes = 0;

    protected array $extraIfo = [];

    public function getDataProvider(): DataProviderInterface
    {
        return $this->dataProvider;
    }

    public function setDataProvider(DataProviderInterface $dataProvider): self
    {
        $this->dataProvider = $dataProvider;
        return $this;
    }

    public function isPeriodicalUpdate(): bool
    {
        return $this->dataStabilizationPeriodMinutes > 0 || $this->timeBetweenAttemptsMinutes > 0;
    }

    public function setPeriodicalUpdateStrategy(int $dataStabilizationPeriodMinutes, int $timeBetweenAttempts): self
    {
        $this->dataStabilizationPeriodMinutes = $dataStabilizationPeriodMinutes;
        $this->timeBetweenAttemptsMinutes = $timeBetweenAttempts;
        return $this;
    }

    public function hasHistoricalData(): bool
    {
        return !empty($this->minDateOffsetMinutes);
    }

    public function isDaily(): bool
    {
        return $this->isDaily;
    }

    public function isMonthly(): bool
    {
        return $this->isMonthly;
    }

    public function setDailyStrategy(int $expirationTimeMinutes = 0): self
    {
        $this->isDaily = true;
        $this->isMonthly = false;
        $this->expirationTimeMinutes = $expirationTimeMinutes;
        $this->periodMaxSizeMinutes = 60 * 24;

        return $this;
    }

    public function setMonthlyStrategy(int $expirationTimeMinutes = 0): self
    {
        $this->isDaily = false;
        $this->isMonthly = true;
        $this->expirationTimeMinutes = $expirationTimeMinutes;
        $this->periodMaxSizeMinutes = 60 * 24 * 31 - 1;
        $this->periodMinSizeMinutes = 60 * 24 * 28 - 2;

        return $this;
    }

    public function getExpirationTimeMinutes(): int
    {
        return $this->expirationTimeMinutes;
    }

    public function getTimeBetweenAttemptsMinutes(): int
    {
        return $this->timeBetweenAttemptsMinutes;
    }

    public function getDataStabilizationPeriodMinutes(): int
    {
        return $this->dataStabilizationPeriodMinutes;
    }

    /**
     * @return DataHandlerInterface[]
     */
    public function getHandlerClasses(): array
    {
        if (empty($this->handlerClasses)) {
            throw new \Exception("No handlers found for amazon report type {$this->getType()}");
        }

        return $this->handlerClasses;
    }

    public function setHandlerClasses(array $handlerClasses): self
    {
        foreach ($handlerClasses as $handlerClass) {
            if (!$handlerClass instanceof DataHandlerInterface) {
                throw new \InvalidArgumentException('Handler class must implement HandlerInterface');
            }
        }

        $this->handlerClasses = $handlerClasses;
        return $this;
    }

    public function getPeriodMaxSizeMinutes(): int
    {
        return $this->periodMaxSizeMinutes;
    }

    public function setPeriodMaxSizeMinutes(int $periodMaxSizeMinutes): self
    {
        $this->periodMaxSizeMinutes = $periodMaxSizeMinutes;
        return $this;
    }

    public function getPeriodMinSizeMinutes(): int
    {
        return $this->periodMinSizeMinutes;
    }

    public function setPeriodMinSizeMinutes(int $periodMinSizeMinutes): self
    {
        $this->periodMinSizeMinutes = $periodMinSizeMinutes;
        return $this;
    }

    public function getPeriodOffsetMinutes(): int
    {
        return $this->periodOffsetMinutes;
    }

    public function setPeriodOffsetMinutes(int $periodOffsetMinutes): self
    {
        $this->periodOffsetMinutes = $periodOffsetMinutes;
        return $this;
    }

    public function getMinDateOffsetMinutes(): int
    {
        return $this->minDateOffsetMinutes;
    }

    public function setMinDateOffsetMinutes(int $minDateOffsetMinutes): self
    {
        $this->minDateOffsetMinutes = $minDateOffsetMinutes;
        return $this;
    }

    public function getExtraIfo(): array
    {
        return $this->extraIfo;
    }

    public function setExtraIfo(array $extraIfo): self
    {
        $this->extraIfo = $extraIfo;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        $this->uniqueType = $type;
        return $this;
    }

    public function getUniqueType(): string
    {
        return $this->uniqueType;
    }

    public function setUniqueType(string $uniqueType): self
    {
        $this->uniqueType = $uniqueType;
        return $this;
    }

    public function isActive()
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;
        return $this;
    }
}
