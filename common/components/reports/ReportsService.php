<?php

namespace common\components\reports;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\dataProvider\BatchSyncInterface;
use common\components\reports\dataProvider\DataProviderFactory;
use common\components\reports\dataProvider\DataProviderInterface;
use common\components\reports\dataProvider\RemoteFileDataProviderInterface;
use common\components\reports\dataProvider\SellingApiProvider;
use common\components\reports\dto\Configuration;
use common\components\reports\dto\SyncResult;
use common\components\reports\exception\CancelOrderException;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\services\PeriodsHelper;
use common\components\tokenService\Exception\NoTokenEntity;
use common\models\customer\AmazonReport;
use yii\caching\CacheInterface;
use yii\db\Expression;

/**
 * Used to interact with the reports API.
 */
class ReportsService
{
    use LogToConsoleTrait;

    protected PeriodsHelper $periodsHelper;
    protected DbManager $dbManager;
    protected Configurator $configurator;
    protected CacheInterface $cache;
    protected MessagesSender $messagesSender;
    protected DataProviderFactory $dataProviderFactory;
    protected ReportsSynchronizer $reportsSynchronizer;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->periodsHelper = new PeriodsHelper();
        $this->configurator = new Configurator();
        $this->cache = \Yii::$app->cache;
        $this->messagesSender = new MessagesSender();
        $this->dataProviderFactory = new DataProviderFactory();
        $this->reportsSynchronizer = new ReportsSynchronizer();
    }

    public function createReportsInAmazonToQueue(string $sellerId)
    {
        $dataProviderTypes = $this->configurator->getRegisteredDataProviderTypes();

        foreach ($dataProviderTypes as $dataProviderType) {
            if (empty($dataProviderType)) {
                $dataProviderType = DataProviderFactory::PROVIDER_SELLING_API;
            }

            $count = AmazonReport::find()
                ->where([
                    'amazon_id' => null,
                    'amazon_processing_status' => AmazonReport::PROCESSING_STATUS_UNKNOWN,
                    'data_provider' => $dataProviderType,
                    'seller_id' => $sellerId,
                ])
                ->count();
            if ($count > 0) {
                $this->info("Add sellerId to handle queue for amazon reports {$sellerId}");
                $this->messagesSender->handleCreateReportsInAmazon($sellerId, $dataProviderType);
            }
        }
    }

    /**
     * Walk through AmazonReport records with amazon status UNKNOWN and create reports in Amazon.
     * @param string $sellerId
     * @return void
     */
    public function createReportsInAmazon(string $sellerId, $dataProviderType): ProcessingResult
    {
        $this->info("Creating reports for seller {$sellerId}");
        $this->dbManager->setSellerId($sellerId);

        $dataProvider = $this->dataProviderFactory->getDataProvider($dataProviderType);

        if ($dataProvider instanceof RemoteFileDataProviderInterface) {
            $maxReportsToCreate = $dataProvider->getMaxReportsToCreatePerIteration();
        } else {
            $maxReportsToCreate = 100;
        }

        $processingResult = new ProcessingResult();

        $reportsQueue = AmazonReport::find()
            ->where([
                'amazon_id' => null,
                'amazon_processing_status' => AmazonReport::PROCESSING_STATUS_UNKNOWN,
                'data_provider' => $dataProviderType,
                'seller_id' => $sellerId,
            ])
            // Temporary exclude all INIT reports related to orders
//            ->andWhere([
//                'OR',
//                ['!=', 'amazon_type', 'GET_FLAT_FILE_ALL_ORDERS_DATA_BY_LAST_UPDATE_GENERAL'],
//                ['=', 'type', AmazonReport::TYPE_REFRESH]
//            ])
            ->limit($maxReportsToCreate)
            ->orderBy(['start_date' => SORT_DESC])
            ->all();


        $this->info("Found " . count($reportsQueue) . " reports to create");

        /** @var AmazonReport[] $reportsQueue */
        foreach ($reportsQueue as $report) {
            try {
                if ($dataProvider instanceof RemoteFileDataProviderInterface) {
                    $config = $this->configurator->getConfig($report->amazon_type);

                    if (!$config->isActive()) {
                        $this->info("Configuration is not active");
                        $report->delete();
                        continue;
                    }

                    if ($config->hasHistoricalData()) {
                        $minAllowedDate = time() - $config->getMinDateOffsetMinutes() * 60;
                        if (strtotime($report->start_date) <= $minAllowedDate) {
                            $this->info("Report start date is too old, skipping");
                            $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_CANCELLED);
                            $report->setStatus(AmazonReport::STATUS_CANCELLED, 'Report start date is too old');
                            $report->save(false);
                            continue;
                        }
                    }

                    $existingOne = $this->reportsSynchronizer->findSimilarExternalReport(
                        $dataProvider,
                        $report,
                        $config->isDaily(),
                        $config->getExpirationTimeMinutes()
                    );

                    if (empty($existingOne)) {
                        $this->info("Creating report in Amazon for report " . $report->id);
                        $report->amazon_id = $dataProvider->createReportInAmazon($report, $config->getExtraIfo());
                        $report->log("Created report in Amazon with ID {$report->amazon_id}");
                        $report->save(false);
                    } else {
                        $report->log("Similar report found: {$existingOne->id}");
                        $report->setAmazonProcessingStatus($dataProvider->mapStatus($existingOne->status));
                        $report->extra_info = array_merge($report->extra_info, $existingOne->extraInfo);
                        $report->amazon_id = $existingOne->id;
                        $report->save(false);
                    }
                } else {
                    $this->info("Report {$report->id} marked as done");
                    $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_DONE);
                    $report->save(false);
                    $processingResult->processedCount += 1;
                }
            } catch (NoTokenEntity $e) {
                $this->info($e->getMessage());
                $report->delete();
            } catch (QuotaExceededException $e) {
                $this->error($e);
                $report->setStatus(AmazonReport::STATUS_WAITING, 'Renew due to quota exception');
                $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_UNKNOWN);
            } catch (CancelOrderException $e) {
                $report->setStatus(
                    AmazonReport::STATUS_CANCELLED,
                    $e->getMessage()
                );
                $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_CANCELLED);
                $this->info($e);
            } catch (\Throwable $e) {
                $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_FATAL);
                $report->setStatus(AmazonReport::STATUS_ERROR);
                $report->log("Error while creating report in Amazon: {$e->getMessage()}");
                $report->save(false, ['log']);
                $this->error($e);
            }
        }

        $processingResult->isSuccess = true;
        return $processingResult;
    }

    public function generateOngoingPeriods(string $sellerId, \DateTime $currTime): void
    {
        /** @var Configuration $configuration */
        foreach ($this->configurator->getConfigs() as $configuration) {

            if (!$configuration->isActive()) {
                continue;
            }

            $lastEndDateCacheKey = implode('_', ['report_last_end_date', $configuration->getUniqueType(), $sellerId]);
            $lastEndDate = \Yii::$app->cache->getOrSet($lastEndDateCacheKey, function () use ($configuration, $sellerId) {
                return AmazonReport::find()
                    ->select('max(end_date)')
                    ->where(['amazon_type' => $configuration->getUniqueType()])
                    ->andWhere(['seller_id' => $sellerId])
                    ->scalar();
            }, 60 * 60);

            $dataProvider = $configuration->getDataProvider();

            if (empty($lastEndDate)) {
                if ($configuration->hasHistoricalData()) {
                    $lastEndDate = (new \DateTime())
                        ->modify('-' . $configuration->getMinDateOffsetMinutes() . ' minutes')
                        ->format('Y-m-d H:i:s');

                    // Need to left few days to be updatable.
                    // Periods for these days will be generated next time.
                    if ($configuration->isPeriodicalUpdate()) {
                        $configuration->setPeriodOffsetMinutes(60 * 24 * 2);
                    }
                } else {
                    $lastEndDate = (new \DateTime())
                        ->modify('-' . ($configuration->getPeriodMinSizeMinutes() + 2) . ' minutes')
                        ->format('Y-m-d H:i:s');
                }
            }

            $minDate = (new \DateTime($lastEndDate))->modify('+1 second');
            $maxDate = (new \DateTime())->modify('-' . $configuration->getPeriodOffsetMinutes() . ' minutes');

            if ($configuration->isDaily()) {
                $maxDate->setTime(23, 59, 59);
                $minDate->setTime(0, 0, 0);
            } else if ($configuration->isMonthly()) {
                $minDate->setDate($minDate->format('Y'), $minDate->format('m'), 1);
                $minDate->setTime(0, 0, 0);

                $maxDate->setTime(23, 59, 59);
                $minDate->setTime(0, 0, 0);
            }

            if ($minDate >= $maxDate) {
                continue;
            }

            $diffMinutes = floor(($maxDate->getTimestamp() - $minDate->getTimestamp()) / 60);

            if ($diffMinutes > $configuration->getPeriodMaxSizeMinutes() * 10) {
                $type = AmazonReport::TYPE_INIT;
            } else {
                $type = AmazonReport::TYPE_REFRESH;
            }
            $periods = $this->periodsHelper->generatePeriods(
                $minDate,
                $maxDate,
                $configuration->getPeriodMaxSizeMinutes(),
                $configuration->getPeriodMinSizeMinutes(),
                $configuration,
            );

            if (count($periods) === 0) {
                continue;
            }

            $transaction = \Yii::$app->db->beginTransaction();
            try {
                foreach ($periods as $period) {
                    $report = new AmazonReport();
                    $report->type = $type;
                    $report->seller_id = $sellerId;
                    $report->amazon_type = $configuration->getUniqueType();
                    $report->start_date = $period->getStartDate();
                    $report->end_date = $period->getFinishDate();
                    $report->extra_info = $configuration->getExtraIfo();
                    $report->data_provider = $configuration->getDataProvider()->getType();

                    if ($configuration->isPeriodicalUpdate()) {
                        $report->stop_repeating_at = date(
                            'Y-m-d H:i:s',
                            strtotime($report->end_date) + $configuration->getDataStabilizationPeriodMinutes() * 60
                        );
                        $report->time_between_attempts_m = $configuration->getTimeBetweenAttemptsMinutes();
                        $report->attempts_made = 0;
                    }

                    $report->setStatus(AmazonReport::STATUS_WAITING);
                    $report->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_UNKNOWN);

                    // Trying to find similar existing report to prevent duplicates
                    if ($dataProvider instanceof BatchSyncInterface) {
                        $existingOne = $this->reportsSynchronizer->findSimilarExternalReport(
                            $dataProvider,
                            $report,
                            $configuration->isDaily(),
                            $configuration->getExpirationTimeMinutes()
                        );

                        if (!empty($existingOne)) {
                            $report->log("Similar report found: {$existingOne->id}");
                            $report->setAmazonProcessingStatus($dataProvider->mapStatus($existingOne->status));
                            $report->extra_info = array_merge($report->extra_info, $existingOne->extraInfo);
                            $report->amazon_id = $existingOne->id;
                        }
                    }
                    $report->saveOrThrowException();

                    if (strtotime($report->end_date) >= strtotime($lastEndDate)) {
                        \Yii::$app->cache->set($lastEndDateCacheKey, $report->end_date, 60 * 60);
                    }
                }
                $transaction->commit();
            } catch (\Throwable $e) {
                $transaction->rollBack();
                $this->error($e);
            }
        }
    }

    public function reInitToUpdateChangedData(): void
    {
        /** @var AmazonReport[] $amazonReports */
        $query = AmazonReport::find()
            ->where([
                'AND',
                [
                    'OR',
                    ['in', 'status', [
                        AmazonReport::STATUS_DONE,
                        AmazonReport::STATUS_ERROR,
                    ]],
                    [
                        'AND',
                        ['in', 'amazon_processing_status', [
                            AmazonReport::STATUS_CANCELLED
                        ]],
                        ['=', 'status', AmazonReport::STATUS_WAITING]
                    ]
                ],
                ['>', 'created_at', date('Y-m-d', strtotime('-10 days'))],
                [
                    '>=',
                    'stop_repeating_at',
                    new Expression('now()')
                ],
                [
                    '<=',
                    new Expression("finished_at + INTERVAL '1 minute' * time_between_attempts_m"),
                    new Expression('now()')
                ]
            ])
            ->orderBy(['end_date' => SORT_DESC]);

        try {
            foreach ($query->batch() as $amazonReports) {
                foreach ($amazonReports as $amazonReport) {
                    $amazonReport->reInit();
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    public function sendReadyToQueue(string $sellerId, string $reportType): void
    {
        $this->info("Sending ready reports to handle queue for seller {$sellerId}");

        $dataProviderTypes = $this->configurator->getRegisteredDataProviderTypes();

        foreach ($dataProviderTypes as $dataProviderType) {
            if (empty($dataProviderType)) {
                $dataProviderType = DataProviderFactory::PROVIDER_SELLING_API;
            }
            $dataProvider = $this->dataProviderFactory->getDataProvider($dataProviderType);

            if ($dataProvider instanceof RemoteFileDataProviderInterface) {
                $maxReports = $dataProvider->getMaxReportsToCreatePerIteration();
            } else {
                $maxReports = 100;
            }

            $countQueued = AmazonReport::find()
                ->where([
                    'seller_id' => $sellerId,
                    'amazon_processing_status' => AmazonReport::PROCESSING_STATUS_DONE,
                    'status' => AmazonReport::STATUS_QUEUED,
                    'type' => $reportType,
                    'data_provider' => $dataProviderType,
                ])
                ->noCache()
                ->count();

            if ($countQueued >= $maxReports) {
                $this->info("Too much reports in queue");
                continue;
            }

            /** @var AmazonReport $report */
            $reports = AmazonReport::find()
                ->where([
                    'seller_id' => $sellerId,
                    'amazon_processing_status' => AmazonReport::STATUS_DONE,
                    'status' => AmazonReport::STATUS_WAITING,
                    'type' => $reportType,
                    'data_provider' => $dataProviderType,
                ])
                ->orderBy(['start_date' => SORT_DESC])
                ->limit($maxReports)
                ->all() ?? [];

            foreach ($reports as $report) {
                $this->messagesSender->handleReadyAmazonReport($report);
            }
        }
    }

    protected function sleep(float $rate): void
    {
        $sleepSeconds = 1 / $rate;
        $sleepSeconds = ceil($sleepSeconds);
        $sleepSeconds += $sleepSeconds * 0.1;

        $this->info("Sleeping for {$sleepSeconds} seconds to prevent quota limit");
        sleep($sleepSeconds);
    }
}
