<?php

namespace common\components\reports;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\dataProvider\BatchSyncInterface;
use common\components\reports\dataProvider\DataProviderFactory;
use common\components\reports\dataProvider\DataProviderInterface;
use common\components\reports\dataProvider\RemoteFileDataProviderInterface;
use common\components\reports\dto\ExternalReport;
use common\components\services\PeriodsHelper;
use common\components\tokenService\Exception\NoTokenEntity;
use common\models\customer\AmazonReport;
use yii\caching\CacheInterface;
use yii\db\Expression;

/**
 * Used to interact with the reports API.
 */
class ReportsSynchronizer
{
    use LogToConsoleTrait;

    protected PeriodsHelper $periodsHelper;
    protected DbManager $dbManager;
    protected Configurator $configurator;
    protected CacheInterface $cache;
    protected MessagesSender $messagesSender;
    protected DataProviderFactory $dataProviderFactory;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->periodsHelper = new PeriodsHelper();
        $this->configurator = new Configurator();
        $this->cache = \Yii::$app->fastPersistentCache;
        $this->messagesSender = new MessagesSender();
        $this->dataProviderFactory = new DataProviderFactory();
    }

    public function sync(string $sellerId): void
    {
        $this->info("Syncing statuses for seller {$sellerId}");
        $this->dbManager->setSellerId($sellerId);

        $dataProviderTypes = $this->configurator->getRegisteredDataProviderTypes();

        foreach ($dataProviderTypes as $dataProviderType) {
            try {
                $dataProvider = $this->dataProviderFactory->getDataProvider($dataProviderType);
                if (!$dataProvider instanceof RemoteFileDataProviderInterface) {
                    continue;
                }

                $this->info("Syncing statuses for data provider $dataProviderType");

                $maxReportsToSync = $dataProvider->getMaxReportsToSyncPerIteration();

                /** @var AmazonReport[] $reportsForSync */
                $reportsForSync = AmazonReport::find()
                    ->where([
                        'AND',
                        ['=', 'seller_id', $sellerId],
                        ['not in', 'amazon_processing_status', [
                            AmazonReport::PROCESSING_STATUS_DONE,
                            AmazonReport::PROCESSING_STATUS_CANCELLED,
                            AmazonReport::PROCESSING_STATUS_FATAL
                        ]],
                        ['is not', 'amazon_id', new Expression('NULL')],
                        ['>=', 'created_at', date('Y-m-d H:i:s', strtotime('-5 days'))],
                        ['data_provider' => $dataProviderType],
                    ])
                    ->orderBy('end_date DESC')
                    ->limit($maxReportsToSync)
                    ->all();
                $this->info('Count reports for sync: ' . count($reportsForSync));

                if (empty($reportsForSync)) {
                    continue;
                }

                if ($dataProvider instanceof BatchSyncInterface) {
                    $this->syncBatch($dataProvider, $sellerId, $reportsForSync);
                } else {
                    $this->syncOneByOne($dataProvider, $reportsForSync);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function findSimilarExternalReport(
        DataProviderInterface $dataProvider,
        AmazonReport $amazonReport,
        bool $isDaily = false,
        int $expirationTimeMinutes = 0
    ): ?ExternalReport
    {
        if (!$dataProvider instanceof BatchSyncInterface) {
            return null;
        }

        $externalReports = $this->getLatestExternalReports($dataProvider, $amazonReport->seller_id, true);
        $ourUniqId = $dataProvider->generateUniqueIdBasedOnExtraInfo($amazonReport->extra_info);

        foreach ($externalReports as $externalReport) {
            $theirUniqueId = $dataProvider->generateUniqueIdBasedOnExtraInfo($externalReport->extraInfo);
            $status = $dataProvider->mapStatus($externalReport->status);

            if (in_array($status, [AmazonReport::PROCESSING_STATUS_FATAL, AmazonReport::PROCESSING_STATUS_CANCELLED])) {
                continue;
            }

            if ($ourUniqId !== $theirUniqueId) {
                continue;
            }

            $ourDateStart = new \DateTime($amazonReport->start_date);
            $ourDateEnd = new \DateTime($amazonReport->end_date);
            $theirDateStart = $externalReport->dateStart;
            $theirDateEnd = $externalReport->dateEnd;

            // Time is no matter, date - is only matter in daily reports
            if ($isDaily) {
                $theirDateEnd->modify('+' . $expirationTimeMinutes . ' minutes');
                $theirDateStart->modify('-' . $expirationTimeMinutes . ' minutes');
                
                $ourDateEnd->setTime(23, 59, 59);
                $theirDateEnd->setTime(23, 59, 59);
                $ourDateStart->setTime(0, 0);
                $theirDateStart->setTime(0, 0);
            }

            if ($ourDateStart >= $theirDateStart && $ourDateEnd <= $theirDateEnd) {
                return $externalReport;
            }
        }

        return null;
    }

    /**
     * @param DataProviderInterface $dataProvider
     * @param AmazonReport[] $reportsForSync
     * @return void
     */
    protected function syncOneByOne(RemoteFileDataProviderInterface $dataProvider, array $reportsForSync): void
    {
        $this->info('Syncing one by one started');

        foreach ($reportsForSync as $amazonReport) {
            try {
                $externalReport = $dataProvider->getExternalReport($amazonReport);
                $this->handleSyncResult($amazonReport, $externalReport, $dataProvider);
            } catch (NoTokenEntity $e) {
                $this->info($e->getMessage());
                $amazonReport->delete();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
        $this->info('Syncing one by one finished');
    }

    /**
     * @param BatchSyncInterface $dataProvider
     * @param string $sellerId
     * @param AmazonReport[] $amazonReports
     * @return void
     */
    protected function syncBatch(BatchSyncInterface $dataProvider, string $sellerId, array $amazonReports)
    {
        $this->info('Syncing batch started');
        try {
            /** @var ExternalReport[] $statuses */
            $latestExternalReports = $this->getLatestExternalReports($dataProvider, $sellerId, false);

            foreach ($amazonReports as $amazonReport) {
                $externalReport = $latestExternalReports[$amazonReport->amazon_id] ?? null;
                if (empty($externalReport)) {
                    $amazonReport->setStatus(AmazonReport::STATUS_ERROR);
                    $amazonReport->setAmazonProcessingStatus(AmazonReport::PROCESSING_STATUS_FATAL);
                    $amazonReport->log('External report not found');
                    $amazonReport->save(false);
                    continue;
                }

                $this->handleSyncResult($amazonReport, $externalReport, $dataProvider);
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->info('Syncing batch finished');
    }

    protected function handleSyncResult(
        AmazonReport $amazonReport,
        ExternalReport $externalReport,
        DataProviderInterface $dataProvider
    ): void
    {
        $this->info("Sync report $amazonReport->id | {$amazonReport->amazon_processing_status}");
        $internalStatus = $dataProvider->mapStatus($externalReport->status);

        if ($internalStatus !== $amazonReport->amazon_processing_status) {
            $amazonReport->setAmazonProcessingStatus($internalStatus);
            $this->info("Report $amazonReport->id status changed to $internalStatus");
        }

        $amazonReport->extra_info = array_merge($amazonReport->extra_info, $externalReport->extraInfo);
        $amazonReport->save(false);
    }

    /**
     * @param DataProviderInterface $dataProvider
     * @param string $sellerId
     * @param bool $useCache
     * @return ExternalReport[]
     */
    public function getLatestExternalReports(
        DataProviderInterface $dataProvider,
        string $sellerId,
        bool $useCache = true
    ): array
    {
        $cacheKey = implode('_', ['latest_external_reports', $dataProvider->getType(), $sellerId]);
        $allReports = $this->cache->get($cacheKey);

        if ($useCache && false !== $allReports) {
            return $allReports;
        }
        $allReports = $allReports ?: [];

        /** @var ExternalReport[] $statuses */
        $latestExternalReports = $dataProvider->getLatestExternalReports($sellerId);
        foreach ($latestExternalReports as $k => $externalReport) {
            $latestExternalReports[$externalReport->id] = $externalReport;
            unset($latestExternalReports[$k]);
        }
        $allReports = array_replace($allReports, $latestExternalReports);
        krsort($allReports);

        $allReports = array_slice($allReports, 0, 1500, true);
        $this->cache->set($cacheKey, $allReports, 60 * 60 * 2);

        return $allReports;
    }
}
