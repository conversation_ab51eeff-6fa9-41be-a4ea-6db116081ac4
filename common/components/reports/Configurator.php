<?php

namespace common\components\reports;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\reports\dataProvider\AmazonAdsApiCallProvider;
use common\components\reports\dataProvider\AmazonAdsReportsV2Provider;
use common\components\reports\dataProvider\AmazonAdsReportsV3Provider;
use common\components\reports\dataProvider\SellingApiProvider;
use common\components\reports\dto\Configuration;
use common\components\reports\dataProvider\DataProviderInterface;
use Symfony\Component\OptionsResolver\Exception\NoConfigurationException;

/**
 * Used to interact with the reports API.
 */
class Configurator
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    /**
     * @return DataProviderInterface[]
     */
    public function getRegisteredDataProviders(): array
    {
        return [
            new AmazonAdsReportsV2Provider(),
            new AmazonAdsReportsV3Provider(),
            new AmazonAdsApiCallProvider(),
            new SellingApiProvider()
        ];
    }

    /**
     * @return string[]
     */
    public function getRegisteredDataProviderTypes(): array
    {
        $types = [];

        foreach ($this->getRegisteredDataProviders() as $dataProvider) {
            $types[] = $dataProvider->getType();
        }

        return $types;
    }

    /**
     * @return Configuration[]
     */
    public function getConfigs(): array
    {
        $dataProviders = $this->getRegisteredDataProviders();

        $configs = [];
        foreach ($dataProviders as $dataProvider) {
            try {
                $dataProviderConfigs = $dataProvider->getConfigurations();

                foreach ($dataProviderConfigs as $dataProviderConfig) {
                    $dataProviderConfig->setDataProvider($dataProvider);
                }

                $configs = array_merge($configs, $dataProviderConfigs);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        return $configs;
    }

    public function getConfig(string $amazonReportType): Configuration
    {
        foreach ($this->getConfigs() as $config) {
            if (false !== strpos($amazonReportType, $config->getType())) {
                return $config;
            }
        }
        throw new NoConfigurationException("Unable to find configuration for amazon report with type $amazonReportType");
    }
}