<?php

namespace common\components;

use yii\db\ActiveQuery;
use yii\db\Expression;

class ExtendedQuery extends ActiveQuery
{
    public const ENGINE_POSTGRES = 'postgres';
    public const ENGINE_CLICKHOUSE = 'clickhouse';

    protected const NOT_AVAILABLE_VALUE = 'na';
    protected string $dbEngine = self::ENGINE_POSTGRES;

    public function __construct($modelClass, $config = [])
    {
        parent::__construct($modelClass, $config);
    }

    public function setDbEngine(string $dbEngine): void
    {
        $this->dbEngine = $dbEngine;
    }

    public function andFilterCompare(
        $name,
        $value,
        $defaultOperator = '=',
        string $valueType = null,
        bool $isAllowMultiSearch = true,
        bool $shouldUseHaving = false
    ) {
        $valueTypes = [
            'int', 'float', 'boolean', null
        ];

        if (!in_array($valueType, $valueTypes)) {
            throw new \Exception("Invalid value type {$valueType}");
        }

        if ($this->dbEngine === self::ENGINE_CLICKHOUSE) {
            $nameAsString= "toString({$name})";
        } else {
            $nameAsString = "{$name}::text";
        }

        if (($valueType === 'int' || $valueType === 'float') && $value === '-') {
            $value = '<0';
        }

        if ($valueType === 'boolean') {
            $defaultOperator = '=';
            $valueType = !!$valueType;
        }

        $andFilterWhereFnName = $shouldUseHaving ? 'andFilterHaving' : 'andFilterWhere';

        if ($isAllowMultiSearch && !in_array($valueType, ['float', 'boolean'])) {
            if (is_array($value)) {
                $parts = $value;
            } else {
                // Allow search for string with comma inside:
                // a1,"a2,a3",a4 => ["a1", "a2,a3", "a4"]
                // "a1" => ["a1"]
                $parts = str_getcsv($value);
            }

            // Allow multi search only on simple one word strings
            foreach ($parts as $k => $part) {
                if (empty($part)) {
                    continue;
                }

                $parts[$k] = trim($part, '= ');
            }

            if (count($parts) > 1) {
                $orQuery = ['OR'];
                foreach ($parts as $part) {
                    $orQuery[] = ['=', new Expression($nameAsString), $part];
                }

                if (!is_array($value)) {
                    $orQuery[] = ['=', new Expression($nameAsString), $value];
                }

                return $this->{$andFilterWhereFnName}($orQuery);
            }
        }

        if (!is_array($value) && preg_match('/^(<>|>=|>|<=|<|=)/', $value, $matches)) {
            $operator = $matches[1];
            $value = substr($value, strlen($operator));
        } else {
            $operator = $defaultOperator;
        }

        if (!empty($value) && !is_array($value)) {
            if (mb_strtolower(preg_replace('/[\s+\/]/', '', $value)) === self::NOT_AVAILABLE_VALUE) {
                return $this->{$andFilterWhereFnName}([
                    'OR',
                    new Expression("{$nameAsString} = ''"),
                    ['is', $name, new Expression('NULL')],
                ]);
            }

            if ($valueType === 'int') {
                $value = is_numeric($value) ? intval($value) : null;
            } elseif ($valueType === 'float') {
                $value = str_replace(',', '.', $value);
                $value = is_numeric($value) ? floatval($value) : null;
            }
        }

        if ($operator === 'like') {
            $name = new Expression("LOWER($nameAsString)");
            if ($this->dbEngine === self::ENGINE_CLICKHOUSE) {
                $value = strtolower($value);
            } else {
                $value = mb_strtolower($value);
            }
        }

        if ($operator === 'arrayExists') {
            $tagsArray = explode(',', $value);

            $includeNoTags = false;
            if (in_array('0', $tagsArray)) {
                $includeNoTags = true;
                $tagsArray = array_filter($tagsArray, fn($tag) => $tag !== '0');
            }

            $conditions = ['OR'];

            if ($includeNoTags) {
                $conditions[] = new Expression("arrayLength($name) = 0");
            }

            if (!empty($tagsArray)) {
                $tagsArrayString = '[' . implode(',', $tagsArray) . ']';
                $conditions[] = new Expression("arrayExists(x -> has($name, x), $tagsArrayString)");
            }

            return $this->andWhere($conditions);
        }


        return $this->{$andFilterWhereFnName}([$operator, $name, $value]);
    }
}
