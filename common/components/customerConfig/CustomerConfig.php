<?php

namespace common\components\customerConfig;

use common\components\core\db\dbManager\DbManager;
use yii\caching\CacheInterface;

class CustomerConfig
{
    // Internal flags (feature flags)
    public const PARAMETER_INTEGER_MONEY_ACCURACY = 'integer_money_accuracy';
    public const PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED = 'is_clickhouse_data_replicated';
    public const PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2 = 'from_db_to_clickhouse_v2';
    public const PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES = 'use_realtime_order_data_updates';

    // Account related settings
    public const PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID = 'product_costs_global_marketplace_id';
    public const PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC = 'product_costs_global_marketplace_is_enabled_cost_of_goods_sync';
    public const PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC = 'product_costs_global_marketplace_is_enabled_other_fees_sync';
    public const PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC = 'product_costs_global_marketplace_is_enabled_fba_shipping_cost_sync';
    public const PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION = 'product_costs_global_marketplace_is_enabled_fba_shipping_cost_sync';

    protected const CACHE_TIME = 60 * 5;

    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct(DbManager $dbManager, CacheInterface $cache)
    {
        $this->dbManager = $dbManager;
        $this->cache = $cache;
    }

    public function set(string $parameter, $value, string $sellerId = null): void
    {
        $customerId = $this->dbManager->getCustomerId();
        /** @var \common\models\CustomerConfig $customerConfig */
        $customerConfig = \common\models\CustomerConfig::find()
            ->where([
                'customer_id' => $customerId,
                'seller_id' => $sellerId,
                'parameter' => $parameter
            ])->one();

        if (empty($customerConfig)) {
            $customerConfig = new \common\models\CustomerConfig();
            $customerConfig->customer_id =  $this->dbManager->getCustomerId();
            $customerConfig->seller_id = $sellerId;
            $customerConfig->parameter = $parameter;
        }

        if ($value === false) {
            $value = 0;
        } else if ($value === true) {
            $value = 1;
        }

        $customerConfig->value = $value;

        if (null === $value) {
            $customerConfig->delete();
        } else {
            $customerConfig->save(false);
        }

        $cacheKey = $this->getCacheKey($parameter, $this->dbManager->getCustomerId(), $sellerId);
        $this->cache->set($cacheKey, $value, self::CACHE_TIME);

        if (null === $value) {
            $this->cache->set($cacheKey . '_as_object', null, self::CACHE_TIME);
        } else {
            $this->cache->set($cacheKey . '_as_object', $customerConfig, self::CACHE_TIME);
        }
    }

    public function get(string $parameter, string $defaultValue = null, string $sellerId = null)
    {
        $customerId = $this->dbManager->getCustomerId();
        $cacheKey = $this->getCacheKey($parameter, $customerId, $sellerId);
        $value = $this->cache->get($cacheKey);

        if (false !== $value) {
            return $value;
        }

        /** @var \common\models\CustomerConfig $customerConfig */
        $customerConfig = \common\models\CustomerConfig::find()
            ->where([
                'customer_id' => $customerId,
                'seller_id' => $sellerId,
                'parameter' => $parameter
            ])->one();

        $value = $customerConfig->value ?? $defaultValue;
        $this->cache->set($cacheKey, $value, self::CACHE_TIME);
        $this->cache->set($cacheKey . '_as_object', $customerConfig, self::CACHE_TIME);

        return $value;
    }

    /**
     * Returns configuration as object (including all columns).
     *
     * @param string $parameter
     * @param string|null $defaultValue
     * @param string|null $sellerId
     * @return \common\models\CustomerConfig
     */
    public function getAsObject(string $parameter, string $defaultValue = null, string $sellerId = null): ?\common\models\CustomerConfig
    {
        $customerId = $this->dbManager->getCustomerId();
        $cacheKey = $this->getCacheKey($parameter, $customerId, $sellerId);
        $configAsObject = $this->cache->get($cacheKey . '_as_object');

        if (false !== $configAsObject) {
            return $configAsObject;
        }

        $this->get($parameter, $defaultValue, $sellerId);
        $configAsObject = $this->cache->get($cacheKey . '_as_object');

        return $configAsObject ?: null;
    }

    protected function getCacheKey(string $parameter, int $customerId, string $sellerId = null): string
    {
        return implode('_', array_filter([
            'customer-config',
            $parameter,
            $customerId,
            $sellerId
        ]));
    }
}