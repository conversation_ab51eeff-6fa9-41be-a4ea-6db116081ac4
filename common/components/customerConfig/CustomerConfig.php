<?php

namespace common\components\customerConfig;

use common\components\core\db\dbManager\DbManager;
use yii\caching\CacheInterface;

class CustomerConfig
{
    public const PARAMETER_INTEGER_MONEY_ACCURACY = 'integer_money_accuracy';
    public const PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED = 'is_clickhouse_data_replicated';
    public const PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2 = 'from_db_to_clickhouse_v2';
    public const PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES = 'use_realtime_order_data_updates';

    protected const CACHE_TIME = 60 * 5;

    protected DbManager $dbManager;
    protected CacheInterface $cache;

    public function __construct(DbManager $dbManager, CacheInterface $cache)
    {
        $this->dbManager = $dbManager;
        $this->cache = $cache;
    }

    public function set(string $parameter, $value): void
    {
        /** @var \common\models\CustomerConfig $customerConfig */
        $customerConfig = \common\models\CustomerConfig::find()
            ->where([
                'customer_id' => $this->dbManager->getCustomerId(),
                'parameter' => $parameter
            ])->one();

        if (empty($customerConfig)) {
            $customerConfig = new \common\models\CustomerConfig();
            $customerConfig->customer_id =  $this->dbManager->getCustomerId();
            $customerConfig->parameter = $parameter;
        }

        if ($value === false) {
            $value = 0;
        } else if ($value === true) {
            $value = 1;
        }

        $customerConfig->value = $value;

        if (null === $value) {
            $customerConfig->delete();
        } else {
            $customerConfig->save(false);
        }

        $cacheKey = $this->getCacheKey($parameter);
        $this->cache->set($cacheKey, $value, self::CACHE_TIME);
    }

    public function get(string $parameter, string $defaultValue = null)
    {
        $cacheKey = $this->getCacheKey($parameter);
        $value = $this->cache->get($cacheKey);

        if (false !== $value) {
            return $value;
        }

        /** @var \common\models\CustomerConfig $customerConfig */
        $customerConfig = \common\models\CustomerConfig::find()
            ->where([
                'customer_id' => $this->dbManager->getCustomerId(),
                'parameter' => $parameter
            ])->one();

        $value = $customerConfig->value ?? $defaultValue;
        $this->cache->set($cacheKey, $value, self::CACHE_TIME);

        return $value;
    }

    protected function getCacheKey(string $parameter): string
    {
        return implode('_', ['customer-config', $parameter, $this->dbManager->getCustomerId()]);
    }
}