<?php

namespace common\components\fbaStorageFee\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\BufferFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;
use common\models\customer\Product;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use common\models\Seller;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class FbaStorageFeeTransactionConsumer extends BaseConsumer
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CustomerComponent $customerComponent;
    protected BufferInterface $transactionsBuffer;
    protected CurrencyRateManager $currencyRateManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->currencyRateManager = new CurrencyRateManager();
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    /**
     * @param AMQPMessage $msg
     * @return int
     */
    public function __execute(AMQPMessage $msg): int
    {
        $this->info($msg->body);
        $sellerId = $msg->body['seller_id'];
        //$customerId = $msg->body['customer_id'];
        $this->dbManager->setSellerId($sellerId);
        //$this->dbManager->setCustomerId($customerId);

        $this->transactionsBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();

        try {
            $message = $msg->body;

            $this->dbManager->setSellerId($sellerId);

            $product = $this->findProductByAsin(
                $message['asin'],
                $message['seller_id'],
                $message['marketplace_id']
            );

            $sku = $message['sku'];
            $asin = $message['asin'];

            if (!empty($product) && ($sku == '' or $sku == null)) {
                $sku = $product->sku;
            }

            if (isset($message['fee_id']) && $message['fee_id']) {
                $fee = FbaStorageFeeHistory::findOne($message['fee_id']);
            } else {
                $fee = FbaStorageFeeHistory::find()
                    ->where([
                        'marketplace_id' => $message['marketplace_id'],
                        'seller_id' => $message['seller_id'],
                        'sku' => $message['sku'],
                        'report_type' => $message['report_type'],
                        'date' => $message['date'],
                    ])
                    ->one();
            }

            if (empty($product)) {
                return ConsumerInterface::MSG_ACK;
            }

            $amount = $message['diff_amount'] !== null ? $message['diff_amount'] : $message['amount'];
            
            if ($amount === null) {
                $fee->markAsMovedToClickhouse();
                return ConsumerInterface::MSG_ACK;
            }

            $euAmazonFeesVat = Seller::getEuAmazonFeesVat($message['seller_id']);
            $this->info('EU VAT: ' . $euAmazonFeesVat);

            if ($euAmazonFeesVat === null) {
                $globalVATValue = $this->customerComponent->getDefaultVATValue(
                    $message['marketplace_id'],
                    $message['seller_id']
                );
                $this->info('Global VAT: ' . $globalVATValue);
                if (null !== $globalVATValue) {
                    $amount *= (1 + $globalVATValue * 0.01);
                }
            }

            $transaction = $this->generateTransactions(
                $message['seller_id'],
                $message['marketplace_id'],
                $sku,
                $asin,
                $message['report_type'],
                $message['date'],
                $amount,
                $message['currency_code']
            );
            $this->transactionsBuffer->put([$transaction]);
            $this->transactionsBuffer->flush();


            if ($fee) {
                $fee->markAsMovedToClickhouse();
                $this->info("Fee ID {$message['fee_id']} marked as moved to Clickhouse");
            }

            return ConsumerInterface::MSG_ACK;
        } catch (\Throwable $e) {
            $this->error($e);
            $this->transactionsBuffer->remove();
            return ConsumerInterface::MSG_REJECT;
        }
    }

    protected function findProductByAsin(string $asin, string $sellerId, string $marketplaceId): ?Product
    {
        try {
            $product = Product::find()
                ->where([
                    'asin' => $asin,
                    'seller_id' => $sellerId,
                    'marketplace_id' => $marketplaceId,
                    'stock_type' => Product::STOCK_TYPE_FBA
                ])
                ->select(['sku'])
                ->one();

            return $product ?: null;
        } catch (\Throwable $e) {
            $this->error($e);
            return null;
        }
    }

    protected function generateTransactions(
        string $sellerId,
        string $marketplaceId,
        string $sku,
        ?string $asin,
        string $reportType,
        string $date,
        float $amount,
        ?string $currency
    ): Transaction {
        $date = new \DateTime($date);
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();
        $transaction = new Transaction();

        if ($reportType === FbaStorageFee::REPORT_TYPE_LONGTERM) {
            $transaction->CategoryId = FinanceEventCategory::getCustomFbaLongTermStorageFeeId();
        } else {
            $transaction->CategoryId = FinanceEventCategory::getCustomFbaStorageFeeId();
        }

        $transaction->IndirectCostId = 0;
        $transaction->IndirectCostTypeId = 0;
        $transaction->Currency = $currency;
        $transaction->Amount = (int)round($amount * -1 * $moneyAccuracy);
        $transaction->MarketplaceId = $marketplaceId;
        $transaction->SellerId = $sellerId;
        $transaction->PostedDate = $date->format('Y-m-d 00:00:00');
        $transaction->MergeCounter = 1;
        $transaction->SellerSKU = $sku;
        $transaction->ASIN = $asin;
        $transaction->SellerOrderId = '';
        $transaction->AmazonOrderId = '';
        $transaction->COGCategoryId = 0;
        $transaction->Quantity = 0;
        $transaction->EventPeriodId = 0;
        $transaction->TransactionDate = $date->format('Y-m-d 00:00:00');
        $transaction->AmountEUR = $this
                ->currencyRateManager
                ->toBaseCurrency(
                    $amount * -1,
                    $currency,
                    $date
                ) * $moneyAccuracy;
        $transaction->AmountEUR = (int)round($transaction->AmountEUR);

        return $transaction;
    }
}
