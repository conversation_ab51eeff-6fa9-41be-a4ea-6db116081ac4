<?php

namespace common\components\nodes;

use common\components\LogToConsoleTrait;
use common\models\NodesAvailability;
use yii\base\Component;
use yii\caching\CacheInterface;
use yii\caching\TagDependency;
use yii\db\Expression;

class NodesManager extends Component
{
    use LogToConsoleTrait;

    public function getActiveIps(string $serviceType, array $whiteListOfIps, int $customerId): array
    {
        $cacheName = NodesAvailability::getDb()->queryCache;
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->{$cacheName};
        $cacheKey = 'active_nodes_' . $customerId . '_' . $serviceType;
        $activeNodes = $cache->get($cacheKey);


        if (false !== $activeNodes) {
            return $activeNodes;
        }

        try {
            $existingNodes = $this->getExistingNodes($serviceType, $whiteListOfIps, $customerId);
            $inactiveCommonNodes = [];
            $activeNodes = [];

            foreach ($existingNodes as $node) {
                if ($node->customer_id === 0 && $node->status === NodesAvailability::STATUS_INACTIVE) {
                    $inactiveCommonNodes[] = $node->ip;
                }
                if ($node->customer_id == $customerId && $node->status === NodesAvailability::STATUS_ACTIVE) {
                    $activeNodes[] = $node->ip;
                }
            }

            if (count($inactiveCommonNodes) > 0) {
                $activeNodes = array_diff($whiteListOfIps, $inactiveCommonNodes);
            } else {
                $activeNodes = array_merge(
                    $activeNodes,
                    $this->createMissingNodes($existingNodes, $serviceType, $whiteListOfIps, $customerId),
                    $this->reActivateNodesIfNeed($existingNodes)
                );
            }
            $activeNodes = array_unique($activeNodes);
            if (count($activeNodes) === 0) {
                throw new \Exception("No active nodes found");
            }
        } catch (\Throwable $e) {
            $this->error($e);
            $activeNodes = $whiteListOfIps;
        }

        $cache->set(
            $cacheKey,
            $activeNodes,
            60 * 60,
            new TagDependency([
                'tags' => NodesAvailability::getCacheTag($serviceType, $customerId)
            ]),
        );

        return $activeNodes;
    }

    public function markAsInactive(string $serviceType, string $ip, ?string $customerId, ?string $message = null): void
    {
        if (empty($customerId)) {
            return;
        }

        /** @var NodesAvailability $node */
        $node = NodesAvailability::find()
            ->where([
                'AND',
                ['type' => $serviceType],
                ['ip' => $ip],
                ['customer_id' => $customerId]
            ])
            ->noCache()
            ->one();

        if (empty($node)) {
            return;
        }

        $countActive = NodesAvailability::find()
            ->where([
                'AND',
                ['type' => $serviceType],
                ['customer_id' => $customerId],
                ['status' => NodesAvailability::STATUS_ACTIVE]
            ])
            ->noCache()
            ->count();

        if ($countActive <= 1) {
            $node->log("Inactive but Last active service can't be marked as inactive.");
        } else {
            $node->setStatus(NodesAvailability::STATUS_INACTIVE, $message);
        }

        $node->save();
    }

    protected function createMissingNodes(array $existingNodes, string $serviceType, array $whiteListOfIps, int $customerId): array
    {
        $nodesToCreate = [];
        foreach ($whiteListOfIps as $nodeIp) {
            $customerNodeFound = false;
            $commonNodeFound = false;

            foreach ($existingNodes as $node) {
                if ($node->ip !== $nodeIp) {
                    continue;
                }
                if ($node->customer_id > 0) {
                    $customerNodeFound = true;
                }
                if ($node->customer_id === 0) {
                    $commonNodeFound = true;
                }
            }

            if (!$customerNodeFound) {
                $nodesToCreate[] = [
                    'ip' => $nodeIp,
                    'customer_id' => $customerId,
                    'type' => $serviceType
                ];
            }

            if (!$commonNodeFound) {
                $nodesToCreate[] = [
                    'ip' => $nodeIp,
                    'customer_id' => 0,
                    'type' => $serviceType
                ];
            }
        }

        $createdNodeIps = [];
        foreach ($nodesToCreate as $nodeToCreate) {
            try {
                $nodeAvailability = new NodesAvailability();
                $nodeAvailability->customer_id = $nodeToCreate['customer_id'];
                $nodeAvailability->type = $nodeToCreate['type'];
                $nodeAvailability->ip = $nodeToCreate['ip'];
                $nodeAvailability->setStatus(NodesAvailability::STATUS_ACTIVE, 'Initialized');
                $nodeAvailability->save();

                if ($nodeAvailability->customer_id === '' || $nodeAvailability->customer_id === null) {
                    $this->error(new \Exception('Nodes availability with empty customer id'));
                }

                $createdNodeIps[] = $nodeToCreate['ip'];
            } catch (\Exception $e) {
                $createdNodeIps[] = $nodeToCreate['ip'];
            }
        }
        return array_unique($createdNodeIps);
    }

    protected function reActivateNodesIfNeed(array $existingNodes = []): array
    {
        $reActivatedIps = [];
        /** @var NodesAvailability $node */
        foreach ($existingNodes as $node) {
            if ($node->customer_id === 0) {
                continue;
            }

            if ($node->status === NodesAvailability::STATUS_INACTIVE
                && strtotime($node->last_state_check_at) < strtotime('-20 minutes')
            ) {
                $node->setStatus(NodesAvailability::STATUS_ACTIVE, "Automatic attempt to enable node");
                $node->save();
            }

            if ($node->status === NodesAvailability::STATUS_ACTIVE) {
                $reActivatedIps[] = $node->ip;
            }
        }
        return $reActivatedIps;
    }

    /**
     * @param string $serviceType
     * @param array $whiteListOfIps
     * @param int|null $customerId
     * @return NodesAvailability[]
     */
    protected function getExistingNodes(string $serviceType, array $whiteListOfIps, int $customerId): array
    {
        /** @var NodesAvailability[] $nodes */
        return NodesAvailability::find()
            ->where([
                'AND',
                ['type' => $serviceType],
                ['ip' => $whiteListOfIps],
                [
                    'OR',
                    ['customer_id' => $customerId],
                    ['customer_id' => 0]
                ]
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    NodesAvailability::getCacheTag($serviceType, $customerId)
                ]])
            )
            ->all();
    }
}