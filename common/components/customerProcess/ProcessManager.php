<?php

namespace common\components\customerProcess;

use common\components\core\db\dbManager\DbManager;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\FromDbToClickhouseProcess;
use common\components\customerProcess\process\InitDemo;
use common\components\customerProcess\process\ProcessInterface;
use common\components\customerProcess\process\RecreateClickhouseAndReExport;
use common\components\customerProcess\process\ReExportEventPeriodsFromApi;
use common\components\LogToConsoleTrait;
use common\components\customerProcess\process\ProcessFactory;
use common\components\rabbitmq\MessagesSender;
use common\models\CustomerProcess;
use common\models\Seller;
use yii\db\Expression;

class ProcessManager
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected ProcessFactory $processFactory;
    protected MessagesSender $messagesSender;

    public function __construct()
    {
        $this->processFactory = new ProcessFactory();
        $this->messagesSender = new MessagesSender();
        $this->dbManager = \Yii::$app->dbManager;
    }

    /**
     * @return ProcessInterface[]
     */
    public function getAllProcesses(): array
    {
        return [
            new FromDbToClickhouseProcess(),
            new RecreateClickhouseAndReExport(),
            new InitDemo(),
            new ReExportEventPeriodsFromApi()
        ];
    }

    /**
     * Creates records in db for future processing (like a schedule)
     *
     * @param string $processName
     * @return void
     * @throws \Exception
     */
    public function schedule(string $processName, bool $shouldSendToQueue = false, bool $shouldRemoveExisting = false)
    {
        $process = $this->processFactory->getProcess($processName);
        /** @var CustomerProcess $existingOne */
        $existingOne = CustomerProcess::find()
            ->where([
                'AND',
                ['=', 'name', $processName],
                ['=', 'customer_id', $this->dbManager->getCustomerId()],
                ['in', 'status', [
                    CustomerProcess::STATUS_CREATED,
                    CustomerProcess::STATUS_QUEUED,
                    CustomerProcess::STATUS_IN_PROGRESS,
                    CustomerProcess::STATUS_IN_PROGRESS_DELAYED
                ]],
                ['is', 'parent_process_id', new Expression('NULL')]
            ])->one();

        if ($shouldRemoveExisting) {
            CustomerProcess::deleteAll([
                'OR',
                ['=', 'id', $existingOne->id],
                ['=', 'parent_process_id', $existingOne->id],
            ]);
            $existingOne = null;
        }

        if (!empty($existingOne)) {
            $this->info(sprintf(
                "Process %s is already running for customer %s (process_id %s)",
                $processName,
                $this->dbManager->getCustomerId(),
                $existingOne->id
            ));
            return;
        }

        $transaction = CustomerProcess::getDb()->beginTransaction();

        try {
            $commonProcess = $this->scheduleProcessRecursively($process);
            $transaction->commit();

            if ($shouldSendToQueue) {
                $this->messagesSender->invokeCustomerProcess($commonProcess);
            }
        } catch (\Throwable $e) {
            $this->error($e);
            $transaction->rollBack();
        }
    }

    private function scheduleProcessRecursively(
        ProcessInterface $process,
        CustomerProcess $parentCustomerProcess = null
    ): CustomerProcess
    {
        /** @var ProcessInterface[] $children */
        $children = $process->getChildren();

        $commonProcess = new CustomerProcess();
        $commonProcess->customer_id = $this->dbManager->getCustomerId();
        $commonProcess->name = $process->getName();
        $commonProcess->count_all = count($children) > 0
            ? count($children)
            : $process->getCountAll();
        $commonProcess->max_attempts = $process->getMaxAttempts();
        $commonProcess->success_rate_percents = $process->getSuccessRatePercents();
        $commonProcess->next_attempt_delay_s = $process->getNextAttemptDelaySeconds();
        $commonProcess->next_attempt_after = date('Y-m-d H:i:s');
        $commonProcess->parent_process_id = $parentCustomerProcess->id ?? null;
        $commonProcess->config = $process->getConfig();
        $commonProcess->setCreated();

        foreach ($children as $child) {
            $this->scheduleProcessRecursively($child, $commonProcess);
        }

        return $commonProcess;
    }

    public function proceed(CustomerProcess $customerProcess): void
    {
        $processKey = static::class . '_' . md5(serialize($customerProcess));
        /** @var \common\components\processManager\ProcessManager $processManager */
        $processManager = \Yii::$app->processManager;
        $processManager->register($processKey);

        try {
            $this->dbManager->setCustomerId($customerProcess->customer_id, false);
            $waitBeforeExecuteS = strtotime($customerProcess->next_attempt_after) - time();

            if ($waitBeforeExecuteS > 0) {
                $this->info("Sleep {$waitBeforeExecuteS} seconds before next  attempt");
                sleep($waitBeforeExecuteS);
            }

            $customerProcess->setInProgress();
            $this->info(str_repeat('-', 40));
            $this->info($customerProcess->toArray());

            $seller = Seller::find()->where(['=', 'customer_id', $customerProcess->customer_id])->one();

            if (empty($seller)) {
                $customerProcess->setCanceled("Customer is not active, unable to find at least one seller");
                return;
            }

            $process = $this->processFactory->getProcess($customerProcess->name);
            $process->setConfig($customerProcess->config);
            $customerProcess->count_all = $process->getCountAll();
            $customerProcess->save(false);
            $invokeResult = $process->proceed($customerProcess);
            $customerProcess->count_all = $invokeResult->countAll;

            if ($invokeResult->countAll === 0) {
                $invokeResult->status = ProcessingResult::STATUS_SUCCESS;
            }

            $this->info("Processing result");
            $this->info(json_encode($invokeResult, JSON_PRETTY_PRINT));

            switch ($invokeResult->status) {
                case ProcessingResult::STATUS_SUCCESS:
                    $customerProcess->incrementSuccessEvents();
                    break;
                case ProcessingResult::STATUS_ERROR:
                    $customerProcess->setFailed($invokeResult->message);
                    break;
                case ProcessingResult::STATUS_DELAYED:
                    $customerProcess->setInProgressDelayed();
                    break;
            }
        } catch (\Throwable $e) {
            $customerProcess->setFailed("Exception: " . $e->getMessage());
            $this->error($e);
        } finally {
            $customerProcess->save(false);
            $processManager->release($processKey);
        }
    }

    public function updateDelayedProcessStatus(CustomerProcess $customerProcess): void
    {
        $this->dbManager->setCustomerId($customerProcess->customer_id, false);
        $process = $this->processFactory->getProcess($customerProcess->name);
        $process->setConfig($customerProcess->config);

        $countProcessed = $process->getCountProcessed();
        $countFailed = $process->getCountFailed();

        $customerProcess->setCountSuccessEvents($countProcessed);
        $customerProcess->setCountFailedEvents($countFailed);
    }
}