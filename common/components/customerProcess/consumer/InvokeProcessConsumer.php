<?php

namespace common\components\customerProcess\consumer;

use common\components\customerProcess\ProcessManager;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\models\CustomerProcess;
use PhpAmqpLib\Message\AMQPMessage;

class InvokeProcessConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    protected ProcessManager $processManager;

    public function __construct()
    {
        $this->processManager = new ProcessManager();
    }

    /**
     * @param  AMQPMessage $msg
     * @return bool
     */
    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 30));
        $this->info($msg->body);

        $processId = $msg->body['customerProcessId'] ?? null;
        $customerProcess = CustomerProcess::findOne($processId);

        if (empty($customerProcess)) {
            $this->info('Unable to find customer process');
            return self::MSG_ACK;
        }

        if (empty($customerProcess->parent_process_id)) {
            $customerProcess = $customerProcess->getNextProcessForInProgress();
            if (empty($customerProcess)) {
                return self::MSG_ACK;
            }

            $customerProcess->setQueued();
        }

        if ($customerProcess->status != CustomerProcess::STATUS_QUEUED) {
            $this->info("Irrelevant status $customerProcess->status");
            return self::MSG_ACK;
        }

        $this->processManager->proceed($customerProcess);

        return self::MSG_ACK;
    }
}
