<?php

namespace common\components\customerProcess\process;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\customerProcess\dto\ProcessingResult;
use common\models\CustomerProcess;
use yii\helpers\Inflector;

abstract class AbstractProcess implements ProcessInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected array $config = [];

    public function __construct()
    {
        $this->setConfig($this->getDefaultConfig());
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): self
    {
        $defaultConfig = $this->getDefaultConfig();

        foreach ($defaultConfig as $key => $value) {
            $this->config[$key] = $config[$key] ?? $value;
        }

        return $this;
    }

    public function getName(): string
    {
        $path = explode('\\', static::class);
        $class = array_pop($path);
        return Inflector::camel2id($class, '_');
    }

    public function getChildren(): array
    {
        return [];
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        throw new \Exception("Method is not implemented");
    }

    public function getCountAll(): int
    {
        return 1;
    }

    public function getCountProcessed(): int
    {
        return 0;
    }

    public function getCountFailed(): int
    {
        return 0;
    }

    public function getMaxAttempts(): int
    {
        return 3;
    }

    public function getNextAttemptDelaySeconds(): int
    {
        return 10;
    }

    public function getSuccessRatePercents(): int
    {
        return 100;
    }

    protected function getDefaultConfig(): array
    {
        return [];
    }
}