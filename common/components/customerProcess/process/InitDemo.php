<?php

namespace common\components\customerProcess\process;

use common\components\customerProcess\process\sub\FreezeClickhouseTransaction;
use common\components\customerProcess\process\sub\InitDemoData;
use common\components\customerProcess\process\sub\ResetDataInDb;
use common\components\customerProcess\process\sub\TruncateTablesForDemo;
use common\components\customerProcess\process\sub\UnFreezeClickhouseTransaction;

class InitDemo extends AbstractProcess implements ProcessInterface
{
    public function getChildren(): array
    {
        return [
            new FreezeClickhouseTransaction(),
            new ResetDataInDb(),
            new UnFreezeClickhouseTransaction(),

            new TruncateTablesForDemo(),
            new InitDemoData()
        ];
    }

    public function getMaxAttempts(): int
    {
        return 1;
    }
}