<?php

namespace common\components\customerProcess\process\sub;

use common\components\customerConfig\CustomerConfig;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\models\CustomerProcess;
use yii\caching\CacheInterface;

class UnFreezeClickhouseTransaction extends AbstractProcess
{
    private CustomerConfig $customerConfig;

    public function __construct()
    {
        /** @var CustomerConfig $customerConfig */
        $this->customerConfig = \Yii::$container->get('customerConfig');
        parent::__construct();
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            /** @var CacheInterface $cache */
            $cache = \Yii::$app->fastPersistentCache;
            $cache->delete("is_transactions_frozen_" . $this->dbManager->getCustomerId());
            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}