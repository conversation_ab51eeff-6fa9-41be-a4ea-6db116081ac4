<?php

namespace common\components\customerProcess\process\sub;

use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\components\LinuxCommander;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\IndirectCost;
use common\models\customer\TransactionBuffer;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\Seller;
use yii\caching\CacheInterface;
use yii\db\Query;
use yii\helpers\Console;

class ReExportFinishedToClickhouse extends AbstractProcess
{
    protected MessagesSender $messagesSender;

    public function __construct()
    {
        $this->messagesSender = new MessagesSender();
        parent::__construct();
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();
        $result->countAll = 0;

        try {
            $sellersQuery = Seller::find()->where([
                'customer_id' => $this->dbManager->getCustomerId()
            ]);

            $this->info('Apply indirect costs started');
            $query = IndirectCost::find();

            /** @var IndirectCost[] $indirectCosts */
            foreach ($query->batch() as $indirectCosts) {
                foreach ($indirectCosts as $indirectCost) {
                    $this->messagesSender->indirectCostChanges(
                        $this->dbManager->getCustomerId(),
                        $indirectCost->id
                    );
                }
            }

            $this->info('Apply indirect costs finished');

            $customerId = $this->dbManager->getCustomerId();

            $this->info('Apply ppc costs started');
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf(
                    "amazon-ads/apply-costs %d %d",
                    $customerId,
                    $customerId + 1,
                )
            );
            $this->info('Apply ppc costs finished');

            foreach ($sellersQuery->batch() as $sellers) {
                foreach ($sellers as $seller) {
                    $this->dbManager->setSellerId($seller->id);

                    $this->info("Re export finished event periods to clickhouse started {$seller->id}");
                    $eventPeriodQuery = $this->getReExportQuery();
                    $eventPeriodQuery->limit(500);
                    $fromId = 0;

                    while (true) {
                        if (!empty($fromId)) {
                            $eventPeriodQuery->andWhere(['<', 'id', $fromId]);
                        }
                        $eventPeriods = $eventPeriodQuery->all();

                        if (count($eventPeriods) === 0) {
                            break;
                        }

                        $fromId = $eventPeriods[count($eventPeriods) - 1]->id;
                        $toId = $eventPeriods[0]->id;
                        $result->countAll += count($eventPeriods);

                        LinuxCommander::safeExecute(
                            sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                            sprintf(
                                "event-period/re-export-finished-to-clickhouse %s %d %d",
                                $seller->id,
                                $fromId - 1,
                                $toId,
                            )
                        );
                    }
                    $this->info('Re export finished event periods to clickhouse finished');
                }
            }
            // Need here to wait while at least some percent of event periods will be in queue
            sleep(10);

            $result->status = ProcessingResult::STATUS_DELAYED;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }

    public function getCountAll(): int
    {
        $count = 0;
        $sellersQuery = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        foreach ($sellersQuery->batch() as $sellers) {
            foreach ($sellers as $seller) {
                $this->dbManager->setSellerId($seller->id);
                $count += $this->getReExportQuery()->count();
            }
        }

        return $count;
    }

    public function getCountProcessed(): int
    {
        $count = 0;
        $sellersQuery = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        foreach ($sellersQuery->batch() as $sellers) {
            foreach ($sellers as $seller) {
                $this->dbManager->setSellerId($seller->id);
                $eventPeriodQuery = EventPeriod::find();
                $eventPeriodQuery
                    ->andWhere(['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])
                    ->andWhere(['in', 'clickhouse_status', [
                        EventPeriod::CLICKHOUSE_STATUS_MOVED,
                        EventPeriod::CLICKHOUSE_STATUS_WAITING
                    ]])
                    ->orderBy('finish_date DESC');
                $count += $eventPeriodQuery->count();
            }
        }

        return $count;
    }

    public function getCountFailed(): int
    {
        $count = 0;
        $sellersQuery = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        foreach ($sellersQuery->batch() as $sellers) {
            foreach ($sellers as $seller) {
                $this->dbManager->setSellerId($seller->id);
                $eventPeriodQuery = EventPeriod::find();
                $eventPeriodQuery
                    ->andWhere(['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])
                    ->andWhere(['in', 'clickhouse_status', [
                        EventPeriod::CLICKHOUSE_STATUS_ERROR,
                    ]])
                    ->orderBy('finish_date DESC');
                $count += $eventPeriodQuery->count();
            }
        }

        return $count;
    }

    public function getSuccessRatePercents(): int
    {
        return 95;
    }

    public function getMaxAttempts(): int
    {
        return 1;
    }

    protected function getReExportQuery(): Query
    {
        $eventPeriodQuery = EventPeriod::find();
        $eventPeriodQuery
            ->andWhere(['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])
            ->orderBy('id DESC');

        return $eventPeriodQuery;
    }
}
