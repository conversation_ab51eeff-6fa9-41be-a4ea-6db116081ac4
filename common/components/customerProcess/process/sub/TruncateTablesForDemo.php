<?php

namespace common\components\customerProcess\process\sub;

use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\RepricerEvent;
use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\customer\RepricerEventBuffer;
use common\models\customer\RepricerEventStorage;
use common\models\customer\TransactionExtendedView;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;

class TruncateTablesForDemo extends AbstractProcess
{
    public function __construct()
    {
        parent::__construct();
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            if (!$this->dbManager->isDemo()) {
                throw new \Exception("Trying to init demo data on non demo customer");
            }

            $tableNames = [
                RepricerEventBuffer::tableName(),
                RepricerEventStorage::tableName()
            ];
            foreach ($tableNames as $tableName) {
                $this->info("Truncating repricer event table $tableName");
                $this->dbManager->getRepricerEventDb()->createCommand("TRUNCATE TABLE " . $tableName)->execute();
            }

            $tableNames = [
                Product::tableName(),
                ProductCostPeriod::tableName(),
                ProductCostItem::tableName()
            ];
            foreach ($tableNames as $tableName) {
                $this->info("Truncating postgres table $tableName");
                $this->dbManager->getCustomerDb()->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
            }

            $tableNames = [
                \common\models\customer\clickhouse\AmazonOrder::tableName(),
                AmazonOrderExtendedView::tableName(),
                AmazonOrderInProgress::tableName(),
                TransactionExtendedView::tableName(),
                RepricerEvent::tableName(),
            ];
            foreach ($tableNames as $tableName) {
                $this->info("Truncating clickhouse table $tableName");
                $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();
                $clickhouseCustomerDb
                    ->createCommand("TRUNCATE TABLE $tableName")
                    ->execute();
            }

            $sellers = Seller::find()->where([
                '=',  'customer_id',  $this->dbManager->getCustomerId()
            ])->all();

            foreach ($sellers as $seller) {
                $this->dbManager->setSellerId($seller->id);

                $tableNames = [
                    AmazonOrder::tableName(),
                    AmazonOrderItem::tableName(),
                ];
                foreach ($tableNames as $tableName) {
                    $this->info("Truncating postgres table $tableName");
                    $this->dbManager->getOrderDb()->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
                }

                $tableNames = [
                    EventPeriod::tableName()
                ];
                foreach ($tableNames as $tableName) {
                    $this->info("Truncating postgres table $tableName");
                    $this->dbManager->getFinanceDb()->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
                }
            }

            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}