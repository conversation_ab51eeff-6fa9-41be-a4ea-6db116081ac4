<?php

namespace common\components\customerProcess\process\sub;

use common\components\COGSync\COGSynchronizer;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\components\demo\DemoDataManager;
use common\components\demo\LoadRepricerEvents;
use common\components\services\financialEvent\EventPeriodService;
use common\components\services\financialEvent\LoadingEventsService;
use common\models\customer\ProductCostItem;
use common\models\CustomerProcess;
use common\models\Seller;

class InitDemoData extends AbstractProcess
{
    protected DemoDataManager $demoDataManager;

    public function __construct()
    {
        $this->demoDataManager = new DemoDataManager();
        parent::__construct();
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            if (!$this->dbManager->isDemo()) {
                throw new \Exception("Trying to init demo data on non demo customer");
            }

            /** @var COGSynchronizer $COGSynchronizer */
            $COGSynchronizer = \Yii::$container->get('COGSynchronizer');
            $COGSynchronizer->synchronizeChunk(
                $this->dbManager->getCustomerId(), 0, 1000
            );
            ProductCostItem::invalidateCachedItemsForCustomer();

            $sellers = Seller::find()->where([
                '=',  'customer_id',  $this->dbManager->getCustomerId()
            ])->all();

            foreach ($sellers as $seller) {
                $this->dbManager->setSellerId($seller->id);
                (new EventPeriodService($seller))->generateInitPeriods();
                (new LoadingEventsService($seller))->loadInit();
            }
            (new LoadRepricerEvents())->loadInit();

            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}