<?php

namespace common\components\customerProcess\process\sub;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\models\Command;
use common\models\CustomerProcess;

class SyncProducts extends AbstractProcess
{
    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            $this->info('Sync products started');

            Command::create(sprintf(
                "product-cost/sync %d %d",
                $customerProcess->customer_id,
                $customerProcess->customer_id + 1
            ));
            Command::create(sprintf(
                "product/sync %d %d",
                $customerProcess->customer_id,
                $customerProcess->customer_id + 1
            ));

            $this->info('Sync products finished');
            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}