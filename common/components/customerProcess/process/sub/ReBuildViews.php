<?php

namespace common\components\customerProcess\process\sub;

use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\components\dataCompleteness\Checker;
use common\components\dataCompleteness\factor\FactorFactory;
use common\components\LinuxCommander;
use common\components\services\order\TransferOrderService;
use common\models\CustomerProcess;
use yii\caching\CacheInterface;

class ReBuildViews extends AbstractProcess
{
    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            $this->info('Re build views started');

            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf(
                    "seller/rebuild-dynamic-tables %d %d 1",
                    $this->dbManager->getCustomerId(),
                    $this->dbManager->getCustomerId() + 1,
                )
            );

            /** @var CacheInterface $cache */
            $cache = \Yii::$app->fastPersistentCache;
            $cache->delete("is_re_export_in_progress_" . $this->dbManager->getCustomerId());
            (new Checker())->check(FactorFactory::FACTOR_DATA_REASSEMBLY);

            $this->info('Re build views finished');
            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}
