<?php

namespace common\components\customerProcess\process\sub;

use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\components\db\ClickhouseDbHelper;
use common\components\services\financialEvent\EventPeriodService;
use common\components\services\order\TransferOrderService;
use common\models\ads\base\AbstractAdsRecord;
use common\models\ads\SbAdGroupStatistic;
use common\models\ads\SdAdvertisedProduct;
use common\models\ads\SpAdvertisedProduct;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBufferTmp;
use common\models\customer\FbaReturn;
use common\models\customer\OrderBasedTransactionExtendedView;
use common\models\customer\OrderBasedTransactionExtendedViewV1;
use common\models\customer\ProductCostItem;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\customer\TransactionBuffer;
use common\models\customer\TransactionExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use yii\db\Query;

class ResetDataInDb extends AbstractProcess
{
    protected CustomerConfig $customerConfig;

    public function __construct()
    {
        $this->customerConfig = \Yii::$container->get("customerConfig");
        parent::__construct();
    }

    public function getDefaultConfig(): array
    {
        return [
            'shouldDeleteEventPeriods' => false
        ];
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            $config = $this->getConfig();

            $this->info('Truncate transaction buffer started');
            TransactionBuffer::deleteAll();
            $this->info('Truncate transaction buffer finished');

            $this->resetPPCData();

            if ($config['shouldDeleteEventPeriods']) {
                $this->deleteEventPeriods();
            }

            $this->info('Truncate postgres customer table started');
            $tableName = RefundTransactionWithoutProductCost::tableName();
            $this->info("Truncate postgres customer table {$tableName}");
            $customerDb = $this->dbManager->getCustomerDb();
            $customerDb->createCommand("TRUNCATE TABLE $tableName CASCADE")->execute();
            $this->info('Truncate postgres customer table finished');

            $this->resetClickhouseTables();
            $this->resetDataForDemoAccountIfNeed();

            $this->customerConfig->set(
                CustomerConfig::PARAMETER_INTEGER_MONEY_ACCURACY,
                CustomerComponent::INTEGER_MONEY_ACCURACY_NEW
            );
            $this->customerConfig->set(
                CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2,
                true
            );

            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }

    protected function resetDataForDemoAccountIfNeed()
    {
        if (!$this->dbManager->isDemo()) {
            return;
        }

        $this->info('Reset data for demo account started');
        $sellers = Seller::find()->where([
            'AND',
            ['=',  'customer_id',  $this->dbManager->getCustomerId()],
            ['=',  'is_demo',  't']
        ])->all();

        foreach ($sellers as $seller) {
            $this->dbManager->setSellerId($seller->id);
            AmazonOrderItem::deleteAll();
            AmazonOrder::deleteAll();
        }
        $this->info('Reset data for demo account finished');
    }

    protected function resetClickhouseTables(): void
    {
        $this->info('Truncate clickhouse data started');
        $dbHelper = new ClickhouseDbHelper();
        $clickhouseTablesToTruncate = [
            Transaction::tableName(),
            TransactionBufferTmp::tableName()
        ];

        foreach ($clickhouseTablesToTruncate as $tableName) {
            $this->info("Truncate clickhouse table $tableName");
            $aql = "TRUNCATE TABLE {$tableName}";
            $dbHelper->executeOnMasterNodes($aql);
        }
        ProductCostItem::invalidateCachedItemsForCustomer();
        $this->info('Truncate clickhouse data finished');

        $this->info('Waiting while all transactions will disappear (through replication)');
        for ($i = 0; $i < 60 * 10; $i++) {
            $count = (int)Transaction::find()->count();
            $this->info("Count transactions after truncate: $count");

            if (0 === $count) {
                break;
            }
            sleep(5);
        }

        $clickhouseViewsToRemove = [
            OrderBasedTransactionExtendedView::tableName(),
            TransactionExtendedView::tableName(),
            AmazonOrderExtendedView::tableName(),
            AmazonOrderInProgressExtendedView::tableName(),
        ];

        foreach ($clickhouseViewsToRemove as $tableName) {
            $this->info("Remove clickhouse view $tableName");
            $aql = "DROP VIEW IF EXISTS {$tableName}";
            $dbHelper->executeOnMasterNodes($aql);
        }
    }

    protected function resetPPCData(): void
    {
        $this->info('Updating amazon ads related tables started');
        $this->updateBatch(
            SpAdvertisedProduct::class,
            ['moved_to_clickhouse_at' => null, 'status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_CREATED],
        );
        $this->updateBatch(
            SdAdvertisedProduct::class,
            ['moved_to_clickhouse_at' => null, 'status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_CREATED],
        );
        $this->updateBatch(
            SbAdGroupStatistic::class,
            ['moved_to_clickhouse_at' => null, 'status_moved_to_clickhouse' => AbstractAdsRecord::STATUS_CREATED],
        );
    }

    protected function deleteEventPeriods(): void
    {
        $this->info("Deleting event periods");
        $sellersQuery = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        /** @var Seller[] $sellers */
        foreach ($sellersQuery->batch() as $sellers) {
            foreach ($sellers as $seller) {
                $eventPeriodsService = new EventPeriodService($seller);
                $this->info("Deleting event periods for seller " . $seller->id);
                $this->dbManager->setSellerId($seller->id);
                EventPeriod::deleteAll();
                $seller->is_order_init_periods_created = false;
                $seller->is_init_periods_loaded = false;
                $seller->save(false, ['is_order_init_periods_created', 'is_init_periods_loaded']);
                $eventPeriodsService->generateInitPeriods();
            }
        }
    }

    protected function updateBatch(string $className, $attributes, int $batchSize = 200000)
    {
        $this->info("Updating {$className} started");
        $this->info($attributes);

        $fromId = 1;
        while (true) {
            $query1 = $className::find()
                ->select('id')
                ->andWhere(['>', 'id', $fromId])
                ->orderBy('id ASC')
                ->limit($batchSize);

            $toId = (new Query())
                ->select('max(q1.id)')
                ->from(['q1' => $query1])
                ->scalar($className::getDb())
            ;

            if (empty($toId)) {
                break;
            }

            $this->info("Updating {$className} from {$fromId} to {$toId}");
            $className::updateAll($attributes, ['between', 'id', $fromId, $toId]);

            $fromId = $toId;
        }
    }
}
