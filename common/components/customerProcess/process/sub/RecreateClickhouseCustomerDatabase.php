<?php

namespace common\components\customerProcess\process\sub;

use common\components\clickhouse\materializedViews\dictionaries\ProductCostCategoryDict;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\customerConfig\CustomerConfig;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\components\db\ClickhouseDbHelper;
use common\components\LinuxCommander;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\ProxyProduct;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\clickhouse\TransactionBufferTmp;
use common\models\CustomerProcess;

class RecreateClickhouseCustomerDatabase extends AbstractProcess
{
    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            /** @var CustomerConfig $customerConfig */
            $customerConfig = \Yii::$container->get("customerConfig");
            $customerConfig->set(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED, 1);
            $dbHelper = new ClickhouseDbHelper();

            $this->info(str_repeat('-', 15));
            $this->info('Recreate clickhouse databases');
            $customerId = $this->dbManager->getCustomerId();

            $dbName = $this->dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);

            $sql = "DROP DATABASE IF EXISTS {$dbName}";
            $dbHelper->executeOnMasterNodes($sql);

            $sql = "CREATE DATABASE IF NOT EXISTS {$dbName}";
            $this->info($sql);
            $dbHelper->executeOnMasterNodes($sql);

//            $tablesToDrop = [
//                AmazonOrder::tableName(),
//                AmazonOrderInProgress::tableName(),
//                ProxyProduct::tableName(),
//                Transaction::tableName(),
//                TransactionBuffer::tableName(),
//                TransactionBufferTmp::tableName(),
//                $dbName . '.migration',
//            ];
//
//            foreach ($tablesToDrop as $tableName) {
//                $sql = "DROP TABLE IF EXISTS $tableName SYNC";
//                $dbHelper->executeOnMasterNodes($sql);
//            }

            $command = "migrate-clickhouse-customer 0 {$customerId} --interactive=0";
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                $command,
                true
            );
            $this->info('Finished');

            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}