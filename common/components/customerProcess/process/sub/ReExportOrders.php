<?php

namespace common\components\customerProcess\process\sub;

use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\models\CustomerProcess;
use common\models\finance\EventPeriod;
use common\models\order\OrderPeriod;
use common\models\Seller;
use yii\helpers\Console;

class ReExportOrders extends AbstractProcess
{
    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            $this->info('Re export orders started');

            $query = Seller::find()->where([
                'customer_id' => $this->dbManager->getCustomerId()
            ]);

            foreach ($query->batch() as $sellers) {
                foreach ($sellers as $seller) {
                    $this->dbManager->setSellerId($seller->id);
                    $this->info("Re export orders for seller $seller->id");
                    OrderPeriod::deleteAll(['type' => OrderPeriod::TYPE_REFRESH]);
                    OrderPeriod::updateAll(
                        ['loading_status' => OrderPeriod::LOADING_STATUS_NEW],
                        ['type' => OrderPeriod::TYPE_INIT]
                    );
                }
            }

            $this->info('Re export orders finished');
            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}