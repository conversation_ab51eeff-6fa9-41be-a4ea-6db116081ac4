<?php

namespace common\components\customerProcess\process\sub;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\FbaFeeFactorView;
use common\components\clickhouse\materializedViews\views\ReferralFeeFactorView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\customerProcess\dto\ProcessingResult;
use common\components\customerProcess\process\AbstractProcess;
use common\models\CustomerProcess;

class StubClickhouseMaterializedViews extends AbstractProcess
{
    protected DynamicTablesManager $dynamicTablesManager;

    public function __construct()
    {
        $this->dynamicTablesManager = new DynamicTablesManager();

        parent::__construct();
    }

    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult
    {
        $result = new ProcessingResult();

        try {
            $this->info('Converting clickhouse materialized views to stubs to speed up inserts into clickhouse started');

            $this->dynamicTablesManager->rebuildDynamicTable(
                new TransactionExtendedViewV1(true, true),
                true,
                true
            );
            $this->dynamicTablesManager->rebuildDynamicTable(
                new ReferralFeeFactorView(true, true),
                true,
                true
            );
            $this->dynamicTablesManager->rebuildDynamicTable(
                new FbaFeeFactorView(true, true),
                true,
                true
            );

            $this->info('Converting clickhouse materialized views to stubs to speed up inserts into clickhouse finished');

            $result->status = ProcessingResult::STATUS_SUCCESS;
        } catch (\Throwable $e) {
            $result->status = ProcessingResult::STATUS_ERROR;
            $result->message = $e->getMessage();
            $this->error($e);
        }

        return $result;
    }
}
