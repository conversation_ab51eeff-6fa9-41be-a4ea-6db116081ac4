<?php

namespace common\components\customerProcess\process;

use common\components\customerProcess\process\sub\FreezeClickhouseTransaction;
use common\components\customerProcess\process\sub\ReBuildViews;
use common\components\customerProcess\process\sub\RecreateClickhouseCustomerDatabase;
use common\components\customerProcess\process\sub\ReExportFinishedToClickhouse;
use common\components\customerProcess\process\sub\ResetDataInDb;
use common\components\customerProcess\process\sub\UnFreezeClickhouseTransaction;

class ReExportEventPeriodsFromApi extends AbstractProcess implements ProcessInterface
{
    public function getChildren(): array
    {
        return [
            new FreezeClickhouseTransaction(),
            (new ResetDataInDb())
                ->setConfig([
                    'shouldDeleteEventPeriods' => true
                ]),
            new UnFreezeClickhouseTransaction(),
            new ReBuildViews()
        ];
    }

    public function getMaxAttempts(): int
    {
        return 1;
    }
}