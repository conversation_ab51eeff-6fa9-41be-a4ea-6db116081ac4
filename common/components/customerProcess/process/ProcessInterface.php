<?php

namespace common\components\customerProcess\process;

use common\components\customerProcess\dto\ProcessingResult;
use common\models\CustomerProcess;

/**
 * Used for all customer processes.
 */
interface ProcessInterface
{
    /**
     * If process has sub processes, they can be defined and returned in this function.
     *
     * @return ProcessInterface[]
     */
    public function getChildren(): array;

    /**
     * Returns process name (unique) which can be used in process factory.
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Literally executes process.
     *
     * @param CustomerProcess|null $customerProcess
     * @return ProcessingResult
     */
    public function proceed(CustomerProcess $customerProcess = null): ProcessingResult;

    public function getConfig(): array;

    public function setConfig(array $config): self;
    /**
     * Returns count all parts of process should be completed.
     *
     * @return int
     */
    public function getCountAll(): int;

    public function getCountProcessed(): int;

    public function getCountFailed(): int;

    /**
     * Max attempts process can move back from failed to created status.
     *
     * @return int
     */
    public function getMaxAttempts(): int;

    /**
     * Delay in seconds before next attempt.
     *
     * @return int
     */
    public function getNextAttemptDelaySeconds(): int;

    /**
     * Percents of success parts to determine that this process has been successfully finished.
     * By default, it can be 100%.
     *
     * @return int
     */
    public function getSuccessRatePercents(): int;
}