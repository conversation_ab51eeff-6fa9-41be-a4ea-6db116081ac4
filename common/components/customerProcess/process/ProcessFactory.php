<?php

namespace common\components\customerProcess\process;

use yii\helpers\Inflector;

class ProcessFactory
{
    public function getProcess(string $processName): ProcessInterface
    {
        $className = __NAMESPACE__ . '\\' . Inflector::camelize($processName);
        if (class_exists($className)) {
            return new $className;
        }

        $className = __NAMESPACE__ . '\\sub\\' . Inflector::camelize($processName);
        if (class_exists($className)) {
            return new $className;
        }

        throw new \Exception("Unable to find process $processName");
    }
}