<?php

namespace common\components\customerProcess\process;

use common\components\customerProcess\process\sub\FreezeClickhouseTransaction;
use common\components\customerProcess\process\sub\ReBuildViews;
use common\components\customerProcess\process\sub\RecreateClickhouseCustomerDatabase;
use common\components\customerProcess\process\sub\ReExportFinishedToClickhouse;
use common\components\customerProcess\process\sub\ResetDataInDb;
use common\components\customerProcess\process\sub\StubClickhouseMaterializedViews;
use common\components\customerProcess\process\sub\UnFreezeClickhouseTransaction;

class RecreateClickhouseAndReExport extends AbstractProcess implements ProcessInterface
{
    public function getChildren(): array
    {
        return [
            new FreezeClickhouseTransaction(),
            new RecreateClickhouseCustomerDatabase(),
            new ResetDataInDb(),
            new StubClickhouseMaterializedViews(),
            new UnFreezeClickhouseTransaction(),
            new ReExportFinishedToClickhouse(),
            new ReBuildViews()
        ];
    }

    public function getMaxAttempts(): int
    {
        return 1;
    }
}