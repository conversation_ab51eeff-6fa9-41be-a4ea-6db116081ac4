<?php

namespace common\components\customerProcess;

use common\components\LogToConsoleTrait;
use common\models\CustomerProcess;

class DelayedChecker
{
    use LogToConsoleTrait;

    public ProcessManager $processManager;

    public function __construct()
    {
        $this->processManager = new ProcessManager();
    }

    public function startLoop()
    {
        while (true) {
            sleep(5);
            /** @var CustomerProcess $customerProcess */
            $customerProcesses = CustomerProcess::find()->where([
                'AND',
                ['in', 'status', [CustomerProcess::STATUS_IN_PROGRESS_DELAYED]],
            ])->orderBy('id ASC')->all();

            foreach ($customerProcesses as $customerProcess) {
                $this->processManager->updateDelayedProcessStatus($customerProcess);
            }
        }
    }
}