<?php

namespace common\components\customerProcess;

use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\CustomerProcess;
use yii\db\Expression;

class ProcessExecutor
{
    use LogToConsoleTrait;

    public const MAX_IN_PROGRESS_PROCESSES = 1;
    public const MAX_FAILED_PROCESSES = 5;

    public ProcessManager $processManager;
    protected MessagesSender $messagesSender;

    public function __construct()
    {
        $this->messagesSender = new MessagesSender();
        $this->processManager = new ProcessManager();
    }

    public function startLoop()
    {
        while (true) {
            $inProgressProcesses = $this->getInProgressProcesses();
            $failedProcesses = $this->getFailedProcesses();

            $this->info([
                'inProgressProcesses' => count($inProgressProcesses),
                'failedProcesses' => count($failedProcesses)
            ]);

            if (count($failedProcesses) > self::MAX_FAILED_PROCESSES
                || count($inProgressProcesses) > self::MAX_IN_PROGRESS_PROCESSES
            ) {
                $this->info(sprintf(
                    "New processes will not be started. Already in progress %d processes (max %d allowed), already failed %d processes (max %d allowed)",
                    count($inProgressProcesses),
                    self::MAX_IN_PROGRESS_PROCESSES,
                    count($failedProcesses),
                    self::MAX_FAILED_PROCESSES
                ));

                sleep(5);
                continue;
            }

            $createdProcesses = $this->getCreatedProcesses(
                self::MAX_IN_PROGRESS_PROCESSES - count($inProgressProcesses)
            );

            foreach ($createdProcesses as $customerProcess) {
                $waitBeforeExecuteS = strtotime($customerProcess->next_attempt_after) - time();

                if ($waitBeforeExecuteS > 0) {
                    continue;
                }
                $this->messagesSender->invokeCustomerProcess($customerProcess);
            }

            sleep(1);
        }
    }

    /**
     * @return CustomerProcess[]
     */
    protected function getInProgressProcesses(): array
    {
        return CustomerProcess::find()
            ->where([
                'AND',
                ['is', 'parent_process_id', new Expression('NULL')],
                ['in', 'status', [
                    CustomerProcess::STATUS_IN_PROGRESS,
                    CustomerProcess::STATUS_IN_PROGRESS_DELAYED,
                    CustomerProcess::STATUS_QUEUED
                ]]
            ])
            ->noCache()
            ->orderBy('id ASC')
            ->all();
    }

    /**
     * @return CustomerProcess[]
     */
    public function getFailedProcesses(): array
    {
        return CustomerProcess::find()
            ->where([
                'AND',
                ['is', 'parent_process_id', new Expression('NULL')],
                ['in', 'status', [
                    CustomerProcess::STATUS_FAILED
                ]]
            ])
            ->noCache()
            ->orderBy('id ASC')
            ->all();
    }

    /**
     * @return CustomerProcess[]
     */
    public function getCreatedProcesses(int $limit = 100): array
    {
        return CustomerProcess::find()
            ->where([
                'AND',
                ['is', 'parent_process_id', new Expression('NULL')],
                ['in', 'status', [
                    CustomerProcess::STATUS_CREATED
                ]]
            ])
            ->noCache()
            ->orderBy('id ASC')
            ->limit($limit)
            ->all();
    }
}