<?php

namespace common\components;

use yii\console\ExitCode;

class LinuxCommander
{
    public static function safeExecute($path, $command, bool $shouldWait = false, bool $debug = true)
    {
        $command = trim($command);

        return self::execute($path.$command, $shouldWait, $debug);
    }

    /**
     * Execute command and return pid.
     *
     * @param string $command
     *
     * @return int|false
     */
    public static function execute($command, bool $shouldWait = false, bool $debug = true)
    {
        $pid = self::exec($command, $shouldWait, $debug);

        return $pid;
    }

    /**
     * Call psexec for command and return pid or false.
     *
     * @param $command
     * @param  bool      $debug
     * @return false|int
     */
    public static function exec($command, bool $shouldWait = false, bool $debug = true)
    {
        $command = str_replace('\\', '/', $command);

        if (!$shouldWait) {
            if (strpos($command, '>') === false) {
                $command = $command . ' > /dev/null';
            }

            if (strpos($command, '2>&1') === false) {
                $command = $command . ' 2>&1';
            }

            $command = $command . ' & echo $!';
        };

        if ($debug) {
            print "command: $command\n";
        }

        exec($command, $op, $exitCode);

        if ($shouldWait && $exitCode !== ExitCode::OK) {
            throw new \Exception("Command '$command' failed with exit code $exitCode");
        }

        $pid = $op[0] ?? null;

        if (!empty($pid)) {
            return (int)$pid;
        }

        return false;
    }

    /**
     * Check if process by pid if exists.
     *
     * @param int $pid
     *
     * @return bool
     */
    public static function exists($pid)
    {
        exec("ps ax | grep $pid 2>&1", $output);

        foreach ($output as $row) {
            $row_array = explode(" ", $row);
            $check_pid = $row_array [0];

            if ($pid === $check_pid) {
                return true;
            }
        }

        return false;
    }

    /**
     * Kill process by pid.
     *
     * @param int $pid
     *
     * @return string
     */
    public static function kill($pid)
    {
        exec("kill -9 $pid", $output);
        return implode('', $output);
    }
}
