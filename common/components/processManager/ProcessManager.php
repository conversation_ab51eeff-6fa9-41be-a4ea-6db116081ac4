<?php

namespace common\components\processManager;

use common\components\LogToConsoleTrait;
use yii\caching\CacheInterface;
use yii\mutex\Mutex;

/**
 * All important processes should work through this manager to prevent data lost.
 * Invoke "register" before process start and always invoke "release" after.
 * If you want to freeze all processes (prevent starting of new processes), use "freeze" function
 * (and don't forget to "unfreeze" later).
 */
class ProcessManager
{
    private const PROCESS_IDLE_TIMEOUT_SEC = 60 * 2;
    private const COMMON_LOCK_KEY = 'process_manager';
    private const PROCESS_LIST_CACHE_KEY = 'process_manager_processes';

    use LogToConsoleTrait;
    public string $cacheComponentName;
    private ?CacheInterface $cache = null;
    private Mutex $mutex;
    private array $registeredProcesses = [];

    public function __construct()
    {
        $this->mutex = \Yii::$app->mutex;
    }

    public function __destruct()
    {
        foreach ($this->registeredProcesses as $processName => $noMatterInt) {
            $this->release($processName);
        }
    }

    /**
     * Register new working process.
     *
     * @param string $processName
     * @return void
     */
    public function register(string $processName): void
    {
        while ($this->isFrozen()) {
            $this->info("Process $processName, process manager has been frozen, waiting for unfreeze");
            sleep(10);
        }

        $this->acquireCommonLock();
        $allProcesses = $this->getCache()->get(self::PROCESS_LIST_CACHE_KEY) ?? [];
        $allProcesses[$processName] = [
            'name' => $processName,
            'startedAt' => date('Y-m-d H:i:s')
        ];
        $this->info("Count processes ". count($allProcesses));
        $this->registeredProcesses[$processName] = 1;
        $this->info("REGISTER PROCESS $processName");
        $this->getCache()->set(self::PROCESS_LIST_CACHE_KEY, $allProcesses);
        $this->releaseCommonLock();
    }

    /**
     * Releases process.
     *
     * @param string $processName
     * @return void
     */
    public function release(string $processName): void
    {
        $this->acquireCommonLock();
        $allProcesses = $this->getCache()->get(self::PROCESS_LIST_CACHE_KEY) ?? [];
        unset($allProcesses[$processName]);
        unset($this->registeredProcesses[$processName]);
        $this->info("RELEASE PROCESS $processName");
        $this->getCache()->set(self::PROCESS_LIST_CACHE_KEY, $allProcesses);
        $this->releaseCommonLock();
    }

    /**
     * Returns list of working processes.
     *
     * @return array
     */
    public function getAll(bool $isUpdateIfNeed = true): array
    {
        if ($isUpdateIfNeed) {
            $this->acquireCommonLock();
        }
        $processes = $this->getCache()->get(self::PROCESS_LIST_CACHE_KEY);

        if (!is_array($processes)) {
            $processes = [];
        }

        foreach ($processes as $k => $process) {
            $startedAt = new \DateTime($process['startedAt']);
            $currentTime = new \DateTime();

            if ($currentTime->getTimestamp() - $startedAt->getTimestamp() > self::PROCESS_IDLE_TIMEOUT_SEC) {
                unset($processes[$k]);
            }
        }

        if ($isUpdateIfNeed) {
            $this->getCache()->set(self::PROCESS_LIST_CACHE_KEY, $processes);
            $this->releaseCommonLock();
        }

        return $processes;
    }

    /**
     * Freeze processes, new process will wait for unfreeze before they can be registered.
     *
     * @return void
     */
    public function freeze(): void
    {
        $this->getCache()->set(__CLASS__ . '_is_frozen', 1, 60 * 60 * 24);
        $this->info("Process manager has been frozen");
    }

    /**
     * Checks whether process manager has been frozen.
     *
     * @return bool
     */
    public function isFrozen(): bool
    {
        return !!$this->getCache()->get(__CLASS__ . '_is_frozen');
    }

    public function waitForRelease(array $processNames = [], int $timeout = 60 * 5): void
    {
        $timeStarted = time();
        $timeDeadline = $timeStarted + $timeout;
        do {
            if (time() > $timeDeadline) {
                break;
            }
            $processes = $this->getAll(false);
            if (empty($processes)) {
                break;
            }

            if (!empty($processNames)) {
                $isProcessRunning= false;

                foreach ($processNames as $processName) {
                    if (array_key_exists($processName, $processes)) {
                        $isProcessRunning = true;
                        break;
                    }
                }

                if (!$isProcessRunning) {
                    break;
                }
            }

            usleep(0.5 * 1000000);
        } while (true);
    }

    /**
     * Unfreezes all process, so they will be registered after wait.
     *
     * @return void
     */
    public function unfreeze(): void
    {
        $this->getCache()->delete(__CLASS__ . '_is_frozen');
        $this->info("Process manager has been unfreeze");
    }

    public function setCache(CacheInterface $cache): self
    {
        $this->cache= $cache;
        return $this;
    }

    private function acquireCommonLock()
    {
        $this->info("ACQUIRING COMMON LOCK");
        $this->mutex->acquire(self::COMMON_LOCK_KEY, 60);
        $this->info("ACQUIRED COMMON LOCK");
    }

    private function releaseCommonLock()
    {
        $this->mutex->release(self::COMMON_LOCK_KEY);
        $this->info("RELEASED COMMON LOCK");
    }

    /**
     * @return CacheInterface
     */
    private function getCache(): CacheInterface
    {
        if (empty($this->cache)) {
            $this->cache = \Yii::$app->{$this->cacheComponentName};
        }
        return $this->cache;
    }
}