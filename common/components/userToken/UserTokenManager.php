<?php

namespace common\components\userToken;

use common\components\exception\SellerNotFoundException;
use common\models\AuthUser;
use common\models\UserToken;
use SellerLogic\InternalApi\MainApiSimpleClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;
use yii\web\ForbiddenHttpException;
use yii\web\UnauthorizedHttpException;

class UserTokenManager
{
    const GRANT_TYPE_CLIENT_CREDENTIALS = 4;

    /**
     * @param string $token
     * @return UserToken
     * @throws ForbiddenHttpException
     */
    public function getByAccessToken(string $token): UserToken
    {
        try {
            $api = new MainApiSimpleClient([
                'baseApiUri' => \Yii::$app->params['mainApiUrl'],
                'accessToken' => (string)$token
            ]);

            $response = $api->token()->validateBas();
            if ((int)$response->getStatusCode() !== 200) {
                $this->throwUnauthorizedException();
            }

            $tokenData = (new ResponseTransformer())->transform($response);

            if (!array_key_exists('userId', $tokenData) || !array_key_exists('customerId', $tokenData) || !array_key_exists('grantType', $tokenData)) {
                $this->throwUnauthorizedException();
            }

            if ($this->isInternalClientToken($tokenData['userId'], $tokenData['customerId'], $tokenData['grantType'])) {
                $userToken = new UserToken();
                $userToken->setIsInternalClient(true);
                $userToken->access_token = $token;
                return $userToken;
            }

            $transaction = UserToken::getDb()->beginTransaction();
            try {
                /** @var UserToken $userToken */
                $userToken = UserToken::find()->where(['access_token' => $token])->one();
                if (is_null($userToken)) {
                    $userToken = $this->create($tokenData['userId'], $tokenData['customerId'], $token);
                }

                $userToken = $this->appendAuthUser($userToken, $tokenData);
                $userToken = $this->appendUserLanguage($userToken, $tokenData);
                $userToken->customer_id = $tokenData['customerId'] ?? $userToken->customer_id;
                $userToken->setTimezone($tokenData['timezone'] ?? null);

                $transaction->commit();
                return $userToken;
            } catch (\Throwable $e) {
                $transaction->rollBack();
                throw $e;
            }
        } catch (SellerNotFoundException $e) {
            throw $e;
        } catch (\Throwable $e) {
            if ($e->getCode() !== 401) {
                \Yii::error($e);
            }
            $this->throwUnauthorizedException();
        }
    }

    protected function throwUnauthorizedException()
    {
        throw new UnauthorizedHttpException('The resource owner or authorization server denied the request.');
    }

    /**
     * @param $userId
     * @param $customerId
     * @param $grantType
     * @return bool
     */
    protected function isInternalClientToken($userId, $customerId, $grantType): bool
    {
        return is_null($userId) && is_null($customerId) && $grantType === self::GRANT_TYPE_CLIENT_CREDENTIALS;
    }

    /**
     * @param int $userId
     * @param $customerId
     * @param string $accessToken
     * @return UserToken
     */
    public function create(int $userId, $customerId, string $accessToken): UserToken
    {
        \Yii::$app->db->createCommand()->upsert(UserToken::tableName(),
            [
                'user_id' => $userId,
                'customer_id' => $customerId,
                'access_token' => $accessToken,
                'created_at' => date('Y-m-d H:i:s')
            ], [
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [])->execute();

        $userToken = UserToken::findIdentityByAccessToken($accessToken);

        if (is_null($userToken)) {
            throw new \Exception('User token is not saved');
        }

        return $userToken;
    }

    /**
     * @param UserToken $userToken
     * @param array $data
     * @return UserToken
     */
    protected function appendAuthUser(UserToken $userToken, array $data = []): UserToken
    {
        $authUser = new AuthUser(
            $data['userId'],
            $data['side'],
            $data['type'],
            $data['role'],
            $data['permissions'],
            $data['language']
        );
        $userToken->setAuthUser($authUser);

        return $userToken;
    }

    protected function appendUserLanguage(UserToken $userToken, array $data = []): UserToken
    {
        $language = $data['language'] ?? UserToken::DEFAULT_LANGUAGE;
        $userToken->setLanguage($language);
        \Yii::$app->language = $language;

        return $userToken;
    }
}
