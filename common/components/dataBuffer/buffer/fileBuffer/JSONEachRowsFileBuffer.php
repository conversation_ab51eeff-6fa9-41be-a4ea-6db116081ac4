<?php

namespace common\components\dataBuffer\buffer\fileBuffer;

use common\components\dataBuffer\buffer\AbstractBuffer;
use common\components\dataBuffer\buffer\BrokenAwareInterface;
use common\components\dataBuffer\exception\FlushBufferedDataException;
use common\components\dataBuffer\target\FilePathAwareTargetInterface;
use common\components\dataBuffer\target\TargetInterface;
use common\components\prometheus\Prometheus;
use common\models\Seller;
use yii\helpers\FileHelper;
use yii\mutex\Mutex;

class JSONEachRowsFileBuffer extends AbstractFileBuffer
{
    /**
     * CsvFileNumber constructor.
     *
     * @param int $flushSize
     * @param int $flushIntervalSeconds
     * @param string $bufferName
     * @param string $baseDir
     * @param Mutex $mutex
     * @param TargetInterface|null $target
     * @throws \Exception
     */
    public function __construct(
        int $flushSize,
        int $flushIntervalSeconds,
        int $customerId,
        string $bufferName,
        string $baseDir,
        Mutex $mutex,
        Prometheus $prometheus,
        TargetInterface $target = null
    ) {
        parent::__construct(
            $flushSize,
            $flushIntervalSeconds,
            $customerId,
            $bufferName,
            $baseDir,
            $mutex,
            $prometheus,
            $target
        );

        static $processId = null;
        if (null === $processId) {
            $processId = random_int(0, 100000000);
        }

        $this->dataKey = date('Y-m-d') . "/customer_{$customerId}_{$bufferName}/pid_{$processId}";
        $this->remove();
    }

    /**
     * {@inheritDoc}
     */
    protected function onPutData(array $data): void
    {
        if (count($data) === 0) {
            return;
        }

        $lockName = $this->getLockName();
        $this->acquireLock($lockName, self::LOCK_WAIT_TIMEOUT);

//        $allCustomersIDsMappedBySellerIds = Seller::getAllCustomersIDsMappedBySellerIds();

        try {
            $this->initFilesStructureIfNeed();

            foreach ($data as $i => $datum) {
                foreach ($datum as $k => $value) {
                    if (is_object($datum)) {
                        $datum->{$k} = (string)$value;
                    } else {
                        $data[$i][$k] = (string)$value;
                    }
                }
/*
                if (isset($allCustomersIDsMappedBySellerIds[$datum->SellerId]) &&
                    (int)$allCustomersIDsMappedBySellerIds[$datum->SellerId] !== (int)\Yii::$app->dbManager->getCustomerId()){
                    throw new FlushBufferedDataException(
                        sprintf('Wrong seller in data! Seller %s, customer id %s, correct customer for seller %s',
                            $datum->SellerId,
                            \Yii::$app->dbManager->getCustomerId(),
                            $allCustomersIDsMappedBySellerIds[$datum->SellerId]
                        )
                    );
                }*/
            }


            $jsonData = json_encode($data);
            $jsonData = strtr($jsonData, [
                '[' => '',
                ']' => "\n",
                '},' => "}\n"
            ]);
            $bufferStats = $this->getStats();
            if ($bufferStats->count === 0) {
                $dataAsArray = json_decode(json_encode($data[0]), true);
                $bufferStats->columnNames = array_keys($dataAsArray);
                $this->saveStats($bufferStats);
            }

            $handle = fopen($this->getDataFilePath(), "a");
            fwrite($handle, $jsonData);
            fclose($handle);

            $bufferStats->count += count($data);
            $this->saveStats($bufferStats);
            $this->markAsReadyIfNeed($bufferStats->count);
        } catch (\Throwable $e) {
            $this->mutex->release($lockName);
            throw $e;
        }
        $this->mutex->release($lockName);
    }

    /**
     * {@inheritDoc}
     */
    protected function onBeforeFlush(): void
    {
        $bufferStats = $this->getStats();

        if (!empty($this->target)) {
            /** @var FilePathAwareTargetInterface $target */
            $target = $this->target;
            $target->setFilePath($this->getDataFilePath());
            $target->setColumnNames($bufferStats->columnNames);
            $target->setCount($this->getCount());
        }
    }
}
