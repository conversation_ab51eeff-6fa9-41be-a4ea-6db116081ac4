<?php

namespace common\components\dataBuffer\buffer\fileBuffer;

use common\components\dataBuffer\buffer\AbstractBuffer;
use common\components\dataBuffer\buffer\BrokenAwareInterface;
use common\components\dataBuffer\target\FilePathAwareTargetInterface;
use common\components\dataBuffer\target\TargetInterface;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use yii\helpers\FileHelper;
use yii\mutex\Mutex;

abstract class AbstractFileBuffer extends AbstractBuffer implements BrokenAwareInterface
{
    use LogToConsoleTrait;

    protected const LOCK_WAIT_TIMEOUT = 60 * 2;

    /**
     * Unique identifier of data (type of data).
     *
     * @var string
     */
    protected $dataKey;

    /**
     * Base directory where buffer will put it's data.
     *
     * @var string
     */
    protected $baseDir;

    /**
     * @var Mutex
     */
    protected $mutex;

    protected int $customerId;

    protected bool $isBroken = false;

    protected bool $isReadyToBeFlushed = false;

    protected ?string $brokenAtDateTime = null;

    protected ?Prometheus $prometheus = null;

    abstract protected function onPutData(array $data): void;

    /**
     * CsvFileNumber constructor.
     *
     * @param int $flushSize
     * @param int $flushIntervalSeconds
     * @param string $bufferName
     * @param string $baseDir
     * @param Mutex $mutex
     * @param TargetInterface|null $target
     * @throws \Exception
     */
    public function __construct(
        int $flushSize,
        int $flushIntervalSeconds,
        int $customerId,
        string $bufferName,
        string $baseDir,
        Mutex $mutex,
        Prometheus $prometheus,
        TargetInterface $target = null
    ) {
        if (null !== $target && !$target instanceof FilePathAwareTargetInterface) {
            throw new \Exception(
                "File buffer only accepts targets which implement FilePathAwareTargetInterface interface"
            );
        }

        $this->dataKey = date('Y-m-d') . "/customer_{$customerId}_{$bufferName}";
        $this->baseDir = $baseDir;
        $this->mutex = $mutex;
        $this->customerId = $customerId;
        $this->prometheus = $prometheus;

        if (empty($this->prometheus)) {
            $this->prometheus = \Yii::$app->prometheus;
        }

        parent::__construct($flushSize, $flushIntervalSeconds, $target);
    }

    public function setCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;
        return $this;
    }

    public function __wakeup()
    {
        $this->mutex = \Yii::$app->mutex;
        $this->prometheus = \Yii::$app->prometheus;
    }

    public function markAsBroken(string $reason): void
    {
        if ($this->isBroken()) {
            return;
        }

        $lockName = $this->getLockName();
        $this->acquireLock($lockName, self::LOCK_WAIT_TIMEOUT);

        try {
            $oldBufferDir = $this->getBufferDir();
            $this->isBroken = true;
            $this->brokenAtDateTime = date('Y-m-d_H-i-s');
            $newBufferDir = $this->getBufferDir();
            FileHelper::copyDirectory($oldBufferDir, $newBufferDir);

            $stats = $this->getStats();
            $stats->exception = $reason;
            $stats->isBroken = $this->isBroken;
            $stats->brokenAtDateTime = $this->brokenAtDateTime;
            $this->saveStats($stats);

            $this->removeDirectory($oldBufferDir);
        } catch (\Throwable $e) {
            $this->mutex->release($lockName);
            throw $e;
        }

        $this->mutex->release($lockName);
    }

    public function remove(): void
    {
        $lockName = $this->getLockName();
        $this->acquireLock($lockName, self::LOCK_WAIT_TIMEOUT);
        try {
            $this->removeDirectory($this->getBufferDir());
        } catch (\Throwable $e) {
            $this->mutex->release($lockName);
            throw $e;
        }
        $this->mutex->release($lockName);
    }

    public function isBroken(): bool
    {
        return $this->isBroken;
    }

    public function setIsBroken(bool $isBroken): void
    {
        $this->isBroken = $isBroken;
    }

    public function setBrokenAt(string $dateTime): void
    {
        $this->brokenAtDateTime = $dateTime;
    }

    public function flush(): void
    {
        try {
            $this->acquireLock($this->getLockName(), self::LOCK_WAIT_TIMEOUT);
            parent::flush();
        } catch (\Throwable $e) {
            $this->mutex->release($this->getLockName());
            throw $e;
        }
        $this->mutex->release($this->getLockName());
    }

    protected function markAsReadyIfNeed(int $currentCount): void
    {
        if ($this->isFlushEnabled || $currentCount < $this->flushSize) {
            return;
        }

        // Moves buffer to separate directory, because it's already ready to be flushed.
        // This  should prevent buffer file overflow.
        $oldBufferDir = $this->getBufferDir();
        $this->isReadyToBeFlushed = true;
        $this->brokenAtDateTime = date('Y-m-d_H:i:s');
        $newBufferDir = $this->getBufferDir();
        FileHelper::copyDirectory($oldBufferDir, $newBufferDir);
        $stats = $this->getStats();
        $this->saveStats($stats);

        // Moves current buffer back (because someone is already using it) and reset stats
        $this->isReadyToBeFlushed = false;
        $this->brokenAtDateTime = null;

        $this->removeDirectory($this->getBufferDir());
    }

    protected function acquireLock(string $lockName, int $timeout)
    {
        $timeStart = microtime(true);
        $this->mutex->acquire($lockName, $timeout);
        $timeEnd = microtime(true) - $timeStart;
        $this->prometheus->gauge('JSONEachRowsFileBuffer', 'file_buffer_lock_wait_timeout', $timeEnd);

        if ($timeEnd > self::LOCK_WAIT_TIMEOUT - 5) {
            throw new \Exception("Unable to acquire lock $lockName due to timeout after $timeEnd seconds");
        }
    }

    /**
     * {@inheritDoc}
     */
    protected function onBeforeFlush(): void
    {
        $bufferStats = $this->getStats();

        if (!empty($this->target)) {
            /** @var FilePathAwareTargetInterface $target */
            $target = $this->target;
            $target->setFilePath($this->getDataFilePath());
            $target->setColumnNames($bufferStats->columnNames);
        }
    }

    /**
     * {@inheritDoc}
     */
    protected function onAfterFlush(): void
    {
        $this->removeDirectory($this->getBufferDir());
    }

    protected function removeDirectory(string $directoryPath): void
    {
        exec("rm -rf {$directoryPath}");
    }

    protected function resetStats(BufferStats $bufferStats)
    {
        $bufferStats->count = 0;
        $bufferStats->flushedAt = date('Y-m-d H:i:s');
        $this->saveStats($bufferStats);
        file_put_contents($this->getDataFilePath(), '');
    }

    /**
     * {@inheritDoc}
     */
    protected function getBufferedData(): array
    {
        return [];
    }

    /**
     * {@inheritDoc}
     */
    public function getCount(): int
    {
        $bufferStats = $this->getStats();
        $count = $bufferStats->count ?? 0;

        if ($this->isBroken() && $count <= 0) {
            $this->removeDirectory($this->getBufferDir());
            return 0;
        }

        return $count;
    }

    /**
     * {@inheritDoc}
     */
    protected function getFlushedAt(): ?\DateTime
    {
        $bufferStats = $this->getStats();

        if (empty($bufferStats->flushedAt)) {
            return null;
        }

        return new \DateTime($bufferStats->flushedAt);
    }

    /**
     * Returns certain buffer stats (collection of technical information about buffer condition).
     *
     * @return BufferStats
     */
    protected function getStats(): BufferStats
    {
        $this->initFilesStructureIfNeed();

        $statsJson = file_get_contents($this->getStatsFilePath());
        $rawStats = json_decode($statsJson);

        $bufferStats = new BufferStats();
        $bufferStats->count = $rawStats->count ?? 0;
        $bufferStats->flushedAt = $rawStats->flushedAt ?? date('Y-m-d H:i:s');
        $bufferStats->columnNames = $rawStats->columnNames ?? [];
        $bufferStats->isBroken = $rawStats->isBroken ?? false;
        $bufferStats->brokenAtDateTime = $rawStats->brokenAtDateTime ?? null;
        $bufferStats->exception = $rawStats->exception ?? null;

        return $bufferStats;
    }

    /**
     * @param BufferStats $bufferStats
     */
    protected function saveStats(BufferStats $bufferStats): void
    {
        file_put_contents($this->getStatsFilePath(), json_encode($bufferStats, JSON_PRETTY_PRINT));
        file_put_contents($this->getInstanceFilePath(), serialize($this));
    }

    /**
     * Creates initial file structure for new buffer (if it was not created before).
     *
     * @throws \yii\base\Exception
     */
    protected function initFilesStructureIfNeed(): void
    {
        if (!file_exists($this->getBufferDir())) {
            mkdir($this->getBufferDir(), 0775, true);
        }

        if (!file_exists($this->getStatsFilePath())) {
            $bufferStats = new BufferStats();
            $bufferStats->count = 0;
            $bufferStats->flushedAt = date('Y-m-d H:i:s');
            $this->saveStats($bufferStats);
        }
    }

    /**
     * Returns path to buffer's data of certain table.
     *
     * @return string
     */
    protected function getBufferDir(): string
    {
        $bufferDir = $this->baseDir;
        if ($this->isBroken()) {
            $bufferDir .= '_broken';
        } else if ($this->isReadyToBeFlushed) {
            $bufferDir .= '_ready';
        }

        $bufferDir .= '/' . $this->getDataKey();
        return $bufferDir;
    }

    /**
     * Returns path for file with collected buffer's data.
     *
     * @return string
     */
    protected function getDataFilePath(): string
    {
        return $this->getBufferDir() . '/data';
    }

    /**
     * Returns path to file with buffer stats (technical info).
     *
     * @return string
     */
    protected function getStatsFilePath(): string
    {
        return $this->getBufferDir() . '/stats.json';
    }

    /**
     * Returns path to file with buffer stats (technical info).
     *
     * @return string
     */
    protected function getInstanceFilePath(): string
    {
        return $this->getBufferDir() . '/instance';
    }

    /**
     * Returns unique lock name (identifier).
     *
     * @return string
     */
    protected function getLockName()
    {
        return 'csv_buffer_' . $this->getDataKey();
    }

    protected function getDataKey(): string
    {
        $dataKey = $this->dataKey;

        if (!empty($this->brokenAtDateTime)) {
            $dataKey .= '_' . $this->brokenAtDateTime;
        }
        return $dataKey;
    }
}