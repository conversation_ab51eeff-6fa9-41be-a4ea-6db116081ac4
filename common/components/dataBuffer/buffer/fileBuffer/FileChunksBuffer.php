<?php

namespace common\components\dataBuffer\buffer\fileBuffer;

use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\target\FilePathAwareTargetInterface;
use common\components\LogToConsoleTrait;

class FileChunksBuffer extends AbstractFileBuffer implements BufferInterface
{
    use LogToConsoleTrait;
    /**
     * {@inheritDoc}
     */
    protected function onPutData(array $data): void
    {
        if (count($data) === 0) {
            return;
        }

        try {
            $this->initFilesStructureIfNeed();
            $bufferStats = $this->getStats();

            $filePath = $data[0];
            $columnNames = $data[1];
            $count = $data[2];

            if ($bufferStats->count === 0) {
                $bufferStats->columnNames = $columnNames;
                $this->saveStats($bufferStats);
            }

            $this->info('Copy tmp chunk to persistent directory started');
            copy($filePath, $this->getChunksDir() . '/data-' . microtime(true).'-'.random_int(1,100000));
            $this->info('Copy tmp chunk to persistent directory finished');

            $bufferStats->count += $count;
            $this->saveStats($bufferStats);
            $this->markAsReadyIfNeed($bufferStats->count);
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * Creates initial file structure for new buffer (if it was not created before).
     *
     * @throws \yii\base\Exception
     */
    protected function initFilesStructureIfNeed(): void
    {
        parent::initFilesStructureIfNeed();

        if (!file_exists($this->getChunksDir())) {
            mkdir($this->getChunksDir(), 0775, true);
        }
    }

    public function markAsBroken(string $reason): void
    {
        parent::markAsBroken($reason);

        unlink($this->getDataFilePath());
    }

    /**
     * Returns path to buffer's data of certain table.
     *
     * @return string
     */
    protected function getChunksDir(): string
    {
        return $this->getBufferDir() . '/chunks';
    }

    /**
     * {@inheritDoc}
     */
    protected function onBeforeFlush(): void
    {
        $bufferStats = $this->getStats();
        if (file_exists($this->getDataFilePath())) {
            unlink($this->getDataFilePath());
        }
        $this->info('Started merging file parts');
        exec(sprintf(
            'cat %s > %s',
            $this->getChunksDir() . '/*',
            $this->getDataFilePath()
        ));
        $this->info('Finished merging file parts');

        if (!empty($this->target)) {
            /** @var FilePathAwareTargetInterface $target */
            $target = $this->target;
            $target->setFilePath($this->getDataFilePath());
            $target->setColumnNames($bufferStats->columnNames);
        }
    }
}