<?php

namespace common\components\dataBuffer\buffer;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\customer\TransactionBuffer;
use yii\db\Transaction;

class PostgresBuffer implements BufferInterface
{
    use LogToConsoleTrait;

    protected string $bufferTableName;
    protected string $targetTableName;
    protected DbManager $dbManager;
    protected ?Transaction $dbTransaction;

    public function __construct(
        string $bufferTableName,
        string $targetTableName
    ) {
        $this->bufferTableName = $bufferTableName;
        $this->targetTableName = $targetTableName;
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function getCount(): int
    {
        return TransactionBuffer::find()->count();
    }

    public function put(array $data): void
    {
        if (empty($data)) {
            return;
        }

        if (empty($this->dbTransaction)) {
            $this->dbTransaction = TransactionBuffer::getDb()->beginTransaction();
            $this->info('Postgres buffer: transaction opened');
        }

        $data = json_decode(json_encode($data), true);
        TransactionBuffer::getDb()
            ->createCommand()
            ->batchInsert(
                TransactionBuffer::tableName(),
                array_keys($data[0]),
                $data
            )->execute();
    }

    public function flush(): void
    {
        if (!empty($this->dbTransaction)) {
            $this->dbTransaction->commit();
            $this->dbTransaction = null;
            $this->info('Postgres buffer: transaction committed');
        }
    }

    public function remove(): void
    {
        if (empty($this->dbTransaction)) {
            return;
        }

        try {
            $this->dbTransaction->rollBack();
            $this->info('Postgres buffer: transaction rollback');
        } catch (\Throwable $e) {
            if (false === strpos($e->getMessage(), 'transaction timeout')) {
                $this->dbTransaction = null;
                throw $e;
            }
        }
        $this->dbTransaction = null;
    }
}