<?php

namespace common\components\dataBuffer\buffer;

use common\components\dataBuffer\target\TargetInterface;

/**
 * Core logic for all kind of buffers.
 *
 * @package common\components\dataBuffer\buffer
 */
abstract class AbstractBuffer implements BufferInterface
{
    /**
     * @var int
     */
    public $flushSize;

    /**
     * @var int
     */
    public $flushIntervalSeconds;

    /**
     * @var TargetInterface
     */
    protected $target;

    protected $isFlushEnabled = true;

    abstract public function getCount(): int;
    abstract public function remove(): void;
    abstract protected function getBufferedData(): array;
    abstract protected function getFlushedAt(): ?\DateTime;
    abstract protected function onPutData(array $data): void;

    /**
     * AbstractBuffer constructor.
     * @param int $flushSize
     * @param int $flushIntervalSeconds
     * @param TargetInterface|null $target
     */
    public function __construct(
        int $flushSize,
        int $flushIntervalSeconds,
        TargetInterface $target = null
    ) {
        $this->target = $target;
        $this->flushSize = $flushSize;
        $this->flushIntervalSeconds = $flushIntervalSeconds;
    }

    /**
     * Puts data into buffer.
     *
     * @param array $data
     */
    public function put(array $data): void
    {
        $this->onPutData($data);
        $this->flushIfNeed();
    }

    public function flushIfNeed(): bool
    {
        if ($this->shouldBeFlushed()) {
            $this->flush();
            return true;
        }

        return false;
    }

    /**
     * Flushes (saves) buffered data into target.
     */
    public function flush(): void
    {
        $this->onBeforeFlush();
        $bufferedData = $this->getBufferedData();

        if (!empty($this->target)) {
            $this->target->saveBufferedData($bufferedData);
        }

        $this->onAfterFlush();
    }

    protected function onBeforeFlush(): void
    {
    }

    protected function onAfterFlush(): void
    {
    }

    public function getTarget(): TargetInterface
    {
        return $this->target;
    }

    public function setTarget(TargetInterface $target): void
    {
        $this->target = $target;
    }

    public function setIsFlushEnabled(bool $isFlushEnabled): self
    {
        $this->isFlushEnabled = $isFlushEnabled;
        return $this;
    }

    /**
     * Checks whether buffer should be flushed at the moment.
     *
     * @return bool
     */
    protected function shouldBeFlushed(): bool
    {
        if (!$this->isFlushEnabled) {
            return false;
        }

        $count = $this->getCount();
        $flushedAt = $this->getFlushedAt();

//        if ($count === 0) {
//            return false;
//        }

        if (null !== $flushedAt && time() - $flushedAt->getTimestamp() >= $this->flushIntervalSeconds) {
            return true;
        }

        if ($count >= $this->flushSize) {
            return true;
        }

        return false;
    }
}