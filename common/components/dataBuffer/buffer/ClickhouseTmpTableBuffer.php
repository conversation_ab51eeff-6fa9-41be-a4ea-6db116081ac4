<?php

namespace common\components\dataBuffer\buffer;

use bashkarev\clickhouse\Connection;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\components\nodes\NodesManager;

class ClickhouseTmpTableBuffer implements BufferInterface, CleanAwareInterface
{
    use LogToConsoleTrait;

    protected string $tmpTableName;
    protected string $targetTableName;
    protected DbManager $dbManager;
    protected Connection $clickhouseCustomerDb;
    protected ClickhouseDbHelper $clickhouseDbHelper;
    protected NodesManager $nodesManager;
    protected string $processIdColumnName;
    protected int $processId;
    protected bool $hasDataToFlush = false;

    public function __construct(
        string $tmpTableName,
        string $targetTableName,
        string $processIdColumnName = 'ProcessId'
    ) {
        $this->tmpTableName = $tmpTableName;
        $this->targetTableName = $targetTableName;
        $this->dbManager = \Yii::$app->dbManager;
        $this->clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();
        $this->processIdColumnName = $processIdColumnName;
        $this->clickhouseDbHelper = new ClickhouseDbHelper();
        $this->nodesManager = new NodesManager();
        $this->updateProcessId();
    }

    public function getCount(): int
    {
        return 0;
    }

    public function isMemoryFull(): bool
    {
        $rowCount = $this->clickhouseCustomerDb
            ->createCommand("SELECT count(*) FROM {$this->tmpTableName} WHERE CreatedAt < now() - INTERVAL 4 HOUR")
            ->queryScalar();

        return $rowCount > 5000000;
    }

    public function put(array $data): void
    {
        if (empty($data)) {
            return;
        }

        $this->hasDataToFlush = true;

        $data = json_decode(json_encode($data), true);
        foreach ($data as $k => $datum) {
            $data[$k][$this->processIdColumnName] = $this->processId;
        }

        $query = $this
            ->clickhouseCustomerDb
            ->createCommand()
            ->batchInsert(
                $this->tmpTableName,
                array_keys($data[0]),
                $data
            );
        $sql = $query->getRawSql();
        $sql = str_replace(
            'VALUES',
            'SETTINGS max_partitions_per_insert_block = 10000 VALUES',
            $sql
        );
        $this->clickhouseCustomerDb->execute($sql);
    }

    public function flush(): void
    {
        if (!$this->hasDataToFlush) {
            $this->updateProcessId();
            return;
        }

        Command::$isNodeChangingEnabled = false;
        try {
            $sql = $this
                ->clickhouseCustomerDb
                ->createCommand("
                INSERT INTO {$this->targetTableName} 
                SETTINGS max_partitions_per_insert_block = 10000
                SELECT * EXCEPT ({$this->processIdColumnName})
                FROM {$this->tmpTableName} 
                WHERE {$this->processIdColumnName} = :processId",
                    [
                        ':processId' => $this->processId
                    ]
                );
            $sql->execute();

            // Try to debug duplicated transactions issue.
            $functionCallUniqId = random_int(1, 10000000);

            // Marker that current process has been finished.
            $this
                ->clickhouseCustomerDb
                ->createCommand("
                INSERT INTO {$this->tmpTableName} ({$this->processIdColumnName}, EventPeriodId) VALUES (:processId, :eventPeriodId)",
                    [
                        ':processId' => $this->processId * -1,
                        ':eventPeriodId' => $functionCallUniqId
                    ]
                )
                ->execute();
        } catch (\Throwable $e) {
            Command::$isNodeChangingEnabled = true;
            $this->updateProcessId();
            throw $e;
        }

        Command::$isNodeChangingEnabled = true;
        $this->updateProcessId();
    }

    protected function updateProcessId()
    {
        $this->processId = (int)(microtime(true) * 10000) + random_int(0, 100000000);
        $this->hasDataToFlush = false;
    }

    public function remove(): void
    {
        $this->updateProcessId();
    }

    public function clean(): void
    {
        $processToClean = $this
            ->clickhouseCustomerDb
            ->createCommand("
                SELECT $this->processIdColumnName
                FROM {$this->tmpTableName}
                WHERE (
                    {$this->processIdColumnName} < 0
                    AND 
                    CreatedAt < now() - INTERVAL 1 HOUR
                )
                OR CreatedAt < now() - INTERVAL 3 HOUR
                LIMIT 1
            ")
            ->queryScalar();

        if (empty($processToClean)) {
            return;
        }

        $customerMasterNodes = $this->dbManager->getClickhouseCustomerMasterNodes();

        foreach ($customerMasterNodes as $customerMasterNodeIp) {
            try {
                $this->info("Cleaning tmp table on $customerMasterNodeIp");

                $clickhouseCustomerDb = $this->dbManager->getDb('customer', 'clickhouse', $customerMasterNodeIp);
                $clickhouseCustomerDb
                    ->createCommand("
                        ALTER TABLE {$this->tmpTableName}
                        DELETE
                        WHERE (
                            {$this->processIdColumnName} IN (
                                SELECT DISTINCT {$this->processIdColumnName} * -1
                                FROM {$this->tmpTableName}
                                WHERE {$this->processIdColumnName} < 0
                                UNION ALL
                                SELECT DISTINCT {$this->processIdColumnName}
                                FROM {$this->tmpTableName}
                                WHERE {$this->processIdColumnName} < 0
                            )
                            AND CreatedAt < now() - INTERVAL 1 HOUR
                        )
                        OR CreatedAt < now() - INTERVAL 3 HOUR",
                    )
                    ->execute();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }
}