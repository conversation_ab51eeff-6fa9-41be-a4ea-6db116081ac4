<?php

namespace common\components\dataBuffer\buffer;

use common\components\dataBuffer\target\TargetInterface;

/**
 * Interface BufferInterface
 *
 * @package common\components\dataBuffer\buffer
 */
interface BufferInterface
{
    /**
     * Puts data into buffer.
     *
     * @param array $data
     */
    public function put(array $data): void;

    public function getCount(): int;

    /**
     * Flushes data from buffer to end target (force, ignoring conditions).
     */
    public function flush(): void;

    public function remove(): void;
}