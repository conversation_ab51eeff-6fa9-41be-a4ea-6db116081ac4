<?php

namespace common\components\dataBuffer\target;

use ClickHouseDB\Client;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\dataBuffer\exception\FlushBufferedDataException;
use common\components\LogToConsoleTrait;
use common\models\finance\clickhouse\Transaction;
use common\models\Seller;

/**
 * Saves buffered data from file directly into clickhouse table.
 *
 * @package common\components\Buffer\target
 */
class ClickhouseFromFileTarget implements TargetInterface, FilePathAwareTargetInterface
{
    use LogToConsoleTrait;

    /**
     * Clickhouse table name.
     *
     * @var string
     */
    private $tableName;

    /**
     * Column names of data from file.
     *
     * @var array
     */
    private $columnNames = [];

    /**
     * Path to file where data was collected.
     *
     * @var string
     */
    private $filePath;

    private ?int $customerId = null;

    public function setCustomerId(int $customerId): void
    {
        $this->customerId = $customerId;
    }

    /**
     * ToClickhouseFromFileTarget constructor.
     *
     * @param string $tableName
     */
    public function __construct(string $tableName, int $customerId)
    {
        $this->tableName = $tableName;
        $this->customerId = $customerId;
    }

    public function setCount(int $count): void
    {
    }

    /**
     * {@inheritDoc}
     */
    public function setColumnNames(array $filePath): TargetInterface
    {
        $this->columnNames = $filePath;
        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function setFilePath(string $filePath): TargetInterface
    {
        $this->filePath = $filePath;
        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function saveBufferedData(array $bufferedData): void
    {
        if ($this->tableName === 'transaction'){
            throw new FlushBufferedDataException('Table name is incorrect  ' . sprintf("customer %s, filename %s", $this->customerId, $this->filePath));
        }
        if (strpos($this->filePath, (string)$this->customerId) === false){
            throw new FlushBufferedDataException(sprintf("Wrong customer! Customer %s, filename %s", $this->customerId, $this->filePath));
        }
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($this->customerId);

        $connection = $dbManager->getClickhouseCustomerDb();
        $connection->open();
        try {
            /** @var Client $client */
            $client = $connection->getClient();
            $this->info(sprintf("customer %s, filename %s", $this->customerId, $this->filePath));
            $client->insertBatchFiles(
                $this->tableName,
                [
                    $this->filePath
                ],
                $this->columnNames,
                'JSONEachRow'
            );
        } catch (\Throwable $e) {
            throw new FlushBufferedDataException($e->getMessage() . ' ' . json_encode([
                'filePath' => $this->filePath
            ]));
        }
        $connection->close();
    }
}