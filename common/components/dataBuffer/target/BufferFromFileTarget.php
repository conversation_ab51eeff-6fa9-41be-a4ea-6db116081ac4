<?php

namespace common\components\dataBuffer\target;

use common\components\dataBuffer\buffer\BufferInterface;
use common\components\LogToConsoleTrait;

class BufferFromFileTarget implements TargetInterface, FilePathAwareTargetInterface
{
    use LogToConsoleTrait;

    /**
     * Column names of data from file.
     *
     * @var array
     */
    private array $columnNames = [];

    /**
     * Path to file where data was collected.
     *
     * @var string
     */
    private string $filePath;

    private ?int $customerId = null;

    private int $count = 0;

    private BufferInterface $buffer;

    public function setCustomerId(int $customerId): void
    {
        $this->customerId = $customerId;
    }

    public function setCount(int $count): void
    {
        $this->count = $count;
    }

    /**
     * ToClickhouseFromFileTarget constructor.
     *
     * @param string $tableName
     */
    public function __construct(BufferInterface $buffer)
    {
        $this->buffer = $buffer;
    }

    /**
     * {@inheritDoc}
     */
    public function setColumnNames(array $columnNames): TargetInterface
    {
        $this->columnNames = $columnNames;
        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function setFilePath(string $filePath): TargetInterface
    {
        $this->filePath = $filePath;
        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function saveBufferedData(array $bufferedData): void
    {
        $this->buffer->put([$this->filePath, $this->columnNames, $this->count]);
    }
}
