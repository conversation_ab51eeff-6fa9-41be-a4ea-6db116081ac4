<?php

namespace common\components\dataBuffer\target;

use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;

/**
 * Saves buffered data into rabbit mq queue which is linked to clickhouse mq table (clickhouse RabbitMq engine).
 *
 * @package common\components\Buffer\target
 */
class ClickhouseRabbitmqTarget implements TargetInterface
{
    /**
     * Clickhouse table name.
     *
     * @var string
     */
    private $tableName;

    /**
     * ClickhouseRabbitmqTarget constructor.
     * @param string $tableName
     */
    public function __construct(string $tableName)
    {
        $this->tableName = $tableName;
    }

    /**
     * {@inheritDoc}
     */
    public function saveBufferedData(array $bufferedData): void
    {
        if (count($bufferedData) === 0) {
            return;
        }

        /* We should stringify all numeric values
         * because clickhouse Rabbitmq engine accepts them only in quoted format
         * ({"FieldName":123} -> wrong, {"FieldName":"123"} -> correct)
         *
         * json_encode function doesn't quote numeric values by default.
         */
        foreach ($bufferedData as $transaction) {
            foreach ($transaction as $k => $value) {
                if (is_numeric($value)) {
                    $transaction->{$k} = (string)$value;
                }
            }
        }

        $bufferedData = json_decode(json_encode($bufferedData), true);
        $jsonEncodedData = json_encode($bufferedData);
        $jsonEncodedData = trim($jsonEncodedData, "[]");

        $producer = \Yii::$app->rabbitmq->getProducer(MessageAbstract::PRODUCER_NAME);
        $producer->publish($jsonEncodedData, MessageInterface::EXCHANGE_NAME_CLICKHOUSE, $this->tableName);
    }
}