<?php


namespace common\components\dataBuffer\target;

/**
 * Used for all targets which are working with data saved into files.
 *
 * @package common\components\Buffer\target
 */
interface FilePathAwareTargetInterface
{
    /**
     * Sets file path where data is collected in
     *
     * @param string $filePath
     * @return TargetInterface
     */
    public function setFilePath(string $filePath): TargetInterface;

    /**
     * Sets column names of saved data.
     *
     * @param array $columnNames
     * @return TargetInterface
     */
    public function setColumnNames(array $columnNames): TargetInterface;

    /**
     * @param int $count
     * @return void
     */
    public function setCount(int $count): void;
}