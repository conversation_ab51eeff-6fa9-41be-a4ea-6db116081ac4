<?php

namespace common\components\dataBuffer\target;

use common\components\LogToConsoleTrait;

class ClickhouseFromProxyTable implements TargetInterface
{
    use LogToConsoleTrait;

    private int $customerId;
    private string $proxyTableName;
    private string $targetTableName;

    public function setCustomerId(int $customerId): void
    {
        $this->customerId = $customerId;
    }

    /**
     * ToClickhouseFromFileTarget constructor.
     *
     * @param string $tableName
     */
    public function __construct(string $proxyTableName, string $targetTableName)
    {
        $this->proxyTableName = $proxyTableName;
        $this->targetTableName = $targetTableName;
    }

    /**
     * {@inheritDoc}
     */
    public function saveBufferedData(array $bufferedData): void
    {
    }
}