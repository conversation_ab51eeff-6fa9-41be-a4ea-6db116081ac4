<?php

namespace common\components\dataBuffer;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\buffer\ClickhouseTmpTableBuffer;
use common\components\dataBuffer\buffer\fileBuffer\FileChunksBuffer;
use common\components\dataBuffer\buffer\fileBuffer\JSONEachRowsFileBuffer;
use common\components\dataBuffer\buffer\PostgresBuffer;
use common\components\dataBuffer\exception\FlushBufferedDataException;
use common\components\dataBuffer\target\BufferFromFileTarget;
use common\components\dataBuffer\target\ClickhouseFromFileTarget;
use common\components\dbStructure\TableNameGenerator;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBufferTmp;
use common\models\customer\TransactionBuffer;
use common\models\DbStructure;

class BufferFactory
{
    private TableNameGenerator $tableNameGenerator;
    protected CustomerConfig $customerConfig;

    public function __construct()
    {
        /** @var CustomerConfig $customerConfig */
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->tableNameGenerator = \Yii::$container->get('tableNameGenerator');
    }

    /**
     * Builds and returns buffer which is using for saving financial events.
     *
     * @return BufferInterface
     * @throws \Exception
     */
    public function getTransactionsToClickhouseBuffer(): BufferInterface
    {
        return new ClickhouseTmpTableBuffer(
            TransactionBufferTmp::tableName(),
            \common\models\customer\clickhouse\TransactionBuffer::tableName(),
        );
    }

    public function getClickhouseBuffer(string $tableName): BufferInterface
    {
        return new PostgresBuffer(
            TransactionBuffer::tableName(),
            $tableName
        );

        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;

        if (!$dbManger->getCustomerId()){
            throw new FlushBufferedDataException('Customer id is Empty  ' . $tableName);
        }

        $fileChunksBuffer = new FileChunksBuffer(
            1000000,
            $flushTime,
            $dbManger->getCustomerId(),
            "clickhouse_{$tableName}",
            \Yii::getAlias('@runtime') . '/csv_buffers',
            \Yii::$app->mutex,
            \Yii::$app->prometheus,
            new ClickhouseFromFileTarget($tableName, $dbManger->getCustomerId())
        );
        $fileChunksBuffer->setIsFlushEnabled(false);

        $buffer = new JSONEachRowsFileBuffer(
            1000000,
            120,
            $dbManger->getCustomerId(),
            "clickhouse_{$tableName}",
            \Yii::getAlias('@runtime') . '/csv_buffers_tmp',
            \Yii::$app->mutex,
            \Yii::$app->prometheus,
            new BufferFromFileTarget($fileChunksBuffer)
        );
        $buffer->setIsFlushEnabled(false);

        return $buffer;
    }
}