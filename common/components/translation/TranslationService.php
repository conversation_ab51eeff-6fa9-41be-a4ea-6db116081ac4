<?php

namespace common\components\translation;

use common\models\Message;
use common\models\MessageTranslation;

/**
 * Class TranslationService.
 * @package console\service
 */
final class TranslationService
{
    /**
     * Synchronize messages (entity `message`) and message translations (entity `message_translation`).
     * - Entity `message` - insert new record if value does not exist.
     * - Entity `message_translation` - insert new record if value does not exist, update existing entry.
     */
    public function sync(?array $translations = null): array
    {
        $result = [
            'message_updated' => 0,
            'message_inserted' => 0,
            'message_translation_updated' => 0,
            'message_translation_inserted' => 0,
        ];

        if (!$translations || !\is_array($translations)) {
            return $result;
        }

        $messages = $this->normalizeTranslationMessages($translations);

        foreach ($messages as $item) {
            /** @var Message $message */
            $message = Message::find()
                ->where(['category' => $item['message']['category']])
                ->andWhere(['message' => $item['message']['message']])
                ->one();

            if (!$message) {
                $message = new Message();

                $message->category = $item['message']['category'];
                $message->message = $item['message']['message'];
                $message->is_processed = true;

                $message->save(false);

                $result['message_inserted']++;
                $result['message_translation_inserted'] += count($item['translation']);
            } elseif ($message->message !== $item['message']['message']) {
                $message->message = $item['message']['message'];
                $message->is_processed = true;
                $message->save(false);
            }

            $result['message_updated']++;

            foreach ($message->messageTranslations as $messageTranslation) {
                $language = $messageTranslation->language;
                if (!isset($item['translation'][$language])) {
                    continue;
                }

                $messageTranslation->setAttributes([
                    'translation' => $item['translation'][$language]['translation'],
                    'translated' => $item['translation'][$language]['translated'],
                    'is_auto_translated' => $item['translation'][$language]['is_auto_translated'],
                    'comments' => $item['translation'][$language]['comments'],
                    'status' => $item['translation'][$language]['status'],
                ], false);

                //remove updated item
                unset($item['translation'][$language]);

                $messageTranslation->save(false);

                $result['message_translation_updated']++;
            }

            //if any languages are not existed
            if (count($item['translation'])) {
                foreach ($item['translation'] as $language=>$translatedItem) {
                    $messageTranslation = new MessageTranslation();

                    $messageTranslation->setAttributes([
                        'id' => $message->id,
                        'language' => $language,
                        'translation' => $item['translation'][$language]['translation'],
                        'translated' => $item['translation'][$language]['translated'],
                        'is_auto_translated' => $item['translation'][$language]['is_auto_translated'],
                        'comments' => $item['translation'][$language]['comments'],
                        'status' => $item['translation'][$language]['status'],
                    ], false);

                    $messageTranslation->save(false);

                    $result['message_translation_updated']++;
                }
            }
        }

        return $result;
    }

    /**
     * Convert contents to the structured array.
     */
    private function normalizeTranslationMessages(array $translations): array
    {
        $messages = [];

        foreach ($translations as $translation) {
            $id = $translation['message']['id'];
            $messages[$id]['message'] = $translation['message'];
            unset($translation['message']);
            $messages[$id]['translation'][$translation['language']] = $translation;
        }

        return $messages;
    }
}
