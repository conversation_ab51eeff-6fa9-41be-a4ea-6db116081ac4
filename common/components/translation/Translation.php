<?php

namespace common\components\translation;

use common\models\Message;
use yii\base\Component;
use yii\console\Application as ConsoleApplication;
use yii\i18n\MissingTranslationEvent;

/**
 * Class Translation.
 * @package common\components
 */
final class Translation extends Component
{

    /**
     * Allows handle missing translations in console applications.
     *
     * @var bool
     */
    protected static bool $isAllowHandleMissingInConsole = false;

    /**
     * Translation constructor.
     * @param array $config
     */
    public function __construct($config = [])
    {
        parent::__construct($config);
    }

    /**
     * Add missing translations to the source table and if we are using a different translation then the original one.
     * Then add the same message to the translation table.
     *
     * @param MissingTranslationEvent $event
     */
    public static function handleMissingTranslation(MissingTranslationEvent $event): void
    {
        if (\Yii::$app instanceof ConsoleApplication && false === self::$isAllowHandleMissingInConsole) {
            return;
        }

        /** @var Message|null $source */
        $source = Message::find()
            ->where(['message' => $event->message, 'category' => $event->category])
            ->one();

        if (!$source) {
            $model = new Message();
            $model->message = $event->message;
            $model->category = $event->category;
            $model->is_processed = false;

            $model->save();
        }
    }

    /**
     * Sets flag which is allows/disallows handling missing translations in console application.
     *
     * @param bool $isAllowHandleMissingInConsole
     */
    public static function setIsAllowHandleMissingInConsole(bool $isAllowHandleMissingInConsole): void
    {
        self::$isAllowHandleMissingInConsole = $isAllowHandleMissingInConsole;
    }
}
