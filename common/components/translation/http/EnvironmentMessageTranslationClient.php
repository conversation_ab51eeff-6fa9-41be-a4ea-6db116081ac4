<?php

namespace common\components\translation\http;

use SellerLogic\InternalApi\BasApiClient;
use SellerLogic\InternalApi\MainApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;

class EnvironmentMessageTranslationClient implements MessageTranslationClientInterface
{
    private BasApiClient $basApiClient;

    public function __construct()
    {
        /** @var BasApiClient $client */
        $this->basApiClient = \Yii::$container->get('basApiClient');
    }

    public function getMessageTranslations(?string $language, ?string $updatedAt): ?array
    {
        $params = ['all' => 1, 'sync' => 1];
        if (null !== $language) {
            $params['language'] = $language;
        }

        $response = $this->basApiClient->messageTranslation()->index($params);
        if (200 !== $response->getStatusCode()) {
            throw new \Exception('Unable to execute request, invalid response.');
        }

        return (new ResponseTransformer())->transform($response);
    }
}
