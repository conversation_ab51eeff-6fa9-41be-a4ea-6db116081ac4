<?php

namespace common\components\translation\http;

use GuzzleHttp\Exception\ClientException;
use SellerLogic\InternalApi\BasApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;

class EnvironmentMessageClient implements MessageClientInterface
{
    public function postMessage(array $message): ?array
    {
        /** @var BasApiClient $client */
        $client = \Yii::$container->get('basApiClient');

        try {
            $response = $client->message()->create($message);

            if (!in_array($response->getStatusCode(), [200, 201], true)) {
                throw new \Exception('Unable to execute request, invalid response.');
            }

            return (new ResponseTransformer())->transform($response);
        } catch (ClientException $exception) {
            if (!$exception->hasResponse() || $exception->getResponse()->getStatusCode() !== 422) {
                throw $exception;
            }
        }

        return null;
    }
}
