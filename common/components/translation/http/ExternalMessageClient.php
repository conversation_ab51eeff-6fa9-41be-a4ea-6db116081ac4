<?php

namespace common\components\translation\http;

use GuzzleHttp\Exception\ClientException;
use SellerLogic\InternalApi\MainApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;

class ExternalMessageClient implements MessageClientInterface
{
    public function postMessage(array $message): ?array
    {
        /** @var MainApiClient $client */
        $client = \Yii::$container->get('internalApiClient');

        try {
            $response = $client->message()->create($message);

            if (!in_array($response->getStatusCode(), [200, 201], true)) {
                throw new \Exception('Unable to execute request, invalid response.');
            }

            return (new ResponseTransformer())->transform($response);
        } catch (ClientException $exception) {
            if (!$exception->hasResponse() || $exception->getResponse()->getStatusCode() !== 422) {
                throw $exception;
            }
        }

        return null;
    }
}
