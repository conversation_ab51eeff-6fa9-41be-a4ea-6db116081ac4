<?php

namespace common\components\translation\http;

use SellerLogic\InternalApi\MainApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;

class ExternalMessageTranslationClient implements MessageTranslationClientInterface, LanguageClientInterface
{
    private MainApiClient $mainApiClient;

    public function __construct()
    {
        /** @var MainApiClient $client */
        $this->mainApiClient = \Yii::$container->get('internalApiClient');
    }

    public function getMessageTranslations(?string $language, ?string $updatedAt): ?array
    {
        $params = ['all' => 1, 'sync' => 1];
        if (null !== $language) {
            $params['language'] = $language;
        }
        if (null !== $updatedAt) {
            $params['updated_at'] = '>' . $updatedAt;
        }
        $response = $this->mainApiClient->messageTranslation()->index($params);
        if (200 !== $response->getStatusCode()) {
            throw new \Exception('Unable to execute request, invalid response.');
        }

        return (new ResponseTransformer())->transform($response);
    }

    public function getLanguages(): ?array
    {
        $response = $this->mainApiClient->language()->index(['all' => 1, 'active' => 1]);
        if (200 !== $response->getStatusCode()) {
            throw new \Exception('Unable to execute request, invalid response.');
        }

        return (new ResponseTransformer())->transform($response);
    }
}
