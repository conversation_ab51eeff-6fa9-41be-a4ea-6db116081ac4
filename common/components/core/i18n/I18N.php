<?php

namespace common\components\core\i18n;

class I18N extends \yii\i18n\I18N
{
    /**
     * When using this mode, messages will not be translated at the moment.
     * But they will convert into json encoded string which can be decoded in future and suing for translation.
     *
     * @var bool
     */
    public bool $isFutureTranslationMode = false;
    protected ?string $lastCategory = null;

    public function translate($category, $message, $params, $language)
    {
        $this->lastCategory =  $category;
        $translated = parent::translate($category, $message, $params, $language);
        $this->lastCategory = null;

        return $translated;
    }

    public function format($message, $params, $language)
    {
        if (!$this->isFutureTranslationMode) {
            return  parent::format($message, $params, $language);
        }

        return json_encode([
            'message' => $message,
            'params' => $params,
            'category' => $this->lastCategory ?? 'yii'
        ]);
    }

    public function translateFuture(string $rawData): ?string
    {
        $dataForTranslation = json_decode($rawData, true);

        if (!is_array($dataForTranslation)) {
            return $rawData;
        }

        $category = $dataForTranslation['category'] ?? null;
        $params = $dataForTranslation['params'] ?? [];
        $message = $dataForTranslation['message'] ?? null;

        if (empty($category)) {
            return $rawData;
        }

        if (empty($params)) {
            return \Yii::t(
                $category,
                $message,
                $params
            );
        }

        if (isset($params['attribute'])) {
            $params['attribute'] = $this->translateFuture($params['attribute']);
        }

        $message = json_decode($message, true)['message'] ?? null;

        if (empty($message)) {
            return $rawData;
        }

        return \Yii::t(
            $category,
            $message,
            $params
        );
    }
}
