<?php


namespace common\components\core\db\pgsql;


class QueryBuilder extends \yii\db\pgsql\QueryBuilder
{
    /**
     * @var \common\components\core\db\Connection
     */
    public $db;

    public function createIndex($name, $table, $columns, $unique = false)
    {
        if ($unique === self::INDEX_UNIQUE || $unique === true) {
            $index = false;
            $unique = true;
        } else {
            $index = $unique;
            $unique = false;
        }

        return ($unique ? 'CREATE UNIQUE INDEX ' : 'CREATE INDEX ') .
            $this->db->quoteTableName($name, true) . ' ON ' .
            $this->db->quoteTableName($table) .
            ($index !== false ? " USING $index" : '') .
            ' (' . $this->buildColumns($columns) . ')';
    }

    public function renameTable($oldName, $newName)
    {
        return 'ALTER TABLE ' . $this->db->quoteTableName($oldName) . ' RENAME TO ' . $this->db->quoteTableName($newName, true);
    }
}