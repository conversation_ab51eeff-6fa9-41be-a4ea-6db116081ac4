<?php

namespace common\components\core\db\pgsql;

use common\components\LogToConsoleTrait;

class Schema extends \yii\db\pgsql\Schema
{
    use LogToConsoleTrait;

    public static bool $isAllowRefreshCache = false;

    protected function getCacheKey($name)
    {
        return [
            __CLASS__,
            $this->db->prefix,
            $this->getTableNameInSchema($name),
            \Yii::$app->params['isAPICall'] ?? false
        ];
    }

    protected function getCacheTag()
    {
        return md5(serialize([
            __CLASS__,
            $this->db->prefix,
            \Yii::$app->params['isAPICall'] ?? false
        ]));
    }

    public function getTableNameInSchema($name)
    {
        $name = $this->getRawTableName($name);

        $parts = explode('.', $name);
        if (isset($parts[1])) {
            return $parts[1];
        }

        return $name;
    }

    protected function getTableMetadata($name, $type, $refresh)
    {
        $metadata = parent::getTableMetadata($name, $type, $refresh);

        if (empty($metadata)) {
            $isAllowRefreshCachePrev = self::$isAllowRefreshCache;
            self::$isAllowRefreshCache = true;
            $this->refreshTableSchema($name);
            self::$isAllowRefreshCache = $isAllowRefreshCachePrev;
        }

        return $metadata;
    }

    public function createQueryBuilder()
    {
        return new QueryBuilder($this->db);
    }

    // Tmp for debug
    public function refresh()
    {
        // Do not allow to refresh full schema (due a lot of refreshes during migrations), only full refresh after deploy.
        if (!self::$isAllowRefreshCache) {
            return;
        }

        $this->error(new \Exception("BAS-2216: refresh all schemas detected!"));
        parent::refresh();
    }

    public function refreshTableSchema($name)
    {
        // Do not allow to refresh table schema (due a lot of refreshes during migrations), only full refresh after deploy.
        if (!self::$isAllowRefreshCache) {
            return;
        }

        $this->error(new \Exception("BAS-2216: refresh table schema detected!"));
        parent::refreshTableSchema($name);
    }
}
