<?php
/**
 * Created by PhpStorm.
 * User: Valentin
 * Date: 07.06.2018
 * Time: 16:42.
 */
namespace common\components\core\db\mysql;

use common\components\core\db\Connection;

class Schema extends \yii\db\mysql\Schema
{
    /**
     * @var Connection the database connection
     */
    public $db;

    /**
     * {@inheritdoc}
     */
    protected function getCacheKey($name)
    {
        return [
            $this->db->prefix,
            $this->getRawTableName($name),
            isset($_SERVER['HOSTNAME']) ? : 'local',
        ];
    }

    /**
     * {@inheritdoc}
     */
    protected function getCacheTag()
    {
        return md5(serialize([
            $this->db->prefix,
        ]));
    }
    /*
        protected function getTableMetadata($name, $type, $refresh)
        {
            \Yii::trace("getTableMetadata({$name},{$type},{$refresh})",'redis-cache');

            return parent::getTableMetadata($name, $type, $refresh);
        }

        protected function getSchemaMetadata($schema, $type, $refresh)
        {
            \Yii::trace("getTableMetadata({$schema},{$type},{$refresh})",'redis-cache');
            return parent::getSchemaMetadata($schema, $type, $refresh);
        }

        protected function setTableMetadata($name, $type, $data)
        {
            \Yii::trace("setTableMetadata({$name},{$type},{$data})",'redis-cache');
            parent::setTableMetadata($name, $type, $data);
        }
    */
}
