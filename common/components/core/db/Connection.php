<?php

namespace common\components\core\db;

class Connection extends \yii\db\Connection
{

    /**
     * @var string db name or db prefix
     */
    public $prefix;

    /**
     * @var string
     */
    public $schemaName;

    public function __construct(array $config = [])
    {
        parent::__construct($config);

        $this->schemaMap['mysql'] = 'common\components\core\db\mysql\Schema';
        $this->schemaMap['pgsql'] = 'common\components\core\db\pgsql\Schema';

        $this->commandMap['pgsql'] = 'common\components\core\db\Command';
    }

    public function quoteTableName($name, $ignoreSchemaName = false)
    {
        if (!$ignoreSchemaName
            && $this->schemaName
            && strpos($name, $this->schemaName) === false
            && strpos($name, '.') === false // Table name already contains schema name (for example public.seller)
        ){
            return $this->getSchema()->quoteTableName($this->schemaName . '.' . $name);
        }
        return $this->getSchema()->quoteTableName($name);
    }

}
