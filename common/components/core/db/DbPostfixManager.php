<?php

namespace common\components\core\db;

class DbPostfixManager
{
    protected ?string $sellerId = null;
    protected ?int $customerId = null;

    /**
     * @param string|null $sellerId
     * @param int|null $customerId
     */
    public function __construct(?string $sellerId, ?int $customerId)
    {
        $this->sellerId = $sellerId;
        $this->customerId = $customerId;
    }

    public function getDbPostfixForCustomerRelatedDbs(): ?string
    {
        return $this->customerId === null ? null : str_pad($this->customerId, 5, '0', STR_PAD_LEFT);
    }

    public function getDbPostfixForSellerRelatedDbs(): ?string
    {
        if (is_null($this->sellerId)) {
            return null;
        }

        $customerPostfix = $this->getDbPostfixForCustomerRelatedDbs();

        if (is_null($customerPostfix)) {
            throw new \Exception('Customer Id is required for DB postfix');
        }

        return $customerPostfix . '_' . strtolower($this->sellerId);
    }
}
