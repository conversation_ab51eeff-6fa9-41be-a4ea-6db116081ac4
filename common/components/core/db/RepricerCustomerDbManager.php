<?php

namespace common\components\core\db;

use yii\base\Component;
use yii\base\InvalidConfigException;
use yii\db\Connection;

class RepricerCustomerDbManager
{
    protected array $initializedComponents = [];
    protected ?int $customerId = null;

    public function getCustomerId(): ?int
    {
        return $this->customerId;
    }

    public function setCustomerId(int $customerId): void
    {
        if (!$customerId) {
            return;
        }
        $customerId = (integer)trim($customerId, 'c');

        if ($this->customerId !== $customerId) {
            $this->customerId = $customerId;
            $this->initializedComponents = [];
        }
    }

    public function getMainDb(): Connection
    {
        $this->initDb('repricerMainDb');
        return $this->getComponent('repricerMainDb');
    }

    public function getClientDb(): Connection
    {
        $this->initDb('repricerClientDb');
        return $this->getComponent('repricerClientDb');
    }

    public function getComponent(string $componentName): Component
    {
        try {
            $res = \Yii::$app->get($componentName);
        } catch (InvalidConfigException $e) {
            \Yii::error($e->getMessage());
            $res = null;
        }
        return $res;
    }

    protected function initDb(string $key, bool $onlySetting = false): void
    {
        if (array_key_exists($key, $this->initializedComponents)) {
            return;
        }

        $components = \Yii::$app->getComponents();
        $db = $components[$key];

        if (empty($db)) {
            throw new \Exception("Failed to initialize db component $key");
        }

        if ($key !== 'repricerMainDb') {
            $db['dsn'] = $this->setCustomerToDsn($db['dsn']);
        }

        if (!$onlySetting) {
            try {
                \Yii::$app->set($key, $db);
            } catch (InvalidConfigException $e) {
                \Yii::error($e->getMessage());
            }
        }

        $this->initializedComponents[$key] = true;
    }

    protected function setCustomerToDsn(string $dsn): string
    {
        $parts = explode(';', $dsn);

        list($mysql, $hostParam) = explode(':', $parts[0]);
        list($text, $host) = explode('=', $hostParam);

        $parts[0] = "$mysql:$text=$host";

        $last = array_pop($parts);
        list($t, $db) = explode('=', $last);

        $dbParts = explode('_', $db);
        array_pop($dbParts);

        $dbParts[] = $this->getCustomerId();
        $db = implode('_', $dbParts);

        $parts[] = "$t=$db";

        return implode(';', $parts);
    }
}