<?php

namespace common\components\core\db\clickhouse;

use common\components\core\db\dbManager\DbManager;
use common\components\db\ClickhouseDbHelper;
use common\components\nodes\NodesManager;
use common\components\LogToConsoleTrait;
use common\models\NodesAvailability;

class Command extends \bashkarev\clickhouse\Command
{
    use LogToConsoleTrait;

    public static bool $isNodeChangingEnabled = true;

    protected DbManager $dbManager;
    protected NodesManager $nodesManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->nodesManager = new NodesManager();
        parent::__construct();
    }

    protected function queryInternal($method, $fetchMode = null, int $recursionLevel = 0)
    {
        if (!self::$isNodeChangingEnabled) {
            return parent::queryInternal($method, $fetchMode);
        }

        try {
            return parent::queryInternal($method, $fetchMode);
        } catch (\Throwable $e) {
            if (!$this->isBrokenNodeException($e) || $recursionLevel >= 3) {
                throw $e;
            }

            try {
                $this->markCurrentNodeAsBroken($e->getMessage());
                $this->connectToAnotherNode();
            } catch (\Throwable $e1) {
                $this->info($e1);
                throw $e;
            }

            return $this->queryInternal($method, $fetchMode, ++$recursionLevel);
        }
    }

    public function execute(int $recursionLevel = 0)
    {
        if (!self::$isNodeChangingEnabled) {
            return parent::execute();
        }

        try {
            return parent::execute();
        } catch (\Throwable $e) {
            if (!$this->isBrokenNodeException($e) || $recursionLevel >= 3) {
                throw $e;
            }

            try {
                $this->markCurrentNodeAsBroken($e->getMessage());
                $this->connectToAnotherNode();
            } catch (\Throwable $e1) {
                $this->info($e1);
                throw $e;
            }

            return $this->execute(++$recursionLevel);
        }
    }

    protected function isBrokenNodeException(\Throwable $e): bool
    {
        $message = strtolower($e->getMessage());

        if (false !== strpos($message, ClickhouseDbHelper::UNKNOWN_DATABASE_ERROR_IDENTIFIER)) {
            return true;
        }

        if (false !== strpos($message, "due to shutdown")) {
            return true;
        }

        if (false === strpos($message, "db::exception")) {
            return true;
        }

        return false;
    }

    protected function markCurrentNodeAsBroken(string $message)
    {
        $brokenIp = explode(';', explode('host=', $this->db->dsn)[1])[0];
        $this->info("Node {$brokenIp} is broken: " . $message);
        $this->nodesManager->markAsInactive(
            NodesAvailability::TYPE_CLICKHOUSE_CUSTOMER_DB,
            $brokenIp,
            $this->dbManager->getCustomerId(),
            $message
        );
    }

    protected function connectToAnotherNode(): void
    {
        $brokenIp = explode(';', explode('host=', $this->db->dsn)[1])[0];
        $this->db = $this->dbManager->getClickhouseCustomerDb();
        $activeIp = explode(';', explode('host=', $this->db->dsn)[1])[0];

        $this->info("Changed broken node {$brokenIp} to active one {$activeIp}");
    }
}