<?php
/**
 * @copyright Copyright (c) 2017 <PERSON>
 * @license https://github.com/bashkarev/clickhouse/blob/master/LICENSE
 * @link https://github.com/bashkarev/clickhouse#readme
 */

namespace common\components\core\db\clickhouse;

use common\components\core\db\dbManager\DbManager;
use yii\db\TableSchema;

/**
 * <AUTHOR> <<EMAIL>>
 */
class Schema extends \bashkarev\clickhouse\Schema
{
    /**
     * @inheritdoc
     */
    protected function findColumns($table)
    {
        $dbName = \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER);
        $columns = $this->db->createCommand("SELECT * FROM system.columns WHERE database = '{$dbName}' AND table = :name", [':name' => $table->name])->queryAll();

        if ($columns === []) {
            return false;
        }
        foreach ($columns as $info) {
            $column = $this->loadColumnSchema($info);
            $table->columns[$column->name] = $column;
        }

        return true;
    }

    protected function getTableMetadata($name, $type, $refresh)
    {
        $metadata = parent::getTableMetadata($name, $type, $refresh);

        if (empty($metadata)) {
            $this->refreshTableSchema($name);
        }

        return $metadata;
    }
}