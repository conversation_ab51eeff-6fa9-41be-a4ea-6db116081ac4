<?php

namespace common\components\core\db\dbManager\helper;

use yii\db\Connection;
use yii\db\Query;

interface HelperInterface
{
    public function correctBaseComponentName(string $baseComponentName): string;
    public function getBaseConnection(): Connection;
    public function modifyDsnForNewComponent(string $dsn, string $dbName, string $host = null): string;
    public function getPreparedQueryDbNames(string $dbBaseName): Query;
}
