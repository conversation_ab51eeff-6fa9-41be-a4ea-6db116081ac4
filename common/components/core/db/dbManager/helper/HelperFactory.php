<?php

namespace common\components\core\db\dbManager\helper;

use yii\db\Connection;
use yii\db\Query;

class HelperFactory
{
    public const TYPE_CLICKHOUSE = 'clickhouse';
    public const TYPE_POSTGRESS = 'postgress';
    public const TYPE_POSTGRESS_SHARD = 'postgress_shard';
    public const TYPE_POSTGRESS_SPLITTED_BY_DB = 'postgress_splitted';
    public const TYPE_REPRICER_EVENT_POSTGRESS = 'repricer_event_postgress';

    public function getHelper(string $type): HelperInterface
    {
        switch ($type) {
            case self::TYPE_CLICKHOUSE:
                return new Clickhouse();
            case self::TYPE_POSTGRESS:
                return new Postgress();
            case self::TYPE_POSTGRESS_SHARD:
                return new PostgressShard();
            case self::TYPE_POSTGRESS_SPLITTED_BY_DB:
                return new PostgressSplittedByDb();
            case self::TYPE_REPRICER_EVENT_POSTGRESS:
                return new RepricerEventPostgress();
        }

        throw new \Exception("Unable to find db helper for type '$type'");
    }
}
