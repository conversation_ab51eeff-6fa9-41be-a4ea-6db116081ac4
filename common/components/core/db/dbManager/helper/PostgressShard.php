<?php

namespace common\components\core\db\dbManager\helper;

use yii\db\Connection;

class PostgressShard extends Postgress
{
    public function getBaseConnection(?int $node = null): Connection
    {
        $postgresDbIndex = $node ?? \Yii::$app->dbManager->getPostgresDbIndex();
        return $postgresDbIndex == 0 ? \Yii::$app->db : \Yii::$app->db1;
    }

    public function correctBaseComponentName(string $baseComponentName): string
    {
        $postgresDbIndex = \Yii::$app->dbManager->getPostgresDbIndex();
        return $postgresDbIndex == 0 ? $baseComponentName :  $baseComponentName . $postgresDbIndex;
    }
}
