<?php

namespace common\components\core\db\dbManager\helper;

use yii\db\Connection;
use yii\db\Query;

class Clickhouse implements HelperInterface
{
    public function modifyDsnForNewComponent(string $dsn, string $dbName, string $host = null): string
    {
        if (null !== $host) {
            $dsn = preg_replace('/host=(.*?);/', "host=$host;", $dsn);
        }
        return $dsn;
    }

    public function correctBaseComponentName(string $baseComponentName): string
    {
        return 'clickhouse' . ucfirst($baseComponentName);
    }

    public function getBaseConnection(): Connection
    {
        return \Yii::$app->clickhouse;
    }

    public function getPreparedQueryDbNames(string $dbBaseName): Query
    {
        $query = (new Query())
            ->select('name as db_name')
            ->filterWhere(['like', 'name', $dbBaseName . '%', false])
            ->from('system.databases')
        ;
        return $query;
    }
}