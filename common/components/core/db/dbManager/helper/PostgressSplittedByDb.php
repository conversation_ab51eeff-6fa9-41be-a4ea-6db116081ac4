<?php

namespace common\components\core\db\dbManager\helper;

use common\models\Seller;
use yii\caching\TagDependency;
use yii\db\Connection;
use yii\db\Query;

class PostgressSplittedByDb implements HelperInterface
{
    public function modifyDsnForNewComponent(string $dsn, string $dbName, string $host = null): string
    {
        $dsn = preg_replace('/;dbname=(.*)/', ";dbname=$dbName", $dsn);

        if (null !== $host) {
            $dsn = preg_replace('/host=(.*?);/', "host=$host;", $dsn);
        }

        return $dsn;
    }

    public function correctBaseComponentName(string $baseComponentName): string
    {
        return $baseComponentName;
    }

    public function getBaseConnection(): Connection
    {
        return \Yii::$app->db;
    }

    /**
     * Prepares and returns query for fetching db names by given migration namespace
     *
     * @param string $dbBaseName
     * @return Query
     */
    public function getPreparedQueryDbNames(string $dbBaseName): Query
    {
        $likeCondition = $dbBaseName . '%';
        $query = (new Query())
            ->select('datname as db_name')
            ->filterWhere(['like', 'datname', $likeCondition, false])
            ->from('pg_database')->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::COMMON_CACHE_TAG])
            );
        return $query;
    }
}
