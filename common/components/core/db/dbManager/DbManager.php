<?php

namespace common\components\core\db\dbManager;

use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\core\db\dbManager\helper\HelperInterface;
use common\components\core\db\DbPostfixManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\exception\SellerNotFoundException;
use common\components\nodes\NodesManager;
use common\models\Customer;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\DbStructure;
use common\models\NodesAvailability;
use common\models\Seller;
use yii\base\Component;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\caching\ArrayCache;
use yii\caching\CacheInterface;
use yii\caching\TagDependency;
use yii\db\Connection;
use yii\helpers\Inflector;
use yii\helpers\Json;

class DbManager extends Component
{
    protected ?Seller $seller = null;
    protected ?string $sellerId = null;
    protected ?int $customerId = null;
    protected bool $isAPICall = false;
    protected string $blueGreenType = DbStructure::TYPE_GREEN;

    protected HelperFactory $helperFactory;
    protected NodesManager $nodesManager;
    protected ArrayCache $arrayCache;

    const DB_PREFIX_FINANCE = 'finance';
    const DB_PREFIX_ORDER = 'order';
    const DB_PREFIX_ADS = 'ads';
    const DB_PREFIX_CUSTOMER = 'customer';
    const DB_PREFIX_REPRICER_EVENT = 'repricer_event';

    protected const SELLER_RELATED_DBS = [
        self::DB_PREFIX_FINANCE,
        self::DB_PREFIX_ORDER
    ];

    protected const CUSTOMER_RELATED_DBS = [
        self::DB_PREFIX_CUSTOMER,
        self::DB_PREFIX_ADS,
        self::DB_PREFIX_REPRICER_EVENT
    ];

    protected const SIMPLE_CUSTOMER_RELATED_DBS = [
        'repricer_customer_client_db',
        'repricer_customer_client_db_1'
    ];

    private static array $initializedComponents = [];

    public function __construct($config = [])
    {
        $this->helperFactory = new HelperFactory();
        $this->nodesManager = new NodesManager();
        $this->arrayCache = \Yii::$app->arrayCache;
        $this->isAPICall = \Yii::$app->params['isAPICall'] ?? false;

        parent::__construct($config);
    }

    public function __destruct()
    {
        $this->closeAll();
    }

    public function isSellerActive(): bool
    {
        if (empty($this->seller)) {
            return false;
        }

        return $this->seller->is_active;
    }

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function getSellerId(): ?string
    {
        return strtoupper($this->sellerId);
    }
    public function getSeller(): ?Seller
    {
        return $this->seller;
    }

    public function getBlueGreenType(): ?string
    {
        return $this->blueGreenType;
    }

    public function isRepricerSync(): ?bool
    {
        return Customer::find()
                ->select('is_repricer_sync')
                ->where(['=', 'id', $this->customerId])
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->scalar(\Yii::$app->db)
            ;
    }

    public function isSyncDefault(): ?bool
    {
        return Customer::find()
            ->select('is_sync_default')
            ->where(['=', 'id', $this->customerId])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
            )
            ->scalar(\Yii::$app->db)
            ;
    }

    public function isDemo(): bool
    {
        return Seller::find()
            ->where(['=', 'customer_id', $this->customerId])
            ->andWhere(['=', 'is_demo', 't'])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
            )
            ->count('*', \Yii::$app->db) > 0
        ;
    }

    public function isActive(): bool
    {
        return Seller::find()
            ->where(['=', 'customer_id', $this->customerId])
            ->andWhere(['=', 'is_active', 't'])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
            )
            ->count('*', \Yii::$app->db) > 0
        ;
    }

    /**
     * Returns minimum date with available data.
     *
     * @return \DateTime
     * @throws \DateMalformedStringException
     */
    public function getMinimumStatisticDate(bool $isStrict = true): \DateTime
    {
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->cache;

        $cacheKey = 'minimum_statistic_date_' . ($isStrict ? 'strict_' : '') . $this->customerId ;
        $minimumStatisticDate = $cache->get($cacheKey);

        if (!empty($minimumStatisticDate)) {
            return new \DateTime($minimumStatisticDate);
        }

        if ($isStrict) {
            $minimumStatisticDate = TransactionBuffer::find()
                ->select('min(PostedDate)')
                ->scalar();
        } else {
            $minimumStatisticDate = Seller::find()
                ->select("min(created_at - interval '2' year)")
                ->where(['=', 'customer_id', $this->customerId])
                ->scalar()
            ;
        }

        // Nothing found, clickhouse returns this date in this case
        if (empty($minimumStatisticDate) || $minimumStatisticDate === '1970-01-01 00:00:00') {
            $minimumStatisticDate = date('Y-m-01 00:00:00', strtotime('-1 month'));
        } else {
            $cache->set(
                $cacheKey,
                $minimumStatisticDate,
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
            );
        }

        return new \DateTime($minimumStatisticDate);
    }

    public function getPostgresDbIndex()
    {
//        return Seller::find()
//            ->select('postgres_db_index')
//            ->where(['customer_id' => $this->customerId])
//            ->cache(
//                \Yii::$app->params['tagDependencyCacheDuration'],
//                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
//            )
//            ->scalar(\Yii::$app->db) ?? 0;
//
//        // Do not execute this code while amount of consumers not refactored.
//        // This code speeds up processing and can shoot down postgres.
        $cacheKey = __FUNCTION__ . '_customer_' . $this->customerId;
        return $this->arrayCache->getOrSet($cacheKey, function () {
            return Seller::find()
                ->select('postgres_db_index')
                ->where(['customer_id' => $this->customerId])
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->scalar(\Yii::$app->db);
        });
    }

    public function isCustomerDbCreated(): bool
    {
        return Seller::find()
            ->where(['=', 'customer_id', $this->customerId])
            ->andWhere(['=', 'is_db_created', 't'])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
            )
            ->count() > 0
        ;
    }

    public function setBlueGreenType(string $blueGreenType): self
    {
        if (!in_array($blueGreenType, DbStructure::SUPPOERTED_TYPES)) {
            throw new \Exception("Invalid blue green structure type $blueGreenType");
        }

        if ($blueGreenType === DbStructure::TYPE_BLUE) {
            $blueStructure = DbStructure::findOne([
                'type' => DbStructure::TYPE_BLUE,
                'customer_id' => $this->getCustomerId()
            ]);

            if (null ===  $blueStructure) {
                throw new \Exception("Unable  to use blue structure for customer $this->customerId, structure does not exist");
            }
        }

        $this->blueGreenType = $blueGreenType;
        return  $this;
    }

    /**
     * @param int|null $customerId
     * @param bool $checkSeller
     * @param bool $force
     * @return $this
     * @throws SellerNotFoundException
     */
    public function setCustomerId(?int $customerId, bool $checkSeller = true, bool $force = false): self
    {
        if (null !== $this->customerId && (int)$this->customerId === (int)$customerId) {
            return $this;
        }

        $this->customerId = $customerId;

        if ($force) {
            return $this;
        }

        if ($checkSeller) {
            $allowedSellerIds = Seller::find()
                ->select(['id'])
                ->where(['customer_id' => $customerId])
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($customerId)])
                )
                ->column(\Yii::$app->db);

            if (empty($allowedSellerIds)){
                throw new SellerNotFoundException("Any available seller for customer {$this->customerId}");
            }
        }

        return $this;
    }

    /**
     * @param string|null $sellerId
     * @param bool $force
     * @return $this
     * @throws SellerNotFoundException
     * @throws \yii\db\Exception
     */
    public function setSellerId(?string $sellerId, bool $force = false): self
    {
        if ($this->sellerId === $sellerId) {
            return $this;
        }

        $this->sellerId = strtoupper($sellerId);
        $seller = Seller::find()
            ->where([
                'id' => $this->sellerId
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::COMMON_CACHE_TAG])
            )
            ->one(\Yii::$app->db);
        if (is_null($seller)) {
            throw new SellerNotFoundException("Seller: {$sellerId} is not found");
        }
        $this->seller = $seller;
        $this->setCustomerId($seller->customer_id, false, $force);

        return $this;
    }

    public function getAdsDb(): Connection
    {
        return $this->getDb(self::DB_PREFIX_ADS, HelperFactory::TYPE_POSTGRESS_SHARD);
    }

    public function getFinanceDb(): Connection
    {
        return $this->getDb(self::DB_PREFIX_FINANCE, HelperFactory::TYPE_POSTGRESS_SHARD);
    }

    public function getOrderDb(): Connection
    {
        return $this->getDb(self::DB_PREFIX_ORDER, HelperFactory::TYPE_POSTGRESS_SHARD);
    }

    public function getCustomerDb(): Connection
    {
        return $this->getDb(self::DB_PREFIX_CUSTOMER, HelperFactory::TYPE_POSTGRESS_SHARD);
    }

    public function getCustomerDbSlaves(): Connection
    {
        return $this->getDb(self::DB_PREFIX_CUSTOMER, HelperFactory::TYPE_POSTGRESS_SHARD)->getSlave();
    }

    public function getRepricerCustomerClientDb(): Connection
    {
        /** @var \common\components\core\db\Connection $db */
        $db = $this->getRepricerMainDb();
        $customerData = $db->createCommand(
            'SELECT db_index FROM customer WHERE customer.id=:customerId',
            ['customerId' => $this->customerId]
        )->cache(60)->queryOne();

        $dbIndex = null;

        if (!empty($customerData) && !empty($customerData['db_index'])) {
            $dbIndex = $customerData['db_index'];
        }

        return $this->getDb(
            'repricer_customer_client_db_' . $dbIndex,
            HelperFactory::TYPE_POSTGRESS_SPLITTED_BY_DB
        );
    }

    public function getRepricerMainDb(): Connection
    {
        return $this->getDb('repricer_main_db', HelperFactory::TYPE_POSTGRESS, null, true);
    }

    public function getRepricerEventDb(): Connection
    {
        return $this->getDb('repricerEventDb', HelperFactory::TYPE_POSTGRESS, null, true);
    }

    public function getClickhouseCustomerDb(): Connection
    {
        $masterNodes = $this->getClickhouseCustomerMasterNodes();
        $masterNodes = array_values($masterNodes);

        // Sticking customer to single node to prevent data blinking
        // in case when replication works slow do to high amount of data.
        if ($this->isAPICall) {
            $nodeIp = $masterNodes[$this->customerId % count($masterNodes)];
        } else {
            $nodeIp = $masterNodes[array_rand($masterNodes)];
        }

        return $this->getDb(self::DB_PREFIX_CUSTOMER, HelperFactory::TYPE_CLICKHOUSE, $nodeIp);
    }

    public function getClickhouseCustomerMasterNodes(bool $isActiveOnly = true): array
    {
        $connection = $this->getDb(self::DB_PREFIX_CUSTOMER, HelperFactory::TYPE_CLICKHOUSE);
        $nodes = $this->getAllNodesForConnection($connection);
        $nodesMap = getenv('CLICKHOUSE_MASTER_NODES_MAP') ? Json::decode(getenv('CLICKHOUSE_MASTER_NODES_MAP')) : null;

        if (!$nodesMap || !$this->customerId) {
            return $this->nodesManager->getActiveIps(
                NodesAvailability::TYPE_CLICKHOUSE_CUSTOMER_DB,
                $nodes,
                0
            );
        }

        $customerMasterNodes = array_intersect_key(
            $nodes,
            array_flip(
                $nodesMap[$this->customerId % count($nodesMap)]
            )
        );
        $customerMasterNodes = array_values($customerMasterNodes);

        if (!$isActiveOnly) {
            return $customerMasterNodes;
        }

        return $this->nodesManager->getActiveIps(
            NodesAvailability::TYPE_CLICKHOUSE_CUSTOMER_DB,
            $customerMasterNodes,
            $this->customerId
        );
    }

    /**
     * Temp function for BAS-864 something like AB test to prevent overload
     *
     * @param string $schemaPrefix
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @return \Generator
     */
    public function iterateAnalyticActiveSchemasBAS864(
        string $schemaPrefix,
        int $customerIdFrom = null,
        int $customerIdTo = null
    )
    {
        foreach ($this->iterateSchemas($schemaPrefix, $customerIdFrom, $customerIdTo) as $schema) {
            $seller = Seller::find()
                ->where([
                    'is_active' => true,
                    'customer_id' => $this->customerId
                ])
                ->limit(1)
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->one(\Yii::$app->db);
            if (!empty($seller)) {
                yield $schema;
            }

            if ($this->customerId >= 5000) {
                continue;
            }

            $seller = Seller::find()
                ->where([
                    'is_analytic_active' => true,
                    'customer_id' => $this->customerId
                ])
                ->limit(1)
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->one(\Yii::$app->db);

            if (!empty($seller)) {
                yield $schema;
            }
        }
    }

    /**
     * Allows to iterate schemas using different filters (approaches).
     *
     * @param string $schemaPrefix
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @param array $filters Filters to modify schema selection.
     *        - is_only_active` (bool): if true - only active schemas will be walked through
     *
     * @return \Iterator
     */
    public function iterateFilteredSchemas(
        string $schemaPrefix,
        int $customerIdFrom = null,
        int $customerIdTo = null,
        array $filters = []
    ): \Iterator {
        $isOnlyActive = $filters['is_only_active'] ?? false;

        if ($isOnlyActive) {
            return $this->iterateActiveSchemas($schemaPrefix, $customerIdFrom, $customerIdTo);
        }

        return $this->iterateSchemas($schemaPrefix, $customerIdFrom, $customerIdTo);
    }

    public function iterateAnalyticActiveSchemas(
        string $schemaPrefix,
        int $customerIdFrom = null,
        int $customerIdTo = null
    ): \Iterator
    {
        foreach ($this->iterateSchemas($schemaPrefix, $customerIdFrom, $customerIdTo) as $schema) {
            $seller = Seller::find()
                ->where([
                    'is_analytic_active' => true,
                    'customer_id' => $this->customerId
                ])
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->one(\Yii::$app->db);
            if (empty($seller)) {
                continue;
            }

            yield $schema;
        }
    }

    public function iterateActiveSchemas(
        string $schemaPrefix,
        int $customerIdFrom = null,
        int $customerIdTo = null
    ): \Iterator
    {
        foreach ($this->iterateSchemas($schemaPrefix, $customerIdFrom, $customerIdTo) as $schema) {
            $seller = Seller::find()
                ->where([
                    'is_active' => true,
                    'customer_id' => $this->customerId
                ])
                ->limit(1)
                ->cache(
                    \Yii::$app->params['tagDependencyCacheDuration'],
                    new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                )
                ->one(\Yii::$app->db);
            if (empty($seller)) {
                continue;
            }

            yield $schema;
        }
    }

    public function iterateSchemas(
        string $schemaPrefix,
        int $customerIdFrom = null,
        int $customerIdTo = null,
        int $nodeId = null,
        string $dbType = HelperFactory::TYPE_POSTGRESS_SHARD
    ): \Iterator
    {
        $nodes = $nodeId !== null ? [$nodeId] : [0,1];
        foreach ($nodes as $node) {
            $helper = $this->helperFactory->getHelper($dbType);
            $query = $helper->getPreparedQueryDbNames($schemaPrefix);
            foreach ($query->batch(500, $helper->getBaseConnection($node)) as $queryResults) {
                foreach ($queryResults as $queryResult) {
                    $schemaName = $queryResult['schema_name'];
                    // Using base table name as a starting point to extract db postfix from current database name.
                    $dbPostfix = trim(str_replace($schemaPrefix, '', $schemaName), '_');

                    if (empty($dbPostfix)) {
                        $dbPostfix = null;
                    }

                    $this->setDbPostfix($schemaPrefix, $dbPostfix);

                    if (!empty($customerIdFrom) && $this->customerId < $customerIdFrom) {
                        continue;
                    }

                    if (!empty($customerIdTo) && $this->customerId >= $customerIdTo) {
                        continue;
                    }
                    yield [
                        'schema' => $schemaName,
                        'customer_id' => $this->customerId,
                        'dbIndex' => $node
                    ];
                }
            }
        }
    }

    public function iterateDb(
        string $dbBaseName,
        string $dbType = HelperFactory::TYPE_CLICKHOUSE,
        int $customerIdFrom = null,
        int $customerIdTo = null
    ): \Iterator
    {
        $dbBaseName = rtrim($dbBaseName, 'Db');
        $helper = $this->helperFactory->getHelper($dbType);
        $query = $helper->getPreparedQueryDbNames($dbBaseName);
        $baseConnection = $helper->getBaseConnection();
        $nodes = $this->getAllNodesForConnection($baseConnection);

        foreach ($query->batch(500, $baseConnection) as $queryResults) {
            foreach ($queryResults as $queryResult) {
                $dbName = $queryResult['db_name'];
                // Using base table name as a starting point to extract db postfix from current database name.
                $dbPostfix = trim(str_replace($dbBaseName, '', $dbName), '_');

                if (empty($dbPostfix)) {
                    $dbPostfix = null;
                }

                try {
                    $this->setDbPostfix($dbBaseName, $dbPostfix);
                } catch (\Throwable $e) {
                    continue;
                }

                if (!empty($customerIdFrom) && $this->customerId < $customerIdFrom) {
                    continue;
                }

                if (!empty($customerIdTo) && $this->customerId >= $customerIdTo) {
                    continue;
                }

                $seller = Seller::find()
                    ->where(['customer_id' => $this->customerId])
                    ->cache(
                        \Yii::$app->params['tagDependencyCacheDuration'],
                        new TagDependency(['tags' => Seller::getCacheTag($this->customerId)])
                    )
                    ->one(\Yii::$app->db);
                if (!empty($this->customerId) && empty($seller)) {
                    continue;
                }

                foreach ($nodes as $host) {
                    $db = $this->getDb($dbBaseName, $dbType, $host);
                    yield [
                        'dbComponent' => $db,
                        'dbName' => $dbName,
                        'host' => $host
                    ];
                }
            }
        }
    }

    /**
     * @throws InvalidConfigException
     */
    public function getDb(
        string $dbName,
        string $dbType = HelperFactory::TYPE_POSTGRESS,
        string $nodeHost = null,
        bool $isIgnorePostfix = false
    ): Connection
    {
        $baseDbComponentName = lcfirst(Inflector::camelize($dbName));
        if (false === strpos($baseDbComponentName, 'Db') && $baseDbComponentName !== 'db' && $dbType === HelperFactory::TYPE_POSTGRESS_SPLITTED_BY_DB) {
            $baseDbComponentName .= 'Db';
        }

        $helper = $this->helperFactory->getHelper($dbType);
        $baseDbComponentName = $helper->correctBaseComponentName($baseDbComponentName);
        $componentName = $baseDbComponentName;
        $dbPostfix = $this->getDbPostfix($dbName);
        if (!$isIgnorePostfix && !empty($dbPostfix) && $dbType === HelperFactory::TYPE_POSTGRESS_SPLITTED_BY_DB) {
            $componentName = $baseDbComponentName . $dbPostfix;
        }

        $dbSettings = $this->getDbSettings($dbType, $baseDbComponentName, $helper->getBaseConnection());

        $dbPrefix = $dbSettings['prefix'] ?? '';
        $cacheKey = 'node_' . $nodeHost . '_component_' . $componentName . '_' . $dbPrefix;

        if (!empty(self::$initializedComponents[$cacheKey])) {
            return self::$initializedComponents[$cacheKey];
        }

        if (empty($nodeHost) && empty($dbPostfix) && isset($existingComponents[$componentName])) {
            self::$initializedComponents[$cacheKey] = \Yii::$app->get($componentName);
            return self::$initializedComponents[$cacheKey];
        }

        $dbName = $this->constructDbName($dbName, $dbPostfix);
        $dbSettings['dsn'] = $helper->modifyDsnForNewComponent($dbSettings['dsn'], $dbName, $nodeHost);

        if (!empty($dbSettings['slaves'])){
            foreach ($dbSettings['slaves'] as &$slave) {
                $slave['dsn'] = $helper->modifyDsnForNewComponent($slave['dsn'], $dbName, $nodeHost);
            }
            unset($slave);
        }

        \Yii::$app->set($componentName, $dbSettings);
        self::$initializedComponents[$cacheKey] = \Yii::$app->get($componentName);

        return self::$initializedComponents[$cacheKey];
    }

    /**
     * @throws \Exception
     */
    private function getDbSettings(string $dbType, ?string $baseDbComponentName, Connection $baseConnection)
    {
        $existingComponents = \Yii::$app->getComponents();

        $dbSettings = $existingComponents[$baseDbComponentName] ?? null;

        if (empty($dbSettings)) {
            foreach ($existingComponents as $componentName => $componentSettings) {

                if (!is_array($componentSettings)) {
                    continue;
                }
                if (isset($componentSettings['dsn']) && $componentSettings['dsn'] === $baseConnection->dsn) {
                    $dbSettings = $componentSettings;
                    break;
                }
            }
        }

        if (empty($dbSettings)) {
            throw new \Exception("Unable to build component settings for component $componentName ($dbType)");
        }

        return $dbSettings;
    }

    public function closeAll(): void
    {
        /** @var Connection $dbComponent */
        foreach (self::$initializedComponents as $dbComponent) {
            $dbComponent->close();
        }
    }

    public function getAllNodesForConnection(Connection $connection): array
    {
        $nodes = array_merge($connection->masters, $connection->slaves);
        if (empty($nodes)) {
            preg_match('/host=(.*);/', $connection->dsn, $matches);
            $nodes[] = $matches[1] ?? null;
            $nodes = array_filter($nodes);
        }
        return $nodes;
    }

    public function getShardPostgressConfig(): array
    {
        $confEnv = [
            [
                'dbHost' => getenv('APP_DB_HOST'),
                'dbSlaveHost' => getenv('APP_DB_SLAVE_HOST'),
                'dbPort' => getenv('APP_DB_PORT'),
                'dbSlavePort' => getenv('APP_DB_SLAVE_PORT'),
                'dbUser' => getenv('APP_DB_USERNAME'),
                'dbPassword' => getenv('APP_DB_PASSWORD'),
                'dbUserSlave' => getenv('APP_DB_USERNAME_SLAVE'),
                'dbPasswordSlave' => getenv('APP_DB_PASSWORD_SLAVE'),
                'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
            ],
            [
                'dbHost' => getenv('APP_DB_1_HOST'),
                'dbSlaveHost' => getenv('APP_DB_1_SLAVE_HOST'),
                'dbPort' => getenv('APP_DB_1_PORT'),
                'dbSlavePort' => getenv('APP_DB_1_SLAVE_PORT'),
                'dbUser' => getenv('APP_DB_1_USERNAME'),
                'dbPassword' => getenv('APP_DB_1_PASSWORD'),
                'dbUserSlave' => getenv('APP_DB_1_USERNAME_SLAVE'),
                'dbPasswordSlave' => getenv('APP_DB_1_PASSWORD_SLAVE'),
                'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
            ]
        ];

        $postgresDbIndex = $this->getPostgresDbIndex() ?? 0;

        return $confEnv[$postgresDbIndex];
    }
    public function getRepricerEventPostgressConfig(): array
    {
        $confEnv = [
            'dbHost' => getenv('REPRICER_EVENT_DB_HOST'),
            'dbPort' => getenv('REPRICER_EVENT_DB_PORT'),
            'dbUser' => getenv('REPRICER_EVENT_DB_USERNAME'),
            'dbPassword' => getenv('REPRICER_EVENT_DB_PASSWORD'),
            'profitDashDbName' => getenv('REPRICER_EVENT_DB_NAME'),
        ];

        return $confEnv;
    }

    /**
     * Converts passed string to sharding key (number from zero to count nodes).
     *
     * @param string $shardingFieldValue
     * @return int
     */
    public function generateShardingKey(Connection $connection, string $shardingFieldValue): int
    {
        return crc32($shardingFieldValue) % count($this->getAllNodesForConnection($connection));
    }

    protected function constructDbName(string $dbBaseName, ?string $dbPostfix): string
    {
        $nameParts = explode('_', $dbBaseName);

        // Removing shard number from base db name
        if (is_numeric($nameParts[array_key_last($nameParts)]) || empty($nameParts[array_key_last($nameParts)])) {
            unset($nameParts[array_key_last($nameParts)]);
        }

        $dbBaseName = implode('_', $nameParts);

        return $dbBaseName . (null !== $dbPostfix ? '_' . $dbPostfix : '');
    }

    protected function setDbPostfix(string $baseDbName, ?string $postfix = null): void
    {
        if (in_array($baseDbName, self::CUSTOMER_RELATED_DBS)) {
            $this->customerId = $postfix;
        } else if (in_array($baseDbName, self::SELLER_RELATED_DBS)) {
            if (!is_null($postfix)) {
                $postfixParts = explode('_', $postfix);
                $this->customerId = (int)$postfixParts[0];
                $this->sellerId = $postfixParts[1];
            }
        }
    }

    /**
     * Returns schema name based on prefix
     * <AUTHOR>
     * @param $dbNamePrefix
     * @return string
     */
    public function getSchemaName($dbNamePrefix){
        $postfix = null;

        if (in_array($dbNamePrefix, self::CUSTOMER_RELATED_DBS)) {
            $postfix = $this->getDbPostfixForCustomerRelatedDbs();
        } else if (in_array($dbNamePrefix, self::SELLER_RELATED_DBS)) {
            $postfix = $this->getDbPostfixForSellerRelatedDbs();
        }

        return $dbNamePrefix . ($postfix ? '_' . $postfix : '');
    }

    /**
     * Returns clickhouse db name based on prefix
     * <AUTHOR>
     * @param $dbNamePrefix
     * @return string
     */
    public function getClickhouseDbName($dbNamePrefix){
        $postfix = null;

        if (in_array($dbNamePrefix, self::CUSTOMER_RELATED_DBS)) {
            $postfix = $this->getDbPostfixForCustomerRelatedDbs();
        }

        return $dbNamePrefix . ($postfix ? '_' . $postfix : '');
    }

    protected function getDbPostfix(string $baseDbName): ?string
    {
        $baseDbName = trim($baseDbName, '_');

        if (in_array($baseDbName, self::SIMPLE_CUSTOMER_RELATED_DBS)) {
            return empty($this->customerId) ? null : (string)$this->customerId;
        } else if (in_array($baseDbName, self::CUSTOMER_RELATED_DBS)) {
            return $this->getDbPostfixForCustomerRelatedDbs();
        } else if (in_array($baseDbName, self::SELLER_RELATED_DBS)) {
            return $this->getDbPostfixForSellerRelatedDbs();
        }

        return null;
    }

    protected function getDbPostfixForCustomerRelatedDbs(): ?string
    {
        return (new DbPostfixManager($this->sellerId, $this->customerId))->getDbPostfixForCustomerRelatedDbs();
    }

    protected function getDbPostfixForSellerRelatedDbs(): ?string
    {
        return (new DbPostfixManager($this->sellerId, $this->customerId))->getDbPostfixForSellerRelatedDbs();
    }

    public static function getSellerRelatedDbs(): array
    {
        return self::SELLER_RELATED_DBS;
    }

    public static function getCustomerRelatedDbs(): array
    {
        return self::CUSTOMER_RELATED_DBS;
    }
}
