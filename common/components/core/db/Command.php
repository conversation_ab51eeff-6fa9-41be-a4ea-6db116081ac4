<?php

namespace common\components\core\db;

use common\components\LogToConsoleTrait;
use yii\db\Exception;

class Command extends \yii\db\Command
{
    use LogToConsoleTrait;

    protected function internalExecute($rawSql)
    {
        $attempt = 0;
        while (true) {
            try {
                $attempt++;
                parent::internalExecute($rawSql);
                if ($attempt>1){
                    $this->log('Reconnect to db: success after attempts ' . $attempt);
                }
                break;
            } catch (Exception $e) {
                if (!$this->retryHandler($e, $attempt)) {
                    $this->log('Reconnect to db: failed. ');
                    throw $e;
                }
            }
        }
    }

    protected function retryHandler(\Throwable $e, $attempt): bool
    {
        if ($attempt > 3) {
            return false;
        }

        if (false !== strpos($e->getMessage(), 'closed the connection unexpectedly')) {
            $this->db->close();
            $this->db->open();
            return true;
        }

        return false;
    }
}