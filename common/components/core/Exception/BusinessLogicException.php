<?php

namespace common\components\core\Exception;

/**
 * This type of exception is using for showing some business logic errors to customer.
 * Be aware - do not pass technical details to customer.
 * Message should contain only information that can be shown to customer.
 */
class BusinessLogicException extends \Exception implements  InternalCodeAwareInterface
{
    public function __construct(string $translatedCustomerMessage = '', int $code = 422, \Throwable $previous = null)
    {
        parent::__construct($translatedCustomerMessage, $code, $previous);
    }

    public function getInternalCode(): string
    {
        return InternalCodeAwareInterface::INTERNAL_CODE_BUSINESS_LOGIC_ERROR;
    }
}
