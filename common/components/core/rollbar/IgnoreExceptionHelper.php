<?php


namespace common\components\core\rollbar;

use common\components\core\Exception\CustomerDoesNotExistException;
use common\components\core\Exception\ModuleIsNotEnabledException;
use common\components\core\Exception\NotValidPlan;
use common\components\exception\SellerNotFoundException;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\sellingApi\exception\DateRangeIsNotValidException;
use common\components\sellingApi\exception\NextTokenExpiredException;
use common\components\sellingApi\exception\QuotaExceededException;
use common\components\tokenService\Exception\NoAccessTokenException;
use GuzzleHttp\Exception\ConnectException;
use PhpAmqpLib\Exception\AMQPConnectionClosedException;
use SellingPartnerApi\ApiException;
use Symfony\Component\OptionsResolver\Exception\NoConfigurationException;
use yii\db\Exception;
use yii\web\ForbiddenHttpException;
use yii\web\MethodNotAllowedHttpException;
use yii\web\NotFoundHttpException;
use yii\web\TooManyRequestsHttpException;
use yii\web\UnauthorizedHttpException;
use yii\web\UnprocessableEntityHttpException;

class IgnoreExceptionHelper
{
    public static function getIgnoreExceptionList(): array
    {
        return [
            [NotFoundHttpException::class],
            [ForbiddenHttpException::class],
            [MethodNotAllowedHttpException::class],
            [TooManyRequestsHttpException::class],
            [UnauthorizedHttpException::class],
            [UnprocessableEntityHttpException::class],
            [SellerNotFoundException::class],
            [\Throwable::class, 'getCode' => [403]],
            [AMQPConnectionClosedException::class, 'getMessage' => [
                'Broken pipe or closed connection',
            ]],
            [ConnectException::class, 'getMessage' => [
                'Failed to connect to sellingpartnerapi-eu.amazon.com'
            ]],
            [QuotaExceededException::class],
            [NextTokenExpiredException::class],
//            [AccessTokenInvalidException::class],
            [NextTokenExpiredException::class],
            [NoAccessTokenException::class],
            [DateRangeIsNotValidException::class],
            [ModuleIsNotEnabledException::class],
            [CustomerDoesNotExistException::class],
            [NoConfigurationException::class],
            [ApiException::class, 'getMessage' => [
                // Bug in amazon - existing reports does not accessible in some cases
                'Invalid reportDocumentId'
            ]],
            [Exception::class, 'getMessage' => [
                'relation "list_'
            ]],
        ];
    }

    public static function checkIgnore($toLog) : bool
    {
        if (is_string($toLog)) {
            $toLog = new \Exception($toLog);
        }

        if (! ($toLog instanceof \Exception)) {
            return false;
        }

        $ignoreExceptionList = self::getIgnoreExceptionList();

        foreach ($ignoreExceptionList as $ignoreConfig) {
            if ($toLog instanceof $ignoreConfig[0]) {
                if (count($ignoreConfig) === 1) {
                    return true;
                }

                foreach (array_slice($ignoreConfig, 1) as $propertyOrMethod => $ignoredValues) {
                    if (method_exists($toLog, $propertyOrMethod)) {
                        $testValue = $toLog->$propertyOrMethod();
                    } else {
                        $testValue = $toLog->$propertyOrMethod;
                    }

                    foreach ($ignoredValues as $ignoredValue) {
                        if (is_string($ignoredValue) && false !== strpos($testValue, $ignoredValue)) {
                            return true;
                        }

                        if ($ignoredValue == $testValue) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }
}
