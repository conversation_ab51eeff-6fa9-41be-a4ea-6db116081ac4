<?php

declare(strict_types=1);

namespace common\components\core\rollbar;

use b<PERSON><PERSON><PERSON>\yii\rollbar\Rollbar;
use Roll<PERSON>\Rollbar as BaseRollbar;

/**
 * Will initialize Rollbar with Yii2 environment custom payload.
 * @see https://github.com/rollbar/rollbar-php
 * @see https://docs.rollbar.com/docs/basic-php-installation-setup
 */
class RollbarLoader extends Rollbar
{
    /** @var array<string,mixed> */
    public array $config = [];

    #[\Override]
    public function init(): void
    {
        BaseRollbar::init([
            'enabled' => $this->enabled,
            'access_token' => $this->accessToken,
            'base_api_url' => $this->baseApiUrl,
            'batch_size' => $this->batchSize,
            'batched' => $this->batched,
            'branch' => $this->branch,
            'code_version' => $this->codeVersion,
            'environment' => $this->environment,
            'host' => $this->host,
            'included_errno' => $this->includedErrno,
            'check_ignore' => function ($isUncaught, $toLog, $payload) {
                return \common\components\core\rollbar\IgnoreExceptionHelper::checkIgnore($toLog);
            },
            'logger' => $this->logger,
            'person_fn' => $this->personFn,
            'root' => !empty($this->root) ? \Yii::getAlias($this->root) : null,
            'scrub_fields' => $this->scrubFields,
            'timeout' => $this->timeout,
            'proxy' => $this->proxy,
            'enable_utf8_sanitization' => $this->enableUtf8Sanitization,
        ], false, false, false);
    }
}
