<?php

namespace common\components\core\rollbar;

use Rollbar\Rollbar;

/**
 * Will initialize Rollbar with Yii2 environment custom payload.
 * @see https://github.com/rollbar/rollbar-php
 * @see https://docs.rollbar.com/docs/basic-php-installation-setup
 */
class RollbarLoader extends \fl0v\yii2\rollbar\RollbarLoader
{
    /**
     * Send log to Rollbar.
     * @param string $level      Severity level as defined in Rollbar
     * @param mixed  $toLog      The thing to be logged (message, exception, error)
     * @param array  $extra      Extra params to be sent along with the payload
     * @param bool   $isUncaught It will be set to true if the error was caught by the global error handler
     */
    public function log($level, $toLog, $extra = [], $isUncaught = false)
    {
        Rollbar::log($level, $toLog, $extra, $isUncaught);
    }
}
