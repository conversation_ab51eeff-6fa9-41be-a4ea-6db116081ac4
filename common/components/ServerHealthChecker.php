<?php

namespace common\components;

use common\components\healthCheckers\Clickhouse1Checker;
use common\components\healthCheckers\Clickhouse2Checker;
use common\components\healthCheckers\Clickhouse3Checker;
use common\components\healthCheckers\Clickhouse4Checker;
use common\components\healthCheckers\HealthCheckerInterface;
use common\components\healthCheckers\Postgres0MasterChecker;
use common\components\healthCheckers\Postgres0SlaveChecker;
use common\components\healthCheckers\Postgres1MasterChecker;
use common\components\healthCheckers\Postgres1SlaveChecker;
use common\components\healthCheckers\RabbitMQChecker;
use common\components\healthCheckers\RedisChecker;

class ServerHealthChecker
{
    protected function getCheckers()
    {
        return [
            new Postgres0MasterChecker(),
            new Postgres1MasterChecker(),
            new Postgres0SlaveChecker(),
            new Postgres1SlaveChecker(),
            new Clickhouse1Checker(),
            new Clickhouse2Checker(),
            new Clickhouse3Checker(),
            new Clickhouse4Checker(),
            new RedisChecker(),
            new RabbitMQChe<PERSON>()
        ];
    }

    /**
     * @return array
     */
    public function getReport(): array
    {
        $checks = [];

        /**
         * @var HealthCheckerInterface $checker
         */
        foreach ($this->getCheckers() as $checker) {
            $checks[$checker->getName()] = $checker->check();
        }

        return [
            'checks' => $checks,
            'date' => date('Y-m-d H:i:s'),
        ];
    }
}
