<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="Cell",
 *     title="Cell",
 *     description="Table cell with data",
 *     type="object",
 *     required={"columnId", "rowId", "value", "type"},
 *     @OA\Property(
 *         property="columnId",
 *         type="string",
 *         description="Column identifier",
 *         example="column_1"
 *     ),
 *     @OA\Property(
 *         property="rowId",
 *         type="string",
 *         description="Row identifier",
 *         example="row_1"
 *     ),
 *     @OA\Property(
 *         property="value",
 *         description="Cell value",
 *         oneOf={
 *             @OA\Schema(type="string"),
 *             @OA\Schema(type="number"),
 *             @OA\Schema(type="integer"),
 *             @OA\Schema(type="null")
 *         },
 *         example="1000.50"
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         description="Cell type",
 *         enum={"text", "money", "pct", "count"},
 *         example="money"
 *     )
 * )
 */
class Cell
{
    public const TYPE_TEXT = 'text';
    public const TYPE_MONEY = 'money';
    public const TYPE_PERCENTS = 'pct';
    public const TYPE_COUNT = 'count';

    public string $columnId;
    public string $rowId;
    public $value;
    public $type;
}