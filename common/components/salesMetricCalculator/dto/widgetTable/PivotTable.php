<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

class PivotTable
{
    /**
     * @var Column[]
     */
    public array $columns;

    /**
     * @var Row[]
     */
    public array $rows;

    public string $label;

    /**
     * @var Cell[]
     */
    protected array $cellsAll = [];

    protected array $rowsAll = [];

    public function addColumn(Column $column): self
    {
        $this->columns[] = $column;

        return $this;
    }

    public function addRow(Row $row, Row $parentRow = null, string $defaultCellType = Cell::TYPE_TEXT): self
    {
        $this->rowsAll[$row->id] = $row;

        if (!empty($parentRow)) {
            $parentRow->children[] = $row;
        } else {
            $this->rows[] = $row;
        }

        $this->generateCells($row, $defaultCellType);

        return $this;
    }

    public function getCell(string $rowId, string $columnId): Cell
    {
        $cellKey = implode('_', [$rowId, $columnId]);
        $cell = $this->cellsAll[$cellKey];

        if (empty($cell)) {
            throw new \Exception("Unable to find cell $cellKey");
        }

        return $cell;
    }

    public function getRow(string $rowId): Row
    {
        $row = $this->rowsAll[$rowId];

        if (empty($row)) {
            throw new \Exception("Unable to find row $rowId");
        }

        return $row;
    }

    protected function generateCells(Row $row, string $defaultCellType = Cell::TYPE_TEXT): self
    {
        if (empty($this->columns)) {
            throw new \Exception("Empty columns, unable to generate cells");
        }

        foreach ($this->columns as $column) {
            $cell = new Cell();
            $cell->columnId = $column->id;
            $cell->rowId = $row->id;
            $cell->type = $defaultCellType;

            $row->cells[] = $cell;

            $cellKey = implode('_', [$cell->rowId, $cell->columnId]);
            $this->cellsAll[$cellKey] = $cell;
        }

        return $this;
    }
}