<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="Column",
 *     title="Column",
 *     description="Колонка таблицы",
 *     type="object",
 *     required={"id", "name"}
 * )
 */
class Column
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="string",
     *     description="Уникальный идентификатор колонки",
     *     example="revenue"
     * )
     */
    public string $id;

    /**
     * @OA\Property(
     *     property="name",
     *     type="string",
     *     description="Отображаемое название колонки",
     *     example="Выручка"
     * )
     */
    public string $name;
}