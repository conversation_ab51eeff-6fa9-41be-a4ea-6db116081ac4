<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="Column",
 *     title="Column",
 *     description="Table column",
 *     type="object",
 *     required={"id", "name"},
 *     @OA\Property(
 *         property="id",
 *         type="string",
 *         description="Unique column identifier",
 *         example="revenue"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Display column name",
 *         example="Revenue"
 *     )
 * )
 */
class Column
{
    public string $id;
    public string $name;
}