<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="Row",
 *     title="Row",
 *     description="Table row",
 *     type="object",
 *     required={"id", "name", "children", "cells"},
 *     @OA\Property(
 *         property="id",
 *         type="string",
 *         description="Unique row identifier",
 *         example="total_revenue"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Display row name",
 *         example="Total Revenue"
 *     ),
 *     @OA\Property(
 *         property="children",
 *         type="array",
 *         description="Child rows for hierarchical structure",
 *         @OA\Items(ref="#/components/schemas/Row")
 *     ),
 *     @OA\Property(
 *         property="cells",
 *         type="array",
 *         description="Cells of this row",
 *         @OA\Items(ref="#/components/schemas/Cell")
 *     )
 * )
 */
class Row
{
    public string $id;
    public string $name;

    /**
     * @var Row[]
     */
    public array $children = [];

    /**
     * @var Cell[]
     */
    public array $cells = [];
}