<?php

namespace common\components\salesMetricCalculator\dto\widgetTable;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="Row",
 *     title="Row",
 *     description="Строка таблицы",
 *     type="object",
 *     required={"id", "name", "children", "cells"}
 * )
 */
class Row
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="string",
     *     description="Уникальный идентификатор строки",
     *     example="total_revenue"
     * )
     */
    public string $id;

    /**
     * @OA\Property(
     *     property="name",
     *     type="string",
     *     description="Отображаемое название строки",
     *     example="Общая выручка"
     * )
     */
    public string $name;

    /**
     * @OA\Property(
     *     property="children",
     *     type="array",
     *     description="Дочерние строки (для иерархической структуры)",
     *     @OA\Items(ref="#/components/schemas/Row")
     * )
     * @var Row[]
     */
    public array $children = [];

    /**
     * @OA\Property(
     *     property="cells",
     *     type="array",
     *     description="Ячейки данной строки",
     *     @OA\Items(ref="#/components/schemas/Cell")
     * )
     * @var Cell[]
     */
    public array $cells = [];
}