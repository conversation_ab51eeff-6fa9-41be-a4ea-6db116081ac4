<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\CustomerComponent;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\components\salesMetricCalculator\dto\ProfitResult;
use common\components\widget\TransactionQueryExecutor;
use yii\db\Exception;

/**BAS-1366-product-list-filter-options
 * Calculates and returns metrics related to profit.
 *
 * @package common\components\ProfitCalcualtor
 */
class ProfitCalculator
{
    private FiltersForm $filtersForm;
    private UnitsCalculator $unitsCalculator;
    private TreeStructureHelper $adjacencyListHelper;
    private DataSeriesStructureManager $dataSeriesStructureManager;
    private CategoriesStructureManager $categoriesStructureManager;
    private DataSeriesRetriever $dataSeriesRetriever;
    private Calculator $calculator;
    private CustomerComponent $customerComponent;
    private DataSanitizer $dataSanitizer;
    private SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct(FiltersForm $filtersForm)
    {
        $this->dataSeriesStructureManager= new DataSeriesStructureManager();
        $this->categoriesStructureManager = new CategoriesStructureManager();
        $this->dataSeriesRetriever = new DataSeriesRetriever($filtersForm);
        $this->calculator = new Calculator();
        $this->adjacencyListHelper = new TreeStructureHelper();
        $this->unitsCalculator = new UnitsCalculator($filtersForm);
        $this->filtersForm = $filtersForm;
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $this->dataSanitizer = new DataSanitizer();

        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function setFiltersForm(FiltersForm $filtersForm): self
    {
        $this->filtersForm = $filtersForm;
        return $this;
    }

    /**
     * Calculates and returns profit metrics.
     *
     * @param string $periodType
     * @param int|null $maxDepth Maximum depth of categories and data series
     * @param string|null $categoryId
     * @return ProfitResult
     * @throws Exception
     */
    public function calc(
        string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY,
        int $maxDepth = 1,
        string $categoryId = null
    ): ProfitResult {
        $salesCategoryStrategy = $this->salesCategoryStrategyFactory->getStrategyByType($this->filtersForm->salesCategoryStrategy ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY);

        $dataSeries = $this->dataSeriesRetriever->getDataSeries($salesCategoryStrategy, $maxDepth, $periodType);

        $flatTree = $this->categoriesStructureManager->getSalesCategoriesFatTreeData($salesCategoryStrategy, $categoryId);

        $flatTree = $this->categoriesStructureManager->fillFlatTreeWithAmounts($flatTree, $dataSeries);
        ksort($flatTree);
        $salesCategoriesTree = $this->adjacencyListHelper->convertFlatFormatToTree($flatTree, $maxDepth);

        $dataSeries = $this->collapseDataSeries($dataSeries, $flatTree, $maxDepth);
        $allPossibleCategories = $this->getAllPossibleCategories($salesCategoryStrategy, $flatTree);
        $groupedUnits = $this->unitsCalculator->getUnitsGroupedByDate($periodType);

        if (empty($dataSeries) && empty($groupedUnits)) {
            $profitResult = new ProfitResult();
            $profitResult->dataSeries = [];
            $profitResult->salesCategories = $salesCategoriesTree;
            return $profitResult;
        }

        $this->dataSeriesStructureManager->setAllPossibleCategories($allPossibleCategories);
        $this->dataSeriesStructureManager->setAllPossibleUnits([
            'orders',
            'ordersCanceled',
            'units',
            'promo',
            'refunds',
        ]);

        $timezoneName = $this->customerComponent->getUserTimezoneName();
        $timezone = new \DateTimeZone($timezoneName);

        $dataSeriesStructure = $this
            ->dataSeriesStructureManager
            ->prepareEmptyStructure(
                (new \DateTime($this->filtersForm->dateStart))->setTimezone($timezone),
                (new \DateTime($this->filtersForm->dateEnd))->setTimezone($timezone),
                $periodType
            );

        $dataSeriesStructure = $this
            ->dataSeriesStructureManager
            ->fillAmounts($dataSeriesStructure, $dataSeries, $flatTree);

        $dataSeriesStructure = $this
            ->dataSeriesStructureManager
            ->fillUnits($dataSeriesStructure, $groupedUnits);

        $dataSeriesStructure = $this->dataSeriesStructureManager->removeTemporaryFields($dataSeriesStructure);

        $profitResult = new ProfitResult();
        $profitResult->dataSeries = $dataSeriesStructure;
        $profitResult->salesCategories = $salesCategoriesTree;

        $this->fillDynamicMetrics(
            $salesCategoryStrategy,
            $flatTree,
            $groupedUnits,
            $profitResult
        );
        $this->dataSanitizer->sanitizeProfitResult($profitResult);

        return $profitResult;
    }

    private function fillDynamicMetrics(
        SalesCategoryStrategyInterface $salesCategoryStrategy,
        array $flatTreeData,
        array $groupedUnits,
        ProfitResult $profitResult
    ): void
    {
        $productSalesAmount = $salesCategoryStrategy->getProductSalesAmountByFlatTreeData($flatTreeData);
        $PPCCostsAmount = $salesCategoryStrategy->getPPCAmountByFlatTreeData($flatTreeData);
        $netPurchasePriceAmount = $salesCategoryStrategy->getNetPurchasePriceAmountByFlatTreeData($flatTreeData);
        $amazonFeesAmount = $salesCategoryStrategy->getAmazonFeesAmountByFlatTreeData($flatTreeData);
        $revenueAmount = $salesCategoryStrategy->getRevenueAmountByFlatTreeData($flatTreeData);
        $expensesAmount = $salesCategoryStrategy->getExpensesAmountByFlatTreeData($flatTreeData);

        $revenueExpensesStrategy = new RevenueExpensesStrategy();
        $totalExpensesAmount = $revenueExpensesStrategy->getExpensesAmountByFlatTreeData($flatTreeData);
        $totalIncomeAmount = $revenueExpensesStrategy->getRevenueAmountByFlatTreeData($flatTreeData);

        $netProfit = $this->calculator->getNetProfit($revenueAmount, $expensesAmount);
        $margin = $this->calculator->getMargin($revenueAmount, $expensesAmount);
        $markup = $this->calculator->getMarkup($revenueAmount, $expensesAmount, $netPurchasePriceAmount);
        $estimatedPayout = $this->calculator->getEstimatedPayout($revenueAmount, $amazonFeesAmount);
        $ROI = $this->calculator->getROI($revenueAmount, $expensesAmount);
        $totalACOS = $this->calculator->getTotalACOS($productSalesAmount, $PPCCostsAmount);

        $profitResult->totalACOS = round_half_even($totalACOS, 2);
        $profitResult->roi = null === $ROI ? $ROI: round_half_even($ROI, 2);
        $profitResult->orderedProductSalesAmount = round_half_even($productSalesAmount, 2);
        $profitResult->revenueAmount = round_half_even($revenueAmount, 2);
        $profitResult->expensesAmount = round_half_even($expensesAmount, 2);;
        $profitResult->netProfit = round_half_even($netProfit, 2);
        $profitResult->margin = null === $margin ? $margin : round_half_even($margin, 2);
        $profitResult->markup = round_half_even($markup, 2);
        $profitResult->PPCCostsAmount = round_half_even($PPCCostsAmount, 2);
        $profitResult->totalExpensesAmount = round_half_even($totalExpensesAmount, 2);
        $profitResult->totalIncomeAmount = round_half_even($totalIncomeAmount, 2);
        //$profitResult->estimatedPayout = round_half_even($estimatedPayout, 2); FIX BAS-1758 TODO add in future

        foreach ($groupedUnits as $groupedUnit) {
            if ($groupedUnit['type'] === 'orders') {
                $profitResult->orders += $groupedUnit['amount'];
            }
        }
    }

    /**
     * Based on flat categories tree data and maximum depth, returns all possible categories.
     *
     * @param array $flatTree
     * @param int|null $maxDepth
     * @return array
     */
    public function getAllPossibleCategories(SalesCategoryStrategyInterface $salesCategoryStrategy, array $flatTree): array
    {
        $allPossibleCategories = [];

        $maxDepth = $salesCategoryStrategy->getDataSeriesDepth();

        // Collecting all possible categories even if there are no data
        foreach ($flatTree as $categoryInfo) {
            $pathComponents = explode(TreeStructureHelper::PATH_SEPARATOR, $categoryInfo['path']);

            $isRevenue = $salesCategoryStrategy->isRevenueCategories($categoryInfo);
            if (null === $maxDepth || $categoryInfo['depth'] <= $maxDepth) {
                $allPossibleCategories[end($pathComponents)] = [
                    'sortOrder' => $categoryInfo['sort_order'],
                    'isRevenue' => false !== $isRevenue
                ];
            } else {
                $allPossibleCategories[$pathComponents[$maxDepth]] = [
                    'sortOrder' => $categoryInfo['sort_order'],
                    'isRevenue' => false !== $isRevenue
                ];
            }
        }

        uasort($allPossibleCategories, function ($a, $b) {
            return  $a['sortOrder'] <=> $b['sortOrder'];
        });

        return $allPossibleCategories;
    }

    /**
     * Collapses data series to necessary depth level of sales categories.
     *
     * @param array $dataSeries
     * @param array $flatTree
     * @param int $maxDepth
     * @return array
     */
    protected function collapseDataSeries(array $dataSeries, array $flatTree, int $maxDepth = null): array
    {
        $collapsedDataSeries = [];
        foreach ($flatTree as $k => $v) {
            if (is_string($k)) {
                continue;
            }

            $flatTree[$v['id']] = $v;
            unset($flatTree[$k]);
        }

        foreach ($dataSeries as $dataSery) {
            if (empty($flatTree[$dataSery['sales_category_id']])) {
                continue;
            }

            $categoryInfo = $flatTree[$dataSery['sales_category_id']];
            $categoryId = $dataSery['sales_category_id'];

            if (null !== $maxDepth && $categoryInfo['depth'] > $maxDepth) {
                $categoryId = explode(TreeStructureHelper::PATH_SEPARATOR, $categoryInfo['path'])[$maxDepth];
            }
            $dataSery['sales_category_id'] = $categoryId;
            $uniqUeKey = implode('_', [$categoryId, $dataSery['date']]);

            if (empty($collapsedDataSeries[$uniqUeKey])) {
                $collapsedDataSeries[$uniqUeKey] = $dataSery;
                $collapsedDataSeries[$uniqUeKey]['sales_category_id'] = $categoryId;
                $collapsedDataSeries[$uniqUeKey]['amount'] = 0;
            }
            $collapsedDataSeries[$uniqUeKey]['amount'] += $dataSery['amount'];
        }

        return array_values($collapsedDataSeries);
    }

    private function getAmazonFeesByTree(array $flatTree): float
    {
        $valueAmazonFees = 0.00;
        foreach ($flatTree as $categoryInfo) {
            $isExpenses = false !== strpos($categoryInfo['path'], 'expenses|amazon_fees');
            if ($categoryInfo['name'] == 'Amazon fees' && $isExpenses) {
                $valueAmazonFees = (float) $categoryInfo['amount'];
                break;
            }
        }
        return $valueAmazonFees;
    }

    private function getNetPurchasePriceAmountByTree(array $flatTree): float
    {
        $valueNetPurchase = 0.00;
        foreach ($flatTree as $categoryInfo) {
            $isExpenses = false !== strpos($categoryInfo['path'], 'expenses|cost_of_goods');
            if ($categoryInfo['name'] == 'Net purchase price' && $isExpenses) {
                $valueNetPurchase = (float) $categoryInfo['amount'];
                break;
            }
        }
        return $valueNetPurchase;
    }
}
