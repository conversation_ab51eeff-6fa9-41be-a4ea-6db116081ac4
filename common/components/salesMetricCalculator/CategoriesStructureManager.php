<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesCategoryMapper\strategy\AbstractStrategy;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedView;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\Expression;

class CategoriesStructureManager
{
    public const TYPE_MONEY = 'money';
    public const TYPE_PERCENTS = 'pct';
    public const TYPE_COUNT = 'count';

    public function fillFlatTreeWithAmounts(array $flatTree, array $dataSeries): array
    {
        $excludeFromCalculationIds = SalesCategory::getExcludedFromCalculationIds();

        foreach ($flatTree as $salesCategoryId => $salesCategoryInfo) {
            $amount = 0;
            $countTransactions = 0;
            $flatTree[$salesCategoryId]['has_children'] = true;
            $flatTree[$salesCategoryId]['amount_own'] = 0;

            foreach ($dataSeries as $dataSery) {
                if ($dataSery['sales_category_id'] !== $salesCategoryId) {
                    continue;
                }
                $amount += $dataSery['amount'];
                $flatTree[$salesCategoryId]['amount_own'] += $dataSery['amount'];
                $flatTree[$salesCategoryId]['has_children'] = false;
                $countTransactions++;
            }

            // We have some categories which should be displayed as a child of some parent,
            // but their amount should not be included into parent's amount.
            $categoriesToFill = explode(TreeStructureHelper::PATH_SEPARATOR, $salesCategoryInfo['path']);
            $excludedCategories = array_intersect($categoriesToFill, $excludeFromCalculationIds) ?? [];
            $lastExcludedCategory = end($excludedCategories) ?? null;
            $categoriesWillNotBeFilled = [];

            if (!empty($lastExcludedCategory)) {
                $categoriesWillNotBeFilled = array_slice(
                    $categoriesToFill,
                    0,
                    array_search($lastExcludedCategory, $categoriesToFill)
                );
            }

            foreach ($categoriesToFill as $categoryToFillAmount) {
                $flatTree[$categoryToFillAmount]['amount'] = $flatTree[$categoryToFillAmount]['amount'] ?? 0;
                $flatTree[$categoryToFillAmount]['amount'] += !in_array($categoryToFillAmount, $categoriesWillNotBeFilled)
                    ? $amount
                    : 0;
                $flatTree[$categoryToFillAmount]['amount'] = round(
                    $flatTree[$categoryToFillAmount]['amount'],
                    4
                );
                $flatTree[$categoryToFillAmount]['type'] = self::TYPE_MONEY;
                $flatTree[$categoryToFillAmount]['count_transactions'] += $countTransactions;
            }
        }

        return $flatTree;
    }

    public function getSalesCategoriesFatTreeData(
        SalesCategoryStrategyInterface $salesCategoryStrategy,
        string $categoryId = null
    ): array
    {
        $categoryIds = $categoryId ? explode(TreeStructureHelper::PATH_SEPARATOR, $categoryId) : [];

        $whereCondition = [
            'and',
            ['type' => $salesCategoryStrategy->getType()],
            ['=', 'is_visible', true],
        ];

        if (!empty($categoryIds)) {
            $orBlock = ['or'];

            foreach ($categoryIds as $index => $catId) {
                $placeholderExact      = ":category_id_{$index}";
                $placeholderLike1      = ":category_like_1_{$index}";
                $placeholderLike2      = ":category_like_2_{$index}";
                $placeholderLike3      = ":category_like_3_{$index}";

                $orBlock[] = [
                    'or',
                    new Expression("path = {$placeholderExact}", [
                        $placeholderExact => $catId
                    ]),

                    new Expression("path LIKE {$placeholderLike1}", [
                        $placeholderLike1 => '%'
                            . TreeStructureHelper::PATH_SEPARATOR
                            . $catId
                            . TreeStructureHelper::PATH_SEPARATOR
                            . '%'
                    ]),

                    new Expression("path LIKE {$placeholderLike2}", [
                        $placeholderLike2 => $catId
                            . TreeStructureHelper::PATH_SEPARATOR
                            . '%'
                    ]),

                    new Expression("path LIKE {$placeholderLike3}", [
                        $placeholderLike3 => '%'
                            . TreeStructureHelper::PATH_SEPARATOR
                            . $catId
                    ])
                ];
            }

            $orBlock[] = [
                'or',
                ['depth' => 1]
            ];

            $whereCondition[] = $orBlock;
        }

        $flatTree = SalesCategory::find()
            ->select("id, name, path, sort_order, depth, color_hex, tags")
            ->addSelect(new Expression("'0' as count_transactions"))
            ->where($whereCondition)
            ->asArray()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG
                ]])
            )
            ->indexBy('id')
            ->all()
        ;

        foreach ($flatTree as $k => $v) {
            $v['is_default'] = true;
            $v['tags'] = $v['tags']
                ? explode(',', trim($v['tags'], '{}'))
                : [];
            $flatTree[$k] = $v;
        }

        $customCategories = ProductCostCategory::find()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        ProductCostCategory::COMMON_CACHE_TAG,
                    ]
                ])
            )
            ->asArray()
            ->all();

        $netPurchaseCategoryId = ProductCostCategory::getNetPurchaseId();

        foreach ($customCategories as $k => $customCategory) {
            $tagToSearchFor = null;

            switch ($customCategory['sales_category_id']) {
                case SalesCategory::CATEGORY_EXPENSES_COG:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_COST_OF_GOODS;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_TAXES:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_VAT;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_FBM_SHIPPING_COSTS;
                    break;
                case SalesCategory::CATEGORY_EXPENSES_OTHER_FEES:
                    $tagToSearchFor = SalesCategory::TAG_MANUAL_OTHER_FEES;
            }

            /** @var SalesCategory $parentSalesCategory */
            $parentSalesCategory = SalesCategory::findByTag(
                    $tagToSearchFor,
                    $salesCategoryStrategy->getType()
                )[0] ?? null;

            if (null === $parentSalesCategory) {
                continue;
            }

            $tags = $customCategory['id'] == $netPurchaseCategoryId
                ? [SalesCategory::TAG_MANUAL_NET_PURCHASE_PRICE]
                : null;

            $id = ProductCostCategory::INTERNAL_COG_PREFIX.$customCategory['id'];
            $flatTree[$id] = [
                'id' => $id,
                'path' => $parentSalesCategory->path.'|'.$id,
                'is_default' => !!$customCategory['is_default'],
                'name' => $customCategory['name'],
                'depth' => $parentSalesCategory->depth + 1,
                'color_hex' => SalesCategory::generateHexColor(),
                $parentSalesCategory,
                'sort_order' => $k,
                'count_transactions' => 0,
                'tags' => $tags,
            ];
        }

        $indirectCostTypes = IndirectCostType::find()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        IndirectCostType::COMMON_CACHE_TAG,
                    ]
                ])
            )
            ->asArray()
            ->orderBy('name ASC')
            ->all();

        foreach ($indirectCostTypes as $k => $indirectCostType) {
            /** @var SalesCategory $parentSalesCategory */
            $parentSalesCategory = SalesCategory::findByTag(
                    SalesCategory::TAG_MANUAL_INDIRECT_COSTS,
                    $salesCategoryStrategy->getType()
                )[0] ?? null;

            $id = ProductCostCategory::INTERNAL_INDIRECT_COST_PREFIX.$indirectCostType['id'];
            $flatTree[$id] = [
                'id' => $id,
                'path' => $parentSalesCategory->path.'|'.$id,
                'is_default' => !!$indirectCostType['is_predefined'],
                'name' => $indirectCostType['name'],
                'depth' => $parentSalesCategory->depth + 1,
                'color_hex' => SalesCategory::generateHexColor(),
                'sort_order' => $k,
                'count_transactions' => 0,
                'tags' => []
            ];
        }

        return $flatTree;
    }
}
