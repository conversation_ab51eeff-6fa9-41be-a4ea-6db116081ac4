<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\dto\AcosResult;
use common\models\customer\clickhouse\AdsStatistics;
use common\models\customer\clickhouse\AdsStatisticsExtendedView;
use common\models\customer\clickhouse\AdsStatisticsView;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\TransactionExtendedView;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\widget\TransactionQueryExecutor;
use common\models\FinanceEventCategory;
use yii\db\Expression;
use yii\db\Query;

class ACoSCalculator
{
    use ExtraFiltersTrait;

    protected CustomerComponent $customerComponent;
    protected DataSanitizer $dataSanitizer;
    private DbManager $dbManager;

    public function __construct()
    {
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->dataSanitizer = new DataSanitizer();
        /** @var DbManager $dbManager */
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function calc(FiltersForm $filtersForm): AcosResult
    {
        $acosResult = new AcosResult();

        try {
            $params = [
                ':currency_id' => $filtersForm->currencyId,
            ];

            $query = $this->buildQuery(
                $filtersForm,
                $params
            );

            if (empty($query)) {
                return $acosResult;
            }

            $unionQuery = (new Query())
                ->select([
                    '(SUM(cost)/SUM(sales)) * 100 as acos',
                ]);

            $newUnionParts[] = $query->createCommand()->getRawSql();

            $unionQuery->from(['(' . implode(' UNION ALL ', $newUnionParts) . ')']);

            $clickhouseDBConnection = $this->dbManager->getClickhouseCustomerDb();;
            $amounts = $unionQuery->all($clickhouseDBConnection);
            if(!empty($amounts)) {
                $acosResult->acos = (float)$amounts[0]['acos'];
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return $acosResult;
    }

    protected function buildQuery(
        FiltersForm $filtersForm,
        array &$params
    ): ?Query
    {
        $tableName = AdsStatisticsExtendedView::tableName();
        $timezone = $this->customerComponent->getUserTimezoneName();
        $dateColumnName = $filtersForm->isTransactionDateMode ? 'date' : 'date';

        $query = (new Query())
            ->from($tableName);

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $filtersForm->marketplaceSellerIds,
            $filtersForm->marketplaceId,
            $filtersForm->sellerId,
        );

        $this->applyExpiredOrInactiveSubscriptionLogic($query, 'seller_id');

        //$this->applyAmazonAdAccountsLogic($query);

        $this->applyPostedDateTransactionDateFilter($query, $filtersForm, $dateColumnName, $dateColumnName, true);

        (new TransactionQueryExecutor())->applyFiltersByQueryBuilder($query, $filtersForm);

        return $query;
    }
}
