<?php

namespace common\components\salesMetricCalculator;

class Calculator
{
    public function getNetProfit(float $revenueAmount, float $expensesAmount): float
    {
        return abs($revenueAmount) - abs($expensesAmount);
    }

    public function getROI(float $revenueAmount, float $expensesAmount): ?float
    {
        $expensesAmount = abs($expensesAmount);
        $revenueAmount = abs($revenueAmount);
        if ((int)$expensesAmount === 0) {
            return null;
        }
        $netProfit = $this->getNetProfit($revenueAmount, $expensesAmount);
        return ($netProfit / $expensesAmount) * 100;
    }

    public function getMargin(float $revenueAmount, float $expensesAmount): ?float
    {
        $expensesAmount = abs($expensesAmount);
        $revenueAmount = abs($revenueAmount);
        if ((int)$revenueAmount === 0) {
            return null;
        }
        $netProfit = $this->getNetProfit($revenueAmount, $expensesAmount);
        $margin = ($netProfit / $revenueAmount) * 100;

        return max(-100, min(100, $margin)); // from -100 to 100
    }

    public function getEstimatedPayout(float $revenueAmount, float $amazonFees):  float
    {
        $amazonFees = abs($amazonFees);
        $revenueAmount = abs($revenueAmount);
        if ((int)$revenueAmount === 0) {
            return 0;
        }
        return $revenueAmount - $amazonFees;
    }

    public function getMarkup(float $revenueAmount, float $expensesAmount, float $netPurchasePrice):  float
    {
        $expensesAmount = abs($expensesAmount);
        $revenueAmount = abs($revenueAmount);
        $netPurchasePrice = abs($netPurchasePrice);
        if ((int)$revenueAmount === 0) {
            return 0;
        }

        if ((int)$netPurchasePrice === 0) {
            return 0;
        }
        $netProfit = $this->getNetProfit($revenueAmount, $expensesAmount);
        return ($netProfit / $netPurchasePrice) * 100;
    }

    public function getTotalACOS(float $totalSales, float $PPCCostsAmount): float
    {
        if (empty($totalSales)) {
            return 0;
        }

        return (abs($PPCCostsAmount) / abs($totalSales)) * 100;
    }
}
