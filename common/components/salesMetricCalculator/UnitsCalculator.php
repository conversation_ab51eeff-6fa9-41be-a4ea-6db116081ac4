<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\db\ClickhouseDbHelper;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\salesMetricCalculator\dto\ProfitResult;
use common\components\salesMetricCalculator\dto\UnitsResult;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\TransactionExtendedViewV1;
use common\models\FinanceEventCategory;
use Exception;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Expression;
use yii\db\Query;

/**
 * Calculates and returns unit metrics.
 *
 * @package common\components\ProfitCalcualtor
 */
class UnitsCalculator
{
    use ExtraFiltersTrait;

    /**
     * Caching per one API call to prevent duplicate queries execution.
     * Should be very short.
     */
    protected const ONE_API_CALL_CACHE_TIME_S = 15;

    private FiltersForm $filtersForm;
    private ClickhouseDbHelper $clickhouseDbHelper;
    private DbManager $dbManager;
    private CustomerComponent $customerComponent;
    private TransactionQueryExecutor $transactionQuery;
    private SalesCategoryStrategyInterface $salesCategoryStrategy;
    private DataSanitizer $dataSanitizer;
    private DataSeriesRetriever $dataSeriesRetriever;

    public function __construct(FiltersForm $filtersForm)
    {
        $this->filtersForm = $filtersForm;

        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $this->salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($this->filtersForm->salesCategoryStrategy ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY);

        $this->clickhouseDbHelper = new ClickhouseDbHelper();
        /** @var DbManager $dbManager */
        $this->dbManager = \Yii::$app->dbManager;
        /** @var CustomerComponent $customerComponent */
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->transactionQuery = new TransactionQueryExecutor();
        $this->dataSanitizer = new DataSanitizer();
        $this->dataSeriesRetriever = new DataSeriesRetriever($filtersForm);
    }

    /**
     * Calculates and returns unit metrics.
     *
     * @return ProfitResult
     * @throws \yii\db\Exception
     */
    public function calc(): UnitsResult
    {
        $unitsResult = new UnitsResult();
        $unitsByDate = $this->getUnitsGroupedByDate(DataSeriesStructureManager::PERIOD_TYPE_ALL);

        foreach ($unitsByDate as $unit) {
            if (isset($unitsResult->{$unit['type']})) {
                $unitsResult->{$unit['type']} += $unit['amount'];
            }
        }

        $this->dataSanitizer->sanitizeUnitsResult($unitsResult);

        return $unitsResult;
    }

    public function getUnitsGroupedByDateBasedOnOrders(string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY): array
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $query = $this->salesCategoryStrategy->getAmazonOrderExtendedViewQuery();
        $queryInProgress = $this->salesCategoryStrategy->getAmazonOrderInProgressExtendedViewQuery();

        $query1 = $query
            ->select([
                new Expression('order_id'),
                new Expression('any(quantity) as quantity'),
                new Expression('order_purchase_date'),
                new Expression('any(promotion_amount_eur) as promotion_discount'),
                new Expression('any(order_status) as order_status')
            ])
            ->groupBy('order_item_id, order_purchase_date, order_id')
        ;
        $dateStart = (new \DateTime($this->filtersForm->dateStart));
        $dateEnd = (new \DateTime($this->filtersForm->dateEnd));

        $query1->andWhere([
            'AND',
            ['>=', 'order_purchase_date', $dateStart->format('Y-m-d H:i:s')],
            ['<=', 'order_purchase_date', $dateEnd->format('Y-m-d H:i:s')]
        ]);

        $this->applyMarketplaceSellerGroupsFilter(
            $query1,
            $this->filtersForm->marketplaceSellerIds,
            $this->filtersForm->marketplaceId,
            $this->filtersForm->sellerId,
        );
        $this->applyExpiredOrInactiveSubscriptionLogic(
            $query1,
            'seller_id'
        );
        (new TransactionQueryExecutor())->applyFiltersByQueryBuilder($query1, $this->filtersForm, 0, 'order_id');

        $query2 = clone $query1;
        $query2->from($queryInProgress);
        $this->applyClickhouseLatestVersion($query2);

        // Preventing overlapping data between these two tables
        $query1->andWhere([
            'not in', 'order_id', (clone $query2)->select('order_id')
        ]);

        $amazonOrderQuery = (new Query())
            ->from($query1->union($query2, true));

        $dateGroupFunctionSyntax = $this
            ->dataSeriesRetriever
            ->getDateGroupFunctionSyntax('order_purchase_date', $periodType);

        $ordersQuery = clone $amazonOrderQuery;
        $ordersQuery->select([
                "toInt64(count(order_id)) as amount",
                new Expression("'orders' as type"),
                "$dateGroupFunctionSyntax as date"
            ])
            ->where(['!=', 'order_status', Order::ORDER_STATUS_CANCELED])
            ->groupBy('date');

        $ordersCanceledQuery = clone $amazonOrderQuery;
        $ordersCanceledQuery->select([
                "toInt64(count(order_id)) as amount",
                new Expression("'ordersCanceled' as type"),
                "$dateGroupFunctionSyntax as date"
            ])
            ->where(['=', 'order_status', Order::ORDER_STATUS_CANCELED])
            ->groupBy('date');

        $unitsQuery = clone $amazonOrderQuery;
        $unitsQuery->select([
                "toInt64(sum(quantity)) as amount",
                new Expression("'units' as type"),
                "$dateGroupFunctionSyntax as date"
            ])
            ->groupBy('date');

        $promoQuery = clone $amazonOrderQuery;
        $promoQuery->select([
                "toInt64(count(order_id)) as amount",
                new Expression("'promo' as type"),
                "$dateGroupFunctionSyntax as date"
            ])
            ->where(['!=', 'order_status', Order::ORDER_STATUS_CANCELED])
            ->andWhere(['<', 'promotion_discount', 0])
            ->groupBy('date');

        $commonQuery = (new Query())
            ->from(
                $ordersQuery
                    ->union($ordersCanceledQuery, true)
                    ->union($unitsQuery, true)
                    ->union($promoQuery, true)
            );

        return $commonQuery->cache(self::ONE_API_CALL_CACHE_TIME_S)->all($dbManager->getClickhouseCustomerDb());
    }

    /**
     * @throws Exception
     */
    public function getUnitsGroupedByDate(string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY): array
    {
        $basedOnTransactions = $this->getUnitsGroupedByDateBasedOnTransactions($periodType);
        $basedOnOrders = $this->getUnitsGroupedByDateBasedOnOrders($periodType);
        $allUnits = array_merge($basedOnTransactions, $basedOnOrders);

        return $this->dataSanitizer->sanitizeGroupedUnitsResult($allUnits);
    }

    /**
     * @throws Exception
     */
    public function getUnitsGroupedByDateBasedOnTransactions(string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY): array
    {
        $organicRefundsCategoryId = FinanceEventCategory::getOrganicRefundId();

        $unionParts = [];
        $isTransactionDateMode = $this->filtersForm->isTransactionDateMode;

        try {
            $dateGroupFunctionSyntax = $this
                ->dataSeriesRetriever
                ->getDateGroupFunctionSyntax('transaction_date', $periodType);

            $this->filtersForm->isTransactionDateMode = true;

            if (!empty($organicRefundsCategoryId)) {
                $nonTransactionOrderQuery = TransactionExtendedViewV1::find()
                    ->select([
                        'amount' => new Expression("Distinct(toInt64(sum(quantity)))"),
                        'date' => new Expression("$dateGroupFunctionSyntax"),
                        'type' => new Expression("'refunds'")
                    ])
                    ->groupBy('date')
                ;

                $sellerIdColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['seller_id'][0];
                $marketplaceIdColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['marketplace_id'][0];
                $postedDateColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['posted_date'][0];
                $transactionDateColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['transaction_date'][0];
                $this->applyPostedDateTransactionDateFilter(
                    $nonTransactionOrderQuery,
                    $this->filtersForm,
                    $postedDateColumnName,
                    $transactionDateColumnName,
                    true
                );
                $this->applyMarketplaceSellerGroupsFilter(
                    $nonTransactionOrderQuery,
                    $this->filtersForm->marketplaceSellerIds,
                    $this->filtersForm->marketplaceId,
                    $this->filtersForm->sellerId,
                    $marketplaceIdColumnName,
                    $sellerIdColumnName
                );
                $this->applyExpiredOrInactiveSubscriptionLogic($nonTransactionOrderQuery, $sellerIdColumnName);
                (new TransactionQueryExecutor())->applyFiltersByQueryBuilder($nonTransactionOrderQuery, $this->filtersForm);

                $nonTransactionOrderQuery
                    ->andWhere(['category_id' => $organicRefundsCategoryId])
                    ->andWhere(['cog_category_id' => 0]);

                $unionParts[] = $nonTransactionOrderQuery->createCommand()->getRawSql();
            }

            if (count($unionParts) === 0) {
                return [];
            }

            $query = (new Query())
                ->select([
                    'date',
                    'type',
                    'amount'
                ])
                ->from(['(' . implode(' UNION ALL ', $unionParts) . ')']);;

            $clickhouseDBConnection = $this->dbManager->getClickhouseCustomerDb();;
            $groupedUnits = $query->cache(self::ONE_API_CALL_CACHE_TIME_S)->all($clickhouseDBConnection);
            $this->filtersForm->isTransactionDateMode = $isTransactionDateMode;
        } catch (Exception $e) {
            $this->filtersForm->isTransactionDateMode = $isTransactionDateMode;
            throw $e;
        }

        return $groupedUnits;
    }
}
