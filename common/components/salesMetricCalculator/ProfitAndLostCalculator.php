<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\dto\widgetTable\Cell;
use common\components\salesMetricCalculator\dto\widgetTable\Column;
use common\components\salesMetricCalculator\dto\widgetTable\PivotTable;
use common\components\salesMetricCalculator\dto\widgetTable\Row;
use common\components\salesMetricCalculator\dto\ProfitResult;
use yii\db\Exception;

class ProfitAndLostCalculator
{
    private FiltersForm $filtersForm;

    public function __construct(FiltersForm $filtersForm)
    {
        $this->filtersForm = $filtersForm;
    }

    /**
     * Calculates and returns profit and lost PIVOT table data.
     *
     * @param string $periodType
     * @return PivotTable
     * @throws Exception
     */
    public function calc(string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY): PivotTable
    {
        $profitCalculator = new ProfitCalculator($this->filtersForm);
        $profitResult = $profitCalculator->calc($periodType, 1);

        $pivotTable = new PivotTable();
        $this->generateColumns(array_keys($profitResult->dataSeries), $pivotTable);

        $salesCategories = array_merge($profitResult->salesCategories, [
            [
                'id' => 'estimated_margin_amount',
                'name' => \Yii::t('admin', 'Estimated margin'),
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ],
            [
                'id' => 'estimated_margin_pct',
                'name' => \Yii::t('admin', 'Estimated margin %'),
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'revenue_amount',
                'name' => \Yii::t('admin','Revenue'),
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'total_income_amount',
                'name' => \Yii::t('admin','Total income'),
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'total_expenses_amount',
                'name' => \Yii::t('admin','Total expenses'),
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'roi',
                'name' => \Yii::t('admin','ROI'),
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'markup',
                'name' => \Yii::t('admin','Markup'),
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ], [
                'id' => 'total_acos',
                'name' => \Yii::t('admin','Total ACoS'),
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'count_transactions' => 1,
                'is_default' => 0,
                'children' => []
            ]
        ]);

        $this->generateRows($salesCategories, $pivotTable);
        $this->fillAmounts($pivotTable, $profitResult);

        return $pivotTable;
    }

    protected function fillAmounts(PivotTable $pivotTable, ProfitResult $profitResult): void
    {
        // Map row id to parameter from profit result stricture
        $rowIdToProfitResultColumnMap = [
            'estimated_margin_amount' => 'netProfit',
            'estimated_margin_pct' => 'margin',
            'revenue_amount' => 'revenueAmount',
            'total_income_amount' => 'totalIncomeAmount',
            'total_expenses_amount' => 'totalExpensesAmount',
            'roi' => 'roi',
            'markup' => 'markup',
            'total_acos' => 'totalACOS',
        ];

        // Map row id to parameter from data sery stricture
        $rowIdToDataSeryColumnMap = $rowIdToProfitResultColumnMap;
        $rowIdToDataSeryColumnMap['estimated_margin_amount'] = 'estimatedProfit';
        $rowIdToDataSeryColumnMap['revenue_amount'] = 'revenue';

        foreach ($rowIdToProfitResultColumnMap as $rowId => $profitResultColumnName) {
            // Labels for first column cells (parameter name)
            $cell = $pivotTable->getCell($rowId, 'parameter');
            $cell->type = CategoriesStructureManager::TYPE_TEXT;
            $cell->value = $pivotTable->getRow($cell->rowId)->name;

            // Total
            $cell = $pivotTable->getCell($rowId, 'total');
            $cell->value = $profitResult->{$profitResultColumnName};
        }

        foreach ($profitResult->dataSeries as $dataSery) {
            // Sales categories cells and their totals
            foreach ($dataSery['salesCategories'] as $salesCategoryId => $amount) {
                try {
                    $cell = $pivotTable->getCell($salesCategoryId, $dataSery['date']);
                    $cell->type = CategoriesStructureManager::TYPE_MONEY;
                    $cell->value = $amount;

                    $row = $pivotTable->getRow($cell->rowId);

                    $parameterCell = $pivotTable->getCell($salesCategoryId, 'parameter');
                    $parameterCell->type = Cell::TYPE_TEXT;
                    $parameterCell->value = $row->name;

                    $totalCell = $pivotTable->getCell($salesCategoryId, 'total');
                    $totalCell->value += $amount;
                } catch (\Throwable $e) {
                }
            }

            // Dynamic formulas cells and their totals
            foreach ($rowIdToDataSeryColumnMap as $rowId => $dataSeryColumnName) {
                try {
                    $cell = $pivotTable->getCell($rowId, $dataSery['date']);
                    $cell->value = $dataSery[$dataSeryColumnName];
                } catch (\Throwable $e) {
                }
            }
        }
    }

    protected function generateColumns(array $periods, PivotTable $pivotTable): void
    {
        $column = new Column();
        $column->id = 'parameter';
        $column->name = \Yii::t('admin', 'Parameter');
        $pivotTable->addColumn($column);

        foreach ($periods as $period) {
            $column = new Column();
            $column->id = $period;
            $column->name = $period;
            $pivotTable->addColumn($column);
        }

        $column = new Column();
        $column->id = 'total';
        $column->name = \Yii::t('admin','Total');
        $pivotTable->addColumn($column);
    }

    protected function generateRows(array $salesCategoriesTree, PivotTable $pivotTable, Row $parentRow = null): void
    {
        foreach ($salesCategoriesTree as $treeData) {
            if ($treeData['count_transactions'] <= 0) {
                continue;
            }

            $row = new Row();
            $row->id = $treeData['id'];

            // Translate only for predefined categories
            if ($treeData['is_default']) {
                $row->name = \Yii::t('admin', $treeData['name']);
            } else {
                $row->name = $treeData['name'];
            }

            if (!empty($treeData['children'])) {
                $this->generateRows($treeData['children'], $pivotTable,  $row);
            }

            $cellType = $treeData['type'] ?? Cell::TYPE_TEXT;
            $pivotTable->addRow($row, $parentRow);
        }
    }
}
