<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\amazonAds\CostsApplier;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\PpcCostsLastFewDaysTransaction;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedView;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

/**
 * Responsible for retrieving data series using filters form.
 */
class DataSeriesRetriever
{
    use ExtraFiltersTrait;

    /**
     * Caching per one API call or per 1 load of page, to prevent duplicate queries execution.
     * Should be very short.
     */
    protected const ONE_API_CALL_CACHE_TIME_S = 15;

    private FiltersForm $filtersForm;
    private CustomerComponent $customerComponent;
    private DbManager $dbManager;
    private TransactionQueryExecutor $transactionQuery;
    private SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct(FiltersForm $filtersForm)
    {
        $this->filtersForm = $filtersForm;
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->dbManager = \Yii::$app->dbManager;
        $this->transactionQuery = new TransactionQueryExecutor();
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
    }

    /**
     * Returns array of sales history chart data series
     *
     * @param int $depth
     * @param string $periodType
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getDataSeries(
        SalesCategoryStrategyInterface $salesCategoryStrategy,
        int $depth = 0,
        string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY
    ): array
    {
        $orderBasedCategoryIds = OrderBasedTransaction::find()
            ->select('CategoryId')
            ->distinct()
            ->cache(self::ONE_API_CALL_CACHE_TIME_S)
            ->column();
        $nonTransactionOrderQuery = OrderBasedTransaction::find()
            ->select([
                new Expression('DISTINCT AmazonOrderId')
            ])
            ->orderBy('PostedDate DESC')
            ->cache(self::ONE_API_CALL_CACHE_TIME_S)
        ;

        if (!empty($this->filtersForm->dateStart) && !empty($this->filtersForm->dateEnd)) {
            $nonTransactionOrderQuery->andWhere([
                'between',
                'PostedDate',
                $this->filtersForm->dateStart,
                $this->filtersForm->dateEnd
            ]);
        }

        $transactionQuery = $salesCategoryStrategy->getTransactionExtendedViewQuery();

        $query1 = $this->prepareBaseQuery($transactionQuery, $salesCategoryStrategy, $depth, $periodType);
        $query2 = $this->prepareBaseQuery(OrderBasedTransaction::find(), $salesCategoryStrategy, $depth, $periodType, 1);
        $query3 = $this->prepareBaseQuery(PpcCostsLastFewDaysTransaction::find(), $salesCategoryStrategy, $depth, $periodType, 1);

        $amazonAdCategoryIds = FinanceEventCategory::getAmazonAdIds();
        $ppcLastFewDaysMinDate = PpcCostsLastFewDaysTransaction::find()
            ->select('min(PostedDate)')
            ->cache(self::ONE_API_CALL_CACHE_TIME_S)
            ->scalar();
        if ($ppcLastFewDaysMinDate === '1970-01-01 00:00:00') {
            $ppcLastFewDaysMinDate = date('Y-m-d H:i:s');
        }

        $query1->andWhere([
            'AND',
            [
                'OR',
                ['NOT IN', 'amazon_order_id', $nonTransactionOrderQuery],
                ['NOT IN', 'category_id', $orderBasedCategoryIds]
            ],
            [
                'OR',
                ['NOT IN', 'category_id', $amazonAdCategoryIds],
                ['<', 'posted_date', $ppcLastFewDaysMinDate]
            ]
        ]);

        $query2->andWhere(['IN', 'AmazonOrderId', $nonTransactionOrderQuery]);

        $ppcLastFewDaysMaxDate = (new \DateTime())
            ->modify("-" . CostsApplier::DATA_STABILISATION_TIME_DAYS . " days")
            ->format('Y-m-d');

        $query3->andWhere(['>', 'PostedDate', $ppcLastFewDaysMaxDate]);

        $query1Sql = $query1->createCommand()->getRawSql();
        $query2Sql = $query2->createCommand()->getRawSql();
        $query3Sql = $query3->createCommand()->getRawSql();

        $fullSqlQuery = "
            ({$query1Sql}) 
            UNION ALL 
            ({$query2Sql}) 
            UNION ALL 
            ({$query3Sql})
        ";

        $query = (new Query())
            ->select([
                'date',
                'sales_category_id',
                'category_id',
                'SUM(amount) as amount',
                'SUM(income) as total_income',
                'SUM(expenses) as total_expenses',
            ])
            ->from(['(' . $fullSqlQuery . ')'])
            ->groupBy(['date', 'sales_category_id', 'category_id']);

        $clickhouseDBConnection = $this->dbManager->getClickhouseCustomerDb();;

        $response = $query->cache(self::ONE_API_CALL_CACHE_TIME_S)->all($clickhouseDBConnection);

        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $internalCOGPrefix = ProductCostCategory::INTERNAL_COG_PREFIX;

        foreach ($response as $k => $item) {
            if (str_contains((string)$item['sales_category_id'], $internalCOGPrefix)
                && (int)$item['category_id'] == $organicRefundCategoryId
            ) {
                $response[$k]['sales_category_id'] .= '_PLUS';
            }
        }

        return $response;
    }

    /**
     * @throws \Exception
     */
    public function prepareBaseQuery(
        ActiveQuery $query,
        SalesCategoryStrategyInterface $salesCategoryStrategy,
        int $depth,
        string $periodType,
        int $isCamelCase = 0,
        bool $splitAmount = false
    ): ActiveQuery
    {
        $salesCategoryMap = FinanceEventCategory::getSalesCategoriesMap($salesCategoryStrategy, $depth);
        $categoriesWhenThenMapping = [];

        foreach ($salesCategoryMap as $salesCategoryId => $financeEventCategoryIds) {
            $categoriesWhenThenMapping[] = "WHEN {$this->transactionQuery::COLUMN_NAME_MAPPING['category_id'][$isCamelCase]} IN(" . implode(',', $financeEventCategoryIds) . ") THEN '$salesCategoryId'";
        }

        $dateColumnName = $this->transactionQuery::COLUMN_NAME_MAPPING['posted_date'][$isCamelCase];
        if ($this->filtersForm->isTransactionDateMode) {
            $dateColumnName = $this->transactionQuery::COLUMN_NAME_MAPPING['transaction_date'][$isCamelCase];
        }

        $timezone = $this->customerComponent->getUserTimezoneName();
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        $categoriesWhenThenMapping = implode(" ", $categoriesWhenThenMapping);
        $dateGroupFunctionSyntax = $this->getDateGroupFunctionSyntax($dateColumnName, $periodType);

        $internalCOGPrefix = ProductCostCategory::INTERNAL_COG_PREFIX;
        $internalIndirectCostPrefix = ProductCostCategory::INTERNAL_INDIRECT_COST_PREFIX;
        $organicSalesId = FinanceEventCategory::getOrganicSalesId();

        $select = [
            'sales_category_id' => new Expression("case 
                when {$this->transactionQuery::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase]} > 0
                    then concat('{$internalCOGPrefix}', cast({$this->transactionQuery::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase]} AS char))
                when {$this->transactionQuery::COLUMN_NAME_MAPPING['indirect_cost_type_id'][$isCamelCase]} > 0
                    then concat('{$internalIndirectCostPrefix}', cast({$this->transactionQuery::COLUMN_NAME_MAPPING['indirect_cost_type_id'][$isCamelCase]} AS char))
                else
                    case
                        {$categoriesWhenThenMapping}
                    end
                end"),
            "if (
                {$this->transactionQuery::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase]} > 0,
                {$organicSalesId},
                {$this->transactionQuery::COLUMN_NAME_MAPPING['category_id'][$isCamelCase]}
            ) as category_id",
            'date' => new Expression($dateGroupFunctionSyntax),
            'amount' => new Expression("floor(
                sum(
                    {$this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase]} 
                    / $moneyAccuracy
                ) 
                * 
                dictGetOrNull(
                    default.currency_rate_dict, 
                    'value', 
                    tuple(toDate({$dateColumnName}, '$timezone'
                ), :currency_id)), 
                4
            )"),
            'income' => new Expression("floor(
                sumIf(
                    {$this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase]},
                    {$this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase]} > 0
                )
                / $moneyAccuracy
                * dictGetOrNull(
                    default.currency_rate_dict,
                    'value',
                    tuple(toDate({$dateColumnName}, '$timezone'), :currency_id)
                ),
                4
            )"),
            'expenses' => new Expression("floor(
                sumIf(
                    {$this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase]},
                    {$this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase]} < 0
                )
                / $moneyAccuracy
                * dictGetOrNull(
                    default.currency_rate_dict,
                    'value',
                    tuple(toDate({$dateColumnName}, '$timezone'), :currency_id)
                ),
                4
            )"),
        ];

        $baseQuery = $query->select($select);

        $sellerIdColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['seller_id'][$isCamelCase];
        $marketplaceIdColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['marketplace_id'][$isCamelCase];
        $postedDateColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['posted_date'][$isCamelCase];
        $transactionDateColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['transaction_date'][$isCamelCase];
        $categoryIdColumnName = TransactionQueryExecutor::COLUMN_NAME_MAPPING['category_id'][$isCamelCase];

        $this->applyPostedDateTransactionDateFilter(
            $baseQuery,
            $this->filtersForm,
            $postedDateColumnName,
            $transactionDateColumnName
        );
        $this->applyMarketplaceSellerGroupsFilter(
            $baseQuery,
            $this->filtersForm->marketplaceSellerIds,
            $this->filtersForm->marketplaceId,
            $this->filtersForm->sellerId,
            $marketplaceIdColumnName,
            $sellerIdColumnName
        );

        $this->applyAmazonAdAccountsLogic($baseQuery, $categoryIdColumnName, $marketplaceIdColumnName, $sellerIdColumnName);
        $this->applyExpiredOrInactiveSubscriptionLogic($baseQuery, $sellerIdColumnName);

        $baseQuery
            ->andWhere([
                'OR',
                ['!=', $this->transactionQuery::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase], 0],
                [
                    'AND',
                    [$this->transactionQuery::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase] => 0],
                    [$this->transactionQuery::COLUMN_NAME_MAPPING['indirect_cost_type_id'][$isCamelCase] => 0]
                ]
            ])
            ->groupBy([
                'date',
                new Expression("toDate({$dateColumnName}, '$timezone')"),
                'sales_category_id',
                'category_id'
            ])
            ->addParams([
                ':currency_id' => $this->filtersForm->currencyId,
            ])
            ->orderBy('date');

        $this->transactionQuery->applyFiltersByQueryBuilder($baseQuery, $this->filtersForm, $isCamelCase);

        return $baseQuery;
    }

    public function getDateGroupFunctionSyntax(string $dateColumnName, string $periodType): string
    {
        $timezone = $this->customerComponent->getUserTimezoneName();
        $dateGroupFunctionSyntax = "formatDateTime(toDateTime($dateColumnName, '$timezone'), '%F')";

        if ($periodType === DataSeriesStructureManager::PERIOD_TYPE_WEEK) {
            $dateGroupFunctionSyntax = "formatDateTime(toStartOfWeek({$dateColumnName}, 1, '$timezone'), '%F')";
        } elseif ($periodType === DataSeriesStructureManager::PERIOD_TYPE_MONTH) {
            $dateGroupFunctionSyntax = "formatDateTime(toStartOfMonth({$dateColumnName}, '$timezone'), '%F')";
        } elseif ($periodType === DataSeriesStructureManager::PERIOD_TYPE_YEAR) {
            $dateGroupFunctionSyntax = "formatDateTime(toStartOfYear({$dateColumnName}, '$timezone'), '%F')";
        } elseif ($periodType === DataSeriesStructureManager::PERIOD_TYPE_ALL) {
            $dateGroupFunctionSyntax = "formatDateTime(toDate('1900-01-01'), '%F')";
        } elseif ($periodType === DataSeriesStructureManager::PERIOD_TYPE_HOUR) {
            $dateGroupFunctionSyntax = "formatDateTime(toStartOfHour({$dateColumnName}, '$timezone'), '%F %T')";
        }

        return $dateGroupFunctionSyntax;
    }
}
