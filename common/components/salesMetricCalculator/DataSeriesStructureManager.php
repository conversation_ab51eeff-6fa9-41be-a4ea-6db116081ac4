<?php

namespace common\components\salesMetricCalculator;

use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\SalesCategory;

class DataSeriesStructureManager
{
    public const PERIOD_TYPE_DAY = 'day';
    public const PERIOD_TYPE_WEEK = 'week';
    public const PERIOD_TYPE_MONTH = 'month';
    public const PERIOD_TYPE_YEAR = 'year';
    public const PERIOD_TYPE_ALL = 'all';

    public const PERIOD_TYPES = [
        self::PERIOD_TYPE_DAY,
        self::PERIOD_TYPE_WEEK,
        self::PERIOD_TYPE_MONTH,
        self::PERIOD_TYPE_YEAR
    ];
    private Calculator $calculator;
    private array $allPossibleUnits = [];
    private array $allPossibleCategories = [];

    public function __construct()
    {
        $this->calculator = new Calculator();
    }

    public function setAllPossibleCategories(array $allPossibleCategories): self
    {
        foreach ($allPossibleCategories as $categoryName => $data) {
            $this->allPossibleCategories[$categoryName] = 0;
        }

        return $this;
    }

    public function setAllPossibleUnits(array $allPossibleUnits): self
    {
        if (isset($allPossibleUnits[0])) {
            $allPossibleUnits = array_flip($allPossibleUnits);
        }
        array_walk($allPossibleUnits, function (&$v) {
            $v = 0;
        });
        $this->allPossibleUnits = $allPossibleUnits;
        return $this;
    }

    /**
     * Prepares empty data series structure grouped by dates
     *
     * @return array
     * @throws \Exception
     */
    public function prepareEmptyStructure(
        \DateTime $dateStart,
        \DateTime $dateEnd,
        string $periodType = self::PERIOD_TYPE_DAY
    ): array {
        if (!in_array($periodType, self::PERIOD_TYPES)) {
            $periodType = self::PERIOD_TYPE_DAY;
        }

        $filtersDateEnd = (clone $dateEnd)->setTime(23, 59, 59);
        $filtersDateStart = clone $dateStart->setTime(0, 0, 0);
        $dataSeriesStructure = [];
        $dateStart = clone $dateStart;

        if ($periodType === self::PERIOD_TYPE_YEAR) {
            $dateStart->modify('first day of january this year');
        } elseif ($periodType === self::PERIOD_TYPE_MONTH)  {
            $dateStart->modify('first day of this month');
        } elseif ($periodType === self::PERIOD_TYPE_WEEK) {
            $dateStart->modify('monday this week');
        }

        while (true) {
            $dateEnd = clone $dateStart;
            $dateEnd
                ->modify("+1 {$periodType}s")
                ->modify("-1 day")
                ->setTime(23,59,59);

            if ($dateStart < $filtersDateStart) {
                $dateStartFormatted = $filtersDateStart->format('Y-m-d');
            } else {
                $dateStartFormatted = $dateStart->format('Y-m-d');
            }

            if ($dateEnd > $filtersDateEnd) {
                $dateEndFormatted = $filtersDateEnd->format('Y-m-d');
            } else {
                $dateEndFormatted = $dateEnd->format('Y-m-d');
            }

            $dataSeriesStructure[$dateStartFormatted] = [
                'date' => $dateStartFormatted,
                'dateTimeStart' => $dateStart,
                'dateTimeEnd' => $dateEnd,
                'dateStart' => $dateStartFormatted,
                'dateEnd' => $dateEndFormatted,
                'salesCategories' => $this->allPossibleCategories,
                'units' => $this->allPossibleUnits,
                'estimatedProfit' => 0,
                'expenses' => 0,
                'revenue' => 0,
                'orderedProductSalesAmount' => 0,
                'PPCCostsAmount' => 0,
                'totalExpensesAmount' => 0,
                'totalIncomeAmount' => 0,
                'roi' => 0,
                'margin' => 0,
                'markup' => 0,
                'totalACOS' => 0
            ];

            if ($dateEnd >= $filtersDateEnd) {
                break;
            }

            $dateStart = clone $dateEnd;
            $dateStart->modify('next day');
            $dateStart->setTime(0,0);
        }

        return $dataSeriesStructure;
    }

    public function fillAmounts(array $dataSeriesStructure, array $dataSeries, array $flatTree): array
    {
        foreach ($dataSeries as $dataSery) {
            $dataSeryDateTime = new \DateTime($dataSery['date']);

            foreach ($dataSeriesStructure as $k => $dataSeryGroup) {
                if ($dataSeryDateTime > $dataSeryGroup['dateTimeEnd']) {
                    continue;
                }
                if ($dataSeryDateTime < $dataSeryGroup['dateTimeStart']) {
                    continue;
                }

                $flatTreePart = $flatTree[$dataSery['sales_category_id']] ?? null;

                if (empty($flatTreePart)) {
                    continue;
                }

                $tags = $flatTreePart['tags'] ?? [];
                $salesCategoriesToBeFilled = explode(TreeStructureHelper::PATH_SEPARATOR, $flatTreePart['path']);

                // Filling this category and all parents
                foreach ($salesCategoriesToBeFilled as $salesCategoryIdToBeFilled) {
                    $newAmount = $dataSeriesStructure[$k]['salesCategories'][$salesCategoryIdToBeFilled] + $dataSery['amount'];
                    $newAmount = round($newAmount, 2);
                    $dataSeriesStructure[$k]['salesCategories'][$salesCategoryIdToBeFilled] = $newAmount;
                }

                if (in_array(SalesCategory::TAG_REVENUE, $tags)) {
                    $dataSeriesStructure[$k]['revenue'] += $dataSery['amount'];
                    $dataSeriesStructure[$k]['salesCategories']['revenue'] += $dataSery['amount'];
                } else {
                    $dataSeriesStructure[$k]['expenses'] += $dataSery['amount'];
                    $dataSeriesStructure[$k]['salesCategories']['expenses'] += $dataSery['amount'];
                }

                if (in_array(SalesCategory::TAG_PRODUCT_SALES, $tags)) {
                    $dataSeriesStructure[$k]['orderedProductSalesAmount'] += $dataSery['amount'];
                } else if (in_array(SalesCategory::TAG_PPC_COSTS, $tags)) {
                    $dataSeriesStructure[$k]['PPCCostsAmount'] += $dataSery['amount'];
                } else if (in_array(SalesCategory::TAG_MANUAL_NET_PURCHASE_PRICE, $tags)) {
                    $dataSeriesStructure[$k]['orderedProductSalesAmount'] += $dataSery['amount'];
                }

                if ($dataSery['amount'] < 0) {
                    $dataSeriesStructure[$k]['totalExpensesAmount'] += $dataSery['amount'];
                } else {
                    $dataSeriesStructure[$k]['totalIncomeAmount'] += $dataSery['amount'];
                }
            }
        }

        foreach ($dataSeriesStructure as &$dataSeryGroup) {
            $margin = $this->calculator->getMargin($dataSeryGroup['revenue'], $dataSeryGroup['expenses']);
            $markup = $this
                ->calculator
                ->getMarkup(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses'],
                    $dataSeryGroup['orderedProductSalesAmount']
                );
            $ROI = $this
                ->calculator
                ->getROI(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses']
                );
            $estimatedProfit = $this
                ->calculator
                ->getNetProfit(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses']
                );
            $totalACOS = $this
                ->calculator
                ->getTotalACOS(
                    $dataSeryGroup['orderedProductSalesAmount'],
                    $dataSeryGroup['PPCCostsAmount']
                );

            $dataSeryGroup['estimatedProfit'] = round($estimatedProfit, 2);
            $dataSeryGroup['netProfit'] = round($estimatedProfit, 2);
            $dataSeryGroup['roi'] = null === $ROI ? $ROI : round($ROI, 2);
            $dataSeryGroup['margin'] = null === $margin ? $margin : round($margin, 2);
            $dataSeryGroup['markup'] = round($markup, 2);
            $dataSeryGroup['revenue'] = round($dataSeryGroup['revenue'], 2);
            $dataSeryGroup['expenses'] = round($dataSeryGroup['expenses'], 2);
            $dataSeryGroup['PPCCostsAmount'] = round($dataSeryGroup['PPCCostsAmount'], 2);
            $dataSeryGroup['totalACOS'] = round($totalACOS, 2);
            $dataSeryGroup['totalExpensesAmount'] = round($dataSeryGroup['totalExpensesAmount'], 2);
            $dataSeryGroup['totalIncomeAmount'] = round($dataSeryGroup['totalIncomeAmount'], 2);
        }

        return $dataSeriesStructure;
    }

    public function fillUnits(array $dataSeriesStructure, array $groupedUnits): array
    {
        foreach ($groupedUnits as $unit) {
            $unitDateTime = new \DateTime($unit['date']);

            foreach ($dataSeriesStructure as $k => $dataSeryGroup) {
                if ($unitDateTime > $dataSeryGroup['dateTimeEnd']) {
                    continue;
                }
                if ($unitDateTime < $dataSeryGroup['dateTimeStart']) {
                    continue;
                }
                $dataSeriesStructure[$k]['units'][$unit['type']] += $unit['amount'];
            }
        }

        return $dataSeriesStructure;
    }

    public function removeTemporaryFields(array $dataSeriesStructure): array
    {
        foreach ($dataSeriesStructure as $k => $v) {
            unset($dataSeriesStructure[$k]['dateTimeStart']);
            unset($dataSeriesStructure[$k]['dateTimeEnd']);
        }

        return $dataSeriesStructure;
    }
}
