<?php

namespace common\components\salesMetricCalculator;

use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\SalesCategory;

class DataSeriesStructureManager
{
    public const PERIOD_TYPE_HOUR = 'hour';
    public const PERIOD_TYPE_DAY = 'day';
    public const PERIOD_TYPE_WEEK = 'week';
    public const PERIOD_TYPE_MONTH = 'month';
    public const PERIOD_TYPE_YEAR = 'year';
    public const PERIOD_TYPE_ALL = 'all';

    public const PERIOD_TYPES = [
        self::PERIOD_TYPE_DAY,
        self::PERIOD_TYPE_WEEK,
        self::PERIOD_TYPE_MONTH,
        self::PERIOD_TYPE_YEAR,
        self::PERIOD_TYPE_HOUR
    ];
    private Calculator $calculator;
    private array $allPossibleUnits = [];
    private array $allPossibleCategories = [];

    public function __construct()
    {
        $this->calculator = new Calculator();
    }

    public function setAllPossibleCategories(array $allPossibleCategories): self
    {
        foreach ($allPossibleCategories as $categoryName => $data) {
            $this->allPossibleCategories[$categoryName] = 0;
        }

        return $this;
    }

    public function setAllPossibleUnits(array $allPossibleUnits): self
    {
        if (isset($allPossibleUnits[0])) {
            $allPossibleUnits = array_flip($allPossibleUnits);
        }
        array_walk($allPossibleUnits, function (&$v) {
            $v = 0;
        });
        $this->allPossibleUnits = $allPossibleUnits;
        return $this;
    }

    /**
     * Prepares empty data series structure grouped by dates
     *
     * @return array
     * @throws \Exception
     */
    public function prepareEmptyStructure(
        \DateTime $dateStart,
        \DateTime $dateEnd,
        string $periodType = self::PERIOD_TYPE_DAY
    ): array {
        if (!in_array($periodType, self::PERIOD_TYPES)) {
            $periodType = self::PERIOD_TYPE_DAY;
        }

        $filtersDateEnd = (clone $dateEnd)->setTime(23, 59, 59);
        $filtersDateStart = clone $dateStart->setTime(0, 0, 0);
        $dataSeriesStructure = [];
        $dateStart = clone $dateStart;

        if ($periodType === self::PERIOD_TYPE_YEAR) {
            $dateStart->modify('first day of january this year');
        } elseif ($periodType === self::PERIOD_TYPE_MONTH)  {
            $dateStart->modify('first day of this month');
        } elseif ($periodType === self::PERIOD_TYPE_WEEK) {
            $dateStart->modify('monday this week');
        }

        $dateFormat = $periodType === self::PERIOD_TYPE_HOUR ? 'Y-m-d H:00:00' : 'Y-m-d';

        while (true) {
            $dateEnd = clone $dateStart;

            $dateEnd
                ->modify("+1 {$periodType}s")
                ->modify("-1 second");

            if ($dateStart < $filtersDateStart) {
                $dateStartFormatted = $filtersDateStart->format($dateFormat);
            } else {
                $dateStartFormatted = $dateStart->format($dateFormat);
            }

            if ($dateEnd > $filtersDateEnd) {
                $dateEndFormatted = $filtersDateEnd->format($dateFormat);
            } else {
                $dateEndFormatted = $dateEnd->format($dateFormat);
            }

            $dataSeriesStructure[$dateStartFormatted] = [
                'date' => $dateStartFormatted,
                'dateTimeStart' => $dateStart,
                'dateTimeEnd' => $dateEnd,
                'dateStart' => $dateStartFormatted,
                'dateEnd' => $dateEndFormatted,
                'salesCategories' => $this->allPossibleCategories,
                'units' => $this->allPossibleUnits,
                'estimatedProfit' => 0,
                'expenses' => 0,
                'revenue' => 0,
                'orderedProductSalesAmount' => 0,
                'PPCCostsAmount' => 0,
                'totalExpensesAmount' => 0,
                'totalIncomeAmount' => 0,
                'roi' => 0,
                'margin' => 0,
                'markup' => 0,
                'totalACOS' => 0
            ];

            if ($dateEnd >= $filtersDateEnd) {
                break;
            }

            $dateStart = clone $dateEnd;
            $dateStart->modify('+1 second');
        }

        return $dataSeriesStructure;
    }

    public function fillAmounts(array $dataSeriesStructure, array $dataSeries, array $flatTree): array
    {
        foreach ($dataSeries as $dataSery) {
            $dataSeryDateTime = new \DateTime($dataSery['date']);

            foreach ($dataSeriesStructure as $k => $dataSeryGroup) {
                if ($dataSeryDateTime > $dataSeryGroup['dateTimeEnd']) {
                    continue;
                }
                if ($dataSeryDateTime < $dataSeryGroup['dateTimeStart']) {
                    continue;
                }

                $flatTreePart = $flatTree[$dataSery['sales_category_id']] ?? null;

                if (empty($flatTreePart)) {
                    continue;
                }

                $tags = $flatTreePart['tags'] ?? [];
                $salesCategoriesToBeFilled = explode(TreeStructureHelper::PATH_SEPARATOR, $flatTreePart['path']);

                // Filling this category and all parents
                foreach ($salesCategoriesToBeFilled as $salesCategoryIdToBeFilled) {
                    $newAmount =
                        ($dataSeriesStructure[$k]['salesCategories'][$salesCategoryIdToBeFilled] ?? 0)
                        + $dataSery['amount'];
                    $newAmount = round_half_even($newAmount, 2);
                    $dataSeriesStructure[$k]['salesCategories'][$salesCategoryIdToBeFilled] = $newAmount;
                }

                $amount = (float)$dataSery['amount'];
                $dataSeriesStructure[$k]['salesCategories'] ??= [];

                if (in_array(SalesCategory::TAG_REVENUE, $tags, true)) {
                    $dataSeriesStructure[$k]['revenue'] = ($dataSeriesStructure[$k]['revenue'] ?? 0) + $amount;
                    $dataSeriesStructure[$k]['salesCategories']['revenue'] = ($dataSeriesStructure[$k]['salesCategories']['revenue'] ?? 0) + $amount;
                } else {
                    $dataSeriesStructure[$k]['expenses'] = ($dataSeriesStructure[$k]['expenses'] ?? 0) + $amount;
                    $dataSeriesStructure[$k]['salesCategories']['expenses'] = ($dataSeriesStructure[$k]['salesCategories']['expenses'] ?? 0) + $amount;
                }

                if (in_array(SalesCategory::TAG_PRODUCT_SALES, $tags)) {
                    $dataSeriesStructure[$k]['orderedProductSalesAmount'] += $dataSery['amount'];
                } else if (in_array(SalesCategory::TAG_PPC_COSTS, $tags)) {
                    $dataSeriesStructure[$k]['PPCCostsAmount'] += $dataSery['amount'];
                } else if (in_array(SalesCategory::TAG_MANUAL_NET_PURCHASE_PRICE, $tags)) {
                    $dataSeriesStructure[$k]['orderedProductSalesAmount'] += $dataSery['amount'];
                }

                $dataSeriesStructure[$k]['totalExpensesAmount'] += $dataSery['total_income'];
                $dataSeriesStructure[$k]['totalIncomeAmount'] += $dataSery['total_expenses'];
            }
        }

        foreach ($dataSeriesStructure as &$dataSeryGroup) {
            $margin = $this->calculator->getMargin($dataSeryGroup['revenue'], $dataSeryGroup['expenses']);
            $markup = $this
                ->calculator
                ->getMarkup(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses'],
                    $dataSeryGroup['orderedProductSalesAmount']
                );
            $ROI = $this
                ->calculator
                ->getROI(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses']
                );
            $estimatedProfit = $this
                ->calculator
                ->getNetProfit(
                    $dataSeryGroup['revenue'],
                    $dataSeryGroup['expenses']
                );
            $totalACOS = $this
                ->calculator
                ->getTotalACOS(
                    $dataSeryGroup['orderedProductSalesAmount'],
                    $dataSeryGroup['PPCCostsAmount']
                );

            $dataSeryGroup['estimatedProfit'] = round_half_even($estimatedProfit, 2);
            $dataSeryGroup['netProfit'] = round_half_even($estimatedProfit, 2);
            $dataSeryGroup['roi'] = null === $ROI ? $ROI : round_half_even($ROI, 2);
            $dataSeryGroup['margin'] = null === $margin ? $margin : round_half_even($margin, 2);
            $dataSeryGroup['markup'] = round_half_even($markup, 2);
            $dataSeryGroup['revenue'] = round_half_even($dataSeryGroup['revenue'], 2);
            $dataSeryGroup['expenses'] = round_half_even($dataSeryGroup['expenses'], 2);
            $dataSeryGroup['PPCCostsAmount'] = round_half_even($dataSeryGroup['PPCCostsAmount'], 2);
            $dataSeryGroup['totalACOS'] = round_half_even($totalACOS, 2);
            $dataSeryGroup['totalExpensesAmount'] = round_half_even($dataSeryGroup['totalExpensesAmount'], 2);
            $dataSeryGroup['totalIncomeAmount'] = round_half_even($dataSeryGroup['totalIncomeAmount'], 2);
        }

        return $dataSeriesStructure;
    }

    public function fillUnits(array $dataSeriesStructure, array $groupedUnits): array
    {
        foreach ($groupedUnits as $unit) {
            $unitDateTime = new \DateTime($unit['date']);

            foreach ($dataSeriesStructure as $k => $dataSeryGroup) {
                if ($unitDateTime > $dataSeryGroup['dateTimeEnd']) {
                    continue;
                }
                if ($unitDateTime < $dataSeryGroup['dateTimeStart']) {
                    continue;
                }
                $dataSeriesStructure[$k]['units'][$unit['type']] += $unit['amount'];
            }
        }

        return $dataSeriesStructure;
    }

    public function removeTemporaryFields(array $dataSeriesStructure): array
    {
        foreach ($dataSeriesStructure as $k => $v) {
            unset($dataSeriesStructure[$k]['dateTimeStart']);
            unset($dataSeriesStructure[$k]['dateTimeEnd']);
        }

        return $dataSeriesStructure;
    }
}
