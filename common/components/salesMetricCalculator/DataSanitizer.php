<?php

namespace common\components\salesMetricCalculator;

use common\components\CustomerComponent;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesMetricCalculator\dto\ProfitResult;
use common\components\salesMetricCalculator\dto\UnitsResult;
use common\models\SalesCategory;
use common\models\UserToken;

class DataSanitizer
{
    public static bool $isEnabled = true;

    protected const VISIBLE_SALES_CATEGORY_IDS = [
        RevenueExpensesStrategy::CATEGORY_REVENUE,
        RevenueExpensesStrategy::CATEGORY_PRODUCT_SALES,
    ];

    protected const VISIBLE_UNITS = [
        'orders',
        'units'
    ];

    protected CustomerComponent $customerComponent;

    public function __construct()
    {
        /** @var CustomerComponent $customerComponent */
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function sanitizeProfitResult(ProfitResult $profitResult): void
    {
        if (!$this->shouldSanitize()) {
            return;
        }

        $sanitizedCopy = new ProfitResult();
        $sanitizedCopy->revenueAmount = $profitResult->revenueAmount;
        $sanitizedCopy->netProfit = $profitResult->netProfit;
        $sanitizedCopy->orderedProductSalesAmount = $profitResult->orderedProductSalesAmount;
        $sanitizedCopy->salesCategories = $this->sanitizeSalesCategories($profitResult->salesCategories);
        $sanitizedCopy->dataSeries = $this->sanitizeDataSeries($profitResult->dataSeries);

        foreach ($profitResult as $k => $v) {
            $profitResult->{$k} = $sanitizedCopy->{$k};
        }
    }

    public function sanitizeUnitsResult(UnitsResult $unitsResult): void
    {
        if (!$this->shouldSanitize()) {
            return;
        }

        foreach ($unitsResult as $unit => $amount) {
            if (!in_array($unit, self::VISIBLE_UNITS)) {
                $unitsResult->$unit = 0;
            }
        }
    }


    public function sanitizeGroupedUnitsResult(array $allUnitsData): array
    {
        if (!$this->shouldSanitize()) {
            return $allUnitsData;
        }

        foreach ($allUnitsData as &$unitData) {
            if (!in_array($unitData['type'], self::VISIBLE_UNITS)) {
                $unitData['amount'] = 0;
            }
        }

        return $allUnitsData;
    }

    public function sanitizePPCResult(array $result): array
    {
        if (!$this->shouldSanitize()) {
            return $result;
        }

        foreach ($result as $key => $value) {
            $result[$key] = 0;
        }

        return $result;
    }

    protected function sanitizeSalesCategories(array $salesCategories): array
    {
        foreach ($salesCategories as $salesCategoryId => $data) {
            if (!in_array($salesCategoryId, self::VISIBLE_SALES_CATEGORY_IDS)) {
                $salesCategories[$salesCategoryId]['amount'] = 0;
            }

            $salesCategories[$salesCategoryId]['children'] = $this->sanitizeSalesCategories($data['children']);
        }

        return $salesCategories;
    }

    protected function sanitizeDataSeries(array $dataSeries): array
    {
        foreach ($dataSeries as $date => $dataSery) {
            foreach ($dataSery['units'] as $unit => $amount) {
                if (!in_array($unit, self::VISIBLE_UNITS)) {
                    $dataSeries[$date]['units'][$unit] = 0;
                }
            }

            $dataSeries[$date]['expenses'] = 0;
        }

        return $dataSeries;
    }

    public function sanitizeData(array $data, array $fieldsConfig): array
    {
        if (!$this->shouldSanitize() || empty($fieldsConfig)) {
            return $data;
        }

        $isMultipleRecords = isset($data[0]) && is_array($data[0]);

        if ($isMultipleRecords) {
            foreach ($data as $key => $record) {
                $data[$key] = $this->sanitizeRecord($record, $fieldsConfig);
            }
        } else {
            $data = $this->sanitizeRecord($data, $fieldsConfig);
        }

        return $data;
    }

    private function sanitizeRecord(array $record, array $fieldsConfig): array
    {
        foreach ($fieldsConfig as $field => $type) {
            if (isset($record[$field])) {
                $record[$field] = $this->getSanitizedValue($type);
            }
        }

        return $record;
    }

    public function getSanitizedValue(string $type)
    {
        switch ($type) {
            case 'text':
                return null;
            case 'number':
                return 0;
            default:
                return null;
        }
    }

    public function getCustomerComponent()
    {
        return $this->customerComponent;
    }

    protected function shouldSanitize(): bool
    {
        $userToken = \Yii::$app->user->identity ?? null;

        if (empty($userToken)
            || $this->customerComponent->isActive()
            || !self::$isEnabled
            || $userToken->isSellerLogicUser()
        ) {
            return false;
        }

        return true;
    }
}
