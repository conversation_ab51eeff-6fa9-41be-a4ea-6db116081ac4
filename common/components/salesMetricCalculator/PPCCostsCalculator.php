<?php

namespace common\components\salesMetricCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\models\customer\clickhouse\PpcCostsLastFewDaysExtendedView;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\TransactionExtendedView;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\TransactionExtendedViewV1;
use common\models\FinanceEventCategory;
use yii\db\Expression;
use yii\db\Query;

class PPCCostsCalculator
{
    use ExtraFiltersTrait;

    protected CustomerComponent $customerComponent;
    protected DataSanitizer $dataSanitizer;
    private DbManager $dbManager;

    public function __construct()
    {
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->dataSanitizer = new DataSanitizer();
        /** @var DbManager $dbManager */
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function calc(FiltersForm $filtersForm): array
    {
        $response = [
            'sponsored_products_amount' => 0,
            'sponsored_display_amount' => 0,
            'sponsored_brands_product_collection_amount' => 0,
            'sponsored_brands_store_spotlight_amount' => 0,
            'sponsored_brands_video_amount' => 0,
        ];

        try {
            $params = [
                ':currency_id' => $filtersForm->currencyId,
            ];

            $queries = [
                $this->buildUnionQuery(
                    $filtersForm,
                    $params,
                    [
                        FinanceEventCategory::PPC_SPONSORED_PRODUCTS_PATH
                    ],
                    'sponsored_products_amount'
                ),
                $this->buildUnionQuery(
                    $filtersForm,
                    $params,
                    [
                        FinanceEventCategory::PPC_SPONSORED_DISPLAY_PATH
                    ],
                    'sponsored_display_amount'
                ),
                $this->buildUnionQuery(
                    $filtersForm,
                    $params,
                    [
                        FinanceEventCategory::PPC_SPONSORED_BRANDS_PRODUCT_COLLECTION_PATH
                    ],
                    'sponsored_brands_product_collection_amount'
                ),
                $this->buildUnionQuery(
                    $filtersForm,
                    $params,
                    [
                        FinanceEventCategory::PPC_SPONSORED_BRANDS_STORE_SPOTLIGHT_PATH
                    ],
                    'sponsored_brands_store_spotlight_amount'
                ),
                $this->buildUnionQuery(
                    $filtersForm,
                    $params,
                    [
                        FinanceEventCategory::PPC_SPONSORED_BRANDS_VIDEO_PATH,
                        FinanceEventCategory::PPC_SPONSORED_BRANDS_BRAND_VIDEO_PATH
                    ],
                    'sponsored_brands_video_amount'
                ),
            ];

            $queries = array_filter($queries);
            if (empty($queries)) {
                return $response;
            }

            $unionQuery = (new Query())
                ->select([
                    '*'
                ]);

            $newUnionParts = [];
            foreach ($queries as $i => $query) {
                $newUnionParts[] = $query->createCommand()->getRawSql();
            }

            $unionQuery->from(['(' . implode(' UNION ALL ', $newUnionParts) . ')']);

            $clickhouseDBConnection = $this->dbManager->getClickhouseCustomerDb();;
            $amounts = $unionQuery->all($clickhouseDBConnection);

            foreach ($amounts as $item) {
                $response[$item['type']] = (float)$item['amount'];
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return $this->dataSanitizer->sanitizePPCResult($response);
    }

    protected function buildUnionQuery(
        FiltersForm $filtersForm,
        array &$params,
        array $financeEventCategoryPaths,
        string $resultColumnName
    ): ?Query
    {
        $tableName = TransactionExtendedViewV1::tableName();
        $timezone = $this->customerComponent->getUserTimezoneName();
        $dateColumnName = $filtersForm->isTransactionDateMode ? 'transaction_date' : 'posted_date';

        $categoryIds = FinanceEventCategory::getIdsByPaths($financeEventCategoryPaths);

        if (empty($categoryIds)) {
            return null;
        }

        $transactionsQuery = TransactionExtendedViewV1::find()
            ->from($tableName)
            ->where(['category_id' => $categoryIds]);
        $this->applyMarketplaceSellerGroupsFilter(
            $transactionsQuery,
            $filtersForm->marketplaceSellerIds,
            $filtersForm->marketplaceId,
            $filtersForm->sellerId,
        );

        $this->applyExpiredOrInactiveSubscriptionLogic($transactionsQuery, 'seller_id');
        $this->applyAmazonAdAccountsLogic($transactionsQuery);
        $this->applyPostedDateTransactionDateFilter($transactionsQuery, $filtersForm);
        (new TransactionQueryExecutor())->applyFiltersByQueryBuilder($transactionsQuery, $filtersForm);

        $ppcCostsLastFewDaysQuery = clone $transactionsQuery;
        $ppcCostsLastFewDaysQuery->from(PpcCostsLastFewDaysExtendedView::tableName());

        $query = (new Query())
            ->select([
                new Expression("round(sum(amount_eur / {$this->customerComponent->getMoneyAccuracy()} * dictGetOrNull(default.currency_rate_dict, 'value', tuple(toDate({$dateColumnName}, '$timezone'), :currency_id))), 2) as amount", $params),
                new Expression("'{$resultColumnName}' as type")
            ])
            ->from((new Query())->from([
                't' => $transactionsQuery
                    ->union($ppcCostsLastFewDaysQuery, true)
            ]));

        return $query;
    }
}
