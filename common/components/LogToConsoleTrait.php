<?php

namespace common\components;

use yii\log\Logger;

trait LogToConsoleTrait
{
    /**
     * Syntax sugar for log() method.
     * @param mixed $message
     */
    protected function warning($message): void
    {
        $this->log($message, Logger::LEVEL_WARNING);
    }

    /**
     * Syntax sugar for log() method.
     * @param mixed $message
     */
    protected function info($message): void
    {
        $this->log($message, Logger::LEVEL_INFO);
    }

    /**
     * Syntax sugar for log() method.
     * @param mixed $message
     */
    protected function error($message): void
    {
        $this->log($message, Logger::LEVEL_ERROR);
    }

    /**
     * Logs (right at the moment) message into consumers category
     * (separate log target which writes directly into console).
     *
     * @param $message
     * @param string $level
     */
    protected function log($message, string $level = Logger::LEVEL_INFO): void
    {
        $flushInterval = \Yii::getLogger()->flushInterval;
        $logCategory = 'console';

        try {
            \Yii::getLogger()->flushInterval = 1;

            switch ($level) {
                case Logger::LEVEL_ERROR:
                    \Yii::error($message, $logCategory);
                break;
                case Logger::LEVEL_WARNING:
                    \Yii::warning($message, $logCategory);
                    break;
                default:
                    \Yii::info($message, $logCategory);
                    break;
            }
        } catch (\Exception $e) {
        } finally {
            \Yii::getLogger()->flushInterval = $flushInterval;
        }
    }
}
