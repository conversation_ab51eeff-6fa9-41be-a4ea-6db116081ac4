<?php

namespace common\components;

use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;

class Permissions
{
    public const DASHBOARD_VIEW = 'basDashboardView';
    public const MY_PRODUCTS_VIEW = 'basMyProductsView';
    public const MY_PRODUCTS_MANAGE = 'basMyProductsManage';
    public const PRODUCT_COST_IMPORT_LIST = 'basProductCostImportList';
    public const PRODUCT_COST_IMPORT_CREATE = 'basProductCostImportCreate';
    public const PRODUCT_COST_EXPORT_LIST = 'basProductCostExportList';
    public const PRODUCT_COST_EXPORT_CREATE = 'basProductCostExportCreate';
    public const PRODUCT_COST_AUTO_EXPORT_MANAGE = 'basProductExportSettingManage';
    public const PRODUCT_COST_AUTO_EXPORT_VIEW = 'basProductExportSettingList';
    public const PRODUCT_COST_AUTO_IMPORT_MANAGE = 'basProductImportSettingManage';
    public const PRODUCT_COST_AUTO_IMPORT_VIEW = 'basProductImportSettingList';
    public const ORDER_VIEW = 'basOrderView';

    public const PRODUCT_EXPORT_TEMPLATE_DELETE = 'basProductExportTemplateDelete';
    public const PRODUCT_EXPORT_TEMPLATE_MANAGE = 'basProductExportTemplateManage';
    public const PRODUCT_EXPORT_TEMPLATE_LIST = 'basProductExportTemplateList';
    public const PRODUCT_EXPORT_TEMPLATE_VIEW = 'basProductExportTemplateView';

    public const INDIRECT_COSTS_VIEW = 'basIndirectCostView';
    public const INDIRECT_COSTS_MANAGE = 'basIndirectCostManage';

    public const TRANSACTION_VIEW = 'basTransactionView';

    const REPRICER_DASHBOARD_VIEW = 'repricerDashboardView';

    private static array $basicPermissions = [
        [
            'allow' => true,
            'verbs' => ['OPTIONS'],
        ],
    ];

    private static array$httpVerbs = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];

    /**
     * ex. argument:
     * ```php
     * [
     *     'GET, PUT' => [
     *         Permissions::USER_MANAGE,
     *         Permissions::STAFF_MANAGE,
     *         Permissions::RBAC_ROLE_MANAGE,
     *      ],
     * ]
     * ```.
     * @param  array $withPermissions
     * @return array
     */
    public static function allowBasicPermissionsWith(array $withPermissions): array
    {
        return [
            'class' => AccessControl::class,
            'rules' => ArrayHelper::merge(self::$basicPermissions, self::expandPermissions($withPermissions)),
        ];
    }

    private static function expandPermissions(array $withPermissions): array
    {
        return array_merge(...array_map(static function ($key, $value) {
            if (!is_array($value)) {
                throw new \InvalidArgumentException(
                    sprintf('Given permission has incorrect type: %s, expected array of strings.', gettype($value))
                );
            }

            return array_map(static function (string $verb) use ($value): array {
                $verb = trim($verb);
                if (!in_array($verb, self::$httpVerbs, true)) {
                    throw new \InvalidArgumentException(
                        sprintf('Incorrect http verb: %s, expected one of: [%s].', $verb, implode(',', self::$httpVerbs))
                    );
                }
                return [
                    'allow' => true,
                    'roles' => $value,
                    'verbs' => [$verb],
                ];
            }, explode(',', $key));
        }, array_keys($withPermissions), $withPermissions));
    }
}