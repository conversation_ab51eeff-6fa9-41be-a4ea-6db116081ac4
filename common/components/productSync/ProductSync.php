<?php

namespace common\components\productSync;

use common\components\COGSync\ProductsSaver;
use common\components\core\db\Connection;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use yii\helpers\Console;

class ProductSync
{
    use LogToConsoleTrait;

    protected const CHUNK_SIZE = 2000;

    private DbManager $dbManager;
    protected Connection $repricerMainDb;
    protected Connection $customerServiceDb;
    protected MessagesSender $messagesSender;
    protected ProductsSaver $productsSaver;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->repricerMainDb = $this->dbManager->getRepricerMainDb();
        $this->customerServiceDb = \Yii::$app->customerServiceDb;
        $this->messagesSender = new MessagesSender();
        $this->productsSaver = new ProductsSaver();
    }

    public function sync(string $sellerId)
    {
        $this->dbManager->setSellerId($sellerId);
        $this->customerServiceDb->createCommand('SET search_path TO product')->execute();

        $limit = self::CHUNK_SIZE;
        $offset = 0;

        $countAll = $this->countAllProducts($sellerId);
        $countChunks = ceil($countAll / $limit);
        $processedCount = 0;
        Console::startProgress($processedCount, $countChunks, 'Chunks sent');

        while (true) {
            $this->messagesSender->customerProductSync($sellerId, $limit, $offset);
            $processedCount++;
            Console::updateProgress($processedCount, $countChunks, 'Chunks sent');
            $offset += $limit;

            if ($offset > $countAll) {
                break;
            }
        }
    }

    public function syncChunk(string $sellerId, int $limit, int $offset): void
    {
        $this->info("Syncing products for seller {$sellerId}, limit: {$limit}, offset: {$offset}");
        $this->dbManager->setSellerId($sellerId);
        $this->customerServiceDb->createCommand('SET search_path TO product')->execute();

        try {
            $tableName = $this->getTableName($sellerId);
        } catch (\Throwable $e) {
            return;
        }

        $createdAt = date('Y-m-d H:i:s');

        $products = $this
            ->customerServiceDb
            ->createCommand("
                    SELECT DISTINCT ON (marketplace_id, sku)
                        '{$sellerId}' as seller_id,
                        '" . Product::SOURCE_REPRICER . "' as source,
                        marketplace_id,
                        COALESCE(catalog_product_name, product_name) as title,
                        catalog_product_name,
                        ean,
                        isbn,
                        upc,
                        main_image,
                        parent_asin,
                        brand,
                        model,
                        product_type,
                        manufacturer,
                        age_range,
                        adult_product,
                        sku,
                        asin,
                        stock_type,
                        condition,
                        '$createdAt' as created_at,
                        '$createdAt' as updated_at
                    FROM {$tableName}
                    ORDER BY marketplace_id, sku, created_at DESC
                    LIMIT {$limit} 
                    OFFSET {$offset}
                ")
            ->queryAll();

        $this->info("Found " . count($products) . " products");

        foreach ($products as $k => $product) {
            if (empty($product['sku']) || empty($product['marketplace_id'])) {
                unset($products[$k]);
                continue;
            }

            $marketplace = AmazonMarketplace::getById($product['marketplace_id']);
            $products[$k]['currency_code'] = $marketplace->currency_code;
            $products[$k]['adult_product'] = (bool) $product['adult_product'];

            if (!ctype_digit($product['condition'])) {
                $products[$k]['condition'] = null;
            }
        }

        $this->saveProducts($products);

        // Prevent server overload
        usleep(random_int(300000, 1000000));
    }

    public function countAllProducts(string $sellerId): int
    {
        try {
            $tableName = $this->getTableName($sellerId);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
            return 0;
        }

        return $this
            ->customerServiceDb
            ->createCommand("SELECT count(*) FROM {$tableName}")
            ->queryScalar();
    }

    protected function saveProducts(array $productInfos): void
    {
        if (count($productInfos) === 0) {
            return;
        }

        try {
            $this->info(sprintf("Found %s products, saving them", count($productInfos)));

            $productInfos = $this->productsSaver->fillIsEnabledSyncWithRepricerDefaultValue($productInfos);

            $insertUpdateSql = $this
                ->dbManager
                ->getCustomerDb()
                ->createCommand()
                ->batchInsert(
                    Product::tableName(),
                    array_keys(array_values($productInfos)[0]),
                    $productInfos
                )
                ->getRawSql();
            $insertUpdateSql .= ' ON CONFLICT (marketplace_id, seller_id, sku)
            DO UPDATE SET 
                stock_type = COALESCE(EXCLUDED.stock_type, ' . Product::tableName() . '.stock_type),
                title = COALESCE(EXCLUDED.title, ' . Product::tableName() . '.title),
                currency_code = COALESCE(EXCLUDED.currency_code, ' . Product::tableName() . '.currency_code),
                condition = COALESCE(' . Product::tableName() . '.condition, EXCLUDED.condition),
                ean = COALESCE(' . Product::tableName() . '.ean, EXCLUDED.ean),
                isbn = COALESCE(' . Product::tableName() . '.isbn, EXCLUDED.isbn),
                upc = COALESCE(' . Product::tableName() . '.upc, EXCLUDED.upc),
                main_image = COALESCE(' . Product::tableName() . '.main_image, EXCLUDED.main_image),
                catalog_product_name = COALESCE(' . Product::tableName() . '.catalog_product_name, EXCLUDED.catalog_product_name),
                parent_asin = COALESCE(' . Product::tableName() . '.parent_asin, EXCLUDED.parent_asin),
                brand = COALESCE(' . Product::tableName() . '.brand, EXCLUDED.brand),
                model = COALESCE(' . Product::tableName() . '.model, EXCLUDED.model),
                product_type = COALESCE(' . Product::tableName() . '.product_type, EXCLUDED.product_type),
                manufacturer = COALESCE(' . Product::tableName() . '.manufacturer, EXCLUDED.manufacturer),
                age_range = COALESCE(' . Product::tableName() . '.age_range, EXCLUDED.age_range),
                adult_product = COALESCE(' . Product::tableName() . '.adult_product, EXCLUDED.adult_product)
            ';
            $this->dbManager->getCustomerDb()->createCommand($insertUpdateSql)->execute();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    protected function getTableName(string $sellerId): string
    {
        $accountId = $this
            ->repricerMainDb
            ->createCommand("
                        SELECT id
                        FROM amazon_customer_account
                        WHERE sellerId = :seller_id and deleted = 0
                        LIMIT 1
                    ", [
                'seller_id' => $sellerId
            ])
            ->cache(60 * 60)
            ->queryScalar();

        if (empty($accountId)) {
            throw new \Exception("Unable to find account_id for seller {$sellerId}");
        }

        $customerId = $this->dbManager->getCustomerId();
        return "list_{$customerId}_{$accountId}";
    }
}
