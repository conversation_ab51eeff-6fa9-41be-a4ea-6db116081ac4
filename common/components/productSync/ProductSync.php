<?php

namespace common\components\productSync;

use yii\db\Query;
use yii\db\Expression;
use common\models\customer\Product;
use common\models\AmazonMarketplace;
use common\components\LogToConsoleTrait;
use common\components\core\db\Connection;
use common\components\COGSync\ProductsSaver;
use common\components\rabbitmq\MessagesSender;
use common\components\COGSync\DefaultVATManager;
use common\components\core\db\dbManager\DbManager;
use common\components\productSync\dto\ProductCursor;
use common\components\exception\SellerNotFoundException;

class ProductSync
{
    use LogToConsoleTrait;

    private DbManager $dbManager;
    protected Connection $repricerMainDb;
    protected Connection $customerServiceDb;
    protected MessagesSender $messagesSender;
    protected ProductsSaver $productsSaver;
    protected DefaultVATManager $defaultVATManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->repricerMainDb = $this->dbManager->getRepricerMainDb();
        $this->customerServiceDb = \Yii::$app->customerServiceDb;
        $this->messagesSender = new MessagesSender();
        $this->productsSaver = new ProductsSaver();
        $this->defaultVATManager = new DefaultVATManager();
    }

    /**
     * @throws SellerNotFoundException
     * @throws \Exception
     */
    public function sync(string $sellerId): void
    {
        $this->dbManager->setSellerId($sellerId);
        $this->customerServiceDb->createCommand('SET search_path TO product')->execute();

        if (!$this->shouldSellerBeSynced($sellerId)) {
            $this->info('Seller skipped - 0 products found.');
            return;
        }

        // cursor on start position, items will be read from first item
        $cursor = new ProductCursor(null, null);

        do {
            $this->messagesSender->customerProductSync($sellerId, $cursor);

            $cursor = $this->moveCursorToLastItem($sellerId, $cursor);

        } while (!$cursor->isStartPosition());
    }

    /**
     * @throws \Exception
     */
    private function moveCursorToLastItem(string $sellerId, ProductCursor $cursor): ProductCursor
    {
        $query = $this->getBaseQuery($sellerId, $cursor);

        $lastKey = (new Query())
            ->from(['chunk' => $query])
            ->select(['marketplace_id', 'sku'])
            ->orderBy(['marketplace_id' => SORT_DESC, 'sku' => SORT_DESC])
            ->limit(1)
            ->one($this->customerServiceDb);

        return ProductCursor::fromArray($lastKey ?: []);
    }

    /**
     * @throws \Exception
     */
    public function getBaseQuery(string $sellerId, ProductCursor $cursor): Query
    {
        $tableName = $this->getTableName($sellerId);

        $query = (new Query())
            ->from($tableName)
            ->select(new Expression('DISTINCT on (marketplace_id, sku) marketplace_id, sku'));

        if (!$cursor->isStartPosition()) {
            // todo: index (marketplace_id + sku) should be added
            $query->andWhere(
                new Expression('(marketplace_id, sku) > (:mkt, :sku)'),
                [':mkt' => $cursor->marketplaceId, ':sku' => $cursor->sku]
            );
        }

        $query
            ->limit(ProductCursor::STEP)
            ->orderBy([
                'marketplace_id' => SORT_ASC,
                'sku' => SORT_ASC,
            ]);

        return $query;
    }

    /**
     * @throws \Exception
     * @throws SellerNotFoundException
     */
    public function syncChunk(string $sellerId, ProductCursor $cursor): void
    {
        $startFrom = $cursor->isStartPosition()
            ? 'start from first item'
            : sprintf('start from item: (marketplace_id: %s sku: %s)', $cursor->marketplaceId,
                $cursor->sku);

        $this->info(
            sprintf(
                'Syncing products for seller %s, limit: %s, %s',
                $sellerId,
                ProductCursor::STEP,
                $startFrom,
            )
        );

        $this->dbManager->setSellerId($sellerId);
        $this->customerServiceDb->createCommand('SET search_path TO product')->execute();

        $createdAt = date('Y-m-d H:i:s');

        $products = $this
            ->getBaseQuery($sellerId, $cursor)
            ->addSelect([
                new Expression(':sllr as seller_id', [':sllr' => $sellerId]),
                new Expression(':src as source', [':src' => Product::SOURCE_REPRICER]),
                new Expression(':updt_at as updated_at', [':updt_at' => $createdAt]),
                new Expression(':crt_at as created_at', [':crt_at' => $createdAt]),
                new Expression('COALESCE(catalog_product_name, product_name) as title'),
                'catalog_product_name',
                'ean',
                'isbn',
                'upc',
                'main_image',
                'parent_asin',
                'brand',
                'model',
                'product_type',
                'manufacturer',
                'age_range',
                'adult_product',
                'asin',
                'stock_type',
                'condition',
            ])
            ->all($this->customerServiceDb);

        if (!$productsCount = count($products)) {
            return;
        }

        $this->info(
            sprintf('Found %d products', $productsCount)
        );

        foreach ($products as $k => $product) {
            if (empty($product['sku']) || empty($product['marketplace_id'])) {
                unset($products[$k]);
                continue;
            }

            $marketplace = AmazonMarketplace::getById($product['marketplace_id']);
            $products[$k]['currency_code'] = $marketplace->currency_code;
            $products[$k]['adult_product'] = (bool) $product['adult_product'];

            if ($product['condition'] === null || !ctype_digit($product['condition'])) {
                $products[$k]['condition'] = null;
            }
        }

        $this->saveProducts($products);
    }

    protected function saveProducts(array $productInfos): void
    {
        try {
            $this->info(sprintf("Found %s products, saving them", count($productInfos)));

            $productInfos = $this->productsSaver->fillIsEnabledSyncWithRepricerDefaultValue($productInfos);

            $insertUpdateSql = $this
                ->dbManager
                ->getCustomerDb()
                ->createCommand()
                ->batchInsert(
                    Product::tableName(),
                    array_keys(array_values($productInfos)[0]),
                    $productInfos
                )
                ->getRawSql();
            $insertUpdateSql .= ' ON CONFLICT (marketplace_id, seller_id, sku)
            DO UPDATE SET 
                stock_type = COALESCE(EXCLUDED.stock_type, ' . Product::tableName() . '.stock_type),
                title = COALESCE(EXCLUDED.title, ' . Product::tableName() . '.title),
                currency_code = COALESCE(EXCLUDED.currency_code, ' . Product::tableName() . '.currency_code),
                condition = COALESCE(' . Product::tableName() . '.condition, EXCLUDED.condition),
                ean = COALESCE(' . Product::tableName() . '.ean, EXCLUDED.ean),
                isbn = COALESCE(' . Product::tableName() . '.isbn, EXCLUDED.isbn),
                fnsku = COALESCE(' . Product::tableName() . '.fnsku, EXCLUDED.fnsku),
                upc = COALESCE(' . Product::tableName() . '.upc, EXCLUDED.upc),
                main_image = COALESCE(' . Product::tableName() . '.main_image, EXCLUDED.main_image),
                catalog_product_name = COALESCE(' . Product::tableName() . '.catalog_product_name, EXCLUDED.catalog_product_name),
                parent_asin = COALESCE(' . Product::tableName() . '.parent_asin, EXCLUDED.parent_asin),
                brand = COALESCE(' . Product::tableName() . '.brand, EXCLUDED.brand),
                model = COALESCE(' . Product::tableName() . '.model, EXCLUDED.model),
                product_type = COALESCE(' . Product::tableName() . '.product_type, EXCLUDED.product_type),
                manufacturer = COALESCE(' . Product::tableName() . '.manufacturer, EXCLUDED.manufacturer),
                age_range = COALESCE(' . Product::tableName() . '.age_range, EXCLUDED.age_range),
                adult_product = COALESCE(' . Product::tableName() . '.adult_product, EXCLUDED.adult_product)
            ';

            $colsToCheck = [
                'stock_type','title','currency_code','condition','ean','isbn', 'upc','main_image',
                'catalog_product_name','parent_asin','brand','model','product_type','manufacturer',
                'age_range','adult_product'
            ];

            $excludedTuple = 'ROW(' . implode(', ', array_map(fn($c) => "EXCLUDED.$c", $colsToCheck)) . ')';

            $tableTuple = 'ROW(' . implode(', ', array_map(fn($c) => Product::tableName() . ".$c", $colsToCheck)) . ')';

            $insertUpdateSql .= " WHERE {$excludedTuple} IS DISTINCT FROM {$tableTuple};";

            $this->dbManager->getCustomerDb()->createCommand($insertUpdateSql)->execute();
            $this->defaultVATManager->generateAndSaveDefaultVATForProducts($productInfos);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    protected function getTableName(string $sellerId): string
    {
        $accountId = $this
            ->repricerMainDb
            ->createCommand("
                        SELECT id
                        FROM amazon_customer_account
                        WHERE sellerId = :seller_id and deleted = 0
                        LIMIT 1
                    ", [
                'seller_id' => $sellerId
            ])
            ->cache(60 * 60)
            ->queryScalar();

        if (empty($accountId)) {
            throw new \Exception("Unable to find account_id for seller {$sellerId}");
        }

        $customerId = $this->dbManager->getCustomerId();
        return "product.list_{$customerId}_{$accountId}";
    }

    /**
     * @throws \Exception
     */
    private function shouldSellerBeSynced(string $sellerId): bool
    {
        $tableName = $this->getTableName($sellerId);

        return (new Query())->from($tableName)->exists($this->customerServiceDb);
    }
}
