<?php

namespace common\components\productSync;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\currencyRate\CurrencyRateManager;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostPeriod;
use yii\caching\CacheInterface;
use yii\db\Command;
use yii\helpers\Console;

class AmountsSync
{
    use LogToConsoleTrait;

    private CacheInterface $cache;
    private DbManager $dbManager;
    private CurrencyRateManager $currencyRateManager;

    public function __construct()
    {
        $this->cache = \Yii::$app->fastPersistentCache;
        $this->dbManager = \Yii::$app->dbManager;
        $this->currencyRateManager = new CurrencyRateManager();
    }

    public function sync(int $customerIdFrom = null, int $customerIdTo = null): void
    {
        foreach ($this->iterateProducts($customerIdFrom, $customerIdTo) as $products) {
            try {
                $this->info(sprintf(
                    "Found %d products for repairing (customer %s)",
                    count($products),
                    $this->dbManager->getCustomerId()
                ));
                $productInfos = [];
                $repairedIds = [];

                foreach ($products as $product) {
                    $repairedIds[] = $product['id'];

                    $shippingCost = ($product['stock_type'] === Product::STOCK_TYPE_FBA && (float)$product['pcp_shipping_costs'] === 0.00) ? null : $product['pcp_shipping_costs'];

                    $productInfos[] = [
                        'id' => $product['id'],
                        'marketplace_id' => $product['marketplace_id'],
                        'seller_id' => $product['seller_id'],
                        'sku' => $product['sku'],
                        'asin' => $product['asin'],
                        'source'=> $product['source'],
                        'buying_price' => $product['pcp_cost_of_goods'],
                        'vat' => $product['pcp_vat'],
                        'shipping_cost' =>  $shippingCost,
                        'other_fees' => $product['pcp_other_fees'],
                    ];

                    if (count($products) <= 1000) {
                        if ($product['pcp_cost_of_goods'] != $product['p_cost_of_goods']) {
                            $this->info(sprintf(
                                "%s => %s | cost_of_goods | product %s",
                                $product['p_cost_of_goods'], $product['pcp_cost_of_goods'], $product['id']
                            ));
                        }
                        if (
                            $product['pcp_shipping_costs'] != $product['p_shipping_costs']
                        ) {
                            $this->info(sprintf(
                                "%s => %s | shipping_cost | product %s",
                                $product['p_shipping_costs'], $product['pcp_shipping_costs'], $product['id']
                            ));
                        }
                        if ($product['pcp_other_fees'] != $product['p_other_fees']) {
                            $this->info(sprintf(
                                "%s => %s | other_fees | product %s",
                                $product['p_other_fees'], $product['pcp_other_fees'], $product['id']
                            ));
                        }
                        if ($product['pcp_vat'] != $product['p_vat']) {
                            $this->info(sprintf(
                                "%s => %s | vat | product %s",
                                $product['p_vat'], $product['pcp_vat'], $product['id']
                            ));
                        }
                    }
                }

                $chunks = array_chunk($productInfos, 1000);

                foreach ($chunks as $chunk) {
                    $insertUpdateSql = $this
                        ->dbManager
                        ->getCustomerDb()
                        ->createCommand()
                        ->batchInsert(
                            Product::tableName(),
                            array_keys(array_values($chunk)[0]),
                            $chunk
                        )
                        ->getRawSql();
                    $insertUpdateSql .= '  ON CONFLICT (marketplace_id, seller_id, sku) DO UPDATE SET
                        buying_price = EXCLUDED.buying_price,
                        shipping_cost = EXCLUDED.shipping_cost,
                        other_fees = EXCLUDED.other_fees,
                        vat = EXCLUDED.vat            
                    ';
                    $this->dbManager->getCustomerDb()->createCommand($insertUpdateSql)->execute();
                }
                $this->info('Repairing finished');
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    private function iterateProducts(int $customerIdFrom = null, int $customerIdTo = null): \Iterator
    {
        foreach ($this->dbManager->iterateActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (!$this->dbManager->getCustomerId()) {
                    continue;
                }

                $this->info("Processing customer " . $this->dbManager->getCustomerId());

                $countAll = Product::find()->count();
                $limit = 500;
                $offset = 0;

                Console::startProgress($offset, $countAll, 'Iterations');

                while (true) {
                    $products = $this->getProductsQuery($limit, $offset)->queryAll();

                    if (count($products) > 0) {
                        yield $products;
                    }
                    $offset += $limit;

                    if ($offset > $countAll) {
                        $offset = $countAll;
                    }

                    Console::updateProgress($offset, $countAll, 'Iterations');

                    if ($offset === $countAll) {
                        break;
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function getProductsQuery(int $limit, int $offset): Command
    {
        $currDate = date('Y-m-d 23:59:59');

        $leftJoinData = [
            'pcp_cost_of_goods' => 'cost_of_goods',
            'pcp_vat' => 'expenses_taxes',
            'pcp_shipping_costs' => 'shipping_costs',
            'pcp_other_fees' => 'other_fees'
        ];
        $leftJoins = "";

        foreach ($leftJoinData as $alias => $salesCategoryId) {
            $leftJoins .= "LEFT JOIN LATERAL (
        SELECT pcp.amount_total
        FROM " . ProductCostPeriod::tableName() . "  pcp
        WHERE 1 = 1
        AND pcp.seller_sku = p.sku
        AND pcp.seller_id = p.seller_id
        AND pcp.marketplace_id = p.marketplace_id
        AND pcp.sales_category_id = '{$salesCategoryId}'
        AND (
            (pcp.date_start <= '{$currDate}' OR pcp.date_start IS NULL)
            AND
            (pcp.date_end >= '{$currDate}' OR pcp.date_end IS NULL)
        )
        ORDER BY pcp.id DESC
        LIMIT 1
    ) {$alias} ON TRUE\n";
        }

        $query = "
SELECT
    p.id,
    max(p.seller_id) as seller_id,
    max(p.marketplace_id) as marketplace_id,
    max(p.source) as source,
    max(p.title) as title,
    max(p.sku) as sku,
    max(p.stock_type) as stock_type,
    max(p.asin) as asin,
    max(p.buying_price) as p_cost_of_goods,
    max(pcp_cost_of_goods.amount_total) as pcp_cost_of_goods,
    max(p.vat) as p_vat,
    max(pcp_vat.amount_total) as pcp_vat,
    max(p.shipping_cost) as p_shipping_costs,
    max(pcp_shipping_costs.amount_total) as pcp_shipping_costs,
    max(p.other_fees) as p_other_fees,
    max(pcp_other_fees.amount_total) as pcp_other_fees
FROM (
    SELECT * 
    FROM " . Product::tableName() . " 
    ORDER BY id DESC
    LIMIT {$limit} OFFSET {$offset}
) p
{$leftJoins}
GROUP BY p.id
HAVING COALESCE(max(p.buying_price), -1) != COALESCE(max(pcp_cost_of_goods.amount_total), -1)
    OR COALESCE(max(p.vat), -1) != COALESCE(max(pcp_vat.amount_total), -1)
    OR COALESCE(max(p.shipping_cost), -1) != COALESCE(max(pcp_shipping_costs.amount_total), -1)
    OR COALESCE(max(p.other_fees), -1) != COALESCE(max(pcp_other_fees.amount_total), -1);
";
        return Product::getDb()->createCommand($query);
    }
}