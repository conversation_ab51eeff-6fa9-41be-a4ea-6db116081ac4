<?php

namespace common\components\productSync\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class ProductSync extends BaseConsumer
{
    use LogToConsoleTrait;

    private DbManager $dbManager;
    private \common\components\productSync\ProductSync $productSynchronizer;

    public function __construct()
    {
        $this->productSynchronizer = new \common\components\productSync\ProductSync();
    }

    public function __execute(AMQPMessage $msg)
    {
        ini_set('memory_limit', '256M');
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;
        $sellerId = $message['sellerId'] ?? null;
        $limit = $message['limit'] ?? null;
        $offset = $message['offset'] ?? null;

        if (null === $limit || null === $offset || null === $sellerId) {
            $this->info('Invalid message body');
            return ConsumerInterface::MSG_ACK;
        }

        try {
            $this->productSynchronizer->syncChunk($sellerId, $limit, $offset);
        } catch (\Throwable $e) {
            // Table does not exist error need to track only on production
            if ($e->getCode() === '42P01') {
                if (YII_ENV_PROD) {
                    $this->error($e);
                }
            } else {
                $this->error($e);
                if (false !== strpos($e->getMessage(), 'server has gone away')) {
                    $this->info('Wait a little bit and requeue');
                    sleep(5);
                    return ConsumerInterface::MSG_REQUEUE;
                }
            }
        }

        return ConsumerInterface::MSG_ACK;
    }
}
