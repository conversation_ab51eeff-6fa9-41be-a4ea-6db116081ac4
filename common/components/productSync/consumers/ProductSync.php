<?php

namespace common\components\productSync\consumers;

use PhpAmqpLib\Message\AMQPMessage;
use common\components\LogToConsoleTrait;
use common\components\core\db\dbManager\DbManager;
use common\components\productSync\dto\ProductCursor;
use common\components\rabbitmq\consumers\BaseConsumer;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;

class ProductSync extends BaseConsumer
{
    use LogToConsoleTrait;

    private DbManager $dbManager;
    private \common\components\productSync\ProductSync $productSynchronizer;

    public function __construct()
    {
        $this->productSynchronizer = new \common\components\productSync\ProductSync();
    }

    public function __execute(AMQPMessage $msg)
    {
        ini_set('memory_limit', '256M');
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;
        $sellerId = $message['sellerId'] ?? null;
        $cursor = ProductCursor::fromArray($message['cursor'] ?? []);

        if (null === $sellerId) {
            $this->info('Invalid seller id');

            return ConsumerInterface::MSG_ACK;
        }

        try {
            $this->productSynchronizer->syncChunk($sellerId, $cursor);
        } catch (\Throwable $e) {
            // Table does not exist error need to track only on production
            if ($e->getCode() === '42P01') {
                if (YII_ENV_PROD) {
                    $this->error($e);
                }
            } else {
                $this->error($e);
                if (false !== strpos($e->getMessage(), 'server has gone away')) {
                    $this->info('Wait a little bit and requeue');
                    sleep(5);
                    return ConsumerInterface::MSG_REQUEUE;
                }
            }
        }

        return ConsumerInterface::MSG_ACK;
    }
}
