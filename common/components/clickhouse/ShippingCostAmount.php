<?php

namespace common\components\clickhouse;

use ClickHouseDB\Client;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderExtendedViewInterface;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\PpcCostsLastFewDaysTransaction;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use common\models\order\AmazonOrder;
use common\models\SalesCategory;
use DateTime;
use Yii;
use yii\db\Exception;
use yii\db\Query;

class ShippingCostAmount implements CostAmountInterface
{

    private DbManager $dbManager;
    protected ClickhouseTransactionExtractor $transactionExtractor;
    protected CustomerComponent $customerComponent;
    protected Client $client;
    protected CurrencyRateManager $currencyRateManager;
    protected string $salesCategoryId;

    public function __construct(string $salesCategoryId = SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS)
    {
        $this->salesCategoryId = $salesCategoryId;
        $this->dbManager = \Yii::$app->dbManager;
        $this->transactionExtractor = new ClickhouseTransactionExtractor();
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->currencyRateManager = new CurrencyRateManager();
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function generateOppositeTransactions(string $amazonOrderId): array
    {
        $shippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId($this->salesCategoryId);

        $transactionsShipping = Transaction::find()
            ->where(['=', 'AmazonOrderId', $amazonOrderId])
            ->andWhere(['in', 'COGCategoryId', $shippingCostCategoryIds])
            ->asArray()
            ->all();

        $transactions = [];
        $transactionOneBySKU = [];
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        foreach ($transactionsShipping as $transactionShipping) {
            $this->dbManager->setSellerId($transactionShipping['SellerId']);
            if ($transactionShipping['Amount'] != 0) {
                $transactionForZero = $this->transactionExtractor->convertCurrencyItemToTransaction([
                    'CurrencyAmount' => ($transactionShipping['Amount'] / $moneyAccuracy) * -1,
                    'CurrencyCode' => $transactionShipping['Currency'],
                ], $transactionShipping, false);
                $transactionForZero->CreatedAt = date('Y-m-d H:i:s');
                $transactionForZero->COGCategoryId = $transactionShipping['COGCategoryId'];
                $transactionForZero->CategoryId = $transactionShipping['CategoryId'];
                $transactionForZero->EventPeriodId = $transactionShipping['EventPeriodId'];
                $transactionForZero->ASIN = $transactionShipping['ASIN'];
                $transactionForZero->SellerOrderId = $transactionShipping['SellerOrderId'];
                $transactionForZero->IndirectCostId = $transactionShipping['IndirectCostId'];
                $transactionForZero->IndirectCostTypeId = $transactionShipping['IndirectCostTypeId'];
                $transactionForZero->PostedDate = $transactionShipping['PostedDate'];
                $transactionForZero->TransactionDate =  $transactionShipping['TransactionDate'];
                $transactions[] = $transactionForZero;
                $transactionOneBySKU[$transactionShipping['SellerId']] = $transactionShipping;
            }
        }

        return [$transactions, $transactionOneBySKU];
    }

    public function getCostAmounts(string $orderId, array $orderItems): array
    {
        $shippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId($this->salesCategoryId);

        $dateOrderPurchase = (new \DateTime($orderItems[0]->order_purchase_date));
        $currencyId = $orderItems[0]->currency_id;

        $items = [];
        $sellerSkuToOrderItemId = [];
        foreach ($orderItems as $orderItem) {
            /* @var AmazonOrderExtendedView $orderItem */
            $items[$orderItem->order_item_id] = [
                'amount' => 0,
                'product_title' => $orderItem->product_title,
                'product_asin' => $orderItem->product_asin,
                'marketplace_id' => $orderItem->marketplace_id,
                'seller_id' => $orderItem->seller_id,
                'seller_sku' => $orderItem->seller_sku,
                'order_item_id' => $orderItem->order_item_id,
            ];
            $sellerSkuToOrderItemId[$orderItem->seller_sku] = $orderItem->order_item_id;
        }

        $mainQuery = $this->buildTransactionQuery(OrderBasedTransaction::tableName(), $orderId, $shippingCostCategoryIds);

        $mainQuery->union($this->buildTransactionQuery(PpcCostsLastFewDaysTransaction::tableName(), $orderId, $shippingCostCategoryIds));


        $query1 = $this->buildTransactionQuery(OrderBasedTransaction::tableName(), $orderId, $shippingCostCategoryIds, 1);
        $query2 = $this->buildTransactionQuery(TransactionExtendedViewV1::tableName(), $orderId, $shippingCostCategoryIds);

        $query1Sql = $query1->createCommand()->getRawSql();
        $query2Sql = $query2->createCommand()->getRawSql();

        $fullSqlQuery = "
            ({$query1Sql}) 
            UNION ALL 
            ({$query2Sql}) 
        ";

        $finalQuery = (new Query())
            ->select([
                'seller_sku as SellerSKU',
                'SUM(amount_eur) as totalAmount',
                'argMax(currency, created_at) as CurrencyId'
            ])
            ->from(['(' . $fullSqlQuery . ')'])
            ->groupBy('seller_sku');

        $clickhouseDBConnection = $this->dbManager->getClickhouseCustomerDb();

        $transactionsShipping = $finalQuery->all($clickhouseDBConnection);

        $costByProducts = [];
        foreach ($transactionsShipping as $transactionShipping) {
            $currencyId = $transactionShipping['CurrencyId'];
            $orderItemId = $sellerSkuToOrderItemId[$transactionShipping['SellerSKU']];
            $costByProducts[$orderItemId] = $transactionShipping['totalAmount'];
        }

        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        foreach ($costByProducts as $orderItemId => $costByProduct) {
            $amountByCurrency = round($this->currencyRateManager->convert(
                $costByProduct,
                CurrencyRateManager::BASE_CURRENCY,
                $currencyId,
                $dateOrderPurchase
            ) / $moneyAccuracy , 2);
            $items[$orderItemId]['amount'] = $amountByCurrency * -1;
        }

        return [array_values($items), $currencyId];
    }

    public function buildTransactionQuery($tableName, $orderId, $shippingCostCategoryIds, int $isCamelCase = 0)
    {
        return (new Query())
            ->select([
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['seller_sku'][$isCamelCase] . ' as seller_sku',
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase] . ' as amount_eur',
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['currency_code'][$isCamelCase] . ' as currency',
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['created_at'][$isCamelCase] . ' as created_at',
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase] . ' as cog_category_id',
                TransactionQueryExecutor::COLUMN_NAME_MAPPING['amazon_order_id'][$isCamelCase] . ' as amazon_order_id'
            ])
            ->from($tableName)
            ->where(['=', TransactionQueryExecutor::COLUMN_NAME_MAPPING['amazon_order_id'][$isCamelCase], $orderId])
            ->andWhere(['<>', TransactionQueryExecutor::COLUMN_NAME_MAPPING['amount_eur'][$isCamelCase], 0])
            ->andWhere(['in', TransactionQueryExecutor::COLUMN_NAME_MAPPING['cog_category_id'][$isCamelCase], $shippingCostCategoryIds]);
    }

    public function generateNewCostTransactions(AmazonOrderExtendedViewInterface $order, string $currencyId, float $amount, ?Transaction $transactionForCopy): \common\models\finance\clickhouse\Transaction
    {
        $shippingCostCategoryId = ProductCostCategory::getDefaultIdBySalesCategoryId($this->salesCategoryId);

        $sellerId = $order->seller_id ?: '';
        $marketplaceId = $order->marketplace_id ?: '';
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        if ($transactionForCopy !== null) {
            $transaction = clone $transactionForCopy;
            $transaction->CreatedAt = date('Y-m-d H:i:s');
            $datePosted = $transaction->PostedDate;
        } else {
            $datePosted = $order->order_purchase_date;

            $transaction = new \common\models\finance\clickhouse\Transaction();
            $transaction->IndirectCostId = 0;
            $transaction->IndirectCostTypeId = 0;
            $transaction->Currency = $currencyId;
            $transaction->MarketplaceId = $marketplaceId;
            $transaction->CreatedAt = date('Y-m-d H:i:s');
            $transaction->SellerId = strtoupper($sellerId);
            $transaction->PostedDate = $datePosted;
            $transaction->MergeCounter = 1;
            $transaction->SellerSKU = $order->seller_sku;
            $transaction->SellerOrderId = $order->seller_id;
            $transaction->AmazonOrderId = $order->order_id;
            $transaction->CategoryId = 0;
            $transaction->COGCategoryId = $shippingCostCategoryId ?? 0;
            $transaction->Quantity = 1;
            $transaction->EventPeriodId = 0;
            $transaction->TransactionDate = $datePosted;
            $transaction->ASIN = $order->product_asin;
        }

        $transaction->Amount = (int)round($amount * -1 * $moneyAccuracy);
        $transaction->AmountEUR = (int)round($this->currencyRateManager
                ->toBaseCurrency(
                    $amount  * -1,
                    $currencyId,
                    (new \DateTime($datePosted))
                ) * $moneyAccuracy);

        return $transaction;
    }

    /**
     * @throws Exception
     */
    public function apply(array $transactions)
    {
        $transactions = json_decode(json_encode($transactions), true);
        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        /** @var Client $client */
        $client = $connection->getClient();

        $client->insertAssocBulk(Transaction::tableName(), $transactions);
    }

    /**
     * @throws \Exception
     */
    public function optimizeTable(): void
    {
        $dataBaseTableName = TransactionExtendedViewV1::tableName();
        $sql = "OPTIMIZE TABLE {$dataBaseTableName} FINAL";

        Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();

        $dataBaseTableName = TransactionBuffer::tableName();
        $sql = "OPTIMIZE TABLE {$dataBaseTableName}";

        Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();
    }
}
