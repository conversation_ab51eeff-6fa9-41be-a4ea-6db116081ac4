<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\PopulateAwareInterface;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\dbStructure\TableNameGenerator;
use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\ProxyProduct;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\Product;
use common\models\customer\TransactionBuffer;
use common\models\DbStructure;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use DateTime;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Expression;

class AmazonOrderExtendedViewV1 implements DynamicTableInterface, CronAwareInterface, PopulateAwareInterface
{
    use LogToConsoleTrait;

    protected bool $isAutoPopulate = true;
    protected DbManager $dbManager;
    public ?DateTime $dateStart = null;
    public ?DateTime $dateEnd = null;

    public function __construct(bool $isAutoPopulate = true, $dateStart = null, $dateEnd = null)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->dbManager = \Yii::$app->dbManager;
        $this->dateStart = $dateStart;
        $this->dateEnd = $dateEnd;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_VIEW;
    }

    public function getName(): string
    {
        return \common\models\customer\clickhouse\AmazonOrderExtendedViewV1::tableName();
    }

    public function getCronExpression(): string
    {
        return "00 00 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return false;
    }

    public function getSQL(): string
    {
        $maxExecutionTime = 60 * 40;

        $dbHelper = new ClickhouseDbHelper();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedMergeTree('%s', '{replica}')",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'MergeTree';

        $selectSQL = $this->getAmazonOrderSelectStatement();
        $sql = "CREATE MATERIALIZED VIEW IF NOT EXISTS {table_name} {cluster_part}
            (
                `order_item_id` String,
                `order_id` String,
                `order_status` String,
                `order_purchase_date` DateTime,
                `last_update_date` DateTime,
                `scheduled_delivery_start_date` DateTime,
                `currency_id` String,
                `seller_id` String,
                `seller_sku` String,
                `marketplace_id` String,
                `item_price` Nullable(Float64),
                `item_price_eur` Nullable(Float64),
                `offer_type` String,
                `quantity` Nullable(Int32),
                `quantity_refunded` Nullable(Int32),
                `promotion_amount_eur` Nullable(Float64),
                `amazon_fees_amount_eur` Nullable(Float64),
                `expenses_amount_eur` Nullable(Float64),
                `revenue_amount_eur` Nullable(Float64),
                `estimated_profit_amount_eur` Nullable(Float64),
                `is_approximate_amounts_calculation` Bool,
                `version` Int32,
                `product_id` Nullable(Int64),
                `product_asin` Nullable(String),
                `product_ean` Nullable(String),
                `product_upc` Nullable(String),
                `product_isbn` Nullable(String),
                `product_brand` Nullable(String),
                `product_manufacturer` Nullable(String),
                `product_adult` Nullable(Boolean),
                `product_parent_asin` Nullable(String),
                `product_type` Nullable(String),
                `product_title` Nullable(String),
                `product_stock_type` Nullable(String),
                `product_condition` Nullable(String)
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(order_purchase_date)
            PRIMARY KEY (marketplace_id, seller_id, seller_sku, order_id, order_item_id, version)
            AS 
            {$selectSQL}
            SETTINGS max_execution_time = {$maxExecutionTime}
        ";

        return $sql;
    }
    protected function getMinOrderDate(): ?string
    {
        $minStatisticTime = (new DateTime())->modify('-2 years')->format('Y-m-d H:i:s');
        $minOrderDate = AmazonOrder::find()
            ->select('min(order_purchase_date)')
            ->where(['>', 'order_purchase_date', new Expression("toDateTime('$minStatisticTime')")])
            ->scalar();

        if (empty($minOrderDate)) {
            return null;
        }

        if (strtotime($minOrderDate) < strtotime($minStatisticTime)) {
            $minOrderDate = $minStatisticTime;
        }
        return $minOrderDate;
    }

    public function getPopulateSQL(string $tableName): \Iterator
    {
        if (!$this->isAutoPopulate) {
            return;
        }

        $minOrderDate = $this->getMinOrderDate();

        if (empty($minOrderDate)) {
            return;
        }

        $maxDateTime = $this->dateEnd ?? (new \DateTime());
        $minDateTime = $this->dateStart ?? new \DateTime($minOrderDate);

        if ($minDateTime > $maxDateTime) {
            return;
        }

        $dateTimeTo = clone $maxDateTime;
        $dateTimeFrom = (clone $maxDateTime)->modify('+1 second');
        $iterationPeriod = '-3 month';

        while (true) {
            $dateTimeFrom->modify($iterationPeriod);
            $this->info("Posted date: " . $dateTimeFrom->format('Y-m-d H:i:s') . ' => ' . $dateTimeTo->format('Y-m-d H:i:s'));
            yield $this->getAmazonOrderSelectStatement($dateTimeFrom, $dateTimeTo);

            $dateTimeTo = (clone $dateTimeFrom)->modify('-1 second');
            if ($dateTimeTo <= $minDateTime) {
                $this->info("Populating finished");
                break;
            }
        }
    }

    protected function getTargetTableName(): string
    {
        return AmazonOrder::tableName();
    }

    protected function getAmazonOrderSelectStatement(
        DateTime $fromDateTime = null,
        DateTime $toDateTime = null
    ): string
    {
        $orderBasedTransactionTableName = OrderBasedTransaction::tableName();
        $moneyAccuracy = \Yii::$app->customerComponent->getMoneyAccuracy();

        $promoCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_PROMOTION);

        $amazonFeesCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_AMAZON_FEES);

        $revenueCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_REVENUE);

        $organicRefundsCategoryId = FinanceEventCategory::getOrganicRefundId();

        $amazonOrderTableName = $this->getTargetTableName();
        $productProxyTableName = ProxyProduct::tableName();
        $transactionTableName = TransactionBuffer::tableName();
        $amazonMarketplaceDictName = 'default.amazon_marketplace_dict';
        $currencyRateDictName = 'default.currency_rate_dict';

        $conditionCaseWhen = "CASE\n";
        foreach (Product::CONDITIONS_MAP as $id => $condition) {
            $conditionFunc = "arrayStringConcat(arrayDistinct([ao.condition_id, ao.condition_subtype_id]), '; ')";
            $conditionCaseWhen .= "WHEN $conditionFunc = '$condition' THEN $id\n";
        }
        $conditionCaseWhen .= "ELSE NULL\n";
        $conditionCaseWhen .= "END";
        $versionField = $amazonOrderTableName === AmazonOrderInProgress::tableName()
            ? "ao.version"
            : "0";

        $isManualPopulate = !empty($fromDateTime) && !empty($toDateTime);
        $postedDateFilter = $isManualPopulate
            ? "(
                PostedDate BETWEEN toDateTime('{$fromDateTime->modify('-60 seconds')->format('Y-m-d H:i:s')}') 
                AND 
                toDateTime('{$toDateTime->format('Y-m-d H:i:s')}')
            )"
            : "(
                PostedDate BETWEEN subtractSeconds((SELECT min(order_purchase_date) FROM {$amazonOrderTableName}), 60)
                AND
                (SELECT max(order_purchase_date) FROM {$amazonOrderTableName})
            )";
        $orderPurchaseDateFilter = $isManualPopulate
            ? "(
                order_purchase_date BETWEEN toDateTime('{$fromDateTime->format('Y-m-d H:i:s')}') 
                AND 
                toDateTime('{$toDateTime->format('Y-m-d H:i:s')}')
            )"
            : "1 = 1";
        $orderBasedCategoryIds = OrderBasedTransaction::find()->select('CategoryId')->distinct()->column();
        $noTransactionOrderIdsFn = "(
            SELECT DISTINCT AmazonOrderId
            FROM {$orderBasedTransactionTableName}
            WHERE {$postedDateFilter}
            ORDER BY PostedDate DESC
        )";
        $orderBasedExclusionCondition = !empty($orderBasedCategoryIds)
            ? "(
                CategoryId NOT IN (" . implode(',', $orderBasedCategoryIds) . ")
                OR
                AmazonOrderId NOT IN {$noTransactionOrderIdsFn}
            )"
            : "1 = 1";

        $fromClause = $amazonOrderTableName;

        $offerTypeB2B = \common\models\order\AmazonOrder::OFFER_TYPE_B2B;
        $offerTypeB2C = \common\models\order\AmazonOrder::OFFER_TYPE_B2C;

        $sql = "
            SELECT 
                ao.order_item_id as order_item_id,
                ao.order_id as order_id,
                IF (
                    ao.quantity = 0,
                    '" . Order::ORDER_STATUS_CANCELED . "',
                    ao.order_status
                ) as order_status,
                ao.order_purchase_date as order_purchase_date,
                ao.last_update_date as last_update_date,
                ao.scheduled_delivery_start_date as scheduled_delivery_start_date,
                dictGetOrNull(
                    {$amazonMarketplaceDictName}, 
                    'currency_code', 
                    ao.order_marketplace_id
                ) as currency_id,
                ao.seller_id as seller_id,
                ao.sku as seller_sku,
                ao.order_marketplace_id as marketplace_id,
                CASE 
                    WHEN order_status = '" . Order::ORDER_STATUS_CANCELED . "' 
                    THEN NULL
                    ELSE round(ao.item_price / 100, 2) 
                END as item_price,
                round(
                    (
                        item_price
                        / dictGetOrNull(
                            {$currencyRateDictName}, 
                            'value', 
                            tuple(
                                toDate(ao.order_purchase_date), 
                                dictGetOrNull({$amazonMarketplaceDictName}, 'currency_code', ao.order_marketplace_id)
                            )
                        )
                    ),
                    2
                ) as item_price_eur,
                IF (
                    ao.is_business_order = 1,
                    '$offerTypeB2B',
                    '$offerTypeB2C'
                ) as offer_type,
                CASE 
                    WHEN order_status = '" . Order::ORDER_STATUS_CANCELED . "' 
                    THEN NULL 
                    ELSE ao.quantity 
                END as quantity,
                t_refunded.quantity as quantity_refunded,
                CASE
                    WHEN (
                        t_promotion.count = 0 
                        OR order_status = '" . Order::ORDER_STATUS_CANCELED . "'
                    )
                    THEN 0 
                    ELSE round(t_promotion.amount_eur, 2)
                END as promotion_amount_eur,
                CASE
                    WHEN (
                        t_amazon_fees.count = 0 
                        OR order_status = '" . Order::ORDER_STATUS_CANCELED . "'
                    )
                    THEN NULL 
                    ELSE round(t_amazon_fees.amount_eur, 2)
                END as amazon_fees_amount_eur,
                CASE
                    WHEN (
                        t_expenses.count = 0 
                        OR order_status = '" . Order::ORDER_STATUS_CANCELED . "'
                    )
                    THEN NULL 
                    ELSE 
                        -1 * ROUND(
                            ABS(t_expenses.amount_eur) 
                            - ABS(COALESCE(promotion_amount_eur, 0)) 
                            - ABS(COALESCE(amazon_fees_amount_eur, 0)),
                            2
                        )
                END AS expenses_amount_eur,
                CASE 
                    WHEN (
                        t_revenue.count = 0 
                        OR order_status = '" . Order::ORDER_STATUS_CANCELED . "'
                    )
                    THEN NULL 
                    ELSE round(t_revenue.amount_eur, 2)
                END as revenue_amount_eur,
                CASE 
                    WHEN (
                        (t_expenses.count = 0 AND t_revenue.count = 0 AND t_amazon_fees.count = 0 AND t_promotion.count = 0)
                        OR order_status = '" . Order::ORDER_STATUS_CANCELED . "'
                    )
                    THEN NULL
                    ELSE round(t_revenue.amount_eur + t_expenses.amount_eur, 2)
                END as estimated_profit_amount_eur,
                CASE
                    WHEN (
                        order_status NOT IN ('" . Order::ORDER_STATUS_CANCELED . "')
                        AND ao.item_price > 0
                        AND 
                        (
                            (
                                t_expenses.order_based_count > 0 
                                OR 
                                t_revenue.order_based_count > 0 
                                OR 
                                t_amazon_fees.order_based_count > 0
                                OR 
                                t_promotion.order_based_count > 0
                            )
                            OR
                            (t_expenses.count = 0 AND t_revenue.count = 0 AND t_amazon_fees.count = 0 AND t_promotion.count = 0)
                        )
                    )
                    THEN 1
                    ELSE 0
                END as is_approximate_amounts_calculation,
                {$versionField} as version,
                CASE
                    WHEN pp.id = 0
                    THEN null
                    ELSE pp.id
                END as product_id,
                ao.asin as product_asin,
                pp.ean as product_ean,
                pp.upc as product_upc,
                pp.isbn as product_isbn,
                pp.brand as product_brand,
                pp.manufacturer as product_manufacturer,
                pp.adult_product as product_adult,
                pp.parent_asin as product_parent_asin,
                pp.product_type as product_type,
                CASE
                    WHEN pp.id = 0
                    THEN ao.title
                    ELSE pp.title
                END as product_title,
                CASE
                    WHEN ao.fulfillment_channel = 'AFN' THEN 'FBA'
                    WHEN ao.fulfillment_channel = 'MFN' THEN 'FBM'
                    WHEN pp.stock_type IS NOT NULL THEN pp.stock_type    
                    ELSE NULL
                END as product_stock_type,
                CASE 
                    WHEN ao.condition_id != ''
                    THEN ($conditionCaseWhen)
                    ELSE toInt32OrNull(pp.condition)
                END as product_condition
            FROM $fromClause ao
            ANY LEFT JOIN (
                SELECT 
                    id,
                    title,
                    marketplace_id,
                    seller_id,
                    sku,
                    stock_type,
                    condition,
                    ean,
                    upc,
                    isbn,
                    brand,
                    manufacturer,
                    adult_product,
                    parent_asin,
                    product_type
                FROM {$productProxyTableName}
                WHERE sku IN (
                    SELECT DISTINCT sku
                    FROM {$amazonOrderTableName}
                    WHERE {$orderPurchaseDateFilter}
                )
            ) pp ON (
                pp.marketplace_id = ao.order_marketplace_id 
                AND pp.seller_id = ao.seller_id 
                AND pp.sku = ao.sku
            )
            ANY LEFT JOIN (
                SELECT
                    AmazonOrderId as amazon_order_id,
                    sum(toInt64(AmountEUR) / $moneyAccuracy) as amount_eur,
                    SellerId as seller_id,
                    SellerSKU as seller_sku,
                    count(*) as count,
                    sum(is_order_based) as order_based_count
                FROM (
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        0 as is_order_based
                    FROM {$transactionTableName}
                    WHERE {$postedDateFilter}
                    AND (CategoryId NOT IN (" . implode(', ', $revenueCategoryIds) . ") OR COGCategoryId > 0)
                    AND {$orderBasedExclusionCondition}
                    UNION ALL
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        1 as is_order_based
                    FROM {$orderBasedTransactionTableName}
                    WHERE {$postedDateFilter} 
                    AND (CategoryId NOT IN (" . implode(', ', $revenueCategoryIds) . ") OR COGCategoryId > 0)
                    AND AmazonOrderId IN {$noTransactionOrderIdsFn}
                )
                GROUP BY AmazonOrderId, SellerId, SellerSKU
            ) t_expenses ON (
                t_expenses.amazon_order_id = ao.order_id
                AND
                t_expenses.seller_id = ao.seller_id
                AND
                t_expenses.seller_sku = ao.sku
            )
            ANY LEFT JOIN (
                SELECT
                    AmazonOrderId as amazon_order_id,
                    sum(toInt64(AmountEUR) / $moneyAccuracy) as amount_eur,
                    SellerId as seller_id,
                    SellerSKU as seller_sku,
                    count(*) as count,
                    sum(is_order_based) as order_based_count
                FROM (            
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        0 as is_order_based
                    FROM {$transactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $revenueCategoryIds) . ") AND COGCategoryId = 0
                    AND {$orderBasedExclusionCondition}
                    UNION ALL
                    SELECT
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        1 as is_order_based
                    FROM {$orderBasedTransactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $revenueCategoryIds) . ") AND COGCategoryId = 0
                    AND AmazonOrderId IN {$noTransactionOrderIdsFn}
                )
                GROUP BY AmazonOrderId, SellerId, SellerSKU
            ) t_revenue ON (
                t_revenue.amazon_order_id = ao.order_id
                AND
                t_revenue.seller_id = ao.seller_id
                AND
                t_revenue.seller_sku = ao.sku
            )
            ANY LEFT JOIN (
                SELECT
                    AmazonOrderId as amazon_order_id,
                    sum(toInt64(AmountEUR) / $moneyAccuracy) as amount_eur,
                    SellerId as seller_id,
                    SellerSKU as seller_sku,
                    count(*) as count,
                    sum(is_order_based) as order_based_count
                FROM (   
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        0 as is_order_based
                    FROM {$transactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $promoCategoryIds). ") 
                    AND {$orderBasedExclusionCondition}
                    UNION ALL
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        1 as is_order_based
                    FROM {$orderBasedTransactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $promoCategoryIds). ") 
                    AND AmazonOrderId IN {$noTransactionOrderIdsFn}
                )
                GROUP BY AmazonOrderId, SellerId, SellerSKU
            ) t_promotion ON (
                t_promotion.amazon_order_id = ao.order_id
                AND
                t_promotion.seller_id = ao.seller_id
                AND
                t_promotion.seller_sku = ao.sku
            )
            ANY LEFT JOIN (
                SELECT
                    AmazonOrderId as amazon_order_id,
                    sum(toInt64(AmountEUR) / $moneyAccuracy) as amount_eur,
                    SellerId as seller_id,
                    SellerSKU as seller_sku,
                    count(*) as count,
                    sum(is_order_based) as order_based_count
                FROM (   
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        0 as is_order_based
                    FROM {$transactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $amazonFeesCategoryIds). ") 
                    AND {$orderBasedExclusionCondition}
                    UNION ALL
                    SELECT 
                        AmazonOrderId,
                        AmountEUR,
                        SellerId,
                        SellerSKU,
                        1 as is_order_based
                    FROM {$orderBasedTransactionTableName}
                    WHERE {$postedDateFilter}
                    AND CategoryId IN (" . implode(', ', $amazonFeesCategoryIds). ") 
                    AND AmazonOrderId IN {$noTransactionOrderIdsFn}
                )
                GROUP BY AmazonOrderId, SellerId, SellerSKU
            ) t_amazon_fees ON (
                t_amazon_fees.amazon_order_id = ao.order_id
                AND
                t_amazon_fees.seller_id = ao.seller_id
                AND
                t_amazon_fees.seller_sku = ao.sku
            )
            ANY LEFT JOIN (
                SELECT 
                    AmazonOrderId as amazon_order_id,
                    sum(Quantity) as quantity,
                    count(*) as count,
                    SellerId as seller_id,
                    SellerSKU as seller_sku
                FROM {$transactionTableName}
                WHERE {$postedDateFilter}
                AND CategoryId = {$organicRefundsCategoryId}
                AND COGCategoryId = 0
                GROUP BY AmazonOrderId, SellerId, SellerSKU
            ) t_refunded ON (
                t_refunded.amazon_order_id = ao.order_id
                AND
                t_refunded.seller_id = ao.seller_id
                AND
                t_refunded.seller_sku = ao.sku
            )
            WHERE {$orderPurchaseDateFilter}
        ";

        return $sql;
    }
}
