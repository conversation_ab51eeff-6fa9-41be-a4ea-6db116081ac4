<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDictV1;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\PopulateAwareInterface;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\PpcCostsLastFewDaysTransaction;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedViewV1 as TransactionExtendedViewV1Model;
use common\models\FinanceEventCategory;
use yii\db\Expression;

class TransactionExtendedViewV1 implements DynamicTableInterface, CronAwareInterface, PopulateAwareInterface
{
    use LogToConsoleTrait;

    protected TransactionQueryExecutor $transactionQueryExecutor;
    protected DbManager $dbManager;

    protected const DATA_TARGET_CLASS = Transaction::class;
    protected const MODEL_CLASS = TransactionExtendedViewV1Model::class;

    protected bool $isAutoPopulate = true;

    public function __construct(bool $isAutoPopulate = true)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->transactionQueryExecutor = new TransactionQueryExecutor();
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_VIEW;
    }

    public function getName(): string
    {
        return (static::MODEL_CLASS)::tableName();
    }

    public function getCronExpression(): string
    {
        return "00 02 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return false;
    }

    public function getSQL(): string
    {
        $dbHelper = new ClickhouseDbHelper();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedSummingMergeTree('%s', '{replica}', (quantity, amount, amount_eur, merge_counter))",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'SummingMergeTree((quantity, amount, amount_eur, merge_counter))';

        $maxExecutionTime = 60 * 40;
        $selectSQL = $this->getTransactionSelectStatement();

        $sql = "CREATE MATERIALIZED VIEW IF NOT EXISTS {table_name} {cluster_part}
            (
                `posted_date` DateTime,
                `transaction_date` DateTime,
                `marketplace_id` String,
                `seller_id` String,
                `seller_sku` String,
                `seller_order_id` String,
                `amazon_order_id` String,
                `category_id` Int32,
                `cog_category_id` Int32,
                `indirect_cost_id` Int32,
                `indirect_cost_type_id` Int32,
                `quantity` Int32,
                `amount` Int64,
                `amount_eur` Int64,
                `currency_code` String,
                `merge_counter` Int32,
                `created_at` DateTime,
                `transaction_type` String,
                `offer_type` Nullable(String),
                `finance_event_category_path` Nullable(String),
                `sales_category_is_visible` Bool,
                `product_id` Nullable(Int64),
                `product_asin` Nullable(String),
                `product_ean` Nullable(String),
                `product_upc` Nullable(String),
                `product_isbn` Nullable(String),
                `product_brand` Nullable(String),
                `product_type` Nullable(String),
                `product_manufacturer` Nullable(String),
                `product_adult` Nullable(Bool),
                `product_title` Nullable(String),
                `product_parent_asin` Nullable(String),
                `product_stock_type` Nullable(String),
                `product_condition` Nullable(String),
                `tag_id` Array(Int32),
                `sales_category_path` String,
                `sales_category_depth_1` String,
                `sales_category_depth_2` Nullable(String),
                `sales_category_depth_3` Nullable(String),
                `sales_category_depth_4` Nullable(String),
                `transaction_level` Nullable(String)
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(transaction_date)
            PRIMARY KEY (transaction_date, posted_date, marketplace_id, seller_id, seller_sku, seller_order_id, amazon_order_id, category_id, cog_category_id, indirect_cost_id, indirect_cost_type_id, currency_code)
            SETTINGS index_granularity = 8192
            AS
            {$selectSQL}
            SETTINGS max_execution_time = {$maxExecutionTime}
        ";

        return $sql;
    }

    public function getPopulateSQL(string $tableName): \Iterator
    {
        if (!$this->isAutoPopulate) {
            return;
        }

        $maxDateTime = (new \DateTime());
        $minDateTime = $this->dbManager->getMinimumStatisticDate();

        $dateTimeTo = clone $maxDateTime;
        $dateTimeFrom = (clone $maxDateTime)->modify('+1 second');
        $iterationPeriod = '-3 month';

        while (true) {
            $dateTimeFrom->modify($iterationPeriod);
            $this->info("Posted date: " . $dateTimeFrom->format('Y-m-d H:i:s') . ' => ' . $dateTimeTo->format('Y-m-d H:i:s'));
            yield $this->getTransactionSelectStatement($dateTimeFrom, $dateTimeTo);

            $dateTimeTo = (clone $dateTimeFrom)->modify('-1 second');
            if ($dateTimeTo <= $minDateTime) {
                $this->info("Populating finished");
                break;
            }
        }
    }

    protected function getTransactionSelectStatement(
        \DateTime $fromDateTime = null,
        \DateTime $toDateTime = null
    ): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $ppcLastFewDaysTableName = PpcCostsLastFewDaysTransaction::tableName();
        $transactionTableName = (static::DATA_TARGET_CLASS)::tableName();
        $transactionSourceType = explode('.', $transactionTableName)[1];
        $productDistTableName = $dbManager
                ->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER) . '.proxy_product';

        $productTagDistTableName = $dbManager
                ->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER) . '.proxy_product_tag';
        $financeEventCategoryDictName = 'default.finance_event_category_dict';

        $offerTypeB2B = \common\models\order\AmazonOrder::OFFER_TYPE_B2B;
        $offerTypeB2C = \common\models\order\AmazonOrder::OFFER_TYPE_B2C;

        $plusPostfix = FinanceEventCategory::PLUS_POSTFIX;
        $zeroPlusPostfix = FinanceEventCategory::PLUS_ZERO_POSTFIX;
        $isManualPopulate = !empty($fromDateTime) && !empty($toDateTime);
        $postedDateFilter = $isManualPopulate
            ? "(
                    PostedDate BETWEEN toDateTime('{$fromDateTime->format('Y-m-d H:i:s')}') 
                    AND 
                    toDateTime('{$toDateTime->format('Y-m-d H:i:s')}')
                )"
            : "1 = 1";

        $amazonOrderTableName = AmazonOrder::tableName();
        $amazonOrderInProgressTableName = AmazonOrderInProgress::tableName();

        $salesCategoryExtendedDictName = (new SalesCategoryExtendedDictV1())->getName();
        $internalCOGPrefix = ProductCostCategory::INTERNAL_COG_PREFIX;
        $internalIndirectCostPrefix = ProductCostCategory::INTERNAL_INDIRECT_COST_PREFIX;
        $salesCategoryStrategyType = SalesCategoryStrategyFactory::STRATEGY_CUSTOM;

        $fromQueryPart = "(
            SELECT '$transactionSourceType' as SourceTable, PostedDate, SellerId, MarketplaceId, CategoryId, EventPeriodId, SellerSKU, ASIN, SellerOrderId, AmazonOrderId, Quantity, Amount, Currency, AmountEUR, MergeCounter, COGCategoryId, CreatedAt, TransactionDate, IndirectCostId, IndirectCostTypeId
            FROM $transactionTableName
        )";

        if ($isManualPopulate && !($transactionSourceType === 'order_based_transaction')) {
            $fromQueryPart = "(
            SELECT '$transactionSourceType' as SourceTable, PostedDate, SellerId, MarketplaceId, CategoryId, EventPeriodId, SellerSKU, ASIN, SellerOrderId, AmazonOrderId, Quantity, Amount, Currency, AmountEUR, MergeCounter, COGCategoryId, CreatedAt, TransactionDate, IndirectCostId, IndirectCostTypeId
            FROM $transactionTableName
            WHERE $postedDateFilter
            UNION ALL
            SELECT 'ppc' as SourceTable, PostedDate, SellerId, MarketplaceId, CategoryId, EventPeriodId, SellerSKU, ASIN, SellerOrderId, AmazonOrderId, Quantity, Amount, Currency, AmountEUR, MergeCounter, COGCategoryId, CreatedAt, TransactionDate, IndirectCostId, IndirectCostTypeId
            FROM $ppcLastFewDaysTableName
            WHERE $postedDateFilter
        )";
        }


        $sql = "SELECT 
            PostedDate as posted_date,
            TransactionDate as transaction_date,
            MarketplaceId as marketplace_id,
            SellerId as seller_id,
            SellerSKU as seller_sku,
            SellerOrderId as seller_order_id,
            AmazonOrderId as amazon_order_id,
            CategoryId as category_id,
            COGCategoryId as cog_category_id,
            IndirectCostId as indirect_cost_id,
            IndirectCostTypeId as indirect_cost_type_id,
            Quantity as quantity,
            Amount as amount,
            AmountEUR as amount_eur,
            Currency as currency_code,
            MergeCounter as merge_counter,
            CreatedAt as created_at,
            IF (
                COGCategoryId > 0 
                OR IndirectCostTypeId > 0
                OR position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'Custom.') > 0,
                '" . TransactionExtendedViewV1Model::TRANSACTION_TYPE_MANUAL.  "',
                IF (
                    SourceTable = 'order_based_transaction',
                    '" . TransactionExtendedViewV1Model::TRANSACTION_TYPE_ESTIMATED . "',
                    IF (
                        (
                            position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'Adjustment.') = 1
                            OR (
                                position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), '{$plusPostfix}') > 0
                                AND
                                position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), '{$zeroPlusPostfix}') = 0
                                AND
                                (
                                    position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), '.FeeAmount') > 0
                                    AND
                                    position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'Refund.') = 0
                                    AND
                                    position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'Chargeback.') = 0
                                    AND
                                    position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'GuaranteeClaim.') = 0
                                )
                            )
                        ),
                        '" . TransactionExtendedViewV1Model::TRANSACTION_TYPE_ADJUSTMENT . "',
                        IF (
                            position(dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId), 'Retrocharge.') = 1,
                            '" . TransactionExtendedViewV1Model::TRANSACTION_TYPE_RETROCHARGE . "',
                            '" . TransactionExtendedViewV1Model::TRANSACTION_TYPE_STANDARD . "'
                        )
                    )
                )
            ) as transaction_type,
            IF (
                ao.is_business_order IS NULL,
                NULL,
                IF(ao.is_business_order = 1, '$offerTypeB2B', '$offerTypeB2C')
            ) as offer_type,
            dictGetOrNull({$financeEventCategoryDictName}, 'path', CategoryId) as finance_event_category_path,
            IF (
                COGCategoryId > 0 OR IndirectCostTypeId > 0,
                1,
                dictGetOrNull(
                    {$financeEventCategoryDictName}, 
                    'sales_category_is_visible', 
                    CategoryId
                ) 
            ) as sales_category_is_visible,
            pd.id as product_id,
            pd.asin as product_asin,
            pd.ean as product_ean,
            pd.upc as product_upc,
            pd.isbn as product_isbn,
            pd.brand as product_brand,
            pd.product_type as product_type,
            pd.manufacturer as product_manufacturer,
            pd.adult_product as product_adult,
            pd.title as product_title,
            pd.parent_asin as product_parent_asin,
            CASE
                WHEN ao.fulfillment_channel = 'AFN' THEN 'FBA'
                WHEN ao.fulfillment_channel = 'MFN' THEN 'FBM'
                ELSE pd.stock_type
            END as product_stock_type,
            pd.condition as product_condition,
            tags as tag_id,
            dictGetOrNull(
                {$salesCategoryExtendedDictName},
                'path',
                tuple(
                    coalesce(
                        CASE
                            WHEN COGCategoryId > 0
                                THEN concat('$internalCOGPrefix', toString(COGCategoryId))
                            WHEN IndirectCostTypeId > 0
                                THEN concat('$internalIndirectCostPrefix', toString(IndirectCostTypeId))
                            ELSE dictGetOrNull(
                                    {$financeEventCategoryDictName},
                                    'sales_category_id_custom',
                                    CategoryId
                                )
                            END,
                        ''
                    ),
                    '{$salesCategoryStrategyType}'
                )
            ) AS sales_category_path,
            NULLIF(arrayElement(splitByChar('|', coalesce(sales_category_path, '')), 1), '') AS sales_category_depth_1,
            NULLIF(arrayElement(splitByChar('|', coalesce(sales_category_path, '')), 2), '') AS sales_category_depth_2,
            null AS sales_category_depth_3,
            null AS sales_category_depth_4,
            CASE 
                WHEN (
                    SellerId = ''
                    AND 
                    MarketplaceId = ''
                    AND
                    SellerSKU = ''
                    AND
                    AmazonOrderId = ''
                ) 
                THEN '" . TransactionExtendedViewV1Model::TRANSACTION_LEVEL_GLOBAL .  "'
                WHEN (
                    SellerId != ''
                    AND 
                    MarketplaceId = ''
                    AND
                    SellerSKU = ''
                    AND
                    AmazonOrderId = ''
                ) 
                THEN '" . TransactionExtendedViewV1Model::TRANSACTION_LEVEL_ACCOUNT .  "'
                WHEN (
                    MarketplaceId != ''
                    AND
                    SellerSKU = ''
                    AND
                    AmazonOrderId = ''
                ) 
                THEN '" . TransactionExtendedViewV1Model::TRANSACTION_LEVEL_MARKETPLACE .  "'
                WHEN (
                    SellerSKU = ''
                    AND
                    AmazonOrderId != ''
                ) 
                THEN '" . TransactionExtendedViewV1Model::TRANSACTION_LEVEL_ORDER .  "'
                WHEN (
                    SellerSKU != ''
                ) 
                THEN '" . TransactionExtendedViewV1Model::TRANSACTION_LEVEL_PRODUCT .  "'
            END as transaction_level
        FROM {$fromQueryPart} as t
        ANY LEFT JOIN (
            SELECT
                order_id, 
                is_business_order,
                fulfillment_channel
            FROM {$amazonOrderTableName}
            WHERE order_id IN (
                SELECT DISTINCT AmazonOrderId
                FROM $transactionTableName 
                WHERE 1 = 1
                AND $postedDateFilter
            )
            UNION ALL
            SELECT
                order_id, 
                is_business_order,
                fulfillment_channel
            FROM {$amazonOrderInProgressTableName}
            WHERE order_id IN (
                SELECT DISTINCT AmazonOrderId
                FROM $transactionTableName 
                WHERE 1 = 1
                AND $postedDateFilter
            )
        ) ao ON (
            ao.order_id = t.AmazonOrderId
        )
        LEFT JOIN (
            SELECT 
                id,
                asin,
                ean,
                upc,
                isbn,
                brand,
                product_type,
                manufacturer,
                adult_product,
                parent_asin,
                title,
                stock_type,
                condition,
                marketplace_id,
                seller_id,
                sku
            FROM {$productDistTableName}
            WHERE sku IN (
                SELECT DISTINCT SellerSKU
                FROM $transactionTableName 
                WHERE 1 = 1
                AND $postedDateFilter
            )
        ) pd ON (
            pd.marketplace_id = MarketplaceId
            AND pd.seller_id = SellerId 
            AND pd.sku = SellerSKU
        )
        LEFT JOIN (
            SELECT 
                product_id,
                groupUniqArray(tag_id) AS tags
            FROM {$productTagDistTableName}
            WHERE tag_id IS NOT NULL
            GROUP BY product_id
        ) AS pt
        ON pt.product_id = pd.id
        WHERE 1 = 1
        AND $postedDateFilter
        AND (CategoryId > 0 OR COGCategoryId > 0 OR IndirectCostTypeId > 0)
        ";

        return $sql;
    }
}
