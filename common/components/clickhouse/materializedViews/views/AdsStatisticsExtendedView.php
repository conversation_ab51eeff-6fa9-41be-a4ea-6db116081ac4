<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CustomFunctionPopulateAwareInterface;
use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\PopulateAwareInterface;
use common\components\clickhouse\materializedViews\tables\AbstractTransactionStructureTable;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\AdsStatistics;
use common\models\customer\clickhouse\AdsStatisticsExtendedView as AdsStatisticsViewModel;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\ProxyFbaEstimatedFeeHistory;
use common\models\customer\clickhouse\ProxyProduct;
use common\models\customer\clickhouse\ProxyProductTag;
use DateTime;

class AdsStatisticsExtendedView implements DynamicTableInterface, CronAwareInterface, PopulateAwareInterface
{
    use LogToConsoleTrait;

    protected bool $isAutoPopulate = true;
    protected DbManager $dbManager;
    public ?DateTime $dateStart = null;
    public ?DateTime $dateEnd = null;

    public function __construct(bool $isAutoPopulate = true, $dateStart = null, $dateEnd = null)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->dbManager = \Yii::$app->dbManager;
        $this->dateStart = $dateStart;
        $this->dateEnd = $dateEnd;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_VIEW;
    }

    public function getName(): string
    {
        return AdsStatisticsViewModel::tableName();
    }

    public function getCronExpression(): string
    {
        return "0 */6 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return true;
    }

    public function getSQL(): string
    {
        $maxExecutionTime = 60 * 40;

        $dbHelper = new ClickhouseDbHelper();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedMergeTree('%s', '{replica}')",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'MergeTree';

        $selectSQL = $this->getAdsSelectStatement();
        $sql = "CREATE MATERIALIZED VIEW IF NOT EXISTS {table_name} {cluster_part}
            (
                `id` Int64,
                `seller_id` String,
                `seller_sku` Nullable(String),
                `product_asin` String,
                `marketplace_id` String,
                `sales` Float64,
                `sales_eur` Float64,
                `cost` Float64,
                `cost_eur` Float64,
                `clicks` Int32,
                `currency_code` String,
                `date` DateTime,
                `type` String,
                `created_at` DateTime,
                `product_id` Nullable(Int64),
                `product_ean` Nullable(String),
                `product_upc` Nullable(String),
                `product_isbn` Nullable(String),
                `product_brand` Nullable(String),
                `product_manufacturer` Nullable(String),
                `product_adult` Nullable(Boolean),
                `product_parent_asin` Nullable(String),
                `product_type` Nullable(String),
                `product_title` Nullable(String),
                `product_stock_type` Nullable(String),
                `product_condition` Nullable(String),
                `tag_id` Array(Int32)
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(date)
            PRIMARY KEY (id, seller_id, date, type)
            AS
            {$selectSQL}
            SETTINGS max_execution_time = {$maxExecutionTime}
        ";

        return $sql;
    }


    protected function getAdsSelectStatement(
        ?\DateTime $fromDateTime = null,
        ?\DateTime $toDateTime = null
    ): string
    {
        if ($this->isTempStub) {
            return "SELECT 1";
        }

        $adsStatisticsTableName = AdsStatistics::tableName();
        $productProxyTableName = ProxyProduct::tableName();
        $productTagTableName = ProxyProductTag::tableName();
        $currencyRateDictName = 'default.currency_rate_dict';

        $isManualPopulate = !empty($fromDateTime) && !empty($toDateTime);

        $postedDateFilterWithoutTime = $isManualPopulate
            ? "(
                    date BETWEEN toDateTime('{$fromDateTime->format('Y-m-d')}')
                    AND
                    toDateTime('{$toDateTime->format('Y-m-d')}')
                )"
            : "1 = 1";

        $sql = "SELECT
                ads.id,
                ads.seller_id,
                ads.seller_sku,
                ads.product_asin,
                ads.marketplace_id,
                ads.sales,
                multiIf(
                    ads.currency_code = 'EUR', ads.sales,
                    dictGetOrNull('{$currencyRateDictName}', 'value', tuple(ads.date, ads.currency_code)) > 0,
                    round(ads.sales / dictGetOrNull('{$currencyRateDictName}', 'value', tuple(ads.date, ads.currency_code)), 2),
                    0
                ) as sales_eur,
                ads.cost,
                multiIf(
                    ads.currency_code = 'EUR', ads.cost,
                    dictGetOrNull('{$currencyRateDictName}', 'value', tuple(ads.date, ads.currency_code)) > 0,
                    round(ads.cost / dictGetOrNull('{$currencyRateDictName}', 'value', tuple(ads.date, ads.currency_code)), 2),
                    0
                ) as cost_eur,
                ads.clicks,
                ads.currency_code,
                ads.date,
                ads.type,
                ads.created_at,
                p.id as product_id,
                p.ean as product_ean,
                p.upc as product_upc,
                p.isbn as product_isbn,
                p.brand as product_brand,
                p.manufacturer as product_manufacturer,
                p.adult_product as product_adult,
                p.parent_asin as product_parent_asin,
                p.product_type,
                p.title as product_title,
                p.stock_type as product_stock_type,
                p.condition as product_condition,
                arrayDistinct(arrayFilter(x -> x != 0 AND x IS NOT NULL, groupArray(pt.tag_id))) as product_tags
            FROM {$adsStatisticsTableName} ads
            ANY LEFT JOIN (
                SELECT 
                    id,
                    title,
                    marketplace_id,
                    seller_id,
                    sku,
                    stock_type,
                    condition,
                    ean,
                    upc,
                    isbn,
                    brand,
                    manufacturer,
                    adult_product,
                    parent_asin,
                    product_type
                FROM {$productProxyTableName}
                WHERE sku IN (
                    SELECT DISTINCT seller_sku
                    FROM {$adsStatisticsTableName}
                    WHERE {$postedDateFilterWithoutTime}
                )
            ) p ON (
                p.marketplace_id = ads.marketplace_id 
                AND p.seller_id = ads.seller_id 
                AND p.sku = ads.seller_sku
            )
            LEFT JOIN {$productTagTableName} pt ON (
                pt.product_id = p.id
            )
            WHERE {$postedDateFilterWithoutTime}
            GROUP BY
                ads.id, ads.seller_id, ads.seller_sku, ads.marketplace_id, ads.sales, ads.cost, ads.clicks,
                ads.currency_code, ads.created_at, ads.date, ads.type, p.id, ads.product_asin,
                product_ean, product_upc, product_isbn, product_brand, product_manufacturer,
                product_adult, product_parent_asin, product_type, product_title,
                product_stock_type, product_condition, pt.tag_id
        ";

        return $sql;
    }

    public function populate(string $tableName): void
    {
        $this->info("View created successfully: {$tableName}");
    }

    public function getPopulateSQL(string $tableName): \Iterator
    {
        if (!$this->isAutoPopulate) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $maxDateTime = (new \DateTime());
        $minDateTime = $dbManager->getMinimumStatisticDate(false);

        $dateTimeTo = clone $maxDateTime;
        $dateTimeFrom = (clone $maxDateTime)->modify('+1 second');
        $iterationPeriod = '-3 month';

        while (true) {
            $dateTimeFrom->modify($iterationPeriod);
            $this->info("Posted date: " . $dateTimeFrom->format('Y-m-d H:i:s') . ' => ' . $dateTimeTo->format('Y-m-d H:i:s'));
            yield $this->getAdsSelectStatement($dateTimeFrom, $dateTimeTo);

            $dateTimeTo = (clone $dateTimeFrom)->modify('-1 second');
            if ($dateTimeTo <= $minDateTime) {
                $this->info("Populating finished");
                break;
            }
        }
    }
}
