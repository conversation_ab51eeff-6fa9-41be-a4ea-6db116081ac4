<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\PopulateAwareInterface;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\ProxyFbaEstimatedFeeHistory;
use common\models\customer\clickhouse\Transaction;
use yii\db\Expression;

class FbaFeeFactorView implements DynamicTableInterface, CronAwareInterface, PopulateAwareInterface
{
    use LogToConsoleTrait;

    protected TransactionQueryExecutor $transactionQueryExecutor;

    protected bool $isAutoPopulate = true;
    protected bool $isTempStub = false;

    public function __construct(bool $isAutoPopulate = true, bool $isTempStub = false)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->transactionQueryExecutor = new TransactionQueryExecutor();
        $this->isTempStub = $isTempStub;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_VIEW;
    }

    public function getName(): string
    {
        return \common\models\customer\clickhouse\FbaFeeFactorView::tableName();
    }

    public function getCronExpression(): string
    {
        return "00 00 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return false;
    }

    public function getSQL(): string
    {
        $dbHelper = new ClickhouseDbHelper();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");

        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedMergeTree('%s', '{replica}')",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'MergeTree';

        $maxExecutionTime = 60 * 40;
        $selectSQL = $this->getTransactionSelectStatement();

        $sql = "CREATE MATERIALIZED VIEW IF NOT EXISTS {table_name} {cluster_part}
            (
                `posted_date` DateTime,
                `marketplace_id` String,
                `seller_id` String,
                `seller_sku` String,
                `amazon_order_id` String,
                `order_item_id` String,
                `product_id` Nullable(Int64),
                `product_asin` Nullable(String),
                `product_title` Nullable(String),
                `fba_fee` Float,
                `fba_fee_eur` Float,
                `fba_fee_currency` String,
                `order_item_price` Float,
                `order_item_price_eur` Float,
                `order_currency_code` String,
                `fba_fee_item_price` Float,
                `estimated_fba_fee_per_item` Float,
                `estimated_fba_fee_per_item_eur` Float,
                `estimated_fba_fee_currency` String,
                `estimated_fba_fee_date` Date
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(posted_date)
            PRIMARY KEY (posted_date, marketplace_id, seller_id, seller_sku, amazon_order_id, estimated_fba_fee_date)
            SETTINGS index_granularity = 8192
            AS
            {$selectSQL}
            SETTINGS max_execution_time = {$maxExecutionTime}
        ";

        return $sql;
    }

    public function getPopulateSQL(string $tableName): \Iterator
    {
        if (!$this->isAutoPopulate) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $maxDateTime = (new \DateTime());
        $minDateTime = $dbManager->getMinimumStatisticDate(false);

        $dateTimeTo = clone $maxDateTime;
        $dateTimeFrom = (clone $maxDateTime)->modify('+1 second');
        $iterationPeriod = '-3 month';

        while (true) {
            $dateTimeFrom->modify($iterationPeriod);
            $this->info("Posted date: " . $dateTimeFrom->format('Y-m-d H:i:s') . ' => ' . $dateTimeTo->format('Y-m-d H:i:s'));
            yield $this->getTransactionSelectStatement($dateTimeFrom, $dateTimeTo);

            $dateTimeTo = (clone $dateTimeFrom)->modify('-1 second');
            if ($dateTimeTo <= $minDateTime) {
                $this->info("Populating finished");
                break;
            }
        }
    }

    protected function getTransactionSelectStatement(
        ?\DateTime $fromDateTime = null,
        ?\DateTime $toDateTime = null
    ): string
    {
        if ($this->isTempStub) {
            return "SELECT 1";
        }

        $transactionTableName = \common\models\customer\TransactionExtendedViewV1::tableName();
        $proxyFbaEstimatedFeeHistoryTableName = ProxyFbaEstimatedFeeHistory::tableName();

        $isManualPopulate = !empty($fromDateTime) && !empty($toDateTime);
        $postedDateFilter = $isManualPopulate
            ? "(
                    posted_date BETWEEN toDateTime('{$fromDateTime->format('Y-m-d H:i:s')}') 
                    AND 
                    toDateTime('{$toDateTime->format('Y-m-d H:i:s')}')
                )"
            : "1 = 1";

        $postedDateFilterWithoutTime = $isManualPopulate
            ? "(
                    date BETWEEN toDateTime('{$fromDateTime->format('Y-m-d')}') 
                    AND 
                    toDateTime('{$toDateTime->format('Y-m-d')}')
                )"
            : "1 = 1";

        $amazonOrderTableName = AmazonOrder::tableName();
        $amazonOrderInProgressTableName = AmazonOrderInProgress::tableName();

        $sql = "SELECT 
            posted_date,
            t.marketplace_id,
            t.seller_id,
            t.seller_sku,
            amazon_order_id,
            order_item_id,
            product_id,
            product_asin,
            product_title,
            round((amount / 10000 * -1), 2) as fba_fee,
            round((amount_eur / 10000 * -1), 2) as fba_fee_eur,
            t.currency_code as fba_fee_currency,
            (`ao`.`item_price` / 100) as order_item_price,
            round(
                        (order_item_price) /
                        dictGetOrNull(
                                default.currency_rate_dict,
                                'value',
                                tuple(toDate(posted_date), order_currency_code)
                            ),
                        2
            ) as order_item_price_eur,
            `ao`.`currency_code` as order_currency_code,
            `fef`.`sales_price` as fba_fee_item_price,
            `fef`.`expected_domestic_fulfilment_fee_per_unit` as estimated_fba_fee_per_item,
            round((estimated_fba_fee_per_item / fba_fee_item_price) * (`order_item_price_eur`) , 2) as estimated_fba_fee_per_item_eur,
            `fef`.`currency` as estimated_fba_fee_currency,
            `fef`.`date` as estimated_fba_fee_date
        FROM {$transactionTableName} as t
        ANY LEFT JOIN (
            SELECT *
            FROM {$proxyFbaEstimatedFeeHistoryTableName}
            WHERE 1 = 1
            AND $postedDateFilterWithoutTime
        ) fef ON (
            fef.marketplace_id = t.marketplace_id
            AND fef.seller_id = t.seller_id 
            AND fef.sku = t.seller_sku
        )
        ANY LEFT JOIN (
            SELECT
                order_id, 
                order_item_id,
                item_price,
                currency_code,
                quantity
            FROM {$amazonOrderTableName}
            WHERE order_id IN (
                SELECT DISTINCT amazon_order_id
                FROM $transactionTableName 
                WHERE 1 = 1
                AND $postedDateFilter
            )
            UNION ALL
            SELECT
                order_id,
                order_item_id,
                item_price,
                currency_code,
                quantity
            FROM {$amazonOrderInProgressTableName}
            WHERE order_id IN (
                SELECT DISTINCT amazon_order_id
                FROM $transactionTableName 
                WHERE 1 = 1
                AND $postedDateFilter
            )
        ) ao ON (
            ao.order_id = t.amazon_order_id
        )
        WHERE 1 = 1
        AND $postedDateFilter
        AND 
          (finance_event_category_path LIKE 'Shipment.ShipmentItem.ItemFee.FBAPerUnitFulfillmentFee.FeeAmount%' )
        AND round((estimated_fba_fee_per_item / fba_fee_item_price) * (`order_item_price_eur`) , 2) <> `amount_eur`
        AND toDate(`posted_date`) = toDate(`date`)
        ";

        return $sql;
    }
}
