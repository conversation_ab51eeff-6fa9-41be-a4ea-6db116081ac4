<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\models\customer\clickhouse\AmazonOrderInProgress;

class AmazonOrderInProgressExtendedView extends AmazonOrderExtendedView implements DynamicTableInterface, CronAwareInterface
{
    protected bool $isAutoPopulate = true;

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_VIEW;
    }

    public function getName(): string
    {
        return \common\models\customer\clickhouse\AmazonOrderInProgressExtendedView::tableName();
    }

    public function getCronExpression(): string
    {
        return "00 01 * * *";
    }

    protected function getTargetTableName(): string
    {
        return AmazonOrderInProgress::tableName();
    }

    protected function getMinOrderDate(): ?string
    {
        return (new \DateTime())->modify('-1 years')->format('Y-m-d H:i:s');
    }
}