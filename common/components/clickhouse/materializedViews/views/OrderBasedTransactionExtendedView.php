<?php

namespace common\components\clickhouse\materializedViews\views;

use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\PopulateAwareInterface;
use common\models\customer\clickhouse\OrderBasedTransaction;

final class OrderBasedTransactionExtendedView extends TransactionExtendedView implements DynamicTableInterface, CronAwareInterface, PopulateAwareInterface
{
    protected const DATA_TARGET_CLASS = OrderBasedTransaction::class;
    protected const MODEL_CLASS = \common\models\customer\OrderBasedTransactionExtendedView::class;
    protected bool $isAutoPopulate = true;

}
