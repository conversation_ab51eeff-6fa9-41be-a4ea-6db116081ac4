<?php

namespace common\components\clickhouse\materializedViews\tables;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\CustomFunctionPopulateAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\AdsStatistics;
use DateTime;
use yii\db\Exception;
use yii\db\Query;

class AdsStatisticsTable extends AbstractTransactionStructureTable implements CronAwareInterface, CustomFunctionPopulateAwareInterface
{
    use LogToConsoleTrait;

    protected bool $isAutoPopulate = true;
    protected DbManager $dbManager;
    public ?DateTime $dateStart = null;
    public ?DateTime $dateEnd = null;

    public function __construct(bool $isAutoPopulate = true, $dateStart = null, $dateEnd = null)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->dbManager = \Yii::$app->dbManager;
        $this->dateStart = $dateStart;
        $this->dateEnd = $dateEnd;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        return AdsStatistics::tableName();
    }

    public function getCronExpression(): string
    {
        return "0 */6 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return true;
    }

    public function getSQL(): string
    {
        $dbHelper = new ClickhouseDbHelper();
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedReplacingMergeTree('%s', '{replica}')",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'ReplacingMergeTree';

        $sql = "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
            (
                `id` Int64,
                `seller_id` String,
                `seller_sku` Nullable(String),
                `product_asin` String,
                `marketplace_id` String,
                `sales` Float64,
                `cost` Float64,
                `clicks` Int32,
                `currency_code` String,
                `date` Date,
                `type` String,
                `created_at` DateTime,
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(date)
            PRIMARY KEY (id, type, seller_id, product_asin)
            ORDER BY (id, type, seller_id, product_asin, date)
        ";

        return $sql;
    }

    public function populate(string $tableName): void
    {
        if (!$this->isAutoPopulate || $this->dbManager->isDemo()) {
            return;
        }

        $maxDateTime = $this->dateEnd ?? (new \DateTime());
        $minDateTime = $this->dateStart ?? $this->getMinDateFromAdsTables();

        if ($minDateTime > $maxDateTime) {
            return;
        }

        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        $client = $connection->getClient();

        $currentPeriodStart = clone $minDateTime;
        $currentPeriodStart->setDate($currentPeriodStart->format('Y'), $currentPeriodStart->format('m'), 1);
        $currentPeriodStart->setTime(0, 0, 0);

        while ($currentPeriodStart < $maxDateTime) {
            $currentPeriodEnd = clone $currentPeriodStart;
            $currentPeriodEnd->modify('+1 month -1 day');
            $currentPeriodEnd->setTime(23, 59, 59);

            if ($currentPeriodEnd > $maxDateTime) {
                $currentPeriodEnd = clone $maxDateTime;
            }

            $this->info("Processing period: " . $currentPeriodStart->format('Y-m-d') . " to " . $currentPeriodEnd->format('Y-m-d'));

            if ($this->dateStart !== null || $this->dateEnd !== null) {
                $this->info("Deleting existing data for period: " . $currentPeriodStart->format('Y-m-d') . " to " . $currentPeriodEnd->format('Y-m-d'));
                $client->write("ALTER TABLE {$tableName} DELETE WHERE date BETWEEN toDate('{$currentPeriodStart->format('Y-m-d')}') AND toDate('{$currentPeriodEnd->format('Y-m-d')}')");
            }

            $dateStart = $currentPeriodStart->format('Y-m-d');
            $dateEnd = $currentPeriodEnd->format('Y-m-d');

            $this->processAllDataInOneBatch($client, $tableName, $dateStart, $dateEnd);

            $currentPeriodStart->modify('+1 month');
        }
    }

    /**
     * @throws \Throwable
     * @throws Exception
     */
    protected function processAllDataInOneBatch(
        Client $client,
        string $tableName,
        string $dateStart,
        string $dateEnd
    ): void {
        $this->info("Processing all data for period: {$dateStart} to {$dateEnd}");

        $pgConfig = $this->dbManager->getShardPostgressConfig();

        $pgHost = $pgConfig['dbSlaveHost'];
        $pgPort = $pgConfig['dbSlavePort'];
        $pgUser = $pgConfig['dbUserSlave'];
        $pgPassword = $pgConfig['dbPasswordSlave'];
        $pgDatabase = 'profit_dash_db';

        $spQuery = (new Query())
            ->select([
                'sp.id as id',
                'sp.profile_id as profile_id',
                'sp.sku as sku',
                'sp.asin as product_asin',
                'sp.attributed_sales_same_sku_14d as sales',
                'sp.cost as cost',
                'sp.clicks as clicks',
                'sp.currency_code as currency_code',
                'sp.created_at as created_at',
                'sp.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
                "CAST('sp' AS VARCHAR) as type",
            ])
            ->from(["sp" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sp_advertised_product', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"])
            ->join('JOIN', ["aap" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'amazon_ads_profile', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'aap.id = sp.profile_id')
            ->where(['between', 'sp.date', $dateStart, $dateEnd]);

        $sdQuery = (new Query())
            ->select([
                'sd.id as id',
                'sd.profile_id as profile_id',
                'sd.sku as sku',
                'sd.asin as product_asin',
                'sd.sales as sales',
                'sd.cost as cost',
                'sd.clicks as clicks',
                'sd.currency_code as currency_code',
                'sd.created_at as created_at',
                'sd.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
                "CAST('sd' AS VARCHAR) as type",
            ])
            ->from(["sd" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sd_advertised_product', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"])
            ->join('JOIN', ["aap" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'amazon_ads_profile', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'aap.id = sd.profile_id')
            ->where(['between', 'sd.date', $dateStart, $dateEnd]);

        $sbQuery = (new Query())
            ->select([
                'sbags.id as id',
                'sbags.profile_id as profile_id',
                "CAST('' AS VARCHAR) as sku",
                'sba.asin as product_asin',
                'sbags.attributed_sales_same_sku_14d as sales',
                'sbags.cost as cost',
                'sbags.clicks as clicks',
                'sbags.currency_code as currency_code',
                'sbags.created_at as created_at',
                'sbags.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
                "CAST('sb' AS VARCHAR) as type"
            ])
            ->from(["sbags" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sb_ad_group_statistic', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"])
            ->join('JOIN', ["sba" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sb_ad', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'sba.ad_group_id = sbags.ad_group_id')
            ->join('JOIN', ["aap" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'amazon_ads_profile', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'aap.id = sbags.profile_id')
            ->where(['between', 'sbags.date', $dateStart, $dateEnd]);

        $selectSql = $spQuery->createCommand()->getRawSql() .
                    ' UNION ALL ' . $sdQuery->createCommand()->getRawSql() .
                    ' UNION ALL ' . $sbQuery->createCommand()->getRawSql();

        $insertSql = "
            INSERT INTO {$tableName} (
                id, seller_id, seller_sku, marketplace_id, sales, cost,
                clicks, currency_code, created_at, date, type, product_asin
            )
            SELECT
                id,
                toString(seller_id) as seller_id,
                coalesce(sku, '') as seller_sku,
                marketplace_id,
                sales,
                cost,
                clicks,
                currency_code,
                created_at,
                date,
                type,
                product_asin,
            FROM (
                {$selectSql}
            )
        ";

        $this->info("Executing direct INSERT FROM SELECT");

        $client->write($insertSql);
        $this->info("Direct insert completed successfully");
    }

    /**
     * Get minimum date from all ads tables (SP, SD, SB)
     *
     * @return \DateTime
     */
    protected function getMinDateFromAdsTables(): \DateTime
    {
        try {
            $spTableName = 'sp_advertised_product';
            $sdTableName = 'sd_advertised_product';
            $sbAgsTableName = 'sb_ad_group_statistic';
            $schemaName = $this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS);

            $sql = "
                SELECT MIN(min_date) as overall_min_date
                FROM (
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$spTableName}
                    UNION ALL
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$sdTableName}
                    UNION ALL
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$sbAgsTableName}
                ) combined_dates
                WHERE min_date IS NOT NULL
            ";

            $result = $this->dbManager->getAdsDb()->createCommand($sql)->queryScalar();

            if ($result) {
                $this->info("Found minimum date from ads tables: {$result}");
                return new \DateTime($result);
            } else {
                $this->info("No data found in ads tables, using default date");
                return new \DateTime('-1 year');
            }

        } catch (\Exception $e) {
            $this->error("Error getting minimum date from ads tables: " . $e->getMessage());
            // Fallback to default
            return new \DateTime('-1 year');
        }
    }
}
