<?php

namespace common\components\clickhouse\materializedViews\tables;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\CustomFunctionPopulateAwareInterface;
use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\models\ads\AmazonAdsProfile;
use common\models\ads\SbAd;
use common\models\ads\SbAdGroupStatistic;
use common\models\customer\clickhouse\AdsStatistics;
use common\models\customer\clickhouse\AdsStatisticsExtendedView as AdsStatisticsViewModel;
use common\models\customer\Product;
use DateTime;
use yii\db\Exception;
use yii\db\Query;

class AdsStatisticsTable implements DynamicTableInterface, CronAwareInterface, CustomFunctionPopulateAwareInterface
{
    use LogToConsoleTrait;

    protected bool $isAutoPopulate = true;
    protected DbManager $dbManager;
    public ?DateTime $dateStart = null;
    public ?DateTime $dateEnd = null;

    public function __construct(bool $isAutoPopulate = true, $dateStart = null, $dateEnd = null)
    {
        $this->isAutoPopulate = $isAutoPopulate;
        $this->dbManager = \Yii::$app->dbManager;
        $this->dateStart = $dateStart;
        $this->dateEnd = $dateEnd;
    }

    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        return AdsStatistics::tableName();
    }

    public function getCronExpression(): string
    {
        return "0 */6 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return true;
    }

    public function getSQL(): string
    {
        $dbHelper = new ClickhouseDbHelper();
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedReplacingMergeTree('%s', '{replica}', created_at)",
                $dbHelper->generateReplicationPath($this->getName())
            )
            : 'ReplacingMergeTree(created_at)';

        $sql = "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
            (
                `id` Int64,
                `seller_id` String,
                `seller_sku` String,
                `product_asin` String,
                `marketplace_id` String,
                `sales` Float64,
                `cost` Float64,
                `clicks` Int32,
                `currency_code` String,
                `date` Date,
                `type` String,
                `created_at` DateTime,
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(date)
            PRIMARY KEY (id, type, date)
            ORDER BY (id, type, date, seller_id, marketplace_id, seller_sku)
        ";

        return $sql;
    }

    public function populate(string $tableName): void
    {
        if (!$this->isAutoPopulate || $this->dbManager->isDemo() || !$this->dbManager->isActiveAnalytics()) {
            return;
        }

        $maxDateTime = $this->dateEnd ?? (new \DateTime());
        $minDateTime = $this->dateStart ?? $this->getMinDateFromAdsTables();

        if ($minDateTime > $maxDateTime) {
            return;
        }

        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        $client = $connection->getClient();

        $currentPeriodStart = clone $minDateTime;
        $currentPeriodStart->setDate($currentPeriodStart->format('Y'), $currentPeriodStart->format('m'), 1);
        $currentPeriodStart->setTime(0, 0, 0);

        while ($currentPeriodStart < $maxDateTime) {
            $currentPeriodEnd = clone $currentPeriodStart;
            $currentPeriodEnd->modify('+10 days -1 day');
            $currentPeriodEnd->setTime(23, 59, 59);

            if ($currentPeriodEnd > $maxDateTime) {
                $currentPeriodEnd = clone $maxDateTime;
            }

            $this->info("Processing period: " . $currentPeriodStart->format('Y-m-d') . " to " . $currentPeriodEnd->format('Y-m-d'));

            $dateStart = $currentPeriodStart->format('Y-m-d');
            $dateEnd = $currentPeriodEnd->format('Y-m-d');

            $this->processAllDataInOneBatch($client, $tableName, $dateStart, $dateEnd);

            $currentPeriodStart->modify('+10 days');
        }
        $this->optimizeTable();
    }

    /**
     * @param Client $client
     * @param string $tableName
     * @param string $dateStart
     * @param string $dateEnd
     * @return void
     */
    public function processAllDataInOneBatch(
        Client $client,
        string $tableName,
        string $dateStart,
        string $dateEnd
    ): void {
        $this->info("Processing all data for period: {$dateStart} to {$dateEnd}");

        $pgConfig = $this->dbManager->getShardPostgressConfig();

        $pgHost = $pgConfig['dbSlaveHost'];
        $pgPort = $pgConfig['dbSlavePort'];
        $pgUser = $pgConfig['dbUserSlave'];
        $pgPassword = $pgConfig['dbPasswordSlave'];
        $pgDatabase = 'profit_dash_db';

        $this->processSpSdData($client, $tableName, $dateStart, $dateEnd, $pgHost, $pgPort, $pgUser, $pgPassword, $pgDatabase);

        $this->processSbData($client, $tableName, $dateStart, $dateEnd);
    }

    private function processSbData(
        Client $client,
        string $tableName,
        string $dateStart,
        string $dateEnd
    )
    {
        $this->info("Processing SB data with product linking via batch processing");

        $sbQuery = SbAdGroupStatistic::find()
            ->alias('sbags')
            ->select([
                'sbags.id as id',
                'sbags.profile_id as profile_id',
                'sba.asin as product_asin',
                'sbags.attributed_sales_same_sku_14d as sales',
                'sbags.cost as cost',
                'sbags.clicks as clicks',
                'sbags.currency_code as currency_code',
                'sbags.created_at as created_at',
                'sbags.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
            ])
            ->leftJoin(SbAd::tableName() . ' sba', 'sba.ad_group_id = sbags.ad_group_id')
            ->leftJoin(AmazonAdsProfile::tableName() . ' aap', 'aap.id = sbags.profile_id')
            ->where(['between', 'sbags.date', $dateStart, $dateEnd])
            ->asArray();

        $total = (clone $sbQuery)->limit(null)->offset(null)->orderBy(null)->count('*');
        if ($total) {
            $this->info("Found {$total} SB records to process");
        }

        $processed = 0;
        $outerBatchSize = 500;
        foreach ($sbQuery->batch($outerBatchSize) as $sbBatch) {
            $this->processSbBatchOptimized($client, $tableName, $sbBatch);

            $processed += count($sbBatch);
            $this->info("Processed {$processed}/{$total} SB records");

            unset($sbBatch);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }

        $this->info("SB data processing completed successfully");
    }

    private function processSbBatchOptimized(
        Client $client,
        string $tableName,
        array $sbBatch
    ): void {
        $insertData = [];
        $productsCache = [];
        $pendingProducts = [];
        $miniBatchSize = 1000;
        $productsBatchSize = 1000;
        $processedCount = 0;

        foreach ($sbBatch as $sbRow) {
            $productKey = $sbRow['product_asin'] . '|' . $sbRow['marketplace_id'] . '|' . $sbRow['seller_id'];

            if (!isset($productsCache[$productKey])) {
                $pendingProducts[$productKey] = [
                    'asin' => $sbRow['product_asin'],
                    'marketplace_id' => $sbRow['marketplace_id'],
                    'seller_id' => $sbRow['seller_id']
                ];

                if (count($pendingProducts) >= $productsBatchSize) {
                    $this->loadProductsBatch($client, $pendingProducts, $productsCache);
                    $pendingProducts = [];
                }
            }
        }

        if (!empty($pendingProducts)) {
            $this->loadProductsBatch($client, $pendingProducts, $productsCache);
        }

        foreach ($sbBatch as $sbRow) {
            $productKey = $sbRow['product_asin'] . '|' . $sbRow['marketplace_id'] . '|' . $sbRow['seller_id'];

            $createdAt = $this->trimMicroseconds((string)$sbRow['created_at']);
            $date = (string)$sbRow['date'];
            $products = $productsCache[$productKey];

            if (!empty($products)) {
                $productCount = count($products);
                $salesPerProduct = (float)$sbRow['sales'] / $productCount;
                $costPerProduct = (float)$sbRow['cost'] / $productCount;

                foreach ($products as $product) {
                    $insertData[] = [
                        'id' => (int)$sbRow['id'],
                        'seller_id' => (string)$sbRow['seller_id'],
                        'seller_sku' => (string)($product['sku'] ?? ''),
                        'product_asin' => (string)$sbRow['product_asin'],
                        'marketplace_id' => (string)$sbRow['marketplace_id'],
                        'sales' => $salesPerProduct,
                        'cost' => $costPerProduct,
                        'clicks' => (int)$sbRow['clicks'],
                        'currency_code' => (string)$sbRow['currency_code'],
                        'created_at' => $createdAt,
                        'date' => $date,
                        'type' => 'sb'
                    ];
                }
            }

            $processedCount++;
            if (count($insertData) >= $miniBatchSize) {
                $this->insertData($client, $tableName, $insertData);
                $insertData = [];

                if ($processedCount % 200 === 0) {
                    $productsCache = [];
                }
            }
        }

        if (!empty($insertData)) {
            $this->insertData($client, $tableName, $insertData);
        }

        unset($insertData, $productsCache, $pendingProducts);
    }

    private function loadProductsBatch(
        Client $client,
        array $pendingProducts,
        array &$productsCache
    ): void {
        if (empty($pendingProducts)) {
            return;
        }

        $conditions = ['or'];
        foreach ($pendingProducts as $productKey => $productData) {
            $conditions[] = [
                'and',
                ['p.asin' => $productData['asin']],
                ['p.marketplace_id' => $productData['marketplace_id']],
                ['p.seller_id' => $productData['seller_id']]
            ];
        }

        $productsQuery = Product::find()->alias('p')
            ->select([
                'p.asin',
                'p.marketplace_id',
                'p.seller_id',
                'p.sku',
                'p.id'
            ])
            ->where($conditions);

        try {
            $productsRows = $productsQuery->asArray()->all();

            foreach ($productsRows as $product) {
                $key = $product['asin'] . '|' . $product['marketplace_id'] . '|' . $product['seller_id'];
                if (!isset($productsCache[$key])) {
                    $productsCache[$key] = [];
                }
                $productsCache[$key][] = $product;
            }

            foreach ($pendingProducts as $productKey => $productData) {
                if (!isset($productsCache[$productKey])) {
                    $productsCache[$productKey] = [];
                }
            }

        } catch (\Exception $e) {
            foreach ($pendingProducts as $productKey => $productData) {
                $productsCache[$productKey] = [];
            }
        }
    }

    private function trimMicroseconds(string $dateTime): string
    {
        return preg_replace('/\.\d{6}$/', '', $dateTime);
    }

    private function insertData(Client $client, string $tableName, array $insertData): void
    {
        if (empty($insertData)) {
            return;
        }

        Command::$isNodeChangingEnabled = false;
        $client->insertAssocBulk($tableName, $insertData);
        Command::$isNodeChangingEnabled = true;
    }

    private function processSpSdData (
        Client $client,
        string $tableName,
        string $dateStart,
        string $dateEnd,
        string $pgHost,
        string $pgPort,
        string $pgUser,
        string $pgPassword,
        string $pgDatabase
    )
    {
        $this->info("Processing SP and SD data");
        $spQuery = (new Query())
            ->select([
                'sp.id as id',
                'sp.profile_id as profile_id',
                'sp.sku as sku',
                'sp.asin as product_asin',
                'sales_1d as sales',
                'sp.cost as cost',
                'sp.clicks as clicks',
                'sp.currency_code as currency_code',
                'sp.created_at as created_at',
                'sp.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
                "CAST('sp' AS VARCHAR) as type",
            ])
            ->from(["sp" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sp_advertised_product', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"])
            ->join('JOIN', ["aap" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'amazon_ads_profile', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'aap.id = sp.profile_id')
            ->where(['between', 'sp.date', $dateStart, $dateEnd]);

        $sdQuery = (new Query())
            ->select([
                'sd.id as id',
                'sd.profile_id as profile_id',
                'sd.sku as sku',
                'sd.asin as product_asin',
                'sd.sales as sales',
                'sd.cost as cost',
                'sd.clicks as clicks',
                'sd.currency_code as currency_code',
                'sd.created_at as created_at',
                'sd.date as date',
                'aap.marketplace_id as marketplace_id',
                'aap.seller_id as seller_id',
                "CAST('sd' AS VARCHAR) as type",
            ])
            ->from(["sd" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'sd_advertised_product', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"])
            ->join('JOIN', ["aap" => "postgresql('{$pgHost}:{$pgPort}', '{$pgDatabase}', 'amazon_ads_profile', '{$pgUser}', '{$pgPassword}', '{$this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS)}')"], 'aap.id = sd.profile_id')
            ->where(['between', 'sd.date', $dateStart, $dateEnd]);


        $selectSql = $spQuery->createCommand()->getRawSql() .
            ' UNION ALL ' . $sdQuery->createCommand()->getRawSql();

        $insertSql = "
            INSERT INTO {$tableName} (
                id, seller_id, seller_sku, marketplace_id, sales, cost,
                clicks, currency_code, created_at, date, type, product_asin
            )
            SELECT
                id,
                toString(seller_id) as seller_id,
                coalesce(sku, '') as seller_sku,
                marketplace_id,
                sales,
                cost,
                clicks,
                currency_code,
                created_at,
                date,
                type,
                product_asin,
            FROM (
                {$selectSql}
            )
        ";

        $this->info("Executing direct INSERT FROM SELECT");
        $this->info($selectSql);

        $client->write($insertSql);
        $this->info("Direct insert completed successfully");
    }
    /**
     * Get minimum date from all ads tables (SP, SD, SB)
     *
     * @return \DateTime
     */
    protected function getMinDateFromAdsTables(): \DateTime
    {
        try {
            $spTableName = 'sp_advertised_product';
            $sdTableName = 'sd_advertised_product';
            $sbAgsTableName = 'sb_ad_group_statistic';
            $schemaName = $this->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS);

            $sql = "
                SELECT MIN(min_date) as overall_min_date
                FROM (
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$spTableName}
                    UNION ALL
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$sdTableName}
                    UNION ALL
                    SELECT MIN(date) as min_date FROM {$schemaName}.{$sbAgsTableName}
                ) combined_dates
                WHERE min_date IS NOT NULL
            ";

            $result = $this->dbManager->getAdsDb()->createCommand($sql)->queryScalar();

            if ($result) {
                $this->info("Found minimum date from ads tables: {$result}");
                return new \DateTime($result);
            } else {
                $this->info("No data found in ads tables, using default date");
                return new \DateTime('-1 year');
            }

        } catch (\Exception $e) {
            $this->error("Error getting minimum date from ads tables: " . $e->getMessage());
            // Fallback to default
            return new \DateTime('-1 year');
        }
    }

    public function optimizeTable(): void
    {
        $dataBaseTableName = AdsStatistics::tableName();
        $sql = "OPTIMIZE TABLE {$dataBaseTableName} FINAL";

        \Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();

        $dataBaseTableName = AdsStatisticsViewModel::tableName();
        $sql = "OPTIMIZE TABLE {$dataBaseTableName} FINAL";

        \Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();
    }
}
