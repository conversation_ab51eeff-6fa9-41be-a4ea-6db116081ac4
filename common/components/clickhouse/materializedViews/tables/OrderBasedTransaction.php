<?php

namespace common\components\clickhouse\materializedViews\tables;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\CustomFunctionPopulateAwareInterface;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\models\AmazonMarketplace;
use common\models\customer\FbaEstimatedFee;
use common\models\customer\Product;
use common\models\customer\ProductTag;
use common\models\customer\ReferralFeePreview;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Expression;
use yii\db\Query;

class OrderBasedTransaction extends AbstractTransactionStructureTable implements CronAwareInterface, CustomFunctionPopulateAwareInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected ClickhouseTransactionExtractor $transactionExtractor;

    protected bool $isAutoPopulate = true;

    public function __construct(bool $isAutoPopulate = true)
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->transactionExtractor = new ClickhouseTransactionExtractor();
        $this->isAutoPopulate = $isAutoPopulate;
    }

    public function getName(): string
    {
        return \common\models\customer\clickhouse\OrderBasedTransaction::tableName();
    }

    public function getCronExpression(): string
    {
        if ($this->dbManager->isActive()) {
            return "00 * * * *";
        }

        return "00 */3 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return false;
    }

    public function populate(string $tableName): void
    {
        if (!$this->isAutoPopulate || $this->dbManager->isDemo()) {
            return;
        }

        $connection = $this->dbManager->getClickhouseCustomerDb();
        $connection->open();
        /** @var Client $client */
        $client = $connection->getClient();

        foreach ($this->iterateOrders() as $orders) {
            try {
                $transactions = $this->generateOrderBasedTransactionsForOrders($orders, $tableName, $client);
                $this->saveGeneratedTransactions($transactions, $tableName, $client);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function saveGeneratedTransactions(array $transactions, string $tableName, Client $client): void
    {
        $this->info("Saving order based transactions to clickhouse");
        if (empty($transactions)) {
            $this->info('Skipped. Nothing to save');
            return;
        }

        Command::$isNodeChangingEnabled = false;
        $client->insertAssocBulk($tableName, $transactions);
        Command::$isNodeChangingEnabled = true;
        $this->info("Memory after: ".(memory_get_peak_usage(true)/1024/1024)." MiB\n");
        $this->info('Saved successfully');
    }

    public function generateOrderBasedTransactionsForOrders(array $orders): array
    {
        $sellerId = $this->dbManager->getSellerId();
        $seller = Seller::findOneUseCache($sellerId);
        $euAmazonFeesVat = Seller::getEuAmazonFeesVat($sellerId);

        $this->info("Processing batch started for seller $sellerId");
        $fbaEstimatedFees = $this->getFBAEstimatedFees($orders);
        $referralFeePreviews = $this->getReferralFeePreviews($orders);
        $this->info("Memory before: ".(memory_get_peak_usage(true)/1024/1024)." MiB\n");
        $financialEvents = [];

        foreach ($orders as $order) {
            try {
                $itemPrice = (float)$order['item_price'];

                if (empty($itemPrice)) {
                    $this->info($order['purchase_date'] . ' has empty item price. Nothing to calculate, skipped');
                }

                $key = implode(
                    '_',
                    [
                        $order['marketplace_id'],
                        $order['seller_id'],
                        $order['sku']
                    ]
                );
                $fbaEstimatedFee = $fbaEstimatedFees[$key] ?? null;
                $referralFeePreview = $referralFeePreviews[$key] ?? null;
                $isFBAOrder = $order['fulfillment_channel'] === AmazonOrder::FULFILMENT_CHANNEL_AFN;

                // Not applicable for FBM orders
                if (!$isFBAOrder) {
                    $fbaEstimatedFee = null;
                }

                $facts = [
                    ClickhouseTransactionExtractor::FACT_AMAZON_ORDER_ID => $order['order_id'],
                    ClickhouseTransactionExtractor::FACT_POSTED_DATE => $order['purchase_date'],
                    ClickhouseTransactionExtractor::FACT_SELLER_SKU => $order['sku'],
                    ClickhouseTransactionExtractor::FACT_QUANTITY => $order['quantity'],
                    ClickhouseTransactionExtractor::FACT_MARKETPLACE_ID => $order['marketplace_id'],
                    ClickhouseTransactionExtractor::FACT_SELLER_ID => $order['seller_id'],
                    ClickhouseTransactionExtractor::FACT_ASIN => $order['asin'] ?? '',
                    ClickhouseTransactionExtractor::FACT_TITLE => $order['title'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_ID => $order['product_id'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_EAN => $order['ean'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_ISBN => $order['isbn'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_UPC => $order['upc'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_PARENT_ASIN => $order['parent_asin'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_BRAND => $order['brand'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_MODEL => $order['model'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_TYPE => $order['product_type'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_MANUFACTURER => $order['manufacturer'] ?? '',
                    ClickhouseTransactionExtractor::FACT_PRODUCT_IS_ADULT_ONLY => $order['adult_product'] ?? null,
                    ClickhouseTransactionExtractor::FACT_PRODUCT_MAIN_IMAGE => $order['product_main_image'] ?? '',
                    ClickhouseTransactionExtractor::FACT_OFFER_TYPE => $order['offer_type'] ?? '',
                ];

                $financialEvents[] = array_merge($facts, [
                    'Shipment.ShipmentItem.ItemCharge.Principal.ChargeAmount' => [
                        'CurrencyCode' => $order['currency_code'],
                        'CurrencyAmount' => $order['item_price'] - $order['item_tax']
                    ]
                ]);

                $financialEvents[] = array_merge($facts, [
                    'Shipment.ShipmentItem.ItemCharge.Tax.ChargeAmount' => [
                        'CurrencyCode' => $order['currency_code'],
                        'CurrencyAmount' => $order['item_tax']
                    ]
                ]);

                $financialEvents[] = array_merge($facts, [
                    'Shipment.ShipmentItem.ItemCharge.ShippingCharge.ChargeAmount' => [
                        'CurrencyCode' => $order['currency_code'],
                        'CurrencyAmount' => $order['shipping_price'] - $order['shipping_tax']
                    ]
                ]);

                $financialEvents[] = array_merge($facts, [
                    'Shipment.ShipmentItem.ItemCharge.ShippingTax.ChargeAmount' => [
                        'CurrencyCode' => $order['currency_code'],
                        'CurrencyAmount' => $order['shipping_tax']
                    ]
                ]);

                $financialEvents[] = array_merge($facts, [
                    'Shipment.ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount' => [
                        'CurrencyCode' => $order['currency_code'],
                        'CurrencyAmount' => ($order['promotion_discount'] + $order['shipping_discount']) * -1
                    ]
                ]);

                if (!empty($fbaEstimatedFee) && null !== $fbaEstimatedFee['estimated_referral_fee_per_unit']) {
                    $pct = $fbaEstimatedFee['estimated_referral_fee_per_unit'] * 100 / $fbaEstimatedFee['sales_price'];
                    $currencyAmount = ($order['item_price'] + $order['shipping_price']) * $pct * 0.01 * -1;
                    $currencyAmount = $this->addEUAmazonFeesVat($currencyAmount, $euAmazonFeesVat);
                    $financialEvents[] = array_merge($facts, [
                        'Shipment.ShipmentItem.ItemFee.Commission.FeeAmount' => [
                            'CurrencyCode' => $order['currency_code'],
                            'CurrencyAmount' => $currencyAmount
                        ]
                    ]);
                } elseif (!empty($referralFeePreview) && null !== $referralFeePreview['estimated_referral_fee_per_item']) {
                    $pct = $referralFeePreview['estimated_referral_fee_per_item'] * 100 / $referralFeePreview['price'];
                    $currencyAmount = ($order['item_price'] + $order['shipping_price'])* $pct * 0.01 * -1;
                    $currencyAmount = $this->addEUAmazonFeesVat($currencyAmount, $euAmazonFeesVat);
                    $financialEvents[] = array_merge($facts, [
                        'Shipment.ShipmentItem.ItemFee.Commission.FeeAmount' => [
                            'CurrencyCode' => $order['currency_code'],
                            'CurrencyAmount' => $currencyAmount
                        ]
                    ]);
                }

                if (!empty($fbaEstimatedFee) && null !== $fbaEstimatedFee['expected_domestic_fulfilment_fee_per_unit']) {
                    $currencyAmount = $fbaEstimatedFee['expected_domestic_fulfilment_fee_per_unit'] * -1 * $order['quantity'];
                    $currencyAmount = $this->addEUAmazonFeesVat($currencyAmount, $euAmazonFeesVat);
                    $financialEvents[] = array_merge($facts, [
                        'Shipment.ShipmentItem.ItemFee.FBAPerUnitFulfillmentFee.FeeAmount' => [
                            'CurrencyCode' => $order['currency_code'],
                            'CurrencyAmount' => $currencyAmount
                        ]
                    ]);
                }

                if (!empty($fbaEstimatedFee) && null !== $fbaEstimatedFee['estimated_variable_closing_fee']) {
                    $currencyAmount = $fbaEstimatedFee['estimated_variable_closing_fee'] * -1 * $order['quantity'];
                    $currencyAmount = $this->addEUAmazonFeesVat($currencyAmount, $euAmazonFeesVat);
                    $financialEvents[] = array_merge($facts, [
                        'Shipment.ShipmentItem.ItemFee.VariableClosingFee.FeeAmount' => [
                            'CurrencyCode' => $order['currency_code'],
                            'CurrencyAmount' => $currencyAmount
                        ]
                    ]);
                }

                if (!empty($fbaEstimatedFee) && null !== $fbaEstimatedFee['estimated_fixed_closing_fee']) {
                    $currencyAmount = $fbaEstimatedFee['estimated_fixed_closing_fee'] * -1 * $order['quantity'];
                    $currencyAmount = $this->addEUAmazonFeesVat($currencyAmount, $euAmazonFeesVat);
                    $financialEvents[] = array_merge($facts, [
                        'Shipment.ShipmentItem.ItemFee.FixedClosingFee.FeeAmount' => [
                            'CurrencyCode' => $order['currency_code'],
                            'CurrencyAmount' => $currencyAmount
                        ]
                    ]);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->info(sprintf("Generated %d financial events, processing", count($financialEvents)));
        $this->transactionExtractor->process($financialEvents);
        $transactions = $this->transactionExtractor->getAndFlushTransactions($seller, true);
        $this->info([
            'countTransactions' => count($transactions)
        ]);

        return json_decode(json_encode($transactions), true);
    }

    protected function addEUAmazonFeesVat(float $currencyAmount, ?int $euAmazonFeesVat): float
    {
        if (empty($euAmazonFeesVat)) {
            return $currencyAmount;
        }

        return $currencyAmount + $currencyAmount * $euAmazonFeesVat * 0.01;
    }

    protected function iterateOrders(): \Iterator
    {
        /** @var Seller[] $activeSellers */
        $activeSellers = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId()
        ])->all();
        $offerTypeB2B = \common\models\order\AmazonOrder::OFFER_TYPE_B2B;
        $offerTypeB2C = \common\models\order\AmazonOrder::OFFER_TYPE_B2C;

        foreach ($activeSellers as $activeSeller) {
            $this->info('Seller ' . $activeSeller->id);
            $this->dbManager->setSellerId($activeSeller->id);
            $query = (new Query())
                ->select([
                    'ao.marketplace_id',
                    'ao.seller_id',
                    'ao.is_business_order',
                    'aoi.sku',
                    'p.id as product_id',
                    'p.ean',
                    'p.asin',
                    'p.isbn',
                    'p.upc',
                    'p.parent_asin',
                    'p.brand',
                    'p.model',
                    'p.product_type',
                    'p.manufacturer',
                    'p.age_range',
                    'cast(p.adult_product as integer) as adult_product',
                    'tags as tag_id',
                    'p.title as title',
                    'aoi.order_id',
                    'aoi.quantity',
                    'aoi.item_price',
                    'aoi.shipping_price',
                    'aoi.item_tax',
                    'aoi.shipping_tax',
                    'aoi.shipping_discount',
                    'aoi.promotion_discount',
                    'am.currency_code',
                    'ao.purchase_date',
                    'ao.fulfillment_channel',
                    new Expression("CASE
                        WHEN ao.is_business_order = 't' 
                        THEN '$offerTypeB2B'
                        ELSE '$offerTypeB2C'
                    END as offer_type")
                ])
                ->where([
                    'AND',
                    ['>', 'ao.purchase_date', (new \DateTime('-2 years'))->format('Y-m-d H:i:s')],
                    ['=', 'ao.has_transactions', 'f'],
                    ['in', 'ao.order_status', [
                        Order::ORDER_STATUS_SHIPPED,
                        Order::ORDER_STATUS_UNSHIPPED,
                        Order::ORDER_STATUS_PARTIALLY_SHIPPED,
                        Order::ORDER_STATUS_PENDING
                    ]],
                    ['>', 'aoi.item_price', 0]
                ])
                ->from(AmazonOrderItem::tableName() . ' as aoi')
                ->leftJoin(
                    AmazonOrder::tableName(). ' as ao',
                    'aoi.order_id = ao.amazon_order_id'
                )
                ->leftJoin(
                    Product::tableName(). ' as p',
                    '(p.marketplace_id = ao.marketplace_id AND p.seller_id = ao.seller_id AND p.sku = aoi.sku)'
                )
                ->leftJoin(
                    AmazonMarketplace::tableName() . ' as am',
                    'ao.marketplace_id = am.id'
                )
                ->leftJoin(
                    [
                        'pt' => (new Query())
                            ->select([
                                'product_id',
                                new Expression('to_json(array_agg(DISTINCT tag_id)) AS tags')
                            ])
                            ->from(ProductTag::tableName())
                            ->where(['is not', 'tag_id', null])
                            ->groupBy('product_id')
                    ],
                    'pt.product_id = p.id'
                )
                ->orderBy('purchase_date desc')
            ;

            $processedCount = 0;

            foreach ($query->batch(300, AmazonOrderItem::getDb()) as $orders) {
                // From time to time there can be orders with transactions (wrong has_transactions value), so we need to fix them
                $orders = $this->removeOrdersWithTransactions($orders);
                yield $orders;

                $processedCount += count($orders);
                $this->info([
                    'countProcessedOrders' => $processedCount
                ]);

                if ($processedCount >= 20000) {
                    break;
                }
            }
        }
    }

    protected function removeOrdersWithTransactions(array $orders): array
    {
        $orderIds = [];

        foreach ($orders as $order) {
            $orderIds[] = $order['order_id'];
        }

        if (empty($orderIds)) {
            return $orders;
        }

        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $orderIdsWithTransactions = \common\models\customer\clickhouse\TransactionBuffer::find()
            ->select('distinct(AmazonOrderId)')
            ->where([
                'AND',
                ['in', 'AmazonOrderId', $orderIds],
                ['=', 'CategoryId', $organicSalesId],
                ['=', 'COGCategoryId', 0]
            ])->column();

        if (count($orderIdsWithTransactions) === 0) {
            return $orders;
        }

        foreach ($orders as $k => $order) {
            if (in_array($order['order_id'], $orderIdsWithTransactions)) {
                unset($orders[$k]);
            }
        }

        $this->info("Updated has_transactions for " . count($orderIdsWithTransactions) . " orders");
        AmazonOrder::updateAll([
            'has_transactions' => true,
        ], [
            'amazon_order_id' => $orderIdsWithTransactions
        ]);
        return $orders;
    }

    protected function getFBAEstimatedFees(array $amazonOrders): array
    {
        $marketplaceIds = [];
        $sellerIds = [];
        $skus = [];

        foreach ($amazonOrders as $order) {
            $marketplaceIds[] = $order['marketplace_id'];
            $sellerIds[] = $order['seller_id'];
            $skus[] = $order['sku'];
        }
        $marketplaceIds = array_unique($marketplaceIds);
        $sellerIds = array_unique($sellerIds);
        $skus = array_unique($skus);

        $fbaEstimatedFees = FbaEstimatedFee::find()->where([
            'marketplace_id' => $marketplaceIds,
            'seller_id' => $sellerIds,
            'sku' => $skus
        ])->asArray()->all();
        foreach ($fbaEstimatedFees as $k => $fbaEstimatedFee) {
            $key = implode(
                '_',
                [
                    $fbaEstimatedFee['marketplace_id'],
                    $fbaEstimatedFee['seller_id'],
                    $fbaEstimatedFee['sku']
                ]
            );
            $fbaEstimatedFees[$key] = $fbaEstimatedFee;
            unset($k);
        }
        return $fbaEstimatedFees;
    }

    protected function getReferralFeePreviews(array $amazonOrders): array
    {
        $marketplaceIds = [];
        $sellerIds = [];
        $skus = [];

        foreach ($amazonOrders as $order) {
            $marketplaceIds[] = $order['marketplace_id'];
            $sellerIds[] = $order['seller_id'];
            $skus[] = $order['sku'];
        }
        $marketplaceIds = array_unique($marketplaceIds);
        $sellerIds = array_unique($sellerIds);
        $skus = array_unique($skus);

        $referralFeePreviews = ReferralFeePreview::find()->where([
            'marketplace_id' => $marketplaceIds,
            'seller_id' => $sellerIds,
            'seller_sku' => $skus
        ])->asArray()->all();
        foreach ($referralFeePreviews as $k => $referralFeePreview) {
            $key = implode(
                '_',
                [
                    $referralFeePreview['marketplace_id'],
                    $referralFeePreview['seller_id'],
                    $referralFeePreview['seller_sku']
                ]
            );
            $referralFeePreviews[$key] = $referralFeePreview;
            unset($k);
        }
        return $referralFeePreviews;
    }
}
