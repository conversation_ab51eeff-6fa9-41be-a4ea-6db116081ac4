<?php

namespace common\components\clickhouse\materializedViews\tables;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;

abstract class AbstractTransactionStructureTable implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getSQL(): string
    {
        $dbHelper = new ClickhouseDbHelper();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
            "ReplicatedSummingMergeTree('%s', '{replica}', (Quantity, Amount, AmountEUR, MergeCounter))",
                $dbHelper->generateReplicationPath(static::getName())
            )
            : 'SummingMergeTree';

        $sql = "
            CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
            (
                `PostedDate` DateTime,
                `SellerId` String,
                `MarketplaceId` String,
                `CategoryId` Int32,
                `EventPeriodId` String,
                `SellerSKU` String,
                `ProductId` Nullable(Int64),
                `ASIN` String,
                `EAN` Nullable(String),
                `ISBN` Nullable(String),
                `UPC` Nullable(String),
                `Title` Nullable(String),
                `MainImage` Nullable(String),
                `ParentASIN` Nullable(String),
                `Brand` Nullable(String),
                `Model` Nullable(String),
                `ProductType` Nullable(String),
                `Manufacturer` Nullable(String),
                `AgeRange` Nullable(String),
                `AdultProduct` Nullable(Boolean),
                `ProductStockType` Nullable(String),
                `OfferType` Nullable(String),
                `SellerOrderId` String,
                `AmazonOrderId` String,
                `Quantity` Int32,
                `Amount` Int64,
                `TagId` Array(Int32),
                `Currency` String,
                `AmountEUR` Int64,
                `MergeCounter` Int32,
                `COGCategoryId` Int32,
                `CreatedAt` DateTime DEFAULT now(),
                `TransactionDate` DateTime,
                `IndirectCostId` Int32,
                `IndirectCostTypeId` Int32
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(PostedDate)
            PRIMARY KEY (PostedDate, MarketplaceId, SellerId, SellerSKU, ASIN, EAN, ISBN, UPC, Title, MainImage, ParentASIN, Brand, Model, ProductType, Manufacturer, AgeRange, AdultProduct, SellerOrderId, AmazonOrderId, CategoryId, COGCategoryId, Currency)
            ORDER BY (PostedDate, MarketplaceId, SellerId, SellerSKU, ASIN, EAN, ISBN, UPC, Title, MainImage, ParentASIN, Brand, Model, ProductType, Manufacturer, AgeRange, AdultProduct, SellerOrderId, AmazonOrderId, CategoryId, COGCategoryId, Currency, IndirectCostId)
            SETTINGS index_granularity = 8192, allow_nullable_key=1
        ";

        return $sql;
    }

    protected function convertCurrencyItemToTransaction(
        array $facts,
        string $currencyCode,
        float $amount,
        string $financeEventCategoryPathLike
    ): Transaction
    {
        $financeEventCategoryPath = FinanceEventCategory::find()
            ->select('path')
            ->where(['like', 'path', $financeEventCategoryPathLike])
            ->cache(60 * 5)
            ->scalar() ?? [];

        return $this->transactionExtractor->convertCurrencyItemToTransaction(
            [
                'CurrencyCode' => $currencyCode,
                'CurrencyAmount' => $amount
            ],
            array_merge($facts, [
                ClickhouseTransactionExtractor::FACT_CATEGORY_PATH => $financeEventCategoryPath
            ]),
            false
        );
    }
}
