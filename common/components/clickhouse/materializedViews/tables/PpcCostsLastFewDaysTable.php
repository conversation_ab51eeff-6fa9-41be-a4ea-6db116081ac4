<?php

namespace common\components\clickhouse\materializedViews\tables;

use common\components\amazonAds\CostsApplier;
use common\components\clickhouse\materializedViews\CronAwareInterface;
use common\components\clickhouse\materializedViews\CustomFunctionPopulateAwareInterface;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;

class PpcCostsLastFewDaysTable extends AbstractTransactionStructureTable implements CronAwareInterface, CustomFunctionPopulateAwareInterface
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CostsApplier $costsApplier;
    protected bool $isAutoPopulate;

    public function __construct(bool $isAutoPopulate = true)
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->isAutoPopulate = $isAutoPopulate;
    }

    public function getName(): string
    {
        return \common\models\customer\clickhouse\PpcCostsLastFewDaysTransaction::tableName();
    }

    public function getCronExpression(): string
    {
        if ($this->dbManager->isActive()) {
            return "15 * * * *";
        }

        return "15 */3 * * *";
    }

    public function isSeparateCronProcessing(): bool
    {
        return true;
    }

    public function populate(string $tableName): void
    {
        if (!$this->isAutoPopulate || $this->dbManager->isDemo()) {
            return;
        }

        $this->costsApplier = new CostsApplier(false, $tableName);
        $this->costsApplier->apply($this->dbManager->getCustomerId(),);
    }
}
