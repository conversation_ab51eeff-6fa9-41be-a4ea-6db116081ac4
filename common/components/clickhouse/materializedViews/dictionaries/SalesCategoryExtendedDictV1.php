<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;
use common\models\customer\SalesCategoryExtendedView;

class SalesCategoryExtendedDictV1 implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.sales_category_extended_dict_v1';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $salesCategoryExtendedView = SalesCategoryExtendedView::tableName();

        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        return sprintf("
               CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part}  (
                  id String,
                  type String,
                  name String,
                  path String,
                  is_visible Bool
                )
                PRIMARY KEY id, type
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  query '
                    SELECT 
                        id, 
                        type,
                        name String,
                        path, 
                        is_visible
                    FROM {$salesCategoryExtendedView}
                  '
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 300)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
