<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;

class FinanceEventCategoryDict implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return false;
    }

    public function getName(): string
    {
        return 'finance_event_category_dict';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $financeEventCategoryTableName = FinanceEventCategory::tableName();
        $salesCategoryTableName = SalesCategory::tableName();

        return sprintf("
               CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part}  (
                  id Int32,
                  path String,
                  sales_category_id String,
                  sales_category_path String,
                  sales_category_path_custom String,
                  sales_category_is_visible Bool,
                  sales_category_id_custom String
                )
                PRIMARY KEY id
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  query '
                    SELECT 
                        fec.id, 
                        fec.path, 
                        fec.sales_category_id,
                        sc.path as sales_category_path, 
                        sc1.path as sales_category_path_custom, 
                        sc.is_visible as sales_category_is_visible,
                        fec.sales_category_id_custom
                    FROM {$financeEventCategoryTableName} fec
                    LEFT JOIN {$salesCategoryTableName} sc ON fec.sales_category_id = sc.id
                    LEFT JOIN {$salesCategoryTableName} sc1 ON fec.sales_category_id_custom = sc1.id
                  '
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 300)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
