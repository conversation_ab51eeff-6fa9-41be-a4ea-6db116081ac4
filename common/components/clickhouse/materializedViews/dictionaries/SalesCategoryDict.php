<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\models\SalesCategory;

class SalesCategoryDict implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return false;
    }

    public function getName(): string
    {
        return 'sales_category_dict';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $salesCategoryTableName = SalesCategory::tableName();
        return sprintf("
                CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part} (
                  id String,
                  name String,
                  path String,
                  is_visible Bool
                )
                PRIMARY KEY id
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  query '
                    SELECT 
                        id, 
                        name, 
                        path,
                        is_visible 
                    FROM {$salesCategoryTableName}
                  '
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 300)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
