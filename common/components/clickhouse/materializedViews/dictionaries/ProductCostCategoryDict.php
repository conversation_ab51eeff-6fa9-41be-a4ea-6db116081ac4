<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;
use common\models\customer\ProductCostCategory;
use common\models\SalesCategory;

class ProductCostCategoryDict implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.product_cost_category_dict';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $productCostCategoryTableName = ProductCostCategory::tableName();
        $salesCategoryTableName = SalesCategory::tableName();

        return sprintf("
               CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part} (
                  id String,
                  name String,
                  sales_category_id String,
                  sales_category_path String,
                  sales_category_is_visible Bool
                )
                PRIMARY KEY id
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  query '
                    SELECT 
                        pcc.id::text, 
                        pcc.name,
                        pcc.sales_category_id, 
                        sc.path as sales_category_path, 
                        sc.is_visible as sales_category_is_visible
                    FROM {$productCostCategoryTableName} pcc
                    LEFT JOIN {$salesCategoryTableName} sc ON pcc.sales_category_id = sc.id
                    UNION
                    SELECT 
                        concat(pcc.id, \'_refund\') as id,
                        pcc.name,
                        sc.id as sales_category_id, 
                        sc.path as sales_category_path, 
                        sc.is_visible as sales_category_is_visible
                    FROM {$productCostCategoryTableName} pcc
                    LEFT JOIN
                    ( 
                        SELECT *
                        FROM {$salesCategoryTableName}
                    ) sc on sc.id = (
                        CASE 
                        WHEN pcc.sales_category_id = \'expenses_taxes\' THEN \'revenue_vat\' 
                        WHEN pcc.sales_category_id = \'cost_of_goods\' THEN \'revenue_cost_of_goods\' 
                        WHEN pcc.sales_category_id = \'shipping_costs\' THEN \'revenue_shipping_costs\' 
                        WHEN pcc.sales_category_id = \'other_fees\' THEN \'revenue_other_fees\' 
                        END
                    )
                  '
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 300)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
