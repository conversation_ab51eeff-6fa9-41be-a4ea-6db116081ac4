<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\models\CurrencyRateHistory;

class CurrencyRateDict implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return false;
    }

    public function getName(): string
    {
        return 'currency_rate_dict';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $currencyRateHistoryTableName = CurrencyRateHistory::tableName();
        return sprintf("
                CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part}  (
                  id Int32,
                  currency_id String,
                  value Float,
                  date Date
                )
                PRIMARY KEY date, currency_id
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  table '{$currencyRateHistoryTableName}'
                  invalidate_query 'select count(*) from {$currencyRateHistoryTableName}'
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 20)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
