<?php

namespace common\components\clickhouse\materializedViews\dictionaries;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\models\AmazonMarketplace;

class AmazonMarketplaceDict implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return false;
    }

    public function getName(): string
    {
        return 'amazon_marketplace_dict';
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_DICTIONARY;
    }

    public function getSQL(): string
    {
        $config = \Yii::$app->dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $tableName = AmazonMarketplace::tableName();
        return sprintf("
                CREATE DICTIONARY IF NOT EXISTS {table_name} {cluster_part} (
                  id String,
                  title String,
                  country_code String,
                  currency_code String
                )
                PRIMARY KEY id
                SOURCE(POSTGRESQL(
                  port '%s'
                  host '%s'
                  user '%s'
                  password '%s'
                  db 'profit_dash_db'
                  invalidate_query 'select count(*) from {$tableName}'
                  query '
                    SELECT 
                        id, 
                        title, 
                        country_code,
                        currency_code 
                    FROM {$tableName}
                  '
                ))
                LAYOUT(COMPLEX_KEY_HASHED())
                LIFETIME(MIN 10 MAX 300)
            ",
            $dbPort,
            $dbHost,
            $dbUser,
            $dbPassword
        );
    }
}
