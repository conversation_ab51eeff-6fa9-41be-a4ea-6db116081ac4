<?php

namespace common\components\clickhouse\materializedViews\proxy;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;

class ProxyProduct implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.proxy_product';
    }

    public function getSQL(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $config = $dbManager->getShardPostgressConfig();

        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $sql = sprintf(
            "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
                (
                    `id` Int64,
                    `marketplace_id` String,
                    `seller_id` String,
                    `sku` String,
                    `title` String,
                    `asin` String,
                    `currency_code` String,
                    `ean` String,
                    `upc` String,
                    `isbn` String,
                    `brand` String,
                    `product_type` String,
                    `manufacturer` String,
                    `adult_product` Boolean,
                    `catalog_product_name` String,
                    `parent_asin` String,
                    `main_image` String,
                    `stock_type` String,
                    `condition` String,
                    `created_at` DateTime DEFAULT now()
                )
                ENGINE = PostgreSQL('%s:%s', 'profit_dash_db', 'product', '%s', '%s', '%s')
            ",
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPassword,
            $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER)
        );

        return $sql;
    }
}
