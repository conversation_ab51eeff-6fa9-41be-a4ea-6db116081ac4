<?php

namespace common\components\clickhouse\materializedViews\proxy;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;

class ProxyReferralFeePreviewHistory implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.proxy_referral_fee_preview_history';
    }

    public function getSQL(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];


        $sql = sprintf(
            "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
                (
                    `id` Int64,
                    `marketplace_id` String,
                    `seller_id` String,
                    `seller_sku` String,
                    `asin` Nullable(String),
                    `price` Nullable(Float),
                    `estimated_referral_fee_per_item` Nullable(Float),
                    `currency` String,
                    `created_at` DateTime DEFAULT now(),
                    `date` Date DEFAULT today()
                )
                ENGINE = PostgreSQL('%s:%s', 'profit_dash_db', 'referral_fee_preview_history', '%s', '%s', '%s')
            ",
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPassword,
            $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER)
        );

        return $sql;
    }
}
