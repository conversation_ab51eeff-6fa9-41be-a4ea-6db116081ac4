<?php

namespace common\components\clickhouse\materializedViews\proxy;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;

class ProxyFbaReturn implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.proxy_fba_return';
    }

    public function getSQL(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $sql = sprintf(
            "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
                (
                    `id` Int64,
                    `seller_id` String,
                    `marketplace_id` String,
                    `return_date` DateTime,
                    `order_id` String,
                    `sku` String,
                    `asin` String,
                    `fnsku` String,
                    `product_name` String,
                    `quantity` Int64,
                    `fulfillment_center_id` String,
                    `reason` String,
                    `status` String,
                    `license_plate_number` String,
                    `customer_comments` String,
                    `created_at` DateTime DEFAULT now(),
                    `updated_at` DateTime DEFAULT now()
                )
                ENGINE = PostgreSQL('%s:%s', 'profit_dash_db', 'fba_return', '%s', '%s', '%s')
            ",
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPassword,
            $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER)
        );

        return $sql;
    }
}
