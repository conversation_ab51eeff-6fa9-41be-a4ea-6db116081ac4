<?php

namespace common\components\clickhouse\materializedViews\proxy;

use common\components\clickhouse\materializedViews\DynamicTableInterface;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;

class ProxyFbaEstimatedFeeHistory implements DynamicTableInterface
{
    public function isCustomerRelated(): bool
    {
        return true;
    }

    public function getType(): string
    {
        return DynamicTablesManager::TYPE_TABLE;
    }

    public function getName(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDbName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER);
        return $customerDbName . '.proxy_fba_estimated_fee_history';
    }

    public function getSQL(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbSlavePort'];
        $dbHost = $config['dbSlaveHost'];
        $dbUser = $config['dbUserSlave'];
        $dbPassword = $config['dbPasswordSlave'];

        $sql = sprintf(
            "CREATE TABLE IF NOT EXISTS {table_name} {cluster_part}
                (
                    `id` Int64,
                    `marketplace_id` String,
                    `seller_id` String,
                    `sku` String,
                    `currency` String,
                    `sales_price` Float,
                    `estimated_referral_fee_per_unit` Float,
                    `estimated_variable_closing_fee` Float,
                    `estimated_fixed_closing_fee` Float,
                    `expected_domestic_fulfilment_fee_per_unit` Float,
                    `created_at` DateTime DEFAULT now(),
                    `date` Date DEFAULT today()
                )
                ENGINE = PostgreSQL('%s:%s', 'profit_dash_db', 'fba_estimated_fee_history', '%s', '%s', '%s')
            ",
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPassword,
            $dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER)
        );

        return $sql;
    }
}
