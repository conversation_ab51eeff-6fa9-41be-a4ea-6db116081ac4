<?php

namespace common\components\clickhouse\materializedViews;

use bashkarev\clickhouse\Connection;
use common\components\clickhouse\materializedViews\dictionaries\AmazonMarketplaceDict;
use common\components\clickhouse\materializedViews\dictionaries\CurrencyRateDict;
use common\components\clickhouse\materializedViews\dictionaries\FinanceEventCategoryDict;
use common\components\clickhouse\materializedViews\dictionaries\ProductCostCategoryDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\proxy\ProxyProduct;
use common\components\clickhouse\materializedViews\proxy\ProxyFbaEstimatedFeeHistory;
use common\components\clickhouse\materializedViews\proxy\ProxyReferralFeePreviewHistory;
use common\components\clickhouse\materializedViews\proxy\ProxyProductTag;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\tables\PpcCostsLastFewDaysTable;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\FbaFeeFactorView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\ReferralFeeFactorView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use Cron\CronExpression;
use Yii;
use yii\mutex\Mutex;

class DynamicTablesManager
{
    use LogToConsoleTrait;

    public const TYPE_DICTIONARY = 'dictionary';
    public const TYPE_VIEW = 'view';
    public const TYPE_TABLE = 'table';

    private DbManager $dbManager;
    private ProcessManager $processManager;
    private ClickhouseDbHelper $dbHelper;
    private Mutex $mutex;

    protected bool $shouldUseLock = true;
    protected bool $isSilentExceptions = true;
    protected int $maxAttemptsToRebuild = 1;

    public function __construct(bool $shouldUseLock = true, bool $isSilentExceptions = true, int $maxAttemptsToRebuild = 1)
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->dbHelper = new ClickhouseDbHelper();
        $this->processManager = \Yii::$app->processManager;
        $this->mutex = \Yii::$app->mutex;
        $this->shouldUseLock = $shouldUseLock;
        $this->isSilentExceptions = $isSilentExceptions;
        $this->maxAttemptsToRebuild = $maxAttemptsToRebuild;
    }

    /**
     * @return DynamicTableInterface[]
     */
    public function getRegisteredDynamicTables(): array
    {
        return [
            new SalesCategoryDict(),
            new CurrencyRateDict(),
            new FinanceEventCategoryDict(),
            new ProductCostCategoryDict(),
            new AmazonMarketplaceDict(),
            new SalesCategoryExtendedDict(),

            // Customers materialized views
            new OrderBasedTransactionExtendedViewV1(),
            new TransactionExtendedViewV1(),
            new AmazonOrderExtendedViewV1(),
            new AmazonOrderInProgressExtendedViewV1(),
            new ReferralFeeFactorView(),
            new FbaFeeFactorView(),

            // Customer proxy tables
            new ProxyProduct(),
            new ProxyFbaEstimatedFeeHistory(),
            new ProxyReferralFeePreviewHistory(),
            new ProxyProductTag(),

            // Customer tables
            new OrderBasedTransaction(),
            new PpcCostsLastFewDaysTable()
        ];
    }

    public function rebuildCronAwareTables(\DateTime $currTime = null, string $tableName = null): void
    {
        $customerId = $this->dbManager->getCustomerId();

        /** @var DynamicTableInterface $dynamicTable */
        foreach ($this->getRegisteredDynamicTables() as $dynamicTable) {
            try {
                if (!$dynamicTable instanceof CronAwareInterface) {
                    continue;
                }
                if ($dynamicTable->isCustomerRelated() && empty($customerId)) {
                    continue;
                }
                if (!$dynamicTable->isCustomerRelated() && !empty($customerId)) {
                    continue;
                }

                if ($dynamicTable->isSeparateCronProcessing() === empty($tableName)) {
                    continue;
                }

                if ($dynamicTable->isSeparateCronProcessing()) {
                    $tableNameParts = explode('.', $dynamicTable->getName());
                    $dynamicTableName = end($tableNameParts);

                    if (!empty($tableName) && $dynamicTableName !== $tableName) {
                        continue;
                    }
                }

                $expression = new CronExpression($dynamicTable->getCronExpression());

                if (!$expression->isDue($currTime)) {
                    continue;
                }

                $this->rebuildDynamicTable($dynamicTable);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function rebuildByType(string $tableType, string $tableName = null): void
    {
        $customerId = $this->dbManager->getCustomerId();

        /** @var DynamicTableInterface $dynamicTable */
        foreach ($this->getRegisteredDynamicTables() as $dynamicTable) {
            try {
                if ($dynamicTable->getType() !== $tableType) {
                    continue;
                }
                if ($dynamicTable->isCustomerRelated() && empty($customerId)) {
                    continue;
                }
                if (!$dynamicTable->isCustomerRelated() && !empty($customerId)) {
                    continue;
                }

                $this->rebuildDynamicTable($dynamicTable);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function rebuildPartialDynamicTable(DynamicTableInterface $dynamicTable, \DateTime $dateOrderPurchase): void
    {
        $dataBaseTableName = $dynamicTable->getName();

        $partitionId = $dateOrderPurchase->format('Ym');
        $sql = "ALTER TABLE {$dataBaseTableName} DROP PARTITION '{$partitionId}'";

        Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();

        if ($dynamicTable->isCustomerRelated()) {
            $clickhouseDb = $this->dbManager->getClickhouseCustomerDb();
        } else {
            $clickhouseDb = $this->dbManager->getDb('system', HelperFactory::TYPE_CLICKHOUSE);
        }

        $viewName = $dynamicTable->getName();

        $this->info("Update partial table {$viewName}");
        $this->populateIfNeed($dynamicTable, $viewName, $clickhouseDb);
    }

    public function rebuildDynamicTable(
        DynamicTableInterface $dynamicTable,
        bool $isOnCluster = true,
        bool $isJustCopyData = false,
        int $attemptsMade = 0
    ): void
    {
        $processKey = implode('_', [
            'rebuild_dynamic_table',
            $this->dbManager->getCustomerId(),
            $dynamicTable->getName()
        ]);
        $this->processManager->register($processKey);

        $attemptsMade++;
        $this->info("Rebuilding dynamic table {$dynamicTable->getName()}");
        $this->info([
            'currentAttempt' => $attemptsMade,
            'maxAttempts' => $this->maxAttemptsToRebuild,
        ]);

        try {
            if ($this->shouldUseLock && !$this->mutex->acquire($processKey)) {
                $this->info("Unable to acquire lock, already rebuilding in another process");
                $this->processManager->release($processKey);
                return;
            }

            $viewName = $dynamicTable->getName();
            $viewNameTmp = $viewName . '_tmp';
            $type = strtoupper($dynamicTable->getType());
            $clusterPart = "";

            if (YII_ENV !== 'local' && $isOnCluster) {
                $clusterName = getenv('CLICKHOUSE_CLUSTER_NAME');
                $clusterPart = "ON CLUSTER $clusterName";
            }

            if ($dynamicTable->isCustomerRelated()) {
                $clickhouseDb = $this->dbManager->getClickhouseCustomerDb();
            } else {
                $clickhouseDb = $this->dbManager->getDb('system', HelperFactory::TYPE_CLICKHOUSE);
            }

            $viewSQL = $dynamicTable->getSQL();

            try {
                // Check if it's not first initialization
                $clickhouseDb->createCommand("SELECT * FROM {$viewName} LIMIT 1")->execute();
            } catch (\Throwable $e) {
                if (false !== strpos($e->getMessage(), 'UNKNOWN_TABLE')) {
                    $viewSQL = strtr($viewSQL, [
                        '{table_name}' => $viewName,
                        '{cluster_part}' => $clusterPart
                    ]);

                    $this->info("Creating table {$viewName} (if not exist)");
                    $this->dbHelper->executeOnMasterNodes($viewSQL);
                    if (!$isJustCopyData) {
                        $this->populateIfNeed($dynamicTable, $viewName, $clickhouseDb);
                    }
                    $this->mutex->release($processKey);
                    $this->processManager->release($processKey);
                    return;
                }
            }

            $this->info("Removing {$viewNameTmp} if exist");
            $sql = "DROP {$type} IF EXISTS {$viewNameTmp} {$clusterPart} SYNC";
            $this->dbHelper->executeOnMasterNodes($sql);

            $viewSQL = strtr($viewSQL, [
                '{table_name}' => $viewNameTmp,
                '{cluster_part}' => $clusterPart
            ]);

            $this->info("Creating table {$viewNameTmp}");
            $this->dbHelper->executeOnMasterNodes($viewSQL);

            if ($isJustCopyData) {
                $this->populateByCopyingFromExistingTable($viewNameTmp, $viewName, $clickhouseDb);
            } else {
                $this->populateIfNeed($dynamicTable, $viewNameTmp, $clickhouseDb);
            }

            try {
                $this->info("Exchange tables {$viewNameTmp} AND {$viewName}");
                $exchangeType = $type === DynamicTablesManager::TYPE_DICTIONARY
                    ? 'DICTIONARIES'
                    : 'TABLES';

                $sql = "EXCHANGE {$exchangeType} {$viewNameTmp} AND {$viewName} {$clusterPart}";
                $this->dbHelper->executeOnMasterNodes($sql, 3);
            } catch (\Throwable $e) {
                throw $e;
            }

            $this->info("Removing {$viewNameTmp}");
            $sql = "DROP {$type} IF EXISTS {$viewNameTmp} {$clusterPart}";
            $this->dbHelper->executeOnMasterNodes($sql);
        } catch (\Throwable $e) {
            if ($attemptsMade < $this->maxAttemptsToRebuild) {
                $this->mutex->release($processKey);
                $this->processManager->release($processKey);

                $this->info($e);
                sleep(5);

                $this->rebuildDynamicTable(
                    $dynamicTable,
                    $isOnCluster,
                    $isJustCopyData,
                    $attemptsMade
                );
                return;
            }

            if (!$this->isSilentExceptions) {
                $this->mutex->release($processKey);
                $this->processManager->release($processKey);
                throw $e;
            }

            $this->error($e);
        }
        $this->mutex->release($processKey);
        $this->processManager->release($processKey);
    }

    protected function populateByCopyingFromExistingTable(string $toTableName, string $fromTableName, Connection $clickhouseDb): void
    {
        $this->info("Copy records from $fromTableName to $toTableName");
        $singleItem = $clickhouseDb->createCommand("
            SELECT * FROM {$fromTableName} LIMIT 1
        ")->queryOne();
        $this->info($singleItem);

        if (empty($singleItem)) {
            $this->info('Nothing to copy, finished');
            return;
        }

        $columnNames = array_keys($singleItem);

        $this->info('Copy records started');
        $clickhouseDb->createCommand("
            INSERT INTO {$toTableName} (" . implode(',', $columnNames) . ")
            SELECT " . implode(',', $columnNames) . "
            FROM {$fromTableName}
        ")->execute();
        $this->info('Copy records finished');
    }

    protected function populateIfNeed(DynamicTableInterface $dynamicTable, string $tableName, Connection $clickhouseDb): void
    {
        if ($dynamicTable instanceof PopulateAwareInterface) {
            $this->info("Populating table $tableName with data");

            foreach ($dynamicTable->getpopulateSQL($tableName) as $selectSQL) {
                $clickhouseDb->createCommand("
                    INSERT INTO {$tableName} 
                        {$selectSQL}
                        SETTINGS max_partitions_per_insert_block = 10000
                    ")->execute();
            }
            return;
        }

        if ($dynamicTable instanceof CustomFunctionPopulateAwareInterface) {
            $this->info("Populating table $tableName with data");
            $dynamicTable->populate($tableName);
            return;
        }
    }
}
