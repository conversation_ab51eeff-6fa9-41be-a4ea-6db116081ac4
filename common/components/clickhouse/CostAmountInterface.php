<?php

namespace common\components\clickhouse;

use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\Transaction;

interface CostAmountInterface
{
    public function getCostAmounts(string $orderId, array $orderItems): array;

    public function generateOppositeTransactions(string $amazonOrderId): array;

    public function generateNewCostTransactions(AmazonOrderExtendedView $order, string $currencyId, float $amount, ?Transaction $transactionForCopy): \common\models\finance\clickhouse\Transaction;
}
