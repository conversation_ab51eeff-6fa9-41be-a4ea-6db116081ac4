<?php

namespace common\components;

use Aws\Credentials\Credentials;
use Aws\S3\S3Client;
use yii\base\Component;

/**
 * Class S3Component.
 *
 * @property S3Client    $instance
 * @property string      $assetsBucket
 * @property string      $videoBucket
 * @property string|null $encryption
 * @property Credentials
 */
class S3Component extends Component
{
    const STORAGE_CLASS_REDUCED_REDUNDANCY = 'REDUCED_REDUNDANCY';
    const ACL_PUBLIC_READ = 'public-read';
    const ACL_PRIVATE = 'private';

    /**
     * Used for generating pre-signed url for uploading object.
     *
     * @var string
     */
    public const COMMAND_PUT_OBJECT = 'PutObject';

    /** @var S3Client */
    public $_s3;
    public $aKey; // AWS Access key
    public $sKey; // AWS Secret key
    public $region;
    public $version;
    public $encryptionType;

    public $bucket;
    public $publicBucket;

    public function init($params = null)
    {
        // use default credentials if no other are passed
        if ($params === null) {
            // omit credentials here if you are using IAM roles for EC2 instances
            // or credentials sourced from the AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.
            $params = [];
            if (!empty($this->aKey) && !empty($this->sKey)) {
                $params['key'] = $this->aKey;
                $params['secret'] = $this->sKey;
                $params['credentials'] = [
                    'key' => $this->aKey,
                    'secret' => $this->sKey,
                ];
                $params['region'] = $this->region;
                $params['version'] = $this->version;
            }
        }

        $this->_s3 = new S3Client($params);
    }

    /**
     * @return S3Client
     */
    public function getInstance()
    {
        return $this->_s3;
    }

    /**
     * @return |null
     */
    public function getEncryption()
    {
        return $this->encryptionType ?: null;
    }

    /**
     * <AUTHOR>
     * @return Credentials
     */
    public function getCredentials()
    {
        if (!empty($this->aKey) && !empty($this->sKey)) {
            return new Credentials($this->aKey, $this->sKey);
        }

        return null;
    }

    /**
     * Parse AWS S3 url into bucket and resource.
     * Both formats are supported:
     *  https://bucket.s3.amazonaws.com/file.ext
     *  https://s3.amazonaws.com/bucket/file.ext.
     *
     * @param $url
     * @return array|bool
     *                    - bucket
     *                    - resource
     */
    public function parseS3Url($url)
    {
        if (!preg_match('/(?:\/\/s3\.amazonaws.com\/([^\/]+)|:\/\/([^.]+)\.s3\.amazonaws\.com)\/(.+)/', $url, $a)) {
            return false;
        }

        $bucket = isset($a[2]) ? $a[2] : $a[1];
        $resource = $a[3];

        return ['bucket' => $bucket, 'resource' => $resource];
    }

    /**
     * @param $uri
     * @param  string $name
     * @return string
     */
    public function getUri($uri, $name = '')
    {
        $uri = str_replace(DIRECTORY_SEPARATOR, '/', $uri);

        if (substr($uri, 0, 1) === '/') {
            $uri = substr($uri, 1);
        }

        return $uri . (substr($uri, -1) === '/' || empty($name) ? '' : '/') . $name;
    }

    /**
     * Checks whether passed url is safe and relates to our bucket.
     *
     * @param  string $url
     * @return bool
     */
    public function isUrlSafe(string $url): bool
    {
        foreach ($this->getAvailableBuckets() as $bucket) {
            $baseUrl = "https://{$bucket}.s3.{$this->region}.amazonaws.com";

            if (0 === strpos($url, $baseUrl)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return string
     */
    public function baseUrl()
    {
        return "https://" . $this->bucket . ".s3.amazonaws.com/";
    }

    /**
     * @param $uri
     * @param  string $name
     * @param  null   $bucket
     * @return string
     */
    public function getUrl($uri, $name = '', $bucket = null)
    {
        return $this->baseUrl() . $this->getUri($uri, $name);
    }

    /**
     * Upload file to S3.
     * @param  string $key
     * @param  string $file
     * @param  string $contentType
     * @param  string $acl
     * @return mixed
     */
    public function uploadFile(string $key, string $file, string $contentType, string $acl = self::ACL_PUBLIC_READ)
    {
        $upload = $this->_s3->putObject(
            [
                'Bucket' => $this->bucket,
                'Key' => $key,
                'SourceFile' => $file,
                'ContentType' => $contentType,
                'ACL' => $acl,
                'StorageClass' => self::STORAGE_CLASS_REDUCED_REDUNDANCY,
            ]
        );

        return $upload->get('ObjectURL');
    }

    /**
     * Generates pre-signed URL which can be used for managing (get, created, delete, etc.).
     *
     * @param  string     $objectKey       S3 object ket (path)
     * @param  string     $commandName     S3 client's command name
     * @param  int        $lifeTimeSeconds
     * @throws \Exception
     * @return string
     */
    public function createPreSignedUrl(string $objectKey, string $commandName, int $lifeTimeSeconds = 20 * 60): string
    {
        if (!in_array($commandName, [
            self::COMMAND_PUT_OBJECT,
            // other commands can be added here if need.
        ], true)) {
            throw new \Exception("Command name $commandName is not supported for pre-signed url generation");
        }

        $cmd = $this->_s3->getCommand($commandName, [
            'Bucket' => $this->publicBucket,
            'Key'    => $objectKey,
            'ACL' => self::ACL_PUBLIC_READ,
        ]);
        $request = $this->_s3->createPresignedRequest($cmd, "+{$lifeTimeSeconds} seconds")->withMethod('PUT');

        return (string) $request->getUri();
    }

    public function getAvailableBuckets()
    {
        return [
            $this->bucket,
            $this->publicBucket,
        ];
    }

    /**
     * Preparse unsafe object key for using.
     *
     * @param  string $objectKey
     * @return string
     */
    private function prepareObjectKey(string $objectKey): string
    {
        // Allows only digits, words, '/' and '-' characters.
        $objectKey = preg_replace('/[^\w\-\/]/', '-', $objectKey);
        return $objectKey;
    }
}
