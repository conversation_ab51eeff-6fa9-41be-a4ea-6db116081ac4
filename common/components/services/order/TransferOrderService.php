<?php

namespace common\components\services\order;

use bashkarev\clickhouse\Connection;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\DbPostfixManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Order;
use Yii;
use yii\caching\Cache;
use yii\db\Query;

class TransferOrderService
{
    use LogToConsoleTrait;

    protected const MAX_PARTITIONS_COUNT = 2;

    protected int $customerId;
    protected ClickhouseDbHelper $dbHelper;
    protected Connection $clickhouseCustomerDb;
    protected DbManager $dbManager;
    protected Cache $cache;
    protected CustomerConfig $customerConfig;

    /**
     * @param $customerId
     */
    public function __construct($customerId)
    {
        $this->customerId = $customerId;
        $this->dbHelper = new ClickhouseDbHelper();
        \Yii::$app->dbManager->setCustomerId($customerId);
        $this->clickhouseCustomerDb = \Yii::$app->dbManager->getClickhouseCustomerDb();
        $this->dbManager = \Yii::$app->dbManager;
        $this->cache = \Yii::$app->cache;
        $this->customerConfig = \Yii::$container->get("customerConfig");
    }

    protected function dropAmazonOrderTable()
    {
        $amazonOrderTable = $this->dbHelper->getAmazonOrderTableName();
        $sql = "DROP TABLE IF EXISTS {$amazonOrderTable} {$this->dbHelper->getClusterPart()} SYNC";
        $this->dbHelper->executeOnMasterNodes($sql);
    }

    protected function reCreateAmazonOrderTable()
    {
        $amazonOrderTable = $this->dbHelper->getAmazonOrderTableName();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedReplacingMergeTree('%s', '{replica}')",
                $this->dbHelper->generateReplicationPath($amazonOrderTable))
            : 'ReplacingMergeTree';
        $clusterPart = $this->dbHelper->getClusterPart();

        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderTable} {$clusterPart}
              (
                {$this->dbHelper->getAmazonOrderBaseStructure()},
                INDEX idx_id (id) TYPE minmax GRANULARITY 1
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(order_purchase_date)
            {$this->dbHelper->getAmazonOrderBaseOrder()}
            {$this->dbHelper->getAmazonOrderBasePrimaryKey()}
        ";
        $this->dbHelper->executeOnMasterNodes($sql);
    }

    protected function dropAmazonOrderInProgressTable()
    {
        $amazonOrderInProgressTable = $this->dbHelper->getAmazonOrderInProgressTableName();
        $sql = "DROP TABLE IF EXISTS {$amazonOrderInProgressTable} SYNC";
        $this->dbHelper->executeOnMasterNodes($sql);

    }

    protected function reCreateAmazonOrderInProgressTable()
    {
        $clusterPart = $this->dbHelper->getClusterPart();
        $amazonOrderInProgressTable = $this->dbHelper->getAmazonOrderInProgressTableName();
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedReplacingMergeTree('%s', '{replica}')",
                $this->dbHelper->generateReplicationPath($amazonOrderInProgressTable))
            : 'ReplacingMergeTree';

        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderInProgressTable} {$clusterPart}
              (
                {$this->dbHelper->getAmazonOrderBaseStructure()},
                `version` UInt64,
                INDEX idx_id (id) TYPE minmax GRANULARITY 1
            )
            ENGINE = {$engine}
            PARTITION BY version
            {$this->dbHelper->getAmazonOrderBaseOrder()}
            {$this->dbHelper->getAmazonOrderBasePrimaryKey()}
        ";

        $this->dbHelper->executeOnMasterNodes($sql);
    }

    public function reCreateTables()
    {
        $this->dropAmazonOrderTable();
        $this->reCreateAmazonOrderTable();
        $this->dropAmazonOrderInProgressTable();
        $this->reCreateAmazonOrderInProgressTable();

        $clickhouseTablesToTruncate = [
            AmazonOrderInProgressExtendedView::tableName(),
            AmazonOrderExtendedView::tableName(),
        ];

        foreach ($clickhouseTablesToTruncate as $tableName) {
            $this->info("Truncate clickhouse table $tableName");
            $aql = "TRUNCATE TABLE {$tableName}";
            $this->dbHelper->executeOnMasterNodes($aql);
        }
    }

    public function reinit()
    {
        $this->reCreateTables();
        $this->transfer(true);
    }

    public function transfer(bool $isReInit = false)
    {
        $this->info('Transfer orders for customer ID: ' . $this->customerId);

        $sellers = Seller::find()->where(['is_analytic_active' => true])->andWhere(['customer_id' => $this->customerId])->all();

        $amazonOrderTable = $this->dbHelper->getAmazonOrderTableName();
        $amazonOrderInProgressTable = $this->dbHelper->getAmazonOrderInProgressTableName();
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        $currentPartitionVersion = $clickhouseCustomerDb->createCommand("SELECT max(version) from {$amazonOrderInProgressTable}")->queryScalar();
        $nextPartitionVersion = $currentPartitionVersion + 1;

        $cacheKey = self::getTransferInProgressCacheKey($this->customerId);
        $this->cache->set($cacheKey, true, 60 * 5);

        $columns = [
            "id",
            "order_id",
            "asin",
            "sku",
            "order_item_id",
            "title",
            "quantity",
            "quantity_shipped",
            "item_price",
            "shipping_price",
            "item_tax",
            "shipping_tax",
            "shipping_discount",
            "promotion_discount",
            "cod_fee",
            "cod_fee_discount",
            "promotion_id",
            "condition_id",
            "condition_subtype_id",
            "condition_note",
            "scheduled_delivery_start_date",
            "scheduled_delivery_end_date",
            "order_purchase_date",
            "order_marketplace_id",
            "date",
            "profit",
            "order_period_id",
            "seller_id",
            "seller_order_id",
            "last_update_date",
            "order_status",
            "order_type",
            "created_at",
            "updated_at",
            "fulfillment_channel",
            "is_business_order",
            "currency_code"
        ];

        try {
            /** @var Seller $seller */
            foreach ($sellers as $seller) {
                $orderStatusShipped = Order::ORDER_STATUS_SHIPPED;
                $orderStatusCanceled = Order::ORDER_STATUS_CANCELED;
                $this->info("Seller {$seller->id}");
                $this->dbManager->setSellerId($seller->id);

                $schema = 'order_' . (new DbPostfixManager($seller->id, $this->customerId))->getDbPostfixForSellerRelatedDbs();
                $sqlFunction = $this->dbHelper->getPostgresqlFunction($this->dbManager->getShardPostgressConfig(), 'amazon_order_view', $schema);

                if ($isReInit) {
                    $minPurchaseDate = \common\models\order\AmazonOrder::find()->select('min(purchase_date)')->scalar();
                    $this->info($minPurchaseDate);

                    $baseSql = "INSERT INTO {$amazonOrderTable} (" . implode(', ', $columns) . ")
                        SETTINGS max_partitions_per_insert_block = 10000
                        SELECT " . implode(', ', $columns) . "
                        FROM {$sqlFunction}";

                    $currDateTime = new \DateTime();
                    $fromDateTime = new \DateTime($minPurchaseDate);

                    do {
                        $toDateTime = (clone $fromDateTime)->modify('+3 months');
                        $sql = $baseSql . "
                            WHERE order_purchase_date >= '" . $fromDateTime->format('Y-m-d H:i:s') . "'
                            AND order_purchase_date < '" . $toDateTime->format('Y-m-d H:i:s') . "'
                            AND (order_status = '{$orderStatusShipped}' OR order_status = '{$orderStatusCanceled}')
                        ";
                        $this->info($sql);
                        $clickhouseCustomerDb->createCommand($sql)->execute();

                        $fromDateTime = $toDateTime;
                    } while($toDateTime < $currDateTime);
                }

                $inProgressMinDate = date('Y-m-d H:i:s', strtotime('-6 months'));
                $sql = "
                    INSERT INTO {$amazonOrderInProgressTable} (" . implode(', ', $columns) . ", version)
                    SETTINGS max_partitions_per_insert_block = 10000
                    SELECT " . implode(', ', $columns) . ", {$nextPartitionVersion} as version 
                    FROM {$sqlFunction} 
                    WHERE order_purchase_date > '{$inProgressMinDate}'
                    AND order_status != '{$orderStatusShipped}' 
                    AND order_status != '{$orderStatusCanceled}'
                ";
                $this->info($sql);
                $clickhouseCustomerDb->createCommand($sql)->execute();
            }
            $this->cache->delete($cacheKey);
        } catch (\Throwable $e) {
            $this->cache->delete($cacheKey);
            throw $e;
        }
    }

    public function transferByIds(string $sellerId, array $orderIds)
    {
        $this->reCreateAmazonOrderTable();
        $this->reCreateAmazonOrderInProgressTable();

        $this->info('Transfer orders for customer ID: ' . $this->customerId);
        $amazonOrderTable = AmazonOrder::tableName();

        $schema = 'order_' . (new DbPostfixManager($sellerId, $this->customerId))->getDbPostfixForSellerRelatedDbs();
        $sqlFunction = $this->dbHelper->getPostgresqlFunction($this->dbManager->getShardPostgressConfig(), 'amazon_order_view', $schema);

        $orderIds = array_map(function ($orderId) {
            return "'$orderId'";
        }, $orderIds);
        $orderIds = implode(',', $orderIds);
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        $sql = "INSERT INTO {$amazonOrderTable} 
            SELECT * 
            FROM {$sqlFunction} 
            WHERE order_id IN ($orderIds)
        ";
        $this->info($sql);
        $clickhouseCustomerDb->createCommand($sql)->execute();
    }

    public function switchTables()
    {
        $this->info('Switch tables for customer ID: ' . $this->customerId);
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        $amazonOrderInProgressTable = $this->dbHelper->getAmazonOrderInProgressTableName();
        $amazonOrderInProgressTmpTable = $this->dbHelper->getAmazonOrderInProgressTmpTableName();

        $table = explode('.', AmazonOrderInProgress::tableName())[1];
        $db = $this->dbHelper->getCustomerPrefix();
        $sql = "SELECT partition FROM system.parts WHERE table = '{$table}' AND database = '$db' ORDER BY toInt64(partition) DESC";
        $this->info($sql);
        $partitions = $clickhouseCustomerDb->createCommand($sql)->queryColumn();

        if (count($partitions) > self::MAX_PARTITIONS_COUNT) {
            $sql = "DROP TABLE IF EXISTS {$amazonOrderInProgressTmpTable}";
            $this->info($sql);
            $this->dbHelper->executeOnMasterNodes($sql);

            $this->info($partitions[0]);
            $dbHelper = new ClickhouseDbHelper();
            /** @var CustomerConfig $customerConfig */
            $customerConfig = \Yii::$container->get("customerConfig");
            $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
                ? sprintf(
                    "ReplicatedMergeTree('%s', '{replica}')",
                    $dbHelper->generateReplicationPath(AmazonOrderInProgress::tableName()))
                : 'MergeTree';
            $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderInProgressTmpTable}
              (
                {$this->dbHelper->getAmazonOrderBaseStructure()},
                `version` UInt64
            )
            ENGINE = {$engine}
            PARTITION BY version
            {$this->dbHelper->getAmazonOrderBaseOrder()}
            {$this->dbHelper->getAmazonOrderBasePrimaryKey()}
            ";

            $this->info($sql);
            $this->dbHelper->executeOnMasterNodes($sql);

            $sql = "INSERT INTO $amazonOrderInProgressTmpTable 
                SELECT * 
                FROM {$amazonOrderInProgressTable} where version = {$partitions[0]}
            ";
            $this->info($sql);
            $clickhouseCustomerDb->createCommand($sql)->execute();

            $sql = "EXCHANGE TABLES {$amazonOrderInProgressTable} AND {$amazonOrderInProgressTmpTable}";
            $this->info($sql);
            $this->dbHelper->executeOnMasterNodes($sql);

            $sql = "DROP TABLE {$amazonOrderInProgressTmpTable}";
            $this->info($sql);
            $this->dbHelper->executeOnMasterNodes($sql);
        }
    }

    public static function getLatestVersion(int $customerId): int
    {
        /** @var Cache $cache */
        $cache = Yii::$app->cache;
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        $cacheKey = self::getTransferInProgressCacheKey($customerId);
        $isTransferInProgress = $cache->get($cacheKey);
        $lastVersion = (new Query())
            ->select('max(version)')
            ->from(AmazonOrderInProgress::find())
            ->scalar($dbManager->getClickhouseCustomerDb()) ?? 0;

        // Getting previous version while new one is transferring to avoid data disappearing
        if ($lastVersion > 0 && $isTransferInProgress) {
            $lastVersion--;
        }

        return $lastVersion;
    }

    protected static function getTransferInProgressCacheKey(int $customerId): string
    {
        return implode('_', ['amazon_order_transfer_in_progress', $customerId]);
    }
}
