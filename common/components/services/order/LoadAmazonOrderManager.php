<?php

namespace common\components\services\order;

use common\components\rabbitmq\message\order\LoadOrderMessage;
use common\models\Seller;

class LoadAmazonOrderManager
{
    public const DEFAULT_RATE_LIMIT = 0.5;
    public const DEFAULT_BURST = 20;
    protected const IDS_LIST_PREFIX = 'load_order_ids_list';
    protected const LAST_REQUEST_DATE_PREFIX = 'load_order_last_request_date';
    protected const LIMIT_PREFIX = 'load_order_limit';

    public function addOrderIdToLoadingList(Seller $seller, $amazonOrderId)
    {
        if ($this->isExistsOrderIdInLoadingList($seller->id, $amazonOrderId)) {
            return;
        }

        $orderIds = $this->getOrderIdsLoadingList($seller->id);
        $orderIds[] = $amazonOrderId;

        $message = new LoadOrderMessage($seller->id, $seller->region, []);
        $message->setAmazonOrderId($amazonOrderId);
        $message->publish();

        \Yii::$app->cache->set($this->getKeyListForLoading($seller->id), array_values(array_unique($orderIds)));
    }

    public function removeOrderIdFromLoadingList($sellerId, $amazonOrderId)
    {
        $orderIds = $this->getOrderIdsLoadingList($sellerId);
        if (($key = array_search($amazonOrderId, $orderIds)) !== false) {
            unset($orderIds[$key]);
        }

        \Yii::$app->cache->set($this->getKeyListForLoading($sellerId), $orderIds);
    }

    public function getOrderIdsLoadingList($sellerId): array
    {
        $orderIds = \Yii::$app->cache->get($this->getKeyListForLoading($sellerId));

        if (!is_array($orderIds)) {
            return [];
        }

        return $orderIds;
    }

    public function isExistsOrderIdInLoadingList($sellerId, $amazonOrderId): bool
    {
        $orderIds = $this->getOrderIdsLoadingList($sellerId);

        return in_array($amazonOrderId, $orderIds);
    }

    public function setCurrentRateLimit($sellerId, float $limit)
    {
        \Yii::$app->cache->set($this->getKeyCurrentLimit($sellerId), $limit);
    }

    public function getCurrentRateLimit($sellerId): ?float
    {
        $limit =  \Yii::$app->cache->get($this->getKeyCurrentLimit($sellerId));

        return false === $limit ? null : floatval($limit);
    }

    public function setLastAmazonRequestDate($sellerId, \DateTime $date)
    {
        \Yii::$app->cache->set($this->getKeyLastRequestDate($sellerId), $date->format('Y-m-d H:i:s'));
    }

    public function getLastAmazonRequestDate($sellerId): ?\DateTime
    {
        $date = \Yii::$app->cache->get($this->getKeyLastRequestDate($sellerId));

        return false === $date ? null : (new \DateTime($date));
    }

    protected function getKeyListForLoading(string $sellerId): string
    {
        return self::IDS_LIST_PREFIX . '_' . strtolower($sellerId);
    }

    protected function getKeyLastRequestDate(string $sellerId): string
    {
        return self::LAST_REQUEST_DATE_PREFIX . '_' . strtolower($sellerId);
    }

    protected function getKeyCurrentLimit(string $sellerId): string
    {
        return self::LIMIT_PREFIX . '_' . strtolower($sellerId);
    }
}
