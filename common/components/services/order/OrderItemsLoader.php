<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\order\LoadOrderItemsMessage;
use common\models\order\AmazonOrder;
use common\models\Seller;

class OrderItemsLoader
{
    use LogToConsoleTrait;


    private Seller $seller;

    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    /**
     * @deprecated
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function load($amazonOrderId)
    {
        $this->info("{$this->seller->id} entry load order {$amazonOrderId} items");

        /** @var AmazonOrder $order */
        $order = AmazonOrder::findOne(['amazon_order_id' => $amazonOrderId]);
        if (is_null($order)) {
            $this->info("{$this->seller->id} {$amazonOrderId} order is not found");
            return;
        }

        $order->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_QUEUED;
        $order->saveOrThrowException();

        $message = new LoadOrderItemsMessage($this->seller->id, $this->seller->region, []);
        $message->setAmazonOrderId($order->amazon_order_id);
        $message->publish();

        $this->info("{$this->seller->id} {$order->amazon_order_id} message is published");
    }

    /**
     * <AUTHOR>
     * @param array $amazonOrderIds
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadBatch($amazonOrderIds)
    {
        if (!count($amazonOrderIds))
            return;

        AmazonOrder::updateAll(['items_loading_status'=>AmazonOrder::ITEMS_LOADING_STATUS_QUEUED], ['in','amazon_order_id', $amazonOrderIds]);

        foreach ($amazonOrderIds as $amazonOrderId) {
            $message = new LoadOrderItemsMessage($this->seller->id, $this->seller->region, []);
            $message->setAmazonOrderId($amazonOrderId);
            $message->publish();

            $this->info("{$this->seller->id} {$amazonOrderId} message is published");
        }
    }

    public function getLastUpdatedOrdersIds(int $limit): array
    {
        $query = AmazonOrder::find()->select(['amazon_order_id'])
            ->where(['items_loading_status' => [AmazonOrder::ITEMS_LOADING_STATUS_NEW]])
            // Amazon returns error for too old orders in case when trying to get their items
            ->andWhere(['>=', 'purchase_date', (new \DateTime())->modify('-2 years')->format('Y-m-d 00:00:00')])
        ;

        // We don't need to load all init periods on dev servers,
        // this can have an impact on "quota limitation" errors from amazon on production
        if (YII_ENV !== 'prod') {
            $query->andWhere(['>', 'last_update_date', (new \DateTime())->modify('-3 month')->format('Y-m-1 00:00:00')]);
        }

        $query->orderBy('last_update_date DESC');
        $query->limit($limit);

        return $query->column();
    }

    public function getProcessingCount(): int
    {
        return (int)AmazonOrder::find()->where(['items_loading_status' => [AmazonOrder::ITEMS_LOADING_STATUS_PROCESSING, AmazonOrder::ITEMS_LOADING_STATUS_QUEUED]])->count();
    }
}
