<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\order\LoadOrderItemsMessage;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Order;

class OrderItemsLoader
{
    use LogToConsoleTrait;


    private Seller $seller;

    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    /**
     * @deprecated
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function load($amazonOrderId)
    {
        $this->info("{$this->seller->id} entry load order {$amazonOrderId} items");

        /** @var AmazonOrder $order */
        $order = AmazonOrder::findOne(['amazon_order_id' => $amazonOrderId]);
        if (is_null($order)) {
            $this->info("{$this->seller->id} {$amazonOrderId} order is not found");
            return;
        }

        $order->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_QUEUED;
        $order->saveOrThrowException();

        $message = new LoadOrderItemsMessage($this->seller->id, $this->seller->region, []);
        $message->setAmazonOrderId($order->amazon_order_id);
        $message->publish();

        $this->info("{$this->seller->id} {$order->amazon_order_id} message is published");
    }

    /**
     * <AUTHOR>
     * @param array $amazonOrderIds
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadBatch($amazonOrderIds)
    {
        if (!count($amazonOrderIds))
            return;

        AmazonOrder::updateAll(['items_loading_status'=>AmazonOrder::ITEMS_LOADING_STATUS_QUEUED], ['in','amazon_order_id', $amazonOrderIds]);

        foreach ($amazonOrderIds as $amazonOrderId) {
            $message = new LoadOrderItemsMessage($this->seller->id, $this->seller->region, []);
            $message->setAmazonOrderId($amazonOrderId);
            $message->publish();

            $this->info("{$this->seller->id} {$amazonOrderId} message is published");
        }
    }

    public function getLastUpdatedOrdersIds(int $limit): array
    {
        $query = AmazonOrder::find()->select(['amazon_order_id'])
            ->where(['items_loading_status' => [AmazonOrder::ITEMS_LOADING_STATUS_NEW]])
            // Amazon returns error for too old orders in case when trying to get their items
            ->andWhere(['>=', 'purchase_date', (new \DateTime())->modify('-2 years')->format('Y-m-d 00:00:00')])
        ;

        // We don't need to load all init periods on dev servers,
        // this can have an impact on "quota limitation" errors from amazon on production
        if (\Yii::$app->dbManager->shouldApplyDevRestrictions()) {
            $query->andWhere(['>', 'last_update_date', (new \DateTime())->modify('-6 month')->format('Y-m-1 00:00:00')]);
        }

        $query->orderBy('last_update_date DESC');
        $query->limit($limit);

        return $query->column();
    }

    /**
     * Returns order ids which are broken (for example those which are in "shipped" status and have item price = 0).
     *
     * @param int $limit
     * @return array
     */
    public function getBrokenOrderIds(int $limit): array
    {
        $query = AmazonOrder::find()
            ->select(['ao.amazon_order_id'])
            ->from(AmazonOrder::tableName() . ' ao')
            ->leftJoin(AmazonOrderItem::tableName() . ' aoi', 'aoi.order_id = ao.amazon_order_id')
            ->where([
                    'AND',
                    ['=', 'ao.items_loading_status', AmazonOrder::ITEMS_LOADING_STATUS_FINISHED],
                    ['=', 'ao.order_status', Order::ORDER_STATUS_SHIPPED],
                    ['=', 'aoi.item_price', 0],
                    ['>=', 'ao.purchase_date', (new \DateTime())->modify('-30 days')->format('Y-m-d 00:00:00')]
                ]
            )
            ->limit($limit);

        return $query->column();
    }

    public function getProcessingCount(): int
    {
        return (int)AmazonOrder::find()->where(['items_loading_status' => [AmazonOrder::ITEMS_LOADING_STATUS_PROCESSING, AmazonOrder::ITEMS_LOADING_STATUS_QUEUED]])->count();
    }
}
