<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\order\LoadLoadOrdersInitMessage;
use common\components\rabbitmq\message\order\LoadLoadOrdersRefreshMessage;
use common\models\order\OrderPeriod;
use common\models\Seller;

class OrdersLoader
{
    use LogToConsoleTrait;

    const INIT_PROCESSING_LIMIT = 1;
    const REFRESH_PROCESSING_LIMIT = 1;

    private Seller $seller;

    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadInit()
    {
        $this->info("{$this->seller->id} entry loadInit()");
        if ($this->getProcessingCount(OrderPeriod::TYPE_INIT) >= self::INIT_PROCESSING_LIMIT) {
            $this->info("{$this->seller->id} procession and queued limit was exceeded");
            return;
        }

        $orderPeriod = $this->getFirstInitPeriod();
        if (is_null($orderPeriod)) {
            $this->info("{$this->seller->id} init period is not found");
            return;
        }

        $orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_QUEUED;
        $orderPeriod->saveOrThrowException();

        $message = new LoadLoadOrdersInitMessage($this->seller->id, $this->seller->region, []);
        $message->setOrderPeriodId($orderPeriod->id);
        $message->publish();

        $this->info("{$this->seller->id} message is published");
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadRefresh()
    {
        if ($this->getProcessingCount(OrderPeriod::TYPE_REFRESH) >= self::REFRESH_PROCESSING_LIMIT) {
            return;
        }

        $orderPeriod = $this->getFirstRefreshPeriod();
        if (is_null($orderPeriod)) {
            return;
        }

        $orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_QUEUED;
        $orderPeriod->saveOrThrowException();

        $message = new LoadLoadOrdersRefreshMessage($this->seller->id, $this->seller->region, []);
        $message->setOrderPeriodId($orderPeriod->id);
        $message->publish();
    }

    private function getFirstInitPeriod(): ?OrderPeriod
    {
        $query = OrderPeriod::find()
            ->where([
                'loading_status' => [OrderPeriod::LOADING_STATUS_NEW],
                'type' => OrderPeriod::TYPE_INIT
            ]);

        // We don't need to load all init periods on dev servers,
        // this can have an impact on "quota limitation" errors from amazon on production
        if (YII_ENV !== 'prod') {
            $query->andWhere(['>', 'start_date', (new \DateTime())->modify('-3 month')->format('Y-m-1 00:00:00')]);
        }

        $query->orderBy('finish_date DESC');

        return $query->one();
    }

    private function getFirstRefreshPeriod(): ?OrderPeriod
    {
        return OrderPeriod::find()->where(['loading_status' => [OrderPeriod::LOADING_STATUS_NEW], 'type' => OrderPeriod::TYPE_REFRESH])->orderBy('finish_date ASC')->one();
    }

    private function getProcessingCount(string $type): int
    {
        return (int)OrderPeriod::find()->where(['loading_status' => [OrderPeriod::LOADING_STATUS_PROCESSING, OrderPeriod::LOADING_STATUS_QUEUED], 'type' => $type])->count();
    }
}
