<?php

namespace common\components\services\order;

use common\components\COGSync\DefaultVATManager;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;

/**
 * Service responsible for handling amazon order items creation or updating
 * (including realtime logic of synchronisation to clickhouse).
 */
class StoreOrderItemsServiceV2 extends AbstractStoreDataFromAmazonService
{
    use LogToConsoleTrait;

    /**
     * Fields that allowed to be updated after we receive updated order
     */
    private const UPDATABLE_FIELDS = [
        'asin',
        'sku',
        'order_item_id',
        'title',
        'quantity',
        'quantity_shipped',
        'item_price',
        'shipping_price',
        'item_tax',
        'shipping_tax',
        'shipping_discount',
        'promotion_discount',
        'cod_fee',
        'cod_fee_discount',
        'promotion_id',
        'condition_id',
        'profit',
        'is_gift',
        'is_transparency'
    ];

    /**
     * Contains common logic for handling new or updated amazon order items to be saved
     *
     * @param AmazonOrderItem[] $amazonOrderItems Array of AmazonOrderItem as array
     * @param AmazonOrder[]|null $amazonOrders Array of AmazonOrder as array
     * @return void
     * @throws \yii\db\Exception
     */
    public function save(array $amazonOrderItems, ?array $amazonOrders = null): void
    {
        $this->info('Save order items started');
        $amazonOrderItems = $this->filterRemovalData($amazonOrderItems, 'order_id');

        if (empty($amazonOrderItems)) {
            $this->info('Save order items finished. Nothing to save, empty array');
            return;
        }

        $marketplaceIds = [];
        $sellerIds = [];
        $sellerSkus = [];
        $orderIds = [];

        foreach ($amazonOrderItems as $amazonOrderItem) {
            $marketplaceIds[] = $amazonOrderItem['order_marketplace_id'];
            $sellerIds[] = $this->dbManager->getSellerId();
            $sellerSkus[] = $amazonOrderItem['sku'];
            $orderIds[] = $amazonOrderItem['order_id'];
        }

        if (empty($amazonOrders)) {
            $amazonOrders = $this->getAmazonOrders($orderIds);
        } else {
            // Ensuring that all keys are 'amazon_order_id'
            foreach ($amazonOrders as $k => $amazonOrder) {
                if ($k === $amazonOrder['amazon_order_id']) {
                    continue;
                }
                $amazonOrders[$amazonOrder['amazon_order_id']] = $amazonOrder;
                unset($amazonOrders[$k]);
            }
        }

        $amazonMarketplaces = $this->getAmazonMarketplaces();
        $existingItems = $this->getOrderItems($orderIds);

        $insertsAndUpdatesData = $this->collectInsertsAndUpdates($amazonOrderItems, $existingItems);
        $this->updateOrderItemsInPostgres($insertsAndUpdatesData['amazonOrderItemsToUpdate']);
        // $insertedAmazonOrderItems contains their ids that will be used further
        $insertedAmazonOrderItems = $this->insertOrderItemsToPostgres($insertsAndUpdatesData['amazonOrderItemsToInsert']);

        if (!empty($insertedAmazonOrderItems)) {
            try {
                $existingProducts = $this->getProducts($marketplaceIds, $sellerIds, $sellerSkus);
                $insertUpdatesProductData = $this->collectProductInsertsAndUpdates(
                    $insertedAmazonOrderItems,
                    $existingProducts,
                    $amazonOrders,
                    $amazonMarketplaces
                );
                $this->updateProductsInPostgres($insertUpdatesProductData['productsToUpdate']);
                $this->insertProductsToPostgres($insertUpdatesProductData['productsToInsert']);
            } catch (\Throwable $e) {
                $this->error($e);
            }

            $this->insertAmazonOrderItemsToClickhouse(
                $insertedAmazonOrderItems,
                $amazonOrders,
                true
            );
        }

        $this->insertAmazonOrderItemsToClickhouse(
            $insertsAndUpdatesData['amazonOrderItemsWithUpdatedItemPrice'],
            $amazonOrders,
            false
        );

        $this->info('Save order items finished');
    }

    /**
     * @param AmazonOrderItem[] $amazonOrderItems Array of AmazonOrderItem as array
     * @param AmazonOrderItem[] $existingItems Array of AmazonOrderItem as array which already exist in our db
     * @return array {
     *       amazonOrderItemsToUpdate: AmazonOrderItem[],
     *       amazonOrderItemsToInsert: AmazonOrderItem[],
     *       amazonOrderItemsWithUpdatedItemPrice: AmazonOrderItem[]
     * }
     */
    private function collectInsertsAndUpdates(array $amazonOrderItems, array $existingItems): array
    {
        $this->info('Collecting order items inserts and updates started');

        $existingItemsGroupedByOrderId = [];
        foreach ($existingItems as $existingItem) {
            $existingItemsGroupedByOrderId[$existingItem['order_id']][] = $existingItem;
        }
        $countExistingOrderItems = count($existingItems);
        unset($existingItems);

        $countOrderItemsWithNoChanges = 0;
        $amazonOrderItemsToUpdate = [];
        $amazonOrderItemsToInsert = [];
        $amazonOrderItemsWithUpdatedItemPrice = [];

        foreach ($amazonOrderItems as $k => $amazonOrderItem) {
            /** @var AmazonOrderItem[] $existingItems */
            $existingItems = $existingItemsGroupedByOrderId[$amazonOrderItem['order_id']] ?? [];
            $existingItem = null;
            $isCurrentItemFromReport = strpos($amazonOrderItem['order_item_id'], 'REPORT_') !== false;

            // Trying to find existing item by order_item_id
            foreach ($existingItems as $i => $item) {
                if ($item['order_item_id'] == $amazonOrderItem['order_item_id']) {
                    $existingItem = $item;
                    // Prevent double taking into account on next item.
                    unset($existingItemsGroupedByOrderId[$amazonOrderItem['order_id']][$i]);
                    break;
                }
            }

            // Trying to compare items by SKU if failed to compare by order_item_id
            if (empty($existingItem)) {
                foreach ($existingItems as $i => $item) {
                    $isSkuTheSame = $item['sku'] === $amazonOrderItem['sku'];
                    if (!$isSkuTheSame) {
                        continue;
                    }

                    $isExistingItemFromReport = strpos($item['order_item_id'], 'REPORT_') !== false;
                    if (!$isExistingItemFromReport && !$isCurrentItemFromReport) {
                        continue;
                    }

                    $existingItem = $item;
                    // Prevent double taking into account on next iteration with next item.
                    unset($existingItemsGroupedByOrderId[$amazonOrderItem['order_id']][$i]);

                    // We received item from report, but existing one is from api and have valid order item id.
                    if ($isCurrentItemFromReport && !$isExistingItemFromReport) {
                        $amazonOrderItem['order_item_id'] = $existingItem['order_item_id'];
                        $amazonOrderItem['title'] = $existingItem['title'];
                    }
                    break;
                }
            }

            if (empty($existingItem)) {
                $amazonOrderItemsToInsert[$k] = $amazonOrderItem;
                continue;
            }

            $isSomethingChanged = false;
            $itemPriceBefore = (float)$existingItem['item_price'];
            $itemPriceAfter = (float)$amazonOrderItem['item_price'];
            $isPriceBecomeDefined = empty($itemPriceBefore) && !empty($itemPriceAfter);

            foreach (self::UPDATABLE_FIELDS as $field) {
                // Do not allow to reset already filled data
                if (empty($amazonOrderItem[$field])) {
                    $amazonOrderItem[$field] = $existingItem[$field];
                }

                if ($amazonOrderItem[$field] == $existingItem[$field]) {
                    continue;
                }

                $existingItem[$field] = $amazonOrderItem[$field];
                $isSomethingChanged = true;
            }

            // This amazon order itm should not be updated, nothing changed - so no reason to handle it further
            if (!$isSomethingChanged) {
                $countOrderItemsWithNoChanges++;
                continue;
            }

            $amazonOrderItemsToUpdate[$k] = $existingItem;

            if ($isPriceBecomeDefined) {
                $amazonOrderItemsWithUpdatedItemPrice[] = $existingItem;
            }
        }

        $this->info([
            'countAllOrderItems' => count($amazonOrderItems),
            'countExistingOrderItems' => $countExistingOrderItems,
            'countOrderItemsNoChanges' => $countOrderItemsWithNoChanges,
            'countOrderItemsToUpdate' => count($amazonOrderItemsToUpdate),
            'countOrderItemsToInsert' => count($amazonOrderItemsToInsert),
            'countOrderItemsWithUpdatedItemPrice' => count($amazonOrderItemsWithUpdatedItemPrice)
        ]);

        $this->info('Avoid resetting important fields finished');

        return [
            'amazonOrderItemsToUpdate' => $amazonOrderItemsToUpdate,
            'amazonOrderItemsToInsert' => $amazonOrderItemsToInsert,
            'amazonOrderItemsWithUpdatedItemPrice' => $amazonOrderItemsWithUpdatedItemPrice
        ];
    }

    /**
     * @param AmazonOrderItem[] $amazonOrderItems Array of AmazonOrderItem as array
     * @param Product[] $existingProducts Array of Product as array related to passed amazon order items
     * @param AmazonOrder[] $amazonOrders Array of AmazonOrder as array related to passed amazon order items
     * @param AmazonMarketplace[] $amazonMarketplaces Array of AmazonMarketplace as array
     * @return array {
     *      productsToUpdate: AmazonProduct[],
     *      productsToInsert: AmazonProduct[],
     * }
     */
    protected function collectProductInsertsAndUpdates(
        array $amazonOrderItems,
        array $existingProducts,
        array $amazonOrders,
        array $amazonMarketplaces
    ): array
    {
        if (empty($amazonOrderItems)) {
            return [];
        }

        $productsToUpdate = [];
        $productsToInsert = [];

        foreach ($amazonOrderItems as $amazonOrderItem) {
            try {
                $amazonOrder = $amazonOrders[$amazonOrderItem['order_id']] ?? null;

                if (empty($amazonOrder)) {
                    throw new \Exception('Unable to find appropriate amazon order for amazon order item');
                }

                // Product with invalid characters, should be ignored
                if (strpos($amazonOrderItem['title'], "ï¿½") !== false
                    || strpos($amazonOrderItem['sku'], "ï¿½") !== false
                ) {
                    continue;
                }

                $productKey = implode('_', [
                    $amazonOrder['marketplace_id'],
                    $amazonOrder['seller_id'],
                    $amazonOrderItem['sku']
                ]);

                $existingProduct = $existingProducts[$productKey] ?? null;

                $stockType = null;
                if ($amazonOrder['fulfillment_channel'] === AmazonOrder::FULFILMENT_CHANNEL_AFN) {
                    $stockType = Product::STOCK_TYPE_FBA;
                } else if ($amazonOrder['fulfillment_channel'] === AmazonOrder::FULFILMENT_CHANNEL_MFN) {
                    $stockType = Product::STOCK_TYPE_FBM;
                }

                if (!empty($existingProduct)) {
                    if (!$existingProduct['is_multiple_stock_type'] && $stockType !== $existingProduct['stock_type']) {
                        $existingProduct['is_multiple_stock_type'] = true;
                        $existingProduct['stock_type'] = $stockType;
                        $productsToUpdate[] = $existingProduct;
                    }
                    continue;
                }

                $marketplace = $amazonMarketplaces[$amazonOrder['marketplace_id']] ?? null;

                if (empty($marketplace)) {
                    throw new \Exception('Unable to find appropriate marketplace for amazon order');
                }

                $conditionMap = array_flip(Product::CONDITIONS_MAP);
                $conditionString = implode(
                    '; ',
                    array_filter(array_unique([$amazonOrderItem['condition_id'], $amazonOrderItem['condition_subtype_id']]))
                );

                $productsToInsert[$productKey] = [
                    'marketplace_id' => $amazonOrder['marketplace_id'],
                    'seller_id' => $amazonOrder['seller_id'],
                    'sku' => $amazonOrderItem['sku'],
                    'asin' => $amazonOrderItem['asin'] ?: null,
                    'title' => $amazonOrderItem['title'] ?: null,
                    'condition' => $conditionMap[$conditionString] ?? null,
                    'source' => Product::SOURCE_ORDER_INFO,
                    'currency_code' => $marketplace['currency_code'] ?? null,
                    'stock_type' => $stockType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->info([
            'countAllOrderItems' => count($amazonOrderItems),
            'countExistingProducts' => count($existingProducts),
            'countProductsToUpdate' => count($productsToUpdate),
            'countProductsToInsert' => count($productsToInsert)
        ]);

        return [
            'productsToUpdate' => $productsToUpdate,
            'productsToInsert' => $productsToInsert
        ];
    }


    /**
     * @param AmazonOrderItem[] $amazonOrderItems Array of AmazonOrderItem as array
     * @return void
     */
    private function updateOrderItemsInPostgres(array $amazonOrderItems): void
    {
        if (empty($amazonOrderItems)) {
            return;
        }

        $this->info(sprintf('Update %d amazon order items started', count($amazonOrderItems)));

        $sql = AmazonOrderItem::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrderItem::tableName(),
                array_keys(array_values($amazonOrderItems)[0]),
                $amazonOrderItems
            )
            ->getRawSql();
        $sql .= ' ON CONFLICT (id) DO UPDATE SET';

        $lastKey = array_key_last(self::UPDATABLE_FIELDS);
        foreach (self::UPDATABLE_FIELDS as $k => $updatableField) {
            $sql .= sprintf(
                " %s = EXCLUDED.%s%s",
                $updatableField,
                $updatableField,
                $k !== $lastKey ? ',' : ''
            );
        }

        AmazonOrderItem::getDb()->createCommand($sql)->execute();

        $this->info('Update amazon order items finished');
    }

    /**
     * @param AmazonOrderItem[] $amazonOrderItems Array of AmazonOrderItem as array
     * @return AmazonOrderItem[] Array of AmazonOrderItems as array with ids
     * @throws \yii\db\Exception
     */
    private function insertOrderItemsToPostgres(array $amazonOrderItems): array
    {
        if (empty($amazonOrderItems)) {
            return [];
        }

        $this->info(sprintf('Insert %d amazon order items started', count($amazonOrderItems)));

        $sql = AmazonOrderItem::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrderItem::tableName(),
                array_keys(array_values($amazonOrderItems)[0]),
                $amazonOrderItems
            )
            ->getRawSql();
        $sql .= ' RETURNING id';

        $ids = AmazonOrderItem::getDb()->createCommand($sql)->queryColumn();

        foreach ($amazonOrderItems as $k => $amazonOrderItem) {
            $amazonOrderItems[$k]['id'] = array_shift($ids);
        }

        $this->info('Insert amazon order items finished');

        return $amazonOrderItems;
    }

    /**
     * @param Product[] $products Array of products as array
     * @return void
     */
    private function updateProductsInPostgres(array $products): void
    {
        if (empty($products)) {
            return;
        }

        try {
            $this->info(sprintf('Update %d products started', count($products)));
            $sql = Product::getDb()
                ->createCommand()
                ->batchInsert(
                    Product::tableName(),
                    array_keys(array_values($products)[0]),
                    $products
                )
                ->getRawSql();
            $sql .= ' ON CONFLICT (id) DO UPDATE SET
                is_multiple_stock_type = EXCLUDED.is_multiple_stock_type,
                stock_type = EXCLUDED.stock_type
            ';
            Product::getDb()->createCommand($sql)->execute();

            $this->info('Update products finished');
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    /**
     * @param Product[] $products Array of products as array
     * @return void
     */
    private function insertProductsToPostgres(array $products): void
    {
        if (empty($products)) {
            return;
        }

        try {
            $this->info(sprintf('Insert %d products started', count($products)));
            $products = $this->productsSaver->fillIsEnabledSyncWithRepricerDefaultValue($products);

            Product::getDb()
                ->createCommand()
                ->batchInsert(
                    Product::tableName(),
                    array_keys(array_values($products)[0]),
                    $products
                )
                ->execute();

            $this->info('Insert products finished');
            $this->defaultVATManager->generateAndSaveDefaultVATForProducts($products);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}
