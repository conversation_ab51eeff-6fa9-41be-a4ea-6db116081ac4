<?php

namespace common\components\services\order;

use common\components\LogToConsoleTrait;
use common\models\order\AmazonOrder;

/**
 * Service responsible for handling amazon orders creation or updating
 * (including realtime logic of synchronisation to clickhouse).
 */
class StoreOrdersServiceV2 extends AbstractStoreDataFromAmazonService
{
    use LogToConsoleTrait;

    /**
     * Fields that allowed to be updated after we receive updated order
     */
    private const UPDATABLE_FIELDS = [
        'order_period_id',
        'is_business_order',
        'order_status',
        'last_update_date'
    ];

    /**
     * @param AmazonOrder[] $amazonOrders Array of AmazonOrder as array to be saved
     * @return void
     */
    public function save(array $amazonOrders): void
    {
        $this->info('Save orders started');

        $amazonOrders = $this->filterRemovalData($amazonOrders, 'amazon_order_id');

        if (empty($amazonOrders)) {
            $this->info('Save orders finished. Nothing to save, empty array');
            return;
        }

        $orderIds = [];
        foreach ($amazonOrders as $k => $amazonOrder) {
            $orderIds[] = $amazonOrder['amazon_order_id'];

            // Ensuring that all keys are 'amazon_order_id'
            if ($k !== $amazonOrder['amazon_order_id']) {
                $amazonOrders[$amazonOrder['amazon_order_id']] = $amazonOrder;
                unset($amazonOrders[$k]);
            }
        }

        $existingAmazonOrders = $this->getAmazonOrders($orderIds);
        $insertsAndUpdatesData = $this->collectInsertsAndUpdates($amazonOrders, $existingAmazonOrders);

        $this->insertAmazonOrdersToPostgres($insertsAndUpdatesData['amazonOrdersToInsert']);
        $this->updateAmazonOrdersInPostgres($insertsAndUpdatesData['amazonOrdersToUpdate']);

        if (!empty($insertsAndUpdatesData['amazonOrdersToInsertToClickhouse'])) {
            try {
                $amazonOrderItems = $this->getOrderItems(
                    array_column($insertsAndUpdatesData['amazonOrdersToInsertToClickhouse'], 'amazon_order_id')
                );

                $this->insertAmazonOrderItemsToClickhouse(
                    $amazonOrderItems,
                    $insertsAndUpdatesData['amazonOrdersToInsertToClickhouse'],
                    false
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->info('Save orders finished');
    }

    private function insertAmazonOrdersToPostgres(array $amazonOrders): void
    {
        if (empty($amazonOrders)) {
            return;
        }

        $this->info(sprintf('Insert %d amazon orders started', count($amazonOrders)));
        AmazonOrder::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrder::tableName(),
                array_keys(array_values($amazonOrders)[0]),
                $amazonOrders
            )
            ->execute();
        $this->info('Insert amazon orders finished');
    }

    private function updateAmazonOrdersInPostgres(array $amazonOrders): void
    {
        if (empty($amazonOrders)) {
            return;
        }

        $this->info(sprintf('Update %d amazon orders started', count($amazonOrders)));
        $sql = AmazonOrder::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrder::tableName(),
                array_keys(array_values($amazonOrders)[0]),
                $amazonOrders
            )
            ->getRawSql();
        $sql .= ' ON CONFLICT (amazon_order_id) DO UPDATE SET';

        $lastKey = array_key_last(self::UPDATABLE_FIELDS);
        foreach (self::UPDATABLE_FIELDS as $k => $updatableField) {
            $sql .= sprintf(
                " %s = EXCLUDED.%s%s",
                $updatableField,
                $updatableField,
                $k !== $lastKey ? ',' : ''
            );
        }
        AmazonOrder::getDb()->createCommand($sql)->execute();
        $this->info('Update amazon orders finished');
    }

    /**
     *  Handles amazon orders and splits them to separate arrays of inserts and updates into different
     *  destination databases.
     *
     * @param AmazonOrder[] $amazonOrders Array of AmazonOrder as array
     * @param AmazonOrder[] $existingAmazonOrders Array of AmazonOrder as array that already exist in our db
     * @return array {
     *      amazonOrdersToInsert: AmazonOrder[],
     *      amazonOrdersToUpdate: AmazonOrder[],
     *      amazonOrdersToInsertToClickhouse: AmazonOrder[]
     * }
     */
    private function collectInsertsAndUpdates(array $amazonOrders, array $existingAmazonOrders): array
    {
        $this->info('Collecting orders inserts and updates started');

        $countOrdersNoChanges = 0;
        $amazonOrdersToInsert = [];
        $amazonOrdersToUpdate = [];
        $amazonOrdersToInsertToClickhouse = [];

        foreach ($amazonOrders as $k => $amazonOrder) {
            $existingAmazonOrder = $existingAmazonOrders[$amazonOrder['amazon_order_id']] ?? null;

            // New order should be added everywhere
            if (empty($existingAmazonOrder)) {
                $amazonOrdersToInsert[$k] = $amazonOrder;
                $amazonOrdersToInsertToClickhouse[$k] = $amazonOrder;
                continue;
            }

            $hasUpdatedInfo = strtotime($amazonOrder['last_update_date']) > strtotime($existingAmazonOrder['last_update_date']);
            $isInFinalStatusPrev = in_array($existingAmazonOrder['order_status'], AmazonOrder::FINAL_STATUSES);
            $isInFinalStatusCurr = in_array($amazonOrder['order_status'], AmazonOrder::FINAL_STATUSES);
            $shouldBeUpdatedInClickhouse = false;

            if ($hasUpdatedInfo) {
                // Do not allow to change final status of existing order
                if ($isInFinalStatusPrev) {
                    $amazonOrder['order_status'] = $existingAmazonOrder['order_status'];
                }

                if (!$isInFinalStatusPrev && $isInFinalStatusCurr) {
                    $shouldBeUpdatedInClickhouse = true;
                }
            } else {
                // Do not allow to update these fields in case if order info older than existing info
                $amazonOrder['order_status'] = $existingAmazonOrder['order_status'];
                $amazonOrder['last_update_date'] = $existingAmazonOrder['last_update_date'];
            }

            $isSomethingChanged = false;
            foreach (self::UPDATABLE_FIELDS as $fieldToUpdate) {
                if ($existingAmazonOrder[$fieldToUpdate] == $amazonOrder[$fieldToUpdate]) {
                    continue;
                }
                $existingAmazonOrder[$fieldToUpdate] = $amazonOrder[$fieldToUpdate];
                $isSomethingChanged = true;
            }

            if ($shouldBeUpdatedInClickhouse) {
                $amazonOrdersToInsertToClickhouse[$k] = $existingAmazonOrder;
            }

            if (!$isSomethingChanged) {
                $countOrdersNoChanges++;
                continue;
            }

            $amazonOrders[$k] = $existingAmazonOrder;
            $amazonOrdersToUpdate[$k] = $existingAmazonOrder;
        }

        $this->info([
            'countAllOrders' => count($amazonOrders),
            'countExistingOrders' => count($existingAmazonOrders),
            'countOrdersNoChanges' => $countOrdersNoChanges,
            'countOrdersToInsert' => count($amazonOrdersToInsert),
            'countOrdersToUpdate' => count($amazonOrdersToUpdate),
            'countOrdersToInsertToClickhouse' => count($amazonOrdersToInsertToClickhouse)
        ]);

        $this->info('Collecting orders inserts and updates finished');

        return [
            'amazonOrdersToInsert' => $amazonOrdersToInsert,
            'amazonOrdersToUpdate' => $amazonOrdersToUpdate,
            'amazonOrdersToInsertToClickhouse' => $amazonOrdersToInsertToClickhouse
        ];
    }

}
