<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\order\OrderPeriod;
use common\models\Seller;

class RenewTerminatedPeriodsService
{
    use LogToConsoleTrait;

    /** @var Seller */
    private Seller $seller;
    /**
     * @param Seller $seller
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    public function renew()
    {
        $periods = OrderPeriod::find()->where([
                'or',
                ['=', 'loading_status', OrderPeriod::LOADING_STATUS_TERMINATED],
                [
                    'and',
                    ['in', 'loading_status', [
                        OrderPeriod::LOADING_STATUS_PROCESSING,
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ],
                [
                    'and',
                    ['in', 'loading_status', [
                        OrderPeriod::LOADING_STATUS_QUEUED
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-20 minutes')->format('Y-m-d H:i:s')]
                ]
            ])
            ->all();

        if (count($periods) > 0) {
            $this->info(sprintf("Found %d periods for renew (seller %s)", count($periods), $this->seller->id));
        }

        /** @var OrderPeriod $period */
        foreach ($periods as $period) {

            $transaction = OrderPeriod::getDb()->beginTransaction();

            try {
                $newOrderPeriod = new OrderPeriod();
                $newOrderPeriod->start_date = $period->start_date;
                $newOrderPeriod->finish_date = $period->finish_date;
                $newOrderPeriod->type = $period->type;
                $newOrderPeriod->loading_status = OrderPeriod::LOADING_STATUS_NEW;
                $period->delete();
                $newOrderPeriod->saveOrThrowException();

                $transaction->commit();
            } catch (\Throwable $e) {
                $transaction->rollBack();
                throw $e;
            }
        }
    }
}
