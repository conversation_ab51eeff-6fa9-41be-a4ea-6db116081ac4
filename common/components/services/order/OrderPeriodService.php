<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\order\OrderPeriod;
use common\models\Seller;

class OrderPeriodService
{
    use LogToConsoleTrait;

    const SHIFT_WINDOW_MINUTES = 15;
    const PERIOD_MIN_SIZE_MINUTES = 10;
    const PERIOD_MAX_SIZE_MINUTES = 60 * 24;

    /** @var Seller */
    private Seller $seller;
    private DbManager $dbManager;

    /**
     * @param Seller $seller
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
        $this->dbManager = \Yii::$app->dbManager;
    }

    private function isAllInitPeriodsLoaded(): bool
    {
        return !OrderPeriod::find()->where(['type' => OrderPeriod::TYPE_INIT])->andWhere(['!=', 'loading_status', OrderPeriod::LOADING_STATUS_FINISHED])->exists();
    }

    /**
     * @throws \Exception
     */
    public function checkIsAllInitPeriodsLoaded()
    {
        $this->seller->is_order_init_periods_loaded = $this->isAllInitPeriodsLoaded();
        $this->seller->saveOrThrowException();
    }

    /**
     * @throws \Exception
     */
    public function generateInitPeriods()
    {
        $tableName = OrderPeriod::tableName();
        $startDate = OrderPeriod::getDb()->createCommand("select min(start_date) from {$tableName} where type = 'INIT'")->queryScalar();

        $dateTo = $startDate ? new \DateTime($startDate) : (new \DateTime())->modify("-" . self::PERIOD_MAX_SIZE_MINUTES . " minutes");

        $periods = $this->generatePeriods(
            new \DateTime(date('Y-m-01 H:i:s', strtotime('-2 year'))),
            $dateTo,
            self::PERIOD_MAX_SIZE_MINUTES
        );

        $this->saveInitPeriods($periods);
    }


    /**
     * <AUTHOR>
     * @param $periodStartDate
     * @param $periodEndDate
     * @throws \Exception
     */
    public function generateCustomPeriods($periodStartDate, $periodEndDate){
        $periods = $this->generatePeriods($periodStartDate, $periodEndDate, self::PERIOD_MAX_SIZE_MINUTES);

        foreach ($periods as $period) {
            $this->savePeriod($period, OrderPeriod::TYPE_INIT);
        }
    }

    /**
     * @return Period[]
     */
    protected function generatePeriods(
        \DateTime $dateStart,
        \DateTime $dateEnd,
        int $periodMaxSizeMinutes
    ): array
    {
        if ($dateStart > $dateEnd) {
            return [];
        }

        $periods = [];
        $periodStartDate = clone $dateStart;

        while(true) {
            $periodEndDate = (clone $periodStartDate)
                ->modify("+{$periodMaxSizeMinutes} minutes")
                ->modify('-1 second');
            if ($periodEndDate->getTimestamp() >= $dateEnd->getTimestamp()) {
                $periodEndDate = (clone $dateEnd)->modify('-1 second');
            }

            if ($periodStartDate >= $periodEndDate) {
                break;
            }

            $periods[] = new Period(
                $periodStartDate->format('Y-m-d H:i:s'),
                $periodEndDate->format('Y-m-d H:i:s'),
            );
            $periodStartDate = $periodEndDate->modify('+1 second');
        }

        return $periods;
    }

    /**
     * @param Period[]
     * @throws \Exception
     */
    private function saveInitPeriods(array $periods)
    {
        $transaction = OrderPeriod::getDb()->beginTransaction();

        try {
            foreach ($periods as $period) {
                $this->savePeriod($period, OrderPeriod::TYPE_INIT);
            }
            $this->seller->is_order_init_periods_created = true;
            $this->seller->update(false, ['is_order_init_periods_created']);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param Period $period
     * @param string $type
     * @throws \Exception
     */
    private function savePeriod(Period $period, string $type)
    {
        $orderPeriod = new OrderPeriod();
        $orderPeriod->finish_date = $period->getFinishDate();
        $orderPeriod->start_date = $period->getStartDate();
        $orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_NEW;
        $orderPeriod->type = $type;
        $orderPeriod->saveOrThrowException();
    }

    /**
     * @throws \Exception
     */
    public function generateRefreshPeriod()
    {
        $this->info("Generate refresh periods");
        $this->info([
            $this->seller->toArray()
        ]);
        /** @var OrderPeriod $last */
        $last = OrderPeriod::find()
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();

        if (is_null($last)) {
            return;
        }

        $periodEndDate = (new \DateTime())->modify("-" . self::SHIFT_WINDOW_MINUTES . " minutes");
        $periodStartDate = (new \DateTime($last->finish_date))->modify('+1 second');

        if ($periodEndDate->getTimestamp() - $periodStartDate->getTimestamp() < 60 * self::PERIOD_MIN_SIZE_MINUTES) {
            return;
        }
        $periods = $this->generatePeriods($periodStartDate, $periodEndDate, self::PERIOD_MAX_SIZE_MINUTES);
        $this->info('Generated total ' . count($periods));
        $this->savePeriods($periods);
    }

    /**
     * @param Period $period
     * @param string $type
     * @throws \Exception
     */
    private function savePeriods(array $periods)
    {
        $this->info("Saving periods started");
        $periodsToSave = [];

        foreach ($periods as $period) {
            $periodsToSave[] = [
                'finish_date' => $period->getFinishDate(),
                'start_date' => $period->getStartDate(),
                'loading_status' => OrderPeriod::LOADING_STATUS_NEW,
                'type' => OrderPeriod::TYPE_INIT,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }

        $periodsToSave[count($periodsToSave) - 1]['type'] = OrderPeriod::TYPE_REFRESH;
        $this->info($periodsToSave);

        OrderPeriod::getDb()
            ->createCommand()
            ->batchInsert(
                OrderPeriod::tableName(),
                array_keys(array_values($periodsToSave)[0]),
                $periodsToSave
            )
            ->execute()
        ;
        $this->info("Saving periods finihsed");
    }
}
