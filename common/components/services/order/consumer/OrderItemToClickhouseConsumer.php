<?php

namespace common\components\services\order\consumer;

use ClickHouseDB\Client;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\services\order\dataConverter\PostgresAmazonOrderItemToClickhouseConverter;
use common\components\services\order\dataConverter\PostgresAmazonOrderItemToClickhouseOrderBasedConverter;
use common\components\services\order\TransferOrderService;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\AmazonOrderBuffer;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\AmazonOrderInProgressBuffer;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class OrderItemToClickhouseConsumer extends BaseConsumer
{
    use LogToConsoleTrait;
    use ExtraFiltersTrait;

    protected DbManager $dbManager;
    protected CustomerComponent $customerComponent;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;

        $amazonOrderId = $message['amazonOrderId'] ?? null;
        $amazonOrderItemId = $message['amazonOrderItemId'] ?? null;
        $sellerId = $message['sellerId'] ?? null;
        $isJustCreated = $message['isJustCreated'] ?? false;

        if (empty($amazonOrderItemId)) {
            $this->error('Empty amazon order item id received');

            return ConsumerInterface::MSG_ACK;
        }

        $this->dbManager->setSellerId($sellerId);

        /** @var AmazonOrder $amazonOrder */
        $amazonOrder = AmazonOrder::find()->where([
            'amazon_order_id' => $amazonOrderId
        ])->noCache()->one();

        if (empty($amazonOrder)) {
            $this->error('Unable to find amazon order for order item');

            return ConsumerInterface::MSG_ACK;
        }

        $amazonOrderItem = null;
        /** @var AmazonOrderItem[] $allAmazonOrderItems */
        $allAmazonOrderItems = AmazonOrderItem::find()
            ->where(['order_id' => $amazonOrderId])
            ->noCache()
            ->all();
        $countExistingInPostgresBySku = [];

        foreach ($allAmazonOrderItems as $orderItem) {
            $key = implode('_', [
                $orderItem->sku,
                $orderItem->quantity,
                // We store item_price with this money accuracy on clickhouse
                $orderItem->item_price * 100
            ]);
            $countExistingInPostgresBySku[$key] = ($countExistingInPostgresBySku[$key] ?? 0) + 1;
            if ($orderItem->id == $amazonOrderItemId) {
                $amazonOrderItem = $orderItem;
            }
        }

        if (empty($amazonOrderItem)) {
            $this->error('Unable to find amazon order item');

            return ConsumerInterface::MSG_REJECT;
        }

        /** @var AmazonMarketplace $amazonMarketplace */
        $amazonMarketplace = AmazonMarketplace::find()
            ->where(['id' => $amazonOrder->marketplace_id])
            ->cache(60 * 5)
            ->one();

        if (empty($amazonMarketplace)) {
            $this->error('Unable to find amazon marketplace for amazon order');

            return ConsumerInterface::MSG_REJECT;
        }

        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();
        $clickhouseCustomerDb->open();
        $clickhouseClient = $clickhouseCustomerDb->getClient();
        $clickhouseClient->settings()->apply([
            'async_insert' => 1,
            'enable_http_compression' => 1
        ]);

        try {
            $this->saveOrderItemToClickhouse(
                $amazonOrderItem,
                $amazonOrder,
                $amazonMarketplace,
                $clickhouseClient
            );
        } catch (\Throwable $e) {
            $this->error($e);
            $clickhouseCustomerDb->close();

            return ConsumerInterface::MSG_REJECT;
        }

        if ($isJustCreated) {
            try {
                $this->generateAndSaveOrderBasedTransactions(
                    $amazonOrderItem,
                    $amazonOrder,
                    $amazonMarketplace,
                    $countExistingInPostgresBySku,
                    $clickhouseClient
                );
            } catch (\Throwable $e) {
                $this->error($e);
                // No reject here because not important functional
                // Will be re-generated automatically by cron
            }
        }
        $clickhouseCustomerDb->close();

        return ConsumerInterface::MSG_ACK;
    }

    /**
     * Saves amazon order item to clickhouse tables.
     * Uses amazon order status to determine which table to save to.
     *
     * @param AmazonOrderItem $amazonOrderItem
     * @param AmazonOrder $amazonOrder
     * @param AmazonMarketplace $amazonMarketplace
     * @param array $countOrderItemsBySku
     * @param Client $clickhouseClient
     * @return void
     */
    private function saveOrderItemToClickhouse(
        AmazonOrderItem $amazonOrderItem,
        AmazonOrder $amazonOrder,
        AmazonMarketplace $amazonMarketplace,
        Client $clickhouseClient
    ): void
    {
        $this->info('Saving amazon order item to clickhouse started');

        $converter = new PostgresAmazonOrderItemToClickhouseConverter();
        $clickhouseOrderItem = $converter->convert(
            $amazonOrderItem->toArray(),
            $amazonOrder->toArray(),
            $amazonMarketplace->toArray()
        );

        if (in_array($amazonOrder->order_status, AmazonOrder::FINAL_STATUSES)) {
            $this->saveFinishedItem($clickhouseOrderItem, $clickhouseClient);
        } else {
            $latestVersion = TransferOrderService::getLatestVersion($this->dbManager->getCustomerId());
            $this->info([
                'latestVersion' => $latestVersion
            ]);
            $this->saveInProgressItem($clickhouseOrderItem, $latestVersion, $clickhouseClient);
        }

        $this->info('Saving amazon order item to clickhouse finished');
    }

    /**
     * Saves amazon order item to finished table in clickhouse (amazon_order).
     * Prevents duplicates by checking for existing record.
     *
     * @param array $clickhouseOrderItem
     * @param Client $clickhouseClient
     * @return void
     */
    private function saveFinishedItem(
        array $clickhouseOrderItem,
        Client $clickhouseClient
    ): void
    {
        $finishedTableName = AmazonOrderBuffer::tableName();
        $this->info(sprintf("Saving order item to table %s started", $finishedTableName));

        $countExistingInClickhouse = AmazonOrderBuffer::find()
            ->where([
                'id' => $clickhouseOrderItem['id'],
                'order_id' => $clickhouseOrderItem['seller_id']
            ])
            ->count();

        // Already applied, prevent duplicates
        if ($countExistingInClickhouse > 0) {
            $this->info('Already applied in clickhouse, skipped');
            return;
        }

        $clickhouseClient->insert($finishedTableName, [$clickhouseOrderItem], array_keys($clickhouseOrderItem));
        $this->info(sprintf("Saving order item to table %s finished", $finishedTableName));

        $query = AmazonOrderInProgressBuffer::find()->where([
            'order_id' => $clickhouseOrderItem['order_id']
        ]);
        $this->applyClickhouseLatestVersion($query);
        $countInProgress = $query->count();

        if ($countInProgress > 0) {
            $this->saveInProgressItem($clickhouseOrderItem, 0, $clickhouseClient);
        }
    }

    /**
     * Saves amazon order item to not finished table in clickhouse (amazon_order_in_progress_v1).
     * Prevents duplicates by checking for existing record.
     *
     * @param array $clickhouseOrderItem
     * @param int $version
     * @param Client $clickhouseClient
     * @return void
     */
    private function saveInProgressItem(
        array $clickhouseOrderItem,
        int $version,
        Client $clickhouseClient
    ): void
    {
        $inProgressTableName = AmazonOrderInProgressBuffer::tableName();
        $this->info(sprintf(
            "Saving order item with version %d to table %s started",
            $version,
            $inProgressTableName
        ));

        $countExistingInClickhouse = AmazonOrderInProgressBuffer::find()
            ->where([
                'id' => $clickhouseOrderItem['id'],
                'order_id' => $clickhouseOrderItem['order_id'],
                'version' => $version
            ])
            ->count();

        // Already applied, prevent duplicates
        if ($countExistingInClickhouse > 0) {
            $this->info('Already applied in clickhouse, skipped');
            return;
        }

        $clickhouseOrderItem['version'] = $version;
        $clickhouseClient->insert($inProgressTableName, [$clickhouseOrderItem], array_keys($clickhouseOrderItem));

        $this->info(sprintf(
            "Saving order item with version %d to table %s finished",
            $version,
            $inProgressTableName
        ));
    }

    /**
     * Generates and saves order based transactions for amazon order item (delegates generation logic to separate service).
     * Prevents duplicates by checking for existing record.
     *
     * @param AmazonOrderItem $amazonOrderItem
     * @param AmazonOrder $amazonOrder
     * @param AmazonMarketplace $amazonMarketplace
     * @param array $countExistingInPostgresBySku
     * @param Client $clickhouseClient
     * @return void
     */
    private function generateAndSaveOrderBasedTransactions(
        AmazonOrderItem $amazonOrderItem,
        AmazonOrder $amazonOrder,
        AmazonMarketplace $amazonMarketplace,
        array $countExistingInPostgresBySku,
        Client $clickhouseClient
    ): void
    {
        $this->info('Generating and saving order based transactions for amazon order item started');

        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $key = implode('_', [
            $amazonOrderItem->sku,
            $amazonOrderItem->quantity,
            // We store item_price with this money accuracy on clickhouse
            $amazonOrderItem->item_price * 100
        ]);
        $countExistingInClickhouse = \common\models\customer\clickhouse\OrderBasedTransaction::find()
            ->where([
                'AmazonOrderId' => $amazonOrderItem->order_id,
                'SellerSKU' => $amazonOrderItem->sku,
                'CategoryId' => $organicSalesId,
                'Quantity' => $amazonOrderItem->quantity,
                'Amount' => ($amazonOrderItem->item_price - $amazonOrderItem->item_tax) * $moneyAccuracy,
                'COGCategoryId' => 0
            ])
            ->count();
        $countExistingInPostgres = $countExistingInPostgresBySku[$key] ?? 0;

        // Already applied, prevent duplicates
        if ($countExistingInClickhouse >= $countExistingInPostgres) {
            $this->info('Already applied in clickhouse, skipped');
            return;
        }

        $product = Product::findOne([
            'marketplace_id' => $amazonOrder->marketplace_id,
            'seller_id' => $amazonOrder->seller_id,
            'sku' => $amazonOrderItem->sku
        ]) ?? new Product();

        $dataConverter = new PostgresAmazonOrderItemToClickhouseOrderBasedConverter();
        $orderBasedOrder = $dataConverter->convert(
            $amazonOrderItem->toArray(),
            $amazonOrder->toArray(),
            $product->toArray(),
            $amazonMarketplace->toArray()
        );

        $orderBasedTransactionsService = new OrderBasedTransaction();
        $orderBasedTransactions = $orderBasedTransactionsService
            ->generateOrderBasedTransactionsForOrders([$orderBasedOrder]);

        $orderBasedTransactionsService
            ->saveGeneratedTransactions(
                $orderBasedTransactions,
                \common\models\customer\clickhouse\OrderBasedTransaction::tableName(),
                $clickhouseClient
            );

        $this->info('Generating and saving order based transactions for amazon order item finished');
    }
}
