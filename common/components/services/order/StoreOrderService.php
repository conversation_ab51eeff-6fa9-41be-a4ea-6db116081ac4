<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\EmptyAmazonOrderIdException;
use common\models\order\AmazonOrder;
use SellingPartnerApi\Model\OrdersV0\Order;

class StoreOrderService
{
    protected string $sellerId = '';
    public function __construct(string $sellerId)
    {
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($sellerId);
        $this->sellerId = $sellerId;
    }

    public function saveData(Order $order, $orderPeriodId = null)
    {
        if (empty($order->getAmazonOrderId())) {
            throw new EmptyAmazonOrderIdException();
        }

        /** @var AmazonOrder $model */
        $model = AmazonOrder::find()->where(['amazon_order_id' => $order->getAmazonOrderId()])->one() ?: new AmazonOrder();

        $isOutdatedInfo = false;
        if (!$model->isNewRecord) {
            if (strtotime($model->last_update_date) >= strtotime($order->getLastUpdateDate())) {
                $isOutdatedInfo = true;
            }
        }

        $this->setItemsLoadingStatus($model);

        if (!$isOutdatedInfo) {
            $model->last_update_date = date('Y-m-d H:i:s', strtotime($order->getLastUpdateDate()));
            $model->order_status = $order->getOrderStatus();
        }

        $model->order_period_id = $orderPeriodId;
        $model->amazon_order_id = $order->getAmazonOrderId();
        $model->seller_order_id = $order->getSellerOrderId();
        $model->purchase_date = date('Y-m-d H:i:s', strtotime($order->getPurchaseDate()));
        $model->seller_id = $this->sellerId;
        $model->marketplace_id = $order->getMarketplaceId();
        $model->order_type = $order->getOrderType();
        $model->fulfillment_channel = $order->getFulfillmentChannel();
        $model->latest_ship_date = $order->getLatestShipDate();
        $model->earliest_ship_date = $order->getEarliestShipDate();
        $model->payment_method = $order->getPaymentMethod();
        $model->is_business_order = $order->getIsBusinessOrder();
        $model->is_prime = $order->getIsPrime();
        $model->is_premium_order = $order->getIsPremiumOrder();
        $model->is_global_express_enabled = $order->getIsGlobalExpressEnabled();
        $model->is_replacement_order = $order->getIsReplacementOrder();
        $model->is_sold_by_ab = $order->getIsSoldByAb();
        $model->is_ispu = $order->getIsIspu();
        $model->is_access_point_order = $order->getIsAccessPointOrder();
        $model->has_regulated_items = $order->getHasRegulatedItems();
        $model->shipment_service_level_category = $order->getShipmentServiceLevelCategory();
        $model->ship_service_level = $order->getShipServiceLevel();
        $model->payment_method = $order->getPaymentMethod();

        $model->saveOrThrowException();
    }

    protected function setItemsLoadingStatus(AmazonOrder $model)
    {
        if ($model->isNewRecord) {
            $model->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_NEW;
            return;
        }

        if ($model->order_status === Order::ORDER_STATUS_SHIPPED || $model->order_status === Order::ORDER_STATUS_CANCELED) {
            return;
        }

        $model->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_NEW;
    }
}
