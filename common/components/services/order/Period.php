<?php

namespace common\components\services\order;

class Period
{
    private string $startDate;
    private string $finishDate;

    /**
     * @param string $startDate
     * @param string $finishDate
     */
    public function __construct(string $startDate, string $finishDate)
    {
        $this->startDate = $startDate;
        $this->finishDate = $finishDate;
    }

    /**
     * @return string
     */
    public function getStartDate(): string
    {
        return $this->startDate;
    }

    /**
     * @return string
     */
    public function getFinishDate(): string
    {
        return $this->finishDate;
    }
}
