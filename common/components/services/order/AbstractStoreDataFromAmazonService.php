<?php

namespace common\components\services\order;

use common\components\COGSync\ProductsSaver;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use yii\caching\Cache;

abstract class AbstractStoreDataFromAmazonService
{
    use LogToConsoleTrait;
    use ExtraFiltersTrait;

    protected DbManager $dbManager;
    protected Cache $arrayCache;
    protected MessagesSender $messagesSender;
    protected ProductsSaver $productsSaver;

    public function __construct(string $sellerId)
    {
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->arrayCache = \Yii::$app->arrayCache;
        $this->dbManager->setSellerId($sellerId);
        $this->messagesSender = new MessagesSender();
        $this->productsSaver = new ProductsSaver();
    }


    /**
     * @param AmazonOrder[] $amazonOrders Array of AmazonOrder as array
     * @return void
     */
    protected function insertAmazonOrderItemsToClickhouse(
        array $amazonOrderItems,
        array $amazonOrders,
        bool $isAmazonOrderItemJustCreated
    ): void
    {
        foreach ($amazonOrderItems as $amazonOrderItem) {
            try {
                $amazonOrder = $amazonOrders[$amazonOrderItem['order_id']];

                if (empty($amazonOrder)) {
                    throw new \Exception("Unable to find amazon order for amazon order item");
                }

                $this->messagesSender->saveAmazonOrderItemToClickhouse(
                    $amazonOrderItem['id'],
                    $amazonOrder['amazon_order_id'],
                    $amazonOrder['seller_id'],
                    $amazonOrder['purchase_date'],
                    $isAmazonOrderItemJustCreated
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    /**
     * Retrieves all orders related to amazon order items.
     *
     * @return AmazonOrder[] Array of AmazonOrder as array
     */
    protected function getAmazonOrders(array $orderIds): array
    {
        if (empty($orderIds)) {
            return [];
        }

        $this->info(sprintf('Getting exiting amazon orders for %d order ids', count($orderIds)));

        $orderIds = array_unique($orderIds);
        $orders =  AmazonOrder::find()
            ->where(['amazon_order_id' => $orderIds])
            ->indexBy('amazon_order_id')
            ->noCache()
            ->asArray()
            ->all();

        $this->info(sprintf('Finished. Fetched %d orders', count($orders)));

        return $orders;
    }

    /**
     * @param array $orderIds
     * @return AmazonOrderItem[] Array of AmazonOrderItem as array
     */
    protected function getOrderItems(array $orderIds): array
    {
        if (empty($orderIds)) {
            return [];
        }

        $this->info(sprintf('Getting order items for %d orders started', count($orderIds)));

        $orderItems = AmazonOrderItem::find()
            ->where(['order_id' => $orderIds])
            ->asArray()
            ->noCache()
            ->all();

        $this->info('Getting order items finished');

        return $orderItems;
    }

    /**
     * @param array $sellerIds
     * @param array $marketplaceIds
     * @param array $sellerSkus
     * @return Product[] Array of Product as array
     */
    protected function getProducts(
        array $marketplaceIds,
        array $sellerIds,
        array $sellerSkus
    ): array
    {
        $this->info('Getting existing products started');

        $marketplaceIds = array_unique($marketplaceIds);
        $sellerIds = array_unique($sellerIds);
        $sellerSkus = array_unique($sellerSkus);

        if (empty($marketplaceIds) || empty($sellerIds) || empty($sellerSkus)) {
            return [];
        }

        $products = Product::find()
            ->where([
                'AND',
                ['marketplace_id' => $marketplaceIds],
                ['seller_id' => $sellerIds],
                ['sku' => $sellerSkus]
            ])
            ->indexBy(function ($item) {
                return implode('_', [
                    $item['marketplace_id'],
                    $item['seller_id'],
                    $item['sku']
                ]);
            })
            ->asArray()
            ->all();

        $this->info(sprintf('Finished. Fetched %d products', count($products)));

        return $products;
    }

    protected function getAmazonMarketplaces()
    {
        return $this->arrayCache->getOrSet(__FUNCTION__, function () {
            return AmazonMarketplace::find()
                ->indexBy('id')
                ->cache(60 * 60)
                ->asArray()
                ->all();
        });
    }

    /**
     * Removes from array of data those elements which contain removal orders data.
     * We should not process removal orders.
     *
     * @param array $data Array of order items, orders, etc.
     * @param string $key Order id key (order_id, amazon_order_id, etc.)
     * @return array
     */
    public function filterRemovalData(array $data, string $key): array
    {
        return array_filter($data, function ($item, $k) use ($key) {
            $orderId = $item[$key] ?? '';

            return !str_starts_with($orderId, 'S0');
        }, ARRAY_FILTER_USE_BOTH);
    }
}
