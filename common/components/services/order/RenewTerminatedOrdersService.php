<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\order\AmazonOrder;
use common\models\Seller;

class RenewTerminatedOrdersService
{
    use LogToConsoleTrait;

    private Seller $seller;

    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    public function renew()
    {
        $this->info(sprintf("Started for customer %s, seller %s", $this->seller->customer_id, $this->seller->id));

        $limit = 10000;
        $count = 0;
        do{
            $ids = AmazonOrder::find()->select(['amazon_order.amazon_order_id'])->where([
                'or',
                [
                    'and',
                    ['in', 'items_loading_status', [
                        AmazonOrder::ITEMS_LOADING_STATUS_TERMINATED,
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ],
                [
                    'and',
                    ['in', 'items_loading_status', [
                        AmazonOrder::ITEMS_LOADING_STATUS_PROCESSING,
                        AmazonOrder::ITEMS_LOADING_STATUS_QUEUED
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ],
            ])->limit($limit)->column();

            if (count($ids)) {
                AmazonOrder::updateAll([
                    'items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_NEW,
                    'updated_at' => date('Y-m-d H:i:s')
                ], ['amazon_order.amazon_order_id' => $ids]);
                $count += count($ids);
            }
        }while(count($ids) === $limit);

        if ($count) {
            $this->info(sprintf("Found %d orders", $count));
        }
    }
}
