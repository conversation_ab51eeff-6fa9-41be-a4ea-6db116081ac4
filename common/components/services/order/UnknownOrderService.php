<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\models\order\UnknownAmazonOrder;

class UnknownOrderService
{
    public const ERROR_LIMIT = 3;

    public function __construct(string $sellerId)
    {
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($sellerId);
    }

    public function increaseErrors(string $amazonOrderId)
    {
        $unknownAmazonOrder = UnknownAmazonOrder::findOne($amazonOrderId);

        if (is_null($unknownAmazonOrder)) {
            $unknownAmazonOrder = new UnknownAmazonOrder();
            $unknownAmazonOrder->amazon_order_id = $amazonOrderId;
            $unknownAmazonOrder->errors_count = 0;
        }

        $unknownAmazonOrder->errors_count = $unknownAmazonOrder->errors_count + 1;
        $unknownAmazonOrder->saveOrThrowException();
    }

    public function isReachedErrorLimit(string $amazonOrderId): bool
    {
        $unknownAmazonOrder = UnknownAmazonOrder::findOne($amazonOrderId);

        if (is_null($unknownAmazonOrder)) {
            return false;
        }

        return $unknownAmazonOrder->errors_count > self::ERROR_LIMIT;
    }
}
