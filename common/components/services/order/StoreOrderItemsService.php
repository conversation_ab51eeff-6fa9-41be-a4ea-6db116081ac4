<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Money;
use SellingPartnerApi\Model\OrdersV0\OrderItem;
use SellingPartnerApi\Model\OrdersV0\OrderItemsList;
use yii\helpers\StringHelper;

class StoreOrderItemsService
{
    use LogToConsoleTrait;

    protected AmazonOrder $amazonOrder;
    protected Seller $seller;
    private DbManager $sellerDbManager;
    private Prometheus $prometheus;

    public function __construct(AmazonOrder $amazonOrder, string $sellerId)
    {
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($sellerId);
        $this->prometheus = \Yii::$app->prometheus;
        $this->sellerDbManager = $sellerDbManager;
        $this->amazonOrder = $amazonOrder;
        $this->seller = Seller::find()->where(['id' => strtoupper($sellerId)])->one(\Yii::$app->db);
    }

    public function saveData(OrderItemsList $orderItemsList)
    {
        try {
            $timeStart = microtime(true);

            $items = $orderItemsList->getOrderItems();
            $countItems = count($items);

            foreach ($items as $item) {
                $this->saveItem($item);
            }

            $timeElapsed = microtime(true) - $timeStart;
            $this->prometheus->performancePerSecond(
                'saving_order_items_to_db',
                $countItems / $timeElapsed
            );
            $this->prometheus->customCounter(
                'order_items_saved_to_db',
                $countItems
            );
        } catch (\Throwable $e) {
            $this->error($e);
            $this->amazonOrder->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_TERMINATED;
            $this->amazonOrder->saveOrThrowException();
        }
    }

    protected function saveItem(OrderItem $item)
    {
        $model = AmazonOrderItem::find()->where(['order_item_id' => $item->getOrderItemId()])->one();

        if (empty($model)) {
            $model = AmazonOrderItem::find()
                ->where([
                    'order_id' => $this->amazonOrder->amazon_order_id,
                    'sku' => $item->getSellerSku(),
                ])
                ->andWhere([
                    'like', 'order_item_id', 'REPORT_'
                ])
                ->one();
        }

        if (is_null($model)) {
            $model = new AmazonOrderItem();
        }

        $model->order_id = $this->amazonOrder->amazon_order_id;
        $model->order_purchase_date = $this->amazonOrder->purchase_date;
        $model->order_marketplace_id = $this->amazonOrder->marketplace_id;
        $model->date = date('Y-m-d H:i:s');

        $model->asin = $item->getAsin();
        $model->sku = $item->getSellerSku();
        $model->order_item_id = $item->getOrderItemId();
        $model->title = StringHelper::truncate($item->getTitle(), 200, '');
        $model->quantity_shipped = $item->getQuantityShipped();

        $quantity = $item->getQuantityOrdered();
        if ($model->isNewRecord || $quantity > 0) {
            $model->quantity = $quantity;
        }

        $itemPrice = $this->getPriceFromMoney($item->getItemPrice());
        if ($model->isNewRecord || $itemPrice > 0) {
            $model->item_price = $itemPrice;
        }

        $shippingPrice = $this->getPriceFromMoney($item->getShippingPrice());
        if ($model->isNewRecord || $shippingPrice > 0) {
            $model->shipping_price = $shippingPrice;
        }

        $itemTax = $this->getPriceFromMoney($item->getItemTax());
        if ($model->isNewRecord || $itemTax > 0) {
            $model->item_tax = $itemTax;
        }

        $shippingTax = $this->getPriceFromMoney($item->getShippingTax());
        if ($model->isNewRecord || $shippingTax > 0) {
            $model->shipping_tax = $shippingTax;
        }

        $shippingDiscount = $this->getPriceFromMoney($item->getShippingDiscount());
        if ($model->isNewRecord || $shippingDiscount > 0) {
            $model->shipping_discount = $shippingDiscount;
        }

        $promotionDiscount = $this->getPriceFromMoney($item->getPromotionDiscount());
        if ($model->isNewRecord || $promotionDiscount > 0) {
            $model->promotion_discount = $promotionDiscount;
        }

        $model->cod_fee = $this->getPriceFromMoney($item->getCodFee());
        $model->cod_fee_discount = $this->getPriceFromMoney($item->getCodFeeDiscount());
        $model->promotion_id = StringHelper::truncate($this->listToString($item->getPromotionIds()), 100, '');
        $model->condition_id = StringHelper::truncate($item->getConditionId(), 20, '');
        $model->condition_subtype_id = StringHelper::truncate($item->getConditionSubtypeId(), 20, '');
        $model->condition_note = StringHelper::truncate($item->getConditionNote(), 200, '');
        $model->scheduled_delivery_start_date = $item->getScheduledDeliveryStartDate();
        $model->scheduled_delivery_end_date = $item->getScheduledDeliveryEndDate();
        $model->is_gift = $item->getIsGift();
        $model->is_transparency = $item->getIsTransparency();

        $model->saveOrThrowException();
        $this->createProductIfNotExists($this->amazonOrder, $model);
    }

    public function listToString(?array $list, string $separator = ','): string
    {
        if (is_null($list)) {
            return '';
        }
        return implode($separator, $list);
    }

    public function getPriceFromMoney(?Money $money = null, $default = 0)
    {
        return $money ? $money->getAmount() : $default;
    }

    public function createProductIfNotExists(AmazonOrder $amazonOrder, AmazonOrderItem $amazonOrderItem): void
    {
        if (empty($amazonOrder->marketplace_id)
            || empty($amazonOrder->seller_id)
            || empty($amazonOrderItem->sku)
        ) {
            return;
        }

        try {
            /** @var Product $product */
            $product = Product::find()->where([
                'marketplace_id' => $amazonOrder->marketplace_id,
                'seller_id' => $amazonOrder->seller_id,
                'sku' => $amazonOrderItem->sku,
            ])->noCache()->one();

            $stockType = Product::STOCK_TYPE_FBM;
            if ($amazonOrder->fulfillment_channel === AmazonOrder::FULFILMENT_CHANNEL_AFN) {
                $stockType = Product::STOCK_TYPE_FBA;
            } else if ($amazonOrder->fulfillment_channel === AmazonOrder::FULFILMENT_CHANNEL_MFN) {
                $stockType = Product::STOCK_TYPE_FBM;
            }

            if (!empty($product)) {
                if (!$product->is_multiple_stock_type && $stockType !== $product->stock_type) {
                    $product->is_multiple_stock_type = true;
                    $product->stock_type = $stockType;
                    $product->save(['is_multiple_stock_type', 'stock_type']);
                }
                return;
            }

            $marketplace = AmazonMarketplace::getById($amazonOrder->marketplace_id);

            $conditionMap = array_flip(Product::CONDITIONS_MAP);
            $conditionString = implode(
                '; ',
                array_filter(array_unique([$amazonOrderItem->condition_id, $amazonOrderItem->condition_subtype_id]))
            );

            $product = new Product();
            $product->marketplace_id = $amazonOrder->marketplace_id;
            $product->seller_id = $amazonOrder->seller_id;
            $product->sku = $amazonOrderItem->sku;
            $product->asin = $amazonOrderItem->asin ?: null;
            $product->title = $amazonOrderItem->title ?: null;
            $product->condition = $conditionMap[$conditionString] ?? null;
            $product->source = Product::SOURCE_ORDER_INFO;
            $product->currency_code = $marketplace->currency_code ?? null;
            $product->stock_type = $stockType;

            $product->save(false);
        } catch (\Throwable $e) {
            if (false === strpos($e->getMessage(), 'Unique violation')) {
                $this->error($e);
            }
        }
    }
}
