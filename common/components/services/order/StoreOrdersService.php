<?php

namespace common\components\services\order;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\models\order\AmazonOrder;
use common\models\order\OrderPeriod;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\OrdersList;

class StoreOrdersService
{
    use LogToConsoleTrait;

    protected OrderPeriod $orderPeriod;
    protected Seller $seller;
    private DbManager $sellerDbManager;
    private Prometheus $prometheus;

    public function __construct(OrderPeriod $orderPeriod, string $sellerId)
    {
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($sellerId);
        $this->prometheus = \Yii::$app->prometheus;
        $this->sellerDbManager = $sellerDbManager;
        $this->orderPeriod = $orderPeriod;
        $this->seller = Seller::find()->where(['id' => strtoupper($sellerId)])->one(\Yii::$app->db);
    }

    public function saveData(OrdersList $ordersList)
    {
        try {
            $timeStart = microtime(true);

            $orders = $ordersList->getOrders();
            $countItems = count($orders);

            $storeOrderService = new StoreOrderService($this->seller->id);

            foreach ($orders as $order) {
                $storeOrderService->saveData($order, $this->orderPeriod->id);
            }

            $this->orderPeriod->updated_at = date('Y-m-d H:i:s');
            $this->orderPeriod->update('false', ['updated_at']);

            $timeElapsed = microtime(true) - $timeStart;
            $this->prometheus->performancePerSecond(
                'saving_orders_to_db',
                $countItems / $timeElapsed
            );
            $this->prometheus->customCounter(
                'orders_saved_to_db_saved_to_db',
                $countItems
            );
        } catch (\Throwable $e) {
            $this->error($e);
            $this->orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_TERMINATED;
            $this->orderPeriod->saveOrThrowException();
        }
    }

    /**
     * @throws \Exception
     */
    public function markAsFinished()
    {
        if ($this->orderPeriod->loading_status == OrderPeriod::LOADING_STATUS_PROCESSING) {
            $this->orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_FINISHED;
            $this->orderPeriod->saveOrThrowException();
        }
    }

    /**
     * @throws \Exception
     */
    public function markAsTerminated()
    {
        $this->orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_TERMINATED;
        $this->orderPeriod->saveOrThrowException();
    }
}
