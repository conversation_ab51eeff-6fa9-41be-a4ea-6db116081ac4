<?php

namespace common\components\services\order\dataConverter;

use common\components\LogToConsoleTrait;
use common\models\order\AmazonOrder;
use SellingPartnerApi\Model\OrdersV0\Order;

/**
 * Converts order given from amazon API to our order.
 */
class OrderFromAmazonAPIToOurOrderConverter
{
    use LogToConsoleTrait;

    public function convert(Order $orderFromAmazon, string $sellerId, string $orderPeriodId = null): array
    {
        return [
            'order_period_id' => $orderPeriodId,
            'amazon_order_id' => $orderFromAmazon->getAmazonOrderId(),
            'seller_order_id' => $orderFromAmazon->getSellerOrderId(),
            'order_status' => $orderFromAmazon->getOrderStatus(),
            'last_update_date' => date('Y-m-d H:i:s', strtotime($orderFromAmazon->getLastUpdateDate())),
            'purchase_date' => date('Y-m-d H:i:s', strtotime($orderFromAmazon->getPurchaseDate())),
            'seller_id' => $sellerId,
            'marketplace_id' => $orderFromAmazon->getMarketplaceId(),
            'order_type' => $orderFromAmazon->getOrderType(),
            'fulfillment_channel' => $orderFromAmazon->getFulfillmentChannel(),
            'latest_ship_date' => $orderFromAmazon->getLatestShipDate(),
            'earliest_ship_date' => $orderFromAmazon->getEarliestShipDate(),
            'is_business_order' => $orderFromAmazon->getIsBusinessOrder(),
            'is_prime' => $orderFromAmazon->getIsPrime(),
            'is_premium_order' => $orderFromAmazon->getIsPremiumOrder(),
            'is_global_express_enabled' => $orderFromAmazon->getIsGlobalExpressEnabled(),
            'is_replacement_order' => $orderFromAmazon->getIsReplacementOrder(),
            'is_sold_by_ab' => $orderFromAmazon->getIsSoldByAb(),
            'is_ispu' => $orderFromAmazon->getIsIspu(),
            'is_access_point_order' => $orderFromAmazon->getIsAccessPointOrder(),
            'has_regulated_items' => $orderFromAmazon->getHasRegulatedItems(),
            'shipment_service_level_category' => $orderFromAmazon->getShipmentServiceLevelCategory(),
            'ship_service_level' => $orderFromAmazon->getShipServiceLevel(),
            'payment_method' => $orderFromAmazon->getPaymentMethod(),
            'items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_NEW,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
}
