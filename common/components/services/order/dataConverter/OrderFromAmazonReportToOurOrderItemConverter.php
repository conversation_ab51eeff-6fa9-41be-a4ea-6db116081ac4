<?php

namespace common\components\services\order\dataConverter;

use common\components\LogToConsoleTrait;
use yii\helpers\StringHelper;

/**
 * Converts order given from amazon report to our order item.
 */
class OrderFromAmazonReportToOurOrderItemConverter
{
    use LogToConsoleTrait;

    public function convert(array $orderFromReport, int $ourReportId, string $marketplaceId): array
    {
        return [
            'order_item_id' => 'REPORT_' . $ourReportId . '_' . uniqid(''),
            'order_id' => $orderFromReport['amazon-order-id'],
            'sku' => $orderFromReport['sku'],
            'order_marketplace_id' => $marketplaceId,
            'asin' => $orderFromReport['asin'] ?: '',
            'title' => StringHelper::truncate($orderFromReport['product-name'] ?: '', 200, ''),
            'quantity' => $orderFromReport['quantity'] ?: 0,
            'item_price' => $orderFromReport['item-price'] ?: 0,
            'item_tax' => $orderFromReport['item-tax'] ?: 0,
            'shipping_price' => $orderFromReport['shipping-price'] ?: 0,
            'shipping_tax' => $orderFromReport['shipping-tax'] ?: 0,
            'shipping_discount' => $orderFromReport['ship-promotion-discount'] ?: 0,
            'promotion_discount' => $orderFromReport['item-promotion-discount'] ?: 0,
            'order_purchase_date' => date('Y-m-d H:i:s', strtotime($orderFromReport['purchase-date'])),
            'date' => date('Y-m-d H:i:s'),
        ];
    }
}
