<?php

namespace common\components\services\order\dataConverter;

use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use Selling<PERSON><PERSON>ner<PERSON><PERSON>\Model\OrdersV0\Order;

/**
 * Converts order item given from amazon report to our order.
 */
class OrderFromAmazonReportToOurOrderConverter
{
    use LogToConsoleTrait;

    private const FULFILMENT_CHANNEL_MAP = [
        'Amazon' => AmazonOrder::FULFILMENT_CHANNEL_AFN,
        'Merchant' => AmazonOrder::FULFILMENT_CHANNEL_MFN,
    ];

    private const PRODUCT_STOCK_TYPE_MAP = [
        'Amazon' => Product::STOCK_TYPE_FBA,
        'Merchant' => Product::STOCK_TYPE_FBM,
    ];

    public function convert(array $orderFromReport, string $sellerId, string $marketplaceId): array
    {
        return [
            'seller_id' => $sellerId,
            'amazon_order_id' => $orderFromReport['amazon-order-id'],
            'seller_order_id' => $orderFromReport['merchant-order-id'],
            'purchase_date' => date('Y-m-d H:i:s', strtotime($orderFromReport['purchase-date'])),
            'last_update_date' => date('Y-m-d H:i:s', strtotime($orderFromReport['last-updated-date'])),
            'order_status' => $this->mapOrderStatus($orderFromReport['order-status']),
            'order_status_from_report' => $orderFromReport['order-status'],
            'marketplace_id' => $marketplaceId,
            'fulfillment_channel' => self::FULFILMENT_CHANNEL_MAP[$orderFromReport['fulfillment-channel']],
            'items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_FINISHED,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    private function mapOrderStatus(?string $orderStatus): string
    {
        if ($orderStatus === AmazonOrder::ORDER_STATUS_WRONG_CANCELED) {
            $orderStatus = Order::ORDER_STATUS_CANCELED;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_ON_TRIAL) {
            $orderStatus = Order::ORDER_STATUS_PENDING;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_SHIPPING) {
            $orderStatus = Order::ORDER_STATUS_UNSHIPPED;
        }

        if ($orderStatus === AmazonOrder::ORDER_STATUS_COMPLETE) {
            $orderStatus = Order::ORDER_STATUS_SHIPPED;
        }

        if (false !== strpos($orderStatus, Order::ORDER_STATUS_SHIPPED)) {
            $orderStatus = Order::ORDER_STATUS_SHIPPED;
        }
        if (false !== strpos($orderStatus, Order::ORDER_STATUS_PENDING)) {
            $orderStatus = Order::ORDER_STATUS_PENDING;
        }

        $allowedStatuses = [
            Order::ORDER_STATUS_PENDING,
            Order::ORDER_STATUS_UNSHIPPED,
            Order::ORDER_STATUS_PARTIALLY_SHIPPED,
            Order::ORDER_STATUS_SHIPPED,
            Order::ORDER_STATUS_CANCELED,
            Order::ORDER_STATUS_UNFULFILLABLE,
            Order::ORDER_STATUS_INVOICE_UNCONFIRMED,
            Order::ORDER_STATUS_PENDING_AVAILABILITY,
        ];

        if (in_array($orderStatus, $allowedStatuses)) {
            return $orderStatus;
        }

        throw new \Exception("Unable to map order status from amazon report");
    }
}
