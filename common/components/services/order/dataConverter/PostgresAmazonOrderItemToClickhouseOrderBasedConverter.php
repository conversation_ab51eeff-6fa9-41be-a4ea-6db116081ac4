<?php

namespace common\components\services\order\dataConverter;

use common\models\order\AmazonOrder;

/**
 * Converts amazon order item from our postgres format to format
 * that uses service which generates order based transactions:
 * common\components\clickhouse\materializedViews\tables\OrderBasedTransaction,
 * function generateOrderBasedTransactionsForOrders
 */
class PostgresAmazonOrderItemToClickhouseOrderBasedConverter
{
    /**
     * @param array $amazonOrderItem AmazonOrderItem as array
     * @param array $amazonOrder AmazonOrder as array
     * @param array $product Product as array
     * @param array $amazonMarketplace AmazonMarketplace as array
     * @return array
     */
    public function convert(
        array $amazonOrderItem,
        array $amazonOrder,
        array $product,
        array $amazonMarketplace
    ): array
    {
        $offerType = AmazonOrder::OFFER_TYPE_B2C;

        if ($amazonOrder['is_business_order'] == 1) {
            $offerType = AmazonOrder::OFFER_TYPE_B2B;
        }

        return [
            'marketplace_id' => $amazonOrder['marketplace_id'],
            'seller_id' => $amazonOrder['seller_id'],
            'sku' => $amazonOrderItem['sku'],
            'product_id' => $product['id'] ?? null,
            'ean' => $product['ean'] ?? null,
            'asin' => $product['asin'] ?? null,
            'isbn' => $product['isbn'] ?? null,
            'upc' => $product['upc'] ?? null,
            'parent_asin' => $product['parent_asin'] ?? null,
            'brand' => $product['brand'] ?? null,
            'model' => $product['model'] ?? null,
            'product_type' => $product['product_type'] ?? null,
            'manufacturer' => $product['manufacturer'] ?? null,
            'age_range' => $product['age_range'] ?? null,
            'adult_product' => (int)($product['adult_product'] ?? null),
            'title' => $product['title'] ?? null,
            'order_id' => $amazonOrderItem['order_id'],
            'quantity' => $amazonOrderItem['quantity'],
            'item_price' => $amazonOrderItem['item_price'],
            'shipping_price' => $amazonOrderItem['shipping_price'],
            'item_tax' => $amazonOrderItem['item_tax'],
            'shipping_tax' => $amazonOrderItem['shipping_tax'],
            'shipping_discount' => $amazonOrderItem['shipping_discount'],
            'promotion_discount' => $amazonOrderItem['promotion_discount'],
            'currency_code' => $amazonMarketplace['currency_code'],
            'purchase_date' => $amazonOrder['purchase_date'],
            'fulfillment_channel' => $amazonOrder['fulfillment_channel'],
            'offer_type' => $offerType
        ];
    }
}
