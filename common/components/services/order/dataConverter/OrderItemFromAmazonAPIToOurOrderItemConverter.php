<?php

namespace common\components\services\order\dataConverter;

use common\components\LogToConsoleTrait;
use Selling<PERSON><PERSON>nerApi\Model\OrdersV0\Money;
use SellingPartnerApi\Model\OrdersV0\OrderItem;
use yii\helpers\StringHelper;

/**
 * Converts order item given from amazon API to our order item.
 */
class OrderItemFromAmazonAPIToOurOrderItemConverter
{
    use LogToConsoleTrait;

    public function convert(OrderItem $orderItemFromAmazon, array $ourOrder): array
    {
        return [
            'order_id' => $ourOrder['amazon_order_id'],
            'order_purchase_date' => $ourOrder['purchase_date'],
            'order_marketplace_id' => $ourOrder['marketplace_id'],
            'date' => date('Y-m-d H:i:s'),
            'asin' => $orderItemFromAmazon->getAsin(),
            'sku' => $orderItemFromAmazon->getSellerSku(),
            'order_item_id' => $orderItemFromAmazon->getOrderItemId(),
            'title' => StringHelper::truncate($orderItemFromAmazon->getTitle(), 200, ''),
            'quantity_shipped' => $orderItemFromAmazon->getQuantityShipped(),
            'quantity' => $orderItemFromAmazon->getQuantityOrdered(),
            'item_price' => $this->getPriceFromMoney($orderItemFromAmazon->getItemPrice()),
            'shipping_price' => $this->getPriceFromMoney($orderItemFromAmazon->getShippingPrice()),
            'item_tax' => $this->getPriceFromMoney($orderItemFromAmazon->getItemTax()),
            'shipping_tax' => $this->getPriceFromMoney($orderItemFromAmazon->getShippingTax()),
            'shipping_discount' => $this->getPriceFromMoney($orderItemFromAmazon->getShippingDiscount()),
            'promotion_discount' => $this->getPriceFromMoney($orderItemFromAmazon->getPromotionDiscount()),
            'cod_fee' => $this->getPriceFromMoney($orderItemFromAmazon->getCodFee()),
            'cod_fee_discount' => $this->getPriceFromMoney($orderItemFromAmazon->getCodFeeDiscount()),
            'promotion_id' => StringHelper::truncate($this->listToString($orderItemFromAmazon->getPromotionIds()), 100, ''),
            'condition_id' => StringHelper::truncate($orderItemFromAmazon->getConditionId(), 20, ''),
            'condition_subtype_id' => StringHelper::truncate($orderItemFromAmazon->getConditionSubtypeId(), 20, ''),
            'condition_note' => StringHelper::truncate($orderItemFromAmazon->getConditionNote(), 200, ''),
            'scheduled_delivery_start_date' => $orderItemFromAmazon->getScheduledDeliveryStartDate(),
            'scheduled_delivery_end_date' => $orderItemFromAmazon->getScheduledDeliveryEndDate(),
            'is_gift' => $orderItemFromAmazon->getIsGift(),
            'is_transparency' => $orderItemFromAmazon->getIsTransparency()
        ];
    }

    private function listToString(?array $list, string $separator = ','): string
    {
        if (is_null($list)) {
            return '';
        }
        return implode($separator, $list);
    }

    private function getPriceFromMoney(?Money $money = null, $default = 0)
    {
        return $money ? $money->getAmount() : $default;
    }
}
