<?php

namespace common\components\services\order\dataConverter;

use common\components\LogToConsoleTrait;

/**
 * Converts amazon order item from our postgres format to clickhouse format.
 * (amazon_order and amazon_order_in_progress_v1 tables).
 */
class PostgresAmazonOrderItemToClickhouseConverter
{
    use LogToConsoleTrait;

    /**
     * @param array $amazonOrderItem AmazonOrderItem as array
     * @param array $amazonOrder AmazonOrder as array
     * @param array $amazonMarketplace AmazonMarketplace as array
     * @return array
     */
    public function convert(
        array $amazonOrderItem,
        array $amazonOrder,
        array $amazonMarketplace
    ): array
    {
        // Only for these tables (amazon_order, amazon_order_in_progress_v1),
        // for backward compatibility with existing logic
        $moneyAccuracy = 100;

        return [
            'id' => $amazonOrderItem['id'],
            'order_id' => $amazonOrderItem['order_id'],
            'asin' => $amazonOrderItem['asin'],
            'sku' => $amazonOrderItem['sku'],
            'order_item_id' => $amazonOrderItem['order_item_id'],
            'title' => $amazonOrderItem['title'],
            'quantity' => (int)$amazonOrderItem['quantity'],
            'quantity_shipped' => (int)$amazonOrderItem['quantity_shipped'],
            'item_price' => (int)((float)$amazonOrderItem['item_price'] * $moneyAccuracy),
            'shipping_price' => (int)((float)$amazonOrderItem['shipping_price'] * $moneyAccuracy),
            'item_tax' => (int)((float)$amazonOrderItem['item_tax'] * $moneyAccuracy),
            'shipping_tax' => (int)((float)$amazonOrderItem['shipping_tax'] * $moneyAccuracy),
            'shipping_discount' => (int)((float)$amazonOrderItem['shipping_discount'] * $moneyAccuracy),
            'promotion_discount' => (int)((float)$amazonOrderItem['promotion_discount'] * $moneyAccuracy),
            'cod_fee' => (int)((float)$amazonOrderItem['cod_fee'] * $moneyAccuracy),
            'cod_fee_discount' => (int)((float)$amazonOrderItem['cod_fee_discount'] * $moneyAccuracy),
            'promotion_id' => $amazonOrderItem['promotion_id'],
            'condition_id' => $amazonOrderItem['condition_id'],
            'scheduled_delivery_start_date' => $amazonOrderItem['scheduled_delivery_start_date'],
            'scheduled_delivery_end_date' => $amazonOrderItem['scheduled_delivery_end_date'],
            'order_purchase_date' => $amazonOrderItem['order_purchase_date'],
            'order_marketplace_id' => $amazonOrderItem['order_marketplace_id'],
            'date' => $amazonOrderItem['date'],
            'profit' => (int)((float)$amazonOrderItem['profit'] * $moneyAccuracy),
            'order_period_id' => $amazonOrder['order_period_id'],
            'seller_id' => $amazonOrder['seller_id'],
            'seller_order_id' => $amazonOrder['seller_order_id'],
            'last_update_date' => $amazonOrder['last_update_date'],
            'order_status' => $amazonOrder['order_status'],
            'order_type' => $amazonOrder['order_type'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => $amazonOrder['updated_at'],
            'fulfillment_channel' => $amazonOrder['fulfillment_channel'],
            'is_business_order' => (int)$amazonOrder['is_business_order'],
            'currency_code' => $amazonMarketplace['currency_code']
        ];
    }
}
