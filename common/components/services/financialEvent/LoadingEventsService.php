<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\FinancialEventInitMessage;
use common\components\rabbitmq\message\FinancialEventRefreshMessage;
use common\models\customer\Product;
use common\models\finance\EventPeriod;
use common\models\Seller;

class LoadingEventsService
{
    const INIT_PROCESSING_LIMIT = 1;
    const REFRESH_PROCESSING_LIMIT = 1;

    private Seller $seller;
    private CustomerConfig $customerConfig;
    private CustomerComponent $customerComponent;

    private DbManager $dbManager;

    use LogToConsoleTrait;

    /**
     * LoadingEventsService constructor.
     * @param Seller $seller
     * @throws \yii\base\InvalidConfigException
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
        /** @var CustomerConfig $customerConfig */
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->dbManager = $sellerDbManager;
        $this->customerComponent = new CustomerComponent();
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadInit()
    {
        $this->info('getProcessingCount start');
        if ($this->getProcessingCount(EventPeriod::TYPE_INIT) >= self::INIT_PROCESSING_LIMIT) {
            return;
        }
        $this->info('getProcessingCount done');

        if ($this->dbManager->isDemo() && !$this->canStartLoadingDemoEvents()) {
            return;
        }

        $this->info('getFirstInitPeriod start');
        $eventPeriod = $this->getFirstInitPeriod();
        $this->info('getFirstInitPeriod done');
        if (is_null($eventPeriod)) {
            return;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen');
            return;
        }

        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        $this->info('saveOrThrowException start');
        $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
        $eventPeriod->saveOrThrowException();
        $this->info('saveOrThrowException done');

        $this->info('FinancialEventInitMessage start');
        $message = new FinancialEventInitMessage($this->seller->id, $this->seller->region, []);
        $this->info('FinancialEventInitMessage done');

        $this->info('setEventPeriodId start');
        $message->setEventPeriodId($eventPeriod->id);
        $this->info('setEventPeriodId done');

        $this->info('publish start');
        $message->publish();
        $this->info('publish done');
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadRefresh()
    {
        if ($this->getProcessingCount(EventPeriod::TYPE_REFRESH) >= self::REFRESH_PROCESSING_LIMIT) {
            return;
        }

        if ($this->dbManager->isDemo() && !$this->canStartLoadingDemoEvents()) {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf(
                    "product-cost/sync %d %d",
                    $this->dbManager->getCustomerId(),
                    $this->dbManager->getCustomerId() + 1,
                )
            );
        }

        $eventPeriod = $this->getFirstRefreshPeriod();

        if (is_null($eventPeriod)) {
            return;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen');
            return;
        }

        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
        $eventPeriod->saveOrThrowException();

        $message = new FinancialEventRefreshMessage($this->seller->id, $this->seller->region, []);
        $message->setEventPeriodId($eventPeriod->id);
        $message->publish();
    }

    /**
     * Ensure that demo data is ready, and we can start loading events (orders, transactions)
     *
     * @return bool
     */
    private function canStartLoadingDemoEvents(): bool
    {
        $this->info('Checking if we can start loading demo events');
        $activeSellerIds = Seller::find()
            ->select('id')
            ->where([
                'customer_id' => $this->dbManager->getCustomerId(),
                'is_analytic_active' => true
            ])
            ->column();

        $sellerIdsFromProducts = Product::find()
            ->select('seller_id')
            ->distinct()
            ->column();
        $sellerIdsWithoutProducts = array_diff($activeSellerIds, $sellerIdsFromProducts);
        $this->info([
            'sellerIdsWithoutProducts' => $sellerIdsWithoutProducts,
        ]);

        if (count($sellerIdsWithoutProducts) > 0) {
            $this->info('We can not start loading demo events - products are not created yet');

            return false;
        }

        $this->info('We can start loading demo events');

        return true;
    }

    /**
     * @return EventPeriod|null
     */
    private function getFirstInitPeriod(): ?EventPeriod
    {
        $query = EventPeriod::find()
            ->where([
                'loading_status' => [EventPeriod::LOADING_STATUS_NEW],
                'type' => EventPeriod::TYPE_INIT
            ]);

        // We don't need to load all init periods on dev servers,
        // this can have an impact on "quota limitation" errors from amazon on production
        if (\Yii::$app->dbManager->shouldApplyDevRestrictions()) {
            $query->andWhere(['>', 'start_date', (new \DateTime())->modify('-6 month')->format('Y-m-1 00:00:00')]);
        }

        $query->orderBy('finish_date DESC');

        return $query->one();
    }

    /**
     * @return EventPeriod|null
     */
    private function getFirstRefreshPeriod(): ?EventPeriod
    {
        return EventPeriod::find()->where(['loading_status' => [EventPeriod::LOADING_STATUS_NEW], 'type' => EventPeriod::TYPE_REFRESH])->orderBy('finish_date DESC')->one();
    }

    private function getProcessingCount(string $type): int
    {
        return (int)EventPeriod::find()->where(['loading_status' => [
            EventPeriod::LOADING_STATUS_PROCESSING,
            EventPeriod::LOADING_STATUS_QUEUED,
        ], 'type' => $type])->count();
    }
}
