<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\message\FinancialEventInitMessage;
use common\components\rabbitmq\message\FinancialEventRefreshMessage;
use common\models\finance\EventPeriod;
use common\models\Seller;

class LoadingEventsService
{
    const INIT_PROCESSING_LIMIT = 1;
    const REFRESH_PROCESSING_LIMIT = 1;

    private Seller $seller;
    private CustomerConfig $customerConfig;
    private CustomerComponent $customerComponent;

    use LogToConsoleTrait;

    /**
     * LoadingEventsService constructor.
     * @param Seller $seller
     * @throws \yii\base\InvalidConfigException
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
        /** @var CustomerConfig $customerConfig */
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->customerComponent = new CustomerComponent();
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadInit()
    {
        $this->info('getProcessingCount start');
        if ($this->getProcessingCount(EventPeriod::TYPE_INIT) >= self::INIT_PROCESSING_LIMIT) {
            return;
        }
        $this->info('getProcessingCount done');

        $this->info('getFirstInitPeriod start');
        $eventPeriod = $this->getFirstInitPeriod();
        $this->info('getFirstInitPeriod done');
        if (is_null($eventPeriod)) {
            return;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen');
            return;
        }

        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        $this->info('saveOrThrowException start');
        $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
        $eventPeriod->saveOrThrowException();
        $this->info('saveOrThrowException done');

        $this->info('FinancialEventInitMessage start');
        $message = new FinancialEventInitMessage($this->seller->id, $this->seller->region, []);
        $this->info('FinancialEventInitMessage done');

        $this->info('setEventPeriodId start');
        $message->setEventPeriodId($eventPeriod->id);
        $this->info('setEventPeriodId done');

        $this->info('publish start');
        $message->publish();
        $this->info('publish done');
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    public function loadRefresh()
    {
        if ($this->getProcessingCount(EventPeriod::TYPE_REFRESH) >= self::REFRESH_PROCESSING_LIMIT) {
            return;
        }

        $eventPeriod = $this->getFirstRefreshPeriod();

        if (is_null($eventPeriod)) {
            return;
        }

        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen');
            return;
        }

        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        $eventPeriod->loading_status = EventPeriod::LOADING_STATUS_QUEUED;
        $eventPeriod->saveOrThrowException();

        $message = new FinancialEventRefreshMessage($this->seller->id, $this->seller->region, []);
        $message->setEventPeriodId($eventPeriod->id);
        $message->publish();
    }

    /**
     * @return EventPeriod|null
     */
    private function getFirstInitPeriod(): ?EventPeriod
    {
        $query = EventPeriod::find()
            ->where([
                'loading_status' => [EventPeriod::LOADING_STATUS_NEW],
                'type' => EventPeriod::TYPE_INIT
            ]);

        // We don't need to load all init periods on dev servers,
        // this can have an impact on "quota limitation" errors from amazon on production
        if (YII_ENV !== 'prod') {
            $query->andWhere(['>', 'start_date', (new \DateTime())->modify('-3 month')->format('Y-m-1 00:00:00')]);
        }

        $query->orderBy('finish_date DESC');

        return $query->one();
    }

    /**
     * @return EventPeriod|null
     */
    private function getFirstRefreshPeriod(): ?EventPeriod
    {
        return EventPeriod::find()->where(['loading_status' => [EventPeriod::LOADING_STATUS_NEW], 'type' => EventPeriod::TYPE_REFRESH])->orderBy('finish_date DESC')->one();
    }

    private function getProcessingCount(string $type): int
    {
        return (int)EventPeriod::find()->where(['loading_status' => [
            EventPeriod::LOADING_STATUS_PROCESSING,
            EventPeriod::LOADING_STATUS_QUEUED,
        ], 'type' => $type])->count();
    }
}
