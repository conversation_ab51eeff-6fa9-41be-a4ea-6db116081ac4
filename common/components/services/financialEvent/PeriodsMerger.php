<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\finance\EventPeriod;
use common\models\Seller;

class PeriodsMerger
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;

    protected const MAX_MERGES_PER_ITERATION = 500;
    protected const MAX_MERGED_PERIOD_SIZE_SECONDS = 60 * 60 * 24;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public static function isInProgress(string $sellerId): bool
    {
        return \Yii::$app->cache->get("periods_merge_in_progress_{$sellerId}");
    }

    public function merge(int $customerId)
    {
        $this->dbManager->setCustomerId($customerId);

        $sellers = Seller::find()
            ->where(['customer_id' => $this->dbManager->getCustomerId()])
            ->all(\Yii::$app->db);

        foreach ($sellers as $seller) {
            $cacheKey = "periods_merge_in_progress_{$seller->id}";
            \Yii::$app->cache->set($cacheKey, true, 60 * 60);
            try {
                $this->info("Processing seller {$seller->id} | started");
                $this->dbManager->setSellerId($seller->id);

                $countsBeforeMerge = $this->getCountsByTable();

                $this->info([
                    'countsBeforeMerge' => $countsBeforeMerge,
                ]);

                foreach ($this->getPeriodsToProcessQuery()->batch(10000) as $eventPeriods) {
                    $batchesToBeMerged = $this->generateBatchesToBeMerged($eventPeriods);
                    $this->mergeBatches($batchesToBeMerged);

                    $countsAfterMerge = $this->getCountsByTable();

                    $this->info([
                        'countsBeforeMerge' => $countsBeforeMerge,
                        'countsAfterMerge' => $countsAfterMerge
                    ]);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
            \Yii::$app->cache->delete($cacheKey);
        }
    }

    protected function getCountsByTable(): array
    {
        $eventTables = EventPeriod::getEventTables();
        $counts = [];

        foreach ($eventTables as $eventTable) {
            $counts[$eventTable] = EventPeriod::getDb()
                ->createCommand("SELECT count(*) FROM $eventTable")
                ->queryScalar();
        }

        return $counts;
    }

    protected function getPeriodsToProcessQuery(): \yii\db\ActiveQuery
    {
        return EventPeriod::find()
            ->andWhere([
                'AND',
                ['<', 'start_date', (new \DateTime())->modify('-5 days')->format('Y-m-d H:i:s')],
                ['not in', 'loading_status', [
                    EventPeriod::LOADING_STATUS_QUEUED,
                    EventPeriod::LOADING_STATUS_PROCESSING,
                ]],
            ])
            ->asArray()
            ->orderBy('start_date DESC');
    }

    protected function generateBatchesToBeMerged(array $eventPeriods): array
    {
        $batchesToBeMerged = [];
        $prevPeriod = null;
        $currentBatch = null;
        $currentBatchKey = null;

        $this->info("Generating batches to be merged | " . $this->dbManager->getCustomerId() . ' | ' . $this->dbManager->getSellerId());
        foreach ($eventPeriods as $eventPeriod) {
            $periodKey = implode('_', [
                $eventPeriod['loading_status'],
                $eventPeriod['clickhouse_status'],
            ]);
            $currentBatchKey = $currentBatchKey ?? $periodKey;
            $prevPeriod = $prevPeriod ?? $eventPeriod;

            $isTimeGap = strtotime($eventPeriod['start_date']) - strtotime($prevPeriod['finish_date']) > 10;
            $isBatchReady = !empty($currentBatch)
                && strtotime($currentBatch['finish_date']) - strtotime($eventPeriod['start_date']) >= self::MAX_MERGED_PERIOD_SIZE_SECONDS;
            $isBatchTypeChanged = $periodKey !== $currentBatchKey;
            $isFirstIteration = empty($currentBatch);

            if ($isTimeGap || $isBatchReady || $isBatchTypeChanged || $isFirstIteration) {
                $shouldBeMerged = !$isFirstIteration
                    && count($currentBatch['periods']) > 1
                    && !in_array($currentBatch['clickhouse_status'], [
                        EventPeriod::CLICKHOUSE_STATUS_QUEUED,
                        EventPeriod::CLICKHOUSE_STATUS_PROCESSING,
                    ]);
                if ($shouldBeMerged) {
                    $batchesToBeMerged[] = $currentBatch;
                }

                $currentBatch = [
                    'finish_date' => $eventPeriod['finish_date'],
                    'loading_status' => $eventPeriod['loading_status'],
                    'clickhouse_status' => $eventPeriod['clickhouse_status'],
                    'has_transactions' => false,
                    'periods' => []
                ];
            }

            if ($eventPeriod['has_transactions']) {
                $currentBatch['has_transactions'] = true;
            }
            $currentBatch['periods'][] = $eventPeriod;
            $currentBatch['period_ids'][] = $eventPeriod['id'];
            $currentBatch['start_date'] = $eventPeriod['start_date'];
            $currentBatch['length_hours'] = (strtotime($currentBatch['finish_date']) - strtotime($currentBatch['start_date'])) / 60 / 60;
            $prevPeriod = $eventPeriod;
            $currentBatchKey = $periodKey;
        }

        $this->info("Generated " . count($batchesToBeMerged) . " parts to be merged");

        return $batchesToBeMerged;
    }

    protected function mergeBatches(array $batchesToBeMerged)
    {
        if (count($batchesToBeMerged) === 0) {
            return;
        }

        $this->info("Merging batches | " . $this->dbManager->getCustomerId() . ' | ' . $this->dbManager->getSellerId());
        $eventTables = EventPeriod::getEventTables();

        foreach ($batchesToBeMerged as $k => $batchToBeMerged) {
            if ($k >= self::MAX_MERGES_PER_ITERATION) {
                $this->info("Max periods to merge per iteration reached, finish for this seller");
                break;
            }

            $transaction = EventPeriod::getDb()->beginTransaction();
            try {
                $rootId = array_shift($batchToBeMerged['period_ids']);
                $countMerged = count($batchToBeMerged['period_ids']);

                foreach ($eventTables as $eventTable) {
                    EventPeriod::getDb()->createCommand("
                        UPDATE $eventTable
                        SET event_period_id = $rootId
                        WHERE event_period_id IN (" . implode(',', $batchToBeMerged['period_ids']) . ")"
                    )->execute();
                }

                $clickhouseStatuses = EventPeriod::find()
                    ->select('clickhouse_status')
                    ->distinct()
                    ->where(['id' => $batchToBeMerged['period_ids']])
                    ->column();

                if (count($clickhouseStatuses) > 1
                    || $clickhouseStatuses[0] !== $batchToBeMerged['clickhouse_status']
                ) {
                    $this->info('Periods status has been changed, skip part');
                    continue;
                }

                EventPeriod::updateAll([
                    'start_date' => $batchToBeMerged['start_date'],
                    'has_transactions' => $batchToBeMerged['has_transactions'],
                    'type' => EventPeriod::TYPE_INIT,
                    'updated_at' => date('Y-m-d H:i:s'),
                ], ['id' => $rootId]);

                $this->info('Deleting merged periods');
                EventPeriod::deleteAll(['id' => $batchToBeMerged['period_ids']]);

                $transaction->commit();
                $this->info([
                    'customer_id' => $this->dbManager->getCustomerId(),
                    'seller_id' => $this->dbManager->getSellerId(),
                    'root_id' => $rootId,
                    'count_merged' => $countMerged,
                    'start_date' => $batchToBeMerged['start_date'],
                    'finish_date' => $batchToBeMerged['finish_date'],
                ]);
            } catch (\Throwable $e) {
                $this->error($e);
                $transaction->rollBack();
            }
        }
        $this->info("Merging finished");
    }
}
