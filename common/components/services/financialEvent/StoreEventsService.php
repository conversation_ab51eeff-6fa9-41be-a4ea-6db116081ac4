<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\prometheus\Prometheus;
use common\models\finance\base\AbstractFinanceRecord;
use common\models\finance\EventPeriod;
use common\models\Seller;
use SellingPartnerApi\Model\FinancesV0\FinancialEvents;
use yii\db\JsonExpression;

class StoreEventsService
{
    use LogToConsoleTrait;

    protected EventPeriod $eventPeriod;
    protected Seller $seller;
    private DbManager $sellerDbManager;
    private Prometheus $prometheus;

    /**
     * StoreEventsService constructor.
     * @param EventPeriod $eventPeriod
     * @param string $sellerId
     * @throws \yii\base\InvalidConfigException
     */
    public function __construct(EventPeriod $eventPeriod, string $sellerId)
    {
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($sellerId);
        $this->prometheus = \Yii::$app->prometheus;
        $this->sellerDbManager = $sellerDbManager;
        $this->eventPeriod = $eventPeriod;
        $this->seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
    }

    /**
     * @param FinancialEvents $financialEvents
     * @throws \Exception
     */
    public function saveData(FinancialEvents $financialEvents)
    {
        try {
            $timeStart = microtime(true);
            $jsonData = $financialEvents->__toString();
            $data = json_decode($jsonData, true);
            $countItems = 0;

            foreach ($data as $listName => $items) {

                $modelName = "common\models\\finance\\" . str_replace('EventList', '', $listName);
                $countItems += count($items);

                foreach ($items as $item) {
                    /** @var AbstractFinanceRecord $model */
                    $model = new $modelName;
                    foreach($item as $key => $value) {
                        if ($model->hasAttribute($key)) {
                            if ($model->getTableSchema()->columns[$key]->dbType === 'timestamp') {
                                $value = empty($value) ? null : date('Y-m-d H:i:s', strtotime($value));
                            }
                            $preparedValue = is_array($value) ? (new JsonExpression($value)) : $value;
                            $model->{$key} = empty($preparedValue) ? null : $preparedValue;
                        }
                    }
                    $model->event_period_id = $this->eventPeriod->id;
                    $model->saveOrThrowException();
                }
            }
            $this->eventPeriod->updated_at = date('Y-m-d H:i:s');
            $this->eventPeriod->update('false', ['updated_at']);

            $timeElapsed = microtime(true) - $timeStart;
            $this->prometheus->performancePerSecond(
                'saving_financial_events_to_db',
                $countItems / $timeElapsed
            );
            $this->prometheus->customCounter(
                'financial_events_saved_to_db',
                $countItems
            );
        } catch (\Throwable $e) {
            $this->error($e);
            $this->eventPeriod->loading_status = EventPeriod::LOADING_STATUS_TERMINATED;
            $this->eventPeriod->saveOrThrowException();
        }
    }

    public function rollbackCurrentVersionToInitialState(): void
    {
        $eventTables = EventPeriod::getEventTables();

        $db = $this->sellerDbManager->getFinanceDb();

        foreach ($eventTables as $tableName) {
            $db
                ->createCommand()
                ->delete($tableName, [
                    'event_period_id' => $this->eventPeriod->id
                ])
                ->execute();
        }
    }

    /**
     * @throws \Exception
     */
    public function markAsFinished()
    {
        if ($this->eventPeriod->loading_status == EventPeriod::LOADING_STATUS_PROCESSING) {
            $this->eventPeriod->loading_status = EventPeriod::LOADING_STATUS_FINISHED;
            $this->eventPeriod->saveOrThrowException();
        }
    }

    /**
     * @throws \Exception
     */
    public function markAsTerminated(string $reason = null)
    {
        $this->info('TERMINATED with reason: ' . $reason);
        $this->eventPeriod->loading_status = EventPeriod::LOADING_STATUS_TERMINATED;
        $this->eventPeriod->saveOrThrowException();
        $this->rollbackCurrentVersionToInitialState();
    }
}
