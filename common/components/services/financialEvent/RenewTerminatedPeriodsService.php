<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\finance\EventPeriod;
use common\models\Seller;

class RenewTerminatedPeriodsService
{
    use LogToConsoleTrait;

    /** @var Seller */
    private Seller $seller;
    /**
     * @param Seller $seller
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $sellerDbManager->setSellerId($seller->id);
    }

    public function renew()
    {
        $periods = EventPeriod::find()->where([
                'or',
                ['=', 'loading_status', EventPeriod::LOADING_STATUS_TERMINATED],
                [
                    'and',
                    ['in', 'loading_status', [
                        EventPeriod::LOADING_STATUS_PROCESSING,
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ],
                [
                    'and',
                    ['in', 'loading_status', [
                        EventPeriod::LOADING_STATUS_QUEUED
                    ]],
                    ['<', 'updated_at', (new \DateTime())->modify('-20 minutes')->format('Y-m-d H:i:s')]
                ]
            ])
            ->all();

        if (count($periods) > 0) {
            $this->info(sprintf("Found %d periods for renew (seller %s)", count($periods), $this->seller->id));
        }

        /** @var EventPeriod $period */
        foreach ($periods as $period) {
            $storeService = new StoreEventsService($period, $this->seller->id);
            $storeService->rollbackCurrentVersionToInitialState();

            $transaction = EventPeriod::getDb()->beginTransaction();
            try {
                $newEventPeriod = new EventPeriod();
                $newEventPeriod->start_date = $period->start_date;
                $newEventPeriod->finish_date = $period->finish_date;
                $newEventPeriod->type = $period->type;
                $newEventPeriod->loading_status = EventPeriod::LOADING_STATUS_NEW;
                $period->delete();
                $newEventPeriod->save(false);

                $transaction->commit();
            } catch (\Throwable $e) {
                $transaction->rollBack();
                throw $e;
            }

        }
    }

    /**
     * <AUTHOR>
     */
    public function renewClickHouseStatus()
    {
        $periods = EventPeriod::find()->where(
            [
                'or',
                [
                    'and',
                    ['in', 'clickhouse_status', [
                        EventPeriod::CLICKHOUSE_STATUS_ERROR,
                        EventPeriod::CLICKHOUSE_STATUS_PROCESSING,
                        EventPeriod::CLICKHOUSE_STATUS_WAITING,
                    ]],
                    ['<', 'clickhouse_queued_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ],
                [
                    'and',
                    ['in', 'clickhouse_status', [
                        EventPeriod::CLICKHOUSE_STATUS_QUEUED,
                    ]],
                    ['<', 'clickhouse_queued_at', (new \DateTime())->modify('-3 hours')->format('Y-m-d H:i:s')]
                ]
            ]
        )->all();

        if (count($periods) > 0) {
            $this->info(sprintf("Found %d periods for renew clickhouse status (seller %s)", count($periods), $this->seller->id));
        }

        /** @var EventPeriod $period */
        foreach ($periods as $period) {
            $period->clickhouse_status = EventPeriod::CLICKHOUSE_STATUS_NEW;
            $period->save(false);
        }
    }
}
