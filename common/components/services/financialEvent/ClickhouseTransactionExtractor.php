<?php

namespace common\components\services\financialEvent;

use common\components\COGSync\COGChangesManager;
use common\components\COGSync\COGTransactionsGenerator;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\errors\OrderNotFoundException;
use common\components\services\order\LoadAmazonOrderManager;
use common\components\services\order\UnknownOrderService;
use common\models\AmazonMarketplace;
use common\models\Customer;
use common\models\customer\ProductCostCategory;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\finance\clickhouse\Transaction;
use common\models\finance\EventPeriod;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\order\UnknownAmazonOrder;
use common\models\SalesCategory;
use common\models\Seller;
use Exception;
use yii\caching\CacheInterface;

/**
 * Responsible for processing of amazon financial events and extracting transactions from them.
 *
 * @package common\components\services\financialEvent
 */
class ClickhouseTransactionExtractor
{
    use LogToConsoleTrait;

    /**
     * Fact - important information about transaction.
     */
    public const FACT_POSTED_DATE = 'PostedDate';
    public const FACT_EVENT_PERIOD_ID = 'event_period_id';
    public const FACT_SELLER_SKU = 'SellerSKU';
    public const FACT_SELLER_ORDER_ID = 'SellerOrderId';
    public const FACT_MARKETPLACE_NAME = 'MarketplaceName';
    public const FACT_AMAZON_ORDER_ID = 'AmazonOrderId';
    public const FACT_CATEGORY_PATH = 'CategoryPath';
    public const FACT_QUANTITY = 'Quantity';
    public const FACT_SELLER_ID = 'SellerId';
    public const FACT_MARKETPLACE_ID = 'MarketplaceId';
    public const FACT_ASIN = 'ASIN';
    public const FACT_TITLE = 'Title';
    public const FACT_PRODUCT_ID = 'ProductId';
    public const FACT_TAG_ID = 'TagId';
    public const FACT_ORDER_ITEM_ID = 'OrderItemId';

    public const FACT_OFFER_TYPE = 'OfferType';
    public const FACT_PRODUCT_EAN = 'ProductEAN';
    public const FACT_PRODUCT_ISBN = 'ProductISBN';
    public const FACT_PRODUCT_UPC = 'ProductUPC';
    public const FACT_PRODUCT_PARENT_ASIN = 'ProductParentASIN';
    public const FACT_PRODUCT_BRAND = 'ProductBrand';
    public const FACT_PRODUCT_MODEL = 'ProductModel';
    public const FACT_PRODUCT_TYPE = 'ProductType';
    public const FACT_PRODUCT_MANUFACTURER = 'ProductManufacturer';
    public const FACT_PRODUCT_IS_ADULT_ONLY = 'ProductIsAdultOnly';
    public const FACT_PRODUCT_MAIN_IMAGE = 'ProductMainImage';

    private CurrencyRateManager $currencyRateManager;
    private CacheInterface $cache;
    private COGChangesManager $COGChangesManager;
    private CustomerComponent $customerComponent;
    private CustomerConfig $customerConfig;
    private DbManager $dbManager;
    private COGTransactionsGenerator $COGTransactionsGenerator;

    private array $orderIdsForLoading = [];
    private array $orderIdsForUpdateHasTransactions = [];
    public array $b2bOrderIds = [];
    public array $fbaOrderIds = [];
    public array $manualShippingCostOrderIds = [];

    public function __construct()
    {
        $this->currencyRateManager = new CurrencyRateManager();
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->dbManager = \Yii::$app->dbManager;
        $this->cache = \Yii::$app->cache;
        $this->COGChangesManager = \Yii::$container->get('COGChangesManager');
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->COGTransactionsGenerator = \Yii::$container->get('COGTransactionsGenerator');
    }

    /**
     * Contains fields mapping from amazon API to normalized format.
     * Amazon API often provides different field names for the same property.
     */
    public const AMAZON_FACTS_NORMALIZATION_MAPPING = [
        'event_period_id' => self::FACT_EVENT_PERIOD_ID,
        'postedDate' => self::FACT_POSTED_DATE,
        'PostedDate' => self::FACT_POSTED_DATE,
        'TransactionCreationDate' => self::FACT_POSTED_DATE,
        'TransactionPostedDate' => self::FACT_POSTED_DATE,

        'SKU' => self::FACT_SELLER_SKU,
        'SellerSKU' => self::FACT_SELLER_SKU,
        'SellerId' => self::FACT_SELLER_ID,

        'SellerOrderId' => self::FACT_SELLER_ORDER_ID,
        'AmazonOrderId' => self::FACT_AMAZON_ORDER_ID,

        'Quantity' => self::FACT_QUANTITY,
        'quantity' => self::FACT_QUANTITY,
        'QuantityShipped' => self::FACT_QUANTITY,

        'ASIN' => self::FACT_ASIN,
        'MarketplaceName' => self::FACT_MARKETPLACE_NAME,

        'OrderItemId' => self::FACT_ORDER_ITEM_ID,

        self::FACT_TITLE => self::FACT_TITLE,
        self::FACT_PRODUCT_ID => self::FACT_PRODUCT_ID,
        self::FACT_SELLER_ID => self::FACT_SELLER_ID,
        self::FACT_OFFER_TYPE => self::FACT_OFFER_TYPE,
        self::FACT_PRODUCT_EAN => self::FACT_PRODUCT_EAN,
        self::FACT_PRODUCT_ISBN => self::FACT_PRODUCT_ISBN,
        self::FACT_PRODUCT_UPC => self::FACT_PRODUCT_UPC,
        self::FACT_PRODUCT_PARENT_ASIN => self::FACT_PRODUCT_PARENT_ASIN,
        self::FACT_PRODUCT_BRAND => self::FACT_PRODUCT_BRAND,
        self::FACT_PRODUCT_MODEL => self::FACT_PRODUCT_MODEL,
        self::FACT_PRODUCT_TYPE => self::FACT_PRODUCT_TYPE,
        self::FACT_PRODUCT_MANUFACTURER => self::FACT_PRODUCT_MANUFACTURER,
        self::FACT_PRODUCT_IS_ADULT_ONLY => self::FACT_PRODUCT_IS_ADULT_ONLY,
        self::FACT_PRODUCT_MAIN_IMAGE => self::FACT_PRODUCT_MAIN_IMAGE,
        self::FACT_TAG_ID => self::FACT_TAG_ID
    ];

    private const TYPE_PROPERTIES = [
        'AdjustmentType',
        'TransactionType',
        'transactionType',
        'ChargeType',
        'DirectPaymentType',
        'FeeType',
        'FeeReason',
        'PromotionType',
        'RentalEventType',
        'ProviderTransactionType',
        'TaxCollectionModel',
        'FundsTransfersType',
        'PaymentDisbursementType',
        'Status',
        'SourceBusinessEventType'
    ];

    private const CATEGORY_PATH_DELIMITER = '.';

    /**
     * Collected clickhouse transactions.
     *
     * @var Transaction[]
     */
    private $collectedTransactions = [];

    /**
     * Goes recursively through financial events received from amazon.
     * Creates and collects clickhouse transactions.
     *
     * @param $financialEvents
     * @param string $parentCategoryPath
     * @param array $facts
     */
    public function process(
        $financialEvents,
        $parentCategoryPath = '',
        $facts = []
    ): void
    {
        foreach ($financialEvents as $key => $financeItem) {
            if (!is_array($financeItem)) {
                continue;
            }

            $categoryPath = $this->getCategoryPath($key, $financeItem, $parentCategoryPath);
            $extendedFacts = array_merge($facts, $this->getFacts($financeItem));
            $this->process($financeItem, $categoryPath, $extendedFacts);

            if ($this->isCurrencyItem($financeItem)) {
                $extendedFacts[self::FACT_CATEGORY_PATH] = $categoryPath;
                $transaction = $this->convertCurrencyItemToTransaction($financeItem, $extendedFacts);
                $this->collectedTransactions[] = $transaction;
            }
        }
    }

    /**
     * Returns collected transactions.
     *
     * @return array
     */
    public function getAndFlushTransactions(Seller $seller = null, bool $isEstimatedTransactions = false)
    {
        if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
            return $this->getAndFlushTransactionsV2($seller, $isEstimatedTransactions);
        }

        $transactions = array_values($this->collectedTransactions);
        $transactions = $this->fillWithCOGTransactions($transactions);
        $this->collectedTransactions = [];
        $this->b2bOrderIds = [];
        $this->fbaOrderIds = [];

        return $transactions;
    }

    /**
     * This transaction necessary for applying COG
     *
     * @param Transaction[] $transactions
     * @return Transaction[]
     */
    public function createEmptyOrganicRefundTransactionsIfNeeded(array $transactions): array
    {
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $refundOrderPriceCategoryIds = FinanceEventCategory::getRefundOrderPriceIds();

        $itemsWithOrganicRefundTransaction = [];
        $itemsWithRefunds = [];

        foreach ($transactions as $transaction) {
            $key = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);

            if ($transaction->CategoryId == $organicRefundCategoryId) {
                $itemsWithOrganicRefundTransaction[$key] = $transaction;
            }

            if (in_array($transaction->CategoryId, $refundOrderPriceCategoryIds)) {
                $itemsWithRefunds[$key] = $transaction;
            }
        }

        $itemsWithoutOrganicRefundTransaction = array_diff_key($itemsWithRefunds, $itemsWithOrganicRefundTransaction);

        /** @var Transaction $transactionToCopy */
        foreach ($itemsWithoutOrganicRefundTransaction as $transactionToCopy) {
            $postedDateOffsetS = random_int(1, 60);
            $organicRefundTransaction = clone $transactionToCopy;
            $organicRefundTransaction->PostedDate = date(
                'Y-m-d H:i:s',
                strtotime($organicRefundTransaction->PostedDate) - $postedDateOffsetS
            );
            $organicRefundTransaction->AmountEUR = 0;
            $organicRefundTransaction->Amount = 0;
            $organicRefundTransaction->CategoryId = $organicRefundCategoryId;
            $organicRefundTransaction->Quantity = 0;
            $organicRefundTransaction->MergeCounter = 0;

            $transactions[] = $organicRefundTransaction;
        }

        return $transactions;
    }

    /**
     * @throws OrderNotFoundException
     * @throws Exception
     */
    public function getAndFlushTransactionsV2(Seller $seller, bool $isEstimatedTransactions = false): array
    {
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $transactions = array_values($this->collectedTransactions);
        $this->populateDateBulk($transactions);
        $transactions = $this->createManualOrderItemCostsTransaction($transactions);
        $transactions = $this->createEmptyOrganicRefundTransactionsIfNeeded($transactions);
        $transactions = $this->fillWithCOGTransactions($transactions);
        $this->applyMoneyAccuracy($transactions);

        // TODO: we realized that  shipping cost for FBM products should bot be returned back because customer already payed for it.
        // TODO: will be removed after we double check it on prod after deploy.
//        $refundTransactions = [];
//        $shippingCOGCategories = ProductCostCategory::getAllBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
//        $shippingCategoryIds = [];
//        foreach ($shippingCOGCategories as $shippingCOGCategory) {
//            $shippingCategoryIds[] = $shippingCOGCategory['id'];
//        }
//
//        foreach ($transactions as $k => $transaction) {
//            if ($transaction->CategoryId === $organicRefundCategoryId) {
//                if (empty($transaction->COGCategoryId)) {
//                    $refundTransactions[] = $transaction;
//                }
//
//                // Shipping costs for refunds will be applied in separate process
//                if (in_array($transaction->COGCategoryId, $shippingCategoryIds)) {
//                    unset($transactions[$k]);
//                }
//            }
//        }
//        $this->delayRefundShippingCostApplying($refundTransactions);

        $hasOrderIdsForLoading = false;
        if (!$isEstimatedTransactions && !empty($this->orderIdsForLoading)) {
            $hasOrderIdsForLoading = true;
            $this->info(sprintf("Sending %s orders for loading", count($this->orderIdsForLoading)));
            $loadAmazonOrderManager =  new LoadAmazonOrderManager();
            foreach ($this->orderIdsForLoading as $amazonOrderId) {
                $loadAmazonOrderManager->addOrderIdToLoadingList($seller, $amazonOrderId);
            }
            $this->info("Orders sent for loading");
        }

        if (!$isEstimatedTransactions && !empty($this->orderIdsForUpdateHasTransactions)) {
            $this->info(sprintf(
                "Updating orders has_transactions for %s orders",
                count($this->orderIdsForUpdateHasTransactions)
            ));
            AmazonOrder::updateAll([
                'has_transactions' => true
            ], [
                'id' => $this->orderIdsForUpdateHasTransactions
            ]);
            $this->info("Orders has_transactions updated");
        }

        $this->collectedTransactions = [];
        $this->b2bOrderIds = [];
        $this->fbaOrderIds = [];
        $this->orderIdsForLoading = [];
        $this->orderIdsForUpdateHasTransactions = [];

        if (!$isEstimatedTransactions && $hasOrderIdsForLoading) {
            throw new OrderNotFoundException("Some orders were not found and will be loaded later");
        }

        return $transactions;
    }

    /**
     * @throws Exception
     */
    private function createManualOrderItemCostsTransaction(array $transactions): array
    {
        $this->info('Create transaction with manual shipping cost started');

        if (empty($this->manualShippingCostOrderIds)) {
            return $transactions;
        }

        $orderIdsToFind = array_unique($this->manualShippingCostOrderIds);

        $amazonOrderItems = AmazonOrderItem::find()
            ->select('manual_shipping_cost_currency, manual_shipping_cost, sku, order_id')
            ->where(['order_id' => $orderIdsToFind])
            ->asArray()
            ->all();

        $indexedAmazonOrderItems = [];
        foreach ($amazonOrderItems as $item) {
            $key = $item['sku'] . $item['order_id'];
            $indexedAmazonOrderItems[$key] = $item;
        }

        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $shippingCostCategoryId = FinanceEventCategory::getShippingChargeId();

        /**
         *@var Transaction $transaction
         */
        $newTransactions = [];
        foreach ($transactions as $transaction) {
            $amazonOrderItem = $indexedAmazonOrderItems[$transaction->SellerSKU . $transaction->AmazonOrderId] ?? null;
            if (
                in_array($transaction->CategoryId, [$organicSalesCategoryId])
                && !empty($amazonOrderItem)
                && $amazonOrderItem['manual_shipping_cost'] !== null
                && $amazonOrderItem['manual_shipping_cost_currency'] !== null
            ) {
                $amount = $amazonOrderItem['manual_shipping_cost'];
                $amountCurrency = $amazonOrderItem['manual_shipping_cost_currency'];
                $postedDateOffsetS = random_int(1, 60);
                $shippingTransaction = clone $transaction;
                $shippingTransaction->PostedDate = date(
                    'Y-m-d H:i:s',
                    strtotime($shippingTransaction->PostedDate) - $postedDateOffsetS
                );
                $amountEur = $this->currencyRateManager->toBaseCurrency(
                    $amount,
                    $amountCurrency,
                    new \DateTime($transaction->PostedDate)
                );

                $shippingTransaction->AmountEUR = $amountEur;
                $shippingTransaction->Amount = (float)$amount;
                $shippingTransaction->CategoryId = $shippingCostCategoryId;
                $shippingTransaction->Amount *= -1;
                $shippingTransaction->AmountEUR *= -1;
                $shippingTransaction->MergeCounter = 1;

                $newTransactions[] = $shippingTransaction;
            }
        }
        return array_merge($transactions, $newTransactions);
    }

    /**
     * @param Transaction[] $refundTransactions
     * @return void
     */
    protected function delayRefundShippingCostApplying(array $refundTransactions): void
    {
        $this->info('Delaying refund transactions COG applying started');

        try {
            $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
            $refundsGrouped = [];

            foreach ($refundTransactions as $refundTransaction) {
                if (empty($refundTransaction->MarketplaceId)
                    || empty($refundTransaction->SellerId)
                    || empty($refundTransaction->SellerSKU)
                    || empty($refundTransaction->AmazonOrderId)
                ) {
                    continue;
                }

                // Partial refund without principal transaction no need to delay (nothing to apply except VAT)
                if ((int)$refundTransaction->Quantity === 0) {
                    continue;
                }

                $key = implode('_', [
                    $refundTransaction->MarketplaceId,
                    $refundTransaction->SellerId,
                    $refundTransaction->AmazonOrderId,
                    $refundTransaction->SellerSKU,
                    $refundTransaction->Currency
                ]);

                if (empty($refundsGrouped[$key])) {
                    $refundsGrouped[$key] = [
                        'posted_date' => $refundTransaction->PostedDate,
                        'marketplace_id' => $refundTransaction->MarketplaceId,
                        'seller_id' => $refundTransaction->SellerId,
                        'seller_sku' => $refundTransaction->SellerSKU,
                        'asin' => $refundTransaction->ASIN,
                        'category_id' => $organicRefundCategoryId,
                        'amount' => 0,
                        'currency' => $refundTransaction->Currency,
                        'quantity' => 0,
                        'amazon_order_id' => $refundTransaction->AmazonOrderId,
                        'seller_order_id' => $refundTransaction->SellerOrderId,
                        'event_period_id' => $refundTransaction->EventPeriodId,
                    ];
                }

                $refundsGrouped[$key]['amount'] += $refundTransaction->Amount;

                if ($refundTransaction->CategoryId === $organicRefundCategoryId) {
                    $refundsGrouped[$key]['quantity'] += $refundTransaction->Quantity;
                }
            }

            if (count($refundsGrouped) > 0) {
                $this->dbManager
                    ->getCustomerDb()
                    ->createCommand()
                    ->batchInsert(
                        RefundTransactionWithoutProductCost::tableName(),
                        array_keys(array_values($refundsGrouped)[0]),
                        $refundsGrouped
                    )
                    ->execute();
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->info('Delaying refund transactions COG applying finished');
    }

    /**
     * @param Transaction[] $transactions
     * @return void
     */
    protected function applyMoneyAccuracy(array $transactions)
    {
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();
        $this->info("Applying money accuracy $moneyAccuracy started");

        foreach ($transactions as $transaction) {
            $transaction->Amount = (int)round($transaction->Amount * $moneyAccuracy);
            $transaction->AmountEUR = (int)round($transaction->AmountEUR * $moneyAccuracy);
        }
        $this->info('Applying money accuracy finished');
    }

    /**
     * @param Transaction[] $transactions
     * @return array
     */
    public function fillWithCOGTransactions(array $transactions, bool $shouldApplyMoneyAccuracy = false): array
    {
        if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
            return $this->fillWithCOGTransactionsV2($transactions, $shouldApplyMoneyAccuracy);
        }

        $this->info('Filling with COG transactions started');
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $vatFromAmazonCategoryIds = FinanceEventCategory::getVatIds();
        $orderPriceCategoryIds = FinanceEventCategory::getOrderPriceIds();
        $refundOrderPriceCategoryIds = FinanceEventCategory::getRefundOrderPriceIds();
        $ordersWithVatFromAmazon = [];
        $ordersWithAlreadyAppliedVat = [];
        $vcsReverseCategoriesMapping = FinanceEventCategory::getVCSReverseTransactionsMapping();

        // We need this price to calculate VAT
        $orderPrices = [];
        $refundOrderPrices = [];

        foreach ($transactions as $transaction) {
            $key = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);

            if (in_array($transaction->CategoryId, $orderPriceCategoryIds)) {
                $orderPrices[$key] = $orderPrices[$key] ?? 0;
                $orderPrices[$key] += $transaction->Amount;
            }

            if (in_array($transaction->CategoryId, $refundOrderPriceCategoryIds)) {
                $refundOrderPrices[$key] = $refundOrderPrices[$key] ?? 0;
                $refundOrderPrices[$key] += $transaction->Amount;
            }

            if (in_array($transaction->CategoryId, $vatFromAmazonCategoryIds)) {
                $ordersWithVatFromAmazon[] = $key;
            }
        }

        $ordersWithVatFromAmazon = array_unique($ordersWithVatFromAmazon);

        foreach ($transactions as $transaction) {
            $key = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);
            if (array_key_exists($transaction->CategoryId, $vcsReverseCategoriesMapping)
                && in_array($transaction->AmazonOrderId, $this->b2bOrderIds)
                && !in_array($key, $ordersWithVatFromAmazon)
                && $transaction->AmountEUR !== 0
                && $this->dbManager->getSeller()->is_vcs_enabled
            ) {
                $categoryId = $vcsReverseCategoriesMapping[$transaction->CategoryId];
                $oppositeTransaction = clone $transaction;
                $oppositeTransaction->Amount *= -1;
                $oppositeTransaction->AmountEUR *= -1;
                $oppositeTransaction->CategoryId = $categoryId;
                $transactions[] = $oppositeTransaction;
                continue;
            }

            $allowedCategories = [$organicSalesCategoryId];
            if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
                $allowedCategories = [$organicSalesCategoryId, $organicRefundCategoryId];
            }

            if (!in_array($transaction->CategoryId, $allowedCategories)) {
                continue;
            }

            $shouldSkipVATCalculation = in_array($key, $ordersWithVatFromAmazon)
                || in_array($key, $ordersWithAlreadyAppliedVat);

            if (in_array($transaction->AmazonOrderId, $this->b2bOrderIds) && !$shouldSkipVATCalculation) {
                $shouldSkipVATCalculation = !!$this->dbManager->getSeller()->is_vcs_enabled;
            }

            $orderPrice = $transaction->CategoryId == $organicSalesCategoryId
                ? $orderPrices[$key] ?? null
                : $refundOrderPrices[$key] ?? null
            ;

            if (!empty($orderPrice)) {
                $orderPrice = abs($orderPrice);
            }

            /** @var Transaction[] $COGSalesTransactions */
            $COGSalesTransactions = $this
                ->COGChangesManager
                ->generateNewForSalesTransaction($transaction, $orderPrice, $shouldSkipVATCalculation);

            $transactions = array_merge(
                $transactions,
                $COGSalesTransactions
            );

            if (!$shouldSkipVATCalculation) {
                $ordersWithAlreadyAppliedVat[] = $key;
            }
        }

        $this->info('Filling with COG transactions finished');
        return $transactions;
    }

    public function applyEuVatForAmazonFees(array $transactions): array
    {
        $this->info('Applying EU VAT for Amazon fees started');

        $euMarketplaceIds = AmazonMarketplace::getEuCountriesIds();
        $amazonFeesCategoryIds = FinanceEventCategory::getAmazonFeesIds();
        // Amazon fees VAT can be returned starting from this date in EU zone marketplaces
        // (according to government decision).
        $fromDateTime = strtotime('2024-08-01 00:00:00');

        foreach ($transactions as $transaction) {
            if (!in_array($transaction->CategoryId, $amazonFeesCategoryIds)
                || !in_array($transaction->MarketplaceId, $euMarketplaceIds)
                || empty($transaction->AmountEUR)
                || strtotime($transaction->PostedDate) < $fromDateTime
            ) {
                continue;
            }

            $euAmazonFeesVat = Seller::getEuAmazonFeesVat($transaction->SellerId);
            $this->info('EU VAT for Amazon fees: ' . $euAmazonFeesVat);

            if (empty($euAmazonFeesVat)) {
                $this->info('EU VAT for Amazon fees is not set');
                return $transactions;
            }

            $transaction->AmountEUR = $transaction->AmountEUR / (1 + $euAmazonFeesVat * 0.01);
            $transaction->Amount = $transaction->Amount / (1 + $euAmazonFeesVat * 0.01);
        }
        $this->info('Applying EU VAT for Amazon fees finished');

        return $transactions;
    }

    public function fillWithCOGTransactionsV2(array $transactions, bool $shouldApplyMoneyAccuracy = false): array
    {
        $transactions = $this->applyEuVatForAmazonFees($transactions);

        $this->info('Filling with COG transactions V2 started');
        $VATFromAmazonCategoryIds = FinanceEventCategory::getVatIds();
        $orderPriceCategoryIds = FinanceEventCategory::getOrderPriceIds();
        $refundOrderPriceCategoryIds = FinanceEventCategory::getRefundOrderPriceIds();
        $orderItemsWithVATFromAmazon = [];
        $VCSReverseCategoriesMapping = FinanceEventCategory::getVCSReverseTransactionsMapping();

        // We need this price to calculate VAT
        $orderPrices = [];

        foreach ($transactions as $transaction) {
            $key = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);

            if (in_array($transaction->CategoryId, $orderPriceCategoryIds)) {
                $orderPrices[$key]['order_price'] = $orderPrices[$key]['order_price'] ?? 0;
                $orderPrices[$key]['order_price_refund'] = $orderPrices[$key]['order_price_refund'] ?? 0;
                $orderPrices[$key]['order_price'] += $transaction->Amount;
            }

            if (in_array($transaction->CategoryId, $refundOrderPriceCategoryIds)) {
                $orderPrices[$key]['order_price'] = $orderPrices[$key]['order_price'] ?? 0;
                $orderPrices[$key]['order_price_refund'] = $orderPrices[$key]['order_price_refund'] ?? 0;
                $orderPrices[$key]['order_price_refund'] += $transaction->Amount;
            }

            if (in_array($transaction->CategoryId, $VATFromAmazonCategoryIds)) {
                $orderItemsWithVATFromAmazon[] = $key;
            }
        }

        /** @var Seller[] $sellers */
        $sellers = Seller::find()
            ->where(['customer_id' => $this->dbManager->getCustomerId()])
            ->cache(60 * 5)
            ->all();
        $sellersWithVCSEnabled = [];
        foreach ($sellers as $seller) {
            if ($this->dbManager->isFakeSeller($seller->id)) {
                continue;
            }

            if ($seller->is_vcs_enabled) {
                $sellersWithVCSEnabled[] = $seller->id;
            }
        }

        $reverseVCSTransactions = [];
        foreach ($transactions as $transaction) {
            $key = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);
            $isB2BOrder = in_array($transaction->AmazonOrderId, $this->b2bOrderIds);
            $isVCSReverseCategory = array_key_exists($transaction->CategoryId, $VCSReverseCategoriesMapping);
            $hasVATFromAmazon = in_array($key, $orderItemsWithVATFromAmazon);
            $isVCSEnabled = in_array($transaction->SellerId, $sellersWithVCSEnabled);

            if ($isVCSReverseCategory
                && $isB2BOrder
                && !$hasVATFromAmazon
                && $isVCSEnabled
                && (int)$transaction->AmountEUR !== 0
            ) {
                $categoryId = $VCSReverseCategoriesMapping[$transaction->CategoryId];
                $oppositeTransaction = clone $transaction;
                $oppositeTransaction->Amount *= -1;
                $oppositeTransaction->AmountEUR *= -1;
                $oppositeTransaction->CategoryId = $categoryId;
                $reverseVCSTransactions[] = $oppositeTransaction;
            }
        }
        $transactions = array_merge($transactions, $reverseVCSTransactions);

        $COGTransactions = $this->COGTransactionsGenerator->generateBulk(
            $transactions,
            $shouldApplyMoneyAccuracy,
            $orderPrices,
            $orderItemsWithVATFromAmazon,
            $this->fbaOrderIds,
            $this->b2bOrderIds,
            null,
            $this->manualShippingCostOrderIds
        );

        $transactions = array_merge($transactions, $COGTransactions);

        $this->info('Filling with COG transactions finished');
        return $transactions;
    }

    /**
     * Converts currency item received from amazon to clickhouse transaction model.
     *
     * @param array $currencyItem
     * @param array $facts
     * @return Transaction
     */
    public function convertCurrencyItemToTransaction(array $currencyItem, array $facts, bool $shouldPopulate = true): Transaction
    {
        $useV2Logic = $this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2);
        if ($useV2Logic) {
            $moneyAccuracy = $shouldPopulate
                ? 1
                : $this->customerComponent->getMoneyAccuracy();
        } else {
            $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();
        };

        $transaction = new Transaction();
        $transaction->AmazonOrderId = $facts[self::FACT_AMAZON_ORDER_ID] ?? null;
        $transaction->SellerOrderId = $facts[self::FACT_SELLER_ORDER_ID] ?? null;
        $transaction->EventPeriodId = $facts[self::FACT_EVENT_PERIOD_ID] ?? null;
        $transaction->SellerSKU = html_entity_decode($facts[self::FACT_SELLER_SKU] ?? null);
        $transaction->Quantity = $facts[self::FACT_QUANTITY] ?? 1;
        $transaction->SellerId = $facts[self::FACT_SELLER_ID] ?? null;
        $transaction->MarketplaceId = $facts[self::FACT_MARKETPLACE_ID] ?? null;
        $transaction->Currency = $currencyItem['CurrencyCode'];
        $transaction->ASIN = $facts[self::FACT_ASIN] ?? null;

        // Only for estimated transactions
        if (array_key_exists(self::FACT_PRODUCT_EAN, $facts)) {
            $transaction->EAN = $facts[self::FACT_PRODUCT_EAN] ?? null;
            $transaction->ISBN = $facts[self::FACT_PRODUCT_ISBN] ?? null;
            $transaction->UPC = $facts[self::FACT_PRODUCT_UPC] ?? null;
            $transaction->ProductId = $facts[self::FACT_PRODUCT_ID] ?? null;
            $transaction->Title = $facts[self::FACT_TITLE] ?? null;
            $transaction->ParentASIN = $facts[self::FACT_PRODUCT_PARENT_ASIN] ?? null;
            $transaction->Brand = $facts[self::FACT_PRODUCT_BRAND] ?? null;
            $transaction->Model = $facts[self::FACT_PRODUCT_MODEL] ?? null;
            $transaction->ProductType = $facts[self::FACT_PRODUCT_TYPE] ?? null;
            $transaction->Manufacturer = $facts[self::FACT_PRODUCT_MANUFACTURER] ?? null;
            $transaction->AdultProduct = $facts[self::FACT_PRODUCT_IS_ADULT_ONLY] ?? null;
            $transaction->OfferType = $facts[self::FACT_OFFER_TYPE] ?? null;
            $transaction->MainImage = $facts[self::FACT_PRODUCT_MAIN_IMAGE] ?? null;
            $transaction->TagId = $facts[self::FACT_TAG_ID] ?? [];
        }

        if ($moneyAccuracy === 1) {
            $transaction->Amount = (float)($currencyItem['CurrencyAmount']);
        } else {
            $transaction->Amount = (int)round(($currencyItem['CurrencyAmount'] * $moneyAccuracy));
        }

        if (!empty($facts[self::FACT_MARKETPLACE_NAME])) {
            /** @var AmazonMarketplace $marketplace */
            $salesChannel = strtolower(trim($facts[self::FACT_MARKETPLACE_NAME]));

            $marketplace = AmazonMarketplace::getBySalesChannel($salesChannel);

            if (!empty($marketplace)) {
                $transaction->MarketplaceId = $marketplace->id;
            } else {
                $transaction->MarketplaceId = trim($facts[self::FACT_MARKETPLACE_NAME]);
            }
        }
        $transactionDate = $facts[self::FACT_POSTED_DATE] ?? date('Y-m-d H:i:s');
        $transaction->TransactionDate = $transactionDate;
        $transaction->PostedDate = $transactionDate;

        if (!$useV2Logic && $shouldPopulate) {
            $transaction = $this->populateDates($transaction, $transactionDate);
        }

        $amountEur = $this->currencyRateManager->toBaseCurrency(
            $currencyItem['CurrencyAmount'],
            $transaction->Currency,
            new \DateTime($transaction->PostedDate)
        );

        if (null === $amountEur) {
            throw new Exception("Unable to convert {$currencyItem['CurrencyCode']} to EUR on date {$transaction->PostedDate}");
        }

        if ($moneyAccuracy === 1) {
            $transaction->AmountEUR = $amountEur;
        } else {
            // (int)(35.87 * 100) => 3586 (should be 3587)
            // (int)(25.87 * 100) => 2587 (expected)
            // Round need here to prevent such behaviour of float number.
            $transaction->AmountEUR = (int)round($amountEur * $moneyAccuracy);
        }

        if ($transaction->AmountEUR === 0) {
            $transaction->Quantity = 0;
        }

        if (!empty($facts[self::FACT_CATEGORY_PATH])) {
            $category = $this->getOrCreateCategoryByPath($facts[self::FACT_CATEGORY_PATH], $transaction);
            $transaction->CategoryId = $category->id;
        }

        return $transaction;
    }

    private function populateDates(Transaction $transaction, $date): Transaction
    {
        if (is_null($date)) {
            throw new Exception('Transaction date can not be null');
        }

        $transactionDate = date('Y-m-d H:i:s', strtotime($date));
        $transaction->TransactionDate = $transactionDate;

        /** Set transaction date as PostedDate if Amazon order ID is not existed or has incorrect format or transaction is created more than 23 month ago  */
        if (is_null($transaction->AmazonOrderId) ||
            !preg_match('/\A[0-9]{3}-[0-9]{7}-[0-9]{7}\Z/', $transaction->AmazonOrderId)
        ) {
            $transaction->PostedDate = $transactionDate;
        } else {
            /** @var AmazonOrder $order */
            $order = AmazonOrder::find()->cache(10 * 60)->where(['amazon_order_id' => $transaction->AmazonOrderId])->one();

            if (is_null($order)) {
                if (
                    strtotime($transactionDate) < strtotime('-2 years')
                    || (new UnknownOrderService($transaction->SellerId))->isReachedErrorLimit($transaction->AmazonOrderId)
                ) {
                    $transaction->PostedDate = $transactionDate;
                    return $transaction;
                }

                /** @var Seller $seller */
                $seller = Seller::find()->where(['seller.id' => $transaction->SellerId])->one();

                if ($seller && $seller->is_order_init_periods_loaded) {
                    $this->addOrderIdsForLoading($transaction->AmazonOrderId);

                    return $transaction;
                }
                throw new OrderNotFoundException("Order {$transaction->AmazonOrderId} not found. Order init periods are not loaded");
            }

            $minPeriodStartDate = EventPeriod::find()->cache(60 * 60)->min('start_date');

            if (is_null($minPeriodStartDate)) {
                throw new Exception('Can not get start_date from event periods');
            }

            /** Set transaction date as PostedDate if Amazon order purchase_date is older than min event period start date */
            $transaction->PostedDate = strtotime($order->purchase_date) < strtotime($minPeriodStartDate) ? $transactionDate : $order->purchase_date;

            if (empty($transaction->MarketplaceId)) {
                $transaction->MarketplaceId = $order->marketplace_id;
            }

            if (!$order->has_transactions) {
                $this->orderIdsForUpdateHasTransactions[] = $order->id;
            }

            if ($order->is_business_order) {
                $this->b2bOrderIds[$transaction->AmazonOrderId] = $transaction->AmazonOrderId;
            }
        }

        return $transaction;
    }

    /**
     * @param Transaction[] $transactions
     * @return void
     * @throws OrderNotFoundException
     */
    private function populateDateBulk(array $transactions): void
    {
        $this->info('Populating dates started');
        $orderIdsToFind = [];
        $sellerIdsToFind = [];
        foreach ($transactions as $transaction) {
            $orderIdsToFind[] = (string)$transaction->AmazonOrderId;
            $sellerIdsToFind[] = (string)$transaction->SellerId;
        }
        $orderIdsToFind = array_unique($orderIdsToFind);
        $sellerIdsToFind = array_unique($sellerIdsToFind);

        $amazonOrders = AmazonOrder::find()
            ->where(['amazon_order_id' => $orderIdsToFind])
            ->indexBy('amazon_order_id')
            ->asArray()
            ->all();

        foreach ($amazonOrders as $amazonOrder) {
            if ($amazonOrder['has_manual_shipping_cost']) {
                $this->manualShippingCostOrderIds[$amazonOrder['amazon_order_id']] = $amazonOrder['amazon_order_id'];
            }
        }

        $reachedErrorLimits = UnknownAmazonOrder::find()
            ->where([
                'AND',
                ['amazon_order_id' => $orderIdsToFind],
                ['>', 'errors_count', UnknownOrderService::ERROR_LIMIT]
            ])
            ->indexBy('amazon_order_id')
            ->asArray()
            ->all();
        $sellers = Seller::find()
            ->where(['id' => $sellerIdsToFind])
            ->indexBy('id')
            ->asArray()
            ->all();

        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();

        foreach ($transactions as $transaction) {
            // This time offset is used to prevent important transactions collapsing
            $postedDateOffsetS = 0;

            if (in_array($transaction->CategoryId, [$organicSalesCategoryId, $organicRefundCategoryId])
                && empty($transaction->COGCategoryId)
            ) {
                $postedDateOffsetS = random_int(1, 60);
            }

            if (empty($transaction->AmazonOrderId)
                || !preg_match('/\A[0-9]{3}-[0-9]{7}-[0-9]{7}\Z/', $transaction->AmazonOrderId)
            ) {
                $transaction->PostedDate = date('Y-m-d H:i:s', strtotime($transaction->TransactionDate) - $postedDateOffsetS);
                continue;
            }

            $amazonOrder = $amazonOrders[$transaction->AmazonOrderId] ?? null;

            if (empty($amazonOrder)) {
                $isTooOld = strtotime($transaction->TransactionDate) < strtotime('-2 years');
                $isReachedErrorLimit = array_key_exists($transaction->AmazonOrderId, $reachedErrorLimits);

                if ($isTooOld || $isReachedErrorLimit) {
                    $transaction->PostedDate = date('Y-m-d H:i:s', strtotime($transaction->TransactionDate) - $postedDateOffsetS);;
                    continue;
                }

                $seller = $sellers[$transaction->SellerId] ?? null;
                if ($seller && $seller['is_order_init_periods_loaded']) {
                    $this->orderIdsForLoading[$transaction->AmazonOrderId] = $transaction->AmazonOrderId;
                    continue;
                }

                throw new OrderNotFoundException("Order {$transaction->AmazonOrderId} not found");
            }

            $transaction->PostedDate = date('Y-m-d H:i:s', strtotime($amazonOrder['purchase_date']) - $postedDateOffsetS);
            if (empty($transaction->MarketplaceId)) {
                $transaction->MarketplaceId = $amazonOrder['marketplace_id'];
            }

            // Fill has transactions property only if there is at least on principal sale transaction.
            // We will estimate values if sale transaction is missing.
            if (!$amazonOrder['has_transactions'] && $transaction->CategoryId == $organicSalesCategoryId) {
                $this->orderIdsForUpdateHasTransactions[$amazonOrder['id']] = $amazonOrder['id'];
            }

            if ($amazonOrder['is_business_order']) {
                $this->b2bOrderIds[$transaction->AmazonOrderId] = $transaction->AmazonOrderId;
            }

            if ($amazonOrder['fulfillment_channel'] === AmazonOrder::FULFILMENT_CHANNEL_AFN) {
                $this->fbaOrderIds[] = $transaction->AmazonOrderId;
            }
        }
        $this->info('Populating dates finished');
    }

    /**
     * Checks whether finance item received from amazon is a Currency.
     *
     * @param array $financeItem
     * @return bool
     */
    private function isCurrencyItem(array $financeItem): bool
    {
        return isset($financeItem['CurrencyCode'], $financeItem['CurrencyAmount']);
    }

    /**
     * Generates and returns category path.
     *
     * @param $financeItemKey
     * @param $financeItem
     * @param $parentCategoryPath
     * @return string
     */
    private function getCategoryPath($financeItemKey, $financeItem, $parentCategoryPath)
    {
        if (!is_string($financeItemKey)) {
            $financeItemKey = null;
        }

        $postfix = (strlen($financeItemKey) > 9) ? substr($financeItemKey, -9) : '';
        if ($postfix === 'EventList') {
            $financeItemKey = substr($financeItemKey, 0, -9);
        }

        $postfix = (strlen($financeItemKey) > 4) ? substr($financeItemKey, -4) : '';
        if ($postfix === 'List') {
            $financeItemKey = substr($financeItemKey, 0, -4);
        }

        $pathComponents = [$parentCategoryPath, $financeItemKey];
        $pathComponents = array_filter($pathComponents);
        $typeProperties = array_intersect(array_keys($financeItem), self::TYPE_PROPERTIES);

        foreach ($typeProperties as $typeProperty) {
            if (empty($financeItem[$typeProperty])) {
                continue;
            }

            $pathComponents[] = $financeItem[$typeProperty];
        }

        $categoryPath = implode(self::CATEGORY_PATH_DELIMITER, $pathComponents);

        if ($this->isCurrencyItem($financeItem)) {
            if (0 == $financeItem['CurrencyAmount']) {
                $signPostfix = FinanceEventCategory::PLUS_ZERO_POSTFIX;
            } else {
                $signPostfix = $financeItem['CurrencyAmount'] > 0
                    ? FinanceEventCategory::PLUS_POSTFIX
                    : FinanceEventCategory::MINUS_POSTFIX;
            }

            $categoryPath .= $signPostfix;
        }

        return $categoryPath;
    }

    /**
     * Collects and returns important for transaction fields and their values.
     *
     * @param array $item
     * @return array
     */
    private function getFacts(array $item): array
    {
        $facts = [];

        foreach (self::AMAZON_FACTS_NORMALIZATION_MAPPING as $amazonFactName => $normalizedFactName) {
            if (!isset($item[$amazonFactName])) {
                continue;
            }

            $fact = $item[$amazonFactName];

            if (!is_string($fact) && !is_int($fact) && !is_array($fact)) {
                continue;
            }

            $facts[$normalizedFactName] = $fact;
        }

        return $facts;
    }

    /**
     * @param string $categoryPath
     * @return FinanceEventCategory
     */
    private function getOrCreateCategoryByPath(string $categoryPath, Transaction $transaction): FinanceEventCategory
    {
        static $categoriesCache = [];

        if (isset($categoriesCache[$categoryPath])) {
            return $categoriesCache[$categoryPath];
        }

        if (false !== strpos($categoryPath, FinanceEventCategory::PLUS_ZERO_POSTFIX)) {
            // If we have ZERO value, we should try to find the same value with PLUS or MINUS sign and
            // attach current category path to found one
            $plusCategoryPath = str_replace(FinanceEventCategory::PLUS_ZERO_POSTFIX, FinanceEventCategory::PLUS_POSTFIX, $categoryPath);
            $plusCategory = $this->getCategoryByPath($plusCategoryPath);
            if (false !== $plusCategory) {
                $categoriesCache[$categoryPath] = $plusCategory;
                $categoriesCache[$plusCategoryPath] = $plusCategory;
                return $plusCategory;
            }

            $minusCategoryPath = str_replace(FinanceEventCategory::PLUS_ZERO_POSTFIX, FinanceEventCategory::MINUS_POSTFIX, $categoryPath);
            $minusCategory = $this->getCategoryByPath($minusCategoryPath);
            if (false !== $minusCategory) {
                $categoriesCache[$categoryPath] = $minusCategory;
                $categoriesCache[$minusCategoryPath] = $minusCategory;
                return $minusCategory;
            }
        } else {
            // Here we have PLUS or MINUS sign, we should check if some ZERO values left.
            // If we found ZERO values - we should attach them to current one.
            $isPlusCategory = false !== strpos($categoryPath, FinanceEventCategory::PLUS_POSTFIX);
            $plusZeroCategoryPath = str_replace(
                $isPlusCategory ? FinanceEventCategory::PLUS_POSTFIX : FinanceEventCategory::MINUS_POSTFIX,
                FinanceEventCategory::PLUS_ZERO_POSTFIX,
                $categoryPath
            );
            $plusZeroCategory = $this->getCategoryByPath($plusZeroCategoryPath);

            if (false !== $plusZeroCategory) {
                $plusZeroCategory->path = $categoryPath;
                $plusZeroCategory->first_transaction = json_encode($transaction);
                $plusZeroCategory->save();

                $categoriesCache[$categoryPath] = $plusZeroCategory;
                $categoriesCache[$plusZeroCategoryPath] = $plusZeroCategory;
                return $plusZeroCategory;
            }
        }

        $eventCategory = $this->getCategoryByPath($categoryPath);

        if (empty($eventCategory)) {
            $eventCategory = new FinanceEventCategory();
            $eventCategory->path = $categoryPath;
            $eventCategory->first_transaction = json_encode($transaction);
            $eventCategory->save(false);
        }

        if (empty($eventCategory->first_transaction)) {
            $eventCategory->first_transaction = json_encode($transaction);
            $eventCategory->save(false);
        }

        $categoriesCache[$categoryPath] = $eventCategory;

        return $eventCategory;
    }

    private function getCategoryByPath(string $categoryPath)
    {
        return $this->cache->getOrSet(['event_category_' . $categoryPath], function () use ($categoryPath) {
            $eventCategory = FinanceEventCategory::find()
                ->where(['path' => $categoryPath])
                ->one();
            return $eventCategory ?: false;
        }, 60 * 60);
    }

    public function getOrderIdsForUpdateHasTransactions(): array
    {
        $orderIds = $this->orderIdsForUpdateHasTransactions;
        $orderIds = array_unique($orderIds);
        $this->orderIdsForUpdateHasTransactions = [];
        return $orderIds;
    }

    /**
     * @return array
     */
    public function getOrderIdsForLoading(): array
    {
        $orderIds = $this->orderIdsForLoading;
        $orderIds = array_unique($orderIds);
        $this->orderIdsForLoading = [];
        return $orderIds;
    }

    private function addOrderIdsForLoading(string $id)
    {
        $this->orderIdsForLoading[] = $id;
    }
}
