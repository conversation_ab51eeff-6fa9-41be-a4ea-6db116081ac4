<?php

namespace common\components\services\financialEvent;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\services\PeriodsHelper;
use common\models\finance\EventPeriod;
use common\models\Seller;

class EventPeriodService
{
    use LogToConsoleTrait;

    const SHIFT_WINDOW_MINUTES = 90;
    const PERIOD_MIN_SIZE_MINUTES = 10;
    const PERIOD_MAX_SIZE_MINUTES = 60 * 24;

    /** @var Seller */
    private Seller $seller;

    private PeriodsHelper $periodsHelper;

    protected DbManager $dbManager;

    /**
     * @param Seller $seller
     */
    public function __construct(Seller $seller)
    {
        $this->seller = $seller;
        $this->dbManager = \Yii::$app->get('dbManager');
        $this->dbManager->setSellerId($seller->id);
        $this->periodsHelper = new PeriodsHelper();
    }

    /**
     * @return bool
     */
    private function isAllInitPeriodsLoaded(): bool
    {
        return !EventPeriod::find()->where(['type' => EventPeriod::TYPE_INIT])->andWhere(['!=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])->exists();
    }

    /**
     * @throws \Exception
     */
    public function checkIsAllInitPeriodsLoaded()
    {
        $this->seller->is_init_periods_loaded = $this->isAllInitPeriodsLoaded();
        $this->seller->saveOrThrowException();
    }

    /**
     * @throws \Exception
     */
    public function generateInitPeriods()
    {
        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        $periods = $this->periodsHelper->generatePeriods(
            new \DateTime(date('Y-m-01 H:i:s', strtotime('-2 year'))),
            (new \DateTime())->modify("-" . self::PERIOD_MAX_SIZE_MINUTES . " minutes"),
            self::PERIOD_MAX_SIZE_MINUTES
        );

        $transaction = EventPeriod::getDb()->beginTransaction();

        try {
            $this->savePeriods($periods);
            $this->seller->is_init_periods_created = true;
            $this->seller->update(false, ['is_init_periods_created']);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @param Period $period
     * @param string $type
     * @throws \Exception
     */
    private function savePeriods(array $periods)
    {
        $this->info("Saving periods started");
        $periodsToSave = [];

        foreach ($periods as $period) {
            $periodsToSave[] = [
                'finish_date' => $period->getFinishDate(),
                'start_date' => $period->getStartDate(),
                'loading_status' => EventPeriod::LOADING_STATUS_NEW,
                'type' => EventPeriod::TYPE_INIT,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }

        $periodsToSave[count($periodsToSave) - 1]['type'] = EventPeriod::TYPE_REFRESH;
        $this->info($periodsToSave);

        EventPeriod::getDb()
            ->createCommand()
            ->batchInsert(
                EventPeriod::tableName(),
                array_keys(array_values($periodsToSave)[0]),
                $periodsToSave
            )
            ->execute()
        ;
        $this->info("Saving periods finished");
    }

    /**
     * @throws \Exception
     */
    public function generateRefreshPeriod()
    {
        if (PeriodsMerger::isInProgress($this->seller->id)) {
            $this->info("Periods merge is in progress, skipping this seller");
            return;
        }

        /** @var EventPeriod $last */
        $last = EventPeriod::find()
            ->orderBy('finish_date DESC')
            ->limit(1)
            ->one();

        if (!$last) {
            return;
        }

        $periodEndDate = (new \DateTime())->modify("-" . self::SHIFT_WINDOW_MINUTES . " minutes");
        $periodStartDate = (new \DateTime($last->finish_date))->modify('+1 second');

        if ($periodEndDate->getTimestamp() - $periodStartDate->getTimestamp() < 60 * self::PERIOD_MIN_SIZE_MINUTES) {
            return;
        }

        $periods = $this->periodsHelper->generatePeriods($periodStartDate, $periodEndDate, self::PERIOD_MAX_SIZE_MINUTES);
        if (count($periods) === 0) {
            return;
        }

        $this->savePeriods($periods);
    }
}
