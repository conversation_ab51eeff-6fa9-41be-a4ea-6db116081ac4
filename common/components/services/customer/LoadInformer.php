<?php

namespace common\components\services\customer;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\finance\EventPeriod;
use common\models\LoadInfo;
use common\models\Seller;

class LoadInformer
{
    /** @var int */
    private $customerId;

    public function __construct()
    {
        $this->customerId = \Yii::$app->dbManager->getCustomerId();
    }

    public function getInfo(): array
    {
        $result = [];
        $sellerIds = Seller::find()
            ->where(['customer_id' => $this->customerId])
            ->andWhere(['is_analytic_active' => true])
            ->andWhere(['is_db_created' => true])
            ->select(['seller.id'])
            ->column();

        foreach ($sellerIds as $sellerId) {
            $sellerDbManager = \Yii::$app->get('dbManager');
            $sellerDbManager->setSellerId($sellerId);

            $result[] = new LoadInfo($sellerId, EventPeriod::find()->max('clickhouse_moved_at'));
        }

        return $result;
    }

    public function getMinimumStatisticDate(): array
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $tableName = Transaction::tableName();

        $date = $dbManager->getClickhouseCustomerDb()
            ->createCommand("SELECT min(PostedDate) FROM {$tableName}")->queryScalar();

        if ($date === '1970-01-01 00:00:00') {
            $date = date('Y-m-01 00:00:00', strtotime('-1 month'));
        }

        return [
            'date' => $date
        ];
    }
}
