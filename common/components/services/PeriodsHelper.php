<?php

namespace common\components\services;

use common\components\services\financialEvent\Period;

class PeriodsHelper
{
    /**
     * @param \DateTime $dateStart
     * @param \DateTime $dateEnd
     * @param int $periodMaxSizeMinutes
     * @return Period[]  Array of periods
     * @throws \Exception
     */
    public function generatePeriods(
        \DateTime $dateStart,
        \DateTime $dateEnd,
        int $periodMaxSizeMinutes,
        int $periodMinSizeMinutes = 10
    ): array
    {
        if ($dateStart > $dateEnd) {
            throw new \Exception('Date start can not be greater than date end');
        }

        $periods = [];
        $periodStartDate = clone $dateStart;

        while(true) {
            $periodEndDate = (clone $periodStartDate)
                ->modify("+{$periodMaxSizeMinutes} minutes")
                ->modify('-1 second');
            if ($periodEndDate->getTimestamp() >= $dateEnd->getTimestamp()) {
                $periodEndDate = clone $dateEnd;
            }

            $diffMinutes = floor(($periodEndDate->getTimestamp() - $periodStartDate->getTimestamp()) / 60);

            // Do not allow too short periods
            if ($diffMinutes <= $periodMinSizeMinutes) {
                break;
            }

            $periods[] = new Period(
                $periodStartDate->format('Y-m-d H:i:s'),
                $periodEndDate->format('Y-m-d H:i:s'),
            );
            $periodStartDate = $periodEndDate->modify('+1 second');

            if ($periodStartDate >= $dateEnd) {
                break;
            }
        }

        return $periods;
    }
}