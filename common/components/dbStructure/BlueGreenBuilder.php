<?php

namespace common\components\dbStructure;

use common\components\core\db\dbManager\DbManager;
use common\components\dbStructure\dbChangesApplier\Factory;
use common\components\LogToConsoleTrait;
use common\models\DbStructure;
use common\models\DbStructureTable;

class BlueGreenBuilder
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected TableNameGenerator $tableNameGenerator;
    protected Factory $dbChangesApplierFactory;

    public function __construct(
        DbManager $dbManager, 
        TableNameGenerator $tableNameGenerator, 
        Factory $dbChangesApplierFactory
    ) {
        /** @var DbManager dbManager */
        $this->dbManager = $dbManager;
        $this->tableNameGenerator = $tableNameGenerator;
        $this->dbChangesApplierFactory = $dbChangesApplierFactory;
    }

    public function incVersion(array $changedTables): void
    {
        $isJustInitialized = $this->initIfNeed($changedTables);

        if ($isJustInitialized) {
            return;
        }

        /** @var DbStructureTable[] $greenTables */
        $greenTables = $this->getGreenTables($changedTables);

        foreach ($greenTables as $greenTable) {
            $greenTable->version++;
            $greenTable->save(false);
        }
    }

    /**
     * Creates new blue structure from zero.
     * Copies not changed tables and their versions from green structure.
     * Increment versions of tables which were changed.
     *
     * @param array $changedTables
     * @param string $description
     * @return DbStructure
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    public function buildBlue(array $changedTables = [], string $description = ''): DbStructure
    {
        $this->info("Building blue structure  for customer " . $this->dbManager->getCustomerId());
        $this->initIfNeed($changedTables);

        $blueDbStructure = DbStructure::findOne([
            'type' => DbStructure::TYPE_BLUE,
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        if (!empty($blueDbStructure)) {
            $this->info("Blue structure already exists. Skipping");
            return $blueDbStructure;
        }

        $transaction = DbStructure::getDb()->beginTransaction();

        try {
            $dbStructure = new DbStructure();
            $dbStructure->description = $description;
            $dbStructure->customer_id = $this->dbManager->getCustomerId();
            $dbStructure->type = DbStructure::TYPE_BLUE;
            $dbStructure->save(false);

            /** @var DbStructureTable[] $greenTables */
            $greenTables = $this->getGreenTables();
            $changedTables = array_flip($changedTables);

            // Copying tables from green structure, increments those of them which were changed
            foreach ($greenTables as $greenTable) {
                $blueTable = new DbStructureTable();
                $blueTable->db_structure_id = $dbStructure->id;
                $blueTable->table_name = $greenTable->table_name;
                $blueTable->database_type = $greenTable->database_type;
                $blueTable->version = $greenTable->version;
                if (isset($changedTables[$greenTable->table_name]))  {
                    $blueTable->version++;
                    unset($changedTables[$greenTable->table_name]);
                }
                $blueTable->save(false);

                $newTableName = $this
                    ->tableNameGenerator
                    ->generateByKnownVersion($blueTable->table_name, $blueTable->version);
                $prevTableName = $this
                    ->tableNameGenerator
                    ->generateByKnownVersion($greenTable->table_name, $greenTable->version);

                $dbChangesApplier = $this
                    ->dbChangesApplierFactory
                    ->getDbChangesApplier($greenTable->database_type);
                $dbChangesApplier->createTable($newTableName, $prevTableName);
                $this->info(sprintf(
                    "Created new table %s (based on table %s) in database %s of customer %d",
                    $newTableName,
                    $prevTableName,
                    $blueTable->database_type,
                    $this->dbManager->getCustomerId()
                ));
            }

            // Creating new tables which has not been in green structure
            foreach ($changedTables as $tableName => $nullValue) {
                $databaseType = DbStructure::getDatabaseTypeByTableName($tableName);
                $blueTable = new DbStructureTable();
                $blueTable->db_structure_id = $dbStructure->id;
                $blueTable->table_name = $tableName;
                $blueTable->database_type = $databaseType;
                $blueTable->version = 1;
                $blueTable->save(false);
            }

            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->info("Roll back due to exception");
            throw $e;
        }

        return $dbStructure;
    }

    /**
     * Flips blue structure to green structure.
     * No changes in db, can be reverse flipped any time while blue structure exists.
     *
     * @return void
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    public function flip(): void
    {
        $this->info("Flip blue to green for customer " . $this->dbManager->getCustomerId());
        $greenDbStructure = DbStructure::findOne([
            'type' => DbStructure::TYPE_GREEN,
            'customer_id' => $this->dbManager->getCustomerId()
        ]);
        $blueDbStructure = DbStructure::findOne([
            'type' => DbStructure::TYPE_BLUE,
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        $transaction = DbStructure::getDb()->beginTransaction();
        try {
            if (!empty($greenDbStructure) && !empty($blueDbStructure)) {
                $greenDbStructure->type = DbStructure::TYPE_TMP;
                $greenDbStructure->save(false);
                $blueDbStructure->type = DbStructure::TYPE_GREEN;
                $blueDbStructure->save(false);
                $greenDbStructure->type = DbStructure::TYPE_BLUE;
                $greenDbStructure->save(false);
                $this->info(sprintf(
                    "Flipped blue structure to green one for customer %s",
                    $this->dbManager->getCustomerId()
                ));
            }

            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->info("Rollback due to exception");
            throw $e;
        }
    }

    /**
     * Removes blue structure together with removing its tables from db
     *
     * @return void
     * @throws \Throwable
     * @throws \yii\db\Exception
     * @throws \yii\db\StaleObjectException
     */
    public function removeBlue(): void
    {
        $this->info("Removing blue structure  for customer " . $this->dbManager->getCustomerId());
        $blueDbStructure = DbStructure::findOne([
            'type' => DbStructure::TYPE_BLUE,
            'customer_id' => $this->dbManager->getCustomerId()
        ]);

        if (empty($blueDbStructure)) {
            $this->info("No blue structure found. Skipping");
            return;
        }

        $transaction = DbStructure::getDb()->beginTransaction();
        try {
            /** @var DbStructureTable[] $greenTables */
            $greenTables = $this->getGreenTables();

            /** @var DbStructureTable $blueTable */
            foreach ($blueDbStructure->getDbStructureTables()->all() as $blueTable) {
                $isTableExistInGreenStructure = false;
                foreach ($greenTables as $greenTable) {
                    if ($greenTable->table_name ===  $blueTable->table_name
                        && $greenTable->database_type ===  $blueTable->database_type
                        && $greenTable->version === $blueTable->version
                    ) {
                        $isTableExistInGreenStructure = true;
                        break;
                    }
                }

                if (!$isTableExistInGreenStructure) {
                    $tableNameVersioned = $this
                        ->tableNameGenerator
                        ->generateByKnownVersion($blueTable->table_name, $blueTable->version);
                    $dbChangesApplier = $this
                        ->dbChangesApplierFactory
                        ->getDbChangesApplier($blueTable->database_type);
                    $dbChangesApplier->dropTable($tableNameVersioned);
                    $this->info(sprintf(
                        "Removed table %s in database %s of customer %d",
                        $tableNameVersioned,
                        $blueTable->database_type,
                        $this->dbManager->getCustomerId()
                    ));
                }

                $blueTable->delete();
            }
            $blueDbStructure->delete();
            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->info("Roll back due to exception");
            throw $e;
        }
    }

    /**
     * First initialization, creates green structure based on current database state.
     *
     * @param array $changedTables
     * @return void
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    protected function initIfNeed(array $changedTables): bool
    {
        $transaction = DbStructure::getDb()->beginTransaction();

        try {
            $greenTables = $this->getGreenTables($changedTables);

            if (!empty($greenTables)) {
                $transaction->commit();
                return false;
            }

            $dbStructure = DbStructure::find()
                ->where([
                    'AND',
                    ['=', 'customer_id', $this->dbManager->getCustomerId()],
                    ['=', 'type', DbStructure::TYPE_GREEN]
                ])
                ->one()
            ;

            if (empty($dbStructure)) {
                $dbStructure = new DbStructure();
                $dbStructure->description = 'Initial';
                $dbStructure->customer_id = $this->dbManager->getCustomerId();
                $dbStructure->type = DbStructure::TYPE_GREEN;
            }

            $dbStructure->save(false);

            foreach ($changedTables as $tableName) {
                $databaseType = DbStructure::getDatabaseTypeByTableName($tableName);
                $blueTable = new DbStructureTable();
                $blueTable->db_structure_id = $dbStructure->id;
                $blueTable->table_name = $tableName;
                $blueTable->database_type = $databaseType;
                $blueTable->version = 0;
                $blueTable->save(false);
            }
            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return true;
    }

    /**
     * Returns all tables which belong to green structure.
     *
     * @return array
     */
    protected function getGreenTables(array $tableNames = null): array
    {
        $greenTables = DbStructureTable::find()
            ->leftJoin(DbStructure::tableName() . ' dbs', 'dbs.id = db_structure_id')
            ->where([
                'AND',
                ['=', 'dbs.customer_id', $this->dbManager->getCustomerId()],
                ['=', 'dbs.type', DbStructure::TYPE_GREEN]
            ]);

        if (!empty($tableNames)) {
            $greenTables->andWhere(['in', 'table_name', $tableNames]);
        }

        return $greenTables->all() ?? [];
    }
}