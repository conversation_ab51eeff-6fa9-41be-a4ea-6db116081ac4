<?php

namespace common\components\dbStructure\dbChangesApplier;

use common\components\core\db\dbManager\DbManager;
use yii\db\Connection;

abstract class AbstractApplier implements DbChangesApplierInterface
{
    protected DbManager $dbManager;

    abstract protected function getDb(): Connection;

    public function __construct(DbManager $dbManager)
    {
        $this->dbManager = $dbManager;
    }
}