<?php

namespace common\components\dbStructure\dbChangesApplier;

use common\components\core\db\dbManager\DbManager;
use yii\db\Connection;

abstract class AbstractClickhouseApplier extends AbstractApplier
{
    protected string $clusterName;

    public function __construct(DbManager $dbManager, string $clusterName)
    {
        $this->dbManager = $dbManager;
        $this->clusterName = $clusterName;
        parent::__construct($dbManager);
    }

    public function createTable(string $tableName, string $exampleTableName): void
    {
        $statement = $this->getDb()->createCommand("SHOW CREATE TABLE $exampleTableName")->queryAll();
        $createTableRawQuery = $statement[0]['statement'];
        $createTableRawQuery = str_replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS", $createTableRawQuery);

        if (YII_ENV !== 'local') {
            $createTableRawQuery = str_replace($exampleTableName . "\n", "{$exampleTableName} ON CLUSTER '{$this->clusterName}'\n", $createTableRawQuery);
        }

        $createTableRawQuery = str_replace($exampleTableName, $tableName, $createTableRawQuery);

        if (false !== strpos($createTableRawQuery, 'Distributed')) {
            $sourceTableNameOld = str_replace('_dist', '', $exampleTableName);
            $sourceTableNameNew = str_replace('_dist', '', $tableName);

            $createTableRawQuery = str_replace(
                "'{$sourceTableNameOld}'",
                "'{$sourceTableNameNew}'",
                $createTableRawQuery
            );
        }

        $this->getDb()->createCommand($createTableRawQuery)->execute();
    }

    public function dropTable(string $tableName): void
    {
        $clusterPart = "ON CLUSTER '{$this->clusterName}'";
        if (YII_ENV === 'local') {
            $clusterPart = '';
        }

        $this
            ->getDb()
            ->createCommand("
                DROP TABLE IF EXISTS {$tableName} {$clusterPart} NO DELAY
           ")->execute();
    }
}