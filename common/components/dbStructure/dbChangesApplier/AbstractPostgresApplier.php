<?php

namespace common\components\dbStructure\dbChangesApplier;

abstract class AbstractPostgresApplier extends AbstractApplier
{
    public function createTable(string $tableName, string $exampleTableName): void
    {
        $this
            ->getDb()
            ->createCommand("
                CREATE TABLE {$tableName} IF NOT EXISTS (LIKE {$exampleTableName} INCLUDING ALL)
            ")->execute();
    }

    public function dropTable(string $tableName): void
    {
        $this
            ->getDb()
            ->createCommand("
                DROP TABLE IF EXISTS {$tableName}
           ")->execute();
    }
}