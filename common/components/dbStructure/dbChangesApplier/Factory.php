<?php

namespace common\components\dbStructure\dbChangesApplier;

use common\components\core\db\dbManager\DbManager;
use common\models\DbStructure;

class Factory
{
    private DbManager $dbManager;

    public function __construct(DbManager $dbManager)
    {
        $this->dbManager = $dbManager;
    }

    public function getDbChangesApplier(string $databaseType)
    {
        if ($databaseType === DbStructure::DATABASE_CLICKHOUSE_CUSTOMER) {
            $clusterName = getenv('CLICKHOUSE_CLUSTER_NAME');
            return new ClickhouseCustomerApplier($this->dbManager, $clusterName);
        }

        if ($databaseType === DbStructure::DATABASE_POSTGRES_CUSTOMER) {
            return new PostgresCustomerApplier($this->dbManager);
        }

        throw new \Exception("Unable to determine db changes applier for database type {$databaseType}");
    }
}