<?php

namespace common\components\dbStructure;

use common\components\core\db\dbManager\DbManager;
use common\models\DbStructure;
use common\models\DbStructureTable;
use yii\db\Query;

class TableNameGenerator
{
    protected DbManager $dbManager;

    public function __construct(DbManager $dbManager)
    {
        $this->dbManager = $dbManager;
    }

    public function generateByKnownVersion(string $tableBaseName, ?int $tableVersion, bool $isIncludeDbName = true): string
    {
        $dbName = $isIncludeDbName
            ? $this->dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER) . '.'
            : '';

        if (empty($tableVersion)) {
            return $dbName . $tableBaseName;
        }

        return "{$dbName}{$tableBaseName}_v{$tableVersion}";
    }

    public function generate(string $tableBaseName, bool $isNextVersion = false, bool $isIncludeDbName = true): string
    {
        $tableHasBeenFound = false;
        $databaseType = null;

        foreach (DbStructure::SUPPORTED_DATABASE_TYPES as $databaseType => $tableNames) {
            if (!in_array($tableBaseName, $tableNames)) {
                continue;
            }
            $tableHasBeenFound = true;
            break;
        }

        if (!$tableHasBeenFound) {
            throw new \Exception("Unable to find table {$tableBaseName} under blue green structure supporting");
        }

        $tableVersion = (new Query())
            ->select('dbst.version')
            ->from(DbStructureTable::tableName() . ' dbst')
            ->leftJoin(DbStructure::tableName() . ' dbs', 'dbs.id = dbst.db_structure_id')
            ->where([
                'dbs.type' => $isNextVersion ? DbStructure::TYPE_GREEN : $this->dbManager->getBlueGreenType(),
                'dbs.customer_id' => $this->dbManager->getCustomerId(),
                'dbst.table_name' => $tableBaseName,
                'dbst.database_type' => $databaseType
            ])
            ->noCache()
            ->scalar();

        $tableVersion = $tableVersion ?: 0;

        if ($isNextVersion) {
            $tableVersion++;
        }

        $dbName = $isIncludeDbName
            ? $this->dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER) . '.'
            : '';

        if (empty($tableVersion)) {
            return "{$dbName}{$tableBaseName}";
        }

        return "{$dbName}{$tableBaseName}_v{$tableVersion}";
    }
}