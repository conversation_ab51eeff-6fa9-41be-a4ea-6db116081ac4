<?php

namespace common\components\tokenService\tokenProvider;

use common\components\tokenService\TokenAwareActiveRecordInterface;
use common\models\Seller;
use Psr\Http\Message\ResponseInterface;
use SellerLogic\InternalApi\TokenServiceApiClient;

class Selling<PERSON><PERSON>TokenProvider implements TokenProviderInterface
{
    public function getToken(TokenServiceApiClient $tokenServiceApiClient, string $entityId): ResponseInterface
    {
        return $tokenServiceApiClient->amazonApi()->view($entityId);
    }

    public function getTokenAwareEntity(string $entityId): ?TokenAwareActiveRecordInterface
    {
        return Seller::find()->where(['id' => $entityId])->one(\Yii::$app->db);
    }
}
