<?php

namespace common\components\tokenService\tokenProvider;

use common\components\tokenService\TokenAwareActiveRecordInterface;
use common\models\ads\AmazonAdsAccount;
use Psr\Http\Message\ResponseInterface;
use SellerLogic\InternalApi\TokenServiceApiClient;

class AmazonAdsApiTokenProvider implements TokenProviderInterface
{
    public function getToken(TokenServiceApiClient $tokenServiceApiClient, string $entityId): ResponseInterface
    {
        return $tokenServiceApiClient->amazonAdsApi()->view($entityId);
    }

    public function getTokenAwareEntity(string $entityId): ?TokenAwareActiveRecordInterface
    {
        return AmazonAdsAccount::find()->where(['id' => $entityId])->noCache()->one();
    }
}