<?php

namespace common\components\tokenService\tokenProvider;

class TokenProviderFactory
{
    public const TYPE_AMAZON_SELLING_API = 'amazon_selling_api';
    public const TYPE_AMAZON_ADS_API = 'amazon_ads_api';

    public function getProvider(string $tokenProviderType): TokenProviderInterface
    {
        if ($tokenProviderType === self::TYPE_AMAZON_SELLING_API) {
            return new SellingApiTokenProvider();
        } elseif ($tokenProviderType === self::TYPE_AMAZON_ADS_API) {
            return new AmazonAdsApiTokenProvider();
        }

        throw new \Exception("Unable to find token provider for type $tokenProviderType");
    }
}