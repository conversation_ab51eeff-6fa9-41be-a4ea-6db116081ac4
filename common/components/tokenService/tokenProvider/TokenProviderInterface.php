<?php

namespace common\components\tokenService\tokenProvider;

use common\components\tokenService\TokenAwareActiveRecordInterface;
use Psr\Http\Message\ResponseInterface;
use SellerLogic\InternalApi\TokenServiceApiClient;

interface TokenProviderInterface
{
    public function getToken(TokenServiceApiClient $tokenServiceApiClient, string $entityId): ResponseInterface;
    public function getTokenAwareEntity(string $entityId): ?TokenAwareActiveRecordInterface;
}