<?php

namespace common\components\tokenService;

use common\components\LogToConsoleTrait;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\components\tokenService\Exception\NoTokenEntity;
use common\components\tokenService\tokenProvider\TokenProviderFactory;
use SellerLogic\InternalApi\Message\ResponseTransformer;
use SellerLogic\InternalApi\TokenServiceApiClient;
use yii\caching\CacheInterface;
use yii\db\Exception;

class TokenService
{
    use LogToConsoleTrait;

    public string $cacheComponentName;
    public string $baseApiUrl;

    protected TokenServiceApiClient $client;

    protected TokenProviderFactory $tokenProviderFactory;

    public function __construct()
    {
        $this->tokenProviderFactory = new TokenProviderFactory();
    }

    /**
     * Retrieves and returns authorisation token from token service API.
     *
     * @param string $sellerId
     * @param bool $isIgnoreCache
     * @return Token
     * @throws \Throwable
     */
    public function getToken(
        string $entityId,
        bool $isIgnoreCache = false,
        string $apiType = TokenProviderFactory::TYPE_AMAZON_SELLING_API
    ): Token
    {
        $tokenProvider = $this->tokenProviderFactory->getProvider($apiType);
        $tokenEntity = $tokenProvider->getTokenAwareEntity($entityId);

        if (empty($tokenEntity)) {
            throw new NoTokenEntity("Token entity not found for id $entityId, api type $apiType");
        }

        $token = null;

        if (!$isIgnoreCache) {
            /** @var Token $token */
            $token = $this->getCache()->get($this->getTokenCacheKey($entityId));
        }

        if ($token instanceof Token) {
            return $token;
        }

        try {
            $response = $tokenProvider->getToken($this->getApiClient(), $entityId);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception("Response status is not 200");
            }

            $data = (new ResponseTransformer())->transform($response);

            if (empty($data['access_token'])) {
                $tokenEntity->setIsTokenReceived(false);
                $tokenEntity->saveOrThrowException();
                throw new NoAccessTokenException();
            }

            $token = new Token();
            $token->refreshToken = $data['refresh_token'] ?? null;
            $token->accessToken = $data['access_token'] ?? null;
            $token->expiresAt = new \DateTime($data['expires_at']);

            $cacheTime = $token->expiresAt->getTimestamp() - time() - 60;
            if ($cacheTime <= 0) {
                try {
                    $this->throwAlmostExpiredTokenError($apiType, $entityId, $token->expiresAt);
                } catch (\Throwable $e) {
                    $this->error($e);
                }
                $cacheTime = 5;
            }

            $this->getCache()->set($this->getTokenCacheKey($entityId), $token, $cacheTime);

            $tokenEntity->setIsTokenReceived(true);
            return $token;
        } catch (\Throwable $e) {
            if (false !== strpos($e->getMessage(), 'Not Found')) {
                $tokenEntity->setIsTokenReceived(false);
                $tokenEntity->saveOrThrowException();
                throw new NoAccessTokenException();
            }

            $this->error($e);
            throw $e;
        }
    }

    /**
     * Returns cache key used for token caching for seller.
     *
     * @param string $sellerId
     * @return string
     */
    private function getTokenCacheKey(string $sellerId)
    {
        return 'token_service_api_token_' . $sellerId;
    }

    /**
     * Returns cache component responsible for token caching.
     *
     * @return CacheInterface
     */
    private function getCache(): CacheInterface
    {
        return \Yii::$app->{$this->cacheComponentName};
    }

    /**
     * Creates and returns an instance of token service API client.
     *
     * @return TokenServiceApiClient
     */
    private function getApiClient(): TokenServiceApiClient
    {
        static $apiClient = null;

        if  (null !== $apiClient) {
            return $apiClient;
        }

        $apiClient = new TokenServiceApiClient([
            'baseApiUri' => $this->baseApiUrl
        ]);

        return $apiClient;
    }

    /**
     * Need only for rollbar debug
     *
     * @param string $apiType
     * @param string $entityId
     * @param \DateTime $expiresAt
     * @return void
     * @throws Exception
     */
    private function throwAlmostExpiredTokenError(string $apiType, string $entityId, \DateTime $expiresAt): void
    {
        throw new Exception("Received almost expired access token");
    }
}
