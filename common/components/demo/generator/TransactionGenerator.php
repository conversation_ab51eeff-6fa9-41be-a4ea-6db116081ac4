<?php

namespace common\components\demo\generator;

use common\components\COGSync\COGChangesManager;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\LogToConsoleTrait;
use common\models\AmazonMarketplace;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use SellingPartnerApi\Model\OrdersV0\Order;

class TransactionGenerator
{
    use LogToConsoleTrait;

    protected DbManager $dbManager;
    protected CurrencyRateManager $currencyRateManager;
    protected COGChangesManager $COGChangesManager;
    protected CustomerComponent $customerComponent;

    /**
     * path||appearingPct||amountPct
     */
    protected const PRINCIPAL_TRANSACTIONS = [
        "Shipment.ShipmentItem.ItemCharge.Principal.ChargeAmount_PLUS||100||100",
        "Shipment.ShipmentItem.ItemFee.Commission.FeeAmount_MINUS||47.391||-18.565",
        "Shipment.ShipmentItem.ItemCharge.Tax.ChargeAmount_PLUS||46.824||28.941",
        "Adjustment.CLI_PLANNED_FEE_REIMBURSEMENT.AdjustmentAmount_PLUS||5.3||0.5",
        "Shipment.ShipmentItem.ItemFee.FBAPerUnitFulfillmentFee.FeeAmount_MINUS||21.164||-5.936",
        "Shipment.ShipmentItem.ItemCharge.ShippingCharge.ChargeAmount_PLUS||4.277||0.908",
        "Shipment.ShipmentItem.ItemCharge.ShippingTax.ChargeAmount_PLUS||4.173||0.166",
        "Shipment.ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount_MINUS||3.463||-0.721",
        "Shipment.ShipmentItem.ItemFee.ShippingChargeback.FeeAmount_MINUS||0.459||-0.138",
        "Shipment.ShipmentItem.ItemFee.ShippingHB.FeeAmount_MINUS||0.365||-0.029",
        "TrialShipment.Fee.FBATryBeforeYouBuyMultitierPerUnitFee.FeeAmount_MINUS||0.149||-0.056",
        "Shipment.ShipmentItem.ItemCharge.GiftWrap.ChargeAmount_PLUS||0.017||0.003",
        "Shipment.ShipmentItem.ItemCharge.GiftWrapTax.ChargeAmount_PLUS||0.017||0.001",
        "Shipment.ShipmentItem.ItemFee.GiftwrapChargeback.FeeAmount_MINUS||0.017||-0.003",
        "DebtRecovery.DebtRecoveryItem.OriginalAmount_MINUS||0.5||-0.5",
        "GuaranteeClaim.ShipmentItemAdjustment.ItemFeeAdjustment.RefundCommission.FeeAmount_MINUS||2||-0.6"
    ];

    protected const REFUND_TRANSACTIONS = [
        "Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount_MINUS||100.007||-99.996",
        "Refund.ShipmentItemAdjustment.ItemFeeAdjustment.Commission.FeeAmount_PLUS||51.609||24.276",
        "Refund.ShipmentItemAdjustment.ItemFeeAdjustment.RefundCommission.FeeAmount_MINUS||51.416||-4.838",
        "Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount_MINUS||51.365||-18.766",
        "Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingCharge.ChargeAmount_MINUS||3.432||-0.463",
        "Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount_MINUS||3.385||-0.086",
        "Refund.ShipmentItemAdjustment.PromotionAdjustment.PromotionMetaDataDefinitionValue.PromotionAmount_PLUS||3.185||0.494",
        "Refund.ShipmentItemAdjustment.ItemFeeAdjustment.ShippingChargeback.FeeAmount_PLUS||0.233||0.051",
        "Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Goodwill.ChargeAmount_MINUS||0.063||-0.035",
    ];

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->currencyRateManager = new CurrencyRateManager();
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->COGChangesManager = \Yii::$container->get("COGChangesManager");
    }

    public function generate(array $orderItems, int $eventPeriodId): array
    {
        $transactions = [];

        foreach ($orderItems as $orderItem) {
            if ($orderItem['order_status'] !== Order::ORDER_STATUS_SHIPPED) {
                continue;
            }

            $transactions = array_merge(
                $transactions,
                $this->generateTransactionsByRules(
                    $orderItem,
                    $eventPeriodId,
                    self::PRINCIPAL_TRANSACTIONS
                )
            );

            if (random_int(0, 100) < 5) {
                $transactions = array_merge(
                    $transactions,
                    $this->generateTransactionsByRules(
                        $orderItem,
                        $eventPeriodId,
                        self::REFUND_TRANSACTIONS
                    )
                );
            }
            $this->info("Generated " . count($transactions) . ' transactions');
        }

        return $transactions;
    }

    protected function generateTransactionsByRules(array $orderItem, int $eventPeriodId, array $rules): array
    {
        $transactions = [];
        $transactionPrototype = $this->getTransactionPrototype($orderItem, $eventPeriodId);
        $amount = $orderItem['item_price'];
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        foreach ($rules as $rule) {
            list($financeEventCategoryPath, $appearingPct, $amountPct) = explode('||', $rule);

            if (random_int(0, 100000) > $appearingPct * 1000) {
                continue;
            }

            $transaction = clone $transactionPrototype;
            $transaction->Amount = (int)($amount * $amountPct * 0.01 * $moneyAccuracy);
            $transaction->AmountEUR = $this->currencyRateManager->toBaseCurrency(
                $transaction->Amount,
                $transaction->Currency,
                new \DateTime($transaction->PostedDate)
            );
            $transaction->CategoryId = FinanceEventCategory::find()
                ->select('id')
                ->where(['=', 'path', $financeEventCategoryPath])
                ->cache(60 * 5)
                ->scalar();
            $transactions[] = $transaction;

            if (false !== strpos($financeEventCategoryPath, '.Principal.')) {
                /** @var Transaction[] $COGTransactions */
                $COGTransactions = $this
                    ->COGChangesManager
                    ->generateNewForSalesTransaction($transaction);

                if (false !== strpos($financeEventCategoryPath, 'Refund.ShipmentItemAdjustment.')) {
                    foreach ($COGTransactions as $transaction) {
                        $transaction->Amount *= -1;
                        $transaction->AmountEUR *= -1;
                    }
                }

                $transactions = array_merge($transactions, $COGTransactions);
            }
        }

        return $transactions;
    }

    protected function getPrincipalTransactions(array $orderItem, int $eventPeriodId): array
    {
        $transactionPrototype = $this->getTransactionPrototype($orderItem, $eventPeriodId);
        $transactions = [];

        $transaction = clone $transactionPrototype;
        $this->fillTransaction(
            $transaction,
            'Shipment.ShipmentItem.ItemCharge.Principal.ChargeAmount_PLUS',
            $orderItem['item_price']
        );
        $transactions[] = $transaction;

        return $transactions;
    }

    protected function fillTransaction(Transaction $transaction, string $categoryPath, float $amount): void
    {
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();
        $transaction->Amount = (int)($amount * $moneyAccuracy);
        $transaction->AmountEUR = $this->currencyRateManager->toBaseCurrency(
            $transaction->Amount,
            $transaction->Currency,
            new \DateTime($transaction->PostedDate)
        );
        $transaction->CategoryId = FinanceEventCategory::find()
            ->select('id')
            ->where(['=', 'path', $categoryPath])
            ->cache(60 * 5)
            ->scalar()
        ;
    }

    protected function getTransactionPrototype(array $orderItem, int $eventPeriodId): Transaction
    {
        $currencyCode = AmazonMarketplace::find()->where([
            '=', 'id', $orderItem['order_marketplace_id']
        ])->select('currency_code')->cache(60 * 5)->scalar();
        $transactionDate = (new \DateTime($orderItem['order_purchase_date']))
            ->modify('-' . random_int(0, 5) . ' days')
            ->format('Y-m-d H:i:s')
        ;

        $transaction = new Transaction();
        $transaction->AmazonOrderId = $orderItem['order_id'];
        $transaction->SellerOrderId = $orderItem['order_id'];
        $transaction->EventPeriodId = $eventPeriodId;
        $transaction->SellerSKU = $orderItem['sku'];
        $transaction->Quantity = $orderItem['quantity'];
        $transaction->SellerId = $this->dbManager->getSellerId();
        $transaction->Currency = $currencyCode;
        $transaction->MarketplaceId = $orderItem['order_marketplace_id'];
        $transaction->TransactionDate = $transactionDate;
        $transaction->PostedDate = $orderItem['order_purchase_date'];

        return $transaction;
    }
}