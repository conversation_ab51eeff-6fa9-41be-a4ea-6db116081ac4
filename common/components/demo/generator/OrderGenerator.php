<?php

namespace common\components\demo\generator;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\order\AmazonOrder;
use common\models\Seller;
use SellingPartnerApi\Model\OrdersV0\Order;

class OrderGenerator
{
    use LogToConsoleTrait;

    protected const MAX_ORDERS_PER_PERIOD = 3;
    protected const MIN_ORDERS_PER_PERIOD = 0;

    protected const HOUR_COEFFICIENTS = [
        '00' => 0.3,
        '01' => 0.4,
        '02' => 0.5,
        '03' => 0.5,
        '04' => 0.6,
        '05' => 0.7,
        '06' => 0.7,
        '07' => 0.8,
        '08' => 0.8,
        '09' => 0.9,
        '10' => 0.9,
        '11' => 0.9,
        '12' => 0.9,
        '13' => 1,
        '14' => 1,
        '15' => 1,
        '16' => 0.9,
        '17' => 0.8,
        '18' => 0.7,
        '19' => 0.5,
        '20' => 0.4,
        '21' => 0.4,
        '22' => 0.4,
        '23' => 0.3,
        '24' => 0.3,
    ];

    protected const DAY_OF_WEEK_COEFFICIENTS = [
        '0' => 0.85, // Sunday
        '1' => 0.9,
        '2' => 1,
        '3' => 1,
        '4' => 0.9,
        '5' => 0.9,
        '6' => 0.85
    ];

    protected DbManager $dbManager;
    protected ProductGenerator $productGenerator;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->productGenerator = new ProductGenerator();
    }

    /**
     * @param \DateTime $dateFrom
     * @param \DateTime $dateTo
     * @param string $sellerId
     * @return array
     */
    public function generate(\DateTime $dateFrom, \DateTime $dateTo, string $sellerId): array
    {
        $this->info("Generating orders started");
        $interval = \DateInterval::createFromDateString('20 minutes');

        /** @var \DateTime[] $datePeriod */
        $datePeriod = new \DatePeriod($dateFrom, $interval, $dateTo);
        $orders = [];

        $sellerCreatedAt = Seller::find()->where(['id' => $sellerId])->select('created_at')->scalar();
        $sellerCreatedAt = (new \DateTime($sellerCreatedAt));

        foreach ($datePeriod as $dateTime) {
            $this->info("Generating data for period " . $dateTime->format('Y-m-d H:i:s'));
            $ordersCount = random_int(self::MIN_ORDERS_PER_PERIOD, self::MAX_ORDERS_PER_PERIOD);
            $hourCoefficient = self::HOUR_COEFFICIENTS[$dateTime->format('H')];
            $dayCoefficient = self::DAY_OF_WEEK_COEFFICIENTS[$dateTime->format('w')];
            $monthCoefficient = $this->getMonthCoefficient($dateTime, $sellerCreatedAt);
            $randomCoefficient = $this->getRandomCoefficient();

            $ordersCount = $ordersCount * $hourCoefficient * $dayCoefficient * $monthCoefficient * $randomCoefficient;
            $ordersCount = (int)$ordersCount;
            $this->info("Orders count $ordersCount");

            if ($ordersCount < 1) {
                continue;
            }

            for ($i = 0; $i < $ordersCount; $i++) {
                $orders[] = $this->generateOrder($dateTime, $sellerId);
            }
        }

        $this->info("Count generated orders: " . count($orders));
        $this->info("Generating orders finished");

        return $orders;
    }

    protected function generateOrder(\DateTime $orderDate, string $sellerId): array
    {
        $marketplaceIds = $this->getMarketplaceIds();

        return [
            'seller_id' => $sellerId,
            'marketplace_id' => $marketplaceIds[array_rand($marketplaceIds)],
            'amazon_order_id' => $this->generateOrderId(),
            'purchase_date' => $orderDate->format('Y-m-d H:i:s'),
            'last_update_date' => date('Y-m-d H:i:s'),
            'order_status' => $this->getOrderStatus(),
            'fulfillment_channel' => $this->getFulfillmentChannel(),
            'is_business_order' => $this->getIsBusinessOrder(),
            'items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_FINISHED,
            'created_at' => $orderDate->format('Y-m-d H:i:s'),
            'updated_at' =>  $orderDate->format('Y-m-d H:i:s')
        ];
    }

    public function getMarketplaceIds(): array
    {
        static $marketplaceIds = [];

        if (!empty($marketplaceIds)) {
            return $marketplaceIds;
        }

        $products = $this->productGenerator->generate();
        foreach ($products as $product) {
            $marketplaceIds[] = $product['marketplace_id'];
        }

        return array_unique($marketplaceIds);
    }

    protected function getOrderStatus(): string
    {
        $randomInt = random_int(0, 100);

        if ($randomInt >= 0 && $randomInt < 10) {
            return Order::ORDER_STATUS_CANCELED;
        }

        if ($randomInt >= 10 && $randomInt < 15) {
            return Order::ORDER_STATUS_UNSHIPPED;
        }

        return Order::ORDER_STATUS_SHIPPED;
    }

    protected function getFulfillmentChannel(): ?string
    {
        $channels = [
            AmazonOrder::FULFILMENT_CHANNEL_MFN,
            AmazonOrder::FULFILMENT_CHANNEL_AFN,
            null
        ];

        return $channels[array_rand($channels)];
    }

    protected function getIsBusinessOrder(): bool
    {
        return random_int(0, 1) === 0;
    }

    protected function generateOrderId(): string
    {
        return implode('-', [
            str_pad(random_int(1, 999), 3, '0', STR_PAD_LEFT),
            str_pad(random_int(1, 9999999), 7, '0', STR_PAD_LEFT),
            str_pad(random_int(1, 9999999), 7, '0', STR_PAD_LEFT),
        ]);
    }

    protected function getRandomCoefficient(): float
    {
        return random_int(90, 100) * 0.01;
    }

    protected function getMonthCoefficient(\DateTime $dateTime, \DateTime $sellerCreatedAt): float
    {
        // Date when we start to grow demo account
        $growStartDate = (clone $sellerCreatedAt)->modify('-12 months');
        $diff = $growStartDate->diff($dateTime);

        if ($dateTime < $growStartDate) {
            return 0.1;
        }

        $daysLeft = $diff->days;
        $weeksLeft = $daysLeft / 7;

        return $weeksLeft * 0.05;
    }
}
