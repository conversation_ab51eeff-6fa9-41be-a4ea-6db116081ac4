<?php

namespace common\components\demo\generator;

use common\components\core\db\dbManager\DbManager;

class RepricerEventGenerator
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function generate(
        int $count = 300,
        ?\DateTime $fromDateTime = null
    ): array
    {
        $repricerEvents = [];
        $offerTypes = ['B2B', 'B2C'];
        $toDateTime = new \DateTime();
        if (!$fromDateTime) {
            $fromDateTime = (new \DateTime())->modify('-30 days');
        }

        $products = (new ProductGenerator())->generate();

        for ($i = 0; $i < $count; $i++) {
            $randomProduct = $products[array_rand($products)];
            $randomTimestamp = mt_rand($fromDateTime->getTimestamp(), $toDateTime->getTimestamp());
            $randomDay = (new \DateTime())->setTimestamp($randomTimestamp)->format('Y-m-d');
            $offerType = $offerTypes[array_rand($offerTypes)];
            $eventKey = join('_', [$randomDay , $randomProduct['id'] , $offerType]);
            if (isset($repricerEvents[$eventKey])) {
                $repricerEvents[$eventKey]['amount'] += 1;
            } else {
                $repricerEvents[$eventKey] = [
                    'day'             => $randomDay,
                    'product_id'      => $randomProduct['id'],
                    'sku'             => $randomProduct['sku'],
                    'marketplace_id'  => $randomProduct['marketplace_id'],
                    'seller_id'       => $randomProduct['seller_id'],
                    'amount'          => 1,
                    'offer_type'      => $offerType
                ];
            }

        }

        return array_values($repricerEvents);
    }

}
