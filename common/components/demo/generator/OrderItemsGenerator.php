<?php

namespace common\components\demo\generator;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\Product;

class OrderItemsGenerator
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function generate(array $amazonOrders): array
    {
        $amazonOrderItems = [];

        foreach ($amazonOrders as $amazonOrder) {
            $countProducts = $this->getQuantity();

            for ($i = 0; $i < $countProducts; $i++) {
                $product = $this->getProduct($amazonOrder['marketplace_id']);
                if (empty($product)) {
                    continue 2;
                }

                $quantity = $this->getQuantity();
                $conditionStr = Product::CONDITIONS_MAP[$product['condition']];
                $promotionDiscount = random_int(0, 100) > 90 ? $this->getItemPrice() * 0.05 : 0;
                $itemPrice = $product['buying_price'] ?: $this->getItemPrice();
                $itemPrice += $itemPrice * 0.45;
                $itemPrice *= $quantity;
                $itemPrice = round_half_even($itemPrice / $quantity, 2) * $quantity;

                $amazonOrderItem = [
                    'order_id' => $amazonOrder['amazon_order_id'],
                    'order_item_id' => time() . random_int(0, 9999999),
                    'order_status' => $amazonOrder['order_status'],
                    'asin' => $product['asin'],
                    'sku' => $product['sku'],
                    'title' => substr($product['title'], 0, 200),
                    'quantity' => $quantity,
                    'quantity_shipped' => $quantity,
                    'promotion_discount' => $promotionDiscount,
                    'item_price' => $itemPrice,
                    'condition_id' => trim(explode(';', $conditionStr)[0]),
                    'condition_subtype_id' => trim(explode(';', $conditionStr)[1] ?? explode(';', $conditionStr)[0]),
                    'order_purchase_date' => $amazonOrder['purchase_date'],
                    'order_marketplace_id' => $amazonOrder['marketplace_id'],
                    'date' => $amazonOrder['purchase_date'],
                ];
                $amazonOrderItems[] = $amazonOrderItem;
            }
        }

        return $amazonOrderItems;
    }

    protected function getQuantity(): int
    {
        $randomInt = random_int(1, 100);

        if ($randomInt <= 80) {
            return 1;
        }

        return random_int(2, 3);
    }

    protected function getItemPrice(): float
    {
        return random_int(10, 200) . '.' . random_int(0, 99);
    }

    protected function getProduct(string $marketplaceId): array
    {
        $allProducts = Product::find()
            ->cache(10)
            ->where([
                'marketplace_id' => $marketplaceId,
                'seller_id' => $this->dbManager->getSellerId()
            ])
            ->limit(100)
            ->asArray()
            ->all();

        if (empty($allProducts)) {
            return [];
        }

        return $allProducts[array_rand($allProducts)];
    }
}
