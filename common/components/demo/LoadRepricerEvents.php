<?php

namespace common\components\demo;

use common\components\core\db\dbManager\DbManager;
use common\components\demo\generator\RepricerEventGenerator;
use common\models\customer\RepricerEventBuffer;

class LoadRepricerEvents
{
    protected DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function loadInit(): void
    {
        $this->loadData('- 2 months', 600);
    }

    /**
     * @param string $modify
     * @param int $eventsCount
     * @return void
     * @throws \DateMalformedStringException
     * @throws \yii\db\Exception
     */
    public function loadData(string $modify, int $eventsCount): void
    {
        $fromDateTime = (new \DateTime())->modify($modify);
        $repricerEvents = (new RepricerEventGenerator())->generate($eventsCount, $fromDateTime);
        if (!empty($repricerEvents)) {
            $tableName = RepricerEventBuffer::tableName();
            $insertSql = $this->dbManager->getRepricerEventDb()->createCommand()->batchInsert(
                $tableName,
                array_keys($repricerEvents[0]),
                $repricerEvents
            )->getRawSql();
            $this->dbManager->getRepricerEventDb()->createCommand(
                $insertSql . " ON CONFLICT (day, product_id, offer_type) DO NOTHING"
            )->execute();
        }
    }
}
