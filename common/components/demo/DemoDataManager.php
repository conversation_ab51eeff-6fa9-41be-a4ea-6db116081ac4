<?php

namespace common\components\demo;

use common\components\COGSync\ProductsSaver;
use common\components\core\db\dbManager\DbManager;
use common\components\dataBuffer\BufferFactory;
use common\components\demo\generator\OrderGenerator;
use common\components\demo\generator\OrderItemsGenerator;
use common\components\demo\generator\TransactionGenerator;
use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use common\components\services\order\TransferOrderService;
use common\models\Command;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;

class DemoDataManager
{
    use LogToConsoleTrait;

    protected OrderGenerator $orderGenerator;
    protected OrderItemsGenerator $orderItemsGenerator;
    protected TransactionGenerator $transactionGenerator;
    protected DbManager $dbManager;
    protected BufferFactory $bufferFactory;
    protected ProductsSaver $productsSaver;

    public function __construct()
    {
        $this->orderGenerator = new OrderGenerator();
        $this->orderItemsGenerator = new OrderItemsGenerator();
        $this->transactionGenerator = new TransactionGenerator();
        $this->bufferFactory = new BufferFactory();
        $this->productsSaver = new ProductsSaver();

        $this->dbManager = \Yii::$app->dbManager;
    }

    public function generateOrdersAndTransactions(
        \DateTime $dateFrom,
        \DateTime $dateTo,
        string $sellerId,
        int $eventPeriodId
    ): void
    {
        $this->dbManager->setSellerId($sellerId);
        $this->info("Generating orders started");
        $amazonOrders = $this->orderGenerator->generate($dateFrom, $dateTo, $sellerId);
        $this->info("Generating orders finished");
        $this->info("Count orders " . count($amazonOrders));

        if (count($amazonOrders) === 0) {
            return;
        }

        $this->info("Generating order items started");
        $orderItems = $this->orderItemsGenerator->generate($amazonOrders);
        $this->info("Generating order items finished");

        if (count($orderItems) === 0) {
            return;
        }

        $this->info("Generating transactions started");
        $transactions = $this->transactionGenerator->generate($orderItems, $eventPeriodId);
        $this->info("Generating transactions finished");
        $buffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
        $buffer->put($transactions);
        $buffer->flush();

        $customerId = $this->dbManager->getCustomerId();
        LinuxCommander::safeExecute(
            sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
            "transaction-buffer/force-flush $customerId",
            true
        );

        $this->info("Saving orders and items started");
        AmazonOrder::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrder::tableName(),
                array_keys(array_values($amazonOrders)[0]),
                $amazonOrders
            )
            ->execute();

        foreach ($orderItems as &$orderItem) {
            unset($orderItem['order_status']);
        }

        AmazonOrderItem::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrderItem::tableName(),
                array_keys(array_values($orderItems)[0]),
                $orderItems
            )
            ->execute()
        ;
        $this->info("Saving orders and items finished");

        $orderIds = [];

        foreach ($amazonOrders as $amazonOrder) {
            $orderIds[] = $amazonOrder['amazon_order_id'];
        }

        $this->info("Transfer orders started");
        (new TransferOrderService($this->dbManager->getCustomerId()))
            ->transferByIds(
                $this->dbManager->getSellerId(),
                $orderIds
            );
        $this->info("Transfer orders finished");
    }
}