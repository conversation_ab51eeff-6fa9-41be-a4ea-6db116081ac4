<?php

namespace common\components\demo;

use common\components\COGSync\COGSynchronizer;
use common\components\COGSync\ProductsSaver;
use common\components\core\db\dbManager\DbManager;
use common\components\dataBuffer\BufferFactory;
use common\components\demo\generator\OrderGenerator;
use common\components\demo\generator\OrderItemsGenerator;
use common\components\demo\generator\ProductGenerator;
use common\components\demo\generator\TransactionGenerator;
use common\components\LinuxCommander;
use common\components\LogToConsoleTrait;
use common\components\services\order\TransferOrderService;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;

class DemoDataManager
{
    use LogToConsoleTrait;

    protected OrderGenerator $orderGenerator;
    protected OrderItemsGenerator $orderItemsGenerator;
    protected TransactionGenerator $transactionGenerator;
    protected DbManager $dbManager;
    protected BufferFactory $bufferFactory;
    protected ProductsSaver $productsSaver;

    public function __construct()
    {
        $this->orderGenerator = new OrderGenerator();
        $this->orderItemsGenerator = new OrderItemsGenerator();
        $this->transactionGenerator = new TransactionGenerator();
        $this->bufferFactory = new BufferFactory();
        $this->productsSaver = new ProductsSaver();

        $this->dbManager = \Yii::$app->dbManager;
    }

    public function generateOrdersAndTransactions(
        \DateTime $dateFrom,
        \DateTime $dateTo,
        string $sellerId,
        int $eventPeriodId
    ): void
    {
        $this->dbManager->setSellerId($sellerId);
        $amazonOrders = $this->orderGenerator->generate($dateFrom, $dateTo, $sellerId);

        if (count($amazonOrders) === 0) {
            return;
        }

        $orderItems = $this->orderItemsGenerator->generate($amazonOrders);

        if (count($orderItems) === 0) {
            return;
        }

        $this->saveOrders($amazonOrders);
        $this->saveOrderItems($orderItems);
        $this->transferOrdersToClickhouse($amazonOrders);

        $transactions = $this->transactionGenerator->generate($orderItems, $eventPeriodId);
        $this->saveTransactions($transactions);
    }

    protected function saveOrders(array $amazonOrders): void
    {
        $this->info("Saving orders and items started");

        AmazonOrder::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrder::tableName(),
                array_keys(array_values($amazonOrders)[0]),
                $amazonOrders
            )
            ->execute();

        $this->info("Saving orders and items finished");
    }

    protected function saveOrderItems(array $amazonOrderItems): void
    {
        $this->info("Saving order items started");

        // This field is not in that table (but it uses in another places)
        foreach ($amazonOrderItems as $k => $amazonOrderItem) {
            unset($amazonOrderItems[$k]['order_status']);
        }

        AmazonOrderItem::getDb()
            ->createCommand()
            ->batchInsert(
                AmazonOrderItem::tableName(),
                array_keys(array_values($amazonOrderItems)[0]),
                $amazonOrderItems
            )
            ->execute();

        $this->info("Saving order items finished");
    }

    protected function transferOrdersToClickhouse(array $amazonOrders): void
    {
        $this->info("Transfer orders to clickhouse started");

        $orderIds = array_unique(array_column($amazonOrders, 'amazon_order_id'));

        // We need  to preserve and restore later sellerId
        // because seller can be changed during TransferOrderService execution
        $prevSellerId = $this->dbManager->getSellerId();

        (new TransferOrderService($this->dbManager->getCustomerId()))
            ->transferByIds(
                $this->dbManager->getSellerId(),
                $orderIds
            );
        $this->dbManager->setSellerId($prevSellerId);

        $this->info("Transfer orders to clickhouse finished");
    }

    protected function saveTransactions(array $transactions): void
    {
        $this->info('Saving transactions started');

        $buffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
        $buffer->put($transactions);
        $buffer->flush();

        LinuxCommander::safeExecute(
            sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
            "transaction-buffer/force-flush " . $this->dbManager->getCustomerId(),
            true
        );

        $this->info('Saving transactions finished');
    }
}
