<?php

namespace common\components\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\models\ads\AmazonAdsProfile;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\FinanceEventCategory;
use Exception;
use yii\db\Expression;
use yii\db\Query;

class TransactionQueryExecutor
{
    use ExtraFiltersTrait;

    public const FILTERS_PLACEHOLDER = '[FILTERS]';

    public const COLUMN_NAME_MAPPING = [
        'posted_date' => ['posted_date', 'PostedDate'],
        'transaction_date' => ['transaction_date', 'TransactionDate'],
        'cog_category_id' => ['cog_category_id', 'COGCategoryId'],
        'indirect_cost_type_id' => ['indirect_cost_type_id', 'IndirectCostTypeId'],
        'category_id' => ['category_id', 'CategoryId'],
        'amount' => ['amount', 'Amount'],
        'currency_code' => ['currency_code', 'Currency'],
        'amount_eur' => ['amount_eur', 'AmountEUR'],
        'amazon_order_id' => ['amazon_order_id', 'AmazonOrderId'],
        'order_id' => ['order_id', 'OrderId'],
        'seller_id' => ['seller_id', 'SellerId'],
        'marketplace_id' => ['marketplace_id', 'MarketplaceId'],
        'asin' => ['product_asin', 'ASIN'],
        'ean' => ['product_ean', 'EAN'],
        'upc' => ['product_upc', 'UPC'],
        'isbn' => ['product_isbn', 'ISBN'],
        'brand' => ['product_brand', 'Brand'],
        'product_type' => ['product_type', 'ProductType'],
        'product_stock_type' => ['product_stock_type', 'ProductStockType'],
        'manufacturer' => ['product_manufacturer', 'Manufacturer'],
        'product_adult' => ['product_adult', 'AdultProduct'],
        'parent_asin' => ['product_parent_asin', 'ParentASIN'],
        'seller_sku' => ['seller_sku', 'SellerSKU'],
        'offer_type' => ['offer_type', 'OfferType'],
        'tag_id' => ['tag_id', 'TagId'],
        'created_at' => ['created_at', 'CreatedAt'],
    ];

    private DbManager $dbManager;

    private CustomerComponent $customerComponent;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        /** @var CustomerComponent $customerComponent */
        $this->customerComponent = \Yii::$app->customerComponent;
    }

    public function queryAll(string $query, FiltersForm $filtersForm, array $params = [], int $cacheDuration = null, bool $shouldApplyAccountSubscriptionLogic = false)
    {
        $filteredQuery = $this->applyFilters($query, $filtersForm, $shouldApplyAccountSubscriptionLogic);
        $params = array_merge($params, $filteredQuery['params']);

        return $this->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($filteredQuery['query'], $params)
            ->cache($cacheDuration)
            ->queryAll();
    }

    /**
     * @throws Exception
     */
    public function applyFiltersByQueryBuilder(
        Query $query,
        FiltersForm  $filtersForm,
        $isCamelCase = 0,
        string $orderIdColumnKey = 'amazon_order_id'
    ): void
    {
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['seller_sku'][$isCamelCase] => $this->explodeValue($filtersForm->sellerSku)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING[$orderIdColumnKey][$isCamelCase] => $this->explodeValue($filtersForm->amazonOrderId)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['asin'][$isCamelCase] => $this->explodeValue($filtersForm->asin)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['ean'][$isCamelCase] => $this->explodeValue($filtersForm->ean)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['upc'][$isCamelCase] => $this->explodeValue($filtersForm->upc)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['isbn'][$isCamelCase] => $this->explodeValue($filtersForm->isbn)]);
        $query->andFilterWhere([
            'in',
            new Expression(sprintf("LOWER(%s)", self::COLUMN_NAME_MAPPING['brand'][$isCamelCase])),
            $this->explodeValue(mb_strtolower($filtersForm->brand))
        ]);
        $query->andFilterWhere([
            'in',
            new Expression(sprintf("LOWER(%s)", self::COLUMN_NAME_MAPPING['manufacturer'][$isCamelCase])),
            $this->explodeValue(mb_strtolower($filtersForm->manufacturer))
        ]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['product_type'][$isCamelCase] => $this->explodeValue($filtersForm->productType)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['parent_asin'][$isCamelCase] => $this->explodeValue($filtersForm->parentAsin)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['product_stock_type'][$isCamelCase] => $this->explodeValue($filtersForm->stockType)]);
        $query->andFilterWhere([self::COLUMN_NAME_MAPPING['offer_type'][$isCamelCase] => $this->explodeValue($filtersForm->offerType)]);

        if (!empty($filtersForm->tagId)) {
            $tagsArray = explode(',', $filtersForm->tagId);
            $tagsArrayString = '[' . implode(',', $tagsArray) . ']';
            $tagIdColumn = self::COLUMN_NAME_MAPPING['tag_id'][$isCamelCase];
            $query->andWhere(new Expression("arrayExists(x -> has($tagIdColumn, x), $tagsArrayString)"));
        }

        // Need to check because frontend sends 0 from time to time (0 === null === without this filter).
        if (!empty($filtersForm->adultProduct)) {
            $query->andFilterWhere([self::COLUMN_NAME_MAPPING['product_adult'][$isCamelCase] => $filtersForm->adultProduct]);
        }
    }

    protected function applyFilters(string $rawQuery, FiltersForm $filtersForm, bool $shouldApplyAccountSubscriptionLogic = false): array
    {
        if (false === strpos($rawQuery, self::FILTERS_PLACEHOLDER)) {
            throw new Exception('Unable to find ' . self::FILTERS_PLACEHOLDER . ' placeholder in passed query');
        }

        $extraConditions = [];
        $extraConditionsStr = ' AND ';
        $result = [
            'query' => '',
            'params' => []
        ];

        if (!empty($filtersForm->dateStart) && !empty($filtersForm->dateEnd)) {
            $extraConditions[] = "(PostedDate BETWEEN toDateTime(:date_start) AND toDateTime(:date_end))";
            $dateStart = (new \DateTime($filtersForm->dateStart));
            $dateEnd = (new \DateTime($filtersForm->dateEnd));

            if ($filtersForm->isTransactionDateMode) {
                $extraConditions[] = "(TransactionDate BETWEEN toDateTime(:transaction_date_start) AND toDateTime(:transaction_date_end))";

                $result['params'][':transaction_date_start'] = $dateStart->format('Y-m-d H:i:s');
                $result['params'][':transaction_date_end'] = $dateEnd->format('Y-m-d H:i:s');

                // "PostedDate" is order date, but transaction for order can appear much later or early.
                // So we should take into account all transactions which appear later.
                $dateEnd->modify('+6 months');
                $dateStart->modify('-6 month');
            }

            $result['params'][':date_start'] = $dateStart->format('Y-m-d H:i:s');
            $result['params'][':date_end'] = $dateEnd->format('Y-m-d H:i:s');
        }

        if (!empty($filtersForm->offerType)) {
            $subQuery = (new AmazonOrder())
                ->search([
                    'offer_type' => $filtersForm->offerType
                ])
                ->select('order_id');

            if (!empty($filtersForm->dateStart) && !empty($filtersForm->dateEnd)) {
                $subQuery->andWhere([
                    'between',
                    'order_purchase_date',
                    $dateStart->format('Y-m-d H:i:s'),
                    $dateEnd->format('Y-m-d H:i:s')
                ]);
            }
            $subSql = $subQuery->createCommand()->getRawSql();
            $extraConditions[] = "(AmazonOrderId IN ($subSql))";
        }

        if (!empty($filtersForm->marketplaceSellerIds)) {
            $marketplaceSellerIds = json_decode($filtersForm->marketplaceSellerIds, true) ?? [];
            $orParts = [];

            foreach ($marketplaceSellerIds as $k => $pair) {
                if (!empty($pair['sellerId']) && !empty($pair['marketplaceId'])) {
                    if ($pair['sellerId'] === 'all') {
                        $orParts[] = "(SellerId != '' AND MarketplaceId = :marketplace_id_$k)";
                        $result['params'][':marketplace_id_' . $k] = $pair['marketplaceId'];
                    } else {
                        $orParts[] = "(SellerId = :seller_id_$k AND MarketplaceId = :marketplace_id_$k)";
                        $result['params'][':marketplace_id_' . $k] = $pair['marketplaceId'];
                        $result['params'][':seller_id_' . $k] = $pair['sellerId'];
                    }
                    continue;
                }

                if (!empty($pair['sellerId'])) {
                    if ($pair['sellerId'] === 'all') {
                        $orParts[] = "SellerId != ''";
                    } else {
                        $orParts[] = "SellerId = :seller_id_single_$k";
                        $result['params'][":seller_id_single_$k"] = $pair['sellerId'];
                    }
                }
            }

            if (!empty($orParts)) {
                $extraConditions[] = "(" . implode(" OR ", $orParts) . ")";
            }
        }

        if (!empty($filtersForm->marketplaceId)) {
            $inPart = $this->buildInPart('MarketplaceId', ':marketplace_id_single', $filtersForm->marketplaceId);
            $extraConditions[] = $inPart['queryPart'];
            $result['params'] = array_merge($result['params'], $inPart['params']);
        }

        if ($shouldApplyAccountSubscriptionLogic) {
            $subscriptionInfo = $this->customerComponent->getSubscriptionInfo();

            if ($subscriptionInfo['isActive']) {
                $inPart = $this->buildInPart("SellerId", ':existing_seller_id', implode(",", $subscriptionInfo['sellerIds']));
                $extraConditions[] = "(SellerId = '' OR " . $inPart['queryPart'] . ')';
                $result['params'] = array_merge($result['params'], $inPart['params']);
            }
        }

        if (!empty($filtersForm->sellerId) && $filtersForm->sellerId !== 'global') {
            if ($filtersForm->sellerId === 'all') {
                $extraConditions[] = "SellerId != ''";
            } else {
                $inPart = $this->buildInPart('SellerId', ':seller_id_single', $filtersForm->sellerId);
                $extraConditions[] = $inPart['queryPart'];
                $result['params'] = array_merge($result['params'], $inPart['params']);
            }
        }

        if (!empty($filtersForm->sellerSku)) {
            $inPart = $this->buildInPart("SellerSKU", ':seller_sku', $filtersForm->sellerSku, true);
            $extraConditions[] = $inPart['queryPart'];
            $result['params'] = array_merge($result['params'], $inPart['params']);
        }

        if (!empty($filtersForm->amazonOrderId)) {
            $inPart = $this->buildInPart("AmazonOrderId", ':amazon_order_id', $filtersForm->amazonOrderId);
            $extraConditions[] = $inPart['queryPart'];
            $result['params'] = array_merge($result['params'], $inPart['params']);
        }

        if ($shouldApplyAccountSubscriptionLogic) {
            $amazonAdCategoryIds = FinanceEventCategory::getAmazonAdIds();
            /** @var AmazonAdsProfile[] $activeProfiles */
            $activeProfiles = AmazonAdsProfile::findActive();

            // Exclude all PPC categories and allow only assigned to active profiles
            $amazonAdsInPart = $this->buildNotInPart('CategoryId', ':amazon_ads_category_id', implode(',', $amazonAdCategoryIds));
            $result['params'] = array_merge($result['params'], $amazonAdsInPart['params']);
            $amazonAdsOrPart = [];

            if (empty($activeProfiles)) {
                $amazonAdsOrPart[] = "false";
            } else {
                foreach ($activeProfiles as $k => $activeProfile) {
                    $amazonAdsOrPart[] = "(MarketplaceId = :amazon_ads_marketplace_id_{$k} AND SellerId = :amazon_ads_seller_id_{$k})";
                    $result['params'][":amazon_ads_marketplace_id_{$k}"] = $activeProfile['marketplace_id'];
                    $result['params'][":amazon_ads_seller_id_{$k}"] = $activeProfile['seller_id'];
                }
            }

            $extraConditions[] = "(
            {$amazonAdsInPart['queryPart']}
                OR
                (" . implode(' OR ', $amazonAdsOrPart) . ")
            )";
        }

        if (empty($extraConditions)) {
            $extraConditionsStr = '';
        } else {
            $extraConditionsStr .= implode(" AND ", $extraConditions);
        }

        $result['query'] = rtrim(str_replace(self::FILTERS_PLACEHOLDER, $extraConditionsStr, $rawQuery));

        return $result;
    }

    /**
     * Builds and returns query part and params for IN query based on comma separated values.
     *
     * @param string $columnName
     * @param string $placeholderKey
     * @param string $commaSeparatedValues
     * @return array
     */
    protected function buildInPart(
        string $columnName,
        string $placeholderKey,
        string $commaSeparatedValues,
        bool $isStringColumn = false
    ): array
    {
        $values = explode(',', $commaSeparatedValues);

        if (empty($values)) {
            return [
                'queryPart' => '',
                'params' => []
            ];
        }

        $params = [];
        $placeholders = [];

        if (count($values) > 1 && $isStringColumn) {
            $values[] = $commaSeparatedValues;
        }

        foreach ($values as $k => $value) {
            $placeholders[] = "{$placeholderKey}_{$k}";
            $params["{$placeholderKey}_{$k}"] = $value;
        }

        return [
            "queryPart" => "{$columnName} IN(" . implode(',', $placeholders) . ")",
            "params" => $params
        ];
    }

    protected function explodeValue(string $valueToExplode): array
    {
        $valueToExplode = trim($valueToExplode);
        if (empty($valueToExplode)) {
            return [];
        }

        return explode(',', $valueToExplode);
    }

    protected function buildNotInPart(string $columnName, string $placeholderKey, string $commaSeparatedValues): array
    {
        $inPart = $this->buildInPart($columnName, $placeholderKey, $commaSeparatedValues);
        return [
            'queryPart' => str_replace('IN', 'NOT IN', $inPart['queryPart']),
            'params' => $inPart['params']
        ];
    }
}
