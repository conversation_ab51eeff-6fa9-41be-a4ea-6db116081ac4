<?php

namespace common\components\messenger;

use common\components\messenger\channel\Slack;
use common\components\messenger\channel\Teams;
use common\components\messenger\dto\Message;
use common\components\messenger\dto\Result;
use yii\caching\CacheInterface;

/**
 * Messaging engine can be used to send messages to any kind of channels.
 */
class Messenger
{
    public const CHANNEL_TEAMS = 'teams';
    public const CHANNEL_SLACK = 'slack';

    private CacheInterface $cache;

    public function __construct()
    {
        $this->cache = \Yii::$app->fastPersistentCache;
    }

    /**
     * Sends message using particular channel.
     *
     * @param Message $message
     * @param string $channelName
     * @param string|null $cacheTimeS If specified, message will be sent only once of the specified time
     * (prevention of message "bombing").
     * @return Result
     * @throws \Exception
     */
    public function send(Message $message, string $channelName, string $cacheTimeS = null): Result
    {
        if (null !== $cacheTimeS) {
            $cacheKey = md5(serialize($message));
            $cacheItem = $this->cache->get($cacheKey);

            if (!empty($cacheItem)) {
                $result = new Result();
                $result->isSuccess = true;
                return $result;
            }

            $this->cache->set($cacheKey, 1, $cacheTimeS);
        }

        $channel = null;
        switch ($channelName) {
            case self::CHANNEL_TEAMS:
                $channel = new Teams();
                break;
            case self::CHANNEL_SLACK:
                $channel = new Slack();
                break;
        }

        if (null === $channel) {
            throw new \Exception("Unable to determine processor for channel $channel");
        }

        return $channel->send($message);
    }
}