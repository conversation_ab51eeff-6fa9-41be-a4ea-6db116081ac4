<?php

namespace common\components\messenger\channel;

use common\components\messenger\dto\Message;
use common\components\messenger\dto\Result;
use GuzzleHttp\Client;

class Teams implements ChannelInterface
{
    public function send(Message $message): Result
    {
        $result = new Result();

        if (empty($message->to)) {
            return $result;
        }

        try {
            $client = new Client();
            $response = $client->post($message->to, [
                'json' => ['text' => $message->message]
            ]);
            $result->response = $response->getBody()->getContents();
            $result->isSuccess = ((int)$result->response) === 1;
        } catch (\Throwable  $e) {
            $result->isSuccess = false;
            $result->response = $e->getMessage();
        }

        return $result;
    }
}