<?php

namespace common\components\messenger;

use common\components\messenger\dto\Message;
use common\components\messenger\dto\Result;

/**
 * Contains predefined message templates in one place.
 */
class MessagesSender
{
    private Messenger $messenger;

    public function __construct()
    {
        $this->messenger = new Messenger();
    }

    /**
     * Sends notification about new unmapped finance event category.
     *
     * @param string $financeEventCategoryPath
     * @param string $mappedToSalesCategoryPath
     * @return Result
     * @throws \Exception
     */
    public function sendUnmappedFinanceEventCategory(
        string $financeEventCategoryPath,
        string $mappedToSalesCategoryPath
    ): Result
    {
        return $this->messenger->send(new Message([
            'to' => getenv('SLACK_WEB_HOOK_METRICS_MONITOR'),
            'message' => sprintf("`%s` => `%s`",
                $financeEventCategoryPath,
                $mappedToSalesCategoryPath
            )
        ]), Messenger::CHANNEL_SLACK, 60 * 60 * 24 * 5);
    }

    public function sendFoundDataInconsistency(
        string $caseDescription,
        array $customerIds,
        int $countOccurrences,
        string $caseType
    ): Result
    {
        return $this->messenger->send(new Message([
            'to' =>  getenv('SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR'),
            'message' => sprintf(
                "Case (%s): %s. Found about `%d` occurrences on customers `%s`",
                $caseType,
                $caseDescription,
                $countOccurrences,
                implode(', ', $customerIds)
            )
        ]), Messenger::CHANNEL_SLACK, 60 * 60 * 6);
    }
}
