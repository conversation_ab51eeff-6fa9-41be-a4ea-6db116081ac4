<?php

namespace common\components\helpers;

use yii\helpers\Inflector;

class ParamsHelper
{
    /**
     * Be aware of using it because of duplicating params.
     * Could be used when you have mess with snake/camel case notations.
     */
    public static function unifyParams(array $params): array
    {
        $out = $params;

        foreach ($params as $key => $value) {
            if (!is_string($key)) {
                continue;
            }

            $norm  = str_replace('-', '_', $key);

            $snake = Inflector::camel2id($norm, '_');
            $camel = lcfirst(Inflector::id2camel($norm, '_'));

            if (!array_key_exists($snake, $out)) {
                $out[$snake] = $value;
            }
            if (!array_key_exists($camel, $out)) {
                $out[$camel] = $value;
            }
        }

        return $out;
    }
}
