<?php

namespace common\components\COGSync;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\demo\generator\ProductGenerator;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\widget\TransactionQueryExecutor;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use common\models\Seller;
use yii\db\Expression;
use yii\db\Query;

class COGSynchronizer
{
    protected const CHUNK_SIZE = 500;

    use LogToConsoleTrait;

    private DbManager $dbManager;
    private TransactionQueryExecutor $transactionQueryExecutor;
    private MessagesSender $messagesSender;
    private PeriodsSaver $periodsSaver;
    private ProductsSaver $productsSaver;

    public function __construct(
        DbManager $dbManager,
        TransactionQueryExecutor $transactionQueryExecutor,
        MessagesSender $messagesSender,
        PeriodsSaver $periodsSaver,
        ProductsSaver $productsSaver
    ) {
        $this->dbManager = $dbManager;
        $this->transactionQueryExecutor = $transactionQueryExecutor;
        $this->messagesSender = $messagesSender;
        $this->periodsSaver = $periodsSaver;
        $this->productsSaver = $productsSaver;
    }

    public function generateChunks(int $customerId, bool $isFirstSync = false, bool $shouldSendCOGChanges = true): array
    {
        $this->info(str_repeat('-', 50));
        $this->info("Generating chunks for customer $customerId");
        $this->dbManager->setCustomerId($customerId);
        $repricerCustomerDb = $this->dbManager->getRepricerCustomerClientDb();

        if ($this->dbManager->isDemo()) {
            return [[
                'customerId' => $customerId,
                'fromId' => 0,
                'toId' => 100,
                'isFirstSync' => false,
                'shouldSendCOGChanges' => true
            ]];
        }

        if (false === $isFirstSync) {
            $countProducts = Product::find()->count();
            $isFirstSync = $countProducts === 0;
        }

        $productQuery = $this->getPreparedProductQuery();
        $previousId = 0;

        $chunks = [];

        /**
         * Generating query chunks.
         * Using id as starting point for all queries.
         * Limit and offset much slower (id is a primary key, query will be faster).
         */
        while (true) {
            $productQuery
                ->select('ap.id')
                ->andWhere(['>', 'ap.id', $previousId])
                ->orderBy('ap.id ASC')
                ->limit(self::CHUNK_SIZE);

            $query = (new Query())
                ->select('max(q1.id)')
                ->from(['q1' => $productQuery]);
            $productId = $query->scalar($repricerCustomerDb);

            if (empty($previousId) && empty($productId)) {
                break;
            }

            $chunks[] = [
                'customerId' => $customerId,
                'fromId' => $previousId,
                'toId' => empty($productId) ? null : $productId,
                'isFirstSync' => $isFirstSync,
                'shouldSendCOGChanges' => $shouldSendCOGChanges
            ];
            $previousId = $productId;

            if (empty($productId)) {
                break;
            }
        }

        return $chunks;
    }

    public function synchronize(int $customerId, bool $isFirstSync = false, bool $shouldSendCOGChanges = true): void
    {
        $this->info(str_repeat('-', 50));
        $this->info("Generating chunks for customer $customerId and sending them to queue");
        $this->dbManager->setCustomerId($customerId);
        $repricerCustomerDb = $this->dbManager->getRepricerCustomerClientDb();

        if ($this->dbManager->isDemo()) {
            $this->messagesSender->customerCOGSSync(
                $this->dbManager->getCustomerId(),
                0,
                1000
            );
            return;
        }

        if (false === $isFirstSync) {
            $countProducts = Product::find()->count();
            $isFirstSync = $countProducts === 0;
        }

        $productQuery = $this->getPreparedProductQuery();
        $previousId = 0;

        /**
         * Generating query chunks.
         * Using id as starting point for all queries.
         * Limit and offset much slower (id is a primary key, query will be faster).
         */
        while (true) {
            $productQuery
                ->select('ap.id')
                ->andWhere(['>', 'ap.id', $previousId])
                ->orderBy('ap.id ASC')
                ->limit(self::CHUNK_SIZE);

            $query = (new Query())
                ->select('max(q1.id)')
                ->from(['q1' => $productQuery]);
            $productId = $query->scalar($repricerCustomerDb);

            if (empty($previousId) && empty($productId)) {
                break;
            }

            $this->messagesSender->customerCOGSSync(
                $this->dbManager->getCustomerId(),
                $previousId,
                empty($productId) ? null : $productId,
                $isFirstSync,
                $shouldSendCOGChanges
            );
            $previousId = $productId;

            if (empty($productId)) {
                break;
            }
        }
    }

    public function createProductCostPeriods(array $productCostItemsToCreate, string $source = ProductCostCategory::SOURCE_REPRICER)
    {
        $this->periodsSaver->createProductCostPeriods($productCostItemsToCreate, $source);
    }

    public function synchronizeChunk(
        int $customerId,
        int $fromId,
        int $toId = null,
        bool $isFirstSync = false,
        bool $shouldSendCOGChanges = true
    ): void
    {
        ProductCostPeriod::getDb()->enableSlaves = false;
        $this->dbManager->setCustomerId($customerId);

        $countActiveSellers = Seller::find()->where([
            'is_analytic_active' => true,
            'customer_id' => $customerId
        ])->count();

        if ($countActiveSellers === 0) {
            $this->info("There are no active sellers found for customer {$customerId}, skipping it");
            return;
        }

        foreach ($this->iterateProducts($fromId, $toId, $this->dbManager->isDemo()) as $data) {
            try {
                $productInfos = $data['productInfos'];

                if (!$this->dbManager->isDemo()) {
                    $productInfos = $this->sortAndRemoveDuplicates($productInfos);
                    $productInfos = $this->fillMissingProductInfoFields($productInfos);
                }

                $this->productsSaver->saveProducts($productInfos);
                $this->info(sprintf('Saving %s products', count($productInfos)));

                $prevProductInfos = $this->getPreviousProductInfos($productInfos);

                $existingInClickhouse = [];
                if ($shouldSendCOGChanges) {
                    $existingInClickhouse = $this->getExistingInClickhouse($productInfos);
                }

                $productCostItemsToCreate = [];

                foreach ($productInfos as $k => $productInfo) {
                    try {
                        $prevProductInfo = $prevProductInfos[$productInfo['id']] ?? [];
                        $isEnabledSyncWithRepricer = $prevProductInfo['is_enabled_sync_with_repricer'] ?? true;

                        if ($productInfo['repricer_is_deleted'] && !$isFirstSync) {
                            $isEnabledSyncWithRepricer = false;
                        }

                        if ($this->dbManager->isRepricerSync() === false) {
                            $isEnabledSyncWithRepricer = false;
                        }

                        // We recognise 'null' and '0' values as N/A first time until it changed by customer
                        if (empty($prevProductInfo)) {
                            if ((float)$productInfo['buying_price'] === 0.0) {
                                $productInfo['buying_price'] = null;
                                $productInfos[$k]['buying_price'] = null;
                            }
                        } else {
                            if ((float)$productInfo['buying_price'] === 0.0 && null === $prevProductInfo['buying_price']) {
                                $productInfo['buying_price'] = null;
                                $productInfos[$k]['buying_price'] = null;
                            }
                        }

                        $productInfos[$k]['source'] = $prevProductInfo['source'] ?? ProductCostCategory::SOURCE_REPRICER;
                        $isVatChanged = empty($prevProductInfo)
                            || (float)$productInfo['vat'] !== (float)$prevProductInfo['vat'];
                        $isBuyingPriceChanged = empty($prevProductInfo)
                            || (float)$productInfo['buying_price'] !== (float)$prevProductInfo['buying_price'];
                        $isShippingCostChanged = empty($prevProductInfo)
                            || (float)$productInfo['shipping_cost'] !== (float)$prevProductInfo['shipping_cost'];
                        $isOtherFeesChanged = empty($prevProductInfo)
                            || (float)$productInfo['other_fees'] !== (float)$prevProductInfo['other_fees'];

                        $hasTransactionsInClickhouse = in_array(strtolower($productInfo['sku']), $existingInClickhouse);
                        $isMultipleStockType = $prevProductInfo['is_multiple_stock_type'] ?? false;

                        $buyingPriceToSave = $prevProductInfo['buying_price'] ?? null;
                        $shippingCostToSave = $prevProductInfo['shipping_cost'] ?? null;
                        $otherFeesToSave = $prevProductInfo['other_fees'] ?? null;
                        $vatToSave = $prevProductInfo['vat'] ?? null;

                        $periodPrototype = [
                            'marketplace_id' => $productInfo['marketplace_id'],
                            'seller_id' => strtoupper($productInfo['seller_id']),
                            'seller_sku' => $productInfo['sku'],
                            'is_first' => false,
                            'sales_category_id' => null,
                            'has_transactions_in_clickhouse' => $hasTransactionsInClickhouse,
                            'productCostItems' => []
                        ];

                        if ($isVatChanged
                            && null !== $productInfo['vat']
                            && $isEnabledSyncWithRepricer
                        ) {
                            $vatToSave = $productInfo['vat'];
                            $productCostItemsToCreate[] = array_merge($periodPrototype, [
                                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                                'is_first' => empty($prevProductInfo) || $prevProductInfo['vat'] === null,
                                'productCostItems' => [[
                                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                                    'amount_per_unit' => (float)$productInfo['vat'],
                                    'marketplace_amount_per_unit' => (float)$productInfo['vat'],
                                    'currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_rate' => 1,
                                ]]
                            ]);
                            $productInfos[$k]['source'] = ProductCostCategory::SOURCE_REPRICER;
                        }

                        if (($productInfo['stock_type'] === Product::STOCK_TYPE_FBM || $isMultipleStockType)
                            && $isShippingCostChanged
                            && null !== $productInfo['shipping_cost']
                            && $isEnabledSyncWithRepricer
                        ) {
                            $shippingCostToSave = $productInfo['shipping_cost'];
                            $productCostItemsToCreate[] = array_merge($periodPrototype, [
                                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS,
                                'is_first' => empty($prevProductInfo) || $prevProductInfo['shipping_cost'] === null,
                                'productCostItems' => [[
                                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS,
                                    'amount_per_unit' => (float)$productInfo['shipping_cost'],
                                    'currency_id' => $productInfo['currency_code'],
                                    'marketplace_amount_per_unit' => (float)$productInfo['shipping_cost'],
                                    'marketplace_currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_rate' => 1,
                                ]]
                            ]);
                            $productInfos[$k]['source'] = ProductCostCategory::SOURCE_REPRICER;
                        }

                        if (!empty($prevProductInfo)
                            && $productInfo['stock_type'] === Product::STOCK_TYPE_FBA
                            && !$isMultipleStockType
                            && null !== $prevProductInfo['shipping_cost']
                        ) {
                            $shippingCostToSave = null;
                            $productCostItemsToCreate[] = array_merge($periodPrototype, [
                                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS,
                                'is_first' => false,
                                'productCostItems' => [[
                                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS,
                                    'amount_per_unit' => 0.00,
                                    'currency_id' => $productInfo['currency_code'],
                                    'marketplace_amount_per_unit' => 0.00,
                                    'marketplace_currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_rate' => 1,
                                ]]
                            ]);
                            $productInfos[$k]['source'] = ProductCostCategory::SOURCE_REPRICER;
                        }

                        if ($isBuyingPriceChanged
                            && null !== $productInfo['buying_price']
                            && $isEnabledSyncWithRepricer
                        ) {
                            $buyingPriceToSave = $productInfo['buying_price'];
                            $productCostItemsToCreate[] = array_merge($periodPrototype, [
                                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_COG,
                                'is_first' => empty($prevProductInfo) || $prevProductInfo['buying_price'] === null,
                                'productCostItems' => [[
                                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_COG,
                                    'amount_per_unit' => (float)$productInfo['buying_price'],
                                    'currency_id' => $productInfo['currency_code'],
                                    'marketplace_amount_per_unit' => (float)$productInfo['buying_price'],
                                    'marketplace_currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_rate' => 1,
                                ]]
                            ]);
                            $productInfos[$k]['source'] = ProductCostCategory::SOURCE_REPRICER;
                        }

                        if ($isOtherFeesChanged
                            && null !== $productInfo['other_fees']
                            && $isEnabledSyncWithRepricer
                        ) {
                            $otherFeesToSave = $productInfo['other_fees'];
                            $productCostItemsToCreate[] = array_merge($periodPrototype, [
                                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_OTHER_FEES,
                                'is_first' => empty($prevProductInfo) || $prevProductInfo['other_fees'] === null,
                                'productCostItems' => [[
                                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_OTHER_FEES,
                                    'amount_per_unit' => (float)$productInfo['other_fees'],
                                    'currency_id' => $productInfo['currency_code'],
                                    'marketplace_amount_per_unit' => (float)$productInfo['other_fees'],
                                    'marketplace_currency_id' => $productInfo['currency_code'],
                                    'marketplace_currency_rate' => 1,
                                ]]
                            ]);
                            $productInfos[$k]['source'] = ProductCostCategory::SOURCE_REPRICER;
                        }

                        $productInfos[$k]['buying_price'] = $buyingPriceToSave;
                        $productInfos[$k]['shipping_cost'] = $shippingCostToSave;
                        $productInfos[$k]['other_fees'] = $otherFeesToSave;
                        $productInfos[$k]['vat'] = $vatToSave;

                        unset($productInfos[$k]['id']);
                    } catch (\Throwable $e) {
                        $this->error($e);
                    }
                }

                $this->info(sprintf('Creating %s periods', count($productCostItemsToCreate)));
                $this->periodsSaver->createProductCostPeriods($productCostItemsToCreate);
            } catch (\Throwable $e) {
                // try catch block still here to prevent conflicts
                throw $e;
            }
        }
    }

    private function sortAndRemoveDuplicates(array $productInfos): array
    {
        $productInfosNew = [];

        foreach ($productInfos as $productInfo) {
            $key = implode('_', [
                $productInfo['marketplace_id'],
                $productInfo['amazon_customer_account_id'],
                $productInfo['sku']
            ]);
            $productInfosNew[$key] = $productInfo;
        }

        return array_values($productInfosNew);
    }

    private function getExistingInClickhouse(array $productInfos): array
    {
        if (empty($productInfos)) {
            return [];
        }

        $filtersForm = new FiltersForm();
        $filtersForm->sellerSku = implode(',', array_column($productInfos, 'sku'));
        $tableName = Transaction::tableName();
        $existingSellerSkus = $this
            ->transactionQueryExecutor
            ->queryAll("
                    SELECT DISTINCT lower(SellerSKU) as SellerSKU
                    FROM {$tableName} t
                    WHERE 1 = 1
                    [FILTERS]
                ",
                $filtersForm
            );

        if (empty($existingSellerSkus)) {
            return [];
        }

        return array_column($existingSellerSkus, 'SellerSKU');
    }

    private function getPreviousProductInfos(array $productInfos): array
    {
        $repricerIds = [];

        foreach ($productInfos as $productInfo) {
            $repricerIds[] = $productInfo['id'];
        }

        $result = (new Query())
            ->select('sku, repricer_id, asin, buying_price,other_fees,shipping_cost,vat, seller_id, marketplace_id, is_enabled_sync_with_repricer,source,is_multiple_stock_type')
            ->from(Product::tableName())
            ->where(['in', 'repricer_id', $repricerIds])
            ->all($this->dbManager->getCustomerDb())
        ;

        $prevProductInfos = [];

        foreach ($result as $prevProductInfo) {
            $prevProductInfos[$prevProductInfo['repricer_id']] = $prevProductInfo;
        }

        return $prevProductInfos;
    }

    private function fillMissingProductInfoFields(array $productInfos): array
    {
        foreach ($productInfos as $k => $productInfo) {
            /** @var AmazonMarketplace $marketplace */
            $marketplace = AmazonMarketplace::getById($productInfo['marketplace_id']);
            $productInfos[$k]['currency_code'] = $marketplace->currency_code;
            $productInfos[$k]['source'] = Product::SOURCE_REPRICER;
            $productInfos[$k]['seller_id'] = $this->getSellerIdByCustomerAccountId(
                $productInfo['amazon_customer_account_id']
            );
            $productInfos[$k]['condition'] = ctype_digit($productInfo['condition'])
                ? $productInfo['condition']
                : null;
            $productInfos[$k]['repricer_id'] = $productInfo['id'];

            unset($productInfos[$k]['amazon_customer_account_id']);
        }
        return $productInfos;
    }

    private function iterateProducts(int $fromId, int $toId = null, bool $isDemo = false): \Iterator
    {
        if ($isDemo) {
            yield $this->getDemoProducts();
            return;
        }

        $productInfoQuery = $this->getPreparedProductQuery();
        $productInfoQuery->andWhere(['>=', 'ap.id', $fromId]);

        if (!empty($toId)) {
            $productInfoQuery->andWhere(['<', 'ap.id', $toId]);
        }

        $customerId = $this->dbManager->getCustomerId();

        try {
            $repricerCustomerDb = $this->dbManager->getRepricerCustomerClientDb();
            $this->info(str_repeat('-', 50));
            $this->info('Started processing customer ' . $customerId);

            $this->info("Query:");
            $this->info($productInfoQuery->createCommand()->getRawSql());

            $countAllProducts = $productInfoQuery->count('*', $repricerCustomerDb);
            $countProcessedProducts = 0;

            $productInfos = $productInfoQuery->all($repricerCustomerDb);
            $this->info(sprintf('Fetched %d products for processing', count($productInfos)));

            yield [
                'productInfos' => $productInfos,
            ];
            $countProcessedProducts += count($productInfos);
            $this->info(sprintf("Processed %d from %d products", $countProcessedProducts, $countAllProducts));
            $this->info("Finished processing customer $customerId");

            $repricerCustomerDb->close();
        } catch (\Throwable $e) {
            if (false === strpos($e->getMessage(), 'Unknown database')) {
                $this->error($e);
            }
            $this->info("Skipped exception:" . $e->getMessage());
        }
    }

    private function getSellerIdByCustomerAccountId(string $amazonCustomerAccountId): string
    {
        $sellerId = (new Query())
            ->select('UPPER(aca.sellerId)')
            ->from('amazon_customer_account aca')
            ->where('aca.id = :amazon_customer_account_id', [
                'amazon_customer_account_id' => $amazonCustomerAccountId
            ])
            ->createCommand($this->dbManager->getRepricerMainDb())
            ->cache(60 * 60 * 24 * 2)
            ->queryScalar()
        ;
        return strtoupper($sellerId);
    }

    private function getActiveAmazonCustomerAccountMap(): array
    {
        $activeSellerIds = (new Query())
            ->select('id')
            ->from(Seller::tableName())
            ->where([
                'is_analytic_active' => true,
                'customer_id' => $this->dbManager->getCustomerId()
            ])
            ->createCommand(Seller::getDb())
            ->cache(60)
            ->queryColumn()
        ;
        $this->info('Active sellers ids:');
        $this->info($activeSellerIds);

        $res = (new Query())
            ->select('aca.sellerId as seller_id, aca.id as amazon_customer_account_id, c.api_version')
            ->from('amazon_customer_account aca')
            ->where([
                'AND',
                ['=', 'ca.customer_id', $this->dbManager->getCustomerId()],
                ['=', 'aca.active', true],
                ['in', 'UPPER(aca.sellerId)', $activeSellerIds],
            ])
            ->leftJoin('customer_account ca', 'ca.id = aca.customer_account_id')
            ->leftJoin('customer c', 'c.id = ca.customer_id')
            ->createCommand($this->dbManager->getRepricerMainDb())
            ->cache(60)
            ->queryAll();

        $map = [];
        foreach ($res as $item) {
            $map[$item['amazon_customer_account_id']] = [
                'seller_id' => $item['seller_id'],
                'api_version' => $item['api_version']
            ];
        }
        $this->info('Active accounts map:');
        $this->info($map);

        return $map;
    }

    private function getPreparedProductQuery(): Query
    {
        $activeAccountMap = $this->getActiveAmazonCustomerAccountMap();
        $apiVersion = $activeAccountMap[array_key_first($activeAccountMap)]['api_version'];
        $apiVersionPostfix = (int)$apiVersion === 2
            ? '_v2'
            : '';

        $productInfoQuery = (new Query())
            ->select('
                ap.id,
                ap.asin,
                ap.sku,
                ap.stock_type, 
                ap.condition, 
                ap.tax_amount as vat, 
                ap.marketplace_id, 
                ap.deleted as repricer_is_deleted,
                ps.vendor_price as buying_price, 
                ps.other_fee as other_fees,
                ps.manual_fulfilment_fee as shipping_cost,
                ap.amazon_customer_account_id
            ')
            ->addSelect(new Expression('substring(ap.title, 1, 2500) as title'))
            ->from("amazon_product{$apiVersionPostfix} ap")
            ->where([
                'AND',
                ['IN', 'ap.amazon_customer_account_id', array_keys($activeAccountMap)],
            ])
        ;

        $productInfoQuery
            ->orderBy('ap.id ASC')
            ->leftJoin("product_setting{$apiVersionPostfix} ps", 'ps.id = ap.id');
        return $productInfoQuery;
    }

    protected function getDemoProducts(): array
    {
        $products = Product::find()
            ->addSelect('*, repricer_id as id')
            ->orderBy('random()')
            ->asArray()
            ->all();

        if (count($products) === 0) {
            $products = (new ProductGenerator())->generate();
        }

        shuffle($products);

        for ($i = 0; $i < 5; $i++) {
            $products[$i]['buying_price'] += $products[$i]['buying_price'] * 0.02;

            if (!empty($products[$i]['shipping_cost'])) {
                $products[$i]['shipping_cost'] += $products[$i]['shipping_cost'] * 0.02;
            }

            if (!empty($products[$i]['other_fees'])) {
                $products[$i]['other_fees'] += $products[$i]['other_fees'] * 0.02;
            }
        }

        return [
            'productInfos' => $products
        ];
    }
}
