<?php

namespace common\components\COGSync;

use common\components\COGSync\traits\HelperTrait;
use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\SalesCategory;
use yii\db\Expression;

class DefaultVATManager
{
    use LogToConsoleTrait;
    use HelperTrait;

    private DbManager $dbManager;
    private CustomerComponent $customerComponent;
    private PeriodsSaver $periodsSaver;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerComponent = new CustomerComponent();
        $this->periodsSaver = \Yii::$container->get('PeriodsSaver');
    }

    /**
     * @param Product[] $products Array of Product as array
     * @param bool $shouldCheckForNullVAT Should check products fir NULL vat or leave this to function caller
     * @return void
     */
    public function generateAndSaveDefaultVATForProducts(array $products, bool $shouldCheckForNullVAT = true): void
    {
        $this->info("Setting default VAT for products started");
        $this->info(['countProducts' => count($products)]);

        try {
            $skus = array_column($products, 'sku');
            $sellerIds = array_column($products, 'seller_id');
            $marketplaceIds = array_column($products, 'marketplace_id');

            if ($shouldCheckForNullVAT) {
                $productsWithEmptyVAT = Product::find()->where([
                    'AND',
                    ['is', 'vat', new Expression('NULL')],
                    ['in', 'sku', array_unique($skus)],
                    ['in', 'seller_id', array_unique($sellerIds)],
                    ['in', 'marketplace_id', array_unique($marketplaceIds)],
                ])->asArray()->all();
            } else {
                $productsWithEmptyVAT = $products;
            }

            if (empty($productsWithEmptyVAT)) {
                return;
            }

            $defaultVATPeriods = $this->generateDefaultVATPeriods($productsWithEmptyVAT);
            $this->periodsSaver->createProductCostPeriods($defaultVATPeriods, ProductCostCategory::SOURCE_DEFAULT_VAT);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->info("Setting default VAT for products finished");
    }

    /**
     * Generates and returns VAT product cost periods with default value.
     *
     * @param array $productsWithEmptyVAT
     * @return array
     */
    protected function generateDefaultVATPeriods(array $productsWithEmptyVAT): array
    {
        $existingInClickhouse = $this->getExistingInClickhouse($productsWithEmptyVAT);
        $defaultVATPeriods = [];

        foreach ($productsWithEmptyVAT as $product) {
            $globalVATValue = $this->customerComponent->getDefaultVATValue(
                $product['marketplace_id'],
                $product['seller_id']
            );

            if (null === $globalVATValue) {
                continue;
            }

            $defaultVATPeriods[] = [
                'marketplace_id' => $product['marketplace_id'],
                'seller_id' => $product['seller_id'],
                'seller_sku' => $product['sku'],
                'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                'is_first' => true,
                'has_transactions_in_clickhouse' => in_array(strtolower($product['sku']), $existingInClickhouse),
                'productCostItems' => [[
                    'sales_category_id' => SalesCategory::CATEGORY_EXPENSES_TAXES,
                    'amount_per_unit' => $globalVATValue,
                    'currency_id' => $product['currency_code'],
                    'marketplace_amount_per_unit' => $globalVATValue,
                    'marketplace_currency_id' => $product['currency_code'],
                    'marketplace_currency_rate' => 1,
                ]]
            ];
        }

        return $defaultVATPeriods;
    }
}
