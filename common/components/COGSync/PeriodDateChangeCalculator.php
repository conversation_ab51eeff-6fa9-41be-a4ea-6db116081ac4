<?php

namespace common\components\COGSync;

class PeriodDateChangeCalculator
{
    protected const DATE_FORMAT = 'Y-m-d H:i:s';

    /**
     * Calculates and returns COG changes should be applied when changing date start, date end.
     * Takes into account intersection of dates.
     *
     * @param string $dateTimeStartOld
     * @param string $dateTimeEndOld
     * @param string $dateTimeStartNew
     * @param string $dateTimeEndNew
     * @param float $amount
     * @return array[]
     * @throws \Exception
     */
    public function getCOGChanges(
        string $dateTimeStartOld,
        string $dateTimeEndOld,
        string $dateTimeStartNew,
        string $dateTimeEndNew,
        float $amount
    ): array {
        // Nothing changed
        if ($dateTimeStartOld === $dateTimeStartNew
            && $dateTimeEndOld === $dateTimeEndNew
        ) {
            return [];
        }

        $dateTimeStartOld = strtotime($dateTimeStartOld);
        $dateTimeEndOld = strtotime($dateTimeEndOld);
        $dateTimeStartNew = strtotime($dateTimeStartNew);
        $dateTimeEndNew = strtotime($dateTimeEndNew);

        // Moving date start little left
        if ($dateTimeStartNew < $dateTimeStartOld && $dateTimeEndOld === $dateTimeEndNew) {
            return [[
                'dateStart' => date(self::DATE_FORMAT, $dateTimeStartNew),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeStartOld - 1),
                'amountOld' => 0,
                'amountNew' => $amount
            ]];
        }

        // Moving date start little right
        if ($dateTimeStartNew > $dateTimeStartOld && $dateTimeEndOld === $dateTimeEndNew) {
            return [[
                'dateStart' => date(self::DATE_FORMAT, $dateTimeStartOld),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeStartNew - 1),
                'amountOld' => $amount,
                'amountNew' => 0
            ]];
        }

        // Moving date start to the right, outside old date end
        if ($dateTimeStartNew > $dateTimeStartOld && $dateTimeEndOld < $dateTimeEndNew) {
            return [[
                'dateStart' => date(self::DATE_FORMAT, $dateTimeStartOld),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeEndOld),
                'amountOld' => $amount,
                'amountNew' => 0
            ], [
                'dateStart' => date(self::DATE_FORMAT, $dateTimeStartNew),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeEndNew),
                'amountOld' => 0,
                'amountNew' => $amount
            ]];
        }

        if  ($dateTimeEndOld < $dateTimeEndNew) {
            return [[
                'dateStart' => date(self::DATE_FORMAT, $dateTimeEndOld),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeEndNew),
                'amountOld' => 0,
                'amountNew' => $amount
            ]];
        }

        if  ($dateTimeEndOld > $dateTimeEndNew) {
            return [[
                'dateStart' => date(self::DATE_FORMAT, $dateTimeEndNew + 1),
                'dateEnd' => date(self::DATE_FORMAT, $dateTimeEndOld),
                'amountOld' => $amount,
                'amountNew' => 0,
            ]];
        }

        throw new \Exception("Not expected date change situation");
    }
}