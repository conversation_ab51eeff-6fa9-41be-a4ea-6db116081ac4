<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\db\StaleObjectException;

class PeriodsSaver
{
    use LogToConsoleTrait;

    private DbManager $dbManager;
    private MessagesSender $messagesSender;
    private ProductToProductSynchronizer $productToProductSynchronizer;
    private GlobalMarketplaceService $globalMarketplaceService;
    private array $itemsToSyncWithGlobalMarketplace = [];

    public function __construct(
        DbManager $dbManager,
        MessagesSender $messagesSender,
        ProductToProductSynchronizer $productToProductSynchronizer,
        GlobalMarketplaceService $globalMarketplaceService
    ) {
        $this->dbManager = $dbManager;
        $this->messagesSender = $messagesSender;
        $this->productToProductSynchronizer = $productToProductSynchronizer;
        $this->globalMarketplaceService = $globalMarketplaceService;
    }

    /**
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function createProductCostPeriods(
        array $productCostPeriodsToCreate,
        string $source = ProductCostCategory::SOURCE_REPRICER,
        bool $shouldCollectItemsToSyncWithGlobalMarketplace = false
    ): void
    {
        ProductCostPeriod::$isSyncMode = true;
        ProductCostItem::$isSyncMode = true;
        $this->createPeriodsInLoop($productCostPeriodsToCreate, $source, $shouldCollectItemsToSyncWithGlobalMarketplace);
        ProductCostPeriod::$isSyncMode = false;
        ProductCostItem::$isSyncMode = false;
    }

    /**
     * @throws \Throwable
     * @throws StaleObjectException
     */
    private function createPeriodsInLoop(
        array $productCostPeriodsToCreate,
        string $source = ProductCostCategory::SOURCE_REPRICER,
        bool $shouldCollectItemsToSyncWithGlobalMarketplace = false
    ): void
    {
        $productCostPeriodsToCreate = $this->addFirstInfinityPeriodIfNeed($productCostPeriodsToCreate);

        $usedPeriods = [];
        $itemsToSyncWithGlobalMarketplace = [];

        foreach ($productCostPeriodsToCreate as $periodData) {
            $globalMarketplaceId = $this->globalMarketplaceService->getGlobalMarketplaceId($periodData['seller_id'], $periodData['seller_sku']);

            if (!empty($globalMarketplaceId)) {
                /** @var Product $product */
                $product = Product::find()->where([
                    'marketplace_id' => $periodData['marketplace_id'],
                    'seller_id' => $periodData['seller_id'],
                    'sku' => $periodData['seller_sku']
                ])->cache(60)->one();

                // If global marketplace specified, do not allow to manage other marketplaces directly
                if ($product->marketplace_id !== $globalMarketplaceId
                    && $product->is_enabled_sync_with_global_marketplace
                    && $this->globalMarketplaceService->isEnabledSyncBySalesCategoryId($periodData['sales_category_id'], $periodData['seller_id'])
                ) {
                    continue;
                }

                $key = implode('_', [
                    $periodData['seller_id'],
                    $periodData['seller_sku']
                ]);
                $this->itemsToSyncWithGlobalMarketplace[$key] = [
                    'seller_id' => $periodData['seller_id'],
                    'seller_sku' => $periodData['seller_sku']
                ];
            }

            $startDate = $this->getStartDate($periodData);
            $uniqueKey = implode('-', [
                $periodData['marketplace_id'],
                $periodData['seller_id'],
                $periodData['seller_sku'],
                $periodData['sales_category_id'],
                $startDate->format('Y-m-d 00:00:00')
            ]);

            $periodItemsAmountByCategoryId = [];
            foreach ($periodData['productCostItems'] as $productCostItemToCreate) {
                $periodItemsAmountByCategoryId[$productCostItemToCreate['product_cost_category_id']] = $periodItemsAmountByCategoryId[$productCostItemToCreate['product_cost_category_id']] ?? 0;
                $periodItemsAmountByCategoryId[$productCostItemToCreate['product_cost_category_id']] += (float)$productCostItemToCreate['marketplace_amount_per_unit'];
            }

            $productCostPeriod = $usedPeriods[$uniqueKey] ?? null;
            $isCreatePeriod = false;
            if (empty($productCostPeriod)) {
                [$productCostPeriod, $isCreatePeriod] = $this->getOrCreateProductCostPeriod($periodData, $startDate, $source, $periodItemsAmountByCategoryId);
            }

            $usedPeriods[$uniqueKey] = $productCostPeriod;

            [$nullValueCategories, $valueCategories] = $this->getNullAndValueCategories($periodData['productCostItems']);

            $leftPeriod = [];
            $rightPeriods = [];
            if (!empty($startDate)) {
                $leftPeriod = $productCostPeriod->getLeftPeriod($startDate->format('Y-m-d 00:00:00'));
                $rightPeriods = $productCostPeriod->getRightPeriods($startDate->format('Y-m-d 00:00:00'));

                if (!empty($leftPeriod) && $isCreatePeriod) {
                    $this->copyItemsFromLeftPeriod($productCostPeriod, $valueCategories, $leftPeriod);
                }

                $this->removeNullValueItemsFromRightPeriod(
                    $rightPeriods,
                    $nullValueCategories,
                );

                $this->removeNullValueItemsFromCurrentPeriod(
                    $productCostPeriod,
                    $nullValueCategories,
                );
            }

            $this->createProductCostItems($periodData, $productCostPeriod, $rightPeriods);
            $productCostPeriod->removeIsNullPeriod();
//            $periodsMerge = new PeriodsMerge();
//            $periodsMerge->deleteDuplicatePeriods([$periodData]);
        }

        if (!$shouldCollectItemsToSyncWithGlobalMarketplace) {
            $this->sendItemsToSyncWithGlobalMarketplace();
        }
    }

    public function sendItemsToSyncWithGlobalMarketplace(): void
    {
        foreach ($this->itemsToSyncWithGlobalMarketplace as $item) {
            try {
                Product::updateAll([
                    'global_marketplace_sync_version' => 0
                ], [
                    'seller_id' => $item['seller_id'],
                    'sku' => $item['seller_sku']
                ]);

                $this->messagesSender->syncProductWithGlobalMarketplace(
                    $item['seller_id'],
                    $item['seller_sku'],
                    null,
                    5,
                    false
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
        $this->itemsToSyncWithGlobalMarketplace = [];
    }

    private function getStartDate(array $periodData): \DateTime
    {
        return !empty($periodData['date_start'])
            ? (new \DateTime($periodData['date_start']))->setTime(0, 0)
            : (new \DateTime())->setTime(0, 0);
    }

    private function getOrCreateProductCostPeriod(array $periodData, \DateTime $startDate, string $source, array $periodItemsAmountByCategoryId): array
    {
        $productCostPeriod = new ProductCostPeriod();
        $productCostPeriod->date_start = $startDate->format('Y-m-d 00:00:00');
        $productCostPeriod->source = $source;
        $productCostPeriod->marketplace_id = $periodData['marketplace_id'];
        $productCostPeriod->seller_id = $periodData['seller_id'];
        $productCostPeriod->seller_sku = $periodData['seller_sku'];
        $productCostPeriod->sales_category_id = $periodData['sales_category_id'];

        //TODO add with items
        $existingPeriod = ProductCostPeriod::getExistingPeriod(
            $productCostPeriod->marketplace_id,
            $productCostPeriod->seller_id,
            $productCostPeriod->seller_sku,
            $productCostPeriod->sales_category_id,
            $startDate->format('Y-m-d 00:00:00')
        );

        if ($existingPeriod !== null) {

            $isSameAmountPeriod = strtotime($existingPeriod->date_start) <= strtotime($productCostPeriod->date_start)
                && $this->checkEqualAmount($periodItemsAmountByCategoryId, $existingPeriod['items']);
            if ($isSameAmountPeriod) {
                return [$existingPeriod, false];
            }

            $isSameDayPeriod = strtotime($existingPeriod->date_start) === strtotime($productCostPeriod->date_start);
            if ($isSameDayPeriod) {
                return [$existingPeriod, false];
            }
        }

        $productCostPeriod->save(false);
        return [$productCostPeriod, true];
    }

    private function checkEqualAmount(array $periodItemsAmountByCategoryId, $items): bool
    {
        $periodExistItemsAmountByCategoryId = [];
        foreach ($items as $productCostItem) {
            $periodExistItemsAmountByCategoryId[$productCostItem['product_cost_category_id']] = $periodExistItemsAmountByCategoryId[$productCostItem['product_cost_category_id']] ?? 0;
            $periodExistItemsAmountByCategoryId[$productCostItem['product_cost_category_id']] += (float)$productCostItem['marketplace_amount_per_unit'];
        }

        $isEqual = true;
        $allKeys = array_unique(array_merge(array_keys($periodExistItemsAmountByCategoryId), array_keys($periodItemsAmountByCategoryId)));

        foreach ($allKeys as $key) {

            $value1 = $periodExistItemsAmountByCategoryId[$key] ?? 0;
            $value2 = $periodItemsAmountByCategoryId[$key] ?? 0;

            if ($value1 !== $value2) {
                $isEqual = false;
                break;
            }
        }
        return $isEqual;
    }

    private function getNullAndValueCategories(array $productCostItems): array
    {
        $nullValueCategories = [];
        $valueCategories = [];
        foreach ($productCostItems as $productCostItem) {
            if ($productCostItem['amount_per_unit'] === null && $productCostItem['marketplace_amount_per_unit'] === null) {
                $nullValueCategories[] = $productCostItem['product_cost_category_id'] ?? ProductCostCategory::getDefaultIdBySalesCategoryId($productCostItem['sales_category_id']);
            }
            $valueCategories[] = $productCostItem['product_cost_category_id'] ?? ProductCostCategory::getDefaultIdBySalesCategoryId($productCostItem['sales_category_id']);
        }
        return [$nullValueCategories, $valueCategories];
    }

    /**
     * @throws StaleObjectException
     * @throws \Throwable
     */
    private function removeNullValueItemsFromRightPeriod(array $rightPeriods, array $nullValueCategories): void
    {
        foreach ($rightPeriods as $rightPeriod) {
            $items = $rightPeriod['items'];
            foreach ($items as $item) {
                /**@var ProductCostItem $item */
                $itemCategoryId = $item->product_cost_category_id;

                if (in_array($itemCategoryId, $nullValueCategories)) {
                   $item->delete();
                }
            }
            $rightPeriod->removeIsNullPeriod();
        }
    }

    /**
     * @throws StaleObjectException
     * @throws \Throwable
     */
    private function removeNullValueItemsFromCurrentPeriod(ProductCostPeriod $productCostPeriod, array $nullValueCategories): void
    {
        $items = $productCostPeriod['items'];

        foreach ($items as $item) {
            /**@var ProductCostItem $item */
            $itemCategoryId = $item->product_cost_category_id;

            if (in_array($itemCategoryId, $nullValueCategories)) {
               $item->delete();
            }
        }
    }

    private function copyItemsFromLeftPeriod(
        ProductCostPeriod $productCostPeriod,
        array $valueCategories,
        ProductCostPeriod $leftPeriod
    ): void
    {
        $items = $leftPeriod['items'];
        $itemsCurrentPeriod = $productCostPeriod['items'];

        foreach ($items as $itemToClone) {
            /**@var ProductCostItem $itemToClone */
            $itemCategoryId = $itemToClone->product_cost_category_id;

            // skip ignore and new value categories
            if (in_array($itemCategoryId, $valueCategories)){
                continue;
            }

            //remove dublicate old item
            foreach ($itemsCurrentPeriod as $itemCurrentPeriod) {
                /**@var ProductCostItem $itemCurrentPeriod */
                if ($itemCurrentPeriod->product_cost_category_id == $itemCategoryId) {
                    $itemCurrentPeriod->delete();
                }
            }

            $item = $itemToClone->makeDuplicate();
            $item->product_cost_period_id = $productCostPeriod->id;
            $item->save(false);
        }

        if ($leftPeriod->source === ProductCostCategory::SOURCE_REPRICER && empty($leftPeriod->date_start)) {
            $leftPeriod->source = ProductCostCategory::SOURCE_MANUAL;
            $productCostPeriod->source = ProductCostCategory::SOURCE_MANUAL;
            $leftPeriod->save(false);
            $productCostPeriod->save(false);
        }
    }

    private function createProductCostItems(array $periodData, $productCostPeriod, array $rightPeriods): void
    {
        foreach ($periodData['productCostItems'] as $productCostItemToCreate) {
            if ($productCostItemToCreate['amount_per_unit'] !== null && $productCostItemToCreate['marketplace_amount_per_unit'] !== null) {
                $productCategoryId = $productCostItemToCreate['product_cost_category_id'] ?? ProductCostCategory::getDefaultIdBySalesCategoryId($productCostItemToCreate['sales_category_id']);

                $productCostItem = ProductCostItem::getExistingItem(
                    $productCostPeriod->id,
                    $productCategoryId
                );
                if (empty($productCostItem)) {
                    $productCostItem = new ProductCostItem();
                }

                $productCostItem->currency_id = $productCostItemToCreate['currency_id'];
                $productCostItem->marketplace_currency_id = $productCostItemToCreate['marketplace_currency_id'];
                $productCostItem->marketplace_amount_per_unit = (float)$productCostItemToCreate['marketplace_amount_per_unit'];
                $productCostItem->marketplace_currency_rate = (float)$productCostItemToCreate['marketplace_currency_rate'];
                $productCostItem->amount_total = (float)$productCostItemToCreate['amount_per_unit'];
                $productCostItem->units = 1;
                $productCostItem->product_cost_period_id = $productCostPeriod->id;
                $productCostItem->product_cost_category_id = $productCategoryId;
                $productCostItem->status = ProductCostItem::STATUS_APPLIED;
                $productCostItem->save(false);
                foreach ($rightPeriods as $rightPeriod) {
                    $items = $rightPeriod['items'];

                    $this->updateOrCreateItem($items, $productCategoryId, $productCostItemToCreate, $productCostItem, $rightPeriod);
                }
            }
        }
    }

    /**
     * Looking for periods with specified start_date and creating first period with zero value (infinity -> infinity).
     * Only in case when there are no periods yet.
     *
     * @param array $productCostPeriodsToCreate
     * @return array
     */
    protected function addFirstInfinityPeriodIfNeed(array $productCostPeriods): array
    {
        $productCostPeriodsModified = [];
        foreach ($productCostPeriods as $periodData) {
            if (empty($periodData['date_start'])) {
                $productCostPeriodsModified[] = $periodData;
                continue;
            }

            $countExistingPeriods = ProductCostPeriod::getExistingPeriods(
                $periodData['marketplace_id'],
                $periodData['seller_id'],
                $periodData['seller_sku'],
                $periodData['sales_category_id']
            );

            if (count($countExistingPeriods) === 0) {
                $firstInfinityPeriod = $periodData;
                foreach ($firstInfinityPeriod['productCostItems'] as $key => $productCostItem) {
                    $firstInfinityPeriod['productCostItems'][$key]['amount_per_unit'] = 0;
                    $firstInfinityPeriod['productCostItems'][$key]['marketplace_amount_per_unit'] = 0;
                }
                $firstInfinityPeriod['date_start'] = null;
                $productCostPeriodsModified[] = $firstInfinityPeriod;
            }

            $productCostPeriodsModified[] = $periodData;
        }
        return $productCostPeriodsModified;
    }

    private function updateOrCreateItem(array $items, int $productCategoryId, array $productCostItemToCreate, $productCostItem, $rightPeriod): void
    {
        $isUpdateItem = false;
        foreach ($items as $item) {
            /**@var ProductCostItem $item */
            $itemCategoryId = $item->product_cost_category_id;
            if ($productCategoryId == $itemCategoryId) {
                $item->marketplace_currency_id = $productCostItemToCreate['marketplace_currency_id'];
                $item->currency_id = $productCostItemToCreate['currency_id'];
                $item->amount_total = (float)$productCostItemToCreate['amount_per_unit'];
                $item->marketplace_amount_per_unit = (float)$productCostItemToCreate['marketplace_amount_per_unit'];
                $item->marketplace_currency_rate = (float)$productCostItemToCreate['marketplace_currency_rate'];
                $item->save(false);
                $isUpdateItem = true;
            }
        }
        if (!$isUpdateItem) {
            $item = $productCostItem->makeDuplicate();
            $item->product_cost_period_id = $rightPeriod->id;
            $item->save(false);
        }
    }

}
