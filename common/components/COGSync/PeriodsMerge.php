<?php

namespace common\components\COGSync;

use common\components\LogToConsoleTrait;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use Exception;
use yii\base\InvalidConfigException;
use yii\db\Expression;
use yii\db\Query;

class PeriodsMerge
{
    use LogToConsoleTrait;

    public const BATCH_SIZE = 1000;

    private array $dateStartByPeriod = [];

    /**
     * @throws Exception
     */
    public function deleteDuplicatePeriodsAll(): void
    {
        foreach ($this->yieldGroupedKeyBatches() as $groupBatch) {
            $this->processGroupBatch($groupBatch);
        }
    }

    /**
     * @throws Exception
     */
    public function deleteDuplicatePeriods(?array $productCostPeriods = null): void
    {
        if (empty($productCostPeriods)) {
            return;
        }

        $period = $productCostPeriods[0];

        $group = [[
            'marketplace_id' => $period['marketplace_id'],
            'seller_id' => $period['seller_id'],
            'seller_sku' => $period['seller_sku'],
            'sales_category_id' => $period['sales_category_id'],
        ]];

        $this->processGroupBatch($group);
    }

    /**
     * @throws \yii\db\Exception
     * @throws InvalidConfigException
     */
    private function processGroupBatch(array $groupBatch): void
    {
        $periodsUpdate = [];
        $idsDelete = [];

        $transaction = ProductCostPeriod::getDb()->beginTransaction();
        try {
            foreach ($this->yieldAllPeriods($groupBatch) as $period) {
                $periodId = $period['id'];
                $prevPeriodId = $period['prev_period_id'];

                $nextDateStart = $period['next_date_start'];

                if (isset($this->dateStartByPeriod[$periodId])) {
                    $nextDateStart = $this->dateStartByPeriod[$periodId];
                    unset($this->dateStartByPeriod[$periodId]);
                }

                $this->info("Update period {$prevPeriodId} date_end to {$nextDateStart}");
                $this->dateStartByPeriod[$prevPeriodId] = $nextDateStart;

                $periodsUpdate[$prevPeriodId] = [
                    'id' => $prevPeriodId,
                    'marketplace_id' => $period['marketplace_id'],
                    'seller_sku' => $period['seller_sku'],
                    'date_start' => $period['next_date_start'],
                    'amount_total' => $period['amount_total'],
                    'seller_id' => $period['seller_id'],
                    'sales_category_id' => $period['sales_category_id'],
                    'date_end' => $nextDateStart
                ];

                $this->info("Delete period {$periodId}");
                $idsDelete[] = $periodId;
            }

            if (!empty($periodsUpdate)) {
                $updateSql = \Yii::$app->get('dbManager')
                    ->getCustomerDb()
                    ->createCommand()
                    ->batchInsert(
                        ProductCostPeriod::tableName(),
                        array_keys(array_values($periodsUpdate)[0]),
                        $periodsUpdate
                    )->getRawSql();

                ProductCostPeriod::getDb()->createCommand(
                    $updateSql . ' ON CONFLICT (id) DO UPDATE SET date_end = EXCLUDED.date_end'
                )->execute();
            }

            if (!empty($idsDelete)) {
                ProductCostPeriod::deleteAll(['id' => $idsDelete]);
                $this->updatePeriodsDateEnd($idsDelete);
            }

            unset($periodsUpdate, $idsDelete);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function yieldGroupedKeyBatches(int $batchSize = 500): \Generator
    {
        $groups = (new Query())
            ->select(['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id'])
            ->from(ProductCostPeriod::tableName())
            ->groupBy(['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id'])
            ->having(['>', new Expression('COUNT(*)'), 1]);

        $buffer = [];

        foreach ($groups->each(self::BATCH_SIZE, ProductCostPeriod::getDb()) as $group) {
            $buffer[] = $group;
            if (count($buffer) >= $batchSize) {
                yield $buffer;
                $buffer = [];
            }
        }

        if (!empty($buffer)) {
            yield $buffer;
        }
    }

    private function yieldAllPeriods(
        array $groupBatch
    ): array {

        $where = ['or'];
        foreach ($groupBatch as $group) {
            $where[] = $group;
        }

        $periodsWithLag = (new Query())
            ->select([
                'pcp.id',
                'pcp.marketplace_id',
                'pcp.seller_id',
                'pcp.seller_sku',
                'pcp.amount_total',
                'pcp.date_start',
                'pcp.date_end',
                'pcp.sales_category_id',
                'prev_period_id' => new Expression(
                    'LAG(pcp.id) OVER (PARTITION BY pcp.seller_id, pcp.marketplace_id, pcp.seller_sku, pcp.sales_category_id ORDER BY pcp.date_start ASC NULLS FIRST)'
                ),
                'prev_amount_total' => new Expression(
                    'LAG(pcp.amount_total) OVER (PARTITION BY pcp.seller_id, pcp.marketplace_id, pcp.seller_sku, pcp.sales_category_id ORDER BY pcp.date_start ASC NULLS FIRST)'
                ),
                'prev_date_end' => new Expression(
                    'LAG(pcp.date_end) OVER (PARTITION BY pcp.seller_id, pcp.marketplace_id, pcp.seller_sku, pcp.sales_category_id ORDER BY pcp.date_start ASC NULLS FIRST)'
                ),
                'next_date_start' => new Expression(
                    'LEAD(pcp.date_start) OVER (PARTITION BY pcp.seller_id, pcp.marketplace_id, pcp.seller_sku, pcp.sales_category_id ORDER BY pcp.date_start ASC NULLS FIRST)'
                ),
            ])
            ->from(['pcp' => ProductCostPeriod::tableName()]);

        $periodsWithItemsMatch = (new Query())
            ->select([
                'pwl.*',
                'prev_items_match' => new Expression(
                    "CASE
                WHEN NOT EXISTS (
                    SELECT 1
                    FROM (
                        SELECT product_cost_category_id, amount_total, units
                        FROM " . ProductCostItem::tableName() . "
                        WHERE product_cost_period_id = pwl.id
                    ) i1
                        FULL OUTER JOIN (
                        SELECT product_cost_category_id, amount_total, units
                        FROM " . ProductCostItem::tableName() . "
                        WHERE product_cost_period_id = pwl.prev_period_id
                    ) i2
                    ON i1.product_cost_category_id = i2.product_cost_category_id
                        AND i1.amount_total = i2.amount_total
                        AND i1.units = i2.units
                    WHERE i1.product_cost_category_id IS NULL OR i2.product_cost_category_id IS NULL
                ) AND (
                    SELECT COUNT(*)
                    FROM " . ProductCostItem::tableName() . "
                    WHERE product_cost_period_id = pwl.id
                ) = (
                    SELECT COUNT(*)
                    FROM " . ProductCostItem::tableName() . "
                    WHERE product_cost_period_id = pwl.prev_period_id
                )
                THEN 1
                ELSE 0
            END"
                ),
            ])
            ->from(['pwl' => $periodsWithLag]);

        $query =  (new Query())
            ->select([
                'id',
                'marketplace_id',
                'seller_id',
                'seller_sku',
                'amount_total',
                'date_start',
                'date_end',
                'sales_category_id',
                'prev_period_id',
                'prev_amount_total',
                'prev_date_end',
                'next_date_start',
            ])
            ->from(['periods_with_items_match' => $periodsWithItemsMatch])
            ->where([
                'prev_items_match' => 1,
                'amount_total' => new Expression('prev_amount_total'),
            ])
            ->andWhere($where)
            ->orderBy([
                'sales_category_id' => SORT_ASC,
                'date_start' => SORT_DESC,
            ]);

        return $query->all(ProductCostPeriod::getDb());
    }

    private function updatePeriodsDateEnd(array $idsDelete)
    {
        $groupedQuery = (new Query())
            ->select([
                'id',
                'group_count' => new Expression('COUNT(*) OVER (PARTITION BY marketplace_id, seller_id, seller_sku, sales_category_id)')
            ])
            ->from(ProductCostPeriod::tableName());

        $filterQuery = (new Query())
            ->select(['id'])
            ->from(['grouped' => $groupedQuery])
            ->where(['group_count' => 1])
            ->andWhere(['id' => $idsDelete]);

        ProductCostPeriod::getDb()->createCommand()
            ->update(
                ProductCostPeriod::tableName(),
                ['date_end' => null],
                [
                    'and',
                    ['id' => $idsDelete],
                    ['in', 'id', $filterQuery]

                ]
            )
            ->execute();
    }
}
