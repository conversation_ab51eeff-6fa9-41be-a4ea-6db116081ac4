<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dbStructure\TableNameGenerator;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\FbaReturn;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\SalesCategory;
use common\models\Seller;
use yii\db\Expression;

/**
 * Generates and returns corrective transactions for passed list of organic sales transactions
 */
class COGTransactionsGenerator
{
    use LogToConsoleTrait;

    private CurrencyRateManager $currencyRateManager;
    private DbManager $dbManager;
    private TableNameGenerator $tableNameGenerator;
    private CustomerComponent $customerComponent;
    private CustomerConfig $customerConfig;

    public function __construct(CurrencyRateManager $currencyRateManager)
    {
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->currencyRateManager = $currencyRateManager;
        $this->dbManager = \Yii::$app->dbManager;
        $this->tableNameGenerator = \Yii::$container->get('tableNameGenerator');
        $this->customerConfig = \Yii::$container->get("customerConfig");
    }

    /**
     * @param Transaction[] $transactions
     * @param float $amountPerItemNew
     * @param string $currencyCodeNew
     * @param float $amountPerItemOld
     * @param string $currencyCodeOld
     * @param int $productCostCategoryId
     * @return array
     * @throws \Exception
     */
    public function generateByKnownPriceChange(
        array $transactions,
        float $amountPerItemNewOrig,
        string $currencyCodeNewOrig,
        float $amountPerItemOldOrig,
        string $currencyCodeOldOrig,
        int $productCostCategoryId,
        float $orderPrice = null,
        bool $isVatModuleEnabled = null,
        bool $isSellable = null,
        bool $isFba = null
    ): array {
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $vatCategoryId = ProductCostCategory::getVatCategoryId();
        $COGCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_COG);
        $shippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
        $correctiveTransactions = [];
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        $sellableFbaOrders = [];

        if (in_array($productCostCategoryId, $COGCategoryIds)) {
            if (null !== $isSellable) {
                if (!$isSellable) {
                    foreach ($transactions as $transaction) {
                        $key = $transaction->AmazonOrderId . '_' . $transaction->SellerSKU;
                        $sellableFbaOrders[$key] = 1;
                    }
                }
            } else {
                $orderIds = [];
                $skus = [];
                foreach ($transactions as $transaction) {
                    $orderIds[] = $transaction->AmazonOrderId;
                    $skus[] = $transaction->SellerSKU;
                }
                $orderIds = array_unique($orderIds);
                $sellableFbaOrders = FbaReturn::getSellableQuery()
                    ->andWhere(['in', 'order_id', $orderIds])
                    ->andWhere(['in', 'sku', $skus])
                    ->asArray()->all() ?? [];

                foreach ($sellableFbaOrders as $k => $sellableFbaOrder) {
                    $key = $sellableFbaOrder['order_id'] . '_' . $sellableFbaOrder['sku'];
                    $sellableFbaOrders[$key] = $sellableFbaOrder;
                    unset($sellableFbaOrders[$k]);
                }
            }
        }

        foreach ($transactions as $transaction) {
            // Do not apply shipping costs for FBA products
            if (in_array($productCostCategoryId, $shippingCostCategoryIds)) {
                if (null !== $isFba) {
                    if ($isFba) {
                        continue;
                    }
                } else {
                    $seller = Seller::find()->where([
                        'customer_id' => $this->dbManager->getCustomerId(),
                        'id' => $transaction->SellerId
                    ])->cache(60 * 5)->one();

                    if (!empty($seller)) {
                        $this->dbManager->setSellerId($transaction->SellerId);
                        $amazonOrder = AmazonOrder::find()
                            ->where(['amazon_order_id' => $transaction->AmazonOrderId])
                            ->cache(60)
                            ->one();
                        if (!empty($amazonOrder) && $amazonOrder->fulfillment_channel === AmazonOrder::FULFILMENT_CHANNEL_AFN) {
                            continue;
                        }
                    }
                }
            }

            // Prevention of rewriting these variables during foreach loop
            $amountPerItemNew = $amountPerItemNewOrig;
            $currencyCodeNew = $currencyCodeNewOrig;
            $amountPerItemOld = $amountPerItemOldOrig;
            $currencyCodeOld = $currencyCodeOldOrig;

            try {
                if (
                    $productCostCategoryId !== $vatCategoryId
                    &&
                    ((int)$transaction->Quantity === 0 || $transaction->Amount === 0)
                ) {
                    continue;
                }

                // Do not apply any changes because for not sellable FBA orders there are no COG transactions
                if (in_array($productCostCategoryId, $COGCategoryIds)
                    && !isset($sellableFbaOrders[$transaction->AmazonOrderId . '_' . $transaction->SellerSKU])
                    && $transaction->CategoryId === $organicRefundCategoryId
                ) {
                    continue;
                }

                // Vat calculation is outside of rules yet (because it's a percent from transaction amount)
                if ($productCostCategoryId === $vatCategoryId) {
                    // Amazon manages vat by himself.
                    // Do not allow manual changes.
                    $isVatModuleEnabled = $isVatModuleEnabled ?? $this->isVatModuleEnabled($transaction);
                    if ($isVatModuleEnabled) {
                        continue;
                    }

                    $quantity = $transaction->Quantity > 0 ? $transaction->Quantity : 1;
                    $orderPrice = $orderPrice ?? $this->getOrderPrice($transaction);
                    $orderPricePerUnit = ($orderPrice / $quantity / $moneyAccuracy);
                    $amountPerItemOld = $orderPricePerUnit - $orderPricePerUnit / (1 + $amountPerItemOld * 0.01);
                    $amountPerItemNew = $orderPricePerUnit - $orderPricePerUnit / (1 + $amountPerItemNew * 0.01);
                    $currencyCodeOld = $transaction->Currency;
                    $currencyCodeNew = $transaction->Currency;
                }

                if ($currencyCodeNew !== $currencyCodeOld) {
                    $amountDiff = $amountPerItemNew
                        - $this
                            ->currencyRateManager
                            ->convert(
                                $amountPerItemOld,
                                $currencyCodeOld,
                                $currencyCodeNew,
                                new \DateTime($transaction->PostedDate)
                            );
                } else {
                    $amountDiff = $amountPerItemNew - $amountPerItemOld;
                }

                $sign = ((int)$transaction->CategoryId === (int)$organicRefundCategoryId ? 1 : -1);

                $correctiveTransaction = clone $transaction;
                // All custom costs categories are going to expenses as correction.
                $correctiveTransaction->CategoryId = $organicSalesId;
                $correctiveTransaction->COGCategoryId = $productCostCategoryId;
                $correctiveTransaction->Currency = $currencyCodeNew;

                if ($correctiveTransaction->Quantity == 0 && $productCostCategoryId === $vatCategoryId) {
                    $correctiveTransaction->Quantity = 1;
                }

                $quantity = in_array($productCostCategoryId, $shippingCostCategoryIds) ? 1 : $correctiveTransaction->Quantity;
                $correctiveTransaction->Amount = $amountDiff
                    * $quantity
                    * $moneyAccuracy
                    * $sign
                ;
                $correctiveTransaction->Amount = (int)round($correctiveTransaction->Amount);

                $correctiveTransaction->AmountEUR = ($this
                        ->currencyRateManager
                        ->toBaseCurrency(
                            $amountDiff,
                            $currencyCodeNew,
                            new \DateTime($correctiveTransaction->PostedDate)
                        )
                    * $quantity
                    * $moneyAccuracy
                    * $sign
                );
                $correctiveTransaction->AmountEUR = (int)round($correctiveTransaction->AmountEUR);

                $correctiveTransaction->MergeCounter = 1;
                $correctiveTransaction->CategoryId = $transaction->CategoryId;
                $correctiveTransaction->CreatedAt = date(
                    'Y-m-d H:i:s',
                    // Need this to prevent clickhouse deduplication mechanism
                    random_int(strtotime('-1 hour'), time())
                );

                $correctiveTransactions[] = $correctiveTransaction;
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        return $correctiveTransactions;
    }

    /**
     * @param Transaction[] $transactions
     */
    public function generateBulk(
        array $transactions,
        bool $shouldApplyMoneyAccuracy = true,
        array $orderItemPrices = null,
        array $orderItemsWithVATFromAmazon = null,
        array $fbaOrderIds = null,
        array $b2bOrderIds = null,
        array $groupedProductCostItems = null,
        array $manualShippingCostOrderIds = null
    ): array
    {
        if (count($transactions) === 0) {
            return [];
        }

        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundId = FinanceEventCategory::getOrganicRefundId();
        $vatCategoryId = ProductCostCategory::getVatCategoryId();
        $COGCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_COG);
        $shippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
        $otherFeesCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_OTHER_FEES);
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        foreach ($transactions as $k => $transaction) {
            if ($transaction->COGCategoryId > 0
                || !in_array($transaction->CategoryId, [$organicSalesId, $organicRefundId])) {
                unset($transactions[$k]);
            }
        }
        $this->info(sprintf('Generating COG transactions for %d transactions', count($transactions)));

        if (count($transactions) === 0) {
            $this->info('No transactions to process');
            return [];
        }

        $orderIds = [];
        $skus = [];
        $maxPostedDate = time();
        $minPostedDate = time();

        foreach ($transactions as $transaction) {
            $orderIds[] = $transaction->AmazonOrderId;
            $skus[] = $transaction->SellerSKU;
            $postedDate = strtotime($transaction->PostedDate);
            $maxPostedDate = max($maxPostedDate, $postedDate);
            $minPostedDate = min($minPostedDate, $postedDate);
        }
        $maxPostedDate = date('Y-m-d H:i:s', $maxPostedDate + 60 * 60 * 3);
        $minPostedDate = date('Y-m-d H:i:s', $minPostedDate - 60 * 60 * 3);

        $this->info('Fetching not sellable FBA orders started');
        $sellableFbaOrders = FbaReturn::getSellableQuery()
            ->select([
                'concat(order_id, \'_\', sku)'
            ])
            ->andWhere([
                'AND',
                ['in', 'order_id', $orderIds],
                ['in', 'sku', $skus]
            ])
            ->column();
        $this->info('Fetching not sellable FBA orders finished');

        /** @var Seller[] $sellers */
        $sellers = Seller::find()
            ->where(['customer_id' => $this->dbManager->getCustomerId()])
            ->all();
        $sellersWithVCSEnabled = [];
        $shouldFillFBAOrderIds = null === $fbaOrderIds;
        $shouldFillB2BOrderIds = null === $b2bOrderIds;
        $shouldFillManualShippingCostOrderIds = null === $manualShippingCostOrderIds;

        $transactions = array_values($transactions);

        foreach ($sellers as $seller) {
            $this->dbManager->setSellerId($seller->id);
            if ($shouldFillFBAOrderIds) {
                $fbaOrderIds = array_merge($fbaOrderIds ?? [], AmazonOrder::find()
                    ->select([
                        'amazon_order_id',
                    ])
                    ->where([
                        'AND',
                        ['in', 'amazon_order_id', $orderIds],
                        ['>=', 'purchase_date', $minPostedDate],
                        ['<=', 'purchase_date', $maxPostedDate],
                        ['=', 'fulfillment_channel', AmazonOrder::FULFILMENT_CHANNEL_AFN],
                    ])
                    ->asArray()
                    ->column() ?? []
                );
            }

            if ($shouldFillB2BOrderIds) {
                $b2bOrderIds = array_merge($b2bOrderIds ?? [], AmazonOrder::find()
                    ->select([
                        'amazon_order_id',
                    ])
                    ->where([
                        'AND',
                        ['in', 'amazon_order_id', $orderIds],
                        ['>=', 'purchase_date', $minPostedDate],
                        ['<=', 'purchase_date', $maxPostedDate],
                        ['=', 'is_business_order', 't'],
                    ])
                    ->asArray()
                    ->column() ?? []
                );
            }

            if ($shouldFillManualShippingCostOrderIds) {
                $manualShippingCostOrderIds = array_merge($manualShippingCostOrderIds ?? [], AmazonOrder::find()
                    ->select([
                        'amazon_order_id',
                    ])
                    ->where([
                        'AND',
                        ['in', 'amazon_order_id', $orderIds],
                        ['>=', 'purchase_date', $minPostedDate],
                        ['<=', 'purchase_date', $maxPostedDate],
                        ['=', 'has_manual_shipping_cost', true],
                    ])
                    ->asArray()
                    ->column() ?? []
                );
            }
            if ($seller->is_vcs_enabled) {
                $sellersWithVCSEnabled[] = $seller->id;
            }
        }
        $this->dbManager->setSellerId($transactions[0]->SellerId);

        $fbaOrderIds = array_values(array_unique($fbaOrderIds));
        $b2bOrderIds = array_values(array_unique($b2bOrderIds));
        $manualShippingCostOrderIds = array_values(array_unique($manualShippingCostOrderIds));

        if (null === $orderItemsWithVATFromAmazon) {
            $this->info('Fetching order items with VAT from Amazon started');
            $orderItemsWithVATFromAmazon = TransactionBuffer::find()
                ->select(new Expression('concat("AmazonOrderId", \'_\', "SellerSKU") as key'))
                ->where([
                    'AND',
                    ['>=', 'PostedDate', $minPostedDate],
                    ['<=', 'PostedDate', $maxPostedDate],
                    ['in', 'AmazonOrderId', $orderIds],
                    ['in', 'SellerSKU', $skus],
                    ['in', 'CategoryId', FinanceEventCategory::getVatIds()],
                ])
                ->groupBy('AmazonOrderId, SellerSKU')
                ->column();
            $this->info('Fetching order items with VAT from Amazon finished');
        }
        $orderItemsWithVATFromAmazon = array_values(array_unique($orderItemsWithVATFromAmazon));

        if (null === $orderItemPrices) {
            $this->info('Fetching order items prices started');

            $orderItemPrices = TransactionBuffer::find()
                ->select([
                    new Expression('concat("AmazonOrderId", \'_\', "SellerSKU") as key'),
                    'abs(sum(
                    CASE 
                        WHEN (
                            CategoryId IN (' . implode(',', FinanceEventCategory::getOrderPriceIds()) . ')
                            AND 
                            COGCategoryId = 0
                        )
                    THEN "Amount" 
                    ELSE 0 
                    END
                )) as order_price',
                    'abs(sum(
                    CASE 
                        WHEN (
                            CategoryId IN (' . implode(',', FinanceEventCategory::getRefundOrderPriceIds()) . ')
                            AND 
                            COGCategoryId = 0
                        )
                    THEN Amount
                    ELSE 0 END
                )) as order_price_refund',
                    'max(Currency) as currency_code',
                ])
                ->where([
                    'AND',
                    ['>=', 'PostedDate', $minPostedDate],
                    ['<=', 'PostedDate', $maxPostedDate],
                    ['in', 'AmazonOrderId', $orderIds],
                    ['in', 'SellerSKU', $skus],
                ])
                ->indexBy('key')
                ->groupBy('AmazonOrderId, SellerSKU')
                ->asArray()
                ->all();
            $this->info('Fetching order items prices finished');
        }

        // Do not increase chunk size without testing on large customers.
        // Memory limit can be exceeded.
        $transactionChunks = array_chunk($transactions, 50);
        $correctiveTransactions = [];
        $ordersWithAlreadyAppliedVAT = [];

        if (empty($groupedProductCostItems)) {
            $productCostItemFilters = [];
            foreach ($transactionChunks as $chunk) {
                foreach ($chunk as $transaction) {
                    $key = implode('_', [$transaction->MarketplaceId, $transaction->SellerId, $transaction->SellerSKU]);
                    $postedDate = date('Y-m-d', strtotime($transaction->PostedDate));
                    $productCostItemFilters[$postedDate][$key] = [
                        'marketplace_id' => $transaction->MarketplaceId,
                        'seller_id' => $transaction->SellerId,
                        'seller_sku' => $transaction->SellerSKU,
                    ];
                }
            }
            $groupedProductCostItems = ProductCostItem::findSuitableForSellerAndMarketplaceBulk($productCostItemFilters);
        }
        $this->info('Calculation COG transactions started');

        foreach ($transactionChunks as $chunk) {
            $this->info('Chunk');
            $this->info('Memory usage 2: ' . memory_get_peak_usage(true)/1024/1024);
            $this->info(sprintf('Processing chunk of %d transactions', count($chunk)));

            /** @var Transaction[] $chunk */
            foreach ($chunk as $transaction) {
                $key = implode('_', [$transaction->MarketplaceId, $transaction->SellerId, $transaction->SellerSKU]);
                $postedDate = date('Y-m-d', strtotime($transaction->PostedDate));
                $productCostItems = $groupedProductCostItems[$postedDate][$key] ?? [];
                $COGTransactions = [];

                $orderItemKey = implode('_', [$transaction->AmazonOrderId, $transaction->SellerSKU]);
                $isVATAlreadyApplied = isset($ordersWithAlreadyAppliedVAT[$orderItemKey]);

                foreach ($productCostItems as $productCostItem) {
                    $productCostCategoryId = $productCostItem['product_cost_category_id'];
                    if ($productCostCategoryId !== $vatCategoryId
                        && ((int)$transaction->Quantity === 0 || $transaction->Amount === 0)
                    ) {
                        continue;
                    }

                    $isSellableFBAOrder = in_array($orderItemKey, $sellableFbaOrders);

                    $isCostOfGoods = in_array($productCostCategoryId, $COGCategoryIds);
                    $isVat = (int)$productCostCategoryId === (int)$vatCategoryId;
                    $isShippingCost = in_array($productCostCategoryId, $shippingCostCategoryIds);
                    $isOtherFeesCost = in_array($productCostCategoryId, $otherFeesCategoryIds);
                    $isRefundTransaction = $transaction->CategoryId === $organicRefundId;
                    $hasVATFromAmazon = in_array($orderItemKey, $orderItemsWithVATFromAmazon);
                    $isVCSEnabled = in_array($transaction->SellerId, $sellersWithVCSEnabled);
                    $isB2BOrder = in_array($transaction->AmazonOrderId, $b2bOrderIds);

                    // Do not apply any changes because for not sellable FBA orders there are no COG transactions
                    if ($isCostOfGoods
                        && !$isSellableFBAOrder
                        && $isRefundTransaction
                    ) {
                        continue;
                    }

                    $correctiveTransaction = clone $transaction;
                    $correctiveTransaction->COGCategoryId = $productCostCategoryId;
                    // All custom costs categories are going to expenses as correction.
                    $correctiveTransaction->CategoryId = $organicSalesId;
                    $correctiveTransaction->Currency = $productCostItem['marketplace_currency_id'];
                    $correctiveTransaction->Amount = $productCostItem['marketplace_amount_per_unit'];
                    $correctiveTransaction->CreatedAt = date('Y-m-d H:i:s');
                    $correctiveTransaction->EventPeriodId = random_int(1, 10000000);
                    if ($isShippingCost) {
                        $correctiveTransaction->Quantity = 1;
                        $isFBAOrder = in_array($transaction->AmazonOrderId, $fbaOrderIds);
                        $isFBMOrder = !$isFBAOrder;
                        $isManualCostOrder = in_array($transaction->AmazonOrderId, $manualShippingCostOrderIds);

                        // Do not apply shipping costs for FBA products.
                        // Do not apply shipping costs if they were specified manually through UI.
                        // Do not apply shipping costs for refunds of FMB products.
                        if ($isFBAOrder || $isManualCostOrder || ($isFBMOrder && $isRefundTransaction)) {
                            continue;
                        }
                    }

                    // Do not return other fees in case of refund
                    if ($isOtherFeesCost && $isRefundTransaction) {
                        continue;
                    }

                    // Vat calculation is outside of rules yet (because it's a percent from transaction amount)
                    if ($isVat) {
                        $shouldSkipVATCalculation = $hasVATFromAmazon
                            || ($isB2BOrder && $isVCSEnabled)
                            || $isVATAlreadyApplied;

                        if ($shouldSkipVATCalculation) {
                            continue;
                        }

                        $orderItemsPrice = $orderItemPrices[$orderItemKey] ?? null;

                        if (null === $orderItemsPrice) {
                            continue;
                        }

                        $orderPrice = $transaction->CategoryId === $organicRefundId
                            ? $orderItemsPrice['order_price_refund']
                            : $orderItemsPrice['order_price']
                        ;
                        $orderPrice = abs($orderPrice);

                        if ((int)$correctiveTransaction->Quantity === 0) {
                            $correctiveTransaction->Quantity = 1;
                        }
                        $orderPrice = $orderPrice / $correctiveTransaction->Quantity;
                        $correctiveTransaction->Amount = $orderPrice - $orderPrice / (1 + abs($productCostItem['amount_per_unit']) * 0.01);
                        $ordersWithAlreadyAppliedVAT[$orderItemKey] = 1;
                        if ($productCostItem['amount_per_unit'] < 0) {
                            $correctiveTransaction->Amount *= -1;
                        }
                    }
                    $sign = ((int)$transaction->CategoryId === (int)$organicRefundId ? 1 : -1);
                    $correctiveTransaction->Amount *= $sign;
                    $correctiveTransaction->Amount *= $correctiveTransaction->Quantity;
                    $correctiveTransaction->AmountEUR = $this
                        ->currencyRateManager
                        ->toBaseCurrency(
                            $correctiveTransaction->Amount,
                            $correctiveTransaction->Currency,
                            new \DateTime($correctiveTransaction->PostedDate)
                        );

                    if ($shouldApplyMoneyAccuracy) {
                        // Vat is already calculated with taking into account money accuracy
                        if ($isVat) {
                            $correctiveTransaction->Amount = (int)round($correctiveTransaction->Amount);
                            $correctiveTransaction->AmountEUR = (int)round($correctiveTransaction->AmountEUR);
                        } else {
                            $correctiveTransaction->Amount = (int)round($correctiveTransaction->Amount * $moneyAccuracy);
                            $correctiveTransaction->AmountEUR = (int)round($correctiveTransaction->AmountEUR * $moneyAccuracy);
                        }
                    }

                    $COGTransactions[] = $correctiveTransaction;
                }
                $correctiveTransactions = array_merge($correctiveTransactions, $COGTransactions);
            }
        }
        $this->info(sprintf('Generated %d COG transactions', count($correctiveTransactions)));
        return $correctiveTransactions;
    }

    public function getOrderPrice(Transaction $transaction): float
    {
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $categoryIds = (int)$transaction->CategoryId === (int)$organicRefundCategoryId
            ? FinanceEventCategory::getRefundOrderPriceIds()
            : FinanceEventCategory::getOrderPriceIds();

        $tableName = \common\models\customer\clickhouse\Transaction::tableName();

        $dateStart = (new \DateTime($transaction->PostedDate))->format('Y-m-d 00:00:00');
        $dateEnd = (new \DateTime($transaction->PostedDate))->format('Y-m-d 23:59:59');

        $orderPrice = $this
            ->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand("
                SELECT sum(Amount)
                FROM {$tableName}
                WHERE (PostedDate BETWEEN toDateTime(:date_start) AND toDateTime(:date_end))
                AND AmazonOrderId = :amazon_order_id
                AND SellerSKU = :seller_sku
                AND CategoryId IN (" . implode(',', $categoryIds) . ")
                AND COGCategoryId = 0
            ",
            [
                'seller_sku' => $transaction->SellerSKU,
                'amazon_order_id' => $transaction->AmazonOrderId,
                'date_start' => $dateStart,
                'date_end' => $dateEnd,
            ]
        );
        $orderPrice = $orderPrice->queryScalar() ?? 0;

        return (float)abs($orderPrice);
    }

    protected function isVatModuleEnabled(Transaction $transaction): bool
    {
        /** @var Seller $seller */
        $seller = Seller::find()->where([
            'customer_id' => $this->dbManager->getCustomerId(),
            'id' => $transaction->SellerId
        ])->cache(60 * 5)->one();

        if (!empty($seller)) {
            $this->dbManager->setSellerId($seller->id);
            $order = AmazonOrder::find()
                ->where(['amazon_order_id' => $transaction->AmazonOrderId])
                ->cache(60)
                ->one();

            if (!empty($order) && $order->is_business_order) {
                return !!$seller->is_vcs_enabled;
            }
        }

        $cacheKey = 'vat_transactions_for_sku_'.$transaction->SellerSKU.'_'.(new \DateTime($transaction->PostedDate))->format('YW');
        $ordersWithVatTransactions = \Yii::$app->cache->get($cacheKey);

        if (false === $ordersWithVatTransactions) {
            $vatCategoryIds = FinanceEventCategory::getVatIds();

            $dateStart = (new \DateTime($transaction->PostedDate))->modify('Monday this week')->format('Y-m-d 00:00:00');
            $dateEnd = (new \DateTime($transaction->PostedDate))->modify('Sunday this week')->format('Y-m-d 23:59:59');

            $tableName = \common\models\customer\clickhouse\Transaction::tableName();
            $ordersWithVatTransactions = $this->dbManager->getClickhouseCustomerDb()->createCommand("
                SELECT DISTINCT AmazonOrderId
                FROM {$tableName}
                WHERE (PostedDate BETWEEN toDateTime(:date_start) AND toDateTime(:date_end))
                AND CategoryId IN (" . implode(',', $vatCategoryIds) . ")
                AND SellerSKU = :seller_sku
            ",
                [
                    'date_start' => $dateStart,
                    'date_end' => $dateEnd,
                    'seller_sku' => $transaction->SellerSKU
                ]
            )->queryColumn();

            \Yii::$app->cache->set($cacheKey, $ordersWithVatTransactions, 10 * 60);
        }

        return in_array($transaction->AmazonOrderId, $ordersWithVatTransactions);
    }
}
