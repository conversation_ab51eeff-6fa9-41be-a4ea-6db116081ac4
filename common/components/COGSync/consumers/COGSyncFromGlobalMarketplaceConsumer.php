<?php

namespace common\components\COGSync\consumers;

use common\components\COGSync\ProductToProductSynchronizer;
use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class COGSyncFromGlobalMarketplaceConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    private DbManager $dbManager;

    private ProductToProductSynchronizer $periodsSynchronizer;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->periodsSynchronizer = new ProductToProductSynchronizer();
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;

        $customerId = $message['customerId'] ?? null;
        $sellerSku = $message['sku'] ?? null;
        $sellerId = $message['sellerId'] ?? null;
        $marketplaceId = $message['marketplaceId'] ?? null;

        try {
            $this->dbManager->setCustomerId($customerId);
            $this->periodsSynchronizer->syncWithGlobalMarketplace($sellerId, $sellerSku, $marketplaceId);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        return ConsumerInterface::MSG_ACK;
    }
}