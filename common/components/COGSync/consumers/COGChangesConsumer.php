<?php

namespace common\components\COGSync\consumers;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\COGSync\COGChangesManager;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\models\Command;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\TransactionExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class COGChangesConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    private COGChangesManager $COGChangesManager;

    private CustomerConfig $customerConfig;

    private DbManager $dbManager;

    public function __construct()
    {
        $this->COGChangesManager = \Yii::$container->get("COGChangesManager");
        $this->customerConfig = \Yii::$container->get("customerConfig");
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 30));
        $this->info($msg->body);

        if (isset($msg->body['bulk'])) {
            $messages = $msg->body['bulk'];
        } else {
            $messages = [$msg->body];
        }

        foreach ($messages as $message) {
            try {
                $this->dbManager->setCustomerId($message['customerId']);

                $this->processSingle($message);
            } catch (SellerNotFoundException $e) {
                return ConsumerInterface::MSG_ACK;
            } catch (\Throwable $e) {
                $this->error($e);
                sleep(2);
                return ConsumerInterface::MSG_REQUEUE;
            }
        }

        return ConsumerInterface::MSG_ACK;
    }

    private function processSingle(array $message): int
    {
        $currencyCodeOld = $message['currencyCodeOld'] ?? null;
        $amountPerItemOld = $message['amountPerItemOld'] ?? null;
        $amountPerItemNew = $message['amountPerItemNew'] ?? null;
        $currencyCodeNew = $message['currencyCodeNew'] ?? null;
        $productCostItemId = $message['productCostItemId'] ?? null;
        $customerId = $message['customerId'] ?? null;
        $dateStart = $message['dateStart'] ?? null;
        $dateEnd = $message['dateEnd'] ?? null;
        $sellerSku = $message['sellerSku'] ?? null;
        $sellerId = $message['sellerId'] ?? null;
        $marketplaceId = $message['marketplaceId'] ?? null;
        $productCostCategoryId = $message['productCostCategoryId'] ?? null;
        $createdAt = $message['createdAt'] ?? null;
        $isManual = $message['isManual'] ?? false;

        $this->dbManager->setCustomerId($customerId);

        $this->COGChangesManager->applyCOGChanges(
            $amountPerItemOld,
            $currencyCodeOld,
            $amountPerItemNew,
            $currencyCodeNew,
            $productCostItemId,
            $customerId,
            $dateStart,
            $dateEnd,
            $createdAt,
            $sellerSku,
            $marketplaceId,
            $sellerId,
            $productCostCategoryId
        );

        if ($isManual) {
            $this->onAfterManualChange($customerId);
        }

        return ConsumerInterface::MSG_ACK;
    }

    protected function onAfterManualChange(int $customerId)
    {
        try {
            Command::create("transaction-buffer/force-flush $customerId", 1, true);

            $this
                ->dbManager->getClickhouseCustomerDb()
                ->createCommand("OPTIMIZE TABLE " . TransactionExtendedViewV1::tableName())
                ->execute();

            $manager = new DynamicTablesManager();
            $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
            $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}
