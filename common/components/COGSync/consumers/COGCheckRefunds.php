<?php

namespace common\components\COGSync\consumers;

use common\components\COGSync\COGChangesManager;
use common\components\COGSync\DelayedRefundCOGApplier;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\finance\clickhouse\Transaction;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use yii\db\Query;
use yii\mutex\Mutex;

class COGCheckRefunds extends BaseConsumer
{
    protected const BATCH_SIZE = 200;

    private Mutex $mutex;
    private DbManager $dbManager;
    private COGChangesManager $COGChangesManager;
    private DelayedRefundCOGApplier $delayedRefundCOGApplier;
    private CustomerConfig $customerConfig;

    public function __construct()
    {
        $this->mutex = \Yii::$app->mutex;
        $this->dbManager = \Yii::$app->dbManager;
        $this->COGChangesManager = \Yii::$container->get('COGChangesManager');
        $this->delayedRefundCOGApplier = new DelayedRefundCOGApplier();
        $this->customerConfig = \Yii::$container->get('customerConfig');
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;
        $customerId = $message['customerId'];

        try {
            $this->dbManager->setCustomerId($customerId);
        } catch (SellerNotFoundException $exception){
            return ConsumerInterface::MSG_ACK;
        }

        if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
            return $this->__executeV2($msg);
        }

        $lockKey = "cog_check_refunds_consumer_{$customerId}";
        if (!$this->mutex->acquire($lockKey)) {
            $this->info("Already running in another consumer");
            return ConsumerInterface::MSG_ACK;
        }

        /** @var Transaction[] $refundTransactions */
        foreach ($this->iterateRefunds() as $refunds) {
            try {
                $this->COGChangesManager->checkNewRefundsAndApplyIfFoundSalesTransaction($refunds);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        try {
            $this->COGChangesManager->checkAndApplySellableFBAReturns();
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->mutex->release($lockKey);
        return ConsumerInterface::MSG_ACK;
    }

    public function __executeV2(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;
        $customerId = $message['customerId'];

        $lockKey = "cog_check_refunds_consumer_{$customerId}";
        if (!$this->mutex->acquire($lockKey)) {
            $this->info("Already running in another consumer");
            return ConsumerInterface::MSG_ACK;
        }

        $this->dbManager->setCustomerId($customerId);

        try {
            $this->delayedRefundCOGApplier->checkAndApply();
        } catch (\Throwable $e) {
            $this->error($e);
        }

        try{
            $this->COGChangesManager->checkAndApplySellableFBAReturns();
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->mutex->release($lockKey);
        return ConsumerInterface::MSG_ACK;
    }

    private function iterateRefunds(): \Iterator
    {
        $dateStart = date('Y-m-d H:i:s', strtotime('-24 hours'));
        $dateEnd = date('Y-m-d H:i:s', strtotime('-1 minute'));

        $query = (new Query())
            ->select('*')
            ->from(RefundTransactionWithoutProductCost::tableName())
            ->where([
                'AND',
                ['<=', 'created_at', $dateEnd],
                ['>=', 'created_at', $dateStart]
            ])
            ->addOrderBy('id asc');
        ;

        $lastId = null;
        for ($i = 0; $i < 30; $i++) {
            if (null !== $lastId) {
                $query->andWhere(['>', 'id', $lastId]);
            }

            $refunds = $query->noCache()->limit(self::BATCH_SIZE)->all();

            if (count($refunds) === 0) {
                break;
            }

            yield $refunds;

            $lastId = end($refunds)['id'] ?? null;
            $usleep = random_int(300000, 1000000);
            $this->info("Sleeping for {$usleep} microseconds");
            usleep($usleep);
        }
    }
}
