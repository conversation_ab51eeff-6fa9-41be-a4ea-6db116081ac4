<?php

namespace common\components\COGSync\consumers;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class COGChangesBulkConsumer extends BaseConsumer implements ProcessAwareConsumerInterface
{
    private MessagesSender $messagesSender;

    public function __construct()
    {
        $this->messagesSender = new MessagesSender();
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 30));
        $this->info($msg->body);

        if (!isset($msg->body['bulk'])) {
            return ConsumerInterface::MSG_ACK;
        }

        $messages = $msg->body['bulk'];
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        if (empty($messages)) {
            return ConsumerInterface::MSG_ACK;
        }

        try{
            $dbManager->setCustomerId($messages[0]['customerId']);
        }catch (SellerNotFoundException $exception){
            return ConsumerInterface::MSG_ACK;
        }

        foreach ($messages as $message) {
            $productCostItem = new ProductCostItem();
            $productCostItem->id = $message['productCostItemId'];
            $productCostItem->product_cost_category_id = $message['productCostCategoryId'];
            $productCostPeriod = new ProductCostPeriod();
            $productCostPeriod->marketplace_id = $message['marketplaceId'];
            $productCostPeriod->seller_id = $message['sellerId'];
            $productCostPeriod->seller_sku = $message['sellerSku'];

            $this->messagesSender->COGChanges(
                $message['amountPerItemOld'],
                $message['currencyCodeOld'],
                $message['amountPerItemNew'],
                $message['currencyCodeNew'],
                $message['dateStart'],
                $message['dateEnd'],
                $productCostItem,
                $productCostPeriod
            );
        }

        return ConsumerInterface::MSG_ACK;
    }
}
