<?php

namespace common\components\COGSync\consumers;

use common\components\COGSync\COGSynchronizer;
use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\rabbitmq\consumers\BaseConsumer;
use common\components\rabbitmq\consumers\ProcessAwareConsumerInterface;
use mikemadisonweb\rabbitmq\components\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;

class COGSync extends BaseConsumer implements ProcessAwareConsumerInterface
{
    private DbManager $dbManager;
    private COGSynchronizer $COGSynchronizer;

    public function __construct()
    {
        ini_set('memory_limit', '256M');

        $this->dbManager = \Yii::$app->dbManager;
        /** @var COGSynchronizer COGSynchronizer */
        $this->COGSynchronizer = \Yii::$container->get('COGSynchronizer');
    }

    public function __execute(AMQPMessage $msg)
    {
        $this->info(str_repeat('-', 50));
        $this->info($msg->body);
        $message = $msg->body;

        $customerId = $message['customerId'];
        $fromId = $message['fromId'];
        $toId = $message['toId'];
        $isFirstSync = $message['isFirstSync'];
        $shouldSendCOGChanges = $message['shouldSendCOGChanges'];
        try{
            $this->dbManager->setCustomerId($customerId);
        }catch (SellerNotFoundException $exception){
            return ConsumerInterface::MSG_ACK;
        }

        try {
            $this->COGSynchronizer->synchronizeChunk($customerId, $fromId, $toId, $isFirstSync, $shouldSendCOGChanges);
        } catch (\Throwable $e) {
            $this->error($e);
            if (false !== strpos($e->getMessage(), 'server has gone away')) {
                $this->info('Wait a little bit and requeue');
                sleep(5);
                return ConsumerInterface::MSG_REQUEUE;
            }
        }

        return ConsumerInterface::MSG_ACK;
    }
}
