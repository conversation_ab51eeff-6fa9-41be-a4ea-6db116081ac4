<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\BufferFactory;
use common\components\LogToConsoleTrait;
use common\models\customer\ProductCostCategory;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\db\Query;

class DelayedRefundCOGApplier
{
    protected const BATCH_SIZE = 500;

    use LogToConsoleTrait;

    private BufferFactory $bufferFactory;
    private DbManager $dbManager;
    private COGTransactionsGenerator $COGTransactionsGenerator;
    private CustomerConfig $customerConfig;
    private CustomerComponent $customerComponent;

    public function __construct() {
        $this->bufferFactory = new BufferFactory();
        $this->dbManager = \Yii::$app->dbManager;
        $this->COGTransactionsGenerator = \Yii::$container->get('COGTransactionsGenerator');;
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->customerComponent = new CustomerComponent();
    }

    public function checkAndApply(): void
    {
        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen, skipping');
            return;
        }

        /** @var Transaction[] $refundTransactions */
        foreach ($this->iterateRefunds() as $refunds) {
            try {
                $this->apply($refunds);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function apply(array $refunds)
    {
        $this->info(sprintf("Checking %s refunds", count($refunds)));

        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $shippingCOGCategories = ProductCostCategory::getAllBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
        $shippingCategoryIds = [];
        foreach ($shippingCOGCategories as $shippingCOGCategory) {
            $shippingCategoryIds[] = $shippingCOGCategory['id'];
        }

        $orderIds = [];
        $skus = [];
        $maxPostedDate = time();
        $minPostedDate = time();

        foreach ($refunds as $refund) {
            $orderIds[] = $refund['amazon_order_id'];
            $skus[] = $refund['seller_sku'];
            $postedDate = strtotime($refund['posted_date']);
            $maxPostedDate = max($maxPostedDate, $postedDate);
            $minPostedDate = min($minPostedDate, $postedDate);
        }
        $maxPostedDate = date('Y-m-d H:i:s', $maxPostedDate + 60 * 60 * 3);
        $minPostedDate = date('Y-m-d H:i:s', $minPostedDate - 60 * 60 * 3);
        $orderIds = array_unique($orderIds);

        $alreadyAppliedShippingCosts = \common\models\customer\clickhouse\TransactionBuffer::find()
            ->select([
                'concat(AmazonOrderId, \'_\', SellerSKU) as key',
            ])
            ->where([
                'AND',
                ['>=', 'PostedDate', $minPostedDate],
                ['<=', 'PostedDate', $maxPostedDate],
                ['in', 'AmazonOrderId', $orderIds],
                ['in', 'SellerSKU', $skus],
                ['=', 'CategoryId', $organicRefundCategoryId],
                ['in', 'COGCategoryId', $shippingCategoryIds],
            ])
            ->groupBy('AmazonOrderId, SellerSKU, COGCategoryId')
            ->having(['>', 'count(*)', 0])
            ->column();
        $alreadyAppliedShippingCosts = array_values($alreadyAppliedShippingCosts);

        $haveOrganicRefundTransactions = \common\models\customer\clickhouse\TransactionBuffer::find()
            ->select([
                'concat(AmazonOrderId, \'_\', SellerSKU) as key'
            ])
            ->where([
                'AND',
                ['>=', 'PostedDate', $minPostedDate],
                ['<=', 'PostedDate', $maxPostedDate],
                ['in', 'AmazonOrderId', $orderIds],
                ['in', 'SellerSKU', $skus],
                ['in', 'CategoryId', $organicRefundCategoryId],
                ['=', 'COGCategoryId', 0],
            ])
            ->groupBy('AmazonOrderId, SellerSKU')
            ->asArray()
            ->column();

        $refundIdsToRemove = [];
        foreach ($refunds as $k => $refund) {
            $key = implode('_', [
                $refund['amazon_order_id'],
                $refund['seller_sku']
            ]);
            if (!in_array($key, $haveOrganicRefundTransactions)) {
                $this->info(sprintf('Refund %s does not have organic refund transaction yet', $refund['ids']));
                unset($refunds[$k]);
                continue;
            }

            if (in_array($key, $alreadyAppliedShippingCosts)) {
                $this->info(sprintf('Refund %s already has applied shipping cost', $refund['ids']));
                $refundIdsToRemove = array_merge($refundIdsToRemove, explode(',', $refund['ids']));
                unset($refunds[$k]);
                continue;
            }
            $refundIdsToRemove = array_merge($refundIdsToRemove, explode(',', $refund['ids']));
            $alreadyAppliedShippingCosts[] = $key;
        }

        $refundTransactions = $this->convertRefundsToRefundTransactions($refunds);
        $this->info(sprintf('Refund transactions count: %s', count($refundTransactions)));
        $this->info('Memory usage: ' . memory_get_peak_usage(true)/1024/1024);

        $correctiveTransactions = $this->COGTransactionsGenerator->generateBulk($refundTransactions);
        $this->info(sprintf('Corrective transactions count: %s', count($correctiveTransactions)));

        $shippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
        foreach ($correctiveTransactions as $k => $correctiveTransaction) {
            if (!in_array($correctiveTransaction->COGCategoryId, $shippingCostCategoryIds)) {
                unset($correctiveTransactions[$k]);
            }
        }
        $correctiveTransactions = array_values($correctiveTransactions);
        $this->info(sprintf('Corrective shipping transactions count: %s', count($correctiveTransactions)));

        if (!empty($correctiveTransactions)) {
            $this->info(sprintf("Putting %s corrective transactions into buffer", count($correctiveTransactions)));
            $transactionsBuffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
            $transactionsBuffer->put($correctiveTransactions);
            $transactionsBuffer->flush();
        }

        if (!empty($refundIdsToRemove)) {
            $this->info(sprintf("Removing %s refunds", count($refundIdsToRemove)));
            $this->dbManager->getCustomerDb()->createCommand()->delete(
                RefundTransactionWithoutProductCost::tableName(), [
                'id' => $refundIdsToRemove
            ])->execute();
            $this->info(sprintf("Removing %s refunds finished", count($refundIdsToRemove)));
        }
    }

    private function convertRefundsToRefundTransactions(array $refunds): array
    {
        $refundTransactions = [];
        foreach ($refunds as $refund) {
            $transaction = new Transaction($refund);
            $transaction->TransactionDate = $transaction->PostedDate;
            $transaction->SellerSKU = $refund['seller_sku'];
            $transaction->ASIN = $refund['asin'];
            $refundTransactions[] = $transaction;
        }
        return $refundTransactions;
    }

    private function iterateRefunds(): \Iterator
    {
        $dateStart = date('Y-m-d H:i:s', strtotime('-60 days'));
        $dateEnd = date('Y-m-d H:i:s', strtotime('-1 minute'));

        $query = RefundTransactionWithoutProductCost::find()
            ->select([
                "string_agg(id::text, ',') as ids",
                'max(posted_date) as posted_date',
                'max(marketplace_id) as marketplace_id',
                'max(seller_id) as seller_id',
                'seller_sku',
                'max(asin) as asin',
                'max(category_id) as category_id',
                'sum(amount) as amount',
                'max(currency) as currency',
                'sum(quantity) as quantity',
                'amazon_order_id',
                'max(seller_order_id) as seller_order_id'
            ])
            ->where([
                'AND',
                ['<=', 'created_at', $dateEnd],
                ['>=', 'created_at', $dateStart]
            ])
            ->groupBy('amazon_order_id, seller_sku')
            ->asArray();

        $lastId = null;
        for ($i = 0; $i < 30; $i++) {
            if (null !== $lastId) {
                $query->andWhere(['>', 'id', $lastId]);
            }

            $refunds = $query->noCache()->limit(self::BATCH_SIZE)->all();

            if (count($refunds) === 0) {
                break;
            }

            yield $refunds;

            $lastId = end($refunds)['id'] ?? null;
            $usleep = random_int(300000, 1000000);
            $this->info("Sleeping for {$usleep} microseconds");
            usleep($usleep);
        }
    }
}
