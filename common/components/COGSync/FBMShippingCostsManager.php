<?php

namespace common\components\COGSync;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\db\ClickhouseDbHelper;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrder;
use common\models\SalesCategory;

/**
 * Used for recalculating FBM shipping costs when COG changes are applied.
 * Works like conveyor on a factory with workRawStructure as common object on this conveyor.
 */
class FBMShippingCostsManager
{
    use LogToConsoleTrait;

    protected CurrencyRateManager $currencyRateManager;
    protected CustomerComponent $customerComponent;
    protected DbManager $dbManager;
    protected ClickhouseDbHelper $clickhouseDbHelper;

    public function __construct()
    {
        $this->customerComponent = \Yii::$app->customerComponent;
        $this->currencyRateManager = new CurrencyRateManager();
        $this->dbManager = \Yii::$app->dbManager;
        $this->clickhouseDbHelper = new ClickhouseDbHelper();
    }

    /**
     * Recalculates FBM shipping costs for given orders.
     *
     * @param array $amazonOrderItems Array of AmazonOrderItem as array
     */
    public function recalculateForOrders(array $amazonOrderItems): void
    {
        $this->info('Recalculating FBM shipping costs for orders started:');
        $this->info([
            'countAmazonOrderItems' => count($amazonOrderItems)
        ]);

        // Filtering passed amazon order items - removing not suitable to apply FBM shipping costs.
        $amazonOrderItems = $this->filterAmazonOrderItems($amazonOrderItems);

        if (empty($amazonOrderItems)) {
            return;
        }

        $filtersForm = $this->buildFiltersForm($amazonOrderItems);

        /** @var COGChangesManager $COGChangesManger */
        $COGChangesManger = \Yii::$container->get('COGChangesManager');
        $FBMShippingCostTransactionsIterator = $COGChangesManger
            ->iterateOrganicSalesTransactionBatches(
                $filtersForm,
                date('Y-m-d H:i:s'),
                ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS),
                false
            );
        $FBMShippingCostTransactionsIteratorAsArray = iterator_to_array($FBMShippingCostTransactionsIterator);

        $FBMShippingCostTransactions = [
            'transactions' => [],
            'orderBasedTransactions' => []
        ];

        foreach ($FBMShippingCostTransactionsIteratorAsArray as $iteratedResult) {
            $FBMShippingCostTransactions['transactions'] = array_merge(
                $FBMShippingCostTransactions['transactions'],
                $iteratedResult['transactions']
            );
            $FBMShippingCostTransactions['orderBasedTransactions'] = array_merge(
                $FBMShippingCostTransactions['orderBasedTransactions'],
                $iteratedResult['orderBasedTransactions']
            );
        }

        $this->info('Found FBM shipping cost transactions for this order items:');
        $this->info([
            'countRegular' => count($FBMShippingCostTransactions['transactions']),
            'countOrderBased' => count($FBMShippingCostTransactions['orderBasedTransactions'])
        ]);

        // Getting product cost items bulk for given order items
        $productCostItemsGrouped = $this->getProductCostItemsForOrderItems($amazonOrderItems);

        // Generating base work structure which will be used in whole process
        $workRawStructure = $this->getWorkRawStructure($amazonOrderItems);

        // Filling work structure with maximum FBM shipping cost for each amazon orders
        $workRawStructure = $this->fillFBMShippingCostsShouldBeAppliedPerAmazonOrder(
            $productCostItemsGrouped,
            $workRawStructure
        );

        // Filling work structure with actual amounts given from transactions.
        // This amounts will be used to determine if we need to generate corrective transactions.
        $workRawStructure = $this->fillActualAmountsPerProductCostCategory(
            $FBMShippingCostTransactions,
            $workRawStructure
        );

        // Based on product cost items filling expected amounts for each product cost category per each amazon order
        $workRawStructure = $this->fillExpectedAmountsPerProductCostCategory($workRawStructure);

        // Based on all collected data generating corrective transactions
        $correctiveTransactions = $this->generateCorrectiveTransactions($workRawStructure);

        // Saving corrective transactions
        $this->saveCorrectiveTransactions($correctiveTransactions);
    }

    /**
     * Saves collected corrective transactions to necessary clickhouse tables.
     *
     * @param array $correctiveTransactions
     * @return void
     * @throws \Throwable
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\db\Exception
     */
    protected function saveCorrectiveTransactions(array $correctiveTransactions): void
    {
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        // Saving transactions to regular persistent table
        if (count($correctiveTransactions['transactions']) > 0) {
            $transactions = Transaction::convertTransactionsToArray($correctiveTransactions['transactions']);

            $this->clickhouseDbHelper->insertAssocBulk(
                \common\models\customer\clickhouse\Transaction::tableName(),
                $transactions,
                $clickhouseCustomerDb
            );
        }

        // Saving transactions to order based table
        if (count($correctiveTransactions['orderBasedTransactions']) > 0) {
            $transactions = Transaction::convertTransactionsToArray($correctiveTransactions['transactions']);

            $this->clickhouseDbHelper->insertAssocBulk(
                OrderBasedTransaction::tableName(),
                $transactions,
                $clickhouseCustomerDb
            );
        }
    }

    /**
     * Generates and returns corrective transactions that should be applied.
     *
     * @param array $workRawStructure
     * @return array Array of corrective transactions should be applied
     *
     * @throws \Exception
     */
    protected function generateCorrectiveTransactions(array $workRawStructure): array
    {
        $this->info('Generating corrective transactions started');

        $result = [
            'transactions' => [],
            'orderBasedTransactions' => []
        ];

        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();

        foreach ($workRawStructure as $amazonOrderId => &$item) {
            foreach ($item['expectedAmountsEURPerProductCostCategory'] as $productCostCategoryId => $expectedAmountEUR) {
                $actualAmountEUR = $item['actualAmountsEURPerProductCostCategory'][$productCostCategoryId] ?? 0;
                $amountDiffEUR = $expectedAmountEUR - $actualAmountEUR;

                // No difference, nothing to do
                if ($amountDiffEUR === 0) {
                    $item['actualTransactionsPerProductCostCategory'][$productCostCategoryId] = [];
                    continue;
                }

                $transactionsType = $item['isOrderBasedTransactions'] ? 'orderBasedTransactions' : 'transactions';
                $existingTransactionsPerProductCostCategory = $item['actualTransactionsPerProductCostCategory'][$productCostCategoryId] ?? [];

                /** @var Transaction $existingTransaction */
                foreach ($existingTransactionsPerProductCostCategory as $k => $existingTransaction) {
                    // If we have difference in some product cost category, first we need to null all existing transactions
                    // for this amazon order and product cost category.

                    if ($existingTransaction->Amount === 0) {
                        unset($item['actualTransactionsPerProductCostCategory'][$productCostCategoryId][$k]);
                        continue;
                    }

                    $result[$transactionsType][] = Transaction::createOppositeTransaction($existingTransaction);
                    unset($item['actualTransactionsPerProductCostCategory'][$productCostCategoryId][$k]);
                }

                foreach ($item['FBMProductCostItemsShouldBeApplied'] as $productCostItemShouldBeApplied) {
                    if ($productCostItemShouldBeApplied['product_cost_category_id'] != $productCostCategoryId) {
                        continue;
                    }

                    $amountPerSKU = $productCostItemShouldBeApplied['amount_per_unit'] / count($item['skus']);
                    $amountEURPerSKU = $this->currencyRateManager->toBaseCurrency(
                        $productCostItemShouldBeApplied['amount_per_unit'],
                        $productCostItemShouldBeApplied['currency_id'],
                        new \DateTime($item['orderPurchaseDate'])
                    ) / count($item['skus']);

                    // Apply this cost evenly for all items in order
                    foreach ($item['skus'] as $sku) {
                        $correctiveTransaction = new Transaction();
                        $correctiveTransaction->PostedDate = $item['orderPurchaseDate'];
                        $correctiveTransaction->TransactionDate = $item['orderPurchaseDate'];
                        $correctiveTransaction->CreatedAt = date('Y-m-d H:i:s');
                        $correctiveTransaction->Amount = (int)($amountPerSKU * $moneyAccuracy * -1);
                        $correctiveTransaction->AmountEUR = (int)($amountEURPerSKU * $moneyAccuracy * -1);
                        $correctiveTransaction->Currency = $productCostItemShouldBeApplied['currency_id'];
                        $correctiveTransaction->ASIN = $item['asin'];
                        $correctiveTransaction->MergeCounter = 1;
                        $correctiveTransaction->AmazonOrderId = $amazonOrderId;
                        $correctiveTransaction->SellerSKU = $sku;
                        $correctiveTransaction->MarketplaceId = $item['marketplaceId'];
                        $correctiveTransaction->SellerId = $item['sellerId'];
                        $correctiveTransaction->COGCategoryId = $productCostCategoryId;
                        $correctiveTransaction->CategoryId = $organicSalesCategoryId;
                        $correctiveTransaction->Quantity = 1;
                        $result[$transactionsType][] = $correctiveTransaction;
                    }
                }
            }

            // All transactions that left after previous steps should be removed (set to 0) because they are excess.
            foreach ($item['actualTransactionsPerProductCostCategory'] as $productCostCategoryId => $existingTransactions) {
                $transactionsType = $item['isOrderBasedTransactions'] ? 'orderBasedTransactions' : 'transactions';

                foreach ($existingTransactions as $existingTransaction) {
                    if ($existingTransaction->Amount === 0) {
                        continue;
                    }

                    $result[$transactionsType][] = Transaction::createOppositeTransaction($existingTransaction);
                }
            }
        }

        $this->info('Generating corrective transactions finished');
        $this->info([
            'countRegular' => count($result['transactions']),
            'countOrderBased' => count($result['orderBasedTransactions'])
        ]);

        return $result;
    }

    /**
     * Will fill work raw structure with product cost items that should be applied for given amazon order.
     * Chooses maximum amount of FBM shipping cost that should be applied for given amazon order.
     *
     * @param array $productCostItemsGrouped
     * @param array $workRawStructure
     * @return array
     * @throws \Exception
     */
    protected function fillFBMShippingCostsShouldBeAppliedPerAmazonOrder(
        array $productCostItemsGrouped,
        array $workRawStructure
    ): array
    {
        $this->info('Filling FBM shipping costs that should be applied per amazon order started');

        foreach ($workRawStructure as $amazonOrderId => $item) {
            $maxFBMShippingCostAmountEUR = 0;

            foreach ($item['skus'] as $sku) {
                $productCostKey = implode(
                    '_', [
                        $item['marketplaceId'],
                        $item['sellerId'],
                        $sku
                    ]);
                $productCostItems = $productCostItemsGrouped[$item['orderPurchaseDate']][$productCostKey] ?? [];

                if (count($productCostItems) === 0) {
                    continue;
                }

                $amountEUR = 0;
                foreach ($productCostItems as $productCostItem) {
                    $amountEUR += $this->currencyRateManager->toBaseCurrency(
                        $productCostItem['amount_per_unit'],
                        $productCostItem['currency_id'],
                        new \DateTime($item['orderPurchaseDate'])
                    );
                }

                if ($maxFBMShippingCostAmountEUR <= $amountEUR) {
                    $workRawStructure[$amazonOrderId]['FBMProductCostItemsShouldBeApplied'] = $productCostItems;
                    $maxFBMShippingCostAmountEUR = $amountEUR;
                }
            }
        }

        $this->info('Filling FBM shipping costs that should be applied per amazon order finished');

        return $workRawStructure;
    }

    /**
     * Fills amount per SKU for each amazon order based on real transactions.
     *
     * @param array $FBMShippingCostTransactions
     * @param array $workRawStructure
     * @return array
     */
    protected function fillActualAmountsPerProductCostCategory(
        array $FBMShippingCostTransactions,
        array $workRawStructure
    ): array
    {
        $this->info('Filling actual amounts per product cost category started');

        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();

        /**
         * @var Transaction[] $transactions
         */
        foreach ($FBMShippingCostTransactions as $transactionType => $transactions) {
            foreach ($transactions as $transaction) {
                if ( $transaction->CategoryId !== $organicSalesCategoryId) {
                    continue;
                }

                $transactionKey = implode('_', [
                    $transaction->SellerSKU,
                    $transaction->Currency
                ]);

                if (empty($workRawStructure[$transaction->AmazonOrderId]['actualAmountsEURPerProductCostCategory'][$transaction->COGCategoryId])) {
                    $workRawStructure[$transaction->AmazonOrderId]['actualAmountsEURPerProductCostCategory'][$transaction->COGCategoryId] = 0;
                }

                $workRawStructure[$transaction->AmazonOrderId]['actualAmountsEURPerProductCostCategory'][$transaction->COGCategoryId] += $transaction->AmountEUR;
                $actualTransaction = $workRawStructure[$transaction->AmazonOrderId]['actualTransactionsPerProductCostCategory'][$transaction->COGCategoryId][$transactionKey] ?? null;

                if (null === $actualTransaction) {
                    $workRawStructure[$transaction->AmazonOrderId]['actualTransactionsPerProductCostCategory'][$transaction->COGCategoryId][$transactionKey] = $transaction;
                } else {
                    $workRawStructure[$transaction->AmazonOrderId]['actualTransactionsPerProductCostCategory'][$transaction->COGCategoryId][$transactionKey]->AmountEUR += $transaction->AmountEUR;
                    $workRawStructure[$transaction->AmazonOrderId]['actualTransactionsPerProductCostCategory'][$transaction->COGCategoryId][$transactionKey]->Amount += $transaction->Amount;
                }

                $workRawStructure[$transaction->AmazonOrderId]['isOrderBasedTransactions'] = $transactionType === 'orderBasedTransactions';
            }
        }

        $this->info('Filling actual amounts per product cost category finished');

        return $workRawStructure;
    }

    /**
     * Fills expected amount per product cost category for each amazon order.
     *
     * @param array $workRawStructure
     * @return array
     * @throws \Exception
     */
    public function fillExpectedAmountsPerProductCostCategory(array $workRawStructure): array
    {
        $this->info('Filling expected amounts per product cost category started');

        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        foreach ($workRawStructure as $amazonOrderId => $item) {
            foreach ($item['FBMProductCostItemsShouldBeApplied'] as $productCostItem) {
                $itemAmountEUR = (int)($this->currencyRateManager->toBaseCurrency(
                    $productCostItem['amount_per_unit'],
                    $productCostItem['currency_id'],
                    new \DateTime($item['orderPurchaseDate'])
                ) * $moneyAccuracy * -1);
                $workRawStructure[$amazonOrderId]['expectedAmountsEURPerProductCostCategory'][$productCostItem['product_cost_category_id']] = $itemAmountEUR;
            }
        }

        $this->info('Filling expected amounts per product cost category finished');

        return $workRawStructure;
    }

    /**
     * Generates and returns structure that will be used in whole process.
     *
     * @param array $amazonOrderItems
     * @return array
     */
    protected function getWorkRawStructure(array $amazonOrderItems): array
    {
        $this->info('Generating raw work structure started');
        $workStructure = [];

        foreach ($amazonOrderItems as $amazonOrderItem) {
            $workStructure[$amazonOrderItem['order_id']] = $workStructure[$amazonOrderItem['order_id']] ?? [
                'FBMProductCostItemsShouldBeApplied' => [],
                'skus' => [],
                'marketplaceId' => $amazonOrderItem['order_marketplace_id'],
                'sellerId' => $amazonOrderItem['seller_id'],
                'orderPurchaseDate' => $amazonOrderItem['order_purchase_date'],
                'asin' => $amazonOrderItem['asin'],
                'countItems' => 0,
                'isOrderBasedTransactions' => false,
                'actualTransactionsPerProductCostCategory' => [],
                'actualAmountsEURPerProductCostCategory' => [],
                'expectedAmountsEURPerProductCostCategory' => []
            ];
            $skus = $workStructure[$amazonOrderItem['order_id']]['skus'];
            $skus[] = $amazonOrderItem['sku'];
            $skus = array_unique($skus);
            $workStructure[$amazonOrderItem['order_id']]['skus'] = $skus;
            $workStructure[$amazonOrderItem['order_id']]['countItems'] = count($skus);
        }

        $this->info('Generating raw work structure started');

        return $workStructure;
    }

    /**
     * Generates and returns array of product cost items for given order items.
     *
     * @param array $amazonOrderItems
     * @return array
     */
    protected function getProductCostItemsForOrderItems(array $amazonOrderItems): array
    {
        $this->info('Getting product cost items started');
        $productCostItemFilters = [];

        foreach ($amazonOrderItems as $amazonOrderItem) {
            $key = implode(
                '_',
                [
                    $amazonOrderItem['order_marketplace_id'],
                    $amazonOrderItem['seller_id'],
                    $amazonOrderItem['sku']
                ]
            );
            $productCostItemFilters[$amazonOrderItem['order_purchase_date']][$key] = [
                'marketplace_id' => $amazonOrderItem['order_marketplace_id'],
                'seller_id' => $amazonOrderItem['seller_id'],
                'seller_sku' => $amazonOrderItem['sku'],
            ];
        }

        $FBMShippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(
            SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
        );
        $productCostItems = ProductCostItem::findSuitableForSellerAndMarketplaceBulk(
            $productCostItemFilters,
            $FBMShippingCostCategoryIds
        );

        $this->info('Getting product cost items finished. Received ' . count($productCostItems) . ' items');

        return $productCostItems;
    }

    /**
     * Builds suitable filters form for given order items.
     * This filter form will be used to retrieve all transactions for given order items.
     *
     * @param array $amazonOrderItems
     * @return FiltersForm
     */
    protected function buildFiltersForm(array $amazonOrderItems): FiltersForm
    {
        $filtersForm = new FiltersForm();

        $fromDateTimeStamp = strtotime('+50 years');
        $toDateTimeStamp = strtotime('-50 years');
        $orderIds = [];

        // Choosing max and min order purchase date
        foreach ($amazonOrderItems as $amazonOrderItem) {
            $fromDateTimeStamp = min(
                strtotime($amazonOrderItem['order_purchase_date']),
                $fromDateTimeStamp
            );
            $toDateTimeStamp = max(
                strtotime($amazonOrderItem['order_purchase_date']),
                $toDateTimeStamp
            );
            $orderIds[] = $amazonOrderItem['order_id'];
        }

        $orderIds = array_unique($orderIds);
        $filtersForm->amazonOrderId = implode(',', $orderIds);
        // Some diff in seconds need to be sure that all transactions will be included
        $filtersForm->dateStart = date('Y-m-d H:i:s', $fromDateTimeStamp - 60);
        $filtersForm->dateEnd = date('Y-m-d H:i:s', $toDateTimeStamp + 60);

        return $filtersForm;
    }

    /**
     * Removes non FBM order items and order items which have only 1 item in whole order.
     *
     * @param array $amazonOrderItems
     * @return array
     */
    protected function filterAmazonOrderItems(array $amazonOrderItems): array
    {
        array_filter($amazonOrderItems, function ($item) use ($amazonOrderItems) {
            $isFBMOrderItem = $item['fulfillment_channel'] === AmazonOrder::FULFILMENT_CHANNEL_MFN;
            $hasAlreadyDefinedManualFBMShippingCost = !empty($item['manual_shipping_cost']);

            if (!$isFBMOrderItem || $hasAlreadyDefinedManualFBMShippingCost) {
                return false;
            }

            return true;
        });

        return $amazonOrderItems;
    }
}
