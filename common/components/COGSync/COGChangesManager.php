<?php

namespace common\components\COGSync;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\BufferFactory;
use common\components\db\ClickhouseDbHelper;
use common\components\featureFlag\FeatureFlagService;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\OrderBasedTransaction;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\FbaReturn;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\customer\RefundTransactionWithoutProductCost;
use common\models\finance\clickhouse\Transaction;
use common\models\FinanceEventCategory;
use common\models\order\AmazonOrderItem;
use common\models\order\AmazonOrderView;
use common\models\SalesCategory;
use common\models\Seller;
use yii\db\Expression;
use yii\db\Query;
use yii\mutex\Mutex;

class COGChangesManager
{
    protected const BATCH_SIZE = 500;

    /**
     * Max offset in days when sales transaction of single refund can be appeared.
     */
    protected const REFUND_MAX_SALE_OFFSET_DAYS = 360;

    use LogToConsoleTrait;

    private MessagesSender $messagesSender;
    private BufferFactory $bufferFactory;
    private DbManager $dbManager;
    private COGTransactionsGenerator $COGTransactionsGenerator;
    private TransactionQueryExecutor $transactionQueryExecutor;
    private CustomerConfig $customerConfig;
    private CurrencyRateManager $currencyRateManager;
    private CustomerComponent $customerComponent;
    private ClickhouseDbHelper $clickhouseDbHelper;
    private FBMShippingCostsManager $FBMShippingCostsManager;
    private FeatureFlagService $featureFlagService;
    private Mutex $mutex;

    public function __construct(
        MessagesSender           $messagesSender,
        BufferFactory            $bufferFactory,
        DbManager                $dbManager,
        COGTransactionsGenerator $COGTransactionsGenerator,
        TransactionQueryExecutor $transactionQueryExecutor,
        CustomerConfig           $customerConfig,
        CustomerComponent        $customerComponent
    ) {
        $this->messagesSender = $messagesSender;
        $this->bufferFactory = $bufferFactory;
        $this->dbManager = $dbManager;
        $this->COGTransactionsGenerator = $COGTransactionsGenerator;
        $this->transactionQueryExecutor = $transactionQueryExecutor;
        $this->customerConfig = $customerConfig;
        $this->currencyRateManager = new CurrencyRateManager();
        $this->customerComponent = $customerComponent;
        $this->clickhouseDbHelper = new ClickhouseDbHelper();
        $this->FBMShippingCostsManager = new FBMShippingCostsManager();
        $this->featureFlagService = \Yii::$app->featureFlagService;
        $this->mutex = \Yii::$app->mutex;
    }

    public function sendCOGChangesToQueue(
        float $amountPerItemOld,
        string $currencyCodeOld,
        float $amountPerItemNew,
        string $currencyCodeNew,
        string $dateStart,
        string $dateEnd,
        ProductCostItem $productCostItem,
        ProductCostPeriod $productCostPeriod,
        int $priority = 1
    ): void
    {
        if ($amountPerItemNew === $amountPerItemOld
            && $currencyCodeNew === $currencyCodeOld) {
            return;
        }

        if (abs($amountPerItemOld - $amountPerItemNew) <= 0.01) {
            return;
        }

        $dateDiffSec = abs(time() - strtotime($dateStart));

        // Period just created, so there are no transactions yet to apply COG changes
        if ($dateDiffSec <= 1) {
            return;
        }

        $this->messagesSender->COGChanges(
            $amountPerItemOld,
            $currencyCodeOld,
            $amountPerItemNew,
            $currencyCodeNew,
            $dateStart,
            $dateEnd,
            $productCostItem,
            $productCostPeriod,
            false,
            $priority
        );
//        $productCostItem->status = ProductCostItem::STATUS_QUEUED;
//        $productCostItem->save(false);
    }

    public function applyCOGChanges(
        float $amountPerItemOld,
        string $currencyCodeOld,
        float $amountPerItemNew,
        string $currencyCodeNew,
        int $productCostItemId,
        int $customerId,
        ?string $dateStart,
        ?string $dateEnd,
        ?string $createdUntil,
        string $sellerSku,
        string $marketplaceId,
        string $sellerId,
        int $productCostCategoryId
    ): void
    {
        if ($this->customerConfig->get(CustomerConfig::PARAMETER_USE_FROM_DB_TO_CLICKHOUSE_V2)) {
            $this->applyCOGChangesV2(
                $amountPerItemOld,
                $currencyCodeOld,
                $amountPerItemNew,
                $currencyCodeNew,
                $customerId,
                $dateStart,
                $dateEnd,
                $createdUntil,
                $sellerSku,
                $marketplaceId,
                $sellerId,
                $productCostCategoryId
            );
            return;
        }

        if (empty($dateStart)) {
            $dateStart = (new \DateTime())->modify('-50 years')->format('Y-m-d H:i:s');
        }

        if (empty($dateEnd)) {
            $dateEnd = (new \DateTime())->modify('+10 minutes')->format('Y-m-d H:i:s');
        }

        $createdUntil = $createdUntil ?? date('Y-m-d H:i:s');

        $date1 = strtotime($dateStart);
        $date2 = strtotime($dateEnd);

        $this->dbManager->setCustomerId($customerId);
        $productCostItem = ProductCostItem::findOne(['id' => $productCostItemId]);
        $transactionsBuffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        try {
            if (!empty($productCostItem)) {
//                $productCostItem->status = ProductCostItem::STATUS_APPLYING;
//                $productCostItem->count_affected_transactions = 0;
//                $productCostItem->save(false);
            }

            $filtersForm = new FiltersForm();
            $filtersForm->dateStart = date('Y-m-d H:i:s', min($date1, $date2));
            $filtersForm->dateEnd = date('Y-m-d H:i:s', max($date1, $date2));
            $filtersForm->marketplaceId = $marketplaceId;
            $filtersForm->sellerId = $sellerId;
            $filtersForm->sellerSku = $sellerSku;

            /** @var Transaction[] $organicSalesTransactions */
            foreach ($this->iterateOrganicSalesTransactionBatches($filtersForm, $createdUntil) as $iterationResult) {
                foreach ($iterationResult as $type => $organicSalesTransactions) {
                    if (count($organicSalesTransactions) === 0) {
                        continue;
                    }

                    $this->info('Generating corrections for sales transaction has been started');
                    $correctiveTransactions = $this
                        ->COGTransactionsGenerator
                        ->generateByKnownPriceChange(
                            $organicSalesTransactions,
                            $amountPerItemNew,
                            $currencyCodeNew,
                            $amountPerItemOld,
                            $currencyCodeOld,
                            $productCostCategoryId
                        );
                    $this->info('Generating corrections for sales transaction has been finished');

                    if ($type === 'transactions') {
                        $this->info('Putting corrective sales transactions into clickhouse buffer started');
                        $this->info($correctiveTransactions);
                        $transactionsBuffer->put($correctiveTransactions);
                        $this->info('Putting corrective sales transactions into clickhouse buffer finished');
                    } else {
                        try {
                            $this->info('Putting corrective sales transactions into clickhouse order based table started');
                            $correctiveTransactions = Transaction::convertTransactionsToArray($correctiveTransactions);
                            $this->info($correctiveTransactions);
                            Command::$isNodeChangingEnabled = false;
                            $clickhouseCustomerDb->open();
                            $clickhouseCustomerDb
                                ->getClient()
                                ->insertAssocBulk(OrderBasedTransaction::tableName(), $correctiveTransactions);
                            Command::$isNodeChangingEnabled = true;
                            $this->info('Putting corrective sales transactions into clickhouse order based table finihsed');
                        } catch (\Throwable $e) {
                            Command::$isNodeChangingEnabled = true;
                            $this->error($e);
                        }
                    }

                    if ($productCostItem) {
                        $productCostItem->count_affected_transactions += count($organicSalesTransactions);
                    }
                }
            }

//            if ($productCostItem) {
//                $productCostItem->status = ProductCostItem::STATUS_APPLIED;
//                $productCostItem->save(false);
//            }
            $transactionsBuffer->flush();
        } catch (\Throwable $e) {
            $this->error($e);
            $transactionsBuffer->remove();
//            if ($productCostItem) {
//                $productCostItem->status = ProductCostItem::STATUS_FAILED;
//                $productCostItem->save(false);
//            }
            throw $e;
        }
    }

    public function applyCOGChangesV2(
        float $amountPerItemOld,
        string $currencyCodeOld,
        float $amountPerItemNew,
        string $currencyCodeNew,
        int $customerId,
        ?string $dateStart,
        ?string $dateEnd,
        ?string $createdUntil,
        string $sellerSku,
        string $marketplaceId,
        string $sellerId,
        int $productCostCategoryId
    ): void
    {
        $dateStart = $dateStart ?? date('Y-m-d H:i:s', strtotime('-50 years'));
        $dateEnd = $dateEnd ?? date('Y-m-d H:i:s');
        $createdUntil = $createdUntil ?? date('Y-m-d H:i:s');

        $date1 = strtotime($dateStart);
        $date2 = strtotime($dateEnd);

        $this->dbManager->setCustomerId($customerId);
        $transactionsBuffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
        $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

        try {
            $filtersForm = new FiltersForm();
            $filtersForm->dateStart = date('Y-m-d H:i:s', min($date1, $date2));
            $filtersForm->dateEnd = date('Y-m-d H:i:s', max($date1, $date2));
            $filtersForm->marketplaceId = $marketplaceId;
            $filtersForm->sellerId = $sellerId;
            $filtersForm->sellerSku = $sellerSku;

            $FBMShippingCostCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS);
            $isFBMShippingCostChange = in_array($productCostCategoryId, $FBMShippingCostCategoryIds);
            $isEnabledHigherFBMShippingCostApplying  = $this->featureFlagService->isEnabled(
                FeatureFlagService::FLAG_HIGHER_FBM_SHIPPING_COST_APPLYING,
                $customerId
            );

            /** @var Transaction[] $organicSalesTransactions */
            foreach ($this->iterateOrganicSalesTransactionBatches($filtersForm, $createdUntil) as $iterationResult) {
                $productCostItems = [];

                foreach ($iterationResult as $type => $transactions) {
                    if (empty($transactions)) {
                        continue;
                    }

                    // For all FBM shipping cost changes same logic should be applied.
                    if ($isFBMShippingCostChange && $isEnabledHigherFBMShippingCostApplying) {
                        $lockKey = implode('_', [
                            'fbm_shipping_costs_calculation',
                            $customerId,
                            $marketplaceId,
                            $sellerId,
                            $sellerSku,
                            $dateStart,
                            $dateEnd
                        ]);

                        $this->info('Acquiring lock for FBM shipping costs calculation');
                        $this->mutex->acquire($lockKey, 30);
                        $this->info('Acquired lock for FBM shipping costs calculation');

                        try {
                            $orderIds = [];
                            foreach ($transactions as $transaction) {
                                $orderIds[] = $transaction->AmazonOrderId;
                            }
                            $orderIds = array_unique($orderIds);
                            $amazonOrderItems = $this->getAmazonOrderItems($orderIds, $dateStart, $dateEnd);
                            $this->FBMShippingCostsManager->recalculateForOrders($amazonOrderItems);
                        } catch (\Throwable $e) {
                            $this->error($e);
                            $this->mutex->release($lockKey);
                            throw $e;
                        }

                        continue;
                    }

                    foreach ($transactions as $transaction) {
                        $postedDate = date('Y-m-d', strtotime($transaction->PostedDate));
                        $key = implode('_', [$transaction->MarketplaceId, $transaction->SellerId, $transaction->SellerSKU]);

                        if ($currencyCodeOld === $currencyCodeNew) {
                            $amountDiff = $amountPerItemNew - $amountPerItemOld;
                        } else {
                            $amountDiff = $amountPerItemNew - $this->currencyRateManager
                                ->convert(
                                    $amountPerItemOld,
                                    $currencyCodeOld,
                                    $currencyCodeNew,
                                    new \DateTime($transaction->PostedDate)
                                );
                        }
                        $productCostItems[$postedDate][$key] = [[
                            'product_cost_category_id' => $productCostCategoryId,
                            'amount_per_unit' => $amountDiff,
                            'marketplace_amount_per_unit' => $amountDiff,
                            'marketplace_currency_id' => $currencyCodeNew,
                            'currency_id' => $currencyCodeNew,
                            'marketplace_id' => $transaction->MarketplaceId,
                            'seller_id' => $transaction->SellerId,
                            'seller_sku' => $transaction->SellerSKU,
                        ]];
                    }

                    $correctiveTransactions = $this->COGTransactionsGenerator->generateBulk(
                        $transactions,
                        true,
                        null,
                        null,
                        null,
                        null,
                        $productCostItems
                    );

                    $this->info("Corrective transactions:");
                    $this->info($correctiveTransactions);

                    if (empty($correctiveTransactions)) {
                        continue;
                    }

                    if ($type === 'transactions') {
                        $this->info('Putting corrective sales transactions into clickhouse buffer started');
                        $transactionsBuffer->put($correctiveTransactions);
                        $this->info('Putting corrective sales transactions into clickhouse buffer finished');
                        continue;
                    }

                    try {
                        $this->info('Putting corrective sales transactions into clickhouse order based table started');
                        $correctiveTransactions = Transaction::convertTransactionsToArray($correctiveTransactions);
                        $this->info($correctiveTransactions);
                        Command::$isNodeChangingEnabled = false;
                        $clickhouseCustomerDb->open();
                        $clickhouseCustomerDb
                            ->getClient()
                            ->insertAssocBulk(OrderBasedTransaction::tableName(), $correctiveTransactions);
                        Command::$isNodeChangingEnabled = true;
                        $this->info('Putting corrective sales transactions into clickhouse order based table finihsed');
                    } catch (\Throwable $e) {
                        Command::$isNodeChangingEnabled = true;
                        $this->error($e);
                    }
                }
            }

            $transactionsBuffer->flush();
        } catch (\Throwable $e) {
            $this->error($e);
            $transactionsBuffer->remove();
            throw $e;
        }
    }

    public function checkNewRefundsAndApplyIfFoundSalesTransaction(array $refunds): void
    {
        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen, skipping');
            return;
        }

        $amazonOrderIds = [];
        $sellerSkus = [];
        $sellerIds = [];
        $marketplaceIds = [];

        $dateStart = null;
        $dateEnd = null;
        foreach ($refunds as $refund) {
            $amazonOrderIds[] = $refund['amazon_order_id'];
            $sellerSkus[] = $refund['seller_sku'];
            $sellerIds[] = $refund['seller_id'];
            $marketplaceIds[] = $refund['marketplace_id'];

            if (null === $dateEnd) {
                $dateEnd = $refund['posted_date'];
            }
            $dateStart = $refund['posted_date'];
        }
        $sellerSkus = array_unique($sellerSkus);
        $amazonOrderIds = array_unique($amazonOrderIds);
        $marketplaceIds = array_unique($marketplaceIds);
        $sellerIds = array_unique($sellerIds);
        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $salesTransactions = $this
            ->getDirtyBatchOfTransactions(
                $marketplaceIds,
                $sellerIds,
                $sellerSkus,
                $amazonOrderIds,
                $organicSalesId,
                (new \DateTime($dateStart))->modify('-' . self::REFUND_MAX_SALE_OFFSET_DAYS . ' days'),
                new \DateTime($dateEnd),
            );

        if (empty($salesTransactions)) {
            return;
        }

        $refundIdsToRemove = [];
        $correctiveTransactions = [];

        foreach ($refunds as $refund) {
            $refundTransaction = $this->convertRefundsToRefundTransactions([$refund])[0];
            $index = implode('_', [
                $refundTransaction->MarketplaceId,
                $refundTransaction->SellerId,
                $refundTransaction->SellerSKU,
                $refundTransaction->AmazonOrderId
            ]);
            $salesTransaction = $salesTransactions[$index] ?? null;

            if (empty($salesTransaction)) {
                continue;
            }
            $refundIdsToRemove[] = $refund['id'];

            /** @var ProductCostItem[] $productCostItems */
            $productCostItems = ProductCostItem::findSuitableForSellerAndMarketplace(
                $refundTransaction->MarketplaceId,
                $refundTransaction->SellerId,
                $refundTransaction->SellerSKU,
                new \DateTime($salesTransaction->PostedDate)
            );

            foreach ($productCostItems as $productCostItem) {
                $correctiveTransactions = array_merge(
                    $correctiveTransactions,
                    $this
                        ->COGTransactionsGenerator
                        ->generateByKnownPriceChange(
                            [$refundTransaction],
                            $productCostItem['marketplace_amount_per_unit'],
                            $productCostItem['marketplace_currency_id'],
                            0,
                            $productCostItem['marketplace_currency_id'],
                            $productCostItem['product_cost_category_id']
                        )
                );
            }
        }

        if (!empty($correctiveTransactions)) {
            $transactionsBuffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
            $transactionsBuffer->put($correctiveTransactions);
            $transactionsBuffer->flush();
            $this->info(sprintf("Putting %s corrective transactions into buffer", count($correctiveTransactions)));
        }

        if (!empty($refundIdsToRemove)) {
            $this->info(sprintf("Removing %s refunds", count($refundIdsToRemove)));
            $this->dbManager->getCustomerDb()->createCommand()->delete(
                RefundTransactionWithoutProductCost::tableName(), [
                'id' => $refundIdsToRemove
            ])->execute();
            $this->info(sprintf("Removed %s refunds", count($refundIdsToRemove)));
        }
    }

    public function checkAndApplySellableFBAReturns()
    {
        if ($this->customerComponent->isTransactionsFrozen()) {
            $this->info('Interaction with clickhouse is frozen, skipping');
            return;
        }

        $query = FbaReturn::getSellableQuery()
            ->select([
                'id',
                'order_id',
                'sku',
                'quantity',
                'return_date'
            ])
            ->andWhere(['is', 'moved_to_clickhouse_at', new Expression('null')])
            ->noCache()
            ->asArray();

        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $organicSalesId = FinanceEventCategory::getOrganicSalesId();
        $costOfGoodsCategoryIds = ProductCostCategory::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_COG);
        $transactionsBuffer = $this->bufferFactory->getTransactionsToClickhouseBuffer();
        $moneyAccuracy = $this->customerComponent->getMoneyAccuracy();

        /** @var FbaReturn[] $sellableReturns */
        foreach ($query->batch(1000) as $sellableReturns) {
            try {
                $fromDateTime = (new \DateTime($sellableReturns[count($sellableReturns) - 1]['return_date']))
                    ->modify('-3 months');
                $toDateTime = (new \DateTime($sellableReturns[0]['return_date']));
                $orderIds = [];
                $skus = [];
                $processedIds = [];

                foreach ($sellableReturns as $sellableReturn) {
                    $orderIds[] = $sellableReturn['order_id'];
                    $skus[] = $sellableReturn['sku'];
                    $processedIds[] = $sellableReturn['id'];
                }
                $orderIds = array_unique($orderIds);
                $skus = array_unique($skus);

                $transactionsQuery = \common\models\customer\clickhouse\Transaction::find()
                    ->where([
                        'AND',
                        ['>=', 'TransactionDate', $fromDateTime->format('Y-m-d H:i:s')],
                        ['<=', 'TransactionDate', $toDateTime->format('Y-m-d H:i:s')],
                        ['=', 'CategoryId', $organicRefundCategoryId],
                        ['=', 'COGCategoryId', 0],
                        ['!=', 'AmountEUR', 0],
                        ['in', 'AmazonOrderId', $orderIds],
                        ['in', 'SellerSKU', $skus]
                    ])
                    ->asArray();

                $correctiveTransactions = [];
                foreach ($transactionsQuery->batch() as $transactions) {
                    foreach ($transactions as $k => $transaction) {
                        $transactions[$transaction['AmazonOrderId'] . '_' . $transaction['SellerSKU']] = $transaction;
                        unset($transactions[$k]);
                    }

                    $productCostItemFilters = [];
                    foreach ($transactions as $transaction) {
                        $key = implode('_', [$transaction['MarketplaceId'], $transaction['SellerId'], $transaction['SellerSKU']]);
                        $postedDate = date('Y-m-d', strtotime($transaction['PostedDate']));
                        $productCostItemFilters[$postedDate][$key] = [
                            'marketplace_id' => $transaction['MarketplaceId'],
                            'seller_id' => $transaction['SellerId'],
                            'seller_sku' => $transaction['SellerSKU'],
                        ];
                    }
                    $groupedProductCostItems = ProductCostItem::findSuitableForSellerAndMarketplaceBulk($productCostItemFilters);

                    foreach ($sellableReturns as $sellableReturn) {
                        $key = $sellableReturn['order_id'] . '_' . $sellableReturn['sku'];
                        $transaction = $transactions[$key] ?? null;

                        if (empty($transaction)) {
                            continue;
                        }

                        $productCostItemsKey = implode('_', [$transaction['MarketplaceId'], $transaction['SellerId'], $transaction['SellerSKU']]);
                        $postedDate = date('Y-m-d', strtotime($transaction['PostedDate']));
                        $productCostItems = $groupedProductCostItems[$postedDate][$productCostItemsKey] ?? [];

                        if (empty($productCostItems)) {
                            continue;
                        }

                        foreach ($productCostItems as $productCostItem) {
                            $productCostCategoryId =  $productCostItem['product_cost_category_id'];
                            if (!in_array($productCostItem['product_cost_category_id'], $costOfGoodsCategoryIds)) {
                                continue;
                            }
                            $correctiveTransaction = new Transaction($transaction);
                            $correctiveTransaction->COGCategoryId = $productCostCategoryId;
                            $correctiveTransaction->Currency = $productCostItem['marketplace_currency_id'];
                            $correctiveTransaction->Amount = (int)round_half_even(
                                $productCostItem['marketplace_amount_per_unit']
                                * $sellableReturn['quantity']
                                * $moneyAccuracy
                            );
                            $correctiveTransaction->Quantity = $sellableReturn['quantity'];
                            $correctiveTransaction->MergeCounter = 1;
                            $correctiveTransaction->CreatedAt = date('Y-m-d H:i:s');
                            $correctiveTransaction->CategoryId = $organicSalesId;
                            $correctiveTransaction->AmountEUR = $this
                                ->currencyRateManager
                                ->toBaseCurrency(
                                    $correctiveTransaction->Amount,
                                    $correctiveTransaction->Currency,
                                    new \DateTime($correctiveTransaction->PostedDate)
                                );

                            $correctiveTransactions[] = $correctiveTransaction;
                        }
                    }
                    $this->info("Putting corrective " . count($correctiveTransactions) . " transactions into buffer");
                    $transactionsBuffer->put($correctiveTransactions);
                }

                $this->info('Updating moved to clickhouse date of  FBA returns (' . count($processedIds) . ' items)');
                FbaReturn::updateAll([
                    'moved_to_clickhouse_at' => date('Y-m-d H:i:s')
                ], [
                    'id' => $processedIds
                ]);
                $transactionsBuffer->flush();
            } catch (\Throwable $e) {
                $transactionsBuffer->remove();
                $this->error($e);
            }
        }
    }

    /**
     * @param Transaction[] $transactions
     * @return array
     */
    public function generateNewForSalesTransaction(
        Transaction $salesTransaction,
        float $orderPrice = null,
        bool $isVatModuleEnabled = null
    ): array
    {
        $correctiveTransactions = [];
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();

        if (!in_array($salesTransaction->CategoryId, [$organicSalesCategoryId, $organicRefundCategoryId])) {
            return [];
        }

        $sellerSku = $salesTransaction->SellerSKU;
        $marketplaceId = $salesTransaction->MarketplaceId;
        $sellerId = $salesTransaction->SellerId;

        if (empty($sellerSku) || empty($marketplaceId) || empty($sellerId)) {
            return [];
        }

        /** @var ProductCostItem[] $productCostItems */
        $productCostItems = ProductCostItem::findSuitableForSellerAndMarketplace(
            $salesTransaction->MarketplaceId,
            $salesTransaction->SellerId,
            $salesTransaction->SellerSKU,
            new \DateTime($salesTransaction->PostedDate)
        );

        foreach ($productCostItems as $productCostItem) {
            $correctiveTransactions = array_merge(
                $correctiveTransactions,
                $this
                    ->COGTransactionsGenerator
                    ->generateByKnownPriceChange(
                        [$salesTransaction],
                        $productCostItem['marketplace_amount_per_unit'],
                        $productCostItem['marketplace_currency_id'],
                        0,
                        $productCostItem['marketplace_currency_id'],
                        $productCostItem['product_cost_category_id'],
                        $orderPrice,
                        $isVatModuleEnabled
                    )
                );
        }

        return $correctiveTransactions;
    }

    public function iterateOrganicSalesTransactionBatches(
        FiltersForm $filtersForm,
        $createdUntil,
        array $COGCategoryIds = [],
        bool $shouldCheckTransactionsExistence = true
    ): \Iterator
    {
        if (empty($COGCategoryIds)) {
            $COGCategoryIds = [0];
        }

        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();

        $orderBasedTransactionTableName = OrderBasedTransaction::tableName();

        if ($shouldCheckTransactionsExistence) {
            $hasTransactionsQuery = (new Query())
                ->from(TransactionBuffer::tableName())
                ->where([
                    'AND',
                    ['>=', 'PostedDate', $filtersForm->dateStart],
                    ['<=', 'PostedDate', $filtersForm->dateEnd],
                    ['=', 'SellerSKU', $filtersForm->sellerSku],
                    ['=', 'MarketplaceId', $filtersForm->marketplaceId],
                    ['=', 'SellerId', $filtersForm->sellerId],
                ])
                ->limit(1);
            $hasTransactions = $hasTransactionsQuery->exists(TransactionBuffer::getDb());

            if (!$hasTransactions) {
                $hasTransactionsQuery->from(OrderBasedTransaction::tableName());
                $hasTransactions = $hasTransactionsQuery->exists(TransactionBuffer::getDb());
            }

            if (empty($hasTransactions)) {
                $this->info('No transactions to apply cog changes');
                yield [];
                return;
            }
        }

        $minDate = $this->dbManager->getMinimumStatisticDate();
        $dateStart = new \DateTime($filtersForm->dateStart);

        if ($dateStart < $minDate) {
            $dateStart = $minDate;
        }

        $dateEndOriginal = new \DateTime($filtersForm->dateEnd);
        $interval = '+30 days';

        for ($i = 0; $i < 50; $i++) {
            $dateEnd = (clone $dateStart)->modify($interval);
            if ($dateEnd >= $dateEndOriginal) {
                $dateEnd = $dateEndOriginal;
            }

            $filtersForm2 = clone $filtersForm;
            $filtersForm2->dateStart = $dateStart->format('Y-m-d H:i:s');
            $filtersForm2->dateEnd = $dateEnd->format('Y-m-d H:i:s');

            $this->info([
                'dateStart' => $dateStart->format('Y-m-d H:i:s'),
                'dateEnd' => $dateEnd->format('Y-m-d H:i:s'),
            ]);
            $limit = self::BATCH_SIZE;
            $offset = 0;

            $tableName = TransactionBuffer::tableName();

            while (true) {
                $this->info("Requesting transactions limit $limit, offset $offset");
                $organicSalesTransactions = $this
                    ->transactionQueryExecutor
                    ->queryAll("
                    SELECT 
                        concat(MarketplaceId, '_', SellerId, '_', SellerSKU, '_', AmazonOrderId) as key,
                        *
                    FROM {$tableName} t
                    WHERE 1 = 1
                    [FILTERS]
                    AND t.CategoryId IN(:organic_sales_category_id, :organic_refund_category_id)   
                    AND t.COGCategoryId IN (:cog_category_ids)
                    AND t.CreatedAt <= toDateTime(:created_at)
                    ORDER BY CreatedAt DESC, MarketplaceId, SellerId, SellerSKU, AmazonOrderId
                    LIMIT $limit
                    OFFSET $offset",
                        $filtersForm2,
                        [
                            ':organic_sales_category_id' => $organicSalesCategoryId,
                            ':organic_refund_category_id' => $organicRefundCategoryId,
                            ':created_at' => $createdUntil,
                            ':cog_category_ids' => $COGCategoryIds
                        ]
                    );
                $this->info('Requesting transactions finished');

                try {
                    $this->info('Requesting order based transactions started');
                    $orderBasedOrganicSalesTransactions = $this
                        ->transactionQueryExecutor
                        ->queryAll("
                            SELECT *
                            FROM {$orderBasedTransactionTableName} t
                            WHERE 1 = 1
                            [FILTERS]
                            AND t.CategoryId IN(:organic_sales_category_id, :organic_refund_category_id)   
                            AND t.COGCategoryId IN (:cog_category_ids)
                            AND t.CreatedAt <= toDateTime(:created_at)
                            ORDER BY CreatedAt DESC, MarketplaceId, SellerId, SellerSKU, AmazonOrderId
                            LIMIT $limit
                            OFFSET $offset",
                            $filtersForm2,
                            [
                                ':organic_sales_category_id' => $organicSalesCategoryId,
                                ':organic_refund_category_id' => $organicRefundCategoryId,
                                ':created_at' => $createdUntil,
                                ':cog_category_ids' => $COGCategoryIds
                            ]
                        );
                    $this->info('Requesting order based transactions finished');
                } catch (\Throwable $e) {
                    $orderBasedOrganicSalesTransactions = [];
                    $this->error($e);
                }

                $this->info('Found ' . count($organicSalesTransactions) . ' order related transactions in clickhouse');
                $this->info('Found ' . count($orderBasedOrganicSalesTransactions) . ' order based transactions in clickhouse');

                $isLastIteration = false;
                if (count($organicSalesTransactions) < $limit) {
                    $isLastIteration = true;
                }

                foreach ($organicSalesTransactions as $k => $organicSalesTransaction) {
                    $organicSalesTransactions[$k] = new Transaction($organicSalesTransaction);
                }

                foreach ($orderBasedOrganicSalesTransactions as $k => $orderBasedOrganicSalesTransaction) {
                    $orderBasedOrganicSalesTransactions[$k] = new Transaction($orderBasedOrganicSalesTransaction);
                }

                yield [
                    'transactions' => $organicSalesTransactions,
                    'orderBasedTransactions' => $orderBasedOrganicSalesTransactions,
                ];

                if ($isLastIteration) {
                    $this->info('Loop has been finished');
                    break;
                }

                $offset += $limit;
            }

            if ($dateEnd === $dateEndOriginal) {
                break;
            }

            $dateStart = $dateEnd;
        }
    }

    /**
     * Returns all transactions possible belong to those which we are looking for.
     * Array keys of response array are indexes which can be used to find explicit transaction further.
     *
     * @param Transaction[] $refundTransactions
     * @return Transaction[]
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\di\NotInstantiableException
     */
    private function getDirtyBatchOfTransactions(
        array $marketplaceIds,
        array $sellerIds,
        array $sellerSkus,
        array $amazonOrderIds,
        int $categoryId,
        \DateTime $dateStart,
        \DateTime $dateEnd
    ): array
    {
        $tableName = \common\models\customer\clickhouse\Transaction::tableName();

        $filtersForm = new FiltersForm();
        $filtersForm->dateStart = $dateStart->format('Y-m-d H:i:s');
        $filtersForm->dateEnd = $dateEnd->format('Y-m-d H:i:s');
        $filtersForm->marketplaceId = implode(',', $marketplaceIds);
        $filtersForm->sellerId = implode(',', $sellerIds);
        $filtersForm->sellerSku = implode(',', $sellerSkus);
        $filtersForm->amazonOrderId = implode(',', $amazonOrderIds);

        $query = "
            SELECT
                concat(MarketplaceId, '_', SellerId, '_', SellerSKU, '_', AmazonOrderId) as index_field,
                *
            FROM {$tableName}
            WHERE 1 = 1
            [FILTERS]
            AND CategoryId = :category_id
            AND COGCategoryId = 0
        ";

        $transactions = $this->transactionQueryExecutor->queryAll($query, $filtersForm, [
            'category_id' => $categoryId
        ]);

        $transactionsIndexed = [];

        foreach ($transactions as $k => $transaction) {
            $transactionsIndexed[$transaction['index_field']] = new Transaction($transaction);
        }

        return $transactionsIndexed;
    }

    /**
     * Converts refunds from postgres  to refund transaction objects.
     *
     * @param array $refunds
     * @return Transaction[]
     */
    private function convertRefundsToRefundTransactions(array $refunds): array
    {
        $refundTransactions = [];
        foreach ($refunds as $refund) {
            $refundTransactions[] = new Transaction([
                "PostedDate" => $refund['posted_date'],
                "TransactionDate" => $refund['posted_date'],
                "Amount" => $refund['amount'],
                "Currency" => $refund['currency'],
                "EventPeriodId" => $refund['event_period_id'],
                "SellerSKU" => $refund['seller_sku'],
                "ASIN" => $refund['asin'],
                "SellerOrderId" => $refund['seller_order_id'],
                "AmazonOrderId" => $refund['amazon_order_id'],
                "MarketplaceId" => $refund['marketplace_id'],
                "SellerId" => $refund['seller_id'],
                "CategoryId" => $refund['category_id'],
                "Quantity" => $refund['quantity']
            ]);
        }
        return $refundTransactions;
    }

    private function getAmazonOrderItems(array $amazonOrderIds, string $dateStart, string $dateEnd): array
    {
        /** @var Seller[] $sellers */
        $sellers = Seller::find()
            ->where(['customer_id' => $this->dbManager->getCustomerId()])
            ->cache(60 * 60)
            ->all();

        $amazonOrderItems = [];
        foreach ($sellers as $seller) {
            $this->dbManager->setSellerId($seller->id);
            $amazonOrderItems = array_merge($amazonOrderItems, AmazonOrderView::find()
                ->select([
                    '*',
                    // Seller id
                    new Expression("'$seller->id' as seller_id")
                ])
                ->where([
                    'AND',
                    ['in', 'order_id', $amazonOrderIds],
                    ['>=', 'order_purchase_date', $dateStart],
                    ['<=', 'order_purchase_date', $dateEnd],
                ])
                ->cache(60 * 60)
                ->asArray()
                ->all()
            );
        }
        return $amazonOrderItems;
    }
}
