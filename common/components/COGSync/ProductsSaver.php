<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;

class ProductsSaver
{
    use LogToConsoleTrait;

    private DbManager $dbManager;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
    }

    public function saveProducts(array $productInfos): void
    {
        if (count($productInfos) === 0) {
            return;
        }

        foreach ($productInfos as $k => $productInfo) {
            unset($productInfos[$k]['id']);
            unset($productInfos[$k]['vat']);
            unset($productInfos[$k]['other_fees']);
            unset($productInfos[$k]['buying_price']);
            unset($productInfos[$k]['shipping_cost']);
            if (isset($productInfos[$k]['title']) && str_ends_with($productInfos[$k]['title'], '\\')) {
                $productInfos[$k]['title'] = addslashes($productInfo['title']);
            }
        }

        $productInfos = $this->fillIsEnabledSyncWithRepricerDefaultValue($productInfos);

        $insertUpdateSql = $this
            ->dbManager
            ->getCustomerDb()
            ->createCommand()
            ->batchInsert(
                Product::tableName(),
                array_keys(array_values($productInfos)[0]),
                $productInfos
            )
            ->getRawSql()
        ;
        $insertUpdateSql .= " ON CONFLICT (marketplace_id, seller_id, sku) DO UPDATE SET
            source = EXCLUDED.source,
            stock_type = EXCLUDED.stock_type,
            repricer_id = EXCLUDED.repricer_id,
            repricer_is_deleted = EXCLUDED.repricer_is_deleted
        ";
        $this->dbManager->getCustomerDb()->createCommand($insertUpdateSql)->execute();
    }

    /**
     * Sets correct default value for "is_enabled_sync_with_repricer"
     * for new products that are inserting through batches (batchInsert).
     *
     * @param array $productInfos
     * @return array
     */
    public function fillIsEnabledSyncWithRepricerDefaultValue(array $productInfos): array
    {
        $isEnabledSyncWithRepricer = true;

        if ($this->dbManager->isSyncDefault() === false) {
            $isEnabledSyncWithRepricer = false;
        }

        foreach ($productInfos as $k => $productInfo) {
            $productInfos[$k]['is_enabled_sync_with_repricer'] = $isEnabledSyncWithRepricer;
        }

        return $productInfos;
    }
}
