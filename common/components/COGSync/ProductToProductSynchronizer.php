<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\featureFlag\FeatureFlagService;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use common\models\Seller;

/**
 * Responsible for synchronizing product cost periods between products.
 */
class ProductToProductSynchronizer
{
    use LogToConsoleTrait;

    /**
     * This key is using to mark period that starts before sync and ends after sync.
     * These periods should be synced only from sync start date and have little different processing logic.
     */
    public const INTERMEDIATE_PERIOD_KEY = 'intermediate_period';

    private DbManager $dbManager;
    private CustomerConfig $customerConfig;
    private FeatureFlagService $featureFlagService;
    private GlobalMarketplaceService $globalMarketplaceService;

    public function __construct()
    {
        $this->dbManager = \Yii::$app->dbManager;
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->featureFlagService = \Yii::$app->featureFlagService;
        $this->globalMarketplaceService = \Yii::$container->get('globalMarketplaceService');
    }

    /**
     * Ensures that passed marketplace is global marketplace and syncs all products with it.
     *
     * @param string $sellerId
     * @param string $sku
     * @param string $marketplaceId
     * @return void
     */
    public function ensureGlobalMarketplaceAndSyncAllProductsWithIt(string $sellerId, string $sku, string $marketplaceId): void
    {
        try {
            $globalMarketplaceId = $this->globalMarketplaceService->getGlobalMarketplaceId($sellerId, $sku);

            $this->info([
                'globalMarketplaceId' => $globalMarketplaceId
            ]);

            if (empty($globalMarketplaceId) || $marketplaceId !== $globalMarketplaceId) {
                return;
            }

            $this->syncWithGlobalMarketplace($sellerId, $sku);
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    /**
     * Synchronizes product cost periods between products with global marketplace.
     * If specified marketplace id, only product with this marketplace will be synced.
     *
     * @param string $sellerId
     * @param string $sku
     * @param string|null $marketplaceId Specify marketplace id to sync only to this marketplace (single product).
     * @return void
     */
    public function syncWithGlobalMarketplace(string $sellerId, string $sku, string $marketplaceId = null): void
    {
        if (!$this->featureFlagService->isEnabled(FeatureFlagService::FLAG_PRODUCT_SYNC_WITH_GLOBAL_MARKETPLACE, $this->dbManager->getCustomerId())) {
            return;
        }

        $this->info("Syncing periods with global marketplace started");
        $this->info([
            'sellerId' => $sellerId,
            'sku' => $sku,
            'marketplaceId' => $marketplaceId
        ]);

        if (empty($sellerId)) {
            $this->error(new \Exception("Unable to find seller $sellerId"));
            return;
        }

        $globalMarketplaceIdSetting = $this->customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            null,
            $sellerId
        );
        $isEnabledCostOfGoodsSyncSetting = $this->customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            false,
            $sellerId
        );
        $isEnabledOtherFeesSyncSetting = $this->customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            false,
            $sellerId
        );
        $isEnableFBMShippingCostSyncSetting = $this->customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            false,
            $sellerId
        );
        $globalMarketplaceSyncVersion = (int)$this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION,
            0,
            $sellerId
        );

        $currDateTime =  date('Y-m-d H:i:s');
        $globalMarketplaceId = (string)($globalMarketplaceIdSetting->value ?? null);
        $isEnabledCostOfGoodsSync = (bool)($isEnabledCostOfGoodsSyncSetting->value ?? null);
        $isEnabledOtherFeesSync = (bool)($isEnabledOtherFeesSyncSetting->value ?? null);
        $isEnabledFBMShippingCostSync = (bool)($isEnableFBMShippingCostSyncSetting->value ?? null);
        $globalMarketplaceSpecifiedAt = $globalMarketplaceIdSetting->updated_at ?? $currDateTime;
        $costOfGoodsSyncSpecifiedAt = $isEnabledCostOfGoodsSyncSetting->updated_at ?? $currDateTime;
        $otherFeesSyncSpecifiedAt = $isEnabledOtherFeesSyncSetting->updated_at ?? $currDateTime;
        $FBMShippingCostSyncSpecifiedAt = $isEnabledOtherFeesSyncSetting->updated_at ?? $currDateTime;

        $this->info([
            'sellerId' => $sellerId,
            'globalMarketplaceId' => $globalMarketplaceId,
            'isEnabledCostOfGoodsSync' => $isEnabledCostOfGoodsSync,
            'isEnabledOtherFeesSync' => $isEnabledOtherFeesSync,
            'isEnabledFBMShippingCostSync' => $isEnabledFBMShippingCostSync,
            'globalMarketplaceSpecifiedAt' => $globalMarketplaceSpecifiedAt,
            'costOfGoodsSyncSpecifiedAt' => $costOfGoodsSyncSpecifiedAt,
            'otherFeesSyncSpecifiedAt' => $otherFeesSyncSpecifiedAt,
            'FBMShippingCostSyncSpecifiedAt' => $FBMShippingCostSyncSpecifiedAt
        ]);

        if (empty($globalMarketplaceId)) {
            return;
        }

        /** @var Product[] $products */
        $products = Product::find()->where([
            'AND',
            ['=', 'seller_id', $sellerId],
            ['=', 'sku', $sku],
        ])->all();

        $this->info("Found " . count($products) . " products in total");

        if (empty($products)) {
            $this->info("No products in total. Skipping.");

            return;
        }

        $globalMarketplaceProduct = null;
        $targetProducts = [];

        foreach ($products as $product) {
            if ($product->marketplace_id === $globalMarketplaceId) {
                $globalMarketplaceProduct = $product;
                continue;
            }

            // We can choose only one product to be synced by choosing marketplace id
            $isSelectedProduct = empty($marketplaceId) || $product->marketplace_id === $marketplaceId;
            $isEnabledSync = $product->is_enabled_sync_with_global_marketplace;

            if ($isSelectedProduct && $isEnabledSync) {
                $targetProducts[] = $product;
            }
        }

        $this->info('Found global marketplace product: ' . ($globalMarketplaceProduct ? $globalMarketplaceProduct->id : 'null'));
        $this->info("Found " . count($targetProducts) . " target products");

        if (empty($globalMarketplaceProduct) || empty($targetProducts)) {
            $this->info("No global marketplace product or target products for synchronization. Skipping.");

            return;
        }

        /**
         * @param $periodsGroupedBySalesCategoryId
         * @param Product $targetProduct
         * @return mixed
         */
        $filterPeriodsFn = function ($periodsGroupedBySalesCategoryId, Product $targetProduct) use (
            $isEnabledCostOfGoodsSync,
            $costOfGoodsSyncSpecifiedAt,
            $isEnabledOtherFeesSync,
            $otherFeesSyncSpecifiedAt,
            $isEnabledFBMShippingCostSync,
            $FBMShippingCostSyncSpecifiedAt,
            $globalMarketplaceSpecifiedAt
        ) {
            /**
             * @var $salesCategoryId
             * @var ProductCostPeriod[] $periods
             */
            foreach ($periodsGroupedBySalesCategoryId as $salesCategoryId => $periods) {
                if (empty($periods)) {
                    continue;
                }

                if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_COG) {
                    $isEnabledSync = $isEnabledCostOfGoodsSync;
                    $syncSpecifiedAt = $costOfGoodsSyncSpecifiedAt;
                } else if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_OTHER_FEES) {
                    $isEnabledSync = $isEnabledOtherFeesSync;
                    $syncSpecifiedAt = $otherFeesSyncSpecifiedAt;
                } else if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS) {
                    $isEnabledSync = $isEnabledFBMShippingCostSync;
                    $syncSpecifiedAt = $FBMShippingCostSyncSpecifiedAt;
                } else {
                    $isEnabledSync = false;
                    $syncSpecifiedAt = date('Y-m-d H:i:s');
                }

                if (!$isEnabledSync) {
                    unset($periodsGroupedBySalesCategoryId[$salesCategoryId]);
                    continue;
                }

                /**
                 * Start date of periods which will be synced.
                 * We allow to sync only those periods which were created after enabling sync with global marketplace.
                 */
                $synchronisationStartDate = max(
                    strtotime($targetProduct->sync_with_global_marketplace_enabled_at),
                    strtotime($globalMarketplaceSpecifiedAt),
                    strtotime($syncSpecifiedAt)
                );
                $synchronisationStartDate = date('Y-m-d 00:00:00', $synchronisationStartDate);

                foreach ($periods as $k => $period) {
                    $isStartsBeforeSync = empty($period->date_start) || strtotime($period->date_start) <= strtotime($synchronisationStartDate);
                    $isEndsAfterSync = empty($period->date_end) || strtotime($period->date_end) > strtotime($synchronisationStartDate);

                    // If period overlaps sync start date,
                    // we should apply it only from sync start date.
                    if ($isStartsBeforeSync && $isEndsAfterSync) {
                        // Need to adjust starting of sync period exactly from synchronisation date
                        $period = clone $period;
                        $period->date_start = $synchronisationStartDate;
                        $periods[self::INTERMEDIATE_PERIOD_KEY] = $period;
                        unset($periods[$k]);
                        // Sort periods by key, null first
                        ksort($periods, SORT_STRING);
                    }
                }

                // Sort array of periods by $period->date_start
                uasort($periods, function ($a, $b) {
                    $aDateStart = $a->date_start ?: '1900-01-01 00:00:00';
                    $bDateStart = $b->date_start ?: '1900-01-01 00:00:00';

                    return strtotime($aDateStart) <=> strtotime($bDateStart);
                });

                // Filter periods by created at
                $periodsGroupedBySalesCategoryId[$salesCategoryId] = array_filter($periods, function ($period) use ($synchronisationStartDate) {
                    return strtotime($period->date_start ?? '1900-01-01 00:00:00') >= strtotime($synchronisationStartDate);
                });
            }

            return $periodsGroupedBySalesCategoryId;
        };

        $beforeSyncFn = function (Product $targetProduct) {
            $targetProduct->global_marketplace_sync_version = 0;
            $targetProduct->save(false);
        };

        $afterSyncFn = function (Product $targetProduct) use ($globalMarketplaceSyncVersion) {
            $targetProduct->global_marketplace_sync_version = $globalMarketplaceSyncVersion;
            $targetProduct->save(false);
        };

        $this->syncSourceProductToTargetProducts(
            $globalMarketplaceProduct,
            $targetProducts,
            $filterPeriodsFn,
            $beforeSyncFn,
            $afterSyncFn
        );
    }

    /**
     * Compares periods and items from source product to target products and syncs them.
     *
     * @param Product $sourceProduct
     * @param Product[] $targetProducts
     * @param callable|null $filterPeriodsFn Allows to specify some logic to filter periods which should be synced
     * @return void
     */
    public function syncSourceProductToTargetProducts(
        Product $sourceProduct,
        array $targetProducts,
        callable $filterPeriodsFn = null,
        callable $beforeSyncFn = null,
        callable $afterSyncFn = null
    ): void
    {
        $sourcePeriodsGroupedAll = ProductCostPeriod::getExistingPeriods(
            $sourceProduct->marketplace_id,
            $sourceProduct->seller_id,
            $sourceProduct->sku,
            null,
            false
        );

        foreach ($targetProducts as $targetProduct) {
            try {
                if ($beforeSyncFn !== null) {
                    $beforeSyncFn($targetProduct);
                }

                $targetPeriodsGrouped = ProductCostPeriod::getExistingPeriods(
                    $targetProduct->marketplace_id,
                    $targetProduct->seller_id,
                    $targetProduct->sku,
                    null,
                    false
                );
                $sourcePeriodsGrouped = $sourcePeriodsGroupedAll;

                if ($filterPeriodsFn !== null) {
                    $sourcePeriodsGrouped = $filterPeriodsFn($sourcePeriodsGrouped, $targetProduct);
                    $targetPeriodsGrouped = $filterPeriodsFn($targetPeriodsGrouped, $targetProduct);
                }

                /**
                 * @var string $salesCategoryId
                 * @var ProductCostPeriod[] $sourcePeriods
                 */
                foreach ($sourcePeriodsGrouped as $salesCategoryId => $sourcePeriods) {
                    /** @var ProductCostPeriod[] $targetPeriods */
                    $targetPeriods = $targetPeriodsGrouped[$salesCategoryId] ?? [];

                    $this->compareAndSyncPeriods($sourcePeriods, $targetPeriods, $targetProduct);
                }

                if ($afterSyncFn !== null) {
                    $afterSyncFn($targetProduct);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->info('Finished copying periods from one product to another products');
    }

    /**
     * Search for differences between source and target periods and sync them.
     *
     * @param ProductCostPeriod[] $sourcePeriods
     * @param ProductCostPeriod[] $targetPeriods
     * @param Product $targetProduct
     * @return void
     */
    protected function compareAndSyncPeriods(array $sourcePeriods, array $targetPeriods, Product $targetProduct)
    {
        $this->info("Sync periods for product");
        $this->info([
            'id' => $targetProduct->id,
            'marketplace_id' => $targetProduct->marketplace_id,
            'seller_id' => $targetProduct->seller_id,
            'sku' => $targetProduct->sku
        ]);

        foreach ($sourcePeriods as $dateStart => $sourcePeriod) {
            $targetPeriod = $targetPeriods[$dateStart] ?? null;

            if ($dateStart === self::INTERMEDIATE_PERIOD_KEY
                && null !== $targetPeriod
            ) {
                $targetPeriod->refresh();

                if ($targetPeriod->date_start !== $sourcePeriod->date_start) {
                    // Compare target and source periods by their unique ids
                    $targetPeriodsUniqId = $targetPeriod->getUniqId();
                    $sourcePeriodsUniqId = $sourcePeriod->getUniqId();

                    // That means that target period have different items, and we should split it.
                    if ($targetPeriodsUniqId !== $sourcePeriodsUniqId) {
                        $targetPeriod = null;
                    }
                }
            }

            if (null === $targetPeriod) {
                $targetPeriod = $this->duplicateProductCostPeriod(
                    $sourcePeriod,
                    $targetProduct->marketplace_id
                );
                $dateStartKey = empty($targetPeriod->date_start) && empty($targetPeriod->date_end)
                    ? $dateStart
                    : $targetPeriod->date_start
                ;

                if ($dateStart === self::INTERMEDIATE_PERIOD_KEY) {
                    $dateStartKey = $dateStart;
                }

                $targetPeriods[$dateStartKey] = $targetPeriod;
            }

            $this->syncProductCostPeriodItems($sourcePeriod, $targetPeriod);
        }

        $redundantPeriods = array_diff_key($targetPeriods, $sourcePeriods);
        $this->deleteProductCostPeriods($redundantPeriods);
    }

    /**
     * @param ProductCostPeriod $sourcePeriod
     * @param ProductCostPeriod $targetPeriod
     * @return void
     */
    protected function syncProductCostPeriodItems(ProductCostPeriod $sourcePeriod, ProductCostPeriod $targetPeriod): void
    {
        /** @var ProductCostItem[] $sourcePeriodItems */
        $sourcePeriodItems = $sourcePeriod->getItems()->all();
        /** @var ProductCostItem[] $targetPeriodItems */
        $targetPeriodItems = $targetPeriod->getItems()->all();

        foreach ($sourcePeriodItems as $k => $sourcePeriodItem) {
            $sourcePeriodItems[$sourcePeriodItem->getUniqId()] = $sourcePeriodItem;
            unset($sourcePeriodItems[$k]);
        }

        foreach ($targetPeriodItems as $k => $targetPeriodItem) {
            $targetPeriodItems[$targetPeriodItem->getUniqId()] = $targetPeriodItem;
            unset($targetPeriodItems[$k]);
        }

        $missingItems = array_diff_key($sourcePeriodItems, $targetPeriodItems);
        $redundantItems = array_diff_key($targetPeriodItems, $sourcePeriodItems);

        $this->duplicateProductCostItems($missingItems, $targetPeriod);
        $this->deleteProductCostItems($redundantItems);

        $targetPeriod->source = $sourcePeriod->source;
        $targetPeriod->save(false);
    }

    /**
     * @param ProductCostItem[] $productCostItems
     * @return void
     */
    protected function deleteProductCostItems(array $productCostItems): void
    {
        foreach ($productCostItems as $productCostItem) {
            $this->info("Deleting product cost item");
            $this->info([
                'id' => $productCostItem->id,
                'product_cost_category_id' => $productCostItem->product_cost_category_id,
                'amount_total' => $productCostItem->amount_total,
                'currency_id' => $productCostItem->currency_id
            ]);
            $productCostItem->delete();
        }
    }

    /**
     * Duplicates product cost items to target period.
     *
     * @param ProductCostItem[] $productCostItems
     * @param ProductCostPeriod $targetPeriod
     * @return void
     */
    protected function duplicateProductCostItems(
        array $productCostItems,
        ProductCostPeriod $targetPeriod
    ): void
    {
        foreach ($productCostItems as $productCostItem) {
            $this->info("Creating product cost item based on source item");
            $this->info([
                'id' => $productCostItem->id,
                'product_cost_category_id' => $productCostItem->product_cost_category_id,
                'amount_total' => $productCostItem->amount_total,
                'currency_id' => $productCostItem->currency_id
            ]);

            $targetPeriodItem = $productCostItem->makeDuplicate();
            $targetPeriodItem->product_cost_period_id = $targetPeriod->id;
            $targetPeriodItem->marketplace_currency_rate = null;
            $targetPeriodItem->marketplace_currency_id = null;
            $targetPeriodItem->save(false);

            $this->info('Product cost item created successfully');
            $this->info([
                'id' => $targetPeriodItem->id,
                'product_cost_category_id' => $targetPeriodItem->product_cost_category_id,
                'amount_total' => $targetPeriodItem->amount_total,
                'currency_id' => $targetPeriodItem->currency_id
            ]);
        }
    }

    /**
     * Duplicates product cost period to target marketplace (to product with that marketplace).
     *
     * @param ProductCostPeriod $sourcePeriod
     * @param string $targetMarketplaceId
     * @return ProductCostPeriod
     */
    protected function duplicateProductCostPeriod(
        ProductCostPeriod $sourcePeriod,
        string $targetMarketplaceId
    ): ProductCostPeriod
    {
        $this->info("Creating product cost period based on source period");
        $this->info([
            'id' => $sourcePeriod->id,
            'date_start' => $sourcePeriod->date_start,
            'sales_category_id' => $sourcePeriod->sales_category_id
        ]);
        $targetPeriod = $sourcePeriod->makeDuplicate();
        $targetPeriod->marketplace_id = $targetMarketplaceId;
        $targetPeriod->save(false);

        $this->info('Period created successfully');
        $this->info([
            'id' => $sourcePeriod->id,
            'date_start' => $sourcePeriod->date_start,
            'sales_category_id' => $sourcePeriod->sales_category_id
        ]);

        return $targetPeriod;
    }

    /**
     * Simple deletes product cost periods.
     *
     * @param ProductCostPeriod[] $productCostPeriods
     * @return void
     */
    protected function deleteProductCostPeriods(array $productCostPeriods): void
    {
        foreach ($productCostPeriods as $productCostPeriod) {
            $this->info("Deleting product cost period");
            $this->info([
                'id' => $productCostPeriod->id,
                'date_start' => $productCostPeriod->date_start,
                'sales_category_id' => $productCostPeriod->sales_category_id
            ]);
            $productCostPeriod->delete();
        }
    }
}
