<?php

namespace common\components\COGSync\traits;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\widget\TransactionQueryExecutor;
use common\models\customer\clickhouse\Transaction;

trait HelperTrait
{
    private function getExistingInClickhouse(array $productInfos): array
    {
        if (empty($productInfos)) {
            return [];
        }

        $transactionQueryExecutor = new TransactionQueryExecutor();

        $filtersForm = new FiltersForm();
        $filtersForm->sellerSku = implode(',', array_column($productInfos, 'sku'));
        $tableName = Transaction::tableName();
        $existingSellerSkus = $transactionQueryExecutor
            ->queryAll("
                SELECT DISTINCT lower(SellerSKU) as SellerSKU
                FROM {$tableName} t
                WHERE 1 = 1
                [FILTERS]",
                $filtersForm
            );

        if (empty($existingSellerSkus)) {
            return [];
        }

        return array_column($existingSellerSkus, 'SellerSKU');
    }
}
