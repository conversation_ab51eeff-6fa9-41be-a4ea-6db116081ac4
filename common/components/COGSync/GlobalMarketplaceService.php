<?php

namespace common\components\COGSync;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\customerConfig\CustomerConfig;
use common\components\featureFlag\FeatureFlagService;
use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\SalesCategory;

class GlobalMarketplaceService
{
    use LogToConsoleTrait;

    private DbManager $dbManager;
    private CustomerConfig $customerConfig;
    private FeatureFlagService $featureFlagService;

    public function __construct()
    {
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->dbManager = \Yii::$app->dbManager;
        $this->featureFlagService = \Yii::$app->featureFlagService;
    }

    public function getGlobalMarketplaceId(string $sellerId, string $sellerSku = null): ?string
    {
        $globalMarketplaceId = $this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            null,
            $sellerId
        ) ?? null;

        if (empty($globalMarketplaceId) || empty($sellerSku)) {
            return $globalMarketplaceId;
        }

        // If global marketplace specified,
        // but there is no product with global marketplace - we assume that global marketplace is not set.
        $globalMarketplaceProduct = Product::find()->where([
            'marketplace_id' => $globalMarketplaceId,
            'seller_id' => $sellerId,
            'sku' => $sellerSku
        ])->cache(60 * 5)->one();

        if (empty($globalMarketplaceProduct)) {
            return null;
        }

        return $globalMarketplaceId;
    }

    public function isEnabledCostOfGoodsSync(string $sellerId): bool
    {
        return (bool)$this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            false,
            $sellerId
        );
    }

    public function isEnabledOtherFeesSync(string $sellerId): bool
    {
        return (bool)$this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            false,
            $sellerId
        );
    }

    public function isEnabledFBMShippingCostSync(string $sellerId): bool
    {
        return (bool)$this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            false,
            $sellerId
        );
    }

    public function getCurrentSyncVersion(string $sellerId): int
    {
        return (int)$this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION,
            0,
            $sellerId
        );
    }

    public function isEnabledSyncBySalesCategoryId(string $salesCategoryId, string $sellerId): bool
    {
        switch ($salesCategoryId) {
            case SalesCategory::CATEGORY_EXPENSES_OTHER_FEES:
                return $this->isEnabledOtherFeesSync($sellerId);
            case SalesCategory::CATEGORY_EXPENSES_COG:
                return $this->isEnabledCostOfGoodsSync($sellerId);
            case SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS:
                return $this->isEnabledFBMShippingCostSync($sellerId);
        }

        return false;
    }

    public function hasUnsyncedCosts(string $sellerId, string $sellerSku): bool
    {
        $globalMarketplaceId = $this->getGlobalMarketplaceId($sellerId, $sellerSku);
        
        if (empty($globalMarketplaceId)) {
            return false;
        }

        // Get global marketplace product
        $globalProduct = Product::find()->where([
            'marketplace_id' => $globalMarketplaceId,
            'seller_id' => $sellerId,
            'sku' => $sellerSku
        ])->cache(60 * 5)->one();

        if (empty($globalProduct)) {
            return false;
        }

        // Get all products with same seller and SKU
        $products = Product::find()->where([
            'seller_id' => $sellerId,
            'sku' => $sellerSku
        ])->andWhere(['!=', 'marketplace_id', $globalMarketplaceId])
        ->andWhere(['is_enabled_sync_with_global_marketplace' => true])
        ->cache(60 * 5)->all();

        $currencyRateManager = new CurrencyRateManager();
        $currentDate = new \DateTime();
        $baseCurrency = 'EUR';
        $tolerancePercent = 1; // 1% tolerance for currency fluctuations

        // Convert global product costs to EUR
        $globalBuyingPriceEur = $this->convertToBaseCurrency(
            (float)$globalProduct->buying_price,
            $globalProduct->currency_code,
            $baseCurrency,
            $currencyRateManager,
            $currentDate
        );
        $globalShippingCostEur = $this->convertToBaseCurrency(
            (float)$globalProduct->shipping_cost,
            $globalProduct->currency_code,
            $baseCurrency,
            $currencyRateManager,
            $currentDate
        );
        $globalOtherFeesEur = $this->convertToBaseCurrency(
            (float)$globalProduct->other_fees,
            $globalProduct->currency_code,
            $baseCurrency,
            $currencyRateManager,
            $currentDate
        );

        foreach ($products as $product) {
            // Convert product costs to EUR
            $productBuyingPriceEur = $this->convertToBaseCurrency(
                (float)$product->buying_price,
                $product->currency_code,
                $baseCurrency,
                $currencyRateManager,
                $currentDate
            );
            $productShippingCostEur = $this->convertToBaseCurrency(
                (float)$product->shipping_cost,
                $product->currency_code,
                $baseCurrency,
                $currencyRateManager,
                $currentDate
            );
            $productOtherFeesEur = $this->convertToBaseCurrency(
                (float)$product->other_fees,
                $product->currency_code,
                $baseCurrency,
                $currencyRateManager,
                $currentDate
            );

            // Compare with tolerance
            if (!$this->isWithinTolerance($productBuyingPriceEur, $globalBuyingPriceEur, $tolerancePercent) ||
                !$this->isWithinTolerance($productShippingCostEur, $globalShippingCostEur, $tolerancePercent) ||
                !$this->isWithinTolerance($productOtherFeesEur, $globalOtherFeesEur, $tolerancePercent)
            ) {
                return true; // Found unsynced costs
            }
        }

        return false; // All costs are synced within tolerance
    }

    private function convertToBaseCurrency(
        float $amount,
        string $fromCurrency,
        string $toCurrency,
        $currencyRateManager,
        \DateTime $date
    ): float {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $converted = $currencyRateManager->convert($amount, $fromCurrency, $toCurrency, $date);
        return $converted ?? $amount;
    }

    private function isWithinTolerance(float $value1, float $value2, float $tolerancePercent): bool
    {
        if ($value1 == 0 && $value2 == 0) {
            return true;
        }

        if ($value1 == 0 || $value2 == 0) {
            return false;
        }

        $difference = abs($value1 - $value2);
        $average = ($value1 + $value2) / 2;
        $percentageDifference = ($difference / $average) * 100;

        return $percentageDifference <= $tolerancePercent;
    }
}
