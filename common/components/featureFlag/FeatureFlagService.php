<?php

namespace common\components\featureFlag;

use common\components\cache\ChainedCache;
use common\components\core\db\dbManager\DbManager;
use common\models\Customer;
use common\models\FeatureFlag;
use common\models\FeatureFlagTarget;
use yii\base\Component;

/**
 * Service to manage feature flags
 */
class FeatureFlagService extends Component
{
    private string $cacheKeyPrefix = 'feature_flag';

    public function __construct($config = [])
    {
        parent::__construct($config);
    }

    public function flushCache(?int $customerId = null, ?string $flagName = null): void
    {
        /** @var FeatureFlag[] $flags */
        $flags = FeatureFlag::find()->all();
        if ($flagName !== null) {
            $flags = array_filter($flags, fn($flag) => $flag->name === $flagName);
        }
        /** @var ChainedCache $cache */
        $cache = \Yii::$app->chainedCache;

        // Flush cache for all default value feature flags
        foreach ($flags as $flag) {
            $cache->delete("{$this->cacheKeyPrefix}:{$flag->name}:");
        }

        if ($customerId !== null) {
            // Flush cache for a specific customer
            foreach ($flags as $flag) {
                $cache->delete("{$this->cacheKeyPrefix}:{$flag->name}:{$customerId}");
            }
            return;
        }

        $customerIds = Customer::find()->select(['id'])->cache(3600)->column();
        foreach ($customerIds as $customerId) {
            foreach ($flags as $flag) {
                $cache->delete("{$this->cacheKeyPrefix}:{$flag->name}:{$customerId}");
            }
        }

    }

    public function isEnabled(string $flag, ?int $customerId = null): bool
    {
        $cacheKey = "{$this->cacheKeyPrefix}:{$flag}:{$customerId}";

        $cache = \Yii::$app->chainedCache;

        $cachedData = $cache->get($cacheKey);

        if ($cachedData !== false && is_array($cachedData) && isset($cachedData['exists'])) {
            return $cachedData['value'];
        }

        $result = $this->checkIsEnabled($flag, $customerId);

        $cache->set($cacheKey, ['exists' => true, 'value' => $result], 3600);

        return $result;
    }

    public function getAllFlags(?int $customerId = null): array
    {
        /** @var FeatureFlag[] $flags */
        $flags = FeatureFlag::find()->all();
        $result = [];

        foreach ($flags as $flag) {
            $result[] = [
                'id' => $flag->id,
                'name' => $flag->name,
                'description' => $flag->description,
                'is_enabled' => $customerId === null ? $this->isEnabledForAll($flag->name) : $this->isEnabled($flag->name, $customerId)
            ];
        }

        return $result;
    }

    public function isEnabledForAll(string $flagName): bool
    {
        $flag = FeatureFlag::findOne(['name' => $flagName]);

        if (!$flag) {
            return false;
        }

        $globalTarget = FeatureFlagTarget::findOne(['feature_id' => $flag->id, 'customer_id' => null]);

        if (!$globalTarget) {
            return false;
        }

        $targets = FeatureFlagTarget::find()
            ->where(['feature_id' => $flag->id])->all();

        if (empty($targets)){
            return false;
        }

        foreach ($targets as $target) {
            if ($target->is_enabled === false) {
                return false;
            }
        }

        return true;
    }

    public function enable(string $flagName, int $customerId = null): bool
    {
        return $this->changeFlagTarget($flagName, $customerId, true);
    }

    public function disable(string $flagName, int $customerId = null): bool
    {
        return $this->changeFlagTarget($flagName, $customerId, false);
    }

    private function changeFlagTarget(string $flagName, ?int $customerId, bool $value): bool
    {
        $flag = FeatureFlag::findOne(['name' => $flagName]);

        if (!$flag) {
            throw new \InvalidArgumentException("Feature flag '{$flagName}' not found.");
        }

        $target = FeatureFlagTarget::findOne(['feature_id' => $flag->id, 'customer_id' => $customerId]);
        if (!$target) {
            $target = new FeatureFlagTarget();
            $target->feature_id = $flag->id;
            $target->customer_id = $customerId;
        }

        $target->is_enabled = $value;
        return $target->save();
    }

    private function checkIsEnabled(string $flag, ?int $customerId): bool
    {
        $featureFlag = FeatureFlag::find()->where(['name' => $flag])->one();

        if (!$featureFlag) {
            return false;
        }

        $query = FeatureFlagTarget::find()
            ->where(['feature_id' => $featureFlag->id])
            ->andWhere(["OR", ['customer_id' => $customerId], ['customer_id' => null]])
        ;
        /** @var FeatureFlagTarget[] $targets */
        $targets = $query->all();

        if (empty($targets)) {
            return false;
        }

        foreach ($targets as $target) {
            if ($target->customer_id === $customerId) {
                return $target->is_enabled;
            }
        }

        foreach ($targets as $target) {
            if ($target->customer_id === null) {
                return $target->is_enabled;
            }
        }

        return false;
    }

    public function changeForAll(string $flagName, bool $value): bool
    {
        $flag = FeatureFlag::findOne(['name' => $flagName]);

        if (!$flag) {
            throw new \InvalidArgumentException("Feature flag '{$flagName}' not found.");
        }

        // enable for all customers
        $targets = FeatureFlagTarget::find()
            ->where(['feature_id' => $flag->id])
            ->all();

        if (empty($targets)) {
            $target = new FeatureFlagTarget();
            $target->feature_id = $flag->id;
            $target->customer_id = null; // global
            $target->is_enabled = $value;
            $target->save();
        } else {
            /* @var FeatureFlagTarget[] $targets */
            foreach ($targets as $target) {
                $target->is_enabled = $value;
                $target->save();
            }
        }

        return true;
    }

}
