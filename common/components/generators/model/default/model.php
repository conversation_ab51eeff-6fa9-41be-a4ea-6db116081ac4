<?php
/**
 * This is the template for generating the model class of a specified table.
 */

/* @var $this yii\web\View */
/* @var $generator \common\components\generators\model\Generator */
/* @var $tableName string full table name */
/* @var $className string class name */
/* @var $queryClassName string query class name */
/* @var $tableSchema yii\db\TableSchema */
/* @var $properties array list of properties (property => [type, name. comment]) */
/* @var $labels string[] list of attribute labels (name => label) */
/* @var $rules string[] list of validation rules */
/* @var $relations array list of relations (name => relation declaration) */

$searchConditions = $generator->generateSearchConditions();

echo "<?php\n";
?>

namespace <?= $generator->ns; ?>;

use Yii;
use common\models\MainActiveRecord;

/**
* This is the API model class for table "<?= $generator->generateTableName($tableName); ?>".
*
* @OA\Schema(
* schema="<?= $className; ?>",
<?php foreach ($properties as $property => $data): ?>
*   @OA\Property(
*      property="<?= $property; ?>",
*      type="<?= $data['type'] === 'int' ? 'integer' : $data['type']; ?>",
*      description="<?= $labels[$property]; ?>"
*   ),
<?php endforeach; ?>
<?php if (!empty($relations)): ?>
*
<?php foreach ($relations as $name => $relation): ?>
<?php if (!empty($relation[2])): ?>
*   @OA\Property(property="<?= lcfirst($name); ?>", type="array", @OA\Items(ref="#/components/schemas/<?= $relation[1]; ?>")),
<?php else: ?>
*   @OA\Property(property="<?= lcfirst($name); ?>", type="object", ref="#/components/schemas/<?= $relation[1]; ?>"),
<?php endif; ?>
<?php endforeach; ?>
<?php endif; ?>
* )

<?php foreach ($properties as $property => $data): ?>
* @property <?= "{$data['type']} \${$property}"  . ($data['comment'] ? ' ' . strtr($data['comment'], ["\n" => ' ']) : '') . "\n"; ?>
<?php endforeach; ?>
<?php if (!empty($relations)): ?>
*
<?php foreach ($relations as $name => $relation): ?>
* @property <?= $relation[1] . ($relation[2] ? '[]' : '') . ' $' . lcfirst($name) . "\n"; ?>
<?php endforeach; ?>
<?php endif; ?>
*/
class <?= $className; ?> extends MainActiveRecord
{

    public static $dbId = '<?= $generator->db; ?>';

    /**
    * {@inheritdoc}
    */
    public static function tableName()
    {
        return '<?= $generator->generateTableName($tableName); ?>';
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [<?= empty($rules) ? '' : ("\n            " . implode(",\n            ", $rules) . ",\n        "); ?>];
    }

    public function search($params)
    {
        $query = <?= $className; ?>::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        <?= implode("\n        ", $searchConditions); ?>

        return $query;
    }


<?php foreach ($relations as $name => $relation): ?>

    /**
    * @return \yii\db\ActiveQuery
    */
    public function get<?= $name; ?>()
    {
	    <?= $relation[0] . "\n"; ?>
    }
<?php endforeach; ?>
<?php if ($queryClassName): ?>
	<?php
    $queryClassFullName = ($generator->ns === $generator->queryNs) ? $queryClassName : '\\' . $generator->queryNs . '\\' . $queryClassName;
    echo "\n";
    ?>

    /**
    * {@inheritdoc}
    * @return <?= $queryClassFullName; ?> the active query used by this AR class.
    */
    public static function find()
    {
        return new <?= $queryClassFullName; ?>(get_called_class());
    }
<?php endif; ?>
}
