<?php

namespace common\components\generators\model;

use common\components\generators\traits\GeneratorTrait;
use yii\db\Schema;
use yii\gii\generators\model\Generator as ModelGenerator;

/**
 * @mixin GeneratorTrait
 */
class Generator extends ModelGenerator
{
    use GeneratorTrait;

    public $baseClass = 'common\models\MainActiveRecord';


    /**
     * Generates validation rules for the specified table.
     * @param  \yii\db\TableSchema $table the table schema
     * @return array               the generated validation rules
     */
    public function generateRules($table)
    {
        $rules = parent::generateRules($table);

        $onSearch = [];
        foreach ($table->columns as $column) {
            $onSearch['safe'][] = $column->name;
        }

        $attributes = implode("', '", $onSearch['safe']);
        $rules[] = "[['$attributes'],'safe','on'=>'search']";

        return $rules;
    }
}
