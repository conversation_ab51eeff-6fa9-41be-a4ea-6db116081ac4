<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */
namespace common\components\generators\rest;

use common\components\generators\traits\GeneratorTrait;
use yii\db\ActiveRecord;
use yii\db\Connection;
use yii\gii\CodeFile;
use yii\gii\generators\crud\Generator as ParentGenerator;

/**
 * Generates REST.
 *
 * @mixin GeneratorTrait
 */
class Generator extends ParentGenerator
{
    public $baseControllerClass = 'api\components\controllers\Controller';

    protected function initDbConnection()
    {
        $this->getDbConnection();
    }

    public function setAttributes($values, $safeOnly = true)
    {
        parent::setAttributes($values, $safeOnly);

        $this->initDbConnection();
    }

    /**
     * {@inheritdoc}
     */
    public function getName()
    {
        return 'REST Generator';
    }

    /**
     * {@inheritdoc}
     */
    public function getDescription()
    {
        return 'This generator generates a controller that implement REST methods (Create, Read, Update, Delete)
            operations for the specified data model.';
    }

    /**
     * {@inheritdoc}
     */
    public function generate()
    {
        $controllerFile = \Yii::getAlias('@' . str_replace('\\', '/', ltrim($this->controllerClass, '\\')) . '.php');

        $files = [
            new CodeFile($controllerFile, $this->render('controller.php')),
        ];

        return $files;
    }

    /**
     * <AUTHOR>
     * @return Connection
     */
    protected function getDbConnection()
    {
        /* @var $class ActiveRecord */
        $class = $this->modelClass;

        if ($class) {
            return (new $class)->getDb();
        }

        return null;
    }

    /**
     * <AUTHOR>
     * @return array
     */
    public function getColumns()
    {
        $columns = [];
        /** @var ActiveRecord $model */
        $model = new $this->modelClass;
        $table = $this->getDbConnection()->getTableSchema($model::tableName());

        if ($table) {
            foreach ($table->columns as $column) {
                $columns[$column->name] = $column->type;
            }
        }

        return $columns;
    }
}
