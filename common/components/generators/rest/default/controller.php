<?php
/**
 * This is the template for generating a REST controller class file.
 */
use yii\helpers\StringHelper;

/* @var $this yii\web\View */
/* @var $generator common\components\generators\rest\Generator */

$controllerClass = StringHelper::basename($generator->controllerClass);
/** @var \common\models\MainActiveRecord $modelClass */
$modelClass = StringHelper::basename($generator->modelClass);

/** @var \yii\db\ActiveRecord $model */
$model = new $generator->modelClass;

$isCustomerRelated = in_array($model::$dbId, ['customer'], true) || $model->hasAttribute('customer_id');

echo "<?php\n";
?>

namespace <?= StringHelper::dirname(ltrim($generator->controllerClass, '\\')); ?>;

use Yii;
use api\components\controllers\Controller;
use <?= ltrim($generator->modelClass, '\\'); ?>;


/**
 * <?= $controllerClass; ?> implements the REST actions for <?= $modelClass; ?> model.
* @OA\Get(path="<?= \yii\helpers\BaseInflector::camel2id('/v1/'.lcfirst($modelClass)); ?>",
*   summary="Retrieves the collection of <?= $modelClass; ?> resources.",
*   tags={"<?= $modelClass; ?>"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
<?php foreach ($generator->getColumns() as $column=>$type):?>
*     @OA\Parameter(
*         name="<?= $column; ?>",
*         in="query",
*         description="<?= $model->getAttributeLabel($column); ?>",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
<?php endforeach;?>
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of <?= $modelClass; ?> resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/<?= $modelClass; ?>"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Get(path="<?= \yii\helpers\BaseInflector::camel2id('/v1/'.lcfirst($modelClass)).'/{id}' ?>",
*   summary="View the <?= $modelClass; ?> resource",
*   tags={"<?= $modelClass; ?>"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="path",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*   @OA\Response(
*     response=200,
*     description="View the <?= $modelClass; ?> resource",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/<?= $modelClass; ?>"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="<?= \yii\helpers\BaseInflector::camel2id('/v1/'.lcfirst($modelClass)); ?>",
*   summary="Create <?= $modelClass; ?> resource",
*   tags={"<?= $modelClass; ?>"},
*   security={{"oauth2":{}}},
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
<?php foreach ($generator->getColumns() as $column=>$type):?>
    <?php if ($column === 'id') {
    continue;
} ?>
*	 	            @OA\Property(property="<?= $column; ?>", type="<?= $type === 'int' ? 'integer' : ($type === 'text' ? 'string': $type); ?>"),
<?php endforeach;?>
*          )
*        ),
*
*
*   @OA\Response(
*     response=200,
*     description="<?= $modelClass; ?> resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/<?= $modelClass; ?>"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="<?= \yii\helpers\BaseInflector::camel2id('/v1/'.lcfirst($modelClass)); ?>",
*   summary="Update the <?= $modelClass; ?> resource",
*   tags={"<?= $modelClass; ?>"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
<?php foreach ($generator->getColumns() as $column=>$type):?>
	<?php if ($column === 'id') {
    continue;
} ?>
*	 	       @OA\Property(property="<?= $column; ?>", type="<?= $type === 'int' ? 'integer' : ($type === 'text' ? 'string': $type);?>"),
<?php endforeach;?>
*          )
*        ),
*
*   @OA\Response(
*     response=200,
*     description="<?= $modelClass; ?> resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/<?= $modelClass; ?>"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="<?= \yii\helpers\BaseInflector::camel2id('/v1/'.lcfirst($modelClass)); ?>",
*   summary="Delete <?= $modelClass; ?> resource",
*   tags={"<?= $modelClass; ?>"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=true,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*   @OA\Response(
*     response=204,
*     description="<?= $modelClass; ?> resource is deleted."
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* )
*/
class <?= $controllerClass; ?> extends <?= StringHelper::basename($generator->baseControllerClass) . "\n"; ?>
{

    protected $isCustomerRelated =  <?php echo $isCustomerRelated ? 'true' : 'false'; ?>;

    public function actions()
    {
        $actions = parent::actions();
        //unset($actions['create']);
        //unset($actions['update']);
        //unset($actions['delete']);

        return $actions;
    }

    public $modelClass = '<?= ltrim($generator->modelClass, '\\'); ?>';
}
