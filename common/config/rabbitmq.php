<?php

use common\components\rabbitmq\consumers\FinancialEventInitConsumer;
use common\components\rabbitmq\consumers\CronConsumer;
use common\components\rabbitmq\consumers\FinancialEventRefreshConsumer;
use common\components\rabbitmq\consumers\EventPeriodsFromDbToClickhouseConsumer;
use common\components\rabbitmq\message\MessageAbstract;
use common\components\rabbitmq\message\MessageInterface;
use PhpAmqpLib\Connection\AMQPSSLConnection;
use PhpAmqpLib\Connection\AMQPLazyConnection;
use mikemadisonweb\rabbitmq\Configuration;
use common\components\rabbitmq\consumers\order\LoadOrdersInitConsumer;
use common\components\rabbitmq\consumers\order\LoadOrdersRefreshConsumer;
use common\components\rabbitmq\consumers\order\LoadOrderConsumer;
use common\components\rabbitmq\consumers\order\LoadOrderItemsConsumer;

const LOAD_ORDER_ITEMS_QUEUE_COUNT = 20;

$bindings = [
    [
        'queue' => 'DATA_IMPORT.SPLIT_INTO_PARTS',
        'exchange' => MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
        'routing_keys' => ['split-into-parts'],
    ],
    [
        'queue' => 'DATA_IMPORT.PROCESS_PART',
        'exchange' => MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
        'routing_keys' => ['process-part'],
    ],
    [
        'queue' => 'DATA_IMPORT.BULK_EDIT.PROCESS_PART',
        'exchange' => MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
        'routing_keys' => ['process-part-bulk-edit'],
    ],
    [
        'queue' => 'DATA_EXPORT.MERGE_PARTS',
        'exchange' => MessageInterface::EXCHANGE_NAME_DATA_EXPORT,
        'routing_keys' => ['merge-parts'],
    ],
    [
        'queue' => 'DATA_EXPORT.PROCESS_PART',
        'exchange' => MessageInterface::EXCHANGE_NAME_DATA_EXPORT,
        'routing_keys' => ['process-part'],
    ],
    [
        'queue' => 'EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE',
        'exchange' => 'EX-CLICKHOUSE',
        'routing_keys' => ['from_cache_to_clickhouse'],
    ],
    [
        'queue' => 'CRON.QUEUE',
        'exchange' => 'EX-CRON',
        'routing_keys' => [],
    ],
    [
        'queue' => 'FINANCIAL.EVENT.INIT.QUEUE.0',
        'exchange' => 'EX-FINANCIAL-EVENT-INIT',
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'FINANCIAL.EVENT.INIT.QUEUE.1',
        'exchange' => 'EX-FINANCIAL-EVENT-INIT',
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'FINANCIAL.EVENT.REFRESH.QUEUE.0',
        'exchange' => 'EX-FINANCIAL-EVENT-REFRESH',
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'FINANCIAL.EVENT.REFRESH.QUEUE.1',
        'exchange' => 'EX-FINANCIAL-EVENT-REFRESH',
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'LOAD.ORDER.QUEUE.0',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'LOAD.ORDER.QUEUE.1',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'LOAD.ORDER.QUEUE.2',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
        'routing_keys' => ['2'],
    ],
    [
        'queue' => 'LOAD.ORDER.QUEUE.3',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
        'routing_keys' => ['3'],
    ],
    [
        'queue' => 'LOAD.ORDER.QUEUE.4',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
        'routing_keys' => ['4'],
    ],
    [
        'queue' => 'LOAD.ORDERS.INIT.QUEUE.0',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_INIT,
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'LOAD.ORDERS.INIT.QUEUE.1',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_INIT,
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'LOAD.ORDERS.REFRESH.QUEUE.0',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_REFRESH,
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'LOAD.ORDERS.REFRESH.QUEUE.1',
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_REFRESH,
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'COG_CHANGES.0',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['0'],
    ],
    [
        'queue' => 'COG_CHANGES.1',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['1'],
    ],
    [
        'queue' => 'COG_CHANGES.2',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['2'],
    ],
    [
        'queue' => 'COG_CHANGES.3',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['3'],
    ],
    [
        'queue' => 'COG_CHANGES.4',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['4'],
    ],
    [
        'queue' => 'COG_CHANGES_BULK',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHANGES,
        'routing_keys' => ['bulk'],
    ],
    [
        'queue' => 'INDIRECT_COST_CHANGES',
        'exchange' => MessageInterface::EXCHANGE_NAME_INDIRECT_COST_CHANGES,
        'routing_keys' => [],
    ],
    [
        'queue' => 'COG_CHECK_REFUNDS',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_CHECK_REFUNDS,
    ],
    [
        'queue' => 'COG_SYNC',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_SYNC,
    ],
    [
        'queue' => 'COG_SYNC.FROM_GLOBAL_MARKETPLACE',
        'exchange' => MessageInterface::EXCHANGE_NAME_COG_SYNC_GLOBAL_MARKETPLACE,
    ],
    [
        'queue' => 'PRODUCT_SYNC',
        'exchange' => MessageInterface::EXCHANGE_NAME_PRODUCT_SYNC,
    ],
    [
        'queue' => 'CUSTOMER_PROCESS',
        'exchange' => MessageInterface::EXCHANGE_NAME_CUSTOMER_PROCESS,
    ],
    [
        'queue' => 'AMAZON_REPORTS.CREATE',
        'exchange' => MessageInterface::EXCHANGE_NAME_AMAZON_REPORT,
        'routing_keys' => ['create']
    ],
    [
        'queue' => 'AMAZON_REPORTS.SYNC_STATUSES',
        'exchange' => MessageInterface::EXCHANGE_NAME_AMAZON_REPORT,
        'routing_keys' => ['sync-statuses']
    ],
    [
        'queue' => 'AMAZON_REPORTS.HANDLE_READY_INIT',
        'exchange' => MessageInterface::EXCHANGE_NAME_AMAZON_REPORT,
        'routing_keys' => ['init']
    ],
    [
        'queue' => 'AMAZON_REPORTS.HANDLE_READY_REFRESH',
        'exchange' => MessageInterface::EXCHANGE_NAME_AMAZON_REPORT,
        'routing_keys' => ['refresh']
    ],
    [
        'queue' => 'PPC_COSTS.APPLY',
        'exchange' => MessageInterface::EXCHANGE_NAME_PPC_COSTS,
        'routing_keys' => ['apply']
    ],
    [
        'queue' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE',
        'exchange' => MessageInterface::EXCHANGE_NAME_CLICKHOUSE,
        'routing_keys' => ['save-order-item'],
    ],
    [
        'queue' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE.DLQ',
        'exchange' => MessageInterface::EXCHANGE_NAME_CLICKHOUSE_DLX,
        'routing_keys' => ['save-order-item']
    ],
];

$queues = [
    [
        'name' => 'INDIRECT_COST_CHANGES',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'AMAZON_REPORTS.CREATE',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'AMAZON_REPORTS.SYNC_STATUSES',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'AMAZON_REPORTS.HANDLE_READY_INIT',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'AMAZON_REPORTS.HANDLE_READY_REFRESH',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'PPC_COSTS.APPLY',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'CUSTOMER_PROCESS',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES.0',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES.1',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES.2',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES.3',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES.4',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10]
        ]
    ],
    [
        'name' => 'COG_CHANGES_BULK',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'COG_SYNC',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'COG_SYNC.FROM_GLOBAL_MARKETPLACE',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'PRODUCT_SYNC',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'COG_CHECK_REFUNDS',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'CRON.QUEUE',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'FINANCIAL.EVENT.INIT.QUEUE.0',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'FINANCIAL.EVENT.INIT.QUEUE.1',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'FINANCIAL.EVENT.REFRESH.QUEUE.0',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'FINANCIAL.EVENT.REFRESH.QUEUE.1',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDER.QUEUE.0',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDER.QUEUE.1',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDER.QUEUE.2',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDER.QUEUE.3',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDER.QUEUE.4',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDERS.INIT.QUEUE.0',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDERS.INIT.QUEUE.1',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDERS.REFRESH.QUEUE.0',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'LOAD.ORDERS.REFRESH.QUEUE.1',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'DATA_IMPORT.SPLIT_INTO_PARTS',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'DATA_IMPORT.PROCESS_PART',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'DATA_IMPORT.BULK_EDIT.PROCESS_PART',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'DATA_EXPORT.MERGE_PARTS',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'DATA_EXPORT.PROCESS_PART',
        'durable' => true,
        'auto_delete' => false,
    ],
    [
        'name' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-max-priority' => ['I', 10],
            'x-dead-letter-exchange' => ['S', MessageInterface::EXCHANGE_NAME_CLICKHOUSE_DLX],
            'x-dead-letter-routing-key' => ['S', 'save-order-item'],
        ]
    ],
    [
        'name' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE.DLQ',
        'durable' => true,
        'auto_delete' => false,
        'arguments' => [
            'x-message-ttl' => ['I', 60 * 5 * 1000], // try again in 5 minutes
            'x-max-priority' => ['I', 10],
            'x-dead-letter-exchange' => ['S', MessageInterface::EXCHANGE_NAME_CLICKHOUSE],
            'x-dead-letter-routing-key' => ['S', 'save-order-item'],
        ]
    ],
];

$consumers = [
    [
        'name' => 'INDIRECT_COST_CHANGES.CONSUMER',
        'callbacks' => [
            'INDIRECT_COST_CHANGES' => \common\components\IndirectCost\consumers\IndirectCostChangesConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'AMAZON_REPORTS.CREATE.CONSUMER',
        'callbacks' => [
            'AMAZON_REPORTS.CREATE' => \common\components\reports\consumers\CreateAmazonReportConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'AMAZON_REPORTS.SYNC_STATUSES.CONSUMER',
        'callbacks' => [
            'AMAZON_REPORTS.SYNC_STATUSES' => \common\components\reports\consumers\SyncAmazonReportStatusesConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'AMAZON_REPORTS.HANDLE_READY_INIT.CONSUMER',
        'callbacks' => [
            'AMAZON_REPORTS.HANDLE_READY_INIT' => \common\components\reports\consumers\HandleReadyReportConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],

    ],
    [
        'name' => 'AMAZON_REPORTS.HANDLE_READY_REFRESH.CONSUMER',
        'callbacks' => [
            'AMAZON_REPORTS.HANDLE_READY_REFRESH' => \common\components\reports\consumers\HandleReadyReportConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'PPC_COSTS.APPLY.CONSUMER',
        'callbacks' => [
            'PPC_COSTS.APPLY' => \common\components\rabbitmq\consumers\PpcCostConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'CUSTOMER_PROCESS.CONSUMER',
        'callbacks' => [
            'CUSTOMER_PROCESS' => \common\components\customerProcess\consumer\InvokeProcessConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'DATA_IMPORT.SPLIT_INTO_PARTS.CONSUMER',
        'callbacks' => [
            'DATA_IMPORT.SPLIT_INTO_PARTS' => \common\components\dataImportExport\import\consumer\SplitIntoPartsConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'DATA_IMPORT.PROCESS_PART.CONSUMER',
        'callbacks' => [
            'DATA_IMPORT.PROCESS_PART' => \common\components\dataImportExport\import\consumer\ProcessPartConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'DATA_IMPORT.BULK_EDIT.PROCESS_PART.CONSUMER',
        'callbacks' => [
            'DATA_IMPORT.BULK_EDIT.PROCESS_PART' => \common\components\dataImportExport\import\consumer\ProcessPartConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'DATA_EXPORT.MERGE_PARTS.CONSUMER',
        'callbacks' => [
            'DATA_EXPORT.MERGE_PARTS' => \common\components\dataImportExport\export\consumer\MergePartsConsumer::class
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'DATA_EXPORT.PROCESS_PART.CONSUMER',
        'callbacks' => [
            'DATA_EXPORT.PROCESS_PART' => \common\components\dataImportExport\export\consumer\ProcessPartConsumer::class
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE.CONSUMER',
        'callbacks' => [
            'EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE' => EventPeriodsFromDbToClickhouseConsumer::class
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'CRON.CONSUMER',
        // Every consumer should define one or more callbacks for corresponding queues
        'callbacks' => [
            // queue name => callback class name
            'CRON.QUEUE' => CronConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],

    [
        'name' => 'FINANCIAL.EVENT.INIT.CONSUMER.0',
        // Every consumer should define one or more callbacks for corresponding queues
        'callbacks' => [
            // queue name => callback class name
            'FINANCIAL.EVENT.INIT.QUEUE.0' => FinancialEventInitConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'FINANCIAL.EVENT.INIT.CONSUMER.1',
        // Every consumer should define one or more callbacks for corresponding queues
        'callbacks' => [
            // queue name => callback class name
            'FINANCIAL.EVENT.INIT.QUEUE.1' => FinancialEventInitConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],

    [
        'name' => 'FINANCIAL.EVENT.REFRESH.CONSUMER.0',
        // Every consumer should define one or more callbacks for corresponding queues
        'callbacks' => [
            // queue name => callback class name
            'FINANCIAL.EVENT.REFRESH.QUEUE.0' => FinancialEventRefreshConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'FINANCIAL.EVENT.REFRESH.CONSUMER.1',
        // Every consumer should define one or more callbacks for corresponding queues
        'callbacks' => [
            // queue name => callback class name
            'FINANCIAL.EVENT.REFRESH.QUEUE.1' => FinancialEventRefreshConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES.CONSUMER.0',
        'callbacks' => ['COG_CHANGES.0' => \common\components\COGSync\consumers\COGChangesConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES.CONSUMER.1',
        'callbacks' => ['COG_CHANGES.1' => \common\components\COGSync\consumers\COGChangesConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES.CONSUMER.2',
        'callbacks' => ['COG_CHANGES.2' => \common\components\COGSync\consumers\COGChangesConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES.CONSUMER.3',
        'callbacks' => ['COG_CHANGES.3' => \common\components\COGSync\consumers\COGChangesConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES.CONSUMER.4',
        'callbacks' => ['COG_CHANGES.4' => \common\components\COGSync\consumers\COGChangesConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHANGES_BULK.CONSUMER',
        'callbacks' => ['COG_CHANGES_BULK' => \common\components\COGSync\consumers\COGChangesBulkConsumer::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_SYNC.CONSUMER',
        'callbacks' => ['COG_SYNC' => \common\components\COGSync\consumers\COGSync::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'PRODUCT_SYNC.CONSUMER',
        'callbacks' => ['PRODUCT_SYNC' => \common\components\productSync\consumers\ProductSync::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_CHECK_REFUNDS.CONSUMER',
        'callbacks' => ['COG_CHECK_REFUNDS' => \common\components\COGSync\consumers\COGCheckRefunds::class],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'LOAD.ORDER.CONSUMER.0',
        'callbacks' => [
            'LOAD.ORDER.QUEUE.0' => LoadOrderConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDER.CONSUMER.1',
        'callbacks' => [
            'LOAD.ORDER.QUEUE.1' => LoadOrderConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDER.CONSUMER.2',
        'callbacks' => [
            'LOAD.ORDER.QUEUE.2' => LoadOrderConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDER.CONSUMER.3',
        'callbacks' => [
            'LOAD.ORDER.QUEUE.3' => LoadOrderConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDER.CONSUMER.4',
        'callbacks' => [
            'LOAD.ORDER.QUEUE.4' => LoadOrderConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDERS.INIT.CONSUMER.0',
        'callbacks' => [
            'LOAD.ORDERS.INIT.QUEUE.0' => LoadOrdersInitConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDERS.INIT.CONSUMER.1',
        'callbacks' => [
            'LOAD.ORDERS.INIT.QUEUE.1' => LoadOrdersInitConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ],
    [
        'name' => 'LOAD.ORDERS.REFRESH.CONSUMER.0',
        'callbacks' => [
            'LOAD.ORDERS.REFRESH.QUEUE.0' => LoadOrdersRefreshConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'LOAD.ORDERS.REFRESH.CONSUMER.1',
        'callbacks' => [
            'LOAD.ORDERS.REFRESH.QUEUE.1' => LoadOrdersRefreshConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE.CONSUMER',
        'callbacks' => [
            'ORDER_ITEM.SAVE_TO_CLICKHOUSE' => \common\components\services\order\consumer\OrderItemToClickhouseConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        // Dead letter queue
        'name' => 'ORDER_ITEM.SAVE_TO_CLICKHOUSE.DLQ.CONSUMER',
        'callbacks' => [
            'ORDER_ITEM.SAVE_TO_CLICKHOUSE.DLQ' => \common\components\services\order\consumer\OrderItemToClickhouseConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1],
    ],
    [
        'name' => 'COG_SYNC.FROM_GLOBAL_MARKETPLACE.CONSUMER',
        'callbacks' => [
            'COG_SYNC.FROM_GLOBAL_MARKETPLACE' => \common\components\COGSync\consumers\COGSyncFromGlobalMarketplaceConsumer::class
        ],
        'qos' => ['prefetch_count' => 1],
    ],
];


foreach (range(0, LOAD_ORDER_ITEMS_QUEUE_COUNT - 1) as $number) {
    $bindings[] = [
        'queue' => 'LOAD.ORDER.ITEMS.QUEUE.' . $number,
        'exchange' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER_ITEMS,
        'routing_keys' => [(string)$number],
    ];

    $queues[] = [
        'name' => 'LOAD.ORDER.ITEMS.QUEUE.' . $number,
        'durable' => true,
        'auto_delete' => false,
    ];

    $consumers[] = [
        'name' => 'LOAD.ORDER.ITEMS.CONSUMER.' . $number,
        'callbacks' => [
            'LOAD.ORDER.ITEMS.QUEUE.' . $number => LoadOrderItemsConsumer::class,
        ],
        'qos' => ['prefetch_count' => 1,],
    ];
}

return [
    'container' =>
        [
            'rabbitmq.consumer.financialeventinit' => [
                'class' => FinancialEventInitConsumer::class,
            ],
            'rabbitmq.consumer.financialeventrefresh' => [
                'class' => FinancialEventRefreshConsumer::class,
            ],
            'rabbitmq.consumer.loadordersinit' => [
                'class' => LoadOrdersInitConsumer::class,
            ],
            'rabbitmq.consumer.loadordersrefresh' => [
                'class' => LoadOrdersRefreshConsumer::class,
            ],
            'rabbitmq.consumer.loadorder' => [
                'class' => LoadOrderConsumer::class,
            ],
            'rabbitmq.consumer.loadorderitems' => [
                'class' => LoadOrderItemsConsumer::class,
            ],
        ],
    'component' => [
        'class' => Configuration::class,
        'connections' => [
            [
                // You can pass these parameters as a single `url` option: https://www.rabbitmq.com/uri-spec.html
                'type' => (bool)(getenv('RABBITMQ_SSL'))
                    ? AMQPSSLConnection::class
                    : AMQPLazyConnection::class,
                'host' => getenv('RABBITMQ_HOST'),
                'port' => getenv('RABBITMQ_PORT'),
                'user' => getenv('RABBITMQ_DEFAULT_USER'),
                'password' => getenv('RABBITMQ_DEFAULT_PASS'),
                'vhost' => getenv('RABBITMQ_VHOST'),
                'heartbeat' => 60 * 60 * 6,
                'connection_timeout' => 60,
                'read_write_timeout' => 60,
                'channel_rpc_timeout' => 60,
                'ssl_context' => (bool)(getenv('RABBITMQ_SSL'))
                    ? [
                        'capath' => null,
                        'cafile' => null,
                        'verify_peer' => false,
                    ]
                    : null
            ]
            // When multiple connections is used you need to specify a `name` option for each one and define them in producer and consumer configuration blocks
        ],
        'exchanges' => [
            [
                'name' => 'EX-CRON',
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_FINANCIAL_EVENT_INIT,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_FINANCIAL_EVENT_REFRESH,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_INIT,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_LOAD_ORDERS_REFRESH,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_LOAD_ORDER_ITEMS,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_CLICKHOUSE,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_CLICKHOUSE_DLX,
                'type' => 'direct'
            ],
            [
                'name' => 'EX-COG-CHANGES',
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_PRODUCT_SYNC,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_COG_SYNC,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_COG_CHECK_REFUNDS,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_DATA_IMPORT,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_DATA_EXPORT,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_INDIRECT_COST_CHANGES,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_CUSTOMER_PROCESS,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_AMAZON_REPORT,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_PPC_COSTS,
                'type' => 'direct'
            ],
            [
                'name' => MessageInterface::EXCHANGE_NAME_COG_SYNC_GLOBAL_MARKETPLACE,
                'type' => 'direct'
            ],
        ],
        'queues' => $queues,
        'bindings' => $bindings,
        'producers' => [
            [
                'name' => MessageAbstract::PRODUCER_NAME,
            ],
        ],
        'consumers' => $consumers,
    ],
];
