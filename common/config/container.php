<?php

use common\components\currencyRate\CurrencyRateManager;

return [
    'definitions' => [
        'yii\db\BatchQueryResult' => 'common\components\BatchQueryResultWithLimitOffset',
        'bash<PERSON>ev\clickhouse\Schema' => 'common\components\core\db\clickhouse\Schema',
        'bashkarev\clickhouse\Command' => 'common\components\core\db\clickhouse\Command'
    ],
    'singletons' => [
        'internalApiClient' => function ($container, $params, $config) {
            $authTokenUrl = getenv('AUTH_TOKEN_URL');
            $authTokenUrl = str_replace('oauth2/token', '', $authTokenUrl);

            $mainOptions = [
                'baseApiUri' => getenv('MAIN_API_URL'),
                'baseAuthUri' => $authTokenUrl,
                'clientId' => getenv('INTERNAL_API_CLIENT_ID'),
                'clientSecret' => getenv('INTERNAL_API_CLIENT_SECRET'),
                'grantType' => getenv('INTERNAL_API_GRANT_TYPE')
            ];

            return new \SellerLogic\InternalApi\MainApiClient(
                $mainOptions,
                new \SellerLogic\InternalApi\Authorization\tokenStorage\FileTokenStorage('/app/main_token.json')
            );
        },
        'basApiClient' => function ($container, $params, $config) {
            $mainOptions = [
                'baseApiUri' => getenv('BAS_API_URL'),
                'baseAuthUri' => getenv('BAS_AUTH_TOKEN_URL'),
                'clientId' => getenv('BAS_INTERNAL_API_CLIENT_ID'),
                'clientSecret' => getenv('BAS_INTERNAL_API_CLIENT_SECRET'),
                'grantType' => getenv('BAS_INTERNAL_API_GRANT_TYPE')
            ];

            return new \SellerLogic\InternalApi\BasApiClient(
                $mainOptions,
                new \SellerLogic\InternalApi\Authorization\tokenStorage\FileTokenStorage('/app/bas_token.json')
            );
        },
        'COGSynchronizer' => function ($container, $params, $config) {
            $messagesSender = new \common\components\rabbitmq\MessagesSender();
            $dbManager = Yii::$app->dbManager;
            $prometheus = \Yii::$app->prometheus;
            $queryExecutor = new \common\components\widget\TransactionQueryExecutor();

            return new \common\components\COGSync\COGSynchronizer(
                $dbManager,
                $queryExecutor,
                $messagesSender,
                new \common\components\COGSync\PeriodsSaver(
                    $dbManager,
                    $prometheus,
                    $messagesSender,
                    new \common\components\COGSync\ProductToProductSynchronizer()
                ),
                new \common\components\COGSync\ProductsSaver()
            );
        },
        'COGTransactionsGenerator' => function ($container, $params, $config) {
            return new \common\components\COGSync\COGTransactionsGenerator(
                new \common\components\currencyRate\CurrencyRateManager()
            );
        },
        'COGChangesManager' => function ($container, $params, $config) {
            return new \common\components\COGSync\COGChangesManager(
                new \common\components\rabbitmq\MessagesSender(),
                new \common\components\dataBuffer\BufferFactory(),
                Yii::$app->dbManager,
                $container->get('COGTransactionsGenerator'),
                new \common\components\widget\TransactionQueryExecutor(),
                new \common\components\customerConfig\CustomerConfig(
                    Yii::$app->dbManager,
                    \Yii::$app->fastPersistentCache
                ),
                \Yii::$app->customerComponent
            );
        },
        'periodDateChangeCalculator' => function ($container, $params, $config) {
            return new  \common\components\COGSync\PeriodDateChangeCalculator();
        },
        'dataReLoader' => function ($container, $params, $config) {
            return new  \common\components\dataReloader\DataReLoader(
                Yii::$app->dbManager,
                new \common\components\rabbitmq\MessagesSender(),
                \Yii::$app->fastPersistentCache,
                getenv('CLICKHOUSE_CLUSTER_NAME')
            );
        },
        'tableNameGenerator' =>  function ($container, $params, $config) {
            return new \common\components\dbStructure\TableNameGenerator(Yii::$app->dbManager);
        },
        'blueGreenBuilder' =>  function (\yii\di\Container $container, $params, $config) {
            return new \common\components\dbStructure\BlueGreenBuilder(
                Yii::$app->dbManager,
                $container->get('tableNameGenerator'),
                new \common\components\dbStructure\dbChangesApplier\Factory(Yii::$app->dbManager)
            );
        },
        'currencyRateManager' => function (\yii\di\Container $container, $params, $config) {
            return new CurrencyRateManager();
        },
        'customerConfig' => function (\yii\di\Container $container, $params, $config)
        {
            return new \common\components\customerConfig\CustomerConfig(
                Yii::$app->dbManager,
                Yii::$app->cache,
            );
        }
    ]
];
