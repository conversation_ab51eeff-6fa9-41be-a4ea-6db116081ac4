<?php

use SellerLogic\InternalApi\Authorization\tokenStorage\FileTokenStorage;
use SellerLogic\InternalApi\MainApiClient;
use common\components\translation\Translation;
use nuwber\yii2redis\Cache as RedisCache;
use yii\helpers\Inflector;

$enableSchemaCache = true;
$schemaCache = 'schemaRedisCache';
$schemaCacheDuration = 60 * 60 * 24;

$enableQueryCache = true;
$queryCache = 'cache';
$queryCacheDuration = 5 * 60;

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? null;
if ($requestMethod === 'GET') {
    $queryCache = 'chainedCache';
    $schemaCache = 'schemaChainedCache';
}

$container = require(__DIR__ . '/container.php');
$rabbitmq = require(__DIR__ . '/rabbitmq.php');
$container['singletons'] = array_merge($container['singletons'], $rabbitmq['container']);

$applicationName = 'bas_' . YII_ENV . '_';

if (!empty($argv[1])) {
    $applicationName .= 'console_' . \yii\helpers\Inflector::slug(
        str_replace('/', '-', $argv[1])
    );
} else {
    $applicationName .= (getenv('PROCESS_NAME') ?: 'undefined_process');
}

$dbParams = [
    'dbHost' => getenv('APP_DB_HOST'),
    'dbSlaveHost' => getenv('APP_DB_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_PORT'),
    'dbSlavePort' => getenv('APP_DB_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_USERNAME'),
    'dbPassword' => getenv('APP_DB_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];

$db1Params = [
    'dbHost' => getenv('APP_DB_1_HOST'),
    'dbSlaveHost' => getenv('APP_DB_1_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_1_PORT'),
    'dbSlavePort' => getenv('APP_DB_1_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_1_USERNAME'),
    'dbPassword' => getenv('APP_DB_1_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_1_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_1_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];

$repricerDbParams = [
    'host' => getenv('REPRICER_DB_HOST'),
    'host1' => getenv('REPRICER_DB_HOST_1'),
    'user' => getenv('REPRICER_DB_USERNAME'),
    'password' => getenv('REPRICER_DB_PASSWORD'),
    'clientDbNamePrefix' => 'repricer_customer_client_db_',
    'mainDbNamePrefix' => 'repricer_main_db'
];

$repricerEventDbParams = [
    'host' => getenv('REPRICER_EVENT_DB_HOST'),
    'user' => getenv('REPRICER_EVENT_DB_USERNAME'),
    'password' => getenv('REPRICER_EVENT_DB_PASSWORD'),
    'dbName' => getenv('REPRICER_EVENT_DB_NAME')
];

$customerServiceDbParams = [
    'host' => getenv('CUSTOMER_SERVICE_DB_HOST'),
    'port' => getenv('CUSTOMER_SERVICE_DB_PORT'),
    'user' => getenv('CUSTOMER_SERVICE_DB_USERNAME'),
    'password' => getenv('CUSTOMER_SERVICE_DB_PASSWORD'),
    'dbName' => getenv('CUSTOMER_SERVICE_DB_NAME')
];

$redisConfig = [
    'class' => 'common\components\cache\RedisSafeConnection',
    'hostname' => getenv('APP_REDIS_HOST'),
    'port' =>  getenv('APP_REDIS_PORT'),
    'password' => getenv('APP_REDIS_PASSWORD') ?: null,
    'retries' => 10,
    'dataTimeout' => 30,
];

$logTargets = [
    [
        'class' => 'fl0v\yii2\rollbar\RollbarTarget',
        'levels' => ['error', 'warning'/*, 'info'*/],
        'exportInterval' => 1,
        'logVars' => [],
    ]
];

$logTraceLevel = YII_DEBUG ? 3 : 0;

try {
    // Debug and tmp purpose only
    $customerId = $_GET['customerId'] ?? 0;
    if (in_array($customerId, [1373, 700])) {
        $logTraceLevel = 3;
        $logTargets[] = [
            'class' => 'yii\log\FileTarget',
            'levels' => ['error', 'warning', 'trace', 'profile'],
            'microtime' => true,
            'logFile' => sprintf(
                "@console/runtime/logs/customer_%d/%s/%s_%s.log",
                $customerId,
                date('Y-m-d'),
                date('H-i-s') . '_' . explode('.', microtime(true))[1],
                substr(Inflector::slug($_SERVER['REQUEST_URI']), 0, 200)
            ),
        ];
    }
} catch (Throwable $e) {
}

defined('DEFAULT_RDS_HOST') || define('DEFAULT_RDS_HOST', $dbParams['dbHost']);

$config = [
    'vendorPath' => dirname(__DIR__, 2) . '/vendor',
    'components' => [
        'translation' => [
            'class' => Translation::class,
        ],
        'featureFlagService' => [
            'class' => 'common\components\featureFlag\FeatureFlagService',
        ],
        'i18n' => require(__DIR__ . '/i18n.php'),
        'clickhouse' => [
            'class' => 'bashkarev\clickhouse\Connection',
            'dsn' => sprintf('host=%s;port=%d;database=%s;connect_timeout_with_failover_ms=10',
                getenv('CLICKHOUSE_HOST'),
                getenv('CLICKHOUSE_PORT'),
                getenv('CLICKHOUSE_DB_NAME'),
            ),
            'masters' => explode(',', getenv('CLICKHOUSE_MASTER_NODES')),
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'username' => getenv('CLICKHOUSE_USERNAME'),
            'password' => getenv('CLICKHOUSE_PASSWORD'),
            'attributes' => ['max_execution_time' => 60  * 20]
        ],
        'prometheus' => [
            'class' => 'common\components\prometheus\PrometheusDummy',
            'namespace' => 'bas_' . YII_ENV,
            'defaultOptions' => [
                'host' => getenv('PROMETHEUS_REDIS_HOST'),
                'port' => getenv('PROMETHEUS_REDIS_PORT'),
                'password' => getenv('PROMETHEUS_REDIS_PASSWORD'),
//                'database' => getenv('APP_REDIS_PROMETHEUS_DB'),
                'timeout' => 1, // in seconds
                'read_timeout' => '10', // in seconds
                'persistent_connections' => false
            ]
        ],
        'rollbar' => [
            'class' => 'common\components\core\rollbar\RollbarLoader',
            'config' => [
                'access_token' => getenv('ROLLBAR_ACCESS_TOKEN') ?: '********************************',
                'enabled'      => !empty(getenv('ROLLBAR_ACCESS_TOKEN')),
                'check_ignore' => function ($isUncaught, $toLog, $payload) {
                    return \common\components\core\rollbar\IgnoreExceptionHelper::checkIgnore($toLog);
                }
            ]
        ],
        'mutex' => [
            'class' => 'yii\redis\Mutex',
            'expire' => 60 * 60,
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_MUTEX_CACHE_DB')
            ]),
        ],
        's3' => [
            'class'=> \common\components\S3Component::class,
            'aKey' => getenv('AWS_ACCESS_KEY'),
            'sKey' => getenv('AWS_SECRET_KEY'),
            'region' => getenv('AWS_REGION'),
            'version' => getenv('AWS_VERSION'),
            'bucket' => getenv('AWS_BUCKET'),
            'publicBucket' => getenv('AWS_PUBLIC_BUCKET'),
            'encryptionType' => 'AES256',
        ],
        'rabbitmq' => !empty($rabbitmq['component']) ? $rabbitmq['component'] : null,
        /**
         * DB CONFIG.
         */
        'db' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$dbParams['dbHost']};port={$dbParams['dbPort']};options=--application_name={$applicationName};dbname={$dbParams['profitDashDbName']}",
            'emulatePrepare' => true,
            'username' => $dbParams['dbUser'],
            'password' => $dbParams['dbPassword'],
            'prefix' => $dbParams['profitDashDbName'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'attributes' => [],
            'on afterOpen' => function($event) {
                $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
            },
        ],
        'db1' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$db1Params['dbHost']};port={$db1Params['dbPort']};options=--application_name={$applicationName};dbname={$db1Params['profitDashDbName']}",
            'emulatePrepare' => true,
            'username' => $db1Params['dbUser'],
            'password' => $db1Params['dbPassword'],
            'prefix' => $db1Params['profitDashDbName'] . '1',
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'attributes' => [],
        ],
        'slaveDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$dbParams['dbSlaveHost']};port={$dbParams['dbSlavePort']};options=--application_name={$applicationName};dbname={$dbParams['profitDashDbName']}",
            'emulatePrepare' => true,
            'username' => $dbParams['dbUserSlave'],
            'password' => $dbParams['dbPasswordSlave'],
            'prefix' => $dbParams['profitDashDbName'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'on afterOpen' => function($event) {
                $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
            },
        ],
        'slaveDb01' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$db1Params['dbSlaveHost']};port={$db1Params['dbSlavePort']};options=--application_name={$applicationName};dbname={$db1Params['profitDashDbName']}",
            'emulatePrepare' => true,
            'username' => $db1Params['dbUserSlave'],
            'password' => $db1Params['dbPasswordSlave'],
            'prefix' => $db1Params['profitDashDbName'] . '1',
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerCustomerClientDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "mysql:host={$repricerDbParams['host']};dbname={$repricerDbParams['clientDbNamePrefix']}",
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['clientDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerCustomerClientDb1' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "mysql:host={$repricerDbParams['host1']};dbname={$repricerDbParams['clientDbNamePrefix']}",
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['clientDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerMainDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "mysql:host={$repricerDbParams['host']};dbname={$repricerDbParams['mainDbNamePrefix']}",
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['mainDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerEventDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$repricerEventDbParams['host']};options=--application_name={$applicationName};dbname={$repricerEventDbParams['dbName']}",
            'username' => $repricerEventDbParams['user'],
            'password' => $repricerEventDbParams['password'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'customerServiceDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => "pgsql:host={$customerServiceDbParams['host']};port={$customerServiceDbParams['port']};options=--application_name={$applicationName};dbname={$customerServiceDbParams['dbName']}",
            'username' => $customerServiceDbParams['user'],
            'password' => $customerServiceDbParams['password'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'dbManager' => [
            'class' => 'common\components\core\db\dbManager\DbManager'
        ],
        'customerComponent' => [
            'class' => 'common\components\CustomerComponent'
        ],
        /**
         * CACHE CONFIG.
         */
        'fileCache' => [
            'class' => 'yii\caching\FileCache',
            'cachePath' => '/tmp/file-cache',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            'useFileTransport' => true,
        ],
        'cache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' =>  (int)getenv('APP_REDIS_STANDARD_CACHE_DB')
            ])
        ],
        'arrayCache' => [
            'class' => 'yii\caching\ArrayCache',
            'serializer' => false
        ],
        'schemaRedisCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_SCHEMA_CACHE_DB')
            ])
        ],
        'fastPersistentCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_FAST_PERSISTENT_CACHE_DB')
            ]),
        ],
        'mutexCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_MUTEX_CACHE_DB')
            ]),
        ],
        'chainedCache' => [
            'class' => 'common\components\cache\ChainedCache',
            'caches' => [
                ['class' => 'yii\caching\ArrayCache'],
                'cache'
            ],
        ],
        'schemaChainedCache' => [
            'class' => 'common\components\cache\ChainedCache',
            'caches' => [
                ['class' => 'yii\caching\ArrayCache'],
                'schemaRedisCache'
            ],
        ],
        'log' => [
            'traceLevel' => $logTraceLevel,
            'targets' => $logTargets,
        ],
        'messagesSender' => [
            'class' => 'common\components\messenger\MessagesSender',
        ],
        'tokenService' => [
            'class' => 'common\components\tokenService\TokenService',
            'baseApiUrl' => getenv('TOKEN_SERVICE_API_URL'),
            'cacheComponentName' => 'fastPersistentCache'
        ],
        'processManager' => [
            'class' => 'common\components\processManager\ProcessManager',
            'cacheComponentName' => 'fastPersistentCache'
        ],
        'cronComponent' => require(__DIR__ . '/cron.php')
    ],
    'container' => $container,
];

if (getenv('YII_ENV') == 'local') {
    $config['components']['cache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['schemaRedisCache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['fastPersistentCache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['mutex']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
}

return $config;
