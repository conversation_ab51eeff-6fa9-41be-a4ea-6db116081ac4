<?php

declare(strict_types=1);

use SellerLogic\InternalApi\Authorization\tokenStorage\FileTokenStorage;
use SellerLogic\InternalApi\MainApiClient;
use common\components\translation\Translation;
use nuwber\yii2redis\Cache as RedisCache;
use yii\helpers\Inflector;

$enableSchemaCache = true;
$schemaCache = 'schemaRedisCache';
$schemaCacheDuration = 60 * 60 * 24;

$enableQueryCache = true;
$queryCache = 'cache';
$queryCacheDuration = 5 * 60;

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? null;
if ($requestMethod === 'GET') {
    $queryCache = 'chainedCache';
    $schemaCache = 'schemaChainedCache';
}

$container = require(__DIR__ . '/container.php');
$rabbitmq = require(__DIR__ . '/rabbitmq.php');
$container['singletons'] = array_merge($container['singletons'], $rabbitmq['container']);

$applicationName = 'bas_' . YII_ENV . '_';

if (!empty($argv[1])) {
    $applicationName .= 'console_' . \yii\helpers\Inflector::slug(
            str_replace('/', '-', $argv[1])
        );
} else {
    $applicationName .= (getenv('PROCESS_NAME') ?: 'undefined_process');
}

$dbParams = [
    'dbHost' => getenv('APP_DB_HOST'),
    'dbSlaveHost' => getenv('APP_DB_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_PORT'),
    'dbSlavePort' => getenv('APP_DB_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_USERNAME'),
    'dbPassword' => getenv('APP_DB_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];

$db1Params = [
    'dbHost' => getenv('APP_DB_1_HOST'),
    'dbSlaveHost' => getenv('APP_DB_1_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_1_PORT'),
    'dbSlavePort' => getenv('APP_DB_1_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_1_USERNAME'),
    'dbPassword' => getenv('APP_DB_1_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_1_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_1_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];

$repricerDbParams = [
    'host' => getenv('REPRICER_DB_HOST'),
    'host1' => getenv('REPRICER_DB_HOST_1'),
    'user' => getenv('REPRICER_DB_USERNAME'),
    'password' => getenv('REPRICER_DB_PASSWORD'),
    'clientDbNamePrefix' => 'repricer_customer_client_db_',
    'mainDbNamePrefix' => 'repricer_main_db'
];

$repricerEventDbParams = [
    'host' => getenv('REPRICER_EVENT_DB_HOST'),
    'user' => getenv('REPRICER_EVENT_DB_USERNAME'),
    'password' => getenv('REPRICER_EVENT_DB_PASSWORD'),
    'dbName' => getenv('REPRICER_EVENT_DB_NAME')
];

$customerServiceDbParams = [
    'host' => getenv('CUSTOMER_SERVICE_DB_HOST'),
    'port' => getenv('CUSTOMER_SERVICE_DB_PORT'),
    'user' => getenv('CUSTOMER_SERVICE_DB_USERNAME'),
    'password' => getenv('CUSTOMER_SERVICE_DB_PASSWORD'),
    'dbName' => getenv('CUSTOMER_SERVICE_DB_NAME')
];

$redisConfig = [
    'class' => 'common\components\cache\RedisSafeConnection',
    'hostname' => getenv('APP_REDIS_HOST'),
    'port' =>  getenv('APP_REDIS_PORT'),
    'password' => getenv('APP_REDIS_PASSWORD') ?: null,
    'retries' => 10,
    'dataTimeout' => 30,
];

$logTargets = [
    [
        'class' => \baibaratsky\yii\rollbar\log\Target::class,
        'levels' => ['error', 'warning'/*, 'info'*/],
        'exportInterval' => 1,
        'logVars' => [],
        'except' => [
            'file-client:*Failed to login to SFTP server*',
        ],
    ],
];

$logTraceLevel = YII_DEBUG ? 3 : 0;

try {
    // Debug and tmp purpose only
    $customerId = $_GET['customerId'] ?? 0;
    if (in_array($customerId, [1373, 700]) && YII_ENV == 'prod') {
        $logTraceLevel = 3;
        $logTargets[] = [
            'class' => 'yii\log\FileTarget',
            'levels' => ['error', 'warning', 'trace', 'profile'],
            'microtime' => true,
            'logFile' => sprintf(
                "@console/runtime/logs/customer_%d/%s/%s_%s.log",
                $customerId,
                date('Y-m-d'),
                date('H-i-s') . '_' . explode('.', (string)microtime(true))[1],
                substr(Inflector::slug($_SERVER['REQUEST_URI']), 0, 200)
            ),
        ];
    }
} catch (Throwable $throwable) {
}

defined('DEFAULT_RDS_HOST') || define('DEFAULT_RDS_HOST', $dbParams['dbHost']);

$config = [
    'vendorPath' => dirname(__DIR__, 2) . '/vendor',
    'bootstrap' => ['log', 'rollbar'],
    'components' => [
        'translation' => [
            'class' => Translation::class,
        ],
        'featureFlagService' => [
            'class' => 'common\components\featureFlag\FeatureFlagService',
        ],
        'i18n' => require(__DIR__ . '/i18n.php'),
        'clickhouse' => [
            'class' => 'bashkarev\clickhouse\Connection',
            'dsn' => sprintf('host=%s;port=%d;database=%s;connect_timeout_with_failover_ms=10',
                getenv('CLICKHOUSE_HOST'),
                getenv('CLICKHOUSE_PORT'),
                getenv('CLICKHOUSE_DB_NAME'),
            ),
            'masters' => explode(',', getenv('CLICKHOUSE_MASTER_NODES')),
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'username' => getenv('CLICKHOUSE_USERNAME'),
            'password' => getenv('CLICKHOUSE_PASSWORD'),
            'attributes' => ['max_execution_time' => 60  * 20]
        ],
        'prometheus' => [
            'class' => 'common\components\prometheus\PrometheusDummy',
            'namespace' => 'bas_' . YII_ENV,
            'defaultOptions' => [
                'host' => getenv('PROMETHEUS_REDIS_HOST'),
                'port' => getenv('PROMETHEUS_REDIS_PORT'),
                'password' => getenv('PROMETHEUS_REDIS_PASSWORD'),
//                'database' => getenv('APP_REDIS_PROMETHEUS_DB'),
                'timeout' => 1, // in seconds
                'read_timeout' => '10', // in seconds
                'persistent_connections' => false
            ]
        ],
        'rollbar' => [
            'class'       => \common\components\core\rollbar\RollbarLoader::class,
            'accessToken' => getenv('ROLLBAR_ACCESS_TOKEN') ?: '********************************',
            'enabled'     => (bool) getenv('ROLLBAR_ACCESS_TOKEN'),
            'environment' => YII_ENV,
            'ignoreExceptions' => \common\components\core\rollbar\IgnoreExceptionHelper::getIgnoreExceptionList(),
            'batched'     => false,
            'batchSize'   => (int) (getenv('ROLLBAR_BATCH_SIZE') ?: 1),
            'scrubFields' => [
                'passwd',
                'password',
                'password_repeat',
                'secret',
                'confirm_password',
                'password_confirmation',
                'auth_token',
                'csrf_token',
                'fixerIoApiKey',
                'secretKey',
                'stripeSecretKey',
                'username',
                'awsS3AccessKeyId',
                'awsS3AccessKeySecret',
                'access_key',
                'client_secret',
                'secret_key',
                'refresh_token',
                'verify_service_sid',
                'authy_production_api_key',
                'account_sid',
                'apiKey',
                'awsAccessKey',
                'pass',
                'keyringPassword',
                'Authorization',
                'apiLayerFixerIoApiKey',
                'stripePublicKey',
                'cookieValidationKey',
                'trustedHosts',
                'key_id',
            ],
        ],
        'mutex' => [
            'class' => 'yii\redis\Mutex',
            'expire' => 60 * 60,
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_MUTEX_CACHE_DB')
            ]),
        ],
        's3' => [
            'class'=> \common\components\S3Component::class,
            'aKey' => getenv('AWS_ACCESS_KEY'),
            'sKey' => getenv('AWS_SECRET_KEY'),
            'region' => getenv('AWS_REGION'),
            'version' => getenv('AWS_VERSION'),
            'bucket' => getenv('AWS_BUCKET'),
            'publicBucket' => getenv('AWS_PUBLIC_BUCKET'),
            'encryptionType' => 'AES256',
        ],
        'rabbitmq' => empty($rabbitmq['component']) ? null : $rabbitmq['component'],
        /**
         * DB CONFIG.
         */
        'db' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;port=%s;options=--application_name=%s;dbname=%s', $dbParams['dbHost'], $dbParams['dbPort'], $applicationName, $dbParams['profitDashDbName']),
            'emulatePrepare' => true,
            'username' => $dbParams['dbUser'],
            'password' => $dbParams['dbPassword'],
            'prefix' => $dbParams['profitDashDbName'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'attributes' => [],
            'on afterOpen' => function($event): void {
                $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
            },
        ],
        'db1' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;port=%s;options=--application_name=%s;dbname=%s', $db1Params['dbHost'], $db1Params['dbPort'], $applicationName, $db1Params['profitDashDbName']),
            'emulatePrepare' => true,
            'username' => $db1Params['dbUser'],
            'password' => $db1Params['dbPassword'],
            'prefix' => $db1Params['profitDashDbName'] . '1',
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'attributes' => [],
        ],
        'slaveDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;port=%s;options=--application_name=%s;dbname=%s', $dbParams['dbSlaveHost'], $dbParams['dbSlavePort'], $applicationName, $dbParams['profitDashDbName']),
            'emulatePrepare' => true,
            'username' => $dbParams['dbUserSlave'],
            'password' => $dbParams['dbPasswordSlave'],
            'prefix' => $dbParams['profitDashDbName'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
            'on afterOpen' => function($event): void {
                $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
            },
        ],
        'slaveDb01' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;port=%s;options=--application_name=%s;dbname=%s', $db1Params['dbSlaveHost'], $db1Params['dbSlavePort'], $applicationName, $db1Params['profitDashDbName']),
            'emulatePrepare' => true,
            'username' => $db1Params['dbUserSlave'],
            'password' => $db1Params['dbPasswordSlave'],
            'prefix' => $db1Params['profitDashDbName'] . '1',
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => 'schemaRedisCache',
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerCustomerClientDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('mysql:host=%s;dbname=%s', $repricerDbParams['host'], $repricerDbParams['clientDbNamePrefix']),
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['clientDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerCustomerClientDb1' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('mysql:host=%s;dbname=%s', $repricerDbParams['host1'], $repricerDbParams['clientDbNamePrefix']),
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['clientDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerMainDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('mysql:host=%s;dbname=%s', $repricerDbParams['host'], $repricerDbParams['mainDbNamePrefix']),
            'username' => $repricerDbParams['user'],
            'password' => $repricerDbParams['password'],
            'prefix' => $repricerDbParams['mainDbNamePrefix'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'repricerEventDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;options=--application_name=%s;dbname=%s', $repricerEventDbParams['host'], $applicationName, $repricerEventDbParams['dbName']),
            'username' => $repricerEventDbParams['user'],
            'password' => $repricerEventDbParams['password'],
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'customerServiceDb' => [
            'class' => 'common\components\core\db\Connection',
            'dsn' => sprintf('pgsql:host=%s;port=%s;options=--application_name=%s;dbname=%s', $customerServiceDbParams['host'], $customerServiceDbParams['port'], $applicationName, $customerServiceDbParams['dbName']),
            'username' => $customerServiceDbParams['user'],
            'password' => $customerServiceDbParams['password'],
            'emulatePrepare' => true,
            'charset' => 'utf8',
            'enableSchemaCache' => $enableSchemaCache,
            'schemaCacheDuration' => $schemaCacheDuration,
            'schemaCache' => $schemaCache,
            'enableQueryCache' => $enableQueryCache,
            'queryCache' => $queryCache,
            'queryCacheDuration' => $queryCacheDuration,
        ],
        'dbManager' => [
            'class' => 'common\components\core\db\dbManager\DbManager'
        ],
        'customerComponent' => [
            'class' => 'common\components\CustomerComponent'
        ],
        /**
         * CACHE CONFIG.
         */
        'fileCache' => [
            'class' => 'yii\caching\FileCache',
            'cachePath' => '/tmp/file-cache',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            'useFileTransport' => true,
        ],
        'cache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' =>  (int)getenv('APP_REDIS_STANDARD_CACHE_DB')
            ])
        ],
        'arrayCache' => [
            'class' => 'yii\caching\ArrayCache',
            'serializer' => false
        ],
        'schemaRedisCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_SCHEMA_CACHE_DB')
            ])
        ],
        'fastPersistentCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_FAST_PERSISTENT_CACHE_DB')
            ]),
        ],
        'mutexCache' => [
            'class' => 'yii\redis\Cache',
            'redis' => array_merge($redisConfig, [
                'database' => (int)getenv('APP_REDIS_MUTEX_CACHE_DB')
            ]),
        ],
        'chainedCache' => [
            'class' => 'common\components\cache\ChainedCache',
            'caches' => [
                ['class' => 'yii\caching\ArrayCache'],
                'cache'
            ],
        ],
        'schemaChainedCache' => [
            'class' => 'common\components\cache\ChainedCache',
            'caches' => [
                ['class' => 'yii\caching\ArrayCache'],
                'schemaRedisCache'
            ],
        ],
        'log' => [
            'traceLevel' => $logTraceLevel,
            'targets' => $logTargets,
        ],
        'messagesSender' => [
            'class' => 'common\components\messenger\MessagesSender',
        ],
        'tokenService' => [
            'class' => 'common\components\tokenService\TokenService',
            'baseApiUrl' => getenv('TOKEN_SERVICE_API_URL'),
            'cacheComponentName' => 'fastPersistentCache'
        ],
        'processManager' => [
            'class' => 'common\components\processManager\ProcessManager',
            'cacheComponentName' => 'fastPersistentCache'
        ],
        'cronComponent' => require(__DIR__ . '/cron.php'),
        'fileClient' => [
            'class' => 'common\components\FileClientComponent',
            'useCurlForHttp' => (bool)(getenv('FILE_CLIENT_USE_CURL_FOR_HTTP') ?? false),
            'config' => [
                's3' => [
                    'bucket' => getenv('SFTPGO_S3_BUCKET') ?? '',
                    'region' => getenv('SFTPGO_S3_REGION') ?? '',
                    'accessKey' => getenv('SFTPGO_S3_ACCESS_KEY') ?? '',
                    'secretKey' => getenv('SFTPGO_S3_ACCESS_SECRET') ?? '',
                    'endpoint' => getenv('SFTPGO_S3_ENDPOINT') ?? null,
                ],
                's3_sftp_domains' => ['sftp.sellerlogic.com'],
                'http' => [
                    'timeout' => 300,
                    'verify' => false,
                    'headers' => [
                        'User-Agent' => 'SellerLogic Import',
                    ],
                    'max_redirects' => 3,
                ],
                'ftp' => [
                    'timeout' => 300,
                ],
                'sftp' => [
                    'timeout' => 300,
                ],
            ],
        ],
    ],
    'container' => $container,
];

if (getenv('YII_ENV') == 'local') {
    $config['components']['cache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['schemaRedisCache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['fastPersistentCache']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
    $config['components']['mutex']['redis']['password'] = getenv('APP_REDIS_PASSWORD');
}

return $config;
