<?php

return [
    'class' => 'common\components\core\i18n\I18N',
    'translations' => [
        'admin*' => [
            'db' => 'db',
            'class' => 'common\components\core\i18n\DbMessageSource',
            'sourceLanguage' => 'en',
            'messageTable' => '{{%message_translation}}',
            'sourceMessageTable' => '{{%message}}',
            'on missingTranslation' => ['common\components\translation\Translation', 'handleMissingTranslation'],
            'forceTranslation' => true,
        ],
        'import_export*' => [
            'db' => 'db',
            'class' => 'common\components\core\i18n\DbMessageSource',
            'sourceLanguage' => 'en',
            'messageTable' => '{{%message_translation}}',
            'sourceMessageTable' => '{{%message}}',
            'on missingTranslation' => ['common\components\translation\Translation', 'handleMissingTranslation'],
            'forceTranslation' => true,
        ],
    ],
];
