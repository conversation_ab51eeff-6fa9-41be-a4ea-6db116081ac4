<?php

/**
 * @file: cron.php
 * @contains: all general cron parameters
 */
use common\components\cron\GeneralCronComponent;

return [
    'class' => '\common\components\cron\GeneralCronComponent',
    'commands' => [
        'amazon-order/switch-tables' => [
            'command' => 'amazon-order/switch-tables',
            'expression' => '0 5 * * *',
            'customerIdStep' => 2000
        ],
        'amazon-order/transfer1' => [
            'command' => 'amazon-order/transfer 1 1000',
            'expression' => '10,40 * * * *',
        ],
        'amazon-order/transfer1_1' => [
            'command' => 'amazon-order/transfer 1000 2000',
            'expression' => '15,45 * * * *',
        ],
        'amazon-order/transfer2' => [
            'command' => 'amazon-order/transfer 2000 3000',
            'expression' => '20,50 * * * *',
        ],
        'amazon-order/transfer2_1' => [
            'command' => 'amazon-order/transfer 3000 4000',
            'expression' => '25,55 * * * *',
        ],
        'amazon-order/transfer3' => [
            'command' => 'amazon-order/transfer 4000 5000',
            'expression' => '0,30 * * * *',
        ],
        'amazon-order/transfer3_1' => [
            'command' => 'amazon-order/transfer 5000 6000',
            'expression' => '5,35 * * * *',
        ],
        'amazon-order/transfer4' => [
            'command' => 'amazon-order/transfer 6000 7000',
            'expression' => '40,10 * * * *',
        ],
        'amazon-order/transfer4_1' => [
            'command' => 'amazon-order/transfer 7000 8000',
            'expression' => '45,15 * * * *',
        ],
        'amazon-order/transfer5' => [
            'command' => 'amazon-order/transfer 8000 9000',
            'expression' => '50,20 * * * *',
        ],
        'amazon-order/transfer5_1' => [
            'command' => 'amazon-order/transfer 9000',
            'expression' => '55,25 * * * *',
        ],
        'amazon-order/transfer_active-only' => [
            'command' => 'amazon-order/transfer --isActiveOnly=1',
            'expression' => '*/30 * * * *',
            'customerIdStep' => 500
        ],
        'amazon-order/re-init-for-new-customer' => [
            'command' => 'amazon-order/re-init-for-new-customer',
            'expression' => '30 02 * * *',
            'customerIdStep' => 500
        ],
        'order-period/generate-refresh' => [
            'command' => 'order-period/generate-refresh',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'order-period/generate-init' => [
            'command' => 'order-period/generate-init',
            'expression' => '*/5 * * * *',
        ],
        'orders/load-items' => [
            'command' => 'orders/load-items',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'orders/load-refresh' => [
            'command' => 'orders/load-refresh',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'orders/load-init' => [
            'command' => 'orders/load-init',
            'expression' => '*/5 * * * *',
            'customerIdStep' => 2000
        ],
        'order-period/renew-terminated' => [
            'command' => 'order-period/renew-terminated',
            'expression' => '*/10 * * * *',
        ],
        'orders/renew-terminated' => [
            'command' => 'orders/renew-terminated',
            'expression' => '*/10 * * * *',
        ],
        'order-period/is-all-init-periods-loaded' => [
            'command' => 'order-period/is-all-init-periods-loaded',
            'expression' => '10 */3 * * *',
        ],
        'event-period/export-finished-to-clickhouse' => [
            'command' => 'event-period/export-finished-to-clickhouse',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'event-period/is-all-init-periods-loaded' => [
            'command' => 'event-period/is-all-init-periods-loaded',
            'expression' => '10 */3 * * *',
        ],
        'event-period/generate-refresh' => [
            'command' => 'event-period/generate-refresh',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'event-period/generate-init' => [
            'command' => 'event-period/generate-init',
            'expression' => '*/5 * * * *',
            'customerIdStep' => 2000
        ],
        'event-period/merge-parts' => [
            'command' => 'event-period/merge-parts',
            'expression' => '00 * * * *',
            'customerIdStep' => 2000
        ],
        'financial-event/load-refresh' => [
            'command' => 'financial-event/load-refresh',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'financial-event/load-init' => [
            'command' => 'financial-event/load-init',
            'expression' => '* * * * *',
            'customerIdStep' => 2000
        ],
        'finance-event-category/notify-unmapped' => [
            'command' => 'finance-event-category/notify-unmapped',
            'expression' => '*/30 * * * *',
        ],
        'event-period/renew-terminated' => [
            'command' => 'event-period/renew-terminated',
            'expression' => '*/10 * * * *',
            'customerIdStep' => 2000
        ],
        'translation/transfer' => [
            'command' => 'translation/transfer -l=100',
            'expression' => '0 * * * *',
        ],
        'amazon-marketplace/sync' => [
            'command' => 'amazon-marketplace/sync',
            'expression' => '*/30 * * * *',
        ],
        'currency/refresh' => [
            'command' => 'currency/refresh',
            'expression' => '5 4,17 * * *',
        ],
        'product-cost/sync' => [
            'command' => 'product-cost/sync',
            'expression' => '00 20 * * *',
            'customerIdStep' => 3000
        ],
        'product/sync' => [
            'command' => 'product/sync',
            'expression' => '00 20 * * *',
            'customerIdStep' => 3000
        ],
// Temporary disabled due to high load on postgres.
// There is a chance that we do no longer need this function.
//        'product/sync-amounts' => [
//            'command' => 'product/sync-amounts',
//            'expression' => '10 00 * * *',
//            'customerIdStep' => 3000
//        ],
        'seller/create-db' => [
            'command' => 'seller/create-db',
            'expression' => '*/15 * * * *',
        ],
        'product-cost/check-refunds' => [
            'command' => 'product-cost/check-refunds',
            'expression' => '*/10 * * * *',
            'customerIdStep' => 1000
        ],
        'product-cost/recalculate-currency-rate' => [
            'command' => 'product-cost/recalculate-currency-rate',
            'expression' => '00 00 * * *',
            'customerIdStep' => 3000
        ],
        'seller/recheck-tokens' => [
            'command' => 'seller/recheck-tokens',
            'expression' => '10 * * * *',
            'customerIdStep' => 3000
        ],
        'seller/re-sync-sellers' => [
            'command' => 'seller/re-sync-sellers',
            'expression' => '00 */3 * * *'
        ],
        'seller/check-vat-calculation-service-availability' => [
            'command' => 'seller/check-vat-calculation-service-availability',
            'expression' => '00 * * * *',
            'customerIdStep' => 3000
        ],
        'data-import-export/renew-terminated' => [
            'command' => 'data-import-export/renew-terminated',
            'expression' => '*/15 * * * *',
            'customerIdStep' => 1000
        ],
        'data-import-export/recurrent-export-async' => [
            'command' => 'data-import-export/recurrent-export-async',
            'expression' => '* * * * *',
        ],
        'data-import-export/recurrent-import-async' => [
            'command' => 'data-import-export/recurrent-import-async',
            'expression' => '* * * * *',
        ],
        'data-import-export/schedule-import-part-processing' => [
            'command' => 'data-import-export/schedule-import-part-processing',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'clickhouse/re-build-cron-aware-tables' => [
            'command' => 'clickhouse/re-build-cron-aware-tables',
            'expression' => '*/15 * * * *',
            'customerIdStep' => 500
        ],
        'clickhouse/re-build-cron-aware-ppc_costs_last_few_days_transaction' => [
            'command' => 'clickhouse/re-build-cron-aware-tables --tableName=ppc_costs_last_few_days_transaction',
            'expression' => '*/15 * * * *',
            'customerIdStep' => 500
        ],
        'cleaner/clean' => [
            'command' => 'cleaner/clean',
            'expression' => '*/30 * * * *',
            'customerIdStep' => 3000
        ],
        'customer-process/terminate-if-need' => [
            'command' => 'customer-process/terminate-if-need',
            'expression' => '*/5 * * * *',
        ],
        'indirect-cost/apply' => [
            'command' => 'indirect-cost/apply',
            'expression' => '00 * * * *',
            'customerIdStep' => 1000
        ],
        'amazon-report/create-reports-in-amazon' => [
            'command' => 'amazon-report/create-reports-in-amazon',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'amazon-report/send-ready-to-queue' => [
            'command' => 'amazon-report/send-ready-to-queue',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'amazon-report/sync-statuses' => [
            'command' => 'amazon-report/sync-statuses',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'amazon-report/re-init-to-update-changed-data' => [
            'command' => 'amazon-report/re-init-to-update-changed-data',
            'expression' => '*/15 * * * *',
            'customerIdStep' => 1000
        ],
        'amazon-report/generate-ongoing-periods' => [
            'command' => 'amazon-report/generate-ongoing-periods',
            'expression' => '* * * * *',
            'customerIdStep' => 500
        ],
        'amazon-report/renew-terminated' => [
            'command' => 'amazon-report/renew-terminated',
            'expression' => '*/15 * * * *',
            'customerIdStep' => 1000
        ],
        'data-anomaly/check-common' => [
            'command' => 'data-anomaly/check-common --isAutoMode=1',
            'expression' => '00 */3 * * *'
        ],
        'data-anomaly/check-active' => [
            'command' => 'data-anomaly/check --isAutoMode=1 --isOnlyActiveCustomers=1',
            'expression' => '00 01 * * *'
        ],
        'data-anomaly/check-in-active' => [
            'command' => 'data-anomaly/check --isAutoMode=1 --isOnlyActiveCustomers=0',
            'expression' => '00 02 * * *'
        ],
        'amazon-ads/sync-profiles' => [
            'command' => 'amazon-ads/sync-profiles',
            'expression' => '*/30 * * * *',
            'customerIdStep' => 1000
        ],
        'amazon-ads/apply-costs' => [
            'command' => 'amazon-ads/apply-costs',
            'expression' => '*/10 * * * *',
            'customerIdStep' => 1000
        ],
        'repricer-event/sync-buffer' => [
            'command' => 'repricer-event/sync-buffer',
            'expression' => '*/15 * * * *',
        ],
        'repricer-event/generate-demo-data' => [
            'command' => 'repricer-event/generate-demo-data',
            'expression' => '00 * * * *',
        ],
        'data-completeness/check' => [
            'command' => 'data-completeness/check',
            'expression' => '*/30 * * * *',
            'customerIdStep' => 1000
        ],
    ]
];
