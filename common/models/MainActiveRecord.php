<?php

namespace common\models;

use common\components\core\db\dbManager\DbManager;
use common\components\ExtendedQuery;
use yii\console\Application;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\QueryInterface;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;
use yii\db\Expression;

class MainActiveRecord extends ActiveRecord
{
    public static bool $ignoreFilterByCustomer = false;

    public static $dbId = 'db';
    protected const SCENARIO_SEARCH = 'search';
    protected const SCENARIO_CREATE = 'create';
    protected const SCENARIO_UPDATE = 'update';

    /** @var array */
    protected $relatedSortAttributes = [];

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        return $dbManager->getDb(static::$dbId);
    }

    public static function tableName(){
        $schemaName = 'public';
        $tableName = Inflector::camel2id(StringHelper::basename(get_called_class()), '_');
        return $schemaName . '.' . $tableName;
    }

    /**
     * Returns list of all available fields for sorting.
     *
     * <AUTHOR>
     * @return array
     */
    public function getSortAttributes()
    {
        return array_merge($this->getModelSortAttributes(), $this->buildRelatedSortAttributes());
    }

    /**
     * <AUTHOR>
     * @return array
     */
    protected function buildRelatedSortAttributes()
    {
        $result = [];
        foreach ($this->getRelatedSortAttributes() as $key=>$column) {
            $result[$key] = [
                'asc'=> [$column=> SORT_ASC],
                'desc'=>[$column=> SORT_DESC],
            ];
        }

        return $result;
    }

    protected function buildNullableSortAttributes(array $nullableAttributes): array
    {
        $attributes = [];
        foreach ($nullableAttributes as $k => $attribute) {
            if (!is_string($k)) {
                $k = $attribute;
            }

            $attributes[$k] = [
                'asc' => [new Expression("$attribute = '', $attribute ASC")],
                'desc' => [new Expression("$attribute != '', $attribute DESC")],
            ];
        }
        return $attributes;
    }

    /**
     * Returns list of all available fields for sorting in current model.
     *
     * <AUTHOR>
     * @return array
     */
    protected function getModelSortAttributes()
    {
        return array_keys($this->getAttributes());
    }

    /**
     * Returns list of all available fields for sorting in related models.
     *
     * <AUTHOR>
     * @return array
     */
    protected function getRelatedSortAttributes()
    {
        return $this->relatedSortAttributes;
    }

    public function applyBetweenDateFilter(QueryInterface $query, string $field, ?string $value = null) : QueryInterface
    {
        try {
            if ($value && strpos($value, ' - ')) {
                [$createdAtFrom, $createdAtTo] = explode(' - ', $value);

                // Date and time have space separator between them.
                $isTimeToSpecified = count(explode(' ', $createdAtTo)) === 2;
                $createdAtFrom = new \DateTime($createdAtFrom);
                $createdAtTo = (new \DateTime($createdAtTo));

                if ($createdAtFrom->getTimestamp() <= strtotime('1970-01-01 00:00')) {
                    $createdAtFrom = new \DateTime('1970-01-01 00:00');
                }

                if (!$isTimeToSpecified) {
                    // We should take into account whole day because mysql converts 'Y-m-d' to 'Y-m-d 00:00:00'
                    // and some of values could not be filtered (if column type is date time).
                    $createdAtTo->setTime(23, 59, 59);
                }

                $query->andWhere(['>=', $field, $createdAtFrom->format('Y-m-d H:i:s')]);
                $query->andWhere(['<=', $field, $createdAtTo->format('Y-m-d H:i:s')]);
            } else {
                $query->andFilterCompare($field, $value, 'like');
            }
        } catch (\Throwable $e) {
            \Yii::error('Apply filter between date exception: ' . $e->getMessage() . ' ' . json_encode([
                    'field' => $field,
                    'value' => $value,
                ]));
        }

        return $query;
    }

    /**
     * Get default sort parameters.
     * @return array|null
     */
    public function getDefaultSort()
    {
        return null;
    }

    protected static function getActiveQueryInstance(): ActiveQuery
    {
        return new ExtendedQuery(get_called_class());
    }

    public static function find()
    {
        $query = static::getActiveQueryInstance();

        if (\Yii::$app instanceof Application || static::$ignoreFilterByCustomer || (!(new static())->hasAttribute('customer_id'))) {
            return $query;
        }

        $query->onCondition(static::tableName().'.customer_id = :customerId', [':customerId' => \Yii::$app->dbManager->getCustomerId()]);

        return $query;
    }

    public function isValueChanged(string $attributeName): bool
    {
        $oldValue = $this->getOldAttribute($attributeName);
        return $oldValue != $this->$attributeName;
    }
}
