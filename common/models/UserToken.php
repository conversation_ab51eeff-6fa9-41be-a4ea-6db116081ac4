<?php

namespace common\models;

use common\components\core\db\dbManager\DbManager;
use common\components\core\Exception\CustomerDoesNotExistException;
use common\components\CustomerComponent;
use common\components\exception\SellerNotFoundException;
use common\components\userToken\UserTokenManager;
use common\models\traits\SaveOrThrowException;
use yii\db\ActiveRecord;
use yii\web\IdentityInterface;

/**
 * Class UserToken.
 *
 * @OA\Schema(
 * schema="UserToken",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="access_token",
 *      type="string",
 *      description="Access token"
 *   ),
 *   @OA\Property(
 *      property="user_id",
 *      type="integer",
 *      description="User ID"
 *   ),
 *   @OA\Property(
 *      property="customer_id",
 *      type="integer",
 *      description="Customer ID"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created at"
 *   ),
 * )
 *
 * @property int $id
 * @property string $access_token
 * @property string $user_id
 * @property int|null $customer_id
 * @property string $timezone
 * @property string $created_at
 */
class UserToken extends ActiveRecord implements IdentityInterface
{
    use SaveOrThrowException;

    /**
     * For these routes, db existence check will be skipped.
     */
    public const SKIP_DB_CHECK_ROUTES = [
        'v1/customer/sync'
    ];

    const DEFAULT_LANGUAGE = 'en';

    protected bool $internalClient = false;
    protected ?AuthUser $authUser = null;
    protected string $language = self::DEFAULT_LANGUAGE;
    protected string $timezone = CustomerComponent::DEFAULT_TIMEZONE;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['access_token', 'user_id'], 'required'],
            [['access_token'], 'string', 'max' => 1023],
            [['access_token'], 'unique'],
            [['customer_id', 'user_id'], 'integer'],
            [['created_at'], 'datetime', 'format' => 'php:Y-m-d H:i:s'],
        ];
    }

    /**
     * @inheritdoc
     */
    public static function findIdentity($id)
    {
        return UserToken::findOne($id);
    }

    /**
     * @inheritdoc
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        $manager = new UserTokenManager();
        $identity = $manager->getByAccessToken($token);

        if ($identity->isInternalClient() || $identity->isSellerLogicUser()) {
            $customerId = (int)\Yii::$app->request->getQueryParam('customerId');
        } else {
            $customerId = $identity->customer_id;
        }

        $customerId = $customerId === 0 ? null : $customerId;

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        try {
            try {
                $routeId = \Yii::$app->controller->action->getUniqueId();
                $shouldCheckSellerExistence = !in_array($routeId, self::SKIP_DB_CHECK_ROUTES);
            } catch (\Throwable $e) {
                \Yii::error($e);
                $shouldCheckSellerExistence = !$identity->isInternalClient();
            }

            $dbManager->setCustomerId($customerId, true, !$shouldCheckSellerExistence);

            if ($shouldCheckSellerExistence && !$dbManager->isCustomerDbCreated()) {
                throw new SellerNotFoundException();
            }
        } catch (SellerNotFoundException $e) {
            if (!\Yii::$app->request->isGet) {
                throw $e;
            }

            // For GET requests (RO mode) trying to set up default database
            // to prevent errors in case of not found or inactive seller
            Seller::$ignoreFilterByCustomer = true;
            $customerId = Seller::find()->select('customer_id')
                ->where(['id' => Seller::DEFAULT_SELLER_ID])
                ->scalar();
            Seller::$ignoreFilterByCustomer = false;

            if (null === $customerId) {
                throw $e;
            }

            $dbManager->setCustomerId($customerId, true, $identity->isInternalClient());
        }

        return $identity;
    }

    /**
     * @inheritdoc
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @inheritdoc
     */
    public function getAuthKey()
    {
        // TODO: Implement getAuthKey() method.
    }

    /**
     * @inheritdoc
     */
    public function validateAuthKey($authKey)
    {
        // TODO: Implement validateAuthKey() method.
    }

    /**
     * @return bool
     */
    public function isInternalClient(): bool
    {
        return $this->internalClient;
    }

    /**
     * @param bool $internalClient
     */
    public function setIsInternalClient(bool $internalClient): void
    {
        $this->internalClient = $internalClient;
    }

    /**
     * @return AuthUser|null
     */
    public function getAuthUser(): ?AuthUser
    {
        return $this->authUser;
    }

    /**
     * @param AuthUser|null $authUser
     */
    public function setAuthUser(?AuthUser $authUser): void
    {
        $this->authUser = $authUser;
    }

    public function setLanguage(?string $language): void
    {
        $this->language = $language ?: self::DEFAULT_LANGUAGE;
    }

    public function getTimezone(): string
    {
        return $this->timezone ?? CustomerComponent::DEFAULT_TIMEZONE;
    }

    public function setTimezone(?string $timezone): void
    {
        $this->timezone = $timezone ?: CustomerComponent::DEFAULT_TIMEZONE;
    }

    /**
     * @return string
     */
    public function getLanguage(): string
    {
        return $this->language;
    }

    public function isSellerLogicUser(): bool
    {
        if (is_null($this->authUser)) {
            return false;
        }

        return $this->authUser->getSide() == AuthUser::SIDE_SELLERLOGIC;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'public.user_token';
    }
}
