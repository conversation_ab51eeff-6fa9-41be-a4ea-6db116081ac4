<?php

namespace common\models;

use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\SalesCategoryMapper;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;
use yii\db\Expression;

/**
 * This is the model class for table "finance_event_category".
 *
 * @property int $id
 * @property string $path
 * @property string $sales_category_id
 * @property bool $is_unmapped_notification_sent
 * @property string $first_transaction
 * @property string $created_at
 * @property string $updated_at
 */
class FinanceEventCategory extends MainActiveRecord
{
    use LogToConsoleTrait;

    public const PLUS_POSTFIX = '_PLUS';
    public const PLUS_ZERO_POSTFIX = '_ZERO_PLUS';
    public const MINUS_POSTFIX = '_MINUS';
    public const INTERNAL_CATEGORIES_PREFIX = 'INTERNAL';

    public const PPC_SPONSORED_PRODUCTS_PATH = 'AmazonAds.SponsoredProducts.transactionValue_MINUS';
    public const PPC_SPONSORED_DISPLAY_PATH = 'AmazonAds.SponsoredDisplay.transactionValue_MINUS';
    public const PPC_SPONSORED_BRANDS_STORE_SPOTLIGHT_PATH = 'AmazonAds.SponsoredBrands.STORE_SPOTLIGHT.transactionValue_MINUS';
    public const PPC_SPONSORED_BRANDS_PRODUCT_COLLECTION_PATH = 'AmazonAds.SponsoredBrands.PRODUCT_COLLECTION.transactionValue_MINUS';
    public const PPC_SPONSORED_BRANDS_VIDEO_PATH = 'AmazonAds.SponsoredBrands.VIDEO.transactionValue_MINUS';
    public const PPC_SPONSORED_BRANDS_BRAND_VIDEO_PATH = 'AmazonAds.SponsoredBrands.BRAND_VIDEO.transactionValue_MINUS';

    public const PROMOTION_PATHS = [
        'Shipment.ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount_MINUS',
        'ShipmentSettle.ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount_MINUS'
    ];

    public static function isInternalCategory(string $eventCategoryPath): bool
    {
        return false !== strpos($eventCategoryPath, self::INTERNAL_CATEGORIES_PREFIX);
    }

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['path'], 'required'],
            [['path'], 'string', 'max' => 250],
            [['path'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'path' => 'Path',
        ];
    }

    public function beforeSave($insert)
    {
        $salesCategoryMapper = new SalesCategoryMapper();
        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();

        $strategies = $salesCategoryStrategyFactory->getAllStrategies();

        foreach ($strategies as $strategy) {
            $salesCategoryIdColumnName = $strategy->getSalesCategoryIdColumnName();
            $prevSalesCategoryId = $this->{$salesCategoryIdColumnName};
            $this->{$salesCategoryIdColumnName} = $salesCategoryMapper
                ->getSalesCategoryId(
                    $this->path,
                    $strategy
                );

            $this->info(sprintf(
                "Mapped %s => %s (strategy %s)",
                $this->path,
                $prevSalesCategoryId,
                $strategy->getType()
            ));

            if (SalesCategory::isUndefinedCategory($prevSalesCategoryId)
                && $prevSalesCategoryId !== $this->sales_category_id
            ) {
                $this->is_unmapped_notification_sent = false;
            }
        }

        return parent::beforeSave($insert);
    }

    public static function getVatId(): ?int
    {
        static $organicSalesId = null;

        if (null !== $organicSalesId) {
            return $organicSalesId;
        }

        $category = self::find()
            ->select('id')
            ->andFilterWhere(['like', 'path', 'Shipment.ShipmentItem.'])
            ->andFilterWhere(['like', 'path', 'MarketplaceFacilitatorVAT-Principal'])
            ->limit(1)
            ->cache(10)
            ->column();

        return $category[0] ?? null;
    }

    public static function getVatIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'OR',
                ['=', 'path', 'Shipment.ShipmentItem.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorVAT-Principal.ChargeAmount_MINUS'],
                ['=', 'path', 'Shipment.ShipmentItem.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorTax-Principal.ChargeAmount_MINUS'],
                ['=', 'path', 'Refund.ShipmentItemAdjustment.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorVAT-Principal.ChargeAmount_PLUS'],
                ['=', 'path', 'Refund.ShipmentItemAdjustment.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorTax-Principal.ChargeAmount_PLUS'],
                ['=', 'path', 'Refund.ShipmentItemAdjustment.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorVAT-Shipping.ChargeAmount_PLUS'],
                ['=', 'path', 'Refund.ShipmentItemAdjustment.ItemTaxWithheld.MarketplaceFacilitator.TaxesWithheld.MarketplaceFacilitatorTax-Shipping.ChargeAmount_PLUS'],
            ])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    public static function getIgnoredIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'in', 'sales_category_id', SalesCategory::find()
                    ->select('id')
                    ->where(['is_visible' => 'f'])
            ])
            ->column();
    }

    public static function getVCSReverseTransactionsMapping(): array
    {
        $pathsMapping = [
            'Shipment.ShipmentItem.ItemCharge.Tax.ChargeAmount_PLUS' => 'Custom.ProductTaxes.transactionValue_MINUS',
            'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount_MINUS' => 'Custom.ProductTaxes.transactionValue_PLUS',

            'Shipment.ShipmentItem.ItemCharge.ShippingTax.ChargeAmount_PLUS' => 'Custom.ShippingTaxes.transactionValue_MINUS',
            'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount_MINUS' => 'Custom.ShippingTaxes.transactionValue_PLUS',

            'Shipment.ShipmentItem.ItemCharge.GiftWrapTax.ChargeAmount_PLUS' => 'Custom.GiftWrapTaxes.transactionValue_MINUS',
            'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrapTax.ChargeAmount_MINUS' => 'Custom.GiftWrapTaxes.transactionValue_PLUS',
        ];
        $idsMapping = [];

        foreach ($pathsMapping as $path => $reversePath) {
            $categoryId = self::find()
                ->select('id')
                ->where(['like', 'path', $path])
                ->cache(60 * 5)
                ->scalar();
            $reverseCategoryId = self::find()
                ->select('id')
                ->where(['like', 'path', $reversePath])
                ->cache(60 * 5)
                ->scalar();
            $idsMapping[$categoryId] = $reverseCategoryId;
        }

        return $idsMapping;
    }

    public static function getTaxIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'OR',
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.Tax.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount']
            ])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    public static function getAmazonFeesIds(): array
    {
        return self::find()
            ->select('finance_event_category.id')
            ->leftJoin(SalesCategory::tableName() . ' as sc', 'sc.id = sales_category_id')
            ->where([
                'OR',
                ['like', 'sc.path', 'expenses|amazon_fees'],
                ['like', 'sc.path', 'revenue|refunds_and_chargebacks|fba_inbound_fee'],
                ['like', 'sc.path', 'revenue|refunds_and_chargebacks|fba_outbound_fee'],
                ['like', 'sc.path', 'revenue|refunds_and_chargebacks|other_fee'],
                ['like', 'sc.path', 'revenue|refunds_and_chargebacks|fba_service_fee'],
            ])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    public static function getAmazonAdIds(): array
    {
        return self::find()
            ->select('id')
            ->where(['like', 'path', 'AmazonAds.'])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    public static function getCustomFbaStorageFeeId(): ?int
    {
        $category = self::find()
            ->select('id')
            ->where(['like', 'path', 'Custom.FBAStorageFee.transactionValue'])
            ->cache(60 * 5)
            ->column() ?? null;

        $fbaStorageFeeId = $category[0] ?? null;

        return $fbaStorageFeeId;
    }

    public static function getCustomFbaLongTermStorageFeeId(): ?int
    {
        $category = self::find()
            ->select('id')
            ->where(['like', 'path', 'Custom.FBALongTermStorageFee.transactionValue'])
            ->cache(60 * 5)
            ->column() ?? null;

        $fbaLongTermStorageFeeId = $category[0] ?? null;

        return $fbaLongTermStorageFeeId;
    }

    public static function getFbaStorageFeeId(): ?int
    {
        $category = self::find()
            ->select('id')
            ->where(['like', 'path', 'ServiceFee.Fee.FBAStorageFee.FeeAmount_MINUS'])
            ->cache(60 * 5)
            ->column() ?? null;

        $fbaStorageFeeId = $category[0] ?? null;

        return $fbaStorageFeeId;
    }

    public static function getFbaLongTermStorageFeeId(): ?int
    {
        $category = self::find()
            ->select('id')
            ->where(['like', 'path', 'ServiceFee.Fee.FBALongTermStorageFee.FeeAmount_MINUS'])
            ->cache(60 * 5)
            ->column() ?? null;

        $fbaLongTermStorageFeeId = $category[0] ?? null;

        return $fbaLongTermStorageFeeId;
    }

    public static function getOrganicSalesId(): ?int
    {
        static $organicSalesId = null;

        if (null !== $organicSalesId) {
            return $organicSalesId;
        }

        $category = self::find()
            ->select('id')
            ->where(['=', 'path', 'Shipment.ShipmentItem.ItemCharge.Principal.ChargeAmount_PLUS'])
            ->cache(60 * 5)
            ->scalar() ?: null;

        $organicSalesId = $category ?? null;

        return $organicSalesId;
    }

    public static function getPromoCategoryIds(): array
    {
        static $promoCategoryIds = null;

        if (null !== $promoCategoryIds) {
            return $promoCategoryIds;
        }

        $promotionSalesCategoryIds = SalesCategory::find()
            ->select('id')
            ->where(['like', 'path', RevenueExpensesStrategy::CATEGORY_PROMOTION])
            ->cache(60 * 5)
            ->column()
        ;

        if (empty($promotionSalesCategoryIds)) {
            return [];
        }

        $promotionCategoryIds = self::find()
            ->select('id')
            ->where(['in', 'sales_category_id', $promotionSalesCategoryIds])
            ->cache(60 * 5)
            ->column();

        return $promotionCategoryIds ?? [];
    }

    public static function getOrganicRefundId(): ?int
    {
        static $organicRefundId = null;

        if (null !== $organicRefundId) {
            return $organicRefundId;
        }

        $organicRefundId = self::find()
            ->select('id')
            ->where(['=', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount_MINUS'])
            ->cache(60 * 5)
            ->scalar() ?: null;

        return $organicRefundId;
    }

    public static function getShippingChargeId(): ?int
    {
        static $shippingCostId = null;

        if (null !== $shippingCostId) {
            return $shippingCostId;
        }

        $shippingCostId = self::find()
            ->select('id')
            ->where(['=', 'path', 'Shipment.ShipmentItem.ItemCharge.ShippingCharge.ChargeAmount_PLUS'])
            ->cache(60 * 5)
            ->scalar();
        return $shippingCostId;
    }

    public static function getIdsBySalesCategoryId(string $salesCategoryId): array
    {
        return self::find()
            ->select('id')
            ->where(['=', 'sales_category_id', $salesCategoryId])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        SalesCategory::COMMON_CACHE_TAG
                    ]
                ])
            )
            ->column() ?? [];
    }

    public static function getIdsByPaths(array $paths): array
    {
        return self::find()
            ->select('id')
            ->where(['in', 'path', $paths])
            ->cache(
                60 * 5,
                new TagDependency([
                    'tags' => [
                        SalesCategory::COMMON_CACHE_TAG
                    ]
                ])
            )
            ->column();
    }

    public static function getIdsByTag(string $tag)
    {
        $query = FinanceEventCategory::find()
            ->select('fec.id')
            ->from(FinanceEventCategory::tableName() . ' as fec')
            ->leftJoin(SalesCategory::tableName() . ' as sc', 'sc.id = fec.sales_category_id_custom')
            ->where([
                'AND',
                ['=', 'sc.is_visible', 't']
            ])
            ->andWhere(new Expression('array_position(sc.tags, :tag) IS NOT NULL', [':tag' => $tag]));

        return $query
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        SalesCategory::COMMON_CACHE_TAG
                    ]
                ])
            )
            ->column();
    }
    public static function getIdsBySalesCategoryPaths(array $salesCategoryPaths): array
    {
        $orParts = [
            'OR'
        ];
        $params = [];

        foreach ($salesCategoryPaths as $k => $salesCategoryPath) {
            $orParts[] = new Expression("sc.path like CONCAT(:sales_category_path_{$k}, '%')");
            $params[":sales_category_path_{$k}"] = $salesCategoryPath;
        }

        $query = FinanceEventCategory::find()
            ->select('fec.id')
            ->from(FinanceEventCategory::tableName() . ' as fec')
            ->leftJoin(SalesCategory::tableName() . ' as sc', 'sc.id = fec.sales_category_id')
            ->where([
                'AND',
                ['=', 'sc.is_visible', 't'],
                $orParts
            ])
            ->addParams($params)
        ;

        return $query
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => [
                        SalesCategory::COMMON_CACHE_TAG
                    ]
                ])
            )
            ->column();
    }

    public static function getOrderPriceIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'OR',
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.Tax.ChargeAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.GiftWrapTax.ChargeAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.ShippingTax.ChargeAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.Promotion.PromotionMetaDataDefinitionValue.PromotionAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.Principal.ChargeAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.ShippingCharge.ChargeAmount'],
                ['like', 'path', 'Shipment.ShipmentItem.ItemCharge.GiftWrap.ChargeAmount']
            ])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    public static function getRefundOrderPriceIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'OR',
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Tax.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrapTax.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingTax.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.PromotionAdjustment.PromotionMetaDataDefinitionValue.PromotionAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.Principal.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.ShippingCharge.ChargeAmount'],
                ['like', 'path', 'Refund.ShipmentItemAdjustment.ItemChargeAdjustment.GiftWrap.ChargeAmount']
            ])
            ->cache(60 * 5)
            ->column() ?? [];
    }

    /**
     * Returns array of sales categories and related to them ids of event categories.
     *
     * @example
     * [
     *   'Sales Category 1' => [
     *     1 // event category 1,
     *     5 // event category 5,
     *     6 // event category 5,
     *     ...
     *   ],
     *   ...
     * ]
     *
     * @throws \yii\db\Exception
     */
    public static function getSalesCategoriesMap(
        SalesCategoryStrategyInterface $salesCategoryStrategy,
        int $depth = 1
    ): array
    {
        $result = SalesCategory::find()
            ->select([
                'sc.id as sales_category_id',
                'sc.path as path',
                'fec.id as finance_event_category_id'
            ])
            ->from(SalesCategory::tableName() . ' as sc')
            ->innerJoin(
                FinanceEventCategory::tableName() . ' as fec',
                'fec.' . $salesCategoryStrategy->getSalesCategoryIdColumnName() . ' = sc.id'
            )
            ->where([
                'AND',
                ['sc.type' => $salesCategoryStrategy->getType()],
                ['sc.is_visible' => 't']
            ])
            ->asArray()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->all();

        $salesCategoryMap = [];
        $excludeFromCalculationIds = SalesCategory::getExcludedFromCalculationIds();

        foreach ($result as $item) {
            if (empty($item['finance_event_category_id'])) {
                continue;
            }

            $pathComponents = explode('|', $item['path']);
            $categoryId = $item['sales_category_id'];

            if (count($pathComponents) > $depth) {
                $categoryId = $pathComponents[$depth];
            }

            $excludedFromCalculationId = array_values(
                    array_intersect($pathComponents, $excludeFromCalculationIds)
                )[0] ?? null;

            if (null !== $excludedFromCalculationId && $excludedFromCalculationId !== $categoryId) {
                continue;
            }

            $salesCategoryId = $categoryId;
            $financeEventCategoryId = $item['finance_event_category_id'];
            $salesCategoryMap[$salesCategoryId][] = $financeEventCategoryId;
        }

        return $salesCategoryMap;
    }
}
