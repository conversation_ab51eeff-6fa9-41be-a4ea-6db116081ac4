<?php

namespace common\models;

use yii\db\ActiveQuery;

/**
 * @OA\Schema(
 * schema="RefundReason",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="Id"
 *   ),
 *   @OA\Property(
 *      property="name",
 *      type="string",
 *      description="Name"
 *   ),
 *   @OA\Property(
 *      property="description",
 *      type="text",
 *      description="Description"
 *   ),
 * )
 * @property string $id [varchar(255)]
 * @property string $name [varchar(250)]
 * @property string $description
 */
class RefundReason extends MainActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'refund_reason';
    }

    public function search($params): ActiveQuery
    {
        $query = self::find();
        $this->setAttributes($params);

        return $query;
    }

    public function getDefaultSort(): array
    {
        return [
            'name' => SORT_ASC,
        ];
    }

    public function fields(): array
    {
        return [
            'id',
            'name',
            'description',
        ];

        return parent::fields();
    }
}
