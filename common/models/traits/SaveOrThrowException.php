<?php

namespace common\models\traits;

use yii\db\ActiveRecord;
use yii\helpers\Json;

/**
 * Trait SaveOrThrowException
 * @mixin ActiveRecord
 * @package common\models\traits
 */
trait SaveOrThrowException
{
    /**
     * @param bool $runValidation
     * @param null $attributeNames
     * @return bool
     * @throws \Exception
     */
    public function saveOrThrowException($runValidation = false, $attributeNames = null)
    {
        if (!$this->save($runValidation, $attributeNames)) {
            throw new \Exception(\Yii::t('admin', 'Object not saved: {error}', [
                'error' => Json::encode($this->getErrors())
            ]));
        }

        return true;
    }
}
