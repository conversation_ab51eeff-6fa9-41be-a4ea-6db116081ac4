<?php

namespace common\models\traits;

/**
 * Trait LastId
 * Get last allowed id.
 * See 'search' method in ProductImport and ProductExport models.
 * @package common\models\traits
 */
trait LastId
{
    public function getLastId($last)
    {
        $command = (new \yii\db\Query())
            ->select(['id'])
            ->from($this->tableName())
            ->orderBy('id DESC')
            ->limit($last)
            ->createCommand(self::getDb());

        $ids = $command->queryAll();

        return count($ids) > 0 ? $ids[count($ids) - 1]['id'] : null;
    }
}
