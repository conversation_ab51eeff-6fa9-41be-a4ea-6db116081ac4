<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;

/**
 * @OA\Schema(
 *     schema="DataCompletenessFactor",
 *     type="object",
 *     description="Factor details",
 *     @OA\Property(
 *         property="id",
 *         type="string",
 *         description="Factor identifier",
 *         example="no_cost_of_goods"
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the factor",
 *         example="Cost of goods is not imported"
 *     ),
 *     @OA\Property(
 *         property="importance",
 *         type="string",
 *         description="Factor importance (weight based)",
 *         enum={"low", "medium", "high"},
 *         example="low"
 *     ),
 * )
 */

/**
 * @property string $id
 * @property string $title
 * @property int $weight
 * @property int $sort_order
 * @property string $created_at
 * @property string $updated_at
 */
class DataCompletenessFactor extends MainActiveRecord
{
    public const IMPORTANCE_MEDIUM = 'medium';
    public const IMPORTANCE_LOW = 'low';
    public const IMPORTANCE_HIGH = 'high';
    public const IMPORTANCE_INFO = 'info';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields['importance'] = function () {
            // Importance based on weight (1 - 100). We have 3 level of importance: low, medium, high
            if ($this->weight >= 1 && $this->weight <= 33) {
                return self::IMPORTANCE_LOW;
            } elseif ($this->weight >= 34 && $this->weight <= 66) {
                return self::IMPORTANCE_MEDIUM;
            } elseif ($this->weight >= 67 && $this->weight <= 100) {
                return self::IMPORTANCE_HIGH;
            } elseif ($this->weight == 0) {
                return self::IMPORTANCE_INFO;
            }
        };

        unset($fields['weight']);
        unset($fields['sort_order']);
        unset($fields['created_at']);
        unset($fields['updated_at']);

        return $fields;
    }

    public static function primaryKey(): array
    {
        return ['id'];
    }
}
