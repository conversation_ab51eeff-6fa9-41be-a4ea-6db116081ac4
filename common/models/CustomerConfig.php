<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property int $customer_id
 * @property string $seller_id
 * @property string $parameter
 * @property mixed $value
 * @property string $created_at
 * @property string $updated_at
 */
class CustomerConfig extends MainActiveRecord
{

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
