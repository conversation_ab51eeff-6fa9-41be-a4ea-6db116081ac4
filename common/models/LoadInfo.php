<?php

namespace common\models;

/**
 * Class Seller
 *
 * @package common\models
 *
 * @OA\Schema(
 * schema="LoadInfo",
 *   @OA\Property(
 *      property="sellerId",
 *      type="string",
 *      description="Seller ID"
 *   ),
 *   @OA\Property(
 *      property="date",
 *      type="string",
 *      description="Date of synchronization"
 *   ),
 * )
 */
class LoadInfo implements \JsonSerializable
{
    /** @var string */
    private string $sellerId;

    /** @var string|null */
    private ?string $date;

    /**
     * @param string $sellerId
     * @param string|null $date
     */
    public function __construct(string $sellerId, ?string $date)
    {
        $this->sellerId = $sellerId;
        $this->date = $date;
    }

    /**
     * @return array
     */
    public function jsonSerialize()
    {
        return [
            'sellerId' => $this->sellerId,
            'date' => $this->date,
        ];
    }
}
