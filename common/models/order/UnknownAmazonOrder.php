<?php

namespace common\models\order;

use common\models\order\base\AbstractOrderRecord;

/**
 * This is the API model class for table "unknown_amazon_order".
 *
 * @OA\Schema(
 * schema="UnknownAmazonOrder",
 *   @OA\Property(
 *      property="amazon_order_id",
 *      type="string",
 *      description="Amazon Order ID"
 *   ),
 *   @OA\Property(
 *      property="errors_count",
 *      type="integer",
 *      description="Errors count"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created At"
 *   ),
 *   @OA\Property(
 *      property="updated_at",
 *      type="string",
 *      description="Updated At"
 *   ),
 * )

 * @property int $id
 * @property string $amazon_order_id
 * @property int $errors_count
 * @property string $created_at
 * @property string $updated_at
 */
class UnknownAmazonOrder extends AbstractOrderRecord
{

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['amazon_order_id', 'created_at', 'updated_at', 'errors_count'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['amazon_order_id',], 'string', 'max' => 255],
            [['errors_count'], 'integer', 'min' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert): bool
    {
        if ($this->isNewRecord) {
            $this->created_at = date('Y-m-d H:i:s');
        }
        $this->updated_at = date('Y-m-d H:i:s');

        return parent::beforeSave($insert);
    }
}
