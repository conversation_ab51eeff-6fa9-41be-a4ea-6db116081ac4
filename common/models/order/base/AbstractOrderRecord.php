<?php

namespace common\models\order\base;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\models\traits\SaveOrThrowException;
use yii\db\ActiveRecord;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

/**
 * Class AbstractOrderRecord
 * @package common\models\order\base
 *
 */
    abstract class AbstractOrderRecord extends ActiveRecord
{
    use SaveOrThrowException;

    public static function tableName(){
        $dbManager = \Yii::$app->dbManager;

        $schemaName = $dbManager->getSchemaName(DbManager::DB_PREFIX_ORDER);
        $tableName = Inflector::camel2id(StringHelper::basename(get_called_class()), '_');
        return $schemaName . '.' . $tableName;
    }

    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        return $dbManager->getDb('db', HelperFactory::TYPE_POSTGRESS_SHARD);
    }
}
