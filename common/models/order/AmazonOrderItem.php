<?php

namespace common\models\order;

use common\models\order\base\AbstractOrderRecord;

/**
 * @property int $id
 * @property string $order_id
 * @property string|null $asin
 * @property string $sku
 * @property string $order_item_id
 * @property string|null $title
 * @property int $quantity
 * @property int $quantity_shipped
 * @property float $item_price
 * @property float $shipping_price
 * @property float $item_tax
 * @property float $shipping_tax
 * @property float $shipping_discount
 * @property float $promotion_discount
 * @property float $cod_fee
 * @property float $cod_fee_discount
 * @property string|null $promotion_id
 * @property string|null $condition_id
 * @property bool|null $is_gift
 * @property bool|null $is_transparency
 * @property float|null $manual_shipping_cost
 * @property string|null $manual_shipping_cost_currency
 * @property string|null $condition_subtype_id
 * @property string|null $condition_note
 * @property string|null $scheduled_delivery_start_date
 * @property string|null $scheduled_delivery_end_date
 * @property string|null $date
 * @property float|null $profit
 * @property string $order_purchase_date
 * @property string $order_marketplace_id
 */
class AmazonOrderItem extends AbstractOrderRecord
{

    public const SCENARIO_DATA_IMPORT = 'data_import';

    public $order_shipping_costs = null;

    /**
   * {@inheritdoc}
   */
    public function rules()
    {
      return [
        [['order_id', 'sku', 'order_item_id', 'order_purchase_date', 'order_marketplace_id'], 'required', 'except' => self::SCENARIO_DATA_IMPORT],
        [['quantity', 'quantity_shipped'], 'integer'],
        [['item_price', 'shipping_price', 'item_tax', 'shipping_tax', 'shipping_discount', 'promotion_discount', 'cod_fee', 'cod_fee_discount', 'profit', 'manual_shipping_cost'], 'number'],
        [['scheduled_delivery_start_date', 'scheduled_delivery_end_date', 'date', 'order_purchase_date'], 'safe'],
        [['order_id'], 'string', 'max' => 255],
        [['asin', 'condition_id', 'condition_subtype_id', 'order_marketplace_id'], 'string', 'max' => 20],
        [['sku', 'order_item_id'], 'string', 'max' => 50],
        [['manual_shipping_cost_currency'], 'string', 'max' => 3],
        [['title', 'condition_note', 'promotion_id'], 'string', 'max' => 200],
        [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => AmazonOrder::class, 'targetAttribute' => ['order_id' => 'amazon_order_id']],
        [['order_shipping_costs'], 'default', 'value' => null],
        [['order_shipping_costs'], 'number'],
        [[
              'order_id',
              'sku',
              'order_shipping_costs'
          ], 'required', 'on' => self::SCENARIO_DATA_IMPORT],
      ];
    }
}
