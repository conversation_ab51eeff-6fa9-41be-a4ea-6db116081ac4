<?php

namespace common\models\order;

use common\models\order\base\AbstractOrderRecord;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\ActiveQuery;
use yii\db\Query;

/**
 * This is the API model class for table "amazon_order".
 *
 * @OA\Schema(
 * schema="AmazonOrder",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="order_period_id",
 *      type="integer",
 *      description="Order Period ID"
 *   ),
 *   @OA\Property(
 *      property="seller_id",
 *      type="string",
 *      description="Seller ID"
 *   ),
 *   @OA\Property(
 *      property="amazon_order_id",
 *      type="string",
 *      description="Amazon Order ID"
 *   ),
 *   @OA\Property(
 *      property="seller_order_id",
 *      type="string|null",
 *      description="Seller Order ID"
 *   ),
 *   @OA\Property(
 *      property="purchase_date",
 *      type="string",
 *      description="Purchase Date"
 *   ),
 *   @OA\Property(
 *      property="last_update_date",
 *      type="string",
 *      description="Last Update Date"
 *   ),
 *   @OA\Property(
 *      property="order_status",
 *      type="string",
 *      enum={"Shipped", "Canceled", "Pending", "Unshipped", "PartiallyShipped", "Unfulfillable", "InvoiceUnconfirmed", "PendingAvailability"},
 *      description="Order Status",
 *      example="Shipped"
 *   ),
 *   @OA\Property(
 *      property="marketplace_id",
 *      type="string|null",
 *      description="Marketplace ID"
 *   ),
 *   @OA\Property(
 *      property="order_type",
 *      type="string|null",
 *      description="Order Type"
 *   ),
 *   @OA\Property(
 *      property="items_loading_status",
 *      type="string",
 *      description="Items loading status"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created At"
 *   ),
 *   @OA\Property(
 *      property="updated_at",
 *      type="string",
 *      description="Updated At"
 *   ),
 *   @OA\Property(
 *      property="fulfillment_channel",
 *      type="string",
 *      description="Fulfillment channel"
 *   ),
 *   @OA\Property(property="orderPeriod", type="object", ref="#/components/schemas/OrderPeriod"),
 * )

 * @property int $id
 * @property int $order_period_id
 * @property string $seller_id
 * @property string $amazon_order_id
 * @property string|null $seller_order_id
 * @property string $purchase_date
 * @property string $last_update_date
 * @property string $order_status
 * @property string $order_status_from_report
 * @property string|null $marketplace_id
 * @property string|null $order_type
 * @property string $created_at
 * @property string $updated_at
 * @property string $has_transactions
 * @property string $items_loading_status
 * @property string $fulfillment_channel
 *
 * @property string $latest_ship_date
 * @property string $earliest_ship_date
 * @property bool $is_business_order
 * @property bool $is_prime
 * @property bool $is_premium_order
 * @property bool $is_global_express_enabled
 * @property bool $is_replacement_order
 * @property bool $is_sold_by_ab
 * @property bool $is_ispu
 * @property bool $is_access_point_order
 * @property bool $has_manual_shipping_cost
 * @property bool $has_regulated_items
 * @property string $shipment_service_level_category
 * @property string $ship_service_level
 * @property string $payment_method
 *
 * @property OrderPeriod $orderPeriod
 */
class AmazonOrder extends AbstractOrderRecord
{
    public const FULFILMENT_CHANNEL_AFN = 'AFN';
    public const FULFILMENT_CHANNEL_MFN = 'MFN';

    public const OFFER_TYPE_B2B = 'B2B';
    public const OFFER_TYPE_B2C = 'B2C';

    const ITEMS_LOADING_STATUS_NEW = 'NEW';
    const ITEMS_LOADING_STATUS_FINISHED = 'FINISHED';
    const ITEMS_LOADING_STATUS_TERMINATED = 'TERMINATED';
    const ITEMS_LOADING_STATUS_PROCESSING = 'PROCESSING';
    const ITEMS_LOADING_STATUS_QUEUED = 'QUEUED';

    public const ORDER_STATUS_WRONG_CANCELED = 'Cancelled';
    public const ORDER_STATUS_ON_TRIAL = 'On Trial';

    public const ORDER_STATUS_COMPLETE = 'Complete';

    public const ORDER_STATUS_SHIPPING = 'Shipping';

    public const FINAL_STATUSES = [
        Order::ORDER_STATUS_SHIPPED,
        Order::ORDER_STATUS_CANCELED
    ];

    public const SCENARIO_DATA_IMPORT = 'data_import';
    public $order_shipping_costs = null;

    public static function mapOrderStatus(string $orderStatusToMap): string
    {
        if ($orderStatusToMap === self::ORDER_STATUS_COMPLETE) {
            return Order::ORDER_STATUS_SHIPPED;
        }

        if ($orderStatusToMap === self::ORDER_STATUS_SHIPPING) {
            return Order::ORDER_STATUS_UNSHIPPED;
        }

        if ($orderStatusToMap === self::ORDER_STATUS_WRONG_CANCELED) {
            return Order::ORDER_STATUS_CANCELED;
        }

        return $orderStatusToMap;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_period_id', 'seller_id', 'amazon_order_id', 'purchase_date', 'last_update_date', 'order_status', 'created_at', 'updated_at'], 'required', 'except' => self::SCENARIO_DATA_IMPORT],
            [['order_period_id'], 'default', 'value' => null],
            [['order_period_id'], 'integer'],
            [['purchase_date', 'last_update_date', 'created_at', 'updated_at'], 'safe'],
            [['seller_id', 'amazon_order_id', 'seller_order_id', 'order_status', 'marketplace_id', 'order_type'], 'string', 'max' => 255],
            [['items_loading_status'], 'string', 'max' => 20],
            [['fulfillment_channel'], 'string', 'max' => 3],
            [['order_period_id'], 'exist', 'skipOnError' => true, 'targetClass' => OrderPeriod::class, 'targetAttribute' => ['order_period_id' => 'id']],
            [['order_shipping_costs'], 'default', 'value' => null],
            [['order_shipping_costs'], 'number'],
            [[
                'amazon_order_id',
                'order_shipping_costs'
            ], 'required', 'on' => self::SCENARIO_DATA_IMPORT],
        ];
    }

    public function getOrderPeriod(): ActiveQuery
    {
	    return $this->hasOne(OrderPeriod::class, ['id' => 'order_period_id']);
    }

    public function markAsTerminated(): void
    {
        $this->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_TERMINATED;
        $this->saveOrThrowException();
    }

    public function markAsFinished(): void
    {
        $this->items_loading_status = AmazonOrder::ITEMS_LOADING_STATUS_FINISHED;
        $this->saveOrThrowException();
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert): bool
    {
        if ($this->isNewRecord) {
            $this->created_at = date('Y-m-d H:i:s');
            $this->updated_at = date('Y-m-d H:i:s');
        }

        if (!empty($this->order_status)) {
            $this->order_status = self::mapOrderStatus($this->order_status);
        }

        // Do not allow to change final statuses
        $prevStatus = $this->getOldAttribute('order_status');
        if (in_array($prevStatus, self::FINAL_STATUSES)) {
            $this->order_status = $prevStatus;
        }

        $isStatusChanged = $this->isAttributeChanged('order_status');
        if ($isStatusChanged) {
            $this->updated_at = date('Y-m-d H:i:s');
        }

        return parent::beforeSave($insert);
    }

    public function isLoadingTerminated(): bool
    {
        return $this->items_loading_status === self::ITEMS_LOADING_STATUS_TERMINATED;
    }

    public function getAmazonOrderItems(): Query
    {
        return $this->hasMany(AmazonOrderItem::class, ['amazon_order_id' => 'order_id']);
    }
}
