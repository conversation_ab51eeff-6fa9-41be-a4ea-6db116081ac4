<?php

namespace common\models\order;

use common\components\core\db\dbManager\DbManager;
use common\models\finance\base\AbstractFinanceRecord;
use common\models\order\base\AbstractOrderRecord;
use yii\db\ActiveQuery;

/**
* This is the API model class for table "order_period".
*
* @OA\Schema(
* schema="OrderPeriod",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="start_date",
*      type="string",
*      description="Start Date"
*   ),
*   @OA\Property(
*      property="finish_date",
*      type="string",
*      description="Finish Date"
*   ),
*   @OA\Property(
*      property="loading_status",
*      type="string",
*      description="Loading Status"
*   ),
*   @OA\Property(
*      property="type",
*      type="string",
*      description="Type"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string|null",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string|null",
*      description="Updated At"
*   ),
*
*   @OA\Property(property="amazonOrders", type="array", @OA\Items(ref="#/components/schemas/AmazonOrder")),
* )

* @property int $id
* @property string $start_date
* @property string $finish_date
* @property string $loading_status
* @property string $type
* @property string|null $created_at
* @property string|null $updated_at
*
* @property AmazonOrder[] $amazonOrders
*/
class OrderPeriod extends AbstractOrderRecord
{
    const LOADING_STATUS_NEW = 'NEW';
    const LOADING_STATUS_FINISHED = 'FINISHED';
    const LOADING_STATUS_TERMINATED = 'TERMINATED';
    const LOADING_STATUS_PROCESSING = 'PROCESSING';
    const LOADING_STATUS_QUEUED = 'QUEUED';

    const TYPE_INIT = 'INIT';
    const TYPE_REFRESH = 'REFRESH';


    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['start_date', 'finish_date', 'loading_status', 'type'], 'required'],
            [['start_date', 'finish_date', 'created_at', 'updated_at'], 'safe'],
            [['loading_status', 'type'], 'string', 'max' => 32],
        ];
    }

    public function getAmazonOrders(): ActiveQuery
    {
	    return $this->hasMany(AmazonOrder::class, ['order_period_id' => 'id']);
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        $date = date('Y-m-d H:i:s');
        if ($insert) {
            $this->created_at = $date;
        }

        $this->updated_at = $date;

        return parent::beforeSave($insert);
    }

    public function isLoadingTerminated(): bool
    {
        return $this->loading_status === self::LOADING_STATUS_TERMINATED;
    }

    /**
     * @throws \Exception
     */
    public function markAsFinished(): void
    {
        if ($this->loading_status != OrderPeriod::LOADING_STATUS_PROCESSING) {
            return;
        }
        $this->loading_status = OrderPeriod::LOADING_STATUS_FINISHED;
        $this->saveOrThrowException();
    }

    /**
     * @throws \Exception
     */
    public function markAsTerminated(): void
    {
        $this->loading_status = OrderPeriod::LOADING_STATUS_TERMINATED;
        $this->saveOrThrowException();
    }
}
