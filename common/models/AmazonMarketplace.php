<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "amazon_marketplace".
 *
 * @property int $id
 * @property string $amazon_zone_id
 * @property string $amazon_mws_endpoint
 * @property string $title
 * @property string $country_code
 * @property string $currency_code
 * @property bool $is_active
 * @property int $ordering
 * @property string $sales_channel
 * @property string $created_at
 * @property string $updated_at
 */
class AmazonMarketplace extends MainActiveRecord
{
    public const NON_AMAZON = 'Non-Amazon';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['amazon_zone_id', 'amazon_mws_endpoint', 'title', 'country_code', 'currency_code', 'sales_channel'], 'required'],
            [['is_active'], 'boolean'],
            [['ordering'], 'default', 'value' => null],
            [['ordering'], 'integer'],
            [['amazon_zone_id'], 'string', 'max' => 20],
            [['amazon_mws_endpoint'], 'string', 'max' => 50],
            [['title', 'sales_channel'], 'string', 'max' => 100],
            [['country_code'], 'string', 'max' => 2],
            [['currency_code'], 'string', 'max' => 3],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'amazon_zone_id' => 'Amazon Zone ID',
            'amazon_mws_endpoint' => 'Amazon Mws Endpoint',
            'title' => 'Title',
            'country_code' => 'Country Code',
            'currency_code' => 'Currency Code',
            'is_active' => 'Is Active',
            'ordering' => 'Ordering',
            'sales_channel' => 'Sales Channel',
        ];
    }

    public static function getEuCountriesIds(): array
    {
        return self::find()
            ->select('id')
            ->where(['country_code' => [
                'ES', 'GB', 'UK', 'FR', 'BE', 'NL', 'DE', 'IT', 'SE', 'PL'
            ]])
            ->cache(60 * 60)
            ->column() ?? [];
    }

    public static function getById(string $marketplaceId): ?AmazonMarketplace
    {
        static $cache = [];

        if (!empty($cache[$marketplaceId])) {
            return $cache[$marketplaceId];
        }

        /** @var AmazonMarketplace $marketplace */
        $marketplace = \Yii::$app
            ->cache
            ->getOrSet(['marketplace_' . $marketplaceId], function () use ($marketplaceId) {
                $marketplace = AmazonMarketplace::find()->where([
                    'id' => $marketplaceId
                ])->cache(60 * 5)->one();

                return $marketplace ?? false;
            }, 60 * 60 * 24);

        $cache[$marketplaceId] = $marketplace ?? null;
        return $cache[$marketplaceId];
    }

    public static function getBySalesChannel(string $salesChannel): ?AmazonMarketplace
    {
        static $cacheBySalesChannel = [];

        if (!empty($cacheBySalesChannel[$salesChannel])) {
            return $cacheBySalesChannel[$salesChannel];
        }

        /** @var AmazonMarketplace $newMarketplace */
        $newMarketplace = AmazonMarketplace::find()->cache(60 * 60 * 24)->where(['sales_channel' => $salesChannel])->one();

        $cacheBySalesChannel[$salesChannel] = $newMarketplace ?? null;
        return $cacheBySalesChannel[$salesChannel];
    }

    public function beforeSave($insert)
    {
        $this->sales_channel = strtolower(trim($this->sales_channel, '/'));

        return parent::beforeSave($insert);
    }
}
