<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;

/**
 * This is the model class for table "feature_flag_target".
 *
 * @property int $id
 * @property int $feature_id
 * @property int|null $customer_id
 * @property string|null $initial_params
 * @property string|null $initial_status
 * @property string|null $log
 * @property bool $is_enabled
 * @property string $created_at
 * @property string $updated_at
 *
 * @property FeatureFlag $feature
 *
 */
class FeatureFlagTarget extends MainActiveRecord
{
    public static bool $ignoreFilterByCustomer = true;

    public function behaviors(): array
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'feature_flag_target';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['feature_id'], 'required'],
            [['feature_id'], 'integer'],
            [['customer_id', 'initial_params', 'initial_status', 'log', 'created_at', 'updated_at'], 'safe'],
            [['is_enabled'], 'boolean'],
            [
                ['feature_id'],
                'exist',
                'skipOnError' => true,
                'targetClass' => FeatureFlag::class,
                'targetAttribute' => ['feature_id' => 'id']
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => \Yii::t('admin', 'ID'),
            'feature_id' => \Yii::t('admin', 'Feature ID'),
            'customer_id' => \Yii::t('admin', 'Customer ID'),
            'initial_params' => \Yii::t('admin', 'Initial Params'),
            'initial_status' => \Yii::t('admin', 'Initial Status'),
            'log' => \Yii::t('admin', 'Log'),
            'is_enabled' => \Yii::t('admin', 'Is Enabled'),
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        \Yii::$app->featureFlagService->flushCache($this->customer_id, $this->feature->name);
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        \Yii::$app->featureFlagService->flushCache();
        parent::afterDelete();
    }

    public function getFeature()
    {
        return $this->hasOne(FeatureFlag::class, ['id' => 'feature_id']);
    }

}
