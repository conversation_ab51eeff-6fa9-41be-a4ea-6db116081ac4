<?php

namespace common\models;

use Yii;
use common\models\MainActiveRecord;

/**
* This is the API model class for table "db_structure".
*
* @OA\Schema(
* schema="DbStructure",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="version",
*      type="integer",
*      description="Version"
*   ),
*   @OA\Property(
*      property="customer_id",
*      type="integer",
*      description="Customer ID"
*   ),
*   @OA\Property(
*      property="status",
*      type="string",
*      description="Status"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
*
*   @OA\Property(property="dbStructureTables", type="array", @OA\Items(ref="#/components/schemas/DbStructureTable")),
* )

* @property int $id
* @property int $version
* @property int $customer_id
* @property string $type
* @property string $description
* @property string $created_at
* @property string $updated_at
*
* @property DbStructureTable[] $dbStructureTables
*/
class DbStructure extends MainActiveRecord
{
    public const TYPE_GREEN = 'green';
    public const TYPE_BLUE = 'blue';
    /**
     * Used as a temporary state while flipping from blue to green (due to unique key on 'type' column)
     */
    public const TYPE_TMP = 'tmp';

    public const TABLE_TRANSACTION = 'transaction';
    public const TABLE_AMAZON_ORDER = 'amazon_order';
    public const TABLE_AMAZON_ORDER_IN_PROGRESS = 'amazon_order_in_progress';
    public const DATABASE_CLICKHOUSE_CUSTOMER = 'clickhouse_customer';
    public const DATABASE_POSTGRES_CUSTOMER = 'postgres_customer';
    public const REPRICER_EVENT = 'repricer_event';

    public const SUPPORTED_DATABASE_TYPES = [
        self::DATABASE_CLICKHOUSE_CUSTOMER => [
            self::TABLE_TRANSACTION,
            self::TABLE_AMAZON_ORDER,
            self::TABLE_AMAZON_ORDER_IN_PROGRESS,
        ],
        self::DATABASE_POSTGRES_CUSTOMER => []
    ];

    public const SUPPOERTED_TYPES = [
        self::TYPE_BLUE,
        self::TYPE_GREEN
    ];

    public static $dbId = 'db';

    public static function getDatabaseTypeByTableName(string $tableName): string
    {
        foreach (self::SUPPORTED_DATABASE_TYPES as $databaseType => $tableNames) {
            if (in_array($tableName, $tableNames)) {
                return $databaseType;
            }
        }
        throw new \Exception("Unable to determine database type for table {$tableName}");
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['version', 'customer_id'], 'default', 'value' => null],
            [['version', 'customer_id'], 'integer'],
            [['customer_id'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['status'], 'string', 'max' => 255],
            [['id', 'version', 'customer_id', 'status', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getDbStructureTables()
    {
	    return $this->hasMany(DbStructureTable::className(), ['db_structure_id' => 'id']);
    }
}
