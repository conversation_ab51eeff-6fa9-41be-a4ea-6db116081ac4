<?php

namespace common\models;

use Yii;
use common\models\MainActiveRecord;

/**
* This is the API model class for table "db_structure_table".
*
* @OA\Schema(
* schema="DbStructureTable",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="db_structure_id",
*      type="integer",
*      description="Db Structure ID"
*   ),
*   @OA\Property(
*      property="database_type",
*      type="string",
*      description="Database Type"
*   ),
*   @OA\Property(
*      property="table_name",
*      type="string",
*      description="Table Name"
*   ),
*   @OA\Property(
*      property="version",
*      type="integer",
*      description="Version"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
*
*   @OA\Property(property="dbStructure", type="object", ref="#/components/schemas/DbStructure"),
* )

* @property int $id
* @property int $db_structure_id
* @property string $database_type
* @property string $table_name
* @property int $version
* @property string $created_at
* @property string $updated_at
*
* @property DbStructure $dbStructure
*/
class DbStructureTable extends MainActiveRecord
{
    public static $dbId = 'db';

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['db_structure_id', 'database_type', 'table_name'], 'required'],
            [['db_structure_id', 'version'], 'default', 'value' => null],
            [['db_structure_id', 'version'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['database_type', 'table_name'], 'string', 'max' => 255],
            [['db_structure_id'], 'exist', 'skipOnError' => true, 'targetClass' => DbStructure::className(), 'targetAttribute' => ['db_structure_id' => 'id']],
            [['id', 'db_structure_id', 'database_type', 'table_name', 'version', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DbStructureTable::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('db_structure_id', $this->db_structure_id)
            ->andFilterCompare('database_type', $this->database_type, 'like')
            ->andFilterCompare('table_name', $this->table_name, 'like')
            ->andFilterCompare('version', $this->version)
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);

        return $query;
    }



    /**
    * @return \yii\db\ActiveQuery
    */
    public function getDbStructure()
    {
	    return $this->hasOne(DbStructure::className(), ['id' => 'db_structure_id']);
    }
}
