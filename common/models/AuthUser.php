<?php

namespace common\models;

/**
 * Class UserToken.
 *
 * @OA\Schema(
 * schema="AuthUser",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   ),
 * )
 *
 */
class AuthUser implements \JsonSerializable
{
    public const SIDE_SELLERLOGIC = 'SELLERLOGIC';
    public const SIDE_CUSTOMER = 'CUSTOMER';

    private string $userId;

    private string $side;

    private string $type;

    private string $role;

    private array $permissions;

    private string $languageCode;

    /**
     * @param string $userId
     * @param string $side
     * @param string $type
     * @param string $role
     * @param array $permissions
     */
    public function __construct(string $userId, string $side, string $type, string $role, array $permissions, string $languageCode)
    {
        $this->userId = $userId;
        $this->side = $side;
        $this->type = $type;
        $this->role = $role;
        $this->permissions = $permissions;
        $this->languageCode = $languageCode;
    }

    public function jsonSerialize()
    {
        return [
            'userId' => $this->userId,
            'side' => $this->side,
            'type' => $this->type,
            'role' => $this->role,
            'permissions' => $this->permissions,
            'languageCode' => $this->languageCode,
        ];
    }

    /**
     * @return string
     */
    public function getUserId(): string
    {
        return $this->userId;
    }

    /**
     * @return string
     */
    public function getSide(): string
    {
        return $this->side;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @return string
     */
    public function getRole(): string
    {
        return $this->role;
    }

    /**
     * @return array
     */
    public function getPermissions(): array
    {
        return $this->permissions;
    }

    public function getLanguageCode(): string
    {
        return $this->languageCode ?? 'en';
    }
}
