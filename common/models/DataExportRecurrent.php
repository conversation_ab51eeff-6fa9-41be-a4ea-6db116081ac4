<?php

namespace common\models;

use common\components\dataImportExport\SupportedHandlers;
use common\models\customer\DataExportTemplate;
use common\models\validators\CronExpressionValidator;
use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the API model class for table "data_export_recurrent".
 *
 * @OA\Schema(
 * schema="DataExportRecurrent",
 *      @OA\Property(
 *          property="id",
 *          type="integer",
 *          description="The unique identifier of the data export task",
 *          example=90
 *      ),
 *      @OA\Property(
 *          property="customer_id",
 *          type="integer",
 *          description="The ID of the customer",
 *          example=1
 *      ),
 *      @OA\Property(
 *          property="handler_name",
 *          type="string",
 *          description="The name of the handler",
 *          example="product_cost_periods"
 *      ),
 *      @OA\Property(
 *          property="output_file_format",
 *          type="string",
 *          description="The format of the output file",
 *          example="txt"
 *      ),
 *      @OA\Property(
 *          property="is_enabled",
 *          type="boolean",
 *          description="Indicates if the task is enabled",
 *          example=false
 *      ),
 *      @OA\Property(
 *          property="cron_expr",
 *          type="string",
 *          description="The cron expression defining the schedule",
 *          example="3-59/5 * 1,13,15 * ?"
 *      ),
 *      @OA\Property(
 *          property="auto_export_url",
 *          type="string",
 *          description="The URL where the exported file can be accessed",
 *          example="https://sellerlogic-dev-files.s3.eu-west-1.amazonaws.com/customer_00001.data_export/9090/product_costs_export_2023-02-14_13-58-06.txt"
 *      ),
 *      @OA\Property(
 *          property="invoked_at",
 *          type="string",
 *          format="date-time",
 *          description="The timestamp when the task was invoked",
 *          example="2023-02-14 13:58:05"
 *      ),
 *      @OA\Property(
 *          property="executed_at",
 *          type="string",
 *          format="date-time",
 *          description="The timestamp when the task was executed",
 *          example="2023-02-14 13:58:05"
 *      ),
 *      @OA\Property(
 *          property="created_at",
 *          type="string",
 *          format="date-time",
 *          description="The timestamp when the task was created",
 *          example="2023-02-14 08:38:03"
 *      ),
 *      @OA\Property(
 *          property="updated_at",
 *          type="string",
 *          format="date-time",
 *          description="The timestamp when the task was last updated",
 *          example="2023-08-20 09:10:53"
 *      ),
 *      @OA\Property(
 *          property="template_id",
 *          type="integer",
 *          description="The ID of the template used for the export task",
 *          example=112
 *      )
 * )
 * @property int      $id
 * @property int      $customer_id
 * @property string   $handler_name
 * @property string   $language_code
 * @property int      $template_id
 * @property int      $is_enabled
 * @property string   $output_file_format
 * @property string   $cron_expr
 * @property string   $auto_export_url
 * @property string   $created_at
 * @property string   $updated_at
 * @property string   $invoked_at
 * @property string   $executed_at
 */
class DataExportRecurrent extends MainActiveRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['handler_name', 'cron_expr'], 'required'],
            [['template_id'], 'filter', 'filter' => function($value) {
                if (!empty($value)) {
                    return $value;
                }

                return DataExportTemplate::find()->select('id')->where([
                    'is_default' => 't'
                ])->scalar() ?? null;
            }],
            [['output_file_format'], 'filter', 'filter' => function($value) {
                /** @var DataExportTemplate $template */
                $template = DataExportTemplate::find()->where([
                    'id' => $this->template_id
                ])->one() ?? null;

                if (empty($template)) {
                    return $value;
                }
                return $template->format;
            }],
            [['template_id'], 'required'],
            [['customer_id', 'is_enabled', 'template_id'], 'integer'],
            [['is_enabled'], 'default', 'value' => 1],
            [['customer_id', 'handler_name', 'cron_expr', 'is_enabled'], 'safe'],
            [['handler_name'], 'in', 'range' => SupportedHandlers::EXPORT_SUPPORTED_HANDLERS],
            [['cron_expr'], 'string', 'max' => 200],
            [['cron_expr'], CronExpressionValidator::class],
            [['cron_expr'], 'unique', 'targetAttribute' => ['customer_id', 'handler_name', 'cron_expr'],
                'message' => Yii::t('admin', 'Such time has been already taken, please choose another time'),
            ],
            [['id', 'customer_id', 'handler_name', 'is_enabled', 'output_file_format', 'cron_expr', 'created_at', 'executed_at', 'invoked_at'], 'safe', 'on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DataExportRecurrent::find();

        $this->setScenario('search');
        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('handler_name', $this->handler_name, 'like')
            ->andFilterCompare('is_enabled', $this->is_enabled)
            ->andFilterCompare('output_file_format', $this->output_file_format, 'like');

        $query = $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $query = $this->applyBetweenDateFilter($query, 'updated_at', $this->updated_at);
        $query = $this->applyBetweenDateFilter($query, 'invoked_at', $this->invoked_at);
        $query = $this->applyBetweenDateFilter($query, 'executed_at', $this->executed_at);

        return $query;
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'handler_name',
            'template_id',
            'cron_expr',
            'is_enabled',
        ];
        $scenarios['update'] = [
            'cron_expr',
            'template_id',
            'is_enabled',
        ];
        return $scenarios;
    }

    public function beforeValidate(): bool
    {
        $this->customer_id = Yii::$app->dbManager->getCustomerId();
        return parent::beforeValidate();
    }
}
