<?php

namespace common\models;

/**
 * @OA\Response(
 *      response="CommonErrorResponse",
 *      description="Very common error, appears for example when core required fields have not been specified (by FE application, not by customer). internal_code can have next values:
 * **NO_VALID_PLAN** appears when we could not have time to sync data with common project and need one more time, not an issue at all - expected situation
 * **ACTION_RESTRICTED_FOR_NON_ACTIVE_CUSTOMER** appears when customer is not active and tries to perform some edit/create action. We allow only view in that case.:
 * **UNDEFINED** any other errors without specific code.",
 *      @OA\JsonContent(
 *          type="object",
 *          @OA\Property(
 *              property="name",
 *              type="string",
 *              example="Internal error name which should not be shown to customer"
 *          ),
 *          @OA\Property(
 *              property="message",
 *              type="string",
 *              example="Internal error message which should not be shouwn to customer"
 *          ),
 *          @OA\Property(
 *              property="code",
 *              type="integer",
 *              example=0
 *          ),
 *          @OA\Property(
 *              property="internal_code",
 *              type="string",
 *              enum={"UNDEFINED", "NO_VALID_PLAN", "MODULE_NOT_INITIALIZED_YET"},
 *              example="UNDEFINED",
 *          ),
 *          @OA\Property(
 *              property="status",
 *              type="integer",
 *              example=400
 *          )
 *      )
 *  )
 */

/**
 * @OA\Response(
 *      response="UnauthorizedErrorResponse",
 *      description="Appears when user is not authorized or something went wrong with authorization (expired for example).",
 *      @OA\JsonContent(
 *          type="object",
 *          @OA\Property(
 *              property="name",
 *              type="string",
 *              example="Unauthorized"
 *          ),
 *          @OA\Property(
 *              property="message",
 *              type="string",
 *              example="Unauthorized"
 *          ),
 *          @OA\Property(
 *              property="code",
 *              type="integer",
 *              example=0
 *          ),
 *          @OA\Property(
 *              property="internal_code",
 *              type="string",
 *              enum={"UNDEFINED", "NO_VALID_PLAN", "MODULE_NOT_INITIALIZED_YET"}
 *          ),
 *          @OA\Property(
 *              property="status",
 *              type="integer",
 *              example=401
 *          )
 *      )
 *  )
 */

/**
 * @OA\Response(
 *      response="ForbiddenErrorResponse",
 *      description="Apppears when customer does not have permission to perform action.",
 *      @OA\JsonContent(
 *          type="object",
 *          @OA\Property(
 *              property="name",
 *              type="string",
 *              example="Forbidden"
 *          ),
 *          @OA\Property(
 *              property="message",
 *              type="string",
 *              example="You are not allowed to perform this action."
 *          ),
 *          @OA\Property(
 *              property="code",
 *              type="integer",
 *              example=0
 *          ),
 *          @OA\Property(
 *              property="internal_code",
 *              type="string",
 *              enum={"UNDEFINED", "NO_VALID_PLAN", "MODULE_NOT_INITIALIZED_YET"}
 *          ),
 *          @OA\Property(
 *              property="status",
 *              type="integer",
 *              example=403
 *          )
 *      )
 *  )
 */

/**
 * @OA\Response(
 *      response="NotFoundErrorResponse",
 *      description="Appears when requested resource was not found.",
 *      @OA\JsonContent(
 *          type="object",
 *          @OA\Property(
 *              property="name",
 *              type="string",
 *              example="Not Found"
 *          ),
 *          @OA\Property(
 *              property="message",
 *              type="string",
 *              example="Object not found: 553234"
 *          ),
 *          @OA\Property(
 *              property="code",
 *              type="integer",
 *              example=0
 *          ),
 *          @OA\Property(
 *              property="internal_code",
 *              type="string",
 *              enum={"UNDEFINED", "NO_VALID_PLAN", "MODULE_NOT_INITIALIZED_YET"}
 *          ),
 *          @OA\Property(
 *              property="status",
 *              type="integer",
 *              example=404
 *          )
 *      )
 *  )
 */

/**
 * @OA\Response(
 *      response="FormValidationErrorResponse",
 *      description="Appears when form validation fails (customer input related error).",
 *      @OA\JsonContent(
 *          type="array",
 *          @OA\Items(
 *              type="object",
 *              @OA\Property(
 *                  property="field",
 *                  type="string",
 *                  example="marketplace_id"
 *              ),
 *              @OA\Property(
 *                  property="message",
 *                  type="string",
 *                  example="Marketplace Id cannot be blank."
 *              )
 *          )
 *      )
 *  )
 */

/**
 * @OA\Response(
 *      response="ResourceDeletedResponse",
 *      description="Resource deleted sucessfully."
 *  )
 */

/**
 * Profit breakdown
 *
 * @OA\Schema(
 *     schema="ProfitBreakdownChild",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="string",
 *         example="fba_fulfillment_fee"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         example="FBA fulfillment fee"
 *     ),
 *     @OA\Property(
 *         property="is_default",
 *         type="integer",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="depth",
 *         type="integer",
 *         example=4
 *     ),
 *     @OA\Property(
 *         property="color_hex",
 *         type="string",
 *         example="#f6eeb6"
 *     ),
 *     @OA\Property(
 *         property="count_transactions",
 *         type="integer",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="amount",
 *         type="number",
 *         format="float",
 *         example=-1.64
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         example="money"
 *     ),
 *     @OA\Property(
 *         property="children",
 *         type="array",
 *         @OA\Items(type="object")
 *     ),
 *     @OA\Property(
 *         property="hasChildren",
 *         type="boolean",
 *         example=false
 *     )
 * )
 * @OA\Schema(
 *     schema="ProfitBreakdownItem",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="string",
 *         example="fba_outbound_fee_2"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         example="FBA outbound fee"
 *     ),
 *     @OA\Property(
 *         property="depth",
 *         type="integer",
 *         example=3
 *     ),
 *     @OA\Property(
 *         property="color_hex",
 *         type="string",
 *         example="#8d0a81"
 *     ),
 *     @OA\Property(
 *         property="count_transactions",
 *         type="integer",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="amount",
 *         type="number",
 *         format="float",
 *         example=-1.64
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         example="money"
 *     ),
 *     @OA\Property(
 *         property="children",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ProfitBreakdownChild")
 *     ),
 *     @OA\Property(
 *         property="hasChildren",
 *         type="boolean",
 *         example=true
 *     )
 * )
 * @OA\Schema(
 *     schema="ProfitBreakdownResponse",
 *     type="object",
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="is_approximate_amounts_calculation",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="order_id",
 *         type="string",
 *         nullable="true",
 *         example="028-6180335-5909157"
 *     ),
 *     @OA\Property(
 *         property="breakdown",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ProfitBreakdownItem")
 *     )
 * )
 */

/**
 * @OA\Parameter(
 *     parameter="requestCustomerId",
 *     name="customerId",
 *     in="query",
 *     description="Required for admin user (for regular user will be filled automatically)",
 *     required=false,
 *     @OA\Schema(type="integer")
 * ),
 * @OA\Parameter(
 *     parameter="requestResourceId",
 *     name="id",
 *     in="query",
 *     description="Resource Id",
 *     required=true,
 *     @OA\Schema(type="integer", default="1")
 * )
 */

/**
 * @OA\Parameter(
 *     parameter="listViewPage",
 *     name="page",
 *     in="query",
 *     description="Page number",
 *     required=false,
 *     @OA\Schema(type="integer")
 * ),
 * @OA\Parameter(
 *     parameter="listViewSort",
 *     name="sort",
 *     in="query",
 *     description="Sort by column [{column}, -{column}]",
 *     example="-id",
 *     required=false,
 *     @OA\Schema(type="string")
 *  ),
 * @OA\Parameter(
 *     parameter="listViewResourceId",
 *     name="id",
 *     in="query",
 *     description="Resource id",
 *     required=false,
 *     @OA\Schema(type="integer")
 * ),
 * @OA\Parameter(
 *     parameter="listViewPageSize",
 *     name="pageSize",
 *     in="query",
 *     description="Page size [1,100]",
 *     required=false,
 *     @OA\Schema(type="integer")
 *  ),
 * @OA\Parameter(
 *     parameter="listViewAllRecords",
 *     name="all",
 *     in="query",
 *     description="Show all records with pager",
 *     required=false,
 *     @OA\Schema(type="integer", enum={1,0}, default=0)
 * ),
 *
 */


/**
 * @OA\Schema(
 *     schema="AbstractPaginatedResponse",
 *     type="object",
 *     @OA\Property(
 *         property="totalCount",
 *         type="integer",
 *         description="Total number of items",
 *         example=53754
 *     ),
 *     @OA\Property(
 *         property="pageCount",
 *         type="integer",
 *         description="Total number of pages",
 *         example=538
 *     ),
 *     @OA\Property(
 *         property="currentPage",
 *         type="integer",
 *         description="Current page number",
 *         example=152
 *     ),
 *     @OA\Property(
 *         property="pageSize",
 *         type="integer",
 *         description="Number of items per page",
 *         example=100
 *     )
 * )
 */

/**
 * @OA\Response(
 *        response="ListViewResponse",
 *        description="List of resource items with pagination",
 *        @OA\JsonContent(
 *           type="object",
 *           @OA\Property(
 *           property="totalCount",
 *           type="integer",
 *           description="Total number of items"
 *       ),
 *       @OA\Property(
 *           property="pageCount",
 *           type="integer",
 *           description="Total number of pages"
 *       ),
 *       @OA\Property(
 *           property="currentPage",
 *           type="integer",
 *           description="Current page number"
 *       ),
 *       @OA\Property(
 *           property="pageSize",
 *           type="integer",
 *           description="Number of items per page"
 *       ),
 *        )
 *    ),
 */
class SwaggerCommonAnnotations
{
}
