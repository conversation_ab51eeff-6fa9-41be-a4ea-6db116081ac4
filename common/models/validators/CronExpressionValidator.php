<?php

namespace common\models\validators;

use Cron\CronExpression;
use Cron\FieldFactory;
use yii\i18n\Formatter;
use yii\validators\Validator;

class CronExpressionValidator extends Validator
{
    public int $minIntervalM = 5;

    public function validateAttribute($model, $attribute)
    {
        try {
            $expression = str_replace('?', '*', $model->$attribute);
            $cronExpression = new CronExpression($expression, new FieldFactory());
        } catch (\Throwable $e) {
            $model->addError($attribute, \Yii::t('admin', 'Sorry, something went wrong while recognizing schedule time'));

            \Yii::error("Tried to save wrong cron expression " . json_encode([
                'model' => get_class($model),
                'attribute' => $attribute,
                'value' => $model->$attribute,
            ], JSON_THROW_ON_ERROR));
            return;
        }

        $nextRunDate = $cronExpression->getNextRunDate();
        $prevRunDate = $cronExpression->getPreviousRunDate();
        $interval = $prevRunDate->diff($nextRunDate);
        $minInterval = \DateInterval::createFromDateString($this->minIntervalM .' minutes');
        $intervalS = $nextRunDate->getTimestamp() - $prevRunDate->getTimestamp();

        /** @var Formatter $formatter */
        $formatter = \Yii::$app->formatter;

        if ($intervalS < $this->minIntervalM * 60) {
            $model->addError($attribute, \Yii::t(
                'admin',
                'Minimum interval is {minimum_interval}',
                [
                    'minimum_interval' => $formatter->asDuration($minInterval),
                    'specified_interval' => $formatter->asDuration($interval)
                ]
            ));
        }
    }
}
