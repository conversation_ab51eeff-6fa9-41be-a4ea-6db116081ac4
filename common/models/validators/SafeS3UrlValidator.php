<?php

namespace common\models\validators;

use common\components\S3Component;
use Yii;
use yii\validators\Validator;

/**
 * Checks whether attribute contains url that relates to our bucket and safe for us.
 *
 * @package common\models\validators
 */
class SafeS3UrlValidator extends Validator
{
    public function validateAttribute($model, $attribute)
    {
        /** @var S3Component $s3Component */
        $s3Component = Yii::$app->s3;
        $value = $model->$attribute;

        if (!is_string($value)) {
            return;
        }

        if ($s3Component->isUrlSafe($value)) {
            return;
        }

        $model->addError($attribute, \Yii::t('admin', 'Something is wrong with uploaded image. Please, try again later.', [
            'attribute' => $attribute,
        ]));
        \Yii::error("Tried to save wrong s3 image url " . json_encode([
            'model' => get_class($model),
            'attribute' => $attribute,
        ]));
    }
}
