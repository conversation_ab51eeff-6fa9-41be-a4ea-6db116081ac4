<?php

namespace common\models;

use yii\db\ActiveQuery;

/**
 *
 * @OA\Schema(
 *     schema="MessageTranslation",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Property(
 *         property="language",
 *         type="string",
 *         description="Language code",
 *     ),
 *     @OA\Property(
 *         property="translation",
 *         type="string",
 *         description="Translated message",
 *     ),
 *     @OA\Property(
 *         property="translated",
 *         type="integer",
 *         description="Is message translated",
 *     ),
 *     @OA\Property(
 *         property="is_auto_translated",
 *         type="integer",
 *         description="Is message auto translated",
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         description="Possible values 'APPROVED','PENDING','TRANSLATED'",
 *     ),
 *     @OA\Property(
 *         property="comments",
 *         type="string",
 *         description="Comments"
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="object",
 *         ref="#/components/schemas/Message",
 *     ),
 * )
 *
 * @property int                         $id
 * @property string                      $language
 * @property string                      $translation
 * @property int                         $translated
 * @property int                         $is_auto_translated
 * @property string                      $comments
 * @property string                      $status
 * @property string                      $admin_bar_ignore_till
 * @property Message                     $message
 * @property int                         $translated_by_user_id
 * @property int                         $approved_by_user_id
 */
class MessageTranslation extends MainActiveRecord
{
    public const STATUS_APPROVED = 'APPROVED';

    public const STATUS_PENDING = 'PENDING';

    public const STATUS_TRANSLATED = 'TRANSLATED';

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'language'], 'required'],
            [['id', 'translated'], 'integer'],
            [['id', 'is_auto_translated'], 'integer'],
            [['translation', 'comments'], 'string'],
            [['language'], 'string', 'max' => 2],
            [['language'], 'filter', 'filter' => 'trim'],
            [['status'], 'in', 'range' => self::getStatuses()],
            [['id', 'language'], 'unique', 'targetAttribute' => ['id', 'language']],
            [
                ['id'],
                'exist',
                'skipOnError' => true,
                'targetClass' => Message::class,
                'targetAttribute' => ['id' => 'id'],
            ],
            [
                [
                    'id',
                    'status',
                    'language',
                    'translation',
                    'translated',
                    'is_auto_translated',
                ],
                'safe',
                'on' => 'search',
            ],
        ];
    }

    public function search($params): ActiveQuery
    {
        $query = MessageTranslation::find();

        $query->joinWith('message');

        $this->setScenario(self::SCENARIO_SEARCH);
        $this->setAttributes($params);

        $query
            ->andFilterCompare('message_translation.id', $this->id)
            ->andFilterCompare('message_translation.is_auto_translated', $this->is_auto_translated)
            ->andFilterCompare('message_translation.language', $this->language, 'like')
            ->andFilterCompare('LOWER(message_translation.translation)', mb_strtolower($this->translation), 'like')
            ->andFilterCompare('message_translation.translated', $this->translated)
            ->andFilterCompare('message_translation.status', $this->status, 'like');

        return $query;
    }

    /**
     * @return array
     */
    public function fields(): array
    {
        $fields = parent::fields();

        $fields[] = 'message';

        return $fields;
    }

    /**
     * @return string[]
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_TRANSLATED,
            self::STATUS_APPROVED,
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getMessage(): ActiveQuery
    {
        return $this->hasOne(Message::class, ['id' => 'id']);
    }
}
