<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;

/**
 * This is the model class for table "tag".
 * @OA\Schema(
 *     schema="Tag",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Tag title",
 *     ),
 *     @OA\Property(
 *         property="color",
 *         type="string",
 *         description="Tag Color (Example #999999)",
 *     )
 * )
 * @property int $id
 * @property string $title
 * @property string $color
 */
class Tag extends AbstractCustomerRecord
{
    public function rules()
    {
        return [
            [['title', 'color'], 'required'],
            [['color'], 'string', 'max' => 10],
            [['title'], 'string', 'max' => 255],
            [['title'], 'string', 'min' => 3, 'tooShort' => \Yii::t('admin', "Tag name should contain at least 3 characters.")],
            [['title'], 'validateUniqueTitle'],
        ];
    }

    public function validateUniqueTitle($attribute)
    {
        $existingTag = self::find()
            ->where(['ilike', 'title', $this->title])
            ->andFilterWhere(['<>', 'id', $this->id])
            ->one();

        if ($existingTag) {
            $this->addError($attribute, \Yii::t('admin', "This tag title already exists (case insensitive)"));
        }
    }
}
