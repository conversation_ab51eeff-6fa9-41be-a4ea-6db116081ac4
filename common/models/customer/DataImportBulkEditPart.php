<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\base\AbstractCustomerRecord;

/**
 * This is the API model class for table "data_import_bulk_part".
 *
 * @property int    $id
 * @property int    $data_import_id
 * @property string $status
 * @property int    $start_id
 * @property int    $finish_id
 * @property int    $part_no
 * @property int    $last_processed_id
 * @property int    $count_all_items
 * @property int    $count_imported_items
 * @property int    $count_errors
 * @property string $exception
 * @property string $log
 * @property string $errors
 * @property string $finished_at
 * @property string $started_at
 * @property string $created_at
 * @property string $updated_at
 *
 * @property DataImport $dataImport
 */
class DataImportBulkEditPart extends AbstractCustomerRecord
{
    public const STATUS_TERMINATED = 'terminated';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER) . '.data_import_bulk_part';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['data_import_id'], 'required'],
            [['data_import_id', 'count_all_items', 'count_imported_items', 'count_errors', 'start_id', 'finish_id', 'part_no', 'last_processed_id'], 'integer'],
            [['status', 'errors'], 'string'],
            [['data_import_id'], 'exist', 'skipOnError' => true, 'targetClass' => DataImport::class, 'targetAttribute' => ['data_import_id' => 'id']],
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDataImport()
    {
        return $this->hasOne(DataImport::class, ['id' => 'data_import_id']);
    }

    public function setQueued(): void
    {
        if ($this->status === DataImport::STATUS_QUEUED) {
            return;
        }

        $this->status = DataImport::STATUS_QUEUED;
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function setFinished(): void
    {
        if (in_array($this->status, [DataImport::STATUS_FINISHED, DataImport::STATUS_NO_ITEMS], true)) {
            return;
        }

        if ($this->count_all_items === 0) {
            $this->status = DataImport::STATUS_NO_ITEMS;
        } else {
            $this->status = DataImport::STATUS_FINISHED;
        }

        $this->finished_at = date('Y-m-d H:i:s');
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function setTerminated(): void
    {
        if (!in_array($this->status, [DataImport::STATUS_QUEUED, DataImport::STATUS_NEW, DataImport::STATUS_IN_PROGRESS], true)) {
            return;
        }

        $this->status = DataImport::STATUS_TERMINATED;
        $this->finished_at = date('Y-m-d H:i:s');
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function setInProgress(): void
    {
        if (!in_array($this->status, [
            DataImport::STATUS_NEW,
            DataImport::STATUS_QUEUED
        ])) {
            return;
        }

        $this->status = DataImport::STATUS_IN_PROGRESS;
        $this->started_at = date('Y-m-d H:i:s');
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function log(string $message): void
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }
}
