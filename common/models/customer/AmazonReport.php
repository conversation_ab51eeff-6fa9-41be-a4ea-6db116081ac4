<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use Yii;
use common\models\MainActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
* This is the API model class for table "amazon_report".
*
* @OA\Schema(
* schema="AmazonReport",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="amazon_id",
*      type="integer",
*      description="Amazon ID"
*   ),
*   @OA\Property(
*      property="amazon_type",
*      type="string",
*      description="Amazon Type"
*   ),
*   @OA\Property(
*      property="amazon_document_id",
*      type="string|null",
*      description="Amazon Document ID"
*   ),
*   @OA\Property(
*      property="amazon_processing_status",
*      type="string",
*      description="Amazon Processing Status"
*   ),
*   @OA\Property(
*      property="customer_id",
*      type="integer",
*      description="Customer ID"
*   ),
*   @OA\Property(
*      property="seller_id",
*      type="string",
*      description="Seller ID"
*   ),
*   @OA\Property(
*      property="start_date",
*      type="string",
*      description="Start Date"
*   ),
*   @OA\Property(
*      property="end_date",
*      type="string",
*      description="End Date"
*   ),
*   @OA\Property(
*      property="status",
*      type="string",
*      description="Status"
*   ),
*   @OA\Property(
*      property="type",
*      type="string",
*      description="Type"
*   ),
*   @OA\Property(
*      property="log",
*      type="string|null",
*      description="Log"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )
 * @property int $id
* @property int $amazon_id
* @property string $amazon_type
* @property string|null $amazon_document_id
* @property string $amazon_processing_status
* @property int $attempts_made
* @property int $attempts_need
* @property string $time_between_attempts_m
* @property string $stop_repeating_at
* @property string $finished_at
* @property int $customer_id
* @property string $seller_id
* @property string $start_date
* @property string $end_date
* @property string $status
* @property string $type
* @property string $data_provider
* @property array $extra_info
* @property string|null $log
* @property string $created_at
* @property string $updated_at
*/
class AmazonReport extends AbstractCustomerRecord
{
    public const STATUS_WAITING = 'WAITING';
    public const STATUS_QUEUED = 'QUEUED';
    public const STATUS_IN_PROGRESS = 'IN_PROGRESS';
    public const STATUS_DONE = 'DONE';
    public const STATUS_CANCELLED = 'CANCELLED';
    public const STATUS_ERROR = 'ERROR';

    public const PROCESSING_STATUS_UNKNOWN = 'UNKNOWN';

    public const PROCESSING_STATUS_DONE = 'DONE';
    public const PROCESSING_STATUS_IN_PROGRESS = 'IN_PROGRESS';
    public const PROCESSING_STATUS_QUEUED = 'QUEUED';
    public const PROCESSING_STATUS_CANCELLED = 'CANCELLED';
    public const PROCESSING_STATUS_IN_QUEUE = 'IN_QUEUE';
    public const PROCESSING_STATUS_FATAL = 'FATAL';


    public const TYPE_INIT = 'INIT';
    public const TYPE_REFRESH = 'REFRESH';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
            'json' => [
                'class' => \laxity7\yii2\behaviors\JsonFieldBehavior::class,
                'fields' => [
                    'extra_info',
                ],
            ],
        ];
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['amazon_id', 'amazon_type', 'amazon_processing_status', 'customer_id', 'seller_id', 'start_date', 'end_date', 'type'], 'required'],
            [['amazon_id', 'customer_id'], 'default', 'value' => null],
            [['amazon_id', 'customer_id'], 'integer'],
            [['start_date', 'end_date', 'created_at', 'updated_at'], 'safe'],
            [['log'], 'string'],
            [['amazon_type', 'amazon_document_id', 'amazon_processing_status', 'seller_id', 'status', 'type'], 'string', 'max' => 255],
            [['id', 'amazon_id', 'amazon_type', 'amazon_document_id', 'amazon_processing_status', 'customer_id', 'seller_id', 'start_date', 'end_date', 'status', 'type', 'log', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = AmazonReport::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('amazon_id', $this->amazon_id)
            ->andFilterCompare('amazon_type', $this->amazon_type, 'like')
            ->andFilterCompare('amazon_document_id', $this->amazon_document_id, 'like')
            ->andFilterCompare('amazon_processing_status', $this->amazon_processing_status, 'like')
            ->andFilterCompare('customer_id', $this->customer_id)
            ->andFilterCompare('seller_id', $this->seller_id, 'like')
            ->andFilterCompare('start_date', $this->start_date)
            ->andFilterCompare('end_date', $this->end_date)
            ->andFilterCompare('status', $this->status, 'like')
            ->andFilterCompare('type', $this->type, 'like')
            ->andFilterCompare('log', $this->log, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);

        return $query;
    }

    public function setStatus(string $status, string $message = null)
    {
        $this->status = $status;
        $this->log("Status changed to {$status}" . ($message ? " ({$message})" : ''));

        if (in_array($status, [self::STATUS_DONE, self::STATUS_CANCELLED, self::STATUS_ERROR])) {
            $this->finished_at = date('Y-m-d H:i:s');
        }

        $this->save(false);
    }

    public function reInit()
    {
        $this->status = self::STATUS_WAITING;
        $this->amazon_processing_status = self::PROCESSING_STATUS_UNKNOWN;
        $this->amazon_document_id = null;
        $this->amazon_id = null;

        $this->log(sprintf(
            "Re init report data loading to update changed data, attempt %d",
            $this->attempts_made + 1
        ));

        $this->save(false);
    }

    public function setAmazonProcessingStatus(string $status)
    {
        $this->amazon_processing_status = $status;
        $this->log("Amazon Processing Status changed to {$status}");

        if (in_array($status, [self::STATUS_DONE, self::STATUS_CANCELLED, self::STATUS_ERROR])) {
            $this->attempts_made += 1;
        }

        if (in_array($status, [self::STATUS_CANCELLED, self::STATUS_ERROR])) {
            $this->setStatus(self::STATUS_DONE);
        }

        $this->save(false);
    }

    public function log(string $message)
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }
}
