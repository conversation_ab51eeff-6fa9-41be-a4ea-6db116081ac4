<?php

namespace common\models\customer;

use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDictV1;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\dataCompleteness\factor\FactorFactory;
use common\components\ExtendedQuery;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\AmazonMarketplace;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\DbStructure;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Query;
use yii\db\Expression;

/**
 * @OA\Schema(
 * schema="Transaction",
 *   @OA\Property(
 *      property="amazon_order_id",
 *      type="string",
 *      description="Amazon order id"
 *   ),
 *   @OA\Property(
 *      property="seller_id",
 *      type="string",
 *      description="Seller ID"
 *   ),
 *   @OA\Property(
 *      property="sku",
 *      type="string",
 *      description="Product SKU"
 *   ),
 *   @OA\Property(
 *      property="asin",
 *      type="string",
 *      description="Product ASIN"
 *   ),
 *   @OA\Property(
 *      property="product_ean",
 *      type="string",
 *      description="Product EAN"
 *   ),
 *   @OA\Property(
 *      property="product_upc",
 *      type="string",
 *      description="Product UPC"
 *   ),
 *   @OA\Property(
 *      property="product_isbn",
 *      type="string",
 *      description="Product ISBN"
 *   ),
 *   @OA\Property(
 *      property="product_brand",
 *      type="string",
 *      description="Product Brand"
 *   ),
 *   @OA\Property(
 *      property="product_type",
 *      type="string",
 *      description="Product Type"
 *   ),
 *   @OA\Property(
 *      property="product_manufacturer",
 *      type="string",
 *      description="Product Manufacturer"
 *   ),
 *   @OA\Property(
 *      property="product_parent_asin",
 *      type="string",
 *      description="Parent Asin"
 *   ),
 *   @OA\Property(
 *      property="product_adult",
 *      type="boolean",
 *      description="Product Adult"
 *   ),
 *   @OA\Property(
 *      property="title",
 *      type="string",
 *      description="Product title"
 *   ),
 *   @OA\Property(
 *      property="marketplace_id",
 *      type="string",
 *      description="Marketplace ID"
 *   ),
 *   @OA\Property(
 *      property="product_id",
 *      type="integer",
 *      description="Product ID"
 *   ),
 *   @OA\Property(
 *      property="condition",
 *      type="integer",
 *      description="Product condition"
 *   ),
 *   @OA\Property(
 *      property="stock_type",
 *      type="string",
 *      description="Product stock type"
 *   ),
 *   @OA\Property(property="sales_category_depth_1", type="object", ref="#/components/schemas/SalesCategory"),
 *   @OA\Property(property="sales_category_depth_2", type="object", ref="#/components/schemas/SalesCategory"),
 *   @OA\Property(property="sales_category_depth_3", type="object", ref="#/components/schemas/SalesCategory"),
 *   @OA\Property(property="sales_category_depth_4", type="object", ref="#/components/schemas/SalesCategory"),
 *   @OA\Property(
 *      property="transaction_type",
 *      type="string",
 *      description="Transaction type",
 *   ),
 *   @OA\Property(
 *      property="amount",
 *      type="number",
 *      description="Amount"
 *   ),
 *   @OA\Property(
 *      property="offer_type",
 *      type="string",
 *      description="Offer Type"
 *   ),
 *   @OA\Property(
 *      property="transaction_date",
 *      type="string",
 *      description="Transaction date"
 *   ),
 *   @OA\Property(
 *      property="posted_date",
 *      type="string",
 *      description="Order purchase date"
 *   ),
 * )
 */
class TransactionExtendedView extends AbstractCustomerRecord
{
    use ExtraFiltersTrait;

    public const TRANSACTION_TYPE_ADJUSTMENT = 'adjustment';
    public const TRANSACTION_TYPE_RETROCHARGE = 'retrocharge';
    public const TRANSACTION_TYPE_STANDARD = 'standard';
    public const TRANSACTION_TYPE_MANUAL = 'manual';
    public const TRANSACTION_TYPE_ESTIMATED = 'estimated';

    public const TRANSACTION_LEVEL_ACCOUNT = 'account';
    public const TRANSACTION_LEVEL_MARKETPLACE = 'marketplace';
    public const TRANSACTION_LEVEL_ORDER = 'order';
    public const TRANSACTION_LEVEL_PRODUCT = 'product';
    public const TRANSACTION_LEVEL_GLOBAL = 'global';
    public string $salesCategoryStrategyType = SalesCategoryStrategyFactory::DEFAULT_STRATEGY;

    protected ?string $sku = null;
    protected ?string $asin = null;
    protected ?string $title = null;
    protected ?string $condition = null;
    protected ?string $stock_type = null;
    protected ?string $currency_id = null;
    protected ?string $amount = null;
    protected ?string $amount1 = null;
    protected ?string $ean = null;
    protected ?string $upc = null;
    protected ?string $isbn = null;
    protected ?string $brand = null;
    protected ?string $manufacturer = null;
    protected ?string $parent_asin = null;
    protected ?string $adult_product = null;
    protected ?string $factor = null;

    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        return $dbManager->getClickhouseCustomerDb();
    }

    public static function primaryKey()
    {
        return explode(
            ', ',
            'transaction_date, posted_date, marketplace_id, seller_id, seller_sku, seller_order_id, amazon_order_id, category_id, cog_category_id, indirect_cost_id, indirect_cost_type_id, currency_code'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [[
                'posted_date',
                'transaction_date',
                'amazon_order_id',
                'marketplace_id',
                'seller_id',
                'sku',
                'product_id',
                'asin',
                'ean',
                'upc',
                'isbn',
                'brand',
                'factor',
                'manufacturer',
                'adult_product',
                'parent_asin',
                'product_type',
                'title',
                'condition',
                'stock_type',
                'amount',
                'tag_id',
                'offer_type',
                'transaction_type',
                'transaction_level',
                'sales_category_depth_1',
                'sales_category_depth_2',
                'sales_category_depth_3',
                'sales_category_depth_4'
            ],'safe','on'=>'search'],
        ];
    }

    public function search($params): Query
    {
        $this->setScenario('search');
        $this->setAttributes($params);

        $orderBasedTable = $this->getOrderBasedTableName();
        $transactionTable = self::tableName();

        $queryTransactions = self::find();
        $queryEstimated = clone $queryTransactions;

        $currencyId = $params['currency_id'] ?? CurrencyRateManager::BASE_CURRENCY;
        $moneyAccuracy =  \Yii::$app->customerComponent->getMoneyAccuracy();

        $dynamicAmountExpression = "round(
            (amount_eur / $moneyAccuracy) * 
            dictGetOrNull(
                default.currency_rate_dict, 
                'value', 
                tuple(toDate(transaction_date), :currency_id)
            ),
            2
        )";

        $AmazonOrderIdSubQuery = (new Query())->select('amazon_order_id')->from($transactionTable)->groupBy('amazon_order_id');
        $this->applySearchFilters($AmazonOrderIdSubQuery, $params, $dynamicAmountExpression);
        $queryEstimated->from($orderBasedTable)
            ->from(new Expression("(
                SELECT o.*
                FROM {$orderBasedTable} o
                LEFT JOIN (
                    ". $AmazonOrderIdSubQuery->createCommand()->getRawSql() ."
                ) t ON o.amazon_order_id = t.amazon_order_id
                WHERE t.amazon_order_id = ''
            ) AS filtered_estimated
        "));

        $this->applySearchFilters($queryTransactions, $params, $dynamicAmountExpression);
        $this->applySearchFilters($queryEstimated, $params, $dynamicAmountExpression);

        $query = self::find()->from(['o' => $queryTransactions->union($queryEstimated, true)]);

        if (!empty($this->factor)) {
            $factorFactory = new FactorFactory();
            $factorHandler = $factorFactory->getFactor($this->factor);
            if (in_array($this->factor, $factorFactory->getSupportedFactorsForTable(self::tableName()))) {
                $query = $factorHandler->setFilterByQuery($query);
            }
        }

        $selectColumns = [
            "posted_date",
            "transaction_date",
            "marketplace_id",
            "seller_id",
            "amazon_order_id",
            "quantity",
            "created_at",
            "transaction_type",
            "offer_type",
            "product_asin",
            "product_ean",
            "product_upc",
            "product_isbn",
            "product_brand",
            "product_type",
            "product_manufacturer",
            'cast(product_adult as integer) as product_adult',
            "product_parent_asin",
            "product_title",
            "product_condition",
            "product_stock_type",
            "seller_sku",
            "amount_eur",
            "sales_category_depth_1",
            "sales_category_depth_2",
            "sales_category_depth_3",
            "sales_category_depth_4",
            new Expression("CASE WHEN product_id = 0 THEN NULL ELSE product_id END as product_id"),
            new Expression("{$dynamicAmountExpression} as amount1"),
            new Expression(':currency_id as currency_id')
        ];

        try {
            self::find()->select(['tag_id'])->limit(1)->scalar();
            $selectColumns[] = 'tag_id';
        } catch (\Throwable $e) {
            $selectColumns[] = new Expression('null as tag_id');
        }

        $query
            ->addSelect($selectColumns)
            ->addParams(['currency_id' => $currencyId])
        ;

        return $query;
    }

    public function getDefaultSort()
    {
        return [
            'transaction_date' => SORT_DESC,
        ];
    }

    public function getSortAttributes()
    {
        $attributes = parent::getSortAttributes();
        $attributes['amount'] = [
            'asc' => ['amount_eur' => SORT_ASC],
            'desc' => ['amount_eur' => SORT_DESC],
        ];

        $nullableAttributes = [
            'sku' => 'seller_sku',
            'asin' => 'product_asin',
            'title' => 'product_title',
            'condition' => 'product_condition',
            'stock_type' => 'product_stock_type',
            'product_ean',
            'product_upc',
            'product_type',
            'product_isbn',
            'product_brand',
            'product_manufacturer',
            'offer_type'
        ];
        $attributes = array_merge($attributes, $this->buildNullableSortAttributes($nullableAttributes));

        $salesCategoryExtendedDictName = (new SalesCategoryExtendedDictV1())->getName();

        for ($i = 1; $i <= 4; $i++) {
            $attributes["sales_category_depth_$i"] = [
                'asc' => [new Expression("assumeNotNull(sales_category_depth_$i) = '', dictGetOrNull('$salesCategoryExtendedDictName', 'name', tuple(assumeNotNull(sales_category_depth_$i), '$this->salesCategoryStrategyType')) ASC")],
                'desc' => [new Expression("assumeNotNull(sales_category_depth_$i) != '', dictGetOrNull('$salesCategoryExtendedDictName', 'name', tuple(assumeNotNull(sales_category_depth_$i), '$this->salesCategoryStrategyType')) DESC")],
            ];
        }

        return $attributes;
    }

    public function fields()
    {
        $fields = parent::fields();

        unset($fields['seller_sku']);
        unset($fields['product_asin']);
        unset($fields['product_title']);
        unset($fields['offer_type']);
        unset($fields['product_stock_type']);
        unset($fields['product_condition']);
        unset($fields['seller_order_id']);
        unset($fields['category_id']);
        unset($fields['cog_category_id']);
        unset($fields['amount_eur']);
        unset($fields['merge_counter']);
        unset($fields['tag_id']);

        $fields = array_merge($fields, [
            'product_id' => function() {
                return $this->product_id ?: null;
            },
            'product_adult' => function() {
                if (null === $this->product_adult || '' === $this->product_adult) {
                    return null;
                }
                return (bool)$this->product_adult;
            },
            'amount' => function() {
                // For some reason, it always shows null when using "amount" field.
                return $this->amount1;
            },
            'marketplace_id' => function() {
                if (empty($this->marketplace_id) || $this->marketplace_id === AmazonMarketplace::NON_AMAZON) {
                    return null;
                }

                return $this->marketplace_id;
            },
            'seller_id' => function() {
                return $this->seller_id ?: null;
            },
            'amazon_order_id' => function() {
                return $this->amazon_order_id ?: null;
            },
            'sku' => function() {
                return $this->seller_sku ?: null;
            },
            'asin' => function() {
                return $this->product_asin ?: null;
            },
            'title' => function() {
                return $this->product_title ?: null;
            },
            'condition' => function() {
                return $this->product_condition ?: null;
            },
            'stock_type' => function() {
                return $this->product_stock_type ?: null;
            },
            'product_type' => function() {
                return $this->product_type ?: null;
            },
            'offer_type' => function() {
                return $this->offer_type ?: null;
            },
            'currency_id' => function() {
                return $this->currency_id ?: null;
            },
            'sales_category_depth_1' => function() {
                return $this->getSalesCategoryById($this->sales_category_depth_1);
            },
            'sales_category_depth_2' => function() {
                return $this->getSalesCategoryById($this->sales_category_depth_2);
            },
            'sales_category_depth_3' => function() {
                return $this->getSalesCategoryById($this->sales_category_depth_3);
            },
            'sales_category_depth_4' => function() {
                return $this->getSalesCategoryById($this->sales_category_depth_4);
            }
        ]);

        return $fields;
    }

    public function beforeDelete()
    {
        throw new \Exception("Delete through active record is restricted");
    }

    public function beforeSave($insert)
    {
        throw new \Exception("Save through active record is restricted");
    }

    protected static function getActiveQueryInstance(): ActiveQuery
    {
        $extendedQuery = new ExtendedQuery(get_called_class());
        $extendedQuery->setDbEngine(ExtendedQuery::ENGINE_CLICKHOUSE);
        return $extendedQuery;
    }

    protected function getSalesCategoryById(?string $salesCategoryId): ?array
    {
        if (empty($salesCategoryId)) {
            return null;
        }

        return SalesCategoryExtendedView::find()
            ->select('id, name, path, (NOT is_manual) as is_default')
            ->where(['id' => $salesCategoryId])
            ->cache(
                5 * 60,
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->asArray()
            ->one()
        ;
    }

    /**
     * @param Query $query
     * @param $params
     * @param string $dynamicAmountExpression
     * @return void
     */
    public function applySearchFilters(Query $query, $params, string $dynamicAmountExpression): void
    {
        $this->applyBetweenDateFilter($query, 'posted_date', $this->posted_date);
        $this->applyBetweenDateFilter($query, 'transaction_date', $this->transaction_date);
        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplace_seller_ids'] ?? null,
            $this->marketplace_id ?? null,
            $this->seller_id ?? null
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query);
        $this->applyAmazonAdAccountsLogic($query);

        $query->andFilterWhere(['=', 'sales_category_is_visible', 1]);

        // Hide manual costs with zero amounts
        $query->andFilterWhere([
            'OR',
            ['!=', 'amount_eur', 0],
            [
                'AND',
                ['=', 'cog_category_id', 0],
                ['=', 'indirect_cost_type_id', 0]
            ]
        ]);

        $query
            ->andFilterCompare(new Expression("CASE WHEN product_id = 0 THEN NULL ELSE product_id END"), $this->product_id, 'like', 'int')
            ->andFilterCompare('product_condition', $this->condition)
            ->andFilterCompare('product_stock_type', strtoupper($this->stock_type))
            ->andFilterCompare('quantity', $this->quantity, 'like', 'int')
            ->andFilterCompare('amazon_order_id', $this->amazon_order_id, 'like')
            ->andFilterCompare(new Expression($dynamicAmountExpression), $this->amount, 'like', 'float')
            ->andFilterCompare('product_asin', $this->asin, 'like')
            ->andFilterCompare('seller_sku', $this->sku, 'like');

        $this->applySegmentationFilters($query, $params, [
            'ean' => 'product_ean',
            'upc' => 'product_upc',
            'sku' => 'seller_sku',
            'asin' => 'product_asin',
            'isbn' => 'product_isbn',
            'brand' => 'product_brand',
            'manufacturer' => 'product_manufacturer',
            'product_type' => 'product_type',
            'title' => 'product_title',
            'parent_asin' => 'product_parent_asin',
            'adult_product' => 'product_adult',
            'tag_id' => 'tag_id'
        ]);

        $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);

        if (!empty($this->transaction_level)) {
            $transactionLevels = explode(',', $this->transaction_level);
            $query->andWhere(['in', 'transaction_level', $transactionLevels]);
        }

        if (!empty($this->offer_type)) {
            $offerTypes = explode(',', $this->offer_type);
            $query->andWhere(['in', 'offer_type', $offerTypes]);
        }

        if (!empty($this->transaction_type)) {
            if (!is_array($this->transaction_type)) {
                $this->transaction_type = [$this->transaction_type];
            }

            // Walk through array and replace _type with empty string
            $this->transaction_type = array_map(function ($item) {
                return str_replace('_type', '', $item);
            }, $this->transaction_type);

            $query->andFilterWhere(['in', 'transaction_type', $this->transaction_type]);
        }

        for ($i = 1; $i <= 4; $i++) {
            if (empty($params["sales_category_depth_{$i}"])) {
                continue;
            }
            $chosenSalesCategoryIds = explode('-', implode('-', $params["sales_category_depth_{$i}"]));
            $query->andFilterWhere(['in', "sales_category_depth_{$i}", $chosenSalesCategoryIds]);
        }
    }

    protected function getOrderBasedTableName(): string
    {
        return OrderBasedTransactionExtendedView::tableName();
    }
}
