<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "fba_storage_fee_history".
 *
 * @property int $id
 * @property string $marketplace_id
 * @property string $seller_id
 * @property string $sku
 * @property string $fnsku
 * @property string|null $asin
 * @property string $date
 * @property float|null $amount
 * @property float|null $diff_amount
 * @property string|null $currency
 * @property string $report_type
 * @property string $created_at
 * @property string $status_moved_to_clickhouse
 * @property string|null $moved_to_clickhouse_at
 */
class FbaStorageFeeHistory extends AbstractCustomerRecord
{
    public const REPORT_TYPE_STORAGE = 'storage';
    public const REPORT_TYPE_LONGTERM = 'longterm';

    public const STATUS_CREATED = 'created';
    public const STATUS_MOVED_TO_CLICHOUSE = 'moved';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function markAsMovedToClickhouse(): void
    {
        $this->status_moved_to_clickhouse = self::STATUS_MOVED_TO_CLICHOUSE;
        $this->moved_to_clickhouse_at = date('Y-m-d H:i:s');
        $this->save(false);
    }

    public function isMovedToClickhouse(): bool
    {
        return $this->status_moved_to_clickhouse === self::STATUS_MOVED_TO_CLICHOUSE;
    }
}
