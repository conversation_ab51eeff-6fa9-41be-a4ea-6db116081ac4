<?php

namespace common\models\customer;

use common\components\COGSync\GlobalMarketplaceService;
use common\components\COGSync\ProductToProductSynchronizer;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\dataCompleteness\factor\FactorFactory;
use common\components\LogToConsoleTrait;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\Seller;
use Exception;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

/**
 * This is the model class for table "product_cost".
 *
 * @OA\Schema(
 *     schema="Product",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Parameter(
 *         name="marketplaceSellerIds",
 *         in="query",
 *         description="Pairs of marketplace and seller ids in json format",
 *         required=false,
 *         @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="integer",
 *         description="Marketplace id (comma separated values can be sent)",
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller id (comma separated values can be sent)",
 *     ),
 *     @OA\Property(
 *         property="sku",
 *         type="string",
 *         description="SKU",
 *     ),
 *     @OA\Property(
 *         property="condition",
 *         type="string",
 *         description="Condtion Id",
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title",
 *     ),
 *     @OA\Property(
 *         property="is_enabled_sync_with_repricer",
 *         type="boolean",
 *         description="Is sync with repricer enabled",
 *     ),
 *     @OA\Property(
 *         property="is_enabled_sync_with_global_marketplace",
 *         type="boolean",
 *         description="Is sync with global marketplace enabled",
 *     ),
 *     @OA\Property(
 *         property="repricer_id",
 *         type="boolean",
 *         description="Repricer id",
 *     ),
 *     @OA\Property(
 *         property="source",
 *         type="string",
 *         description="Source (manual|repricer)",
 *     ),
 *     @OA\Property(
 *         property="asin",
 *         type="string",
 *         description="ASIN",
 *     ),
 *     @OA\Property(
 *         property="ean",
 *         type="string",
 *         description="EAN",
 *     ),
 *     @OA\Property(
 *         property="isbn",
 *         type="string",
 *         description="ISBN",
 *     ),
 *     @OA\Property(
 *         property="upc",
 *         type="string",
 *         description="UPC",
 *     ),
 *     @OA\Property(
 *         property="main_image",
 *         type="string",
 *         description="Main Image",
 *     ),
 *     @OA\Property(
 *         property="parent_asin",
 *         type="string",
 *         description="Parent",
 *     ),
 *     @OA\Property(
 *         property="brand",
 *         type="string",
 *         description="Brand",
 *     ),
 *     @OA\Property(
 *         property="model",
 *         type="string",
 *         description="Model",
 *     ),
 *     @OA\Property(
 *         property="product_type",
 *         type="string",
 *         description="Product Type",
 *     ),
 *     @OA\Property(
 *         property="manufacturer",
 *         type="string",
 *         description="Manufacturer",
 *     ),
 *     @OA\Property(
 *         property="age_range_description",
 *         type="string",
 *         description="Age Range",
 *     ),
 *     @OA\Property(
 *         property="adult_product",
 *         type="boolean",
 *         description="Adult Product",
 *     ),
 *     @OA\Property(
 *         property="buying_price",
 *         type="string",
 *         description="Buying price",
 *     ),
 *     @OA\Property(
 *         property="tags",
 *         type="array",
 *         description="Product Tags",
 *         @OA\Items(ref="#/components/schemas/Tag")
 *     ),
 *     @OA\Property(
 *         property="is_global_marketplace_product",
 *         type="boolean",
 *         description="Is Global Marketplace Product",
 *     ),
 *     @OA\Property(
 *         property="is_enabled_cost_of_goods_sync",
 *         type="boolean",
 *         description="Is Enabled Cost Of Goods Sync",
 *     ),
 *     @OA\Property(
 *         property="is_enabled_other_fees_sync",
 *         type="boolean",
 *         description="Is Enabled Other Fees Sync",
 *     ),
 *     @OA\Property(
 *         property="is_enabled_fbm_shipping_cost_sync",
 *         type="boolean",
 *         description="Is Enabled FBM Shipping Cost Sync",
 *     )
 * )
 *
 * @property int $id
 * @property int $repricerId
 * @property string $marketplace_id
 * @property string $seller_id
 * @property int $condition
 * @property string $sku
 * @property string $title
 * @property string $source
 * @property string $asin
 * @property string $ean
 * @property string $isbn
 * @property string $upc
 * @property string $fnsku
 * @property string $parent_asin
 * @property string $brand
 * @property string $model
 * @property string $product_type
 * @property string $manufacturer
 * @property string $age_range
 * @property string $stock_type
 * @property bool|null $adult_product
 * @property float|null $buying_price
 * @property float|null $other_fees
 * @property float|null $shipping_cost
 * @property float|null $vat
 * @property bool $is_multiple_stock_type
 * @property bool $is_enabled_sync_with_repricer
 * @property bool $is_product_cost_periods_linked
 * @property bool $is_enabled_sync_with_global_marketplace
 * @property string $sync_with_global_marketplace_enabled_at
 * @property int $global_marketplace_sync_version
 * @property string $synced_with_marketplace_at
 * @property string $currency_code
 * @property array $tags
 * @property string $created_at
 * @property string $updated_at
 */
class Product extends AbstractCustomerRecord
{
    use LogToConsoleTrait;
    use ExtraFiltersTrait;

    public const CONDITIONS_MAP = [
        1 => 'Used; Like New',
        2 => 'Used; Very Good',
        3 => 'Used; Good',
        4 => 'Used; Acceptable',
        5 => 'Collectible; Like New',
        6 => 'Collectible; Very Good',
        7 => 'Collectible; Good',
        8 => 'Collectible; Acceptable',
        9 => 'Used; Refurbished',
        10 => 'Refurbished',
        11 => 'New',
    ];

    public const SCENARIO_DATA_IMPORT = 'data_import';
    public const SOURCE_REPRICER = 'repricer';
    public const SOURCE_ORDER_INFO = 'order_info';
    public const SOURCE_REPORT = 'report';

    public const SOURCE_CUSTOMER_SERVICE = 'customer_service';
    public const SOURCE_MANUAL = 'manual';

    public const STOCK_TYPE_FBA = 'FBA';
    public const STOCK_TYPE_FBM = 'FBM';

    // Import related virtual fields
    public $cost_of_goods = null;
    public $marketplace = null;
    public $item_sku = null;
    public $shipping_cost_start_date = null;
    public $cost_of_goods_start_date = null;
    public $other_fees_start_date = null;
    public $vat_start_date = null;
    public $factor = null;
    public $tags = [];
    public $tag_id = null;

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function getDefaultSort()
    {
        return [
            'repricer_id' => SORT_DESC,
            'id' => SORT_DESC,
        ];
    }

    public function getSortAttributes()
    {
        $attributes = parent::getSortAttributes();
        $attributes['tag_id'] = [
            'asc' => ['tag_id' => SORT_ASC],
            'desc' => ['tag_id' => SORT_DESC],
        ];

        return $attributes;
    }

    /**
     * @param $params
     * @return ActiveQuery
     * @throws \Exception
     */
    public function search($params): ActiveQuery
    {
        $this->setScenario('search');
        $this->setAttributes($params);

        $query = self::find()->alias('product');

        if (!empty($this->factor)) {
            $factorFactory = new FactorFactory();
            $factorHandler = $factorFactory->getFactor($this->factor);
            if (in_array($this->factor, $factorFactory->getSupportedFactorsForTable(self::tableName()))) {
                $query = $factorHandler->setFilterByQuery($query);
            }
        }
        $query->joinWith('tags');

        if (isset($params['tag_id'])) {
            $tagsArray = explode(',', $params['tag_id']);
            $conditions = ['OR'];

            foreach ($tagsArray as $tagId) {
                if ($tagId == '0') {
                    $conditions[] = [
                        'NOT EXISTS',
                        (new Query())
                            ->from(ProductTag::tableName())
                            ->where('product_tag.product_id = product.id')
                    ];
                } else {
                    $conditions[] = ['tag.id' => $tagId];
                }
            }

            $query->andWhere($conditions);
            unset($params['tag_id']);
        }

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplaceSellerIds'] ?? '',
            $this->marketplace_id,
            $this->seller_id
        );

        $this->applySegmentationFilters($query, $params);

        $query
            ->andFilterCompare('product.id', $this->id)
            ->andFilterCompare('stock_type', $this->stock_type)
            // BAS-1957 - temporary disabled, FE wrongly sends this filter
//            ->andFilterCompare('currency_code', $this->currency_code)
            ->andFilterCompare('adult_product', $this->adult_product)
            ->andFilterCompare('condition', $this->condition)
            ->andFilterCompare('is_enabled_sync_with_repricer', $this->is_enabled_sync_with_repricer)
            ->andFilterCompare('buying_price', $this->buying_price, 'like', 'float')
            ->andFilterCompare('other_fees', $this->other_fees, 'like', 'float')
            ->andFilterCompare('shipping_cost', $this->shipping_cost, 'like', 'float')
            ->andFilterCompare('vat', $this->vat, 'like', 'float')
        ;

        if (array_key_exists('repricer_id', $params)) {
            if ($params['repricer_id'] == 0) {
                $query->andWhere([
                    'OR',
                    ['=', 'repricer_id', 0],
                    ['=', 'repricer_is_deleted', 't']
                ]);
            } else {
                $query->andFilterCompare('repricer_id', $this->repricer_id);
            }
        }

        if (!empty($params['search'])) {
            $params['search'] = mb_strtolower($params['search']);
            $query->andFilterWhere([
                'OR',
                ['like', new Expression("LOWER(sku)"), $params['search']],
                ['like', new Expression("LOWER(asin)"), $params['search']],
                ['like', new Expression("LOWER(product.title)"), $params['search']]
            ]);
        } else {
            if (!empty($params['exact_sku'])) {
                $query->andFilterCompare('sku', $params['exact_sku']);
            }
        }

        $this->applyExpiredOrInactiveSubscriptionLogic($query, 'seller_id');
        $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $this->applyBetweenDateFilter($query, 'updated_at', $this->updated_at);

        // Need to avoid error on frontend ui
        $query->andWhere(['!=', 'sku', '']);

        $query->groupBy('product.id, product_tag.tag_id');

        return $query;
    }

    public function beforeSave($insert)
    {
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        if ($insert && $dbManager->isSyncDefault() === false) {
            $this->is_enabled_sync_with_repricer = false;
        }

        if (!$this->is_enabled_sync_with_global_marketplace) {
            $this->global_marketplace_sync_version = 0;
            $this->synced_with_marketplace_at = null;
            $this->sync_with_global_marketplace_enabled_at = null;
        }

        if ($this->is_enabled_sync_with_global_marketplace
            && empty($this->sync_with_global_marketplace_enabled_at)
        ) {
            $this->sync_with_global_marketplace_enabled_at = date('Y-m-d H:i:s');
        }

        if ($this->global_marketplace_sync_version && $this->isAttributeChanged('global_marketplace_sync_version')) {
            $this->synced_with_marketplace_at = date('Y-m-d H:i:s');
        }

        return parent::beforeSave($insert);
    }

    public function fields()
    {
        $fields = parent::fields();

        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        /** @var GlobalMarketplaceService $globalMarketplaceService */
        $globalMarketplaceService = \Yii::$container->get('globalMarketplaceService');

        $fields['repricer_is_deleted'] = function (Product $model) {
            if (empty($model->repricer_id)) {
                return null;
            }
            if (null === $model->repricer_is_deleted) {
                return true;
            }
            return $model->repricer_is_deleted;
        };
        $fields['tags'] = function (Product $model) {
            return $model->getTags()->all() ?? [];
        };

        $globalMarketplacesBySeller = \common\models\CustomerConfig::find()
            ->select('value')
            ->where([
                'parameter' => CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
                'customer_id' => \Yii::$app->dbManager->getCustomerId()
            ])
            ->indexBy('seller_id')
            ->column();

        $fields['is_global_marketplace_product'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;

            return $globalMarketplaceId == $model->marketplace_id;
        };

        $fields['is_enabled_sync_with_global_marketplace'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;
            $isGlobalMarketplaceProduct = $globalMarketplaceId == $model->marketplace_id;

            return !$isGlobalMarketplaceProduct
                && $model->is_enabled_sync_with_global_marketplace;
        };

        $fields['is_enabled_cost_of_goods_sync'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller, $globalMarketplaceService) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;
            $isGlobalMarketplaceProduct = $globalMarketplaceId == $model->marketplace_id;

            return
                !empty($globalMarketplaceId)
                && !$isGlobalMarketplaceProduct
                && $globalMarketplaceService->isEnabledCostOfGoodsSync($model->seller_id);
        };
        $fields['synced_with_marketplace_at'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller, $globalMarketplaceService) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;
            $isGlobalMarketplaceProduct = $globalMarketplaceId == $model->marketplace_id;

            if (empty($globalMarketplaceId) || $isGlobalMarketplaceProduct) {
                return null;
            }

            $currentSyncVersion = $globalMarketplaceService->getCurrentSyncVersion($model->seller_id);

            if ($model->global_marketplace_sync_version < $currentSyncVersion) {
                return null;
            }

            return $this->synced_with_marketplace_at;
        };

        $fields['is_enabled_other_fees_sync'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller, $globalMarketplaceService) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;
            $isGlobalMarketplaceProduct = $globalMarketplaceId == $model->marketplace_id;

            return
                !empty($globalMarketplaceId)
                && !$isGlobalMarketplaceProduct
                && $globalMarketplaceService->isEnabledOtherFeesSync($model->seller_id);
        };

        $fields['is_enabled_fbm_shipping_cost_sync'] = function (Product $model) use ($customerConfig, $globalMarketplacesBySeller, $globalMarketplaceService) {
            $globalMarketplaceId = $globalMarketplacesBySeller[$model->seller_id] ?? null;
            $isGlobalMarketplaceProduct = $globalMarketplaceId == $model->marketplace_id;

            return
                !empty($globalMarketplaceId)
                && !$isGlobalMarketplaceProduct
                && $globalMarketplaceService->isEnabledFBMShippingCostSync($model->seller_id);
        };

        return $fields;
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (isset($changedAttributes['is_enabled_sync_with_repricer'])) {
            ProductCostPeriod::getDb()->createCommand()->update(
                ProductCostPeriod::tableName(),
                [
                    'source' => ProductCostCategory::SOURCE_MANUAL
                ],
                [
                    'AND',
                    ['=', 'marketplace_id', $this->marketplace_id],
                    ['=', 'seller_id', $this->seller_id],
                    ['=', 'seller_sku', $this->sku]
                ]
            )->execute();
            $this->source = ProductCostCategory::SOURCE_MANUAL;
            $this->save(false);
        }

        if (isset($changedAttributes['is_enabled_sync_with_global_marketplace'])
            && $this->is_enabled_sync_with_global_marketplace
        ) {
            try {
                $productToProductSynchronizer = new ProductToProductSynchronizer();
                $productToProductSynchronizer->syncWithGlobalMarketplace($this->seller_id, $this->sku, $this->marketplace_id);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        return parent::afterSave($insert, $changedAttributes);
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['default'] = [
            'is_enabled_sync_with_repricer',
            'is_enabled_sync_with_global_marketplace',
            'tag_id',
            'tags'
        ];
        $scenarios[self::SCENARIO_DATA_IMPORT] = [
            'cost_of_goods',
            'shipping_cost_start_date', 'cost_of_goods_start_date', 'other_fees_start_date', 'vat_start_date',
            'other_fees',
            'shipping_cost',
            'vat',
            'seller_id',
            'marketplace',
            'item_sku',
            'is_enabled_sync_with_repricer',
            'is_enabled_sync_with_global_marketplace',
            'tag_id'
        ];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['marketplace_id', 'sku', 'asin', 'currency_code'], 'required'],
            [['is_enabled_sync_with_repricer', 'is_multiple_stock_type', 'is_enabled_sync_with_global_marketplace'], 'boolean'],
            [['buying_price', 'other_fees', 'shipping_cost', 'vat', 'cost_of_goods', 'shipping_cost_start_date', 'cost_of_goods_start_date', 'other_fees_start_date', 'vat_start_date'], 'default', 'value' => null],
            [['buying_price', 'other_fees', 'shipping_cost', 'vat', 'cost_of_goods'], 'number'],
            [['created_at', 'updated_at'], 'safe'],
            [['marketplace_id', 'sku', 'fnsku', 'asin', 'source', 'factor'], 'string', 'max' => 255],
            [['currency_code'], 'string', 'max' => 3],
            [['tag_id'], 'each', 'rule' => ['integer']],
            ['tag_id', 'checkMaxTags'],
            [['sku', 'marketplace_id'], 'unique', 'targetAttribute' => ['sku', 'marketplace_id']],
            [
                [
                    'id',
                    'title',
                    'sku',
                    'asin',
                    'buying_price',
                    'other_fees',
                    'shipping_cost',
                    'vat',
                    'seller_id',
                    'condition',
                    'source',
                    'stock_type',
                    'factor',
                    'currency_code',
                    'marketplace_id',
                    'is_enabled_sync_with_repricer',
                    'is_enabled_sync_with_global_marketplace',
                    'is_multiple_stock_type',
                    'repricer_id',
                    'tag_id'
                ],
                'safe',
                'on' => self::SCENARIO_SEARCH,
            ],
            [['cost_of_goods', 'other_fees', 'shipping_cost', 'vat'],
                'compare',
                'compareValue' => 0,
                'operator' => '>=',
                'skipOnEmpty' => true,
                'on' => self::SCENARIO_DATA_IMPORT
            ],
            [['shipping_cost_start_date', 'cost_of_goods_start_date', 'other_fees_start_date', 'vat_start_date'], 'datetime', 'format' => 'php:Y-m-d', 'skipOnEmpty' => true],
            [['cost_of_goods', 'other_fees', 'shipping_cost', 'vat', 'shipping_cost_start_date', 'cost_of_goods_start_date', 'other_fees_start_date', 'vat_start_date'],
                'safe',
                'on' => self::SCENARIO_DATA_IMPORT
            ],
            [[
                'seller_id',
                'marketplace',
                'item_sku',
            ], 'required', 'on' => self::SCENARIO_DATA_IMPORT],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => \Yii::t('admin', 'ID'),
            'marketplace_id' => \Yii::t('admin', 'Marketplace ID'),
            'seller_id' => \Yii::t('admin', 'Seller ID'),
            'marketplace' => \Yii::t('admin', 'Marketplace'),
            'sku' => \Yii::t('admin', 'SKU'),
            'fnsku' => \Yii::t('admin', 'FNSKU'),
            'item_sku' => \Yii::t('admin', 'Item SKU'),
            'condition' => \Yii::t('admin', 'Condition'),
            'fulfillment_method' => \Yii::t('admin', 'Fulfillment Method'),
            'asin' => \Yii::t('admin', 'ASIN'),
            'title' => \Yii::t('admin', 'Title'),
            'factor' => \Yii::t('admin', 'Factor'),
            'buying_price' => \Yii::t('admin', 'Buying Price'),
            'other_fees' => \Yii::t('admin', 'Other Fees'),
            'other_fees_start_date' => \Yii::t('admin', 'Other Fees Start Date'),
            'shipping_cost' => \Yii::t('admin', 'FBM Shipping Costs'),
            'shipping_cost_start_date' => \Yii::t('admin', 'FBM Shipping Costs Start Date'),
            'cost_of_goods' => \Yii::t('admin', 'Cost Of Goods'),
            'cost_of_goods_start_date' => \Yii::t('admin', 'Cost Of Goods Start Date'),
            'date_start' => \Yii::t('admin', 'Start Date'),
            'vat' => \Yii::t('admin', 'VAT'),
            'vat_start_date' => \Yii::t('admin', 'VAT Start Date'),
            'currency_code' => \Yii::t('admin', 'Currency Code'),
            'tags' => \Yii::t('admin', 'Tags'),
            'tag_id' => \Yii::t('admin', 'Tag Id'),
            'is_enabled_sync_with_global_marketplace' => \Yii::t('admin', 'Is enabled sync with global marketplace'),
        ];
    }

    public function checkMaxTags($attribute, $params)
    {
        $tagsArray = $this->$attribute;
        if (!is_array($this->$attribute)) {
            $tagsArray = explode(',', $this->$attribute);
        }

        if (count($tagsArray) > 30) {
            $this->addError($attribute, \Yii::t('admin', "Tags limit exceeded"));
        }
    }

    /**
     * @throws InvalidConfigException
     */
    public function getTags()
    {
        return $this->hasMany(Tag::class, ['id' => 'tag_id'])
            ->viaTable(ProductTag::tableName(), ['product_id' => 'id']);
    }
}
