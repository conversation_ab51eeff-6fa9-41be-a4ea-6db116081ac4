<?php

namespace common\models\customer;

use Aws\S3\S3Client;
use common\components\S3Component;
use common\models\customer\base\AbstractCustomerRecord;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\FileHelper;


/**
 * @OA\Parameter(
 *     parameter="importExportHandlerName",
 *     name="handler_name",
 *     in="query",
 *     description="Handler Name",
 *     required=false,
 *     @OA\Schema(type="string", enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost", "orders", "orders_v1", "aggregated_sales_info"})
 * ),
 * @OA\Parameter(
 *     parameter="importExportStatus",
 *     name="status",
 *     in="query",
 *     description="Status",
 *     required=false,
 *     @OA\Schema(type="string",enum={"new", "queued", "done", "in_progress", "no_items", "terminated"})
 * )
 */

/**
 * @property int              $id
 * @property string           $handler_name
 * @property string           $status
 * @property int              $count_parts
 * @property string           $log
 * @property string           $exception
 * @property int              $count_all_items
 * @property int              $count_imported_items
 * @property int              $count_errors
 * @property int              $data_start_line_number
 * @property string           $file_url
 * @property string           $created_at
 * @property string           $started_at
 * @property string           $finished_at
 * @property string           $updated_at
 * @property string           $language_code
 * @property string           $type
 * @property int|null         $last_id
 * @property string           $params
 */
abstract class BaseDataImportExport extends AbstractCustomerRecord
{
    const SIDE_SELECTED = 'selected';
    const SIDE_ALL = 'all';

    public const STATUS_NEW = 'new';
    public const STATUS_QUEUED = 'queued';

    public const STATUS_FINISHED = 'done';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_NO_ITEMS = 'no_items';
    public const STATUS_TERMINATED = 'terminated';

    public const TYPE_MANUAL = 'manual';
    public const TYPE_AUTO = 'auto';
    public const TYPE_BULK_EDIT = 'bulk_edit';

    public static $dbId = 'customer';

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        if (static::class === DataImport::class) {
            $behaviors[] = [
                'class' => \laxity7\yii2\behaviors\JsonFieldBehavior::class,
                'fields' => [
                    'errors',
                ],
            ];
        }

        $behaviors['timestamp'] = [
            'class' => TimestampBehavior::class,
            'value' => date('Y-m-d H:i:s')
        ];

        return $behaviors;
    }

    public function setException(string $exception): self
    {
        $this->log("Exception: " . $exception);
        $this->exception = $exception;

        return $this;
    }

    public function setQueued(): void
    {
        if ($this->status === self::STATUS_QUEUED) {
            return;
        }

        $this->status = self::STATUS_QUEUED;
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function setFinished()
    {
        if (in_array($this->status, [self::STATUS_FINISHED, self::STATUS_NO_ITEMS], true)) {
            return;
        }

        if ($this->count_all_items === 0) {
            $this->status = self::STATUS_NO_ITEMS;
        } else {
            $this->status = self::STATUS_FINISHED;
        }

        $this->finished_at = date('Y-m-d H:i:s');
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function setInProgress()
    {
        if ($this->status === self::STATUS_IN_PROGRESS) {
            return;
        }

        $this->status = self::STATUS_IN_PROGRESS;
        $this->started_at = date('Y-m-d H:i:s');
        $this->log('Status: ' . $this->status);
        $this->save(false);
    }

    public function log(string $message): void
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }

    public function delete()
    {
        if (!empty($this->file_url)) {
            /** @var S3Client $s3 */
            $s3 = Yii::$app->s3->getInstance();
            $bucket = Yii::$app->s3->bucket;
            $s3->deleteMatchingObjects($bucket, $this->getS3Folder() . '/');
        }

        parent::delete();
    }

    public function uploadFileToStorage(string $filePath, string $fileExtension, string $mimeType = null, string $originalFileName = null): void
    {
        $originalFileName = $originalFileName ?: md5(uniqid('', true));

        /** @var S3Component $s3 */
        $s3 = Yii::$app->s3;
        $sourceFileUrl = $s3->uploadFile(
            $this->getS3Folder() . '/' . $originalFileName . '.' . $fileExtension,
            $filePath,
            $mimeType ?? FileHelper::getMimeType($filePath)
        );
        $this->file_url = $sourceFileUrl;
    }

    /**
     * @param $fp
     * @param $url
     * @param  false  $useAuth
     * @param  string $username
     * @param  string $password
     * @return bool
     */
    public static function downloadFile($fp, $url, $useAuth = false, $username = '', $password = '')
    {
        if (strpos(strtolower($url), "://") === false) {
            $url = "http://" . $url;
        }
        $url = self::encodeUrl($url);
        $handle = curl_init($url);

        curl_setopt($handle, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($handle, CURLOPT_TIMEOUT, 300);
        curl_setopt($handle, CURLOPT_USERAGENT, "SellerLogic Import");
        curl_setopt($handle, CURLOPT_FILE, $fp);

        if ($useAuth) {
            curl_setopt($handle, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
            curl_setopt($handle, CURLOPT_USERPWD, "$username:$password");
        }

        curl_exec($handle);
        curl_close($handle);
        fclose($fp);

        return true;
    }

    /**
     * @param $url
     * @return string
     */
    public static function encodeUrl($url)
    {
        if (strpos($url, ' ') === false) {
            return $url;
        }

        $url = explode('/', $url);
        $base = array_pop($url);

        return implode('/', $url) . '/' . rawurlencode($base);
    }

    abstract public function getS3Folder(): string;
}
