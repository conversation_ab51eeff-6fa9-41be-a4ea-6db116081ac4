<?php

namespace common\models\customer;

use common\components\COGSync\COGChangesManager;
use common\components\COGSync\PeriodsMerge;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\SalesCategory;
use yii\behaviors\TimestampBehavior;
use yii\caching\CacheInterface;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Query;

/**
* This is the API model class for table "product_cost_item".
*
* @OA\Schema(
* schema="ProductCostItem",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="product_cost_period_id",
*      type="integer",
*      description="Product Cost Period ID"
*   ),
*   @OA\Property(
*      property="product_cost_category_id",
*      type="integer",
*      description="Product Cost Category ID"
*   ),
*   @OA\Property(
*      property="amount_total",
*      type="number",
*      description="Amount Total"
*   ),
*   @OA\Property(
*      property="currency_id",
*      type="number",
*      description="Currency ID"
*   ),
*   @OA\Property(
*      property="amount_per_unit",
*      type="number",
*      description="Amount Per Unit"
*   ),
*   @OA\Property(
*      property="units",
*      type="integer",
*      description="Units"
*   ),
*   @OA\Property(
*      property="note",
*      type="string",
*      description="Note"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property int $product_cost_period_id
* @property int $product_cost_category_id
* @property float $amount_total
* @property string $currency_id
* @property float $amount_per_unit
* @property float $marketplace_currency_rate
* @property string $marketplace_currency_id
* @property string $marketplace_amount_per_unit
* @property int $units
* @property string|null $note
* @property string|null $status
* @property string|null $count_affected_transactions
* @property bool $is_currency_rate_recalculated
* @property string $created_at
* @property string $updated_at
*/
class ProductCostItem extends AbstractCustomerRecord
{
    public const STATUS_QUEUED = 'queued';
    public const STATUS_APPLYING = 'applying';
    public const STATUS_FAILED = 'failed';
    public const STATUS_APPLIED = 'applied';

    public bool $shouldSendCogChanges = true;
    public static bool $isSyncMode = false;
    public ?float $amount_vat = null;

    private CurrencyRateManager $currencyRateManager;

    public function __construct($config = [])
    {
        /** @var CurrencyRateManager $currencyRateManager */
        $this->currencyRateManager = \Yii::$container->get('currencyRateManager');
        parent::__construct($config);
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['product_cost_period_id', 'product_cost_category_id', 'currency_id', 'amount_total', 'units'], 'required'],
            [
                ['amount_total', 'amount_per_unit'], 'number', 'min' => 0, 'max' => *********,
                 'when' => function() {
                    /** @var ProductCostCategory $productCostCategory */
                    $productCostCategory = $this->getProductCostCategory()->one();
                    return $productCostCategory->sales_category_id !== SalesCategory::CATEGORY_EXPENSES_TAXES;
                 }
            ],
            [
                ['amount_vat'], 'number', 'min' => 0, 'max' => 100,
                'when' => function() {
                    /** @var ProductCostCategory $productCostCategory */
                    $productCostCategory = $this->getProductCostCategory()->one();
                    return $productCostCategory->sales_category_id === SalesCategory::CATEGORY_EXPENSES_TAXES;
                }
            ],
            [['marketplace_currency_rate'], 'compare', 'compareValue' => 300, 'operator' => '<='],
            [['marketplace_currency_rate'], 'compare', 'compareValue' => 0, 'operator' => '>'],
            [['units'], 'default', 'value' => 1],
            [['units'], 'number', 'max' => 1000000, 'min' => 1],
            [['note'], 'string', 'max' => 5000],
            [['product_cost_period_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductCostPeriod::class, 'targetAttribute' => ['product_cost_period_id' => 'id']],
            [['product_cost_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductCostCategory::class, 'targetAttribute' => ['product_cost_category_id' => 'id']],
            [['id', 'product_cost_period_id', 'product_cost_category_id', 'amount_total', 'currency_id', 'amount_per_unit', 'units', 'note', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public static function invalidateCachedItemsForCustomer(): void
    {
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->fastPersistentCache;
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $tag = 'v3_customer_product_cost_items_' . $dbManager->getCustomerId();
        TagDependency::invalidate($cache, $tag);
    }

    public static function warmUpCachedItemsBySellerSku(string $sellerSku): array
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $tag = 'v3_customer_product_cost_items_' . $dbManager->getCustomerId();
        $dependency = new TagDependency(['tags' => $tag]);

        $items = (new Query())
            ->select('
                pci.product_cost_category_id, 
                pci.amount_per_unit, 
                pci.marketplace_amount_per_unit, 
                pci.marketplace_currency_id,
                pci.currency_id,
                pcp.date_start,
                pcp.date_end,
                pcp.marketplace_id,
                pcp.seller_id,
                pcp.seller_sku
            ')
            ->from(ProductCostItem::tableName() . ' pci')
            ->leftJoin(ProductCostPeriod::tableName() . ' pcp', 'pcp.id = pci.product_cost_period_id')
            ->where(['=', 'pcp.seller_sku', $sellerSku])
            ->cache(30, $dependency)
            ->all(ProductCostItem::getDb()) ?? [];

        return $items;
    }

    public static function findSuitableForSellerAndMarketplaceBulk(
        array $dataToFilterBy,
        array $productCostCategoryIds = null
    ): array
    {
        $skus = [];
        $sellerIds = [];
        $marketplaceIds = [];
        foreach ($dataToFilterBy as $chunks) {
            foreach ($chunks as $chunk) {
                $skus[] = $chunk['seller_sku'];
                $sellerIds[] = $chunk['seller_id'];
                $marketplaceIds[] = $chunk['marketplace_id'];
            }
        }
        $skus = array_unique($skus);
        $sellerIds = array_unique($sellerIds);
        $marketplaceIds = array_unique($marketplaceIds);

        $allProductCostItems = (new Query())
            ->select('
                pci.product_cost_category_id, 
                pci.amount_per_unit, 
                pci.marketplace_amount_per_unit, 
                pci.marketplace_currency_id,
                pci.currency_id,
                pcp.date_start,
                pcp.date_end,
                pcp.marketplace_id,
                pcp.seller_id,
                pcp.seller_sku
            ')
            ->from(ProductCostItem::tableName() . ' pci')
            ->leftJoin(ProductCostPeriod::tableName() . ' pcp', 'pcp.id = pci.product_cost_period_id')
            ->where([
                'AND',
                ['in', 'pcp.marketplace_id', $marketplaceIds],
                ['in', 'pcp.seller_id', $sellerIds],
                ['in', 'pcp.seller_sku', $skus],
            ])
            ->andFilterWhere(['in', 'pci.product_cost_category_id', $productCostCategoryIds])
            ->all(ProductCostItem::getDb()) ?? [];

        $resultGroupedByPostedDate = [];
        foreach ($dataToFilterBy as $postedDate => $chunks) {
            foreach ($chunks as $key => $chunk) {
                $postedDateTimestamp = strtotime($postedDate);
                $chunkProductCostItems = [];

                foreach ($allProductCostItems as $productCostItem) {
                    if ($productCostItem['seller_sku'] !== $chunk['seller_sku']
                        || $productCostItem['marketplace_id'] !== $chunk['marketplace_id']
                        || $productCostItem['seller_id'] !== $chunk['seller_id']
                    ) {
                        continue;
                    }

                    $dateStartTimestamp = $productCostItem['date_start']
                        ? strtotime($productCostItem['date_start'])
                        : strtotime('-30 years');
                    $dateEndTimestamp = $productCostItem['date_end']
                        ? strtotime($productCostItem['date_end'])
                        : strtotime('+30 years');
                    // Hotfix to prevent duplicates (there is a bug with similar date_start, date_end on different periods)
                    $dateEndTimestamp -= 0.01;

                    if ($postedDateTimestamp > $dateEndTimestamp) {
                        continue;
                    }
                    if ($postedDateTimestamp < $dateStartTimestamp) {
                        continue;
                    }
                    $chunkProductCostItems[] = $productCostItem;
                }
                $resultGroupedByPostedDate[$postedDate][$key] = $chunkProductCostItems;
            }
        }

        return $resultGroupedByPostedDate;
    }

    public static function removeCachedItemsBySellerSku(string $sellerSku): void
    {
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->fastPersistentCache;
        /** @var DbManager $dbManager */
        $cache->delete(self::getCOGCacheKey($sellerSku));
    }

    public static function findSuitableForSellerAndMarketplace(
        string $marketplaceId,
        string $sellerId,
        string $sellerSku,
        \DateTime $transactionDate
    ): array {
        $items = self::warmUpCachedItemsBySellerSku($sellerSku);
        $itemsByCategory = [];
        $transactionDateTimestamp = $transactionDate->getTimestamp();

        foreach ($items as $item) {
            if (empty($item['seller_id'])) {
                continue;
            }

            $dateStartTimestamp = $item['date_start']
                ? strtotime($item['date_start'])
                : strtotime('-30 years');
            $dateEndTimestamp = $item['date_end']
                ? strtotime($item['date_end'])
                : strtotime('+30 years');

            if ($transactionDateTimestamp > $dateEndTimestamp) {
                continue;
            }
            if ($transactionDateTimestamp < $dateStartTimestamp) {
                continue;
            }
            if ($item['marketplace_id'] !== $marketplaceId) {
                continue;
            }
            if ($item['seller_id'] !== $sellerId) {
                continue;
            }
            $itemsByCategory[] = $item;
        }

        return array_values($itemsByCategory);
    }

    public function makeDuplicate(): ProductCostItem
    {
        $item = new ProductCostItem();
        $item->product_cost_period_id = $this->product_cost_period_id;
        $item->product_cost_category_id =$this->product_cost_category_id;
        $item->amount_total = $this->amount_total;
        $item->amount_per_unit = $this->amount_per_unit;
        $item->marketplace_amount_per_unit = $this->marketplace_amount_per_unit;
        $item->marketplace_currency_id = $this->marketplace_currency_id;
        $item->marketplace_currency_rate = $this->marketplace_currency_rate;
        $item->currency_id = $this->currency_id;
        $item->units = $this->units;
        $item->note = $this->note;
        return $item;
    }

    public function getUniqId(): string
    {
        return implode('_', [
            $this->product_cost_category_id,
            $this->currency_id,
            $this->units,
            round($this->amount_total, 2)
        ]);
    }

    public function recalculateAmounts(): void
    {
        /** @var ProductCostPeriod $period */
        $period = $this->getProductCostPeriod()->one();
        if (!empty($period)) {
            $period->shouldSendCOGChanges = $this->shouldSendCogChanges;
        }

        // Will be filled further with today's currency rate
        if (!empty($period) && $period->isFuturePeriod()) {
            $this->marketplace_currency_rate = null;
            $this->is_currency_rate_recalculated = false;
        }

        if (!empty($period) && $period->sales_category_id === SalesCategory::CATEGORY_EXPENSES_TAXES) {
            $this->marketplace_currency_rate = 1;
        }

        if (empty($this->marketplace_currency_id)) {
            /** @var Product $product */
            $product = $period->getProduct()->one();
            $this->marketplace_currency_id = $product->currency_code;
        }

        if (empty($this->marketplace_currency_rate)) {
            $this->marketplace_currency_rate = $this
                ->currencyRateManager
                ->convert(1, $this->currency_id, $this->getMarketplaceCurrencyId(), new \DateTime());
        }

        $this->amount_per_unit = $this->amount_total / $this->units;
        $this->marketplace_amount_per_unit = $this->amount_per_unit * $this->marketplace_currency_rate;
    }

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }


    public function beforeValidate()
    {
        $this->amount_vat = $this->amount_total;
        return parent::beforeValidate(); // TODO: Change the autogenerated stub
    }

    public function beforeSave($insert)
    {
        $this->recalculateAmounts();
        return parent::beforeSave($insert);
    }

    protected function getMarketplaceCurrencyId(): string
    {
        /** @var ProductCostPeriod $period */
        $period = $this->getProductCostPeriod()->one();
        /** @var Product $product */
        $product = $period->getProduct()->one();
        return $product->currency_code;
    }

    public function beforeDelete()
    {
        /** @var ProductCostPeriod $period */
        $period = $this->getProductCostPeriod()->noCache()->one();
        if (!empty($period)) {
            $period->shouldSendCOGChanges = $this->shouldSendCogChanges;
        }

        if ($period && !$period->isFuturePeriod() && $this->shouldSendCogChanges) {
            /** @var COGChangesManager $COGChangesManager */
            $COGChangesManager = \Yii::$container->get('COGChangesManager');
            /** @var ProcessManager $processManager */
            $processManager = \Yii::$app->processManager;
            $processTraceBuffer = $processManager->getProcessTrace();
            $processManager->appendTrace("Before delete product cost item");

            $COGChangesManager->sendCOGChangesToQueue(
                $this->marketplace_amount_per_unit,
                $this->marketplace_currency_id,
                0,
                $this->marketplace_currency_id,
                $period->date_start ?? '1900-01-01 00:00:00',
                $period->date_end ?? date('Y-m-d H:i:s'),
                $this,
                $period,
                self::$isSyncMode ? 1 : 5
            );
            $processManager->setTrace($processTraceBuffer);
        }

        return parent::beforeDelete();
    }

    public function afterSave($insert, $changedAttributes)
    {
        /** @var ProductCostPeriod $period */
        $period = $this->getProductCostPeriod()->noCache()->one();
        if (!empty($period)) {
            $period->shouldSendCOGChanges = $this->shouldSendCogChanges;
        }

        if (array_key_exists('marketplace_amount_per_unit', $changedAttributes)
            && $period
            && !$period->isFuturePeriod()
            && $this->shouldSendCogChanges
        ) {
            $amountPerUnitOld = array_key_exists('marketplace_amount_per_unit', $changedAttributes)
                ? $changedAttributes['marketplace_amount_per_unit'] ?? 0
                : $this->marketplace_amount_per_unit;
            $amountPerItemNew = $this->marketplace_amount_per_unit;

            /** @var ProcessManager $processManager */
            $processManager = \Yii::$app->processManager;
            $processTraceBuffer = $processManager->getProcessTrace();
            $processManager->appendTrace("After save product cost item");

            /** @var COGChangesManager $COGChangesManager */
            $COGChangesManager = \Yii::$container->get('COGChangesManager');
            $COGChangesManager->sendCOGChangesToQueue(
                $amountPerUnitOld,
                $this->marketplace_currency_id,
                $amountPerItemNew,
                $this->marketplace_currency_id,
                $period->date_start ?? '1900-01-01 00:00:00',
                $period->date_end ?? date('Y-m-d H:i:s'),
                $this,
                $period,
                self::$isSyncMode ? 1 : 5
            );

            $processManager->setTrace($processTraceBuffer);
        }

        if ($period) {
            $period->recalculateTotalAmount();
        }

        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        /** @var ProductCostPeriod $period */
        $period = $this->getProductCostPeriod()->one();

        if (null !== $period) {
            $period->shouldSendCOGChanges = $this->shouldSendCogChanges;
            $period->recalculateTotalAmount();
        }

        parent::afterDelete();
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['default'] = [
            'product_cost_period_id',
            'product_cost_category_id',
            'currency_id',
            'marketplace_currency_rate',
            'units',
            'note',
            'amount_total',
            'amount_vat',
            'units'
        ];
        return $scenarios;
    }

    public function search($params)
    {
        $query = ProductCostItem::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('product_cost_period_id', $this->product_cost_period_id)
            ->andFilterCompare('product_cost_category_id', $this->product_cost_category_id)
            ->andFilterCompare('amount_total', $this->amount_total)
            ->andFilterCompare('currency_id', $this->currency_id)
            ->andFilterCompare('amount_per_unit', $this->amount_per_unit)
            ->andFilterCompare('units', $this->units)
            ->andFilterCompare('note', $this->note, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);

        return $query;
    }

    public function getProductCostPeriod(): ActiveQuery
    {
        return $this->hasOne(ProductCostPeriod::class, ['id' => 'product_cost_period_id']);
    }

    public function getProductCostCategory(): ActiveQuery
    {
        return $this->hasOne(ProductCostCategory::class, ['id' => 'product_cost_category_id']);
    }

    public function attributeLabels()
    {
        $attributeLabels = parent::attributeLabels();
        $attributeLabels['amount_vat'] = \Yii::t('admin', 'VAT (%)');
        $attributeLabels['amount_total'] = \Yii::t('admin', 'Total');
        $attributeLabels['units'] = \Yii::t('admin', 'Quantity');
        $attributeLabels['note'] = \Yii::t('admin', 'Note');

        return $attributeLabels;
    }

    public static function getExistingItem(
        int $productCostPeriodId,
        int $productCostCategoryId
    ): ?self
    {
        return ProductCostItem::find()->where([
            'AND',
            ['=', 'product_cost_period_id', $productCostPeriodId],
            ['=', 'product_cost_category_id', $productCostCategoryId],
        ])
            ->one();
    }

    protected static function getCOGCacheKey(string $sellerSku): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        return implode('_', [
            'product_cost_items_by_seller_sku_v3',
            $dbManager->getCustomerId(),
            $sellerSku
        ]);
    }
}
