<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;

/**
 * This is the model class for table "tag_color".
 * @OA\Schema(
 *     schema="TagColor",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Property(
 *         property="tag_color",
 *         type="string",
 *         description="Tag color (Example #999999)",
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"DEFAULT", "CUSTOM"}
 *     )
 * )
 * @property int $id
 * @property string $type
 * @property string $tag_color
 */
class TagColor extends AbstractCustomerRecord
{
    public const TYPE_DEFAULT = 'DEFAULT';
    public const TYPE_CUSTOM = 'CUSTOM';

    public const TYPES = [
        self::TYPE_DEFAULT,
        self::TYPE_CUSTOM,
    ];

    public function init()
    {
        parent::init();
        if ($this->isNewRecord) {
            $this->type = self::TYPE_CUSTOM;
        }
    }

    public function rules()
    {
        return [
            [['type', 'tag_color'], 'required'],
            [['type'], 'string'],
            [['tag_color'], 'string', 'max' => 10],
            [['type'], 'in', 'range' => self::TYPES]
        ];
    }
}
