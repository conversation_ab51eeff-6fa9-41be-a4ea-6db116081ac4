<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property string $seller_id
 * @property string $marketplace_id
 * @property string $seller_sku
 * @property string|null $asin
 * @property float|null $price
 * @property float|null $estimated_referral_fee_per_item
 * @property string $currency
 * @property string $created_at
 * @property string $date
 */
class ReferralFeePreviewHistory extends AbstractCustomerRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
