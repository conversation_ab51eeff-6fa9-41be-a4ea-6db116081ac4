<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\console\Application;
use yii\db\ActiveQuery;
use yii\db\Expression;

/**
 * This is the model class for table "product_filter_list".
 *
 * @OA\Schema(
 *     schema="ProductFilterList",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Property(
 *         property="customer_id",
 *         type="integer",
 *         description="Customer id",
 *     ),
 *     @OA\Property(
 *         property="user_id",
 *         type="string",
 *         description="User id",
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Name filter list",
 *     ),
 *     @OA\Property(
 *         property="filters",
 *         type="object",
 *         description="Filter list",
 *         @OA\Property(
 *             property="marketplaceId",
 *             type="array",
 *             @OA\Items(type="integer"),
 *             description="List of marketplace IDs",
 *             example={1, 2, 3}
 *         ),
 *         @OA\Property(
 *             property="adult_product",
 *             type="boolean",
 *             description="Whether the product is for adults"
 *         )
 *     )
 * )
 *
 * @property string $id [integer]
 * @property string $user_id [varchar(255)]
 * @property string $customer_id [varchar(255)]
 * @property string $name [varchar(255)]
 * @property string $filters [jsonb]
 * @property int $created_at [timestamp(0)]
 * @property int $updated_at [timestamp(0)]
 */
class ProductFilterList extends AbstractCustomerRecord
{
    public function behaviors(): array
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function getDefaultSort(): array
    {
        return [
            'id' => SORT_DESC,
        ];
    }

    /**
     * @param $params
     * @return ActiveQuery
     */
    public function search($params): ActiveQuery
    {
        $this->setScenario('search');
        $this->setAttributes($params);

        $query = self::find();

        if (!empty($params['name'])) {
            $params['name'] = strtolower($params['name']);
            $query->andFilterWhere(['like', new Expression("LOWER(name)"), $params['name']]);
        }

        return $query;
    }

    public static function find(): ActiveQuery
    {
        $query = parent::find();
        if (!Yii::$app instanceof Application) {
            $query->andWhere(['user_id' => Yii::$app->user->identity->user_id]);
        }

        return $query;
    }

    public function fields()
    {
        return parent::fields();
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();

        return $scenarios;
    }

    public function beforeSave($insert): bool
    {
        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_id', 'user_id', 'name', 'filters'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['name'],
                'unique',
                'targetAttribute' => ['customer_id', 'user_id', 'name'],
                'message' => 'Such name has already been used for a segment'
            ],
            ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => Yii::t('admin', 'ID'),
            'customer_id' => Yii::t('admin', 'Customer ID'),
            'user_id' => Yii::t('admin', 'User ID'),
            'name' => Yii::t('admin', 'Name'),
            'filters' => Yii::t('admin', 'Filters Json'),
        ];
    }
}
