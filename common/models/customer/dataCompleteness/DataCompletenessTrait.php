<?php
namespace common\models\customer\dataCompleteness;

use yii\db\ActiveQuery;

trait DataCompletenessTrait
{
    public function search($params): ActiveQuery
    {
        $parentClass = get_parent_class(static::class);
        $query = $parentClass::search($params);

        if (isset(static::$factorModel)) {
            $query = static::$factorModel::setFilterByQuery($query);
        }
        return $query;
    }

    public static function tableName(){
        $parentClass = get_parent_class(static::class);

        return $parentClass::tableName();
    }
}
