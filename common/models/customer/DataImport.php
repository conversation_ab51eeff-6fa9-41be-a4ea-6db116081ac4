<?php

namespace common\models\customer;

use common\components\core\i18n\I18N;
use common\components\dataImportExport\import\ImportErrorCollapser;
use common\components\dataImportExport\import\validator\ImportFileStructureValidator;
use common\components\dataImportExport\import\validator\ImportUrlFileStructureValidator;
use common\components\dataImportExport\SupportedHandlers;
use common\models\traits\LastId;
use common\models\traits\SaveOrThrowException;
use common\models\validators\SafeS3UrlValidator;
use yii\web\UploadedFile;

/**
 * This is the API model class for table "data_import".
 *
 * @OA\Schema(
 * schema="DataImport",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="handler_name",
 *      type="string",
 *      description="Handler Name"
 *   ),
 *   @OA\Property(
 *      property="status",
 *      type="string",
 *      description="Status"
 *   ),
 *   @OA\Property(
 *      property="exception",
 *      type="string",
 *      description="Exception description"
 *   ),
 *   @OA\Property(
 *      property="count_parts",
 *      type="integer",
 *      description="Count Parts"
 *   ),
 *   @OA\Property(
 *      property="count_all_items",
 *      type="integer",
 *      description="Count All Items"
 *   ),
 *   @OA\Property(
 *      property="count_imported_items",
 *      type="integer",
 *      description="Count Finished Items"
 *   ),
 *   @OA\Property(
 *      property="count_errors",
 *      type="integer",
 *      description="Count Errors"
 *   ),
 *   @OA\Property(
 *      property="file_url",
 *      type="string",
 *      description="File Url"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created At"
 *   ),
 *   @OA\Property(
 *      property="started_at",
 *      type="string",
 *      description="Started At"
 *   ),
 *   @OA\Property(
 *      property="finished_at",
 *      type="string",
 *      description="Finished At"
 *   ),
 *   @OA\Property(
 *      property="updated_at",
 *      type="string",
 *      description="Updated At"
 *   ),
 *   @OA\Property(
 *      property="type",
 *      type="string",
 *      description="Type"
 *   ),
 *   @OA\Property(property="dataImportParts", type="array", @OA\Items(ref="#/components/schemas/DataImportPart")),
 * )

 * @property int              $id
 * @property string           $handler_name
 * @property string           $status
 * @property int              $count_parts
 * @property string           $exception
 * @property string           $log
 * @property int              $count_all_items
 * @property int              $count_imported_items
 * @property int              $count_errors
 * @property string           $errors
 * @property int              $data_start_line_number
 * @property string           $file_url
 * @property string           $created_at
 * @property string           $started_at
 * @property string           $finished_at
 * @property string           $updated_at
 * @property string           $language_code
 * @property string           $type
 * @property int|null         $last_id
 * @property string           $params
 * @property DataImportPart[] $dataImportParts
 */
class DataImport extends BaseDataImportExport
{
    use LastId;
    use SaveOrThrowException;

    public ?UploadedFile $file = null;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['handler_name'], 'required'],
            [['status', 'type', 'params'], 'string'],
            [['count_parts', 'count_all_items', 'count_imported_items', 'count_errors', 'last_id'], 'integer'],
            [['handler_name'], 'in', 'range' => SupportedHandlers::IMPORT_SUPPORTED_HANDLERS, 'when' => function () {
                $this->type !== self::TYPE_BULK_EDIT;
            }],
            [['id', 'handler_name', 'status', 'count_parts', 'count_all_items', 'count_imported_items', 'count_errors', 'file_url', 'created_at', 'started_at', 'finished_at', 'updated_at', 'type'], 'safe', 'on'=>'search'],
            [['file'], 'required', 'when' => function () {
                return empty($this->file_url) && $this->type !== self::TYPE_BULK_EDIT;
            }],
            [['file'], ImportFileStructureValidator::class, 'skipOnEmpty' => true],
            [['file'], 'file', 'maxSize' => 5 * 1024 * 1024, 'extensions' => 'csv,txt', 'checkExtensionByMimeType' => false],
            [['file_url'], 'required', 'when' => function () {
                return empty($this->file) && $this->type !== self::TYPE_BULK_EDIT;
            }],
            [['file_url'], 'url'],
            [['file_url'], ImportUrlFileStructureValidator::class],
            [['file_url'], SafeS3UrlValidator::class],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'handler_name',
            'file',
            'file_url'
        ];
        return $scenarios;
    }

    public function search($params)
    {
        $query = self::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        if ($this->handler_name == null) {
            $this->handler_name = array_merge(SupportedHandlers::IMPORT_SUPPORTED_HANDLERS, [SupportedHandlers::HANDLER_PRODUCT]);
        } else {
            $this->handler_name = [
                $this->handler_name,
                SupportedHandlers::HANDLER_PRODUCT
            ];
        }

        // grid filtering conditions
        $query
            ->andFilterCompare('id', $this->id, '=', 'int')
            ->andFilterCompare('type', $this->type)
            ->andFilterCompare('status', $this->status)
            ->andFilterCompare('count_all_items', $this->count_all_items, '=', 'int')
            ->andFilterCompare('count_errors',  $this->count_errors, '=', 'int')
            ->andFilterCompare('count_imported_items',  $this->count_imported_items, '=', 'int')
        ;

        $query->andFilterWhere(['in', 'handler_name', $this->handler_name]);

        $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $this->applyBetweenDateFilter($query, 'started_at', $this->started_at);
        $this->applyBetweenDateFilter($query, 'finished_at', $this->finished_at);
        $this->applyBetweenDateFilter($query, 'updated_at', $this->updated_at);

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function fields()
    {
        /** @var I18N $i18n */
        $i18n = \Yii::$app->i18n;

        $fields = parent::fields();
        unset($fields['exception']);
        $fields['errors'] = function () use ($i18n) {
            if (empty($this->errors)) {
                return [];
            }

            $errors = [];
            foreach ($this->errors as $error) {
                if (empty($error['errorUntranslated'])) {
                    unset($error['errorUntranslated']);
                    $errors[] = $error;
                    continue;
                }

                $error['error'] = $i18n->translateFuture($error['errorUntranslated']);
                unset($error['errorUntranslated']);
                $errors[] = $error;
            }
            return $errors;
        };
        return $fields;
    }

    public function recalculateStats()
    {
        /** @var DataImportPart[] $dataImportParts */
        $dataImportParts = DataImportPart::find()->where(['data_import_id' => $this->id])->all();

        $this->count_imported_items = 0;
        $this->count_errors = 0;
        $this->errors = [];

        $importErrorCollapser = new ImportErrorCollapser();

        foreach ($dataImportParts as $dataImportPart) {
            $this->count_errors += $dataImportPart->count_errors;
            $this->count_imported_items += $dataImportPart->count_imported_items;
            $this->errors = $importErrorCollapser
                ->collapse(
                    array_merge(
                        $this->errors,
                        $dataImportPart->errors
                    )
                );
        }

        $this->save(false);
    }

    public function getS3Folder(): string
    {
        return $this->tableName() . '/' . $this->id;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDataImportParts()
    {
        return $this->hasMany(DataImportPart::class, ['data_import_id' => 'id']);
    }

    public function getLanguageCode(): string
    {
        return $this->language_code ?? 'en';
    }

    /**
     * @return bool
     */
    public function isStatusTerminated(): bool
    {
        return $this->status === self::STATUS_TERMINATED;
    }

    /**
     * @return bool
     */
    public function canStopProcess(): bool
    {
        return $this->status === self::STATUS_IN_PROGRESS || $this->status === self::STATUS_NEW;
    }
}
