<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property string $marketplace_id
 * @property string $seller_id
 * @property string $sku
 * @property string $currency
 * @property float $sales_price
 * @property float $estimated_referral_fee_per_unit
 * @property float $estimated_variable_closing_fee
 * @property float $estimated_fixed_closing_fee
 * @property float $expected_domestic_fulfilment_fee_per_unit
 * @property string $created_at
 * @property string $date
 */
class FbaEstimatedFeeHistory extends AbstractCustomerRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
