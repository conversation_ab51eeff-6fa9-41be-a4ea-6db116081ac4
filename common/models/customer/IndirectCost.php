<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\validators\CronExpressionValidator;
use Cron\CronExpression;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
* This is the API model class for table "customer_00019.indirect_cost".
*
* @OA\Schema(
* schema="IndirectCost",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="indirect_cost_type_id",
*      type="integer",
*      description="Indirect Cost Type ID"
*   ),
*   @OA\Property(
*      property="product_id",
*      type="int|null",
*      description="Product ID"
*   ),
*   @OA\Property(
*      property="amount",
*      type="float",
*      description="Amount"
*   ),
*   @OA\Property(
*      property="currency_id",
*      type="string",
*      description="Currency ID"
*   ),
*   @OA\Property(
*      property="cron_expr",
*      type="string|null",
*      description="Cron Expr"
*   ),
*   @OA\Property(
*      property="max_iterations",
*      type="int|null",
*      description="Max Iterations"
*   ),
*   @OA\Property(
*      property="date_start",
*      type="string|null",
*      description="Date Start"
*   ),
*   @OA\Property(
*      property="date_end",
*      type="string|null",
*      description="Date End"
*   ),
*   @OA\Property(
*      property="last_apply_date",
*      type="string|null",
*      description="Last Apply Date"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property int $indirect_cost_type_id
* @property int|null $product_id
* @property float $amount
* @property ?string $marketplace_id
* @property ?string $seller_id
* @property string $currency_id
* @property string|null $cron_expr
* @property int|null $count_iterations
* @property int|null $max_iterations
* @property string|null $date_start
* @property string|null $date_end
* @property string|null $last_apply_date
* @property string $created_at
* @property string $updated_at
*/
class IndirectCost extends AbstractCustomerRecord
{
    use ExtraFiltersTrait;

    public const FREQUENCY_ONE_TIME = 'one_time';
    public const FREQUENCY_RECURRING = 'recurring';

    public ?string $product_asin = null;
    public ?string $product_sku = null;
    public ?string $product_title = null;
    public ?string $product_stock_type = null;
    public ?string $product_condition = null;
    public ?string $indirect_cost_type_name = null;
    public ?bool $indirect_cost_type_is_predefined = null;
    public ?string $frequency = null;

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function search($params)
    {
        $query = IndirectCost::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        $query->addSelect([
            'indirect_cost.id',
            'amount',
            'currency_id',
            'cron_expr',
            'max_iterations',
            'date_start',
            'date_end',
            'product_id',
            'indirect_cost.marketplace_id',
            'indirect_cost.seller_id',
            'product.title as product_title',
            'product.asin as product_asin',
            'product.sku as product_sku',
            'product.stock_type as product_stock_type',
            'product.condition as product_condition',
            'indirect_cost_type.name as indirect_cost_type_name',
            'indirect_cost_type.is_predefined as indirect_cost_type_is_predefined',
            'indirect_cost_type_id'
        ]);

        if ($this->seller_id === 'global') {
            $this->seller_id = 'n/a';
        }

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('indirect_cost_type_id', $this->indirect_cost_type_id)
            ->andFilterCompare('indirect_cost_type.name', $this->indirect_cost_type_name, 'like')
            ->andFilterCompare('product.id', $this->product_id)
            ->andFilterCompare('product.title', $this->product_title, 'like')
            ->andFilterCompare('product.asin', $this->product_asin, 'like')
            ->andFilterCompare('product.condition', $this->product_condition)
            ->andFilterCompare('product.sku', $this->product_sku, 'like')
            ->andFilterCompare('product.stock_type', $this->product_stock_type)
            ->andFilterCompare('amount', $this->amount, 'like')
            ->andFilterCompare('cron_expr', $this->cron_expr, 'like')
            ->andFilterCompare('currency_id', $this->currency_id)
            ->andFilterCompare('max_iterations', $this->max_iterations, 'like')
            ->andFilterCompare('updated_at', $this->updated_at)
            ->andFilterCompare('indirect_cost.marketplace_id', $this->marketplace_id)
            ->andFilterCompare('indirect_cost.seller_id', $this->seller_id)
        ;

        $this->applyBetweenDateFilter($query, 'date_start', $this->date_start);
        $this->applyBetweenDateFilter($query, 'date_end', $this->date_end);
        $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $this->applyExpiredOrInactiveSubscriptionLogic($query, 'indirect_cost.seller_id');

        if (isset($params['frequency']) && $params['frequency'] === self::FREQUENCY_ONE_TIME) {
            $query->andFilterWhere(['is', 'cron_expr', new Expression('NULL')]);
        }

        if (isset($params['frequency']) && $params['frequency'] === self::FREQUENCY_RECURRING) {
            $query->andFilterWhere(['is not', 'cron_expr', new Expression('NULL')]);
        }

        $query->joinWith('product');
        $query->joinWith('indirectCostType');

        return $query;
    }

    public function afterDelete()
    {
        /** @var DbManager $dbManger */
        $dbManger = \Yii::$app->dbManager;
        $messagesSender = new MessagesSender();
        $messagesSender->indirectCostChanges(
            $dbManger->getCustomerId(),
            $this->id,
            false,
            true
        );
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert
            || array_key_exists('amount', $changedAttributes)
            || array_key_exists('currency_id', $changedAttributes)
            || array_key_exists('date_start', $changedAttributes)
            || array_key_exists('date_end', $changedAttributes)
            || array_key_exists('cron_expr', $changedAttributes)
            || array_key_exists('product_id', $changedAttributes)
            || array_key_exists('seller_id', $changedAttributes)
            || array_key_exists('marketplace_id', $changedAttributes)
            || array_key_exists('indirect_cost_type_id', $changedAttributes)
        ) {
            /** @var DbManager $dbManger */
            $dbManger = \Yii::$app->dbManager;
            $messagesSender = new MessagesSender();
            $messagesSender->indirectCostChanges($dbManger->getCustomerId(), $this->id);
        }

        parent::afterSave($insert, $changedAttributes);
    }

    public function getDefaultSort()
    {
        return [
            'id' => SORT_DESC,
        ];
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['indirect_cost_type_id', 'amount', 'currency_id', 'seller_id', 'date_start'], 'required'],
            [['product_id', 'max_iterations'], 'default', 'value' => null],
            [['marketplace_id'], 'string'],
            [['indirect_cost_type_id', 'product_id', 'max_iterations'], 'integer'],
            [['amount'], 'number'],
            [['date_end', 'date_start'], function ($attribute) {
                if (!empty($this->$attribute) && false === strpos($this->$attribute, ':')) {
                    $this->$attribute .= ' 00:00:00';
                }
            }],
            [['date_end', 'date_start'], 'datetime', 'format' => 'php:Y-m-d H:i:s', 'skipOnEmpty' => true],
            [['currency_id', 'cron_expr'], 'string', 'max' => 255],
            [['max_iterations'], 'compare', 'compareValue' => 0, 'operator' => '>'],
            [['max_iterations'], 'compare', 'compareValue' => 1000, 'operator' => '<='],
            [['cron_expr'], function() {
                $parts = explode(' ', $this->cron_expr);

                // Minutes and hours are not allowed to manage
                $parts[0] = '00';
                $parts[1] = '00';
                $this->cron_expr = implode(' ', $parts);
            }],
            [['cron_expr'], CronExpressionValidator::class],
            [['indirect_cost_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => IndirectCostType::class, 'targetAttribute' => ['indirect_cost_type_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['id', 'frequency', 'product_asin', 'marketplace_id', 'seller_id', 'indirect_cost_type_id', 'product_id', 'amount', 'currency_id', 'cron_expr', 'date_start', 'date_end', 'max_iterations', 'date_start', 'date_end', 'last_apply_date', 'created_at', 'updated_at', 'marketplace_id', 'seller_id', 'product_title', 'product_sku', 'product_stock_type'],'safe','on'=>'search'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = [
            'indirect_cost_type_id',
            'product_id',
            'marketplace_id',
            'seller_id',
            'amount',
            'currency_id',
            'cron_expr',
            'max_iterations',
            'date_start',
            'date_end'
        ];
        $scenarios[self::SCENARIO_UPDATE] = $scenarios[self::SCENARIO_CREATE];
        return $scenarios;
    }

    public function beforeValidate()
    {
        if (!empty($this->product_id)) {
            $product = Product::findOne($this->product_id);
            $this->marketplace_id = $product->marketplace_id;
            $this->seller_id = $product->seller_id;
        }

        return true;
    }

    public function beforeSave($insert)
    {
        if ($this->seller_id == 'global') {
            $this->marketplace_id = null;
            $this->seller_id = null;
        }

        if (empty($this->cron_expr)) {
            if (empty($this->date_start)) {
                $this->date_start = date('Y-m-d 00:00:00');
            }

            $this->date_end = $this->date_start;
            $this->max_iterations = null;
        }

        if (!empty($this->date_start)) {
            $this->date_start = (new \DateTime($this->date_start))->format('Y-m-d 00:00:00');
        }

        if (!empty($this->date_end)) {
            $this->date_end = (new \DateTime($this->date_end))->format('Y-m-d 00:00:00');
        }

        if (!empty($this->cron_expr)) {
            if ($this->isValueChanged('max_iterations')) {
                if (empty($this->max_iterations)) {
                    $this->date_end = null;
                } else {
                    $nextRun = new \DateTime($this->date_start);
                    $cronExpr = str_replace('?', '*', $this->cron_expr);
                    $expression = new CronExpression($cronExpr);

                    for ($i = 0; $i < $this->max_iterations; $i++) {
                        $nextRun = $expression->getNextRunDate($nextRun, 0, true);
                        $nextRun->setTime(23, 59);
                    }
                    $nextRun->setTime(0, 0);
                    $this->date_end = $nextRun->format('Y-m-d H:i:s');
                }
            } else if ($this->isValueChanged('date_end')) {
                if (empty($this->date_end)) {
                    $this->max_iterations = null;
                } else if (!empty($this->max_iterations)) {
                    $nextRun = new \DateTime($this->date_start);
                    $cronExpr = str_replace('?', '*', $this->cron_expr);
                    $expression = new CronExpression($cronExpr);
                    $dateEnd = new \DateTime($this->date_end);

                    for ($i = 0; $i < 10000; $i++) {
                        $nextRun = $expression->getNextRunDate($nextRun, 0, true);
                        $nextRun->setTime(23, 59);

                        if ($nextRun > $dateEnd) {
                            $this->max_iterations = $i + 1;
                            break;
                        }
                    }
                }
            }
        }

        return parent::beforeSave($insert);
    }

    public function fields()
    {
        $fields = parent::fields();
        unset($fields['created_at']);
        unset($fields['updated_at']);
        unset($fields['product_id']);
        unset($fields['indirect_cost_type_id']);
        unset($fields['last_apply_date']);

        $fields['product_id'] = function() {
            return $this->product_id ?? null;
        };
        $fields['product_title'] = function() {
            return $this->product_title ?? null;
        };
        $fields['product_asin'] = function() {
            return $this->product_asin ?? null;
        };
        $fields['product_sku'] = function() {
            return $this->product_sku ?? null;
        };
        $fields['date_start'] = function () {
            if (empty($this->date_start)) {
                return null;
            }
            return date('Y-m-d', strtotime($this->date_start));
        };
        $fields['date_end'] = function () {
            if (empty($this->date_end)) {
                return null;
            }
            return date('Y-m-d', strtotime($this->date_end));
        };
        $fields['product_stock_type'] = function() {
            return $this->product_stock_type ?? null;
        };
        $fields['product_condition'] = function() {
            return $this->product_condition ?? null;
        };
        $fields['indirect_cost_type_id'] = function() {
            return $this->indirect_cost_type_id ?? null;
        };
        $fields['indirect_cost_type_name'] = function() {
            return $this->indirect_cost_type_name ?? null;
        };
        $fields['indirect_cost_type_is_predefined'] = function() {
            return $this->indirect_cost_type_is_predefined ?? null;
        };
        $fields['frequency'] = function() {
            return empty($this->cron_expr)
                ? self::FREQUENCY_ONE_TIME
                : self::FREQUENCY_RECURRING;
        };
        return $fields;
    }

    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    public function getIndirectCostType()
    {
        return $this->hasOne(IndirectCostType::class, ['id' => 'indirect_cost_type_id']);
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'marketplace_id' => \Yii::t('admin', 'Marketplace'),
            'seller_id' => \Yii::t('admin', 'Amazon account name'),
            'amount' => \Yii::t('admin', 'Amount'),
            'date_start' => \Yii::t('admin', 'Start date'),
            'max_iterations' => \Yii::t('admin', 'Iterations'),
            'indirect_cost_type_id' => \Yii::t('admin', 'Cost type')
        ];
    }
}
