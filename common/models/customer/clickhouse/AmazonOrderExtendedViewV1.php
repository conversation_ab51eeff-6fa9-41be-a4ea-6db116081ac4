<?php

namespace common\models\customer\clickhouse;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\ExtendedQuery;
use common\components\services\order\TransferOrderService;
use common\models\AmazonMarketplace;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

class AmazonOrderExtendedViewV1 extends AmazonOrderExtendedView implements AmazonOrderExtendedViewInterface
{
}
