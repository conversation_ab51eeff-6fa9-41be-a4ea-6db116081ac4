<?php

namespace common\models\customer\clickhouse;

use Exception;
use yii\db\ActiveQuery;
use api\modules\v1\enums\RefundReasons;
use common\components\helpers\ParamsHelper;
use common\components\currencyRate\CurrencyRateManager;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;

/**
 * @OA\Schema(
 *     schema="ProductAggregatedRefund",
 *     type="object",
 *     description="Aggregated sales details of product",
 *     @OA\Property(
 *         property="seller_sku",
 *         type="string",
 *         description="Stock Keeping Unit",
 *         example="fbm1600105204"
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         description="Marketplace ID",
 *         example="A1PA6795UKMFR9"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller ID",
 *         example="A2WY0S4JX228MI"
 *     ),
 *     @OA\Property(
 *         property="asin",
 *         type="string",
 *         description="Amazon Standard Identification Number",
 *         example="B07486PRYV"
 *     ),
 *     @OA\Property(
 *         property="brand",
 *         type="string",
 *         description="Brand of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="ean",
 *         type="string",
 *         description="European Article Number",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="upc",
 *         type="string",
 *         description="Universal Product Code",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="isbn",
 *         type="string",
 *         description="International Standard Book Number",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the product",
 *         example="Panama Jack Damen Felia Igloo Travelling Halblange Stiefel, Schwarz, 39 EU"
 *     ),
 *     @OA\Property(
 *         property="manufacturer",
 *         type="string",
 *         description="Manufacturer of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="product_type",
 *         type="string",
 *         description="Type of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="offer_type",
 *         type="string",
 *         description="B2B",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="parent_asin",
 *         type="string",
 *         description="Parent ASIN",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="stock_type",
 *         type="string",
 *         description="Stock type (e.g., FBA, FBM)",
 *         example="FBM"
 *     ),
 *     @OA\Property(
 *         property="adult_product",
 *         type="boolean",
 *         description="Indicates if the product is for adults",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="tag_id",
 *         type="string",
 *         description="Tag ID"
 *     ),
 *     @OA\Property(
 *         property="revenue_amount",
 *         type="number",
 *         format="float",
 *         description="Revenue amount",
 *         example=22.22
 *     ),
 *     @OA\Property(
 *         property="expenses_amount",
 *         type="number",
 *         format="float",
 *         description="Expenses amount",
 *         example=223.22
 *     ),
 *     @OA\Property(
 *         property="total_income_amount",
 *         type="number",
 *         format="float",
 *         description="Total income amount",
 *         example=22.44
 *     ),
 *     @OA\Property(
 *         property="total_expenses_amount",
 *         type="number",
 *         format="float",
 *         description="Total expenses amount",
 *         example=111.22
 *     ),
 *     @OA\Property(
 *         property="estimated_profit_amount",
 *         type="number",
 *         format="float",
 *         description="Estimated profit amount",
 *         example=23.22
 *     ),
 *     @OA\Property(
 *         property="promotion_amount",
 *         type="number",
 *         format="float",
 *         description="Promotion amount",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="refunds",
 *         type="integer",
 *         description="Number of refunds",
 *         example=24
 *     ),
 *     @OA\Property(
 *         property="orders",
 *         type="integer",
 *         description="Number of orders for which the product was returned",
 *         example=24
 *     ),
 *     @OA\Property(
 *         property="units",
 *         type="integer",
 *         description="Number of units",
 *         example=20
 *     ),
 *     @OA\Property(
 *         property="return_reason",
 *         type="string",
 *         description="Reason for return",
 *         example="AMZ-PG-APP-TOO-SMALL"
 *     ),
 *     @OA\Property(
 *         property="roi",
 *         type="number",
 *         format="float",
 *         description="Return on investment",
 *         example=46.44
 *     ),
 *     @OA\Property(
 *         property="margin",
 *         type="number",
 *         format="float",
 *         description="Profit margin",
 *         example=2.22
 *     ),
 *     @OA\Property(
 *         property="comments_count",
 *         type="number",
 *         format="integer",
 *         description="Comments Count",
 *         example=5
 *     ),
 *     @OA\Property(
 *         property="return_reason_count",
 *         type="number",
 *         format="integer",
 *         description="Unique reasons count",
 *         example=5
 *     ),
 *     @OA\Property(
 *         property="return_reasons",
 *         type="array",
 *         @OA\Items(type="string", example="PENDING")
 *     )
 * )
 */
class ProductAggregatedRefund extends AbstractClickhouseCustomerRecord
{
    use ExtraFiltersTrait;

    public static function find(string $currencyId = null, ?string $salesCategoryStrategyType = SalesCategoryStrategyFactory::DEFAULT_STRATEGY): ActiveQuery
    {
        if (empty($currencyId)) {
            $currencyId =  CurrencyRateManager::BASE_CURRENCY;
        }

        $currencyRateExpr = "dictGetOrNull(
            default.currency_rate_dict, 
            'value', 
            tuple(toDate(refund_date), :currency_id)
        )";

        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($salesCategoryStrategyType);

        return $salesCategoryStrategy
            ->getAmazonOrderExtendedViewQuery()
            ->alias('o')
            ->select([
                'o.seller_sku',
                'o.marketplace_id',
                'o.seller_id',
                "max(nullif(o.product_asin, '')) as asin",
                'any(product_brand) as brand',
                'any(product_ean) as ean',
                'any(product_upc) as upc',
                'any(product_isbn) as isbn',
                'any(product_title) as title',
                'any(product_manufacturer) as manufacturer',
                'any(product_type) as product_type',
                'any(offer_type) as offer_type',
                'any(product_parent_asin) as parent_asin',
                'any(product_stock_type) as stock_type',
                'any(product_adult) as adult_product',
                "ROUND(SUM(
                    IF(
                        revenue_amount_eur >= 0,
                        (revenue_amount_eur) * $currencyRateExpr,
                        0
                    )
                ),2) as revenue_amount",
                "ROUND(SUM(
                    IF(
                        expenses_amount_eur <= 0,
                        (expenses_amount_eur) * $currencyRateExpr,
                        0
                    )
                ),2) as expenses_amount",
                "ROUND(SUM(
                    IF(
                        total_income_amount_eur >= 0,
                        (total_income_amount_eur) * $currencyRateExpr,
                        0
                    )
                ),2) as total_income_amount",
                "ROUND(SUM(
                    IF(
                        expenses_total_amount_eur <= 0,
                        (expenses_total_amount_eur) * $currencyRateExpr,
                        0
                    )
                ),2) as total_expenses_amount",
                "ROUND(SUM(
                    (estimated_profit_amount_eur) * $currencyRateExpr
                ),2) as estimated_profit_amount",
                "ROUND(SUM(
                    (promotion_amount_eur) * $currencyRateExpr
                ),2) as promotion_amount",
                "ROUND(IF (
                    expenses_amount = 0,
                    (estimated_profit_amount / 0.1) * 100,
                    (estimated_profit_amount / abs(expenses_amount)) * 100
                ),2) as roi",
                "ROUND(IF (
                    revenue_amount = 0,
                    null,
                    greatest(-100, least(100, round((estimated_profit_amount / abs(revenue_amount)) * 100, 2)))
                ),2) as margin",
                "COUNT(CASE WHEN refund_comments <> '' THEN 1 END) AS comments_count",
                'countDistinct(return_reason) AS return_reason_count',
                // Total count of units
                'sumIf(quantity, coalesce(quantity_refunded, 0) > 0) AS units',
                // Count of the refunded units
                'SUM(quantity_refunded) AS refunds',
                // Count of order items have at least one refunded unit
                'countIf(order_item_id, coalesce(quantity_refunded, 0) > 0) AS orders',
            ])
            ->params([
                ':currency_id' => $currencyId,
            ])
            ->andWhere('refund_date is not null')
            ->groupBy('marketplace_id, seller_id, seller_sku')
            ->asArray();
    }

    /**
     * @throws Exception
     */
    public function search($params): ActiveQuery
    {
        $params = ParamsHelper::unifyParams($params);

        $salesCategoryStrategy = $params['sales_category_strategy'] ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY;

        $query = self::find($params['currency_id'] ?? null, $salesCategoryStrategy)
            ->andFilterWhere(['in', 'return_reason', $this->resolveReasons($params)]);

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplaceSellerIds'] ?? '',
            $params['marketplaceId'] ?? '',
            $params['sellerId'] ?? '',
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query);

        $this->applyBetweenDateFilter($query, 'refund_date', $params['refund_date'] ?? null);
        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'ean' => 'product_ean',
                'upc' => 'product_upc',
                'sku' => 'seller_sku',
                'asin' => 'product_asin',
                'isbn' => 'product_isbn',
                'brand' => 'product_brand',
                'manufacturer' => 'product_manufacturer',
                'product_type' => 'product_type',
                'offer_type' => 'offer_type',
                'parent_asin' => 'product_parent_asin',
                'title' => 'product_title',
                'catalog_product_name' => 'catalog_product_name',
                'stock_type' => 'product_stock_type',
                'marketplace_id' => 'marketplace_id',
            ]
        );

        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'revenue_amount' => 'revenue_amount',
                'expenses_amount' => 'expenses_amount',
                'estimated_profit_amount' => 'estimated_profit_amount',
                'units' => 'units',
                'promotion_amount' => 'promotion_amount',
                'refunds' => 'refunds',
                'margin' => 'margin',
                'roi' => 'roi',
                'orders' => 'orders',
            ],
            true
        );

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function getSortAttributes():array
    {
        return [
            'roi',
            'ean',
            'brand',
            'title',
            'units',
            'margin',
            'refunds',
            'stock_type',
            'revenue_amount',
            'comments_count',
            'marketplace_id',
            'expenses_amount',
            'promotion_amount',
            'total_income_amount',
            'return_reason_count',
            'orders',
            'total_expenses_amount',
            'estimated_profit_amount',
        ];
    }

    public function getDefaultSort(): array
    {
        return [
            'refunds' => SORT_DESC,
        ];
    }

    public function resolveReasons(array $params): ?array
    {
        try {
            $reasons = array_filter(explode(',', $params['return_reason']));

            return RefundReasons::withAliases(...$reasons);
        } catch (Exception $e) {
            return null;
        }
    }
}
