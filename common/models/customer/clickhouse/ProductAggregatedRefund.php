<?php

namespace common\models\customer\clickhouse;

use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use yii\db\ActiveQuery;

/**
 * @OA\Schema(
 *     schema="ProductAggregatedRefund",
 *     type="object",
 *     description="Aggregated sales details of product",
 *     @OA\Property(
 *         property="seller_sku",
 *         type="string",
 *         description="Stock Keeping Unit",
 *         example="fbm1600105204"
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         description="Marketplace ID",
 *         example="A1PA6795UKMFR9"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller ID",
 *         example="A2WY0S4JX228MI"
 *     ),
 *     @OA\Property(
 *         property="asin",
 *         type="string",
 *         description="Amazon Standard Identification Number",
 *         example="B07486PRYV"
 *     ),
 *     @OA\Property(
 *         property="brand",
 *         type="string",
 *         description="Brand of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="ean",
 *         type="string",
 *         description="European Article Number",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="upc",
 *         type="string",
 *         description="Universal Product Code",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="isbn",
 *         type="string",
 *         description="International Standard Book Number",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the product",
 *         example="Panama Jack Damen Felia Igloo Travelling Halblange Stiefel, Schwarz, 39 EU"
 *     ),
 *     @OA\Property(
 *         property="manufacturer",
 *         type="string",
 *         description="Manufacturer of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="product_type",
 *         type="string",
 *         description="Type of the product",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="parent_asin",
 *         type="string",
 *         description="Parent ASIN",
 *         example=""
 *     ),
 *     @OA\Property(
 *         property="stock_type",
 *         type="string",
 *         description="Stock type (e.g., FBA, FBM)",
 *         example="FBM"
 *     ),
 *     @OA\Property(
 *         property="adult_product",
 *         type="boolean",
 *         description="Indicates if the product is for adults",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="tag_id",
 *         type="string",
 *         description="Tag ID"
 *     ),
 *     @OA\Property(
 *         property="revenue_amount",
 *         type="number",
 *         format="float",
 *         description="Revenue amount",
 *         example=0.47142300000000004
 *     ),
 *     @OA\Property(
 *         property="expenses_amount",
 *         type="number",
 *         format="float",
 *         description="Expenses amount",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="estimated_profit_amount",
 *         type="number",
 *         format="float",
 *         description="Estimated profit amount",
 *         example=0.000468
 *     ),
 *     @OA\Property(
 *         property="promotion_amount",
 *         type="number",
 *         format="float",
 *         description="Promotion amount",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="refunds",
 *         type="integer",
 *         description="Number of refunds",
 *         example=24
 *     ),
 *     @OA\Property(
 *         property="units",
 *         type="integer",
 *         description="Number of units",
 *         example=20
 *     ),
 *     @OA\Property(
 *         property="return_reason",
 *         type="string",
 *         description="Reason for return",
 *         example="AMZ-PG-APP-TOO-SMALL"
 *     ),
 *     @OA\Property(
 *         property="roi",
 *         type="number",
 *         format="float",
 *         description="Return on investment",
 *         example=0.4679999999999999
 *     ),
 *     @OA\Property(
 *         property="margin",
 *         type="number",
 *         format="float",
 *         description="Profit margin",
 *         example=0.09927390050973328
 *     )
 * )
 */
class ProductAggregatedRefund extends AbstractClickhouseCustomerRecord
{
    use ExtraFiltersTrait;

    public static function find(string $currencyId = null, ?string $salesCategoryStrategy = SalesCategoryStrategyFactory::DEFAULT_STRATEGY): ActiveQuery
    {
        $moneyAccuracy = (new CustomerComponent())->getMoneyAccuracy();

        if (empty($currencyId)) {
            $currencyId =  CurrencyRateManager::BASE_CURRENCY;
        }

        $currencyRateExpr = "dictGetOrNull(
            default.currency_rate_dict, 
            'value', 
            tuple(toDate(order_purchase_date), :currency_id)
        )";

        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($salesCategoryStrategy);

        $query = $salesCategoryStrategy->getAmazonOrderExtendedViewQuery();

        $query = $query
            ->select([
                'seller_sku',
                'marketplace_id',
                'seller_id',
                'any(product_asin) as asin',
                'any(product_brand) as brand',
                'any(product_ean) as ean',
                'any(product_upc) as upc',
                'any(product_isbn) as isbn',
                'any(product_title) as title',
                'any(product_manufacturer) as manufacturer',
                'any(product_type) as product_type',
                'any(product_parent_asin) as parent_asin',
                'any(product_stock_type) as stock_type',
                'any(product_adult) as adult_product',
                'tag_id',
                "SUM(
                    IF(
                        revenue_amount_eur >= 0,
                        (revenue_amount_eur / $moneyAccuracy) * $currencyRateExpr,
                        0
                    )
                ) as revenue_amount",
                "SUM(
                    IF(
                        expenses_amount_eur >= 0,
                        (expenses_amount_eur / $moneyAccuracy) * $currencyRateExpr,
                        0
                    )
                ) as expenses_amount",
                "SUM(
                    IF(
                        estimated_profit_amount_eur >= 0,
                        (estimated_profit_amount_eur / $moneyAccuracy) * $currencyRateExpr,
                        0
                    )
                ) as estimated_profit_amount",
                "SUM(
                    IF(
                        promotion_amount_eur >= 0,
                        (promotion_amount_eur / $moneyAccuracy) * $currencyRateExpr,
                        0
                    )
                ) as promotion_amount",
                'COUNT(order_id) AS refunds',
                'SUM(quantity_refunded) AS units',
                'return_reason',
                "IF (
                    expenses_amount = 0,
                    (estimated_profit_amount / 0.1) * 100,
                    (estimated_profit_amount / abs(expenses_amount)) * 100
                ) as roi",
                "IF (
                    revenue_amount = 0,
                    null,
                    greatest(-100, least(100, round((estimated_profit_amount / abs(revenue_amount)) * 100, 2)))
                ) as margin",
            ])
            ->params([
                ':currency_id' => $currencyId,
            ])
            ->groupBy('marketplace_id, seller_id, seller_sku, return_reason, tag_id')
            ->asArray()
        ;

        return $query;
    }

    public function search($params): ActiveQuery
    {
        $salesCategoryStrategy = $params['sales_category_strategy'] ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY;
        $query = self::find($params['currency_id'] ?? null, $salesCategoryStrategy);

        if (!empty($params['refund_reason'])) {
            $reasons = array_filter(explode(',', $params['refund_reason']));
            $query->andFilterWhere(['in', 'return_reason', $reasons]);
        } else {
            $query->andWhere(['AND', ['!=', 'return_reason', ''], ['IS NOT', 'return_reason', null]]);
        }

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplaceSellerIds'] ?? '',
            $params['marketplaceId'] ?? '',
            $params['sellerId'] ?? '',
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query, 'seller_id');
        $this->applyDateStartDateEndFilter(
            $query,
            $params['dateStart'] ?? null,
            $params['dateEnd'] ?? null,
            'order_purchase_date'
        );
        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'ean' => 'product_ean',
                'upc' => 'product_upc',
                'sku' => 'seller_sku',
                'asin' => 'product_asin',
                'isbn' => 'product_isbn',
                'brand' => 'product_brand',
                'manufacturer' => 'product_manufacturer',
                'product_type' => 'product_type',
                'parent_asin' => 'product_parent_asin',
                'title' => 'product_title',
                'catalog_product_name' => 'catalog_product_name',
                'stock_type' => 'product_stock_type',
                'tag_id' => 'tag_id'
            ]
        );

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function getSortAttributes()
    {
        return [
            'refunds',
            'estimated_profit_amount',
            'revenue_amount',
            'expenses_amount',
            'roi',
            'margin',
        ];
    }

    public function getDefaultSort()
    {
        return [
            'refunds' => SORT_DESC,
        ];
    }
}
