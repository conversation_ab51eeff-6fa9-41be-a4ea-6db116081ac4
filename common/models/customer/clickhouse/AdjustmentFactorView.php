<?php

namespace common\models\customer\clickhouse;

use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\ExtendedQuery;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use common\models\customer\SalesCategoryExtendedView;
use common\models\customer\TransactionExtendedView;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

/**
 * @OA\Schema(
 *     schema="AdjustmentFactorViewItem",
 *     type="object",
 *     @OA\Property(
 *         property="transaction_date",
 *         type="string",
 *         format="date-time",
 *         description="Date and time the record was posted",
 *         example="2024-02-16 23:52:12"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller identifier",
 *         example="A2WY0S4JX228MI"
 *     ),
 *     @OA\Property(property="sales_category", type="object", ref="#/components/schemas/SalesCategory"),
 *     @OA\Property(
 *             property="value",
 *             type="float",
 *             example="9.88"
 *     ),
 *     @OA\Property(
 *             property="currency_code",
 *             type="string",
 *             example="EUR"
 *     ),
 * )
 */
class AdjustmentFactorView extends AbstractClickhouseCustomerRecord
{
    protected ?string $sales_category = null;
    protected ?string $sales_category_id = null;
    protected ?float $value = null;

    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        return $dbManager->getClickhouseCustomerDb();
    }

    public static function tableName(): string
    {
        return TransactionExtendedView::tableName();
    }

    public function search($params)
    {
        $query = self::find();
        $this->setScenario('search');
        $this->setAttributes($params);

        $moneyAccuracy =  \Yii::$app->customerComponent->getMoneyAccuracy();

        $query
            ->select([
                'transaction_date',
                'seller_id',
                new Expression("
                COALESCE(
                    sales_category_depth_3, 
                    sales_category_depth_2, 
                    sales_category_depth_1
                ) AS sales_category_id"),
                new Expression("
                CASE WHEN amount IS NOT NULL
                    THEN round(amount / $moneyAccuracy, 2)
                    ELSE NULL
                END as value,
                currency_code
            ")]);

        $query->where(['transaction_type' => 'adjustment'])
            ->andWhere(['!=', 'sales_category_depth_1', 'ignored']);

        return $query;
    }

    public function fields()
    {
        $fields = [
            'transaction_date',
            'seller_id',
            'value',
            'currency_code',
            'sales_category' => function() {
                return $this->getSalesCategoryById($this->sales_category_id);
            }
        ];

        return $fields;
    }

    protected static function getActiveQueryInstance(): ActiveQuery
    {
        $extendedQuery = new ExtendedQuery(get_called_class());
        $extendedQuery->setDbEngine(ExtendedQuery::ENGINE_CLICKHOUSE);
        return $extendedQuery;
    }

    public function rules()
    {
        return [
            [['transaction_date', 'seller_id', 'sales_category'], 'safe', 'on' => 'search'],
        ];
    }

    public function getSortAttributes()
    {
        $attributes = parent::getSortAttributes();

        return $attributes;
    }

    public function getDefaultSort()
    {
        return ['transaction_date' => SORT_DESC];
    }

    protected function getSalesCategoryById(?string $salesCategoryId): ?array
    {
        if (empty($salesCategoryId)) {
            return null;
        }

        return SalesCategoryExtendedView::find()
            ->select('id, name, path, (NOT is_manual) as is_default')
            ->where(['id' => $salesCategoryId])
            ->cache(
                5 * 60,
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->asArray()
            ->one()
            ;
    }
}
