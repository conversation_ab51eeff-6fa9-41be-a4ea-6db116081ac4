<?php

namespace common\models\customer\clickhouse;

use common\components\customerConfig\CustomerConfig;
use common\components\dbStructure\TableNameGenerator;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\DbStructure;

class RepricerEvent extends AbstractClickhouseCustomerRecord
{
    public static function tableName()
    {
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        if ($customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)) {
            return parent::tableName();
        }

        /** @var TableNameGenerator $tableNameGenerator */
        $tableNameGenerator = \Yii::$container->get('tableNameGenerator');
        return $tableNameGenerator->generate(DbStructure::REPRICER_EVENT);
    }
}
