<?php

namespace common\models\customer\clickhouse\traits;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\CustomerComponent;
use common\components\services\order\TransferOrderService;
use common\models\ads\AmazonAdsProfile;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\FinanceEventCategory;
use common\models\Seller;
use yii\caching\TagDependency;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

trait ExtraFiltersTrait
{
    public function applyMarketplaceSellerGroupsFilter(
        Query $query,
        string $marketplaceSellerIds = null,
        string $marketplaceId = null,
        string $sellerId = null,
        string $marketplaceColumnName = 'marketplace_id',
        string $sellerColumnName = 'seller_id'
    ): void
    {
        if (!empty($marketplaceSellerIds)) {
            $marketplaceSellerIds = json_decode($marketplaceSellerIds, true) ?? [];

            $orParts = [];
            foreach ($marketplaceSellerIds as $pair) {
                if (!empty($pair['sellerId']) && !empty($pair['marketplaceId'])) {
                    if ($pair['sellerId'] === 'all') {
                        $orParts[] = ['!=', $sellerColumnName, ''];
                    } else {
                        $orParts[] = [
                            'AND',
                            ['=', $marketplaceColumnName, $pair['marketplaceId']],
                            ['=', $sellerColumnName, $pair['sellerId']]
                        ];
                    }
                    continue;
                }
                if (!empty($pair['sellerId'])) {
                    if ($pair['sellerId'] === 'all') {
                        $query->andWhere(['!=', $sellerColumnName, '']);
                    } else {
                        $query->andFilterWhere(['=', $sellerColumnName, $pair['sellerId']]);
                    }
                }
            }

            $query->andFilterWhere(array_merge(['OR'], $orParts));
        }

        if (!empty($marketplaceId)) {
            $query->andFilterWhere(['in', $marketplaceColumnName, explode(',', $marketplaceId)]);
        }

        if (!empty($sellerId) && $sellerId !== 'global') {
            if ($sellerId === 'all') {
                $query->andWhere(['!=', $sellerColumnName, '']);
            } else {
                $query->andFilterWhere(['in', $sellerColumnName, explode(',', $sellerId)]);
            }
        }

        $sellerIds = Seller::find()
            ->select('id')
            ->where([
                'customer_id' => \Yii::$app->dbManager->getCustomerId()
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag(\Yii::$app->dbManager->getCustomerId())])
            )
            ->column();
        $query->andWhere(['OR',
            ['in', $sellerColumnName, $sellerIds],
            ['=', $sellerColumnName, ''],
        ]);
    }

    public function applyAmazonAdAccountsLogic(
        Query $query,
       string $categoryIdColumnName = 'category_id',
       string $marketplaceColumnName = 'marketplace_id',
       string $sellerColumnName = 'seller_id'
    ): void
    {
        $amazonAdCategoryIds = FinanceEventCategory::getAmazonAdIds();
        $activeProfiles = AmazonAdsProfile::findActive();
        $orParts = ['OR'];
        foreach ($activeProfiles as $activeProfile) {
            $orParts[] = [
                'AND',
                ['=', $marketplaceColumnName, $activeProfile['marketplace_id']],
                ['=', $sellerColumnName, $activeProfile['seller_id']],
            ];
        }
        $query->andWhere([
            'OR',
            ['not in', $categoryIdColumnName, $amazonAdCategoryIds],
            $orParts,
        ]);
    }

    public function applyDateStartDateEndFilter(
        Query $query,
        string $dateStart = null,
        string $dateEnd = null,
        string $dateColumnName
    ): void
    {
        if (empty($dateStart) || empty($dateEnd)) {
            return;
        }

        $query->andWhere(['between', $dateColumnName, $dateStart, $dateEnd]);
    }

    public function applyExpiredOrInactiveSubscriptionLogic(
        Query $query,
        string $sellerColumnName = 'seller_id'
    ): void
    {
        /** @var CustomerComponent $customerComponent */
        $customerComponent = \Yii::$app->customerComponent;
        $subscriptionInfo = $customerComponent->getSubscriptionInfo();
        $sellerColumnName = "upper($sellerColumnName)";

        if (!$subscriptionInfo['isActive']) {
            return;
        }

        $query->andWhere([
            'OR',
            ['in', $sellerColumnName, $subscriptionInfo['sellerIds']],
            ['=', $sellerColumnName, ''],
            ['is', $sellerColumnName, new Expression('NULL')],
        ]);
    }

    public function applyPostedDateTransactionDateFilter(
        Query $query,
        FiltersForm $filtersForm,
        string $postedDateColumnName = 'posted_date',
        string $transactionDateColumnName = 'transaction_date',
        bool $isUnit = false
    ): void
    {
        if (empty($filtersForm->dateStart) || empty($filtersForm->dateEnd)) {
            return;
        }

        $dateStart = (new \DateTime($filtersForm->dateStart))->format('Y-m-d H:i:s');
        $dateEnd = (new \DateTime($filtersForm->dateEnd))->format('Y-m-d H:i:s');

        if ($filtersForm->isTransactionDateMode) {
            $dateStart6Month = (new \DateTime($filtersForm->dateStart))->modify('-6 month')->format('Y-m-d H:i:s');
            $dateEnd6Month = (new \DateTime($filtersForm->dateEnd))->modify('+6 month')->format('Y-m-d H:i:s');
            if (!$isUnit) {
                $query->andWhere(['between', $postedDateColumnName, $dateStart6Month, $dateEnd6Month]);
            }
            $query->andWhere(['between', $transactionDateColumnName, $dateStart, $dateEnd]);
        } else {
            $query->andWhere(['between', $postedDateColumnName, $dateStart, $dateEnd]);
        }

    }

    public function applySegmentationFilters(
        Query $query,
        array $params = [],
        array $columnNameMapping = [],
        bool $shouldUseHaving = false
    ): void
    {
        $fieldsToApplyInFilter = [
            'ean',
            'upc',
            'sku' => [
                'operator' => 'like'
            ],
            'asin' => [
                'operator' => 'like'
            ],
            'isbn',
            'brand' => [
                'is_strict_compare' => 'lower'
            ],
            'manufacturer' => [
                'is_strict_compare' => 'lower'
            ],
            'product_type' => [
                'is_strict_compare' => 'lower'
            ],
            'parent_asin',
            'title' => [
                'operator' => 'like'
            ],
            'product_adult' => [
                'value_type' => 'boolean'
            ],
            'tag_id' => [
                'operator' => 'arrayExists'
            ]
        ];

        foreach ($fieldsToApplyInFilter as $filterName => $filterConfig) {
            if (is_integer($filterName)) {
                $filterName = $filterConfig;
                $filterConfig = [];
            }

            $keyMapping = null;

            if (!empty($columnNameMapping)) {
                if ($columnNameMapping[$filterName] !== null) {
                    $columnName = $columnNameMapping[$filterName];
                    $keyMapping = isset($params[$filterName]) ? $filterName : null;
                } else {
                    $keyMapping = array_search($filterName, $columnNameMapping);
                    $columnName = $keyMapping;
                }
            } else {
                $columnName = $filterName;
            }

            if (!isset($params[$columnName]) && !isset($params[$keyMapping])) {
                continue;
            }

            $operator = $filterConfig['operator'] ?? '=';
            $valueType = $filterConfig['value_type'] ?? null;

            $value = $keyMapping ? $params[$keyMapping] : $params[$columnName];

            if ($valueType == 'boolean') {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ? 1 : 0;
            }

            $isStrictCompare = $filterConfig['is_strict_compare'] ?? true;
            $columnName = $columnNameMapping[$filterName] ?? $filterName;

            $columnName = ($columnName == 'title') ? '"product".title' : $columnName;  //because tag have title column too

            if ($isStrictCompare !== true) {
                $value = mb_strtolower($value);
                $columnName = new Expression("LOWER($columnName)");
            }

            $query->andFilterCompare($columnName, $value, $operator, $valueType, true, $shouldUseHaving);
        }
    }

    /**
     * Applies latest version logic for clickhouse in progress orders.
     * Excludes those of them which were removed (have 0 version).
     *
     * @param ActiveQuery $query
     * @return void
     */
    public function applyClickhouseLatestVersion(ActiveQuery $query): void
    {
        $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());

        if (empty($lastVersion)) {
            return;
        }

        $query
            ->andWhere([
                'AND',
                ['=', 'version', $lastVersion],
                // Do not include order items that were marked as deleted (version 0)
                [
                    'not in',
                    'order_id',
                    AmazonOrderInProgress::find()
                        ->select('order_id')
                        ->distinct()
                        ->where(['=', 'version', 0])
                ]
            ]);
    }
}
