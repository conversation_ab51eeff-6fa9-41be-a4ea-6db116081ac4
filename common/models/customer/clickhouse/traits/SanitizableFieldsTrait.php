<?php

namespace common\models\customer\clickhouse\traits;

use common\components\salesMetricCalculator\DataSanitizer;
use common\interfaces\SanitizableModelInterface;

trait SanitizableFieldsTrait
{
    protected function applySanitizationToFields(array $fields): array
    {
        if (!($this instanceof SanitizableModelInterface)) {
            return $fields;
        }

        $dataSanitizer = new DataSanitizer();

        if ($dataSanitizer->getCustomerComponent()->isActive()) {
            return $fields;
        }

        $sanitizableFields = $this->getSanitizableFields();

        foreach ($fields as $fieldName => $fieldValue) {
            if (isset($sanitizableFields[$fieldName])) {
                $type = $sanitizableFields[$fieldName];
                $fields[$fieldName] = function() use ($type, $dataSanitizer) {
                    return $dataSanitizer->getSanitizedValue($type);
                };
            }
        }

        return $fields;
    }
}
