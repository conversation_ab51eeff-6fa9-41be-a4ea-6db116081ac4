<?php

namespace common\models\customer\clickhouse;

use common\components\currencyRate\CurrencyRateManager;
use common\components\ExtendedQuery;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use yii\db\ActiveQuery;
use yii\db\Expression;

/**
 * @OA\Schema(
 *     schema="FbaFeeFactorViewItem",
 *     type="object",
 *     @OA\Property(
 *         property="posted_date",
 *         type="string",
 *         format="date-time",
 *         description="Date and time the record was posted",
 *         example="2024-02-16 23:52:12"
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         description="Marketplace identifier",
 *         example="A1PA6795UKMFR9"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller identifier",
 *         example="A2WY0S4JX228MI"
 *     ),
 *     @OA\Property(
 *         property="seller_sku",
 *         type="string",
 *         description="Seller SKU",
 *         example="fbm2020302703"
 *     ),
 *     @OA\Property(
 *         property="amazon_order_id",
 *         type="string",
 *         description="Amazon order identifier",
 *         example="306-6552492-7577935"
 *     ),
 *     @OA\Property(
 *         property="order_item_id",
 *         type="string",
 *         description="Order item identifier",
 *         example="35782810830642"
 *     ),
 *     @OA\Property(
 *         property="product_id",
 *         type="integer",
 *         description="Product identifier",
 *         example=310306550
 *     ),
 *     @OA\Property(
 *         property="product_asin",
 *         type="string",
 *         description="Product ASIN",
 *         example="B0BT21QB96"
 *     ),
 *     @OA\Property(
 *         property="product_title",
 *         type="string",
 *         description="Product title",
 *         example="Skechers Herren Flex Advantage 4.0 - UPSHIFT Sneaker"
 *     ),
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="current_amount",
 *         type="number",
 *         format="float",
 *         description="FBA fee amount in EUR",
 *         example=7.50
 *     ),
 *     @OA\Property(
 *         property="prev_amount",
 *         type="number",
 *         format="float",
 *         description="Estimated FBA fee per item in EUR",
 *         example=3.40
 *     )
 * )
 */

class FbaFeeFactorView extends AbstractClickhouseCustomerRecord
{
    public $currency_id = null;
    public $current_amount = null;
    public $prev_amount = null;

    public function search($params): ActiveQuery
    {
        $query = self::find();
        $this->setScenario('search');
        $this->setAttributes($params);

        $currencyId = $params['currency_id'] ?? CurrencyRateManager::BASE_CURRENCY;

        // Conversion expression
        $getAmountExpression = function ($field) use ($currencyId) {
            return new Expression("
                CASE WHEN $field IS NOT NULL
                    THEN round($field * dictGetOrNull(
                        default.currency_rate_dict, 'value',
                        tuple(toDate(posted_date), :currency_id)
                    ), 2)
                    ELSE NULL
                END
            ");
        };

        $query
            ->addSelect([
                'posted_date',
                'marketplace_id',
                'seller_id',
                'seller_sku',
                'amazon_order_id',
                'order_item_id',
                'product_id',
                'product_asin',
                'product_title',
                new Expression("'{$currencyId}' as currency_id"),
                new Expression("{$getAmountExpression('fba_fee_eur')} as current_amount"),
                new Expression("{$getAmountExpression('estimated_fba_fee_per_item_eur')} as prev_amount")
            ])
            ->addParams(['currency_id' => $currencyId]);
        $query->andWhere('fba_fee_eur != estimated_fba_fee_per_item_eur');
        return $query;
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields[] = 'currency_id';
        $fields[] = 'current_amount';
        $fields[] = 'prev_amount';
        return $fields;
    }

    protected static function getActiveQueryInstance(): ActiveQuery
    {
        $extendedQuery = new ExtendedQuery(get_called_class());
        $extendedQuery->setDbEngine(ExtendedQuery::ENGINE_CLICKHOUSE);
        return $extendedQuery;
    }

    public function rules()
    {
        return [
            [['posted_date', 'marketplace_id', 'seller_id', 'seller_sku', 'amazon_order_id', 'order_item_id', 'product_id', 'product_asin', 'product_title', 'fba_fee', 'fba_fee_eur', 'fba_fee_currency', 'order_item_price', 'order_item_price_eur', 'order_currency_code', 'fba_fee_item_price', 'estimated_fba_fee_per_item', 'estimated_fba_fee_per_item_eur', 'estimated_fba_fee_currency', 'estimated_fba_fee_date', 'currency_id', 'prev_amount', 'amount'], 'safe', 'on' => 'search'],
        ];
    }

    public function getSortAttributes()
    {
        $attributes = parent::getSortAttributes();

        return $attributes;
    }

    public function getDefaultSort()
    {
        return ['posted_date' => SORT_DESC];
    }
}
