<?php

declare(strict_types=1);

namespace common\models\customer\clickhouse;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\ExtendedQuery;
use common\components\services\order\TransferOrderService;
use common\interfaces\SanitizableModelInterface;
use common\models\AmazonMarketplace;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\clickhouse\traits\SanitizableFieldsTrait;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

/**
 * Order details
 *
 * @OA\Schema(
 *     schema="SalesTotal",
 *     type="object",
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="amount",
 *         type="number",
 *         format="float",
 *         example=4.8
 *     )
 * )
 * @OA\Schema(
 *     schema="Summary",
 *     type="object",
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         example="A15GEJR91SS7OC"
 *     ),
 *     @OA\Property(
 *         property="order_id",
 *         type="string",
 *         example="303-6829218-2536344"
 *     ),
 *     @OA\Property(
 *         property="offer_type",
 *         type="string",
 *         example="B2C"
 *     ),
 *     @OA\Property(
 *         property="purchase_date",
 *         type="string",
 *         format="date-time",
 *         example="2024-06-12 21:54:30"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         example="Shipped"
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         example="A1PA6795UKMFR9"
 *     ),
 *     @OA\Property(
 *         property="sales_total",
 *         type="object",
 *         @OA\Property(
 *             property="original",
 *             ref="#/components/schemas/SalesTotal"
 *         ),
 *         @OA\Property(
 *             property="chosen",
 *             ref="#/components/schemas/SalesTotal"
 *         )
 *     )
 * )
 * @OA\Schema(
 *     schema="OrderItem",
 *     type="object",
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         example="SiAura Material ® - 10 Stück Metall Endkappen, 7 x 8 mm, Goldfarben"
 *     ),
 *     @OA\Property(
 *         property="asin",
 *         type="string",
 *         example="B073WXKGQT"
 *     ),
 *     @OA\Property(
 *         property="sku",
 *         type="string",
 *         example="END13"
 *     ),
 *     @OA\Property(
 *         property="unit_price",
 *         type="number",
 *         format="float",
 *         example=4.8
 *     ),
 *     @OA\Property(
 *         property="quantity",
 *         type="integer",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="stock_type",
 *         type="string",
 *         example="FBA"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         example="Shipped"
 *     )
 * )
 * @OA\Schema(
 *     schema="OrderDetailsResponse",
 *     type="object",
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="is_approximate_amounts_calculation",
 *         type="boolean",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         example="2024-06-13 01:17:54"
 *     ),
 *     @OA\Property(
 *         property="summary",
 *         ref="#/components/schemas/Summary"
 *     ),
 *     @OA\Property(
 *         property="breakdown",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ProfitBreakdownItem")
 *     ),
 *     @OA\Property(
 *         property="products",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/OrderItem")
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="AmazonOrderExtendedViewItem",
 *     type="object",
 *     @OA\Property(
 *         property="order_item_id",
 *         type="string",
 *         description="Order item identifier",
 *         example="35782810830642"
 *     ),
 *     @OA\Property(
 *         property="order_id",
 *         type="string",
 *         description="Order identifier",
 *         example="306-6552492-7577935"
 *     ),
 *     @OA\Property(
 *         property="order_status",
 *         type="string",
 *         enum={"Shipped", "Canceled", "Pending", "Unshipped", "PartiallyShipped", "Unfulfillable", "InvoiceUnconfirmed", "PendingAvailability"},
 *         description="Order status",
 *         example="Shipped"
 *     ),
 *     @OA\Property(
 *         property="order_purchase_date",
 *         type="string",
 *         format="date-time",
 *         description="Order purchase date",
 *         example="2024-02-16 23:52:12"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller identifier",
 *         example="A2WY0S4JX228MI"
 *     ),
 *     @OA\Property(
 *         property="seller_sku",
 *         type="string",
 *         description="Seller SKU",
 *         example="fbm2020302703"
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         description="Marketplace identifier",
 *         example="A1PA6795UKMFR9"
 *     ),
 *     @OA\Property(
 *         property="quantity",
 *         type="integer",
 *         description="Quantity",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="quantity_refunded",
 *         type="integer",
 *         description="Quantity refunded",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="refund_date",
 *         type="string",
 *         format="date-time",
 *         description="Refund date",
 *         example="2024-02-16 23:52:12"
 *     ),
 *     @OA\Property(
 *         property="product_id",
 *         type="integer",
 *         description="Product identifier",
 *         example=310306550
 *     ),
 *     @OA\Property(
 *         property="product_asin",
 *         type="string",
 *         description="Product ASIN",
 *         example="B0BT21QB96"
 *     ),
 *     @OA\Property(
 *         property="product_title",
 *         type="string",
 *         description="Product title",
 *         example="Skechers Herren Flex Advantage 4.0 - UPSHIFT Sneaker 894159 DKNV Navy, Schuhgröße:43 EU"
 *     ),
 *     @OA\Property(
 *         property="product_stock_type",
 *         type="string",
 *         description="Product stock type",
 *         example="FBM"
 *     ),
 *     @OA\Property(
 *         property="product_condition",
 *         type="string",
 *         description="Product condition",
 *         example="11"
 *     ),
 *    @OA\Property(
 *       property="product_ean",
 *       type="string",
 *       description="Product EAN"
 *    ),
 *    @OA\Property(
 *       property="product_upc",
 *       type="string",
 *       description="Product UPC"
 *    ),
 *    @OA\Property(
 *       property="product_isbn",
 *       type="string",
 *       description="Product ISBN"
 *    ),
 *    @OA\Property(
 *       property="product_brand",
 *       type="string",
 *       description="Product Brand"
 *    ),
 *    @OA\Property(
 *       property="product_type",
 *       type="string",
 *       description="Product Type"
 *    ),
 *    @OA\Property(
 *       property="product_manufacturer",
 *       type="string",
 *       description="Product Manufacturer"
 *    ),
 *    @OA\Property(
 *       property="product_parent_asin",
 *       type="string",
 *       description="Parent Asin"
 *    ),
 *    @OA\Property(
 *       property="product_adult",
 *       type="boolean",
 *       description="Product Adult"
 *    ),
 *     @OA\Property(
 *         property="tag_id",
 *         type="string",
 *         description="Tag ID"
 *     ),
 *     @OA\Property(
 *         property="is_approximate_amounts_calculation",
 *         type="boolean",
 *         description="Flag indicating if amounts calculation is approximate",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="offer_type",
 *         type="string",
 *         description="Offer type",
 *         example="B2C"
 *     ),
 *     @OA\Property(
 *         property="item_price",
 *         type="number",
 *         format="float",
 *         description="Item price",
 *         example=66.59
 *     ),
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         description="Currency identifier",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="promotion_amount",
 *         type="number",
 *         format="float",
 *         description="Promotion amount",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="amazon_fees_amount",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Amazon fees amount",
 *         example=9.99
 *     ),
 *     @OA\Property(
 *         property="expenses_amount",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Expenses amount",
 *         example=68.59
 *     ),
 *     @OA\Property(
 *         property="total_expenses",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Total expenses amount",
 *         example=68.59
 *     ),
 *     @OA\Property(
 *         property="revenue_amount",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Revenue amount",
 *         example=76.58
 *     ),
 *     @OA\Property(
 *        property="total_income",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Total income amount",
 *         example=76.58
 *     ),
 *     @OA\Property(
 *         property="estimated_profit_amount",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         description="Estimated profit amount",
 *         example=-2
 *     )
 * )
 */
class AmazonOrderExtendedView extends AbstractClickhouseCustomerRecord implements AmazonOrderExtendedViewInterface, SanitizableModelInterface
{
    public $amazon_fees_amount;

    public $expenses_amount;

    public $total_expenses;

    public $revenue_amount;

    public $total_income;

    public $estimated_profit_amount;

    public $promotion_amount;

    use ExtraFiltersTrait;
    use SanitizableFieldsTrait;

    public function search($params)
    {
        $activeQuery = self::find();
        $this->setScenario('search');
        $this->setAttributes($params);
        $currencyId = $params['currency_id'] ?? CurrencyRateManager::BASE_CURRENCY;

        $amountFunc = fn(string $columnName): string => "CASE WHEN {$columnName} IS NULL 
                THEN NULL 
                ELSE
                    round(
                        CASE WHEN '{$columnName}' = 'estimated_profit_amount_eur'
                            THEN {$columnName}
                            ELSE abs({$columnName}) 
                        END
                        * dictGetOrNull(
                            default.currency_rate_dict, 
                            'value', 
                            tuple(toDate(order_purchase_date), :currency_id)
                        ),
                        2
                    )
            END";

        $itemPriceExpression = $amountFunc('item_price_eur');
        $amazonFeesExpression = $amountFunc('(amazon_fees_amount_eur * -1)');
        $expensesExpression = $amountFunc('(expenses_amount_eur * -1)');
        $expensesTotalExpression = $amountFunc('(expenses_total_amount_eur * -1)');
        $revenueExpression = $amountFunc('revenue_amount_eur');
        $revenueTotalExpression = $amountFunc('total_income_amount_eur');
        $estimatedProfitExpression = $amountFunc('estimated_profit_amount_eur');
        $promotionAmount = $amountFunc('promotion_amount_eur');

        $selectColumns = [
            'order_item_id',
            'order_id',
            'order_status',
            'order_purchase_date',
            'seller_id',
            'seller_sku',
            'marketplace_id',
            'quantity',
            'quantity_refunded',
            'refund_date',
            'product_id',
            'product_asin',
            'product_ean',
            'product_upc',
            'product_isbn',
            'product_brand',
            'product_manufacturer',
            'product_adult',
            'product_parent_asin',
            'product_type',
            'product_title',
            'product_stock_type',
            'product_condition',
            'amazon_fees_amount_eur',
            'expenses_amount_eur',
            'expenses_total_amount_eur',
            'revenue_amount_eur',
            'total_income_amount_eur',
            'estimated_profit_amount_eur',
            'promotion_amount_eur',
            'is_approximate_amounts_calculation',
            'offer_type',
            new Expression($itemPriceExpression . ' as item_price'),
            new Expression($amazonFeesExpression . ' as amazon_fees_amount'),
            new Expression($expensesExpression . ' as expenses_amount'),
            new Expression($expensesTotalExpression . ' as total_expenses'),
            new Expression($revenueExpression . ' as revenue_amount'),
            new Expression($revenueTotalExpression . ' as total_income'),
            new Expression($estimatedProfitExpression . ' as estimated_profit_amount'),
            new Expression($promotionAmount . ' as promotion_amount'),
            new Expression(':currency_id as currency_id')
        ];

        try {
            self::find()->select(['tag_id'])->limit(1)->scalar();
            $selectColumns[] = 'tag_id';
        } catch (\Throwable) {
            $selectColumns[] = new Expression('null as tag_id');
        }

        $activeQuery
            ->addSelect($selectColumns)
            ->addParams(['currency_id' => $currencyId]);

        $this->applyMarketplaceSellerGroupsFilter(
            $activeQuery,
            $params['marketplace_seller_ids'] ?? null,
            $this->marketplace_id ?? null,
            $this->seller_id ?? null
        );
        $this->applyExpiredOrInactiveSubscriptionLogic(
            $activeQuery,
            'seller_id',
            'order_purchase_date'
        );

        $activeQuery
            ->andFilterCompare('order_item_id', $this->order_item_id, 'like')
            ->andFilterCompare('order_id', $this->order_id, 'like')
            ->andFilterCompare('order_status', $this->order_status)
            ->andFilterCompare('seller_sku', $this->seller_sku, 'like')
            ->andFilterCompare('quantity', $this->quantity, 'like', 'int')
            ->andFilterCompare('quantity_refunded', $this->quantity_refunded, 'like', 'int')
            ->andFilterCompare('product_id', $this->product_id, 'like', 'int')
            ->andFilterCompare('offer_type', $this->offer_type)
            ->andFilterCompare('product_title', $this->product_title, 'like')
            ->andFilterCompare('product_stock_type', $this->product_stock_type)
            ->andFilterCompare('product_condition', $this->product_condition)
            ->andFilterCompare(new Expression($itemPriceExpression), $this->item_price, 'like', 'float')
            ->andFilterCompare(new Expression($amazonFeesExpression), $this->amazon_fees_amount, 'like', 'float')
            ->andFilterCompare(new Expression($expensesExpression), $this->expenses_amount, 'like', 'float')
            ->andFilterCompare(new Expression($revenueExpression), $this->revenue_amount, 'like', 'float')
            ->andFilterCompare(new Expression($expensesTotalExpression), $this->total_expenses, 'like', 'float')
            ->andFilterCompare(new Expression($revenueTotalExpression), $this->total_income, 'like', 'float')
            ->andFilterCompare(new Expression($estimatedProfitExpression), $this->estimated_profit_amount, 'like', 'float')
            ->andFilterCompare(new Expression($promotionAmount), $this->promotion_amount, 'like', 'float')
        ;
        $activeQuery->andWhere(['!=', 'marketplace_id', AmazonMarketplace::NON_AMAZON]);

        $this->applySegmentationFilters($activeQuery, $params, [
            'ean' => 'product_ean',
            'upc' => 'product_upc',
            'sku' => 'seller_sku',
            'asin' => 'product_asin',
            'isbn' => 'product_isbn',
            'brand' => 'product_brand',
            'manufacturer' => 'product_manufacturer',
            'product_type' => 'product_type',
            'title' => 'product_title',
            'parent_asin' => 'product_parent_asin',
            'adult_product' => 'product_adult',
            'tag_id' => 'tag_id'
        ]);

        $this->applyBetweenDateFilter($activeQuery, 'order_purchase_date', $this->order_purchase_date);
        $this->applyBetweenDateFilter($activeQuery, 'refund_date', $this->refund_date);

        $query2 = clone $activeQuery;
        $query2->from(AmazonOrderInProgressExtendedViewV1::tableName());
        $this->applyClickhouseLatestVersion($query2);

        $unionQuery = parent::find()->from(['o' => $activeQuery->union($query2, true)]);

        return $unionQuery;
    }

    #[\Override]
    public function getSortAttributes(): array
    {
        $attributes = parent::getSortAttributes();
        $attributes['amazon_fees_amount'] = [
            'asc' => ['amazon_fees_amount_eur' => SORT_ASC],
            'desc' => ['amazon_fees_amount_eur' => SORT_DESC],
        ];
        $attributes['expenses_amount'] = [
            'asc' => ['expenses_amount_eur' => SORT_ASC],
            'desc' => ['expenses_amount_eur' => SORT_DESC],
        ];
        $attributes['total_expenses'] = [
            'asc' => ['expenses_total_amount_eur' => SORT_ASC],
            'desc' => ['expenses_total_amount_eur' => SORT_DESC],
        ];
        $attributes['revenue_amount'] = [
            'asc' => ['revenue_amount_eur' => SORT_ASC],
            'desc' => ['revenue_amount_eur' => SORT_DESC],
        ];
        $attributes['total_income'] = [
            'asc' => ['total_income_amount_eur' => SORT_ASC],
            'desc' => ['total_income_amount_eur' => SORT_DESC],
        ];
        $attributes['estimated_profit_amount'] = [
            'asc' => ['estimated_profit_amount_eur' => SORT_ASC],
            'desc' => ['estimated_profit_amount_eur' => SORT_DESC],
        ];
        $attributes['promotion_amount'] = [
            'asc' => ['promotion_amount_eur' => SORT_ASC],
            'desc' => ['promotion_amount_eur' => SORT_DESC],
        ];

        $attributes = array_merge(
            $attributes,
            $this->buildNullableSortAttributes([
                'product_asin',
                'product_ean',
                'product_upc',
                'product_isbn',
                'product_brand',
                'product_manufacturer',
                'product_type',
                'seller_sku',
                'product_title',
            ])
        );

        // FE sends this parameter by mistake, and we have 500 error therefore.
        foreach ($attributes as $k => $attribute) {
            if ($attribute === 'id') {
                unset($attributes[$k]);
            }
        }

        return $attributes;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function rules()
    {
        return [
            [[
                'order_item_id',
                'order_id',
                'order_status',
                'seller_id',
                'seller_sku',
                'marketplace_id',
                'item_price',
                'quantity',
                'quantity_refunded',
                'item_price',
                'offer_type',
                'amazon_fees_amount',
                'expenses_amount',
                'total_expenses',
                'revenue_amount',
                'total_income',
                'estimated_profit_amount',
                'promotion_amount',
                'product_id',
                'product_asin',
                'product_title',
                'product_stock_type',
                'product_condition',
                'product_ean',
                'product_upc',
                'product_isbn',
                'product_brand',
                'product_manufacturer',
                'product_adult',
                'product_parent_asin',
                'product_type',
                'currency_id',
                'order_purchase_date',
                'refund_date',
            ],'safe','on'=>'search'],
        ];
    }

    #[\Override]
    public function getDefaultSort(): array
    {
        return [
            'order_purchase_date' => SORT_DESC,
        ];
    }

    #[\Override]
    public function fields()
    {
        $fields = parent::fields();

        unset($fields['amazon_fees_amount_eur']);
        unset($fields['expenses_amount_eur']);
        unset($fields['expenses_total_amount_eur']);
        unset($fields['revenue_amount_eur']);
        unset($fields['total_income_amount_eur']);
        unset($fields['estimated_profit_amount_eur']);
        unset($fields['promotion_amount_eur']);

        $fields['currency_id'] = fn() => $this->currency_id;
        $fields['product_adult'] = function(): ?bool {
            if (null === $this->product_adult || '' === $this->product_adult) {
                return null;
            }

            return (bool)$this->product_adult;
        };
        $fields['quantity'] = fn() => empty($this->quantity) ? null : $this->quantity;
        $fields['promotion_amount'] = fn() => $this->promotion_amount;
        $fields['item_price'] = function(): ?float {
            if (empty($this->item_price) || empty($this->quantity)) {
                return null;
            }

            return round_half_even($this->item_price / $this->quantity, 2);
        };
        $fields['amazon_fees_amount'] = fn(): float|int|array|null|string|false => empty($this->amazon_fees_amount)
            ? $this->amazon_fees_amount
            : abs($this->amazon_fees_amount);
        $fields['expenses_amount'] = fn(): float|int|array|null|string|false => empty($this->expenses_amount)
            ? $this->expenses_amount
            : abs($this->expenses_amount);
        $fields['revenue_amount'] = fn() => $this->revenue_amount;
        $fields['total_expenses'] = fn(): float|int|array|null|string|false => empty($this->total_expenses)
            ? $this->total_expenses
            : abs($this->total_expenses);
        $fields['total_income'] = fn() => $this->total_income;
        $fields['estimated_profit_amount'] = fn() => $this->estimated_profit_amount;
        $fields['is_approximate_amounts_calculation'] = fn(): bool => (bool)($this->is_approximate_amounts_calculation ?? false);

        return $this->applySanitizationToFields($fields);
    }

    #[\Override]
    protected static function getActiveQueryInstance(): ActiveQuery
    {
        $extendedQuery = new ExtendedQuery(static::class);
        $extendedQuery->setDbEngine(ExtendedQuery::ENGINE_CLICKHOUSE);
        return $extendedQuery;
    }

    #[\Override]
    public function getSanitizableFields(): array
    {
        return [
            'product_brand' => 'text',
            'product_ean' => 'text',
            'product_upc' => 'text',
            'product_isbn' => 'text',
            'product_manufacturer' => 'text',
            'product_type' => 'text',
            'offer_type' => 'text',
            'product_parent_asin' => 'text',
            'product_stock_type' => 'text',
            'product_adult' => 'text',
            'expenses_amount' => 'number',
            'amazon_fees_amount' => 'number',
            'item_price' => 'number',
        ];
    }
}
