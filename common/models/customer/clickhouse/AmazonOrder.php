<?php

namespace common\models\customer\clickhouse;

use common\models\customer\base\AbstractClickhouseCustomerRecord;

class AmazonOrder extends AbstractClickhouseCustomerRecord
{
    protected $offer_type = null;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'offer_type',
                ],
                'safe',
                'on' => self::SCENARIO_SEARCH,
            ],
        ];
    }

    public function getDefaultSort()
    {
        return [
            'order_id' => SORT_DESC,
        ];
    }

    public function getSortAttributes()
    {
        return [];
    }

    public function search(array $params)
    {
        $query = self::find();
        $this->setScenario('search');
        $this->setAttributes($params);

        if (strtoupper($this->offer_type) === \common\models\order\AmazonOrder::OFFER_TYPE_B2C) {
            $query->andWhere([
                'OR',
                ['is_business_order' => 0],
                ['is_business_order' => null],
            ]);
        }
        if (strtoupper($this->offer_type) === \common\models\order\AmazonOrder::OFFER_TYPE_B2B) {
            $query->andFilterCompare('is_business_order', 1);
        }

        return $query;
    }
}
