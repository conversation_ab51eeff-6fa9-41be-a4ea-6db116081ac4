<?php

namespace common\models\customer\clickhouse;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\models\customer\base\AbstractClickhouseCustomerRecord;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use common\models\customer\ProductBsr;
use common\models\customer\ProductCostCategory;
use common\models\customer\TransactionExtendedView;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;

/**
 * @OA\Schema(
 *     schema="ProductAggregatedSalesInfo",
 *     type="object",
 *     description="Aggregated sales details of a product",
 *     @OA\Property(
 *         property="product_id",
 *         type="integer",
 *         description="Product identifier",
 *         example=8961563
 *     ),
 *     @OA\Property(
 *         property="marketplace_id",
 *         type="string",
 *         description="Marketplace identifier",
 *         example="A2NODRKZP88ZB9"
 *     ),
 *     @OA\Property(
 *         property="seller_id",
 *         type="string",
 *         description="Seller identifier",
 *         example="A15GEJR91SS7OC"
 *     ),
 *     @OA\Property(
 *         property="seller_sku",
 *         type="string",
 *         description="Seller SKU of the product",
 *         example="NAY004"
 *     ),
 *     @OA\Property(
 *         property="currency_id",
 *         type="string",
 *         description="Currency identifier",
 *         example="EUR"
 *     ),
 *     @OA\Property(
 *         property="product_asin",
 *         type="string",
 *         description="ASIN of the product",
 *         example="B000VOLCO8"
 *     ),
 *     @OA\Property(
 *         property="product_brand",
 *         type="string",
 *         description="Brand of the product",
 *         nullable=true,
 *         example="Adidas"
 *     ),
 *     @OA\Property(
 *         property="product_ean",
 *         type="string",
 *         description="Product EAN",
 *         nullable=true,
 *         example="0070070107008"
 *     ),
 *     @OA\Property(
 *         property="product_upc",
 *         type="string",
 *         description="Product UPC",
 *         nullable=true,
 *         example="070070107008"
 *     ),
 *     @OA\Property(
 *         property="product_isbn",
 *         type="string",
 *         description="Product ISBN",
 *         nullable=true,
 *         example="9790772084"
 *     ),
 *     @OA\Property(
 *         property="product_title",
 *         type="string",
 *         description="Title of the product",
 *         nullable=true,
 *         example="Adidas - Eau de Toilette Team Force 100Ml Vapo"
 *     ),
 *     @OA\Property(
 *         property="product_manufacturer",
 *         type="string",
 *         description="Manufacturer of the product",
 *         nullable=true,
 *         example="Adidas"
 *     ),
 *     @OA\Property(
 *         property="product_type",
 *         type="string",
 *         description="Type of the product",
 *         example="PERSONAL_FRAGRANCE",
 *         nullable=true
 *     ),
 *     @OA\Property(
 *         property="offer_type",
 *         type="string",
 *         example="B2B",
 *         nullable=true
 *     ),
 *     @OA\Property(
 *         property="product_parent_asin",
 *         type="string",
 *         description="Parent ASIN of the product",
 *         nullable=true,
 *         example="C00J0OLCO9"
 *     ),
 *     @OA\Property(
 *         property="product_stock_type",
 *         type="string",
 *         description="Stock type of the product",
 *         example="FBA"
 *     ),
 *     @OA\Property(
 *         property="product_adult",
 *         type="boolean",
 *         description="Whether the product is marked as adult",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="tag_id",
 *         type="string",
 *         description="Tag ID"
 *     ),
 *     @OA\Property(
 *         property="revenue_amount",
 *         type="number",
 *         format="float",
 *         description="Total revenue amount",
 *         example=270950.32
 *     ),
 *     @OA\Property(
 *         property="expenses_amount",
 *         type="number",
 *         format="float",
 *         description="Total expenses amount",
 *         example=344163.01
 *     ),
 *     @OA\Property(
 *         property="expenses_amount_without_fees",
 *         type="number",
 *         format="float",
 *         description="Expenses amount excluding fees",
 *         example=251701.5
 *     ),
 *     @OA\Property(
 *         property="amazon_fees",
 *         type="number",
 *         format="float",
 *         description="Total Amazon fees",
 *         example=338453.51
 *     ),
 *     @OA\Property(
 *         property="estimated_profit_amount",
 *         type="number",
 *         format="float",
 *         description="Estimated profit amount",
 *         example=8.88
 *     ),
 *     @OA\Property(
 *         property="net_purchase_price",
 *         type="number",
 *         format="float",
 *         description="Net purchase price",
 *         example=245992
 *     ),
 *     @OA\Property(
 *         property="orders",
 *         type="integer",
 *         description="Total number of orders",
 *         example=21657
 *     ),
 *     @OA\Property(
 *         property="units",
 *         type="integer",
 *         description="Total number of units sold",
 *         example=22236
 *     ),
 *     @OA\Property(
 *         property="refunds",
 *         type="integer",
 *         description="Number of refunds",
 *         example=270
 *     ),
 *     @OA\Property(
 *         property="promo",
 *         type="integer",
 *         description="Number of promotional transactions",
 *         example=3
 *     ),
 *     @OA\Property(
 *         property="roi",
 *         type="number",
 *         format="float",
 *         description="Return on investment",
 *         example=-21.27
 *     ),
 *     @OA\Property(
 *         property="markup",
 *         type="number",
 *         format="float",
 *         description="Markup percentage",
 *         example=-29.76
 *     ),
 *     @OA\Property(
 *         property="margin",
 *         type="number",
 *         format="float",
 *         description="Profit margin percentage",
 *         example=-27.02
 *     ),
 *     @OA\Property(
 *         property="bsr_avg_curr",
 *         type="integer",
 *         nullable=true,
 *         example=11253.23,
 *         nullable=true
 *     ),
 *     @OA\Property(
 *         property="bsr_avg_prev_compare_percents",
 *         type="number",
 *         format="float",
 *         nullable=true,
 *         example=-11,
 *         nullable=true
 *     )
 * )
 */
class ProductAggregatedSalesInfo extends AbstractClickhouseCustomerRecord
{
    use ExtraFiltersTrait;

    public function rules()
    {
        return [
            [[
                'estimated_profit_amount',
                'revenue_amount',
                'expenses_amount',
                'roi',
                'margin',
            ],'safe','on'=>'search'],
        ];
    }

    public static function find(
        string $currencyId = null,
        bool $isTransactionDateMode = false,
        string $dateStart = null,
        string $dateEnd = null,
        ?string $salesCategoryStrategy = SalesCategoryStrategyFactory::DEFAULT_STRATEGY
    ): Query
    {
        $organicSalesCategoryId = FinanceEventCategory::getOrganicSalesId();
        $organicRefundCategoryId = FinanceEventCategory::getOrganicRefundId();
        $promoCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_PROMOTION);
        $ignoredCategoryIds = FinanceEventCategory::getIgnoredIds();
        $promoCategoryIds = !empty($promoCategoryIds) ?  implode(',', $promoCategoryIds) : '-1';
        $moneyAccuracy = (new CustomerComponent())->getMoneyAccuracy();

        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($salesCategoryStrategy);

        $netPurchasePriceCOG = ProductCostCategory::find()->select('id')->where(['name' => 'Net purchase price'])->scalar() ?? 0;

        $revenueCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_REVENUE);

        $amazonFeesCategoryIds = FinanceEventCategory::getIdsByTag(SalesCategory::TAG_AMAZON_FEES);

        if ($salesCategoryStrategy->getType() === SalesCategoryStrategyFactory::STRATEGY_CUSTOM) {
            $revenueCondition = "category_id IN (" . implode(', ', $revenueCategoryIds) . ") and cog_category_id = 0";
            $expensesCondition = "category_id NOT IN (" . implode(', ', $revenueCategoryIds) . ") or cog_category_id > 0";
        } else {
            $revenueCondition = "amount >= 0";
            $expensesCondition = "amount < 0";
        }

        if (empty($currencyId)) {
            $currencyId =  CurrencyRateManager::BASE_CURRENCY;
        }

        $dateColumnName1 = $isTransactionDateMode ? 'transaction_date' : 'posted_date';
        $dateColumnName2 = $isTransactionDateMode ? 'TransactionDate' : 'PostedDate';

        $query1 = $salesCategoryStrategy->getTransactionExtendedViewQuery()
            ->select([
                'product_id',
                'marketplace_id',
                'seller_id',
                'seller_sku',
                'offer_type',
                'product_asin',
                'product_brand',
                'product_ean',
                'product_upc',
                'product_isbn',
                'product_title',
                'product_manufacturer',
                'product_type',
                'product_parent_asin',
                'product_stock_type',
                'product_adult',
                "tag_id",
                'amount_eur',
                'category_id',
                'cog_category_id',
                'quantity',
                "amount_eur * dictGetOrNull(
                    default.currency_rate_dict, 
                    'value', 
                    tuple(toDate($dateColumnName1), :currency_id)
                ) as amount"
            ])
            ->where([
                'NOT IN', 'category_id', $ignoredCategoryIds
            ]);
            (new self)->applyDateStartDateEndFilter(
                $query1,
                $dateStart,
                $dateEnd,
                $isTransactionDateMode ? 'transaction_date' : 'posted_date'
            );

        $query2 = OrderBasedTransaction::find()
            ->select([
                'ProductId as product_id',
                'MarketplaceId as marketplace_id',
                'SellerId as seller_id',
                'SellerSKU as seller_sku',
                'OfferType as offer_type',
                'ASIN as product_asin',
                'Brand as product_brand',
                'EAN as product_ean',
                'UPC as product_upc',
                'ISBN as product_isbn',
                'Title as product_title',
                'Manufacturer as product_manufacturer',
                'ProductType as product_type',
                'ParentASIN as product_parent_asin',
                'ProductStockType as product_stock_type',
                'AdultProduct as product_adult',
                "TagId AS tag_id",
                'AmountEUR as amount_eur',
                'CategoryId as category_id',
                'COGCategoryId as cog_category_id',
                'Quantity as quantity',
                "AmountEUR * dictGetOrNull(
                    default.currency_rate_dict, 
                    'value', 
                    tuple(toDate($dateColumnName2), :currency_id)
                ) as amount"
            ])
            ->where([
                'NOT IN', 'CategoryId', $ignoredCategoryIds
            ]);

        (new self)->applyDateStartDateEndFilter(
            $query2,
            $dateStart,
            $dateEnd,
            $isTransactionDateMode ? 'TransactionDate' : 'PostedDate'
        );
        $query3 = clone $query2;
        $query3->from(PpcCostsLastFewDaysTransaction::tableName());

        $query = $salesCategoryStrategy->getTransactionExtendedViewQuery()
            ->from([
                't' => $query1->union($query2, true)->union($query3, true)
            ])
            ->select([
                "product_id",
                'marketplace_id',
                'seller_id',
                'seller_sku as seller_sku',
                '(:currency_id) as currency_id',
                "max(nullif(product_asin, '')) as product_asin",
                "max(nullif(product_brand, '')) as product_brand",
                "max(nullif(product_ean, '')) as product_ean",
                "max(nullif(product_upc, '')) as product_upc",
                "max(nullif(product_isbn, '')) as product_isbn",
                "max(nullif(product_title, '')) as product_title",
                "max(nullif(product_manufacturer, '')) as product_manufacturer",
                "max(nullif(product_type, '')) as product_type",
                "max(nullif(offer_type, '')) as offer_type",
                "max(nullif(product_parent_asin, '')) as product_parent_asin",
                "max(nullif(product_stock_type, '')) as product_stock_type",
                "max(product_adult) as product_adult",
                "tag_id AS tag_id",
                "round(SUM(
                    IF(
                        $revenueCondition,
                        (amount / $moneyAccuracy),
                        0
                    )
                ), 2) as revenue_amount",
                "round(SUM(
                    IF(
                        $expensesCondition,
                        abs((amount / $moneyAccuracy)),
                        0
                    )
                ), 2) as expenses_amount",
                "round(expenses_amount - amazon_fees, 2) as expenses_amount_without_fees",
                "round(SUM(
                    CASE 
                        WHEN (category_id IN (" . implode(', ', $amazonFeesCategoryIds) . ")) 
                        AND amount < 0 
                        THEN abs((amount / $moneyAccuracy)) 
                        ELSE 0 
                    END
                ), 2) as amazon_fees",
                "round(SUM(amount / $moneyAccuracy), 2) as estimated_profit_amount",
                "toInt32(SUM(
                    IF(
                        (
                           cog_category_id = $netPurchasePriceCOG 
                           AND 
                           category_id = $organicSalesCategoryId
                        ),
                        amount / $moneyAccuracy,
                        0
                    )
                )) AS net_purchase_price",
                "toInt32(SUM(
                    IF(category_id = $organicSalesCategoryId AND cog_category_id = 0, 1, 0)
                )) AS orders",
                "toInt32(SUM(
                    IF(category_id = $organicSalesCategoryId AND cog_category_id = 0, quantity, 0)
                )) AS units",
                "toInt32(SUM(
                    IF(category_id = $organicRefundCategoryId AND cog_category_id = 0, quantity, 0)
                )) AS refunds",
                "toInt32(SUM(
                    IF(category_id IN ($promoCategoryIds) AND amount != 0, 1, 0)
                )) AS promo",
                "IF (
                    expenses_amount = 0,
                    null,
                    round((estimated_profit_amount / abs(expenses_amount)) * 100, 2)
                ) as roi",
                "round(CASE
                    WHEN revenue_amount = 0 OR net_purchase_price = 0 THEN 0
                    ELSE
                    round(( estimated_profit_amount / net_purchase_price) * 100, 2)
                    END,
                    2) AS markup",
                "IF (
                    revenue_amount = 0,
                    null,
                    greatest(-100, least(100, round((estimated_profit_amount / abs(revenue_amount)) * 100, 2)))
                ) as margin",
            ])
            ->where([
                'AND',
                ['!=', 'seller_sku', ''],
                ['>', 'product_id', 0]
            ])
            ->params([
                ':currency_id' => $currencyId,
            ])
            ->groupBy([
                'marketplace_id',
                'seller_id',
                'seller_sku',
                'product_id',
                'tag_id',
            ])
            ->asArray()
        ;

        return $query;
    }

    public function search($params, $db = null): Query
    {
        $filtersForm = new FiltersForm([
            'dateStart' => $params['date_start'] ?? null,
            'dateEnd' => $params['date_end'] ?? null,
            'currencyId' => $params['currency_id'] ?? ''
        ]);
        $isTransactionDateMode = $params['is_transaction_date_mode'] ?? false;

        $salesCategoryStrategy = $params['sales_category_strategy'] ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY;

        // Do not move it down, it should be executed before query.
        // There are a lot of filters inside should be applied.
        $isFormValidated = $filtersForm->validate();

        $query = self::find(
            $filtersForm->currencyId,
            $isTransactionDateMode,
            $filtersForm->dateStart,
            $filtersForm->dateEnd ?? null,
            $salesCategoryStrategy
        );

        if (!$isFormValidated) {
            $query->andWhere(new Expression('1 = 2'));
            return $query;
        }

        $paramsFilterList = [
            ['attribute' => 'estimated_profit_amount', 'value' => $params['estimated_profit_amount'] ?? null],
            ['attribute' => 'refunds', 'value' => $params['refunds'] ?? null],
            ['attribute' => 'markup', 'value' => $params['markup'] ?? null],
            ['attribute' => 'roi', 'value' => $params['roi'] ?? null],
            ['attribute' => 'margin', 'value' => $params['margin'] ?? null],
            ['attribute' => 'amazon_fees', 'value' => $params['amazon_fees'] ?? null],
            ['attribute' => 'expenses_amount_without_fees', 'value' => $params['expenses_amount_without_fees'] ?? null],
            ['attribute' => 'revenue_amount', 'value' => $params['revenue_amount'] ?? null],
            ['attribute' => 'units', 'value' => $params['units'] ?? null],
            ['attribute' => 'orders', 'value' => $params['orders'] ?? null]
        ];

        foreach ($paramsFilterList as $param) {
            $query->andFilterCompare(
                $param['attribute'],
                $param['value'],
                '=',
                'float',
                false,
                true
            );
        }

        $bsrQuery = $this->buildBsrSelectSQL($filtersForm->dateStart, $filtersForm->dateEnd ?? null);
        $query->leftJoin(
            ['bsr' => '(' . $bsrQuery . ')'],
            'bsr.bsr_marketplace_id = marketplace_id AND bsr.bsr_seller_id = seller_id AND bsr.bsr_sku = seller_sku'
        );
        $query->addSelect([
            'IF (
                avg(bsr.bsr_avg) = 0,
                NULL,
                round(avg(bsr.bsr_avg))
            ) as bsr_avg_curr',
        ]);

        $prevPeriodDateStart = null;
        $prevPeriodDateEnd = null;

        if (!empty($filtersForm->dateStart) && !empty($filtersForm->dateEnd)) {
            $currStartDate = new \DateTime($filtersForm->dateStart);
            $currEndDate = new \DateTime($filtersForm->dateEnd);
            $diffDays = $currStartDate->diff($currEndDate)->days + 1;
            $prevPeriodDateEnd = $currEndDate->modify("-$diffDays days")->format('Y-m-d H:i:s');
            $prevPeriodDateStart = $currStartDate->modify("-$diffDays days")->format('Y-m-d H:i:s');
        }

        $bsrQuery = $this->buildBsrSelectSQL($prevPeriodDateStart, $prevPeriodDateEnd);
        $query->leftJoin(
            ['bsr_prev' => '(' . $bsrQuery . ')'],
            'bsr_prev.bsr_marketplace_id = marketplace_id AND bsr_prev.bsr_seller_id = seller_id AND bsr_prev.bsr_sku = seller_sku'
        );
        // Calculating BSR average for previous period
        $query->addSelect([
            'IF (
                avg(bsr.bsr_avg) = 0 OR avg(bsr_prev.bsr_avg) IS NULL,
                NULL,
                round((
                    (avg(bsr.bsr_avg) - avg(bsr_prev.bsr_avg)) 
                    / 
                    avg(bsr_prev.bsr_avg)
                ) * 100, 2)
            ) as bsr_avg_prev_compare_percents',
        ]);

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplace_seller_ids'] ?? '',
            $params['marketplace_id'] ?? '',
            $params['seller_id'] ?? '',
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query);
        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'ean' => 'product_ean',
                'upc' => 'product_upc',
                'sku' => 'seller_sku',
                'asin' => 'product_asin',
                'isbn' => 'product_isbn',
                'brand' => 'product_brand',
                'manufacturer' => 'product_manufacturer',
                'product_type' => 'product_type',
                'offer_type' => 'offer_type',
                'title' => 'product_title',
                'parent_asin' => 'product_parent_asin',
                'tag_id' => 'tag_id',
                'adult_product' => 'product_adult',
                'stock_type' => 'product_stock_type'
            ],
            true
        );

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function getSortAttributes()
    {
        return [
            'refunds',
            'markup',
            'amazon_fees',
            'expenses_amount_without_fees',
            'estimated_profit_amount',
            'revenue_amount',
            'expenses_amount',
            'roi',
            'margin',
            'units',
            'orders',
        ];
    }

    public function getDefaultSort()
    {
        return [
            'estimated_profit_amount' => SORT_ASC,
        ];
    }

    protected function buildBsrSelectSQL(
        string $dateStart = null,
        string $dateEnd = null
    ): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $config = $dbManager->getShardPostgressConfig();
        $dbPort = $config['dbPort'];
        $dbHost = $config['dbHost'];
        $dbUsername = $config['dbUser'];
        $dbPassword = $config['dbPassword'];
        $profitDashDbName = $config['profitDashDbName'];

        $bsrQuery = ProductBsr::find()
            ->select([
                'marketplace_id as bsr_marketplace_id',
                'seller_id as bsr_seller_id',
                'sku as bsr_sku',
                'avg(bsr) as bsr_avg'
            ])
            ->groupBy('marketplace_id, seller_id, sku');
        $this->applyDateStartDateEndFilter(
            $bsrQuery,
            $dateStart,
            $dateEnd,
            'date'
        );
        [$schemaName, $tableName] = explode('.', ProductBsr::tableName());
        $bsrQuery->from(new Expression(
            sprintf(
                "postgresql(
                    '%s:%s',
                    '%s',
                    '%s',
                    '%s',
                    '%s',
                    '%s'
                )",
                $dbHost,
                $dbPort,
                $profitDashDbName,
                $tableName,
                $dbUsername,
                $dbPassword,
                $schemaName
            )
        ));
        return $bsrQuery->createCommand()->getRawSql();
    }
}
