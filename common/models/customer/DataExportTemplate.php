<?php

namespace common\models\customer;

use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\exporter\ExporterFactory;
use common\components\dataImportExport\SupportedFileFormats;
use common\components\dataImportExport\SupportedHandlers;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\SalesCategory;
use yii\behaviors\TimestampBehavior;

/**
 * @OA\Schema(
 *     schema="ExportTemplateCriteria",
 *     type="object",
 *     description="Criteria for the selection",
 *                   @OA\Property(
 *                   property="some_field_without_filter",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="ALL")
 *               ),
 *               @OA\Property(
 *                   property="some_text_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="TEXT"),
 *                   @OA\Property(property="text", type="string", example="Some text to search")
 *               ),
 *               @OA\Property(
 *                   property="some_integer_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="RANGE"),
 *                   @OA\Property(property="from", type="integer", example=1),
 *                   @OA\Property(property="to", type="integer", example=50)
 *               ),
 *               @OA\Property(
 *                   property="some_integer_field_with_expr",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="EXPR"),
 *                   @OA\Property(property="expr", type="string", example=">10", description=">50, <20, =15")
 *               ),
 *               @OA\Property(
 *                   property="some_empty_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="WITHOUT")
 *               ),
 *                @OA\Property(
 *                   property="some_not_empty_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="WITH")
 *               ),
 *               @OA\Property(
 *                   property="some_multiple_select_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="SELECT"),
 *                   @OA\Property(
 *                       property="value",
 *                       type="object",
 *                       @OA\Property(property="propertyName1", type="string", example="Amazon FR"),
 *                       @OA\Property(property="propertyName2", type="string", example="Amazon SE"),
 *                       @OA\Property(property="propertyName3", type="string", example="Amazon DE")
 *                   )
 *               ),
 *               @OA\Property(
 *                   property="some_date_preset_select_field",
 *                   type="object",
 *                   @OA\Property(property="select", type="string", example="last_7_days")
 *               )
 * )
 */

/**
 * @OA\Schema(
 *     schema="DataExportTemplate",
 *     type="object",
 *     description="Configuration for data export",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Unique identifier of the export configuration",
 *         example=220
 *     ),
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the export configuration",
 *         example="My export template"
 *     ),
 *     @OA\Property(
 *         property="criteria",
 *         ref="#/components/schemas/ExportTemplateCriteria",
 *         description="Criteria for the selection"
 *     ),
 *     @OA\Property(
 *         property="fields",
 *         type="array",
 *         items={
 *             "type": "string"
 *         },
 *         nullable=true,
 *         description="Fields to be included in the export",
 *         example=null
 *     ),
 *     @OA\Property(
 *         property="format",
 *         type="string",
 *         description="Format of the export file",
 *         example="txt"
 *     ),
 *     @OA\Property(
 *         property="is_default",
 *         type="boolean",
 *         description="Indicates if this is the default export configuration",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="handler_name",
 *         type="string",
 *         description="Name of the handler",
 *         example="product_cost_periods"
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Timestamp when the export configuration was created",
 *         example="2023-08-20 09:03:30"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Timestamp when the export configuration was last updated",
 *         example="2023-08-20 09:03:30"
 *     )
 * )
 */

/**
 * @property int    $id
 * @property string $title
 * @property array $criteria
 * @property array $extra_criteria
 * @property array $fields
 * @property string $format
 * @property bool $is_default
 * @property bool $remove_after_export
 * @property string $handler_name
 * @property string $created_at
 * @property string $updated_at
 */
class DataExportTemplate extends AbstractCustomerRecord
{
    public const SCENARIO_STEP_FILTERS = 'step_filters';
    public static $dbId = 'customer';

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['timestamp'] = [
            'class' => TimestampBehavior::class,
            'value' => date('Y-m-d H:i:s')
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'format', 'handler_name'], 'required',
                'on' => [
                    self::SCENARIO_STEP_FILTERS,
                    self::SCENARIO_CREATE,
                    self::SCENARIO_UPDATE,
                ]
            ],
            [['criteria'], 'required',
                'on' => [
                    self::SCENARIO_CREATE,
                    self::SCENARIO_UPDATE,
                ]
            ],
            [['criteria'], 'filter',
                'filter' => function($value) { return $this->filterCriteria($value); },
                'when' => function() { return !empty($this->handler_name); },
                'skipOnEmpty' => true,
            ],
            [['fields'], 'filter',
                'filter' => function($value) { return $this->filterFields($value); },
                'when' => function() { return !empty($this->handler_name); },
                'skipOnEmpty' => true,
            ],
            [['title'], 'string', 'max' => 200],
            [['title'], 'filter', 'filter' => function ($value) {
                return \yii\helpers\HtmlPurifier::process($value);
            }],
            [['title'],
                'unique',
                'targetAttribute' => ['title', 'handler_name'],
                'message' => \Yii::t('admin', 'Such template name has been already taken'),
            ],
            [['format'], 'filter', 'filter' => 'strtolower'],
            [['format'], 'in', 'range' => SupportedFileFormats::EXPORT_SUPPORTED_FORMATS],
            [['handler_name'], 'in', 'range' => SupportedHandlers::EXPORT_SUPPORTED_HANDLERS],
            [['is_default'], 'default', 'value' => 0],
            [['title', 'format', 'handler_name', 'is_default'], 'safe', 'on'=>'search'],
        ];
    }

    public function filterCriteria(array $criteria): array
    {
        $filteredCriteria = [];
        $factory = new ExporterFactory();
        $exporter = $factory->getExporter($this->handler_name);
        $whiteListedFields = $exporter->getWhiteListedFields(new ExportConfig());

        foreach ($criteria as $k => $item) {
            if (!in_array($k, $whiteListedFields)) {
                continue;
            }

            $filteredCriteria[$k] = $item;
        }

        return $filteredCriteria;
    }

    public function filterFields(array $fields): array
    {
        $factory = new ExporterFactory();
        $exporter = $factory->getExporter($this->handler_name);
        $exportConfig = new ExportConfig();
        $exportConfig->fields = $fields;
        $whiteListedFields = $exporter->getWhiteListedFields($exportConfig);
        $filteredFields = array_intersect($whiteListedFields, $fields);

        return array_values($filteredFields);
    }

    public function beforeDelete()
    {
        if ($this->is_default) {
            throw new \Exception("Default template can not be deleted");
        }

        return parent::beforeDelete();
    }

    public function beforeSave($insert)
    {
        if ($this->is_default) {
            throw new \Exception("Default template can not be modified");
        }

        return parent::beforeSave($insert);
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_STEP_FILTERS] = [
            'title',
            'format',
            'handler_name',
            'fields'
        ];
        $scenarios[self::SCENARIO_CREATE] = [
            'title',
            'format',
            'handler_name',
            'fields',
            'criteria'
        ];
        $scenarios[self::SCENARIO_UPDATE] = [
            'title',
            'handler_name',
            'format',
            'fields',
            'criteria'
        ];
        return $scenarios;
    }

    public function attributeLabels()
    {
        $attributeLabels = parent::attributeLabels();
        $attributeLabels['title'] = \Yii::t('admin', 'Template name');

        return $attributeLabels;
    }

    public function search($params)
    {
        $query = self::find();

        $this->setScenario('search');
        $this->setAttributes($params);

        // grid filtering conditions
        $query
            ->andFilterCompare('handler_name', $this->handler_name)
            ->andFilterCompare('format', $this->format)
            ->andFilterCompare('title', $this->title, 'like')
        ;

        return $query;
    }
}
