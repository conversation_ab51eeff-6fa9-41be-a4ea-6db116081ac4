<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property string $marketplace_id
 * @property string $seller_id
 * @property string $sku
 * @property string $bsr
 * @property string $date
 * @property string $created_at
 * @property string $updated_at
 */
class ProductBsr extends AbstractCustomerRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
