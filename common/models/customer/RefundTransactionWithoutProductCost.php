<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use Yii;
use common\models\MainActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
* This is the API model class for table "refund_transaction_without_product_cost".
*
* @OA\Schema(
* schema="RefundTransactionWithoutProductCost",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="posted_date",
*      type="string",
*      description="Posted Date"
*   ),
*   @OA\Property(
*      property="marketplace_id",
*      type="string",
*      description="Marketplace ID"
*   ),
*   @OA\Property(
*      property="seller_id",
*      type="string",
*      description="Seller ID"
*   ),
*   @OA\Property(
*      property="seller_sku",
*      type="string",
*      description="Seller Sku"
*   ),
*   @OA\Property(
*      property="quantity",
*      type="integer",
*      description="Quantity"
*   ),
*   @OA\Property(
*      property="amazon_order_id",
*      type="string",
*      description="Amazon Order ID"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property string $posted_date
* @property string $marketplace_id
* @property string $seller_id
* @property string $seller_sku
* @property int $quantity
* @property string $amazon_order_id
* @property string $created_at
* @property string $updated_at
*/
class RefundTransactionWithoutProductCost extends AbstractCustomerRecord
{

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['posted_date', 'marketplace_id', 'seller_id', 'seller_sku', 'amazon_order_id'], 'required'],
            [['posted_date', 'created_at', 'updated_at'], 'safe'],
            [['quantity'], 'default', 'value' => null],
            [['quantity'], 'integer'],
            [['marketplace_id', 'seller_id', 'seller_sku', 'amazon_order_id'], 'string', 'max' => 255],
            [['id', 'posted_date', 'marketplace_id', 'seller_id', 'seller_sku', 'quantity', 'amazon_order_id', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = RefundTransactionWithoutProductCost::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('posted_date', $this->posted_date)
            ->andFilterCompare('marketplace_id', $this->marketplace_id, 'like')
            ->andFilterCompare('seller_id', $this->seller_id, 'like')
            ->andFilterCompare('seller_sku', $this->seller_sku, 'like')
            ->andFilterCompare('quantity', $this->quantity)
            ->andFilterCompare('amazon_order_id', $this->amazon_order_id, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);

        return $query;
    }


}
