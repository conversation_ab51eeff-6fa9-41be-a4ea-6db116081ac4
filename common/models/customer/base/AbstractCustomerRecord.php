<?php

namespace common\models\customer\base;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\models\MainActiveRecord;
use common\models\traits\SaveOrThrowException;
use yii\db\ActiveRecord;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

/**
 * Class AbstractCustomerRecord
 * @package common\models\customer\base
 *
 */
abstract class AbstractCustomerRecord extends MainActiveRecord
{
    use SaveOrThrowException;

    public static function tableName(){
        $schemaName = \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_CUSTOMER);
        $tableName = Inflector::camel2id(StringHelper::basename(get_called_class()), '_');
        return $schemaName . '.' . $tableName;
    }

    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        return $dbManager->getDb(static::$dbId, HelperFactory::TYPE_POSTGRESS_SHARD);
    }
}
