<?php

namespace common\models\customer;

use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;

class TransactionExtendedViewAggregate extends TransactionExtendedViewV1
{
    protected function getOrderBasedTableName(): string
    {
        return OrderBasedTransactionExtendedViewV1::tableName();
    }

    public static function tableName(): string
    {
        return TransactionExtendedViewV1::tableName();
    }

    public static function primaryKey(): array
    {
        return [
            "product_id",
            "marketplace_id",
            "seller_id",
            "seller_sku",
            "currency_id",
            "product_asin",
            "product_brand",
            "product_ean",
            "product_upc",
            "product_isbn",
            "product_title",
            "product_manufacturer",
            "product_type",
            "offer_type",
            "product_parent_asin",
            "product_stock_type",
            "product_adult",
            "tag_id",
            "revenue_amount",
            "expenses_amount",
            "total_income",
            "total_expenses",
            "expenses_amount_without_fees",
            "amazon_fees",
            "ppc_costs",
            "estimated_profit_amount",
            "net_purchase_price",
            "orders",
            "units",
            "refunds",
            "promo",
            "roi",
            "markup",
            "margin",
            "bsr_avg_curr",
            "bsr_avg_prev_compare_percents"
        ];
    }
}
