<?php

namespace common\models\customer;

use common\components\dataCompleteness\Checker;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\DataCompletenessFactor;
use Exception;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;

/**
 * @OA\Schema(
 *     schema="DataCompleteness",
 *     type="object",
 *     description="Status of the data export",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Unique identifier of the status",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="count_all",
 *         type="integer",
 *         description="Total count of items",
 *         example=32557
 *     ),
 *     @OA\Property(
 *         property="count_unfilled",
 *         type="integer",
 *         description="Count of unfilled items",
 *         example=29550
 *     ),
 *     @OA\Property(
 *         property="is_ignored",
 *         type="boolean",
 *         description="Indicates if the status is ignored",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="checked_at",
 *         type="string",
 *         format="date-time",
 *         description="Timestamp when the status was checked",
 *         example="2024-07-11 06:21:08"
 *     ),
 *     @OA\Property(
 *         property="fill_percentage",
 *         type="integer",
 *         description="Fill percentage",
 *         example=9
 *     ),
 *     @OA\Property(
 *         property="factor",
 *         ref="#/components/schemas/DataCompletenessFactor",
 *         description="Factor details"
 *     ),
 * )
 */

/**
 * @property int $id
 * @property string $factor_id
 * @property int $count_all
 * @property int $count_unfilled
 * @property bool $is_ignored
 * @property string $checked_at
 * @property string $updated_at
 * @property string $created_at
 */
class DataCompleteness extends AbstractCustomerRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function rules()
    {
        return [
            [['is_ignored'], 'boolean'],
            [['id'], 'safe', 'on'=>'search'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['update'] = [
            'is_ignored'
        ];
        return $scenarios;
    }

    public function search(array $params = []): ActiveQuery
    {
        $query = self::find();
        $this->setScenario('search');
        $this->setAttributes($params);

        $query->innerJoinWith('factor');
        $query->orderBy("CAST(sort_order AS INTEGER) ASC");

        return $query;
    }

    /**
     * @throws Exception
     */
    public function afterSave($insert, $changedAttributes)
    {
        if ($this->scenario === 'update') {
            if ($this->is_ignored && $this->count_all !== null && $this->count_unfilled !== null) {
                $this->count_all = null;
                $this->count_unfilled = null;
            } else {
                (new Checker())->check($this->factor_id);
            }
        }

        return parent::afterSave($insert, $changedAttributes);
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields[] = 'factor';
        $fields['fill_percentage'] = function () {
            if (empty($this->count_all)) {
                return 100;
            }

            return 100 - round(($this->count_unfilled / $this->count_all) * 100);
        };

        unset($fields['factor_id']);
        unset($fields['created_at']);
        unset($fields['updated_at']);

        return $fields;
    }

    public function getFactor(): ActiveQuery
    {
        return $this->hasOne(DataCompletenessFactor::class, ['id' => 'factor_id']);
    }
}
