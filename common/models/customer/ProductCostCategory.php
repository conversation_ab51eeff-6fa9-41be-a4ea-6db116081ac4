<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\Expression;
use yii\db\Query;

/**
* This is the API model class for table "product_cost_category".
*
* @OA\Schema(
* schema="ProductCostCategory",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="name",
*      type="string",
*      description="Name"
*   ),
 *   @OA\Property(
 *      property="is_default",
 *      type="integer",
 *      description="Is default"
 *   ),
 *   @OA\Property(
 *      property="is_editable",
 *      type="integer",
 *      description="Is editable"
 *   ),
*   @OA\Property(
*      property="source",
*      type="string",
*      description="Source"
*   ),
*   @OA\Property(
*      property="sales_category_id",
*      type="string",
*      description="Sales Category ID"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property string $name
* @property string $source
* @property string $sales_category_id
* @property boolean $is_default
* @property boolean $is_editable
* @property string $created_at
* @property string $updated_at
*/
class ProductCostCategory extends AbstractCustomerRecord
{
    public const INTERNAL_COG_PREFIX = 'internal_cog_';
    public const INTERNAL_INDIRECT_COST_PREFIX = 'internal_indirect_cost_';

    public const SOURCE_REPRICER = 'repricer';
    public const SOURCE_MANUAL = 'manual';
    public const SOURCE_DEFAULT_VAT = 'default_vat';

    public const COMMON_CACHE_TAG = 'product-cost-categories';

    public const NET_PURCHASE_PRICE_NAME = 'Net purchase price';

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['created_at', 'updated_at'], 'safe'],
            [['name'], 'required'],
            [['name'], 'unique'],
            [['name', 'source', 'sales_category_id'], 'string', 'max' => 255],
            [['source'], 'default', 'value' => self::SOURCE_MANUAL],
            [['id', 'name', 'source', 'sales_category_id', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public static function getNetPurchaseId(): int
    {
        return self::find()->select('id')
            ->where([
                'name' => self::NET_PURCHASE_PRICE_NAME
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->scalar();
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'name',
            'sales_category_id',
        ];
        $scenarios['update'] = [
            'name',
        ];

        return $scenarios;
    }

    public static function getCategoryIdsBySalesCategoryId(string $salesCategoryId)
    {
        return self::find()
            ->select('id')
            ->where(['=', 'sales_category_id', $salesCategoryId])
            ->cache(
                60,
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->orderBy('id ASC')
            ->column();
    }
    public static function getVatCategoryId(): int
    {
        return self::getCategoryIdsBySalesCategoryId(SalesCategory::CATEGORY_EXPENSES_TAXES)[0];
    }

    public function search($params)
    {
        $query = ProductCostCategory::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('name', $this->name, 'like')
            ->andFilterCompare('source', $this->source, 'like')
            ->andFilterCompare('sales_category_id', $this->sales_category_id, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);

        return $query;
    }

    public function getDefaultSort()
    {
        return [
            'is_default' => SORT_DESC,
            'is_editable' => SORT_ASC,
            'name' => SORT_ASC,
        ];
    }

    public function attributeLabels()
    {
        $attributeLabels = parent::attributeLabels();

        if (!in_array($this->sales_category_id, [
            SalesCategory::CATEGORY_EXPENSES_COG,
            SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
        ])) {
            $attributeLabels['name'] = \Yii::t('admin', 'Fee type');
        } else {
            $attributeLabels['name'] = \Yii::t('admin', 'Cost type');
        }

        return $attributeLabels;
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            TagDependency::invalidate(
                \Yii::$app->{SalesCategory::getDb()->queryCache},
                self::COMMON_CACHE_TAG
            );
        }
        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{SalesCategory::getDb()->queryCache},
            self::COMMON_CACHE_TAG
        );
        parent::afterDelete(); // TODO: Change the autogenerated stub
    }

    public static function getDefaultIdBySalesCategoryId(string $salesCategoryId): ?int
    {
        return self::find()
            ->select('id')
            ->where([
                'and',
                ['=', 'sales_category_id', $salesCategoryId],
                ['=', 'is_default', true]
            ])
            ->cache(
                60 * 60,
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->scalar();
    }

    public static function getAllBySalesCategoryId(string $salesCategoryId): array
    {
        return self::find()
            ->select(['id', 'name'])
            ->where([
                'and',
                ['=', 'sales_category_id', $salesCategoryId]
            ])
            ->cache(
                60 * 60,
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->all();
    }

    public function fields()
    {
        $fields = parent::fields();

        $fields['is_deletable'] = function () {
            return $this->isDeletable();
        };
        // FE recognize this column like "is predefined",
        // on BE - we recognize it as "is default" - single category used when nothing selected.
        $fields['is_default'] = function () {
            return !$this->is_editable;
        };

        return $fields;
    }

    public function isDeletable(): bool
    {
        if ($this->getScenario() === 'create') {
            return true;
        }

        $countCosts = ProductCostItem::find()
            ->where(['product_cost_category_id' => $this->id])
            ->count();

        if (0 !== $countCosts) {
            return false;
        }

        return $this->is_editable;
    }

    public function isEditable(): bool
    {
        return $this->is_editable;
    }
}
