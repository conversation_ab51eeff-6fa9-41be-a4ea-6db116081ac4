<?php

namespace common\models\customer;

use api\components\controllers\AllRecordsTrait;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\SalesCategory;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;
use yii\db\Expression;
use yii\db\Query;

/**
* @OA\Schema(
* schema="IndirectCostType",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="name",
*      type="string",
*      description="Name"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property string $name
* @property string $is_predefined
* @property string $created_at
* @property string $updated_at
*/
class IndirectCostType extends AbstractCustomerRecord
{
    use AllRecordsTrait;

    public const COMMON_CACHE_TAG = 'indirect-cost-types';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['name'], 'unique'],
            [['name'], 'string', 'max' => 255],
            [['id', 'name', 'created_at', 'updated_at'],'safe','on'=>'search'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = [
            'name'
        ];
        $scenarios[self::SCENARIO_UPDATE] = $scenarios[self::SCENARIO_CREATE];

        return $scenarios;
    }

    public function fields()
    {
        $fields = parent::fields();
        unset($fields['created_at']);
        unset($fields['updated_at']);

        $fields['is_predefined'] = function () {
            return $this->is_predefined;
        };
        $fields['is_being_used'] = function () {
            return $this->isBeingUsed();
        };
        $fields['is_deletable'] = function () {
            return $this->isDeletable();
        };

        return $fields;
    }

    public function search($params)
    {
        $query = IndirectCostType::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('name', $this->name, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('updated_at', $this->updated_at);
        $query->joinWith('indirectCosts');
        return $query;
    }

    public function beforeSave($insert)
    {
        if (!$this->isEditable()) {
            throw new \Exception(\Yii::t('admin', 'This cost type can not be modified'));
        }
        return true;
    }

    public function beforeDelete()
    {
        if (!$this->isDeletable()) {
            throw new \Exception(\Yii::t('admin', 'This cost type can not be deleted'));
        }
        return true;
    }

    public function isEditable(): bool
    {
        return !$this->isBeingUsed() && !$this->is_predefined;
    }

    public function isDeletable(): bool
    {
        return $this->isEditable();
    }

    public function isBeingUsed(): bool
    {
        return $this->getIndirectCosts()->count() > 0;
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            TagDependency::invalidate(
                \Yii::$app->{self::getDb()->queryCache},
                self::COMMON_CACHE_TAG
            );
        }
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            self::COMMON_CACHE_TAG
        );
        parent::afterDelete(); // TODO: Change the autogenerated stub
    }

    public function getIndirectCosts()
    {
        return $this->hasMany(IndirectCost::class, ['indirect_cost_type_id' => 'id']);
    }
}
