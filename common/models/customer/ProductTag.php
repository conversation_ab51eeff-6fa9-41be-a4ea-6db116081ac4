<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;

/**
 * @property int $id
 * @property int $product_id
 * @property int $tag_id
 */
class ProductTag extends AbstractCustomerRecord
{
    public function rules(): array
    {
        return [
            [['product_id', 'tag_id'], 'required'],
            [['product_id', 'tag_id'], 'integer'],
        ];
    }

    public function behaviors(): array
    {
        return [];
    }

    public function getProduct(): ActiveQuery
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }
}
