<?php

namespace common\models\customer;

use common\components\COGSync\COGChangesManager;
use common\components\COGSync\PeriodDateChangeCalculator;
use common\components\currencyRate\CurrencyRateManager;
use common\models\customer\base\AbstractCustomerRecord;
use common\models\SalesCategory;
use common\models\Seller;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;
use yii\db\Query;
use yii\db\StaleObjectException;

/**
* This is the API model class for table "product_cost_period".
*
* @OA\Schema(
* schema="ProductCostPeriod",
*   @OA\Property(
*      property="id",
*      type="integer",
*      description="ID"
*   ),
*   @OA\Property(
*      property="marketplace_id",
*      type="string",
*      description="Marketplace ID"
*   ),
*   @OA\Property(
*      property="sales_category_id",
*      type="string",
*      description="Sales Category Id"
*   ),
*   @OA\Property(
*      property="seller_sku",
*      type="string",
*      description="Seller Sku"
*   ),
*   @OA\Property(
*      property="date_start",
*      type="string",
*      description="Date Start"
*   ),
*   @OA\Property(
*      property="date_end",
*      type="string",
*      description="Date End"
*   ),
*   @OA\Property(
*      property="amount_total",
*      type="number",
*      description="Amount Total"
*   ),
*   @OA\Property(
*      property="created_at",
*      type="string",
*      description="Created At"
*   ),
*   @OA\Property(
*      property="updated_at",
*      type="string",
*      description="Updated At"
*   ),
* )

* @property int $id
* @property string $marketplace_id
* @property string $seller_id
* @property string $seller_sku
* @property string $source
* @property string $sales_category_id
* @property string $date_start
* @property string $date_end
* @property float|null $amount_total
* @property string $created_at
* @property string $updated_at
*/
class ProductCostPeriod extends AbstractCustomerRecord
{
    public static bool $isSyncMode = false;
    public bool $shouldSendCOGChanges = true;
    public bool $shouldAutoCalculateDateEnd = true;

    private CurrencyRateManager $currencyRateManager;

    public function __construct($config = [])
    {
        $this->currencyRateManager = new CurrencyRateManager();
        parent::__construct($config);
    }

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
    * {@inheritdoc}
    */
    public function rules()
    {
        return [
            [['date_start', 'created_at', 'updated_at'], 'safe'],
            [['date_start'], 'date' ,'type' => 'datetime', 'format' => 'php:Y-m-d H:i:s', 'skipOnEmpty' => true],
            [['amount_total'], 'number'],
            [['amount_total'], 'default', 'value' => 0],
            [['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id'], 'string', 'max' => 255],
            [['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id'], 'required'],
            [['sales_category_id'], 'in', 'range' => [
                SalesCategory::CATEGORY_EXPENSES_COG,
                SalesCategory::CATEGORY_EXPENSES_TAXES,
                SalesCategory::CATEGORY_EXPENSES_OTHER_FEES,
                SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
            ]],
            [['date_start'],
                function() {
                    if (!empty($this->date_start)) {
                        $this->date_start = date('Y-m-d 00:00:00', strtotime($this->date_start));
                    }
                }
            ],
            [['date_start'],
                'unique',
                'targetAttribute' => ['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id', 'date_start'],
                'message' => \Yii::t('admin','Such date start has already been used for anther period'),
                'skipOnEmpty' => false
            ],
            [
                [
                    'id',
                    'marketplace_id',
                    'seller_id',
                    'seller_sku',
                    'date_start',
                    'date_end',
                    'amount_total',
                    'sales_category_id',
                    'created_at',
                    'updated_at',
                    'items',
                    'is_current'
                ],
                'safe',
                'on'=>'search'
            ],
        ];
    }

    public function fields()
    {
        $fields = parent::fields();

        $fields['items'] = function (ProductCostPeriod $productCostPeriod) {
            return $productCostPeriod->getItems()->all();
        };

        $fields['source'] = function (ProductCostPeriod $productCostPeriod) {
            /** @var Product $product */
            $product = $this->getProduct()->one();

            if (empty($product)) {
                return $productCostPeriod->source;
            }

            if ($product->repricer_is_deleted && $this->isActivePeriod()) {
                return Product::SOURCE_MANUAL;
            }

            return $productCostPeriod->source;
        };

        $fields['is_current'] =  function (ProductCostPeriod $productCostPeriod) {
            return $this->isActivePeriod();
        };
        $fields['is_future'] =  function (ProductCostPeriod $productCostPeriod) {
            return $this->isFuturePeriod();
        };

        return $fields;
    }

    public function search($params)
    {
        $query = ProductCostPeriod::find();
        $query->addSelect([
            "product_cost_period.*",
            "COALESCE(date_start, '1900-01-01') as virtual_date_start",
            "COALESCE(date_end, '2100-01-01') as virtual_date_end",
        ]);

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query
            ->andFilterCompare('product_cost_period.id', $this->id)
            ->andFilterCompare('product_cost_period.marketplace_id', $this->marketplace_id)
            ->andFilterCompare('product_cost_period.seller_id', $this->seller_id)
            ->andFilterCompare('product_cost_period.seller_sku', $this->seller_sku)
            ->andFilterCompare('product_cost_period.sales_category_id', $this->sales_category_id)
            ->andFilterCompare('product_cost_period.date_start', $this->date_start)
            ->andFilterCompare('product_cost_period.date_end', $this->date_end)
            ->andFilterCompare('product_cost_period.amount_total', $this->amount_total)
            ->andFilterCompare('product_cost_period.created_at', $this->created_at)
            ->andFilterCompare('product_cost_period.updated_at', $this->updated_at);

        $query->joinWith('product');

        return $query;
    }

    public function getDefaultSort()
    {
        return [
            'virtual_date_start' => SORT_ASC,
            'virtual_date_end' => SORT_ASC,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getSortAttributes()
    {
        $attributes = parent::getSortAttributes();
        $attributes['date_start'] = [
            'asc' => ['virtual_date_start' => SORT_ASC],
            'desc' => ['virtual_date_start' => SORT_DESC],
        ];
        $attributes['date_end'] = [
            'asc' => ['virtual_date_end' => SORT_ASC],
            'desc' => ['virtual_date_end' => SORT_DESC],
        ];
        return $attributes;
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'marketplace_id',
            'seller_id',
            'seller_sku',
            'sales_category_id',
            'date_start'
        ];
        $scenarios['update'] = [
            'date_start'
        ];
        return $scenarios;
    }

    public function beforeSave($insert)
    {
        if (!$this->shouldAutoCalculateDateEnd) {
            return parent::beforeSave($insert);
        }

        if (!empty($this->date_start)) {
            $this->date_start = date('Y-m-d 00:00:00', strtotime($this->date_start));
        }

        $rightPeriod = $this->getRightPeriod($this->date_start);

        if (!empty($rightPeriod)) {
            $this->date_end = date('Y-m-d H:i:s', strtotime($rightPeriod->date_start) - 1);
        } else {
            $this->date_end = null;
        }

        $leftPeriod = $this->getLeftPeriod($this->date_start);
        if (empty($leftPeriod)) {
            $this->date_start = null;
        }

        return parent::beforeSave($insert);
    }

    public function removePeriods(\DateTime $dateStart, bool $shouldRemoveFuturePeriods = true): void
    {
        /** @var ProductCostPeriod[] $futurePeriods */
        $query = self::find()->where([
            'AND',
            ['=', 'marketplace_id', $this->marketplace_id],
            ['=', 'seller_id', $this->seller_id],
            ['=', 'seller_sku', $this->seller_sku],
            ['=', 'sales_category_id', $this->sales_category_id]
        ]);

        if ($shouldRemoveFuturePeriods) {
            $query->andWhere(['>=', 'date_start', $dateStart->format('Y-m-d 00:00:00')]);
        } else {
            $query->andWhere(['=', 'date_start', $dateStart->format('Y-m-d 00:00:00')]);
        }

        $futurePeriods = $query->all();

        foreach ($futurePeriods as $futurePeriod) {
            $futurePeriod->delete();
        }
    }

    public function beforeDelete()
    {
        if (!$this->isFuturePeriod()) {
            /** @var ProductCostItem[] $items */
            $items = $this->getItems()->all();

            foreach ($items as $item) {
                $item->shouldSendCogChanges = $this->shouldSendCOGChanges;
                $item->delete();
            }
        }

        return parent::beforeDelete();
    }

    public function afterDelete()
    {
        if (!$this->shouldAutoCalculateDateEnd) {
            parent::afterDelete();
            return;
        }

        $leftPeriod = $this->getLeftPeriod($this->date_start);

        if (!empty($leftPeriod)) {
            $rightPeriod = $this->getRightPeriod($this->date_start);

            $leftPeriod->date_end = !empty($rightPeriod)
                ? $rightPeriod->date_start
                : null;

            $leftPeriod->shouldSendCOGChanges = $this->shouldSendCOGChanges;

            $leftPeriod->save(false);
        }

        if (is_null($this->date_start)) {
            $rightPeriod = $this->getRightPeriod($this->date_start);

            if (!empty($rightPeriod)) {
                $rightPeriod->date_start = null;
                $rightPeriod->save();
            }
        }
        ProductCostItem::removeCachedItemsBySellerSku($this->seller_sku);
        parent::afterDelete();
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!self::$isSyncMode) {
            $this->updateProductAmountsIfNeed();
        }

        if (!$this->shouldAutoCalculateDateEnd) {
            parent::afterSave($insert, $changedAttributes);
            return;
        }

        if (array_key_exists('date_start', $changedAttributes)) {
            $this->correctDatesForNearestPeriods($changedAttributes);
        }

        $dateStartOrDateEndHasBeenChanged = array_key_exists('date_start', $changedAttributes)
            || array_key_exists('date_end', $changedAttributes);

        if ($dateStartOrDateEndHasBeenChanged) {
            $this->sendCOGChanges($changedAttributes);
            ProductCostItem::removeCachedItemsBySellerSku($this->seller_sku);
        }
    }

    /**
     * Correcting previous and current left and right period dates.
     *
     * @param $changedAttributes
     * @param bool $isJustCreatedFromCurrentTime
     * @return void
     */
    private function correctDatesForNearestPeriods($changedAttributes): void
    {
        $prevDateStart = $changedAttributes['date_start'] ?? $this->date_start;
        $newDateStart = $this->date_start ?? date('Y-m-d H:i:s');

        $prevLeftPeriod = $this->getLeftPeriod($prevDateStart);
        $prevRightPeriod = $this->getRightPeriod($prevDateStart);

        if (!empty($prevLeftPeriod)) {
            if (!empty($prevRightPeriod)) {
                if (strtotime($newDateStart) > strtotime($prevRightPeriod->date_start)
                    || strtotime($newDateStart) < strtotime($prevLeftPeriod->date_start)
                ) {
                    $prevLeftPeriod->date_end = date('Y-m-d H:i:s', strtotime($prevRightPeriod->date_start) - 1);
                } else {
                    $prevLeftPeriod->date_end = date('Y-m-d H:i:s', strtotime($newDateStart) - 1);
                }
            } else {
                if (strtotime($newDateStart) < strtotime($prevLeftPeriod->date_start)) {
                    $prevLeftPeriod->date_end = null;
                } else {
                    $prevLeftPeriod->date_end = date('Y-m-d H:i:s', strtotime($newDateStart) - 1);
                }
            }

            $prevLeftPeriod->save(false);
        }

        $leftPeriod = $this->getLeftPeriod($newDateStart);
        if (!empty($leftPeriod) && $leftPeriod->id !== ($prevLeftPeriod->id ?? null)) {
            $leftPeriod->date_end = date('Y-m-d H:i:s', strtotime($newDateStart) - 1);
            $leftPeriod->save(false);
        }
    }

    private function sendCOGChanges($changedAttributes): void
    {
        if (!$this->shouldSendCOGChanges) {
            return;
        }

        /** @var ProductCostItem[] $items */
        $items = $this->getItems()->all();

        $dateStartOld = array_key_exists('date_start', $changedAttributes)
            ? $changedAttributes['date_start']
            : $this->date_start;

        $dateEndOld = array_key_exists('date_end', $changedAttributes)
            ? $changedAttributes['date_end']
            : $this->date_end;
        $dateStartNew = $this->date_start;
        $dateEndNew = $this->date_end;

        /** @var COGChangesManager $COGChangesManager */
        $COGChangesManager = \Yii::$container->get('COGChangesManager');
        /** @var PeriodDateChangeCalculator $periodDateChangeCalculator */
        $periodDateChangeCalculator = \Yii::$container->get('periodDateChangeCalculator');

        $dateStartOld = $dateStartOld ?? '1900-01-01 00:00:00';
        $dateEndOld = $dateEndOld ?? date('Y-m-d H:i:s');
        $dateStartNew = $dateStartNew ?? '1900-01-01 00:00:00';
        $dateEndNew = $dateEndNew ?? date('Y-m-d H:i:s');

        foreach ($items as $item) {
            $item->shouldSendCogChanges = $this->shouldSendCOGChanges;

            if (empty($item->marketplace_currency_id)) {
                $item->save(false);
            }

            $COGChanges = $periodDateChangeCalculator->getCOGChanges(
                $dateStartOld,
                $dateEndOld,
                $dateStartNew,
                $dateEndNew,
                $item->marketplace_amount_per_unit,
            );

            foreach ($COGChanges as $COGChange) {
                $COGChangesManager->sendCOGChangesToQueue(
                    $COGChange['amountOld'],
                    $item->marketplace_currency_id,
                    $COGChange['amountNew'],
                    $item->marketplace_currency_id,
                    $COGChange['dateStart'],
                    $COGChange['dateEnd'],
                    $item,
                    $this,
                    self::$isSyncMode ? 1 : 5
                );
            }
        }
    }
    private function getCurrentActivePeriod(string $source): ?self
    {
        $query = self::find()->andWhere([
            'marketplace_id' => $this->marketplace_id,
            'seller_id' => $this->seller_id,
            'seller_sku' => $this->seller_sku,
            'sales_category_id' => $this->sales_category_id
        ]);
        $currDate = date('Y-m-d H:i:s');

        $query
            ->andWhere([
                'and',
                ['=', 'source', $source],
                [
                    'or',
                    ['<', 'date_start', $currDate],
                    ['is', 'date_start', new Expression('NULL')],
                ],
                [
                    'or',
                    ['>', 'date_end', $currDate],
                    ['is', 'date_end', new Expression('NULL')],
                ]
            ]);

        return $query->one();
    }

    public function getRightPeriod(?string $dateStart): ?self
    {
        if (empty($dateStart)) {
            $dateStart = '1900-01-01 00:00:00';
        }

        $query = self::find()->andWhere([
            'marketplace_id' => $this->marketplace_id,
            'seller_id' => $this->seller_id,
            'seller_sku' => $this->seller_sku,
            'sales_category_id' => $this->sales_category_id
        ]);

        if (!empty($this->id)) {
            $query->andWhere(['!=', 'id', $this->id]);
        }

        $query
            ->andWhere(['>', 'date_start', $dateStart])
            ->addOrderBy('date_start ASC');
        $rightPeriod = $query->one();

        if (!empty($rightPeriod)) {
            $rightPeriod->shouldSendCOGChanges = $this->shouldSendCOGChanges;
        }

        return $rightPeriod;
    }


    public function getRightPeriods(?string $dateStart): array
    {
        if (empty($dateStart)) {
            $dateStart = '1900-01-01 00:00:00';
        }

        $query = self::find()->andWhere([
            'marketplace_id' => $this->marketplace_id,
            'seller_id' => $this->seller_id,
            'seller_sku' => $this->seller_sku,
            'sales_category_id' => $this->sales_category_id
        ]);

        $query
            ->andWhere(['>', 'date_start', $dateStart])
            ->addOrderBy('date_start ASC')
            ->joinWith('items');
        return $query->all();
    }

    /**
     * @return ProductCostPeriod[]
     */
    public static function getExistingPeriods(
        string $marketplaceId,
        string $sellerId,
        string $sellerSku,
        ?string $salesCategoryId = null,
        bool $sortAsc = true
    ): array
    {
        $query = ProductCostPeriod::find()->where([
            'AND',
            ['=', 'marketplace_id', $marketplaceId],
            ['=', 'seller_id', $sellerId],
            ['=', 'seller_sku', $sellerSku],
        ]);
        if ($salesCategoryId !== null) {
            $query->andWhere(['sales_category_id' => $salesCategoryId]);
        }
        $query
            ->joinWith('items')
            ->cache(5);

        if ($sortAsc) {
            $query->orderBy(new Expression('date_start IS NULL, date_start DESC'));
        } else {
            $query->orderBy(new Expression('date_start IS NULL DESC, date_start ASC'));
        }

        /** @var ProductCostPeriod[] $data */
        $data = $query->all();

        $groupedData = [];
        foreach ($data as $item) {
            $groupedData[$item['sales_category_id']][$item->date_start] = $item;
        }

        if (null !== $salesCategoryId && isset($groupedData[$salesCategoryId])) {
            return $groupedData[$salesCategoryId];
        }

        return $groupedData;
    }

    public static function getAllPeriods(): Query
    {
        return ProductCostPeriod::find()
            ->select(['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id'])
            ->cache(5)
            ->groupBy(['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id']);
    }

    /**
     * @param string $marketplaceId
     * @param string $sellerId
     * @param string $sellerSku
     * @param string $salesCategoryId
     * @param string $dateStart
     * @return ProductCostPeriod|null
     */
    public static function getExistingPeriod(
        string $marketplaceId,
        string $sellerId,
        string $sellerSku,
        string $salesCategoryId,
        string $dateStart
    ): ?self
    {
        return ProductCostPeriod::find()->where([
                'AND',
                ['=', 'marketplace_id', $marketplaceId],
                ['=', 'seller_id', $sellerId],
                ['=', 'seller_sku', $sellerSku],
                ['=', 'sales_category_id', $salesCategoryId],
                ['<=', 'date_start', $dateStart],
            ])
            ->cache(5)
            ->joinWith('items')
            ->orderBy(new Expression('date_start IS NULL, date_start DESC'))
            ->one();
    }

    /**
     * @throws StaleObjectException
     * @throws \Throwable
     */
    public function removeIsNullPeriod()
    {
        if (count($this->getItems()->all()) === 0) {
            $this->delete();
        }
    }

    public function getLeftPeriod(?string $dateStart): ?self
    {
        if (empty($dateStart)) {
            return null;
        }

        $query = self::find()->alias('pcp')->andWhere([
            'marketplace_id' => $this->marketplace_id,
            'seller_id' => $this->seller_id,
            'seller_sku' => $this->seller_sku,
            'sales_category_id' => $this->sales_category_id
        ]);

        if (!empty($this->id)) {
            $query->andWhere(['!=', 'pcp.id', $this->id]);
        }

        $query
            ->andWhere([
                'or',
                ['<', 'date_start', $dateStart],
                ['is', 'date_start', new Expression('NULL')],
            ])
            ->joinWith('items')
            // Trick to put NULL values in the end
            ->orderBy(new Expression('date_start ISNULL, date_start DESC'));
        $leftPeriod = $query->one();

        if (!empty($leftPeriod)) {
            $leftPeriod->shouldSendCOGChanges = $this->shouldSendCOGChanges;
        }

        return $leftPeriod;
    }

    public function makeDuplicate(): ProductCostPeriod
    {
        $duplicate = new ProductCostPeriod();
        $duplicate->marketplace_id = $this->marketplace_id;
        $duplicate->seller_id = $this->seller_id;
        $duplicate->seller_sku = $this->seller_sku;
        $duplicate->source = $this->source;
        $duplicate->date_start = $this->date_start;
        $duplicate->date_end = $this->date_end;
        $duplicate->amount_total = $this->amount_total;
        $duplicate->sales_category_id = $this->sales_category_id;

        return $duplicate;
    }

    public function isActivePeriod(): bool
    {
        $currentTime = time();
        return (empty($this->date_start) || strtotime($this->date_start) <= $currentTime)
            && (empty($this->date_end) || strtotime($this->date_end) >= $currentTime);
    }

    public function isFuturePeriod(): bool
    {
        return !empty($this->date_start) && strtotime($this->date_start) > time();
    }

    public function recalculateTotalAmount()
    {
        $this->amount_total = 0;
        /** @var ProductCostItem[] $items */
        $items = $this->getItems()->noCache()->all();
        /** @var Product $product */
        $product = $this->getProduct()->noCache()->one();

        foreach ($items as $item) {
            $item->shouldSendCogChanges = $this->shouldSendCOGChanges;
            if (empty($product->currency_code)) {
                continue;
            }
            $this->amount_total += $item->marketplace_amount_per_unit * $item->units;
        }
        $this->save(false);
        $this->updateProductAmountsIfNeed($product);
        ProductCostItem::removeCachedItemsBySellerSku($this->seller_sku);
    }

    public function updateProductAmountsIfNeed(Product $product = null): void
    {
        if (!$this->isActivePeriod()) {
            return;
        }

        if (empty($product)) {
            /** @var Product $product */
            $product = $this->getProduct()->noCache()->one();
        }

        if (empty($product)) {
            return;
        }

        $isSomethingChanged = false;

        if ($this->sales_category_id === SalesCategory::CATEGORY_EXPENSES_COG
            && ((float)$product->buying_price !== (float)$this->amount_total || null === $product->buying_price)
        ) {
            $product->buying_price = $this->amount_total;
            $isSomethingChanged = true;
        }

        if ($this->sales_category_id === SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
            && (
                (float)$product->shipping_cost !== (float)$this->amount_total
                || null === $product->shipping_cost
            )
        ) {
            $product->shipping_cost = $this->amount_total;
            $isSomethingChanged = true;
        }

        if ($this->sales_category_id === SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
            && $product->stock_type == Product::STOCK_TYPE_FBA
            && !$product->is_multiple_stock_type
            && $product->shipping_cost !== null
        ) {
            $product->shipping_cost = null;
            $isSomethingChanged = true;
        }

        if ($this->sales_category_id === SalesCategory::CATEGORY_EXPENSES_OTHER_FEES
            && ((float)$product->other_fees !== (float)$this->amount_total || null === $product->other_fees)
        ) {
            $product->other_fees = $this->amount_total;
            $isSomethingChanged = true;
        }

        if ($this->sales_category_id === SalesCategory::CATEGORY_EXPENSES_TAXES
            && ((float)$product->vat !== (float)$this->amount_total || null === $product->vat)
        ) {
            $product->vat = $this->amount_total;
            $isSomethingChanged = true;
        }

        if ($isSomethingChanged) {
            $product->save(false);
        }
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getItems()
    {
        return $this->hasMany(ProductCostItem::class, ['product_cost_period_id' => 'id'])->noCache();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        $q = $this->hasOne(Product::class, [
            'marketplace_id' => 'marketplace_id',
            'seller_id' => 'seller_id',
            'sku' => 'seller_sku',
        ]);

        return $q;
    }
}
