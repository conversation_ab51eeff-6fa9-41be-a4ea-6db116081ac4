<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use common\models\MainActiveRecord;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

class RepricerEventBuffer extends MainActiveRecord
{
    public static $dbId = 'repricerEventDb';

    public static function tableName(): string
    {
        $schemaName = \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_REPRICER_EVENT);
        $tableName = Inflector::camel2id(StringHelper::basename(get_called_class()), '_');
        return $schemaName . '.' . $tableName;
    }
}
