<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "fba_return".
 *
 * @property int $id
 * @property string $return_date
 * @property string $order_id
 * @property string $sku
 * @property string $asin
 * @property string $fnsku
 * @property string|null $product_name
 * @property int|null $quantity
 * @property string|null $fulfillment_center_id
 * @property string|null $detailed_disposition
 * @property string|null $reason
 * @property string|null $status
 * @property string|null $license_plate_number
 * @property string|null $customer_comments
 * @property string $moved_to_clickhouse_at
 * @property string $created_at
 * @property string $updated_at
 */
class FbaReturn extends AbstractCustomerRecord
{
    public const DETAILS_DISPOSITION_SELLABLE = 'SELLABLE';

    public const STATUS_REIMBURSED = 'Reimbursed';
    public const STATUS_REPACKAGED_SUCCESSFULLY = 'Repackaged Successfully';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public static function getSellableQuery(): ActiveQuery
    {
        return self::find()
            ->where([
                'OR',
                ['=', 'detailed_disposition', FbaReturn::DETAILS_DISPOSITION_SELLABLE],
                ['in', 'status', [
                    FbaReturn::STATUS_REIMBURSED,
                    FbaReturn::STATUS_REPACKAGED_SUCCESSFULLY
                ]]
            ])
            ->orderBy('return_date desc, id desc');
    }
}
