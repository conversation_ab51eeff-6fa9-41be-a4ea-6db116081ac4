<?php

namespace common\models\customer;

use common\models\customer\base\AbstractCustomerRecord;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "fbm_return".
 *
 * @property int $id
 * @property string $order_id
 * @property string $order_date
 * @property string $return_request_date
 * @property string|null $return_request_status
 * @property string|null $amazon_rma_id
 * @property string|null $label_type
 * @property float|null $label_cost
 * @property string|null $currency_code
 * @property string|null $return_carrier
 * @property string|null $tracking_id
 * @property string|null $label_to_paid_by
 * @property bool|null $a_to_z_claim
 * @property bool|null $is_prime
 * @property string|null $asin
 * @property string|null $merchant_sku
 * @property string|null $item_name
 * @property int|null $return_quantity
 * @property string|null $return_reason
 * @property bool|null $in_policy
 * @property string|null $return_type
 * @property string|null $resolution
 * @property float|null $order_amount
 * @property int|null $order_quantity
 * @property float|null $refund_amount
 * @property string $created_at
 * @property string $updated_at
 */
class FbmReturn extends AbstractCustomerRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
