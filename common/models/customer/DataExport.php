<?php

namespace common\models\customer;

use common\components\dataImportExport\SupportedHandlers;
use common\components\fileDataWriter\DataWriterFactory;
use common\models\DataExportRecurrent;
use common\models\DbStructure;
use common\models\traits\LastId;

/**
 * This is the API model class for table "data_export".
 *
 * @OA\Schema(
 * schema="DataExport",
 *   @OA\Property(
 *       property="id",
 *       type="integer",
 *       example=31953
 *   ),
 *   @OA\Property(
 *       property="handler_name",
 *       type="string",
 *       example="product_cost_periods"
 *   ),
 *   @OA\Property(
 *       property="status",
 *       type="string",
 *       example="done"
 *   ),
 *   @OA\Property(
 *       property="output_format",
 *       type="string",
 *       example="csv"
 *   ),
 *   @OA\Property(
 *       property="count_parts",
 *       type="integer",
 *       example=0
 *   ),
 *   @OA\Property(
 *       property="count_all_items",
 *       type="integer",
 *       example=1600715
 *   ),
 *   @OA\Property(
 *       property="count_exported_items",
 *       type="integer",
 *       example=1598720
 *   ),
 *   @OA\Property(
 *       property="count_errors",
 *       type="integer",
 *       example=0
 *   ),
 *   @OA\Property(
 *       property="file_url",
 *       type="string",
 *       example="https://sellerlogic-dev-files.s3.eu-west-1.amazonaws.com/customer_00019.data_export/31953/product_costs_export_2024-06-04_13-13-48.csv"
 *   ),
 *   @OA\Property(
 *       property="started_at",
 *       type="string",
 *       format="date-time",
 *       example="2024-06-04 12:47:21"
 *   ),
 *   @OA\Property(
 *       property="finished_at",
 *       type="string",
 *       format="date-time",
 *       example="2024-06-04 13:14:10"
 *   ),
 *   @OA\Property(
 *       property="language_code",
 *       type="string",
 *       example="en"
 *   ),
 *   @OA\Property(
 *       property="type",
 *       type="string",
 *       example="auto"
 *   ),
 *   @OA\Property(
 *       property="recurrent_data_export_id",
 *       type="integer",
 *       example=87
 *   ),
 *   @OA\Property(
 *       property="created_at",
 *       type="string",
 *       format="date-time",
 *       example="2024-06-04 12:00:37"
 *   ),
 *   @OA\Property(
 *       property="updated_at",
 *       type="string",
 *       format="date-time",
 *       example="2024-06-04 13:11:10"
 *   ),
 *   @OA\Property(
 *       property="template_id",
 *       type="integer",
 *       example=82
 *   ),
 * )
 * @property int              $id
 * @property string           $handler_name
 * @property string           $status
 * @property string           $output_format
 * @property int              $template_id
 * @property int              $count_parts
 * @property int              $count_all_items
 * @property int              $count_exported_items
 * @property int              $count_errors
 * @property string           $file_url
 * @property int              $recurrent_data_export_id
 * @property string           $exception
 * @property string           $log
 * @property string           $created_at
 * @property string           $started_at
 * @property string           $finished_at
 * @property string           $updated_at
 * @property string           $language_code
 * @property string           $type
 * @property DataExportPart[] $dataExportParts
 */
class DataExport extends BaseDataImportExport
{
    use LastId;

    public ?string $output_file_format = null;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['template_id'], 'filter', 'filter' => function($value) {
                if (!empty($value)) {
     return $value;
                }

                return DataExportTemplate::find()->select('id')->where([
     'is_default' => 't'
                ])->scalar() ?? null;
            }],
            [['handler_name', 'template_id'], 'required'],
            [['handler_name'], 'in', 'range' => SupportedHandlers::EXPORT_SUPPORTED_HANDLERS],
            [['status', 'output_format', 'exception', 'type'], 'string'],
            [['count_parts', 'count_all_items', 'count_exported_items', 'count_errors'], 'integer'],
            [['output_file_format', 'handler_name', 'output_format', 'template_id'], 'safe'],
            [['handler_name'], 'string', 'max' => 100],
            [['file_url'], 'string', 'max' => 400],
            [['template_id'], 'exist', 'skipOnError' => true, 'targetClass' => DataExportTemplate::class, 'targetAttribute' => ['template_id' => 'id']],
            [['id', 'handler_name', 'status', 'output_format', 'output_file_format', 'count_parts', 'count_all_items', 'count_exported_items', 'count_errors', 'file_url', 'template_id', 'exception', 'created_at', 'started_at', 'finished_at', 'updated_at', 'type'], 'safe', 'on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DataExport::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id, '=', 'int')
            ->andFilterCompare('handler_name', $this->handler_name)
            ->andFilterCompare('type', $this->type)
            ->andFilterCompare('status', $this->status)
            ->andFilterCompare('output_format', $this->output_format)
            ->andFilterCompare('count_all_items', $this->count_all_items, '=', 'int')
            ->andFilterCompare('count_exported_items', $this->count_exported_items, '=', 'int')
            ->andFilterCompare('count_errors', $this->count_errors, '=', 'int')
        ;

        $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $this->applyBetweenDateFilter($query, 'started_at', $this->started_at);
        $this->applyBetweenDateFilter($query, 'finished_at', $this->finished_at);
        $this->applyBetweenDateFilter($query, 'updated_at', $this->updated_at);

        return $query;
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!empty($this->file_url) && !empty($this->recurrent_data_export_id)) {
            /** @var DataExportRecurrent $dataExportRecurrent */
            $dataExportRecurrent = DataExportRecurrent::find()
                ->where([
     'id' => $this->recurrent_data_export_id,
     'customer_id' => \Yii::$app->dbManager->getCustomerId(),
                ])->one();

            if (!empty($dataExportRecurrent)) {
                $dataExportRecurrent->auto_export_url = $this->file_url;
                $dataExportRecurrent->save(false);
            }
        }

        parent::afterSave($insert, $changedAttributes);
    }

    public function afterValidate()
    {
        /** @var DataExportTemplate $template */
        $template = $this->getTemplate()->one();

        if (!empty($template)) {
            $this->output_file_format = $template->format;
        }

        return parent::beforeValidate();
    }

    /**
     * {@inheritdoc}
     */
    public function fields()
    {
        $fields = parent::fields();
        unset($fields['exception']);

        return $fields;
    }

    public function getS3Folder(): string
    {
        return $this->tableName() . '/' . $this->id;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDataExportParts()
    {
        return $this->hasMany(DataExportPart::class, ['data_export_id' => 'id']);
    }

    public function getTemplate()
    {
        return $this->hasOne(DataExportTemplate::class, ['id' => 'template_id']);
    }

    public function getLanguageCode(): string
    {
        return $this->language_code ?? 'en';
    }
}
