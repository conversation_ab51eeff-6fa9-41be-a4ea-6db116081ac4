<?php

namespace common\models\customer;

/**
 * This is the API model class for table "data_export_part".
 *
 * @OA\Schema(
 * schema="DataExportPart",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="part_no",
 *      type="integer",
 *      description="Part No"
 *   ),
 *   @OA\Property(
 *      property="data_export_id",
 *      type="integer",
 *      description="Data Export ID"
 *   ),
 *   @OA\Property(
 *      property="status",
 *      type="string",
 *      description="Status"
 *   ),
 *   @OA\Property(
 *      property="limit",
 *      type="integer",
 *      description="Limit"
 *   ),
 *   @OA\Property(
 *      property="offset",
 *      type="integer",
 *      description="Offset"
 *   ),
 *   @OA\Property(
 *      property="count_all_items",
 *      type="integer",
 *      description="Count All Items"
 *   ),
 *   @OA\Property(
 *      property="count_exported_items",
 *      type="integer",
 *      description="Count Exported Items"
 *   ),
 *   @OA\Property(
 *      property="count_errors",
 *      type="integer",
 *      description="Count Errors"
 *   ),
 *   @OA\Property(
 *      property="file_url",
 *      type="string",
 *      description="File Url"
 *   ),
 *   @OA\Property(
 *      property="exception",
 *      type="string",
 *      description="Exception"
 *   ),
 *   @OA\Property(
 *      property="memory_usage",
 *      type="string",
 *      description="Memory Usage"
 *   ),
 *   @OA\Property(
 *      property="peak_memory_usage",
 *      type="string",
 *      description="Peak Memory Usage"
 *   ),
 *   @OA\Property(
 *      property="duration_ms",
 *      type="string",
 *      description="Duration Ms"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created At"
 *   ),
 *   @OA\Property(
 *      property="started_at",
 *      type="string",
 *      description="Started At"
 *   ),
 *   @OA\Property(
 *      property="finished_at",
 *      type="string",
 *      description="Finished At"
 *   ),
 *
 *   @OA\Property(property="dataExport", type="object", ref="#/components/schemas/DataExport"),
 * )

 * @property int    $id
 * @property int    $part_no
 * @property int    $data_export_id
 * @property string $status
 * @property int    $offset
 * @property int    $limit
 * @property int    $count_all_items
 * @property int    $count_exported_items
 * @property int    $count_errors
 * @property array  $errors
 * @property string $file_url
 * @property string $exception
 * @property string $memory_usage
 * @property string $peak_memory_usage
 * @property string $duration_ms
 * @property string $created_at
 * @property string $started_at
 * @property string $finished_at
 *
 * @property DataExport $dataExport
 */
class DataExportPart extends BaseDataImportExport
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['part_no', 'data_export_id', 'offset', 'limit', 'count_all_items', 'count_exported_items', 'count_errors'], 'integer'],
            [['data_export_id', 'created_at'], 'required'],
            [['status', 'exception'], 'string'],
            [['memory_usage', 'peak_memory_usage', 'duration_ms'], 'number'],
            [['created_at', 'started_at', 'finished_at'], 'safe'],
            [['file_url'], 'string', 'max' => 400],
            [['data_export_id'], 'exist', 'skipOnError' => true, 'targetClass' => DataExport::className(), 'targetAttribute' => ['data_export_id' => 'id']],
            [['id', 'part_no', 'data_export_id', 'status', 'offset', 'count_all_items', 'count_exported_items', 'count_errors', 'file_url', 'exception', 'memory_usage', 'peak_memory_usage', 'duration_ms', 'created_at', 'started_at', 'finished_at'], 'safe', 'on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DataExportPart::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('part_no', $this->part_no)
            ->andFilterCompare('data_export_id', $this->data_export_id)
            ->andFilterCompare('status', $this->status, 'like')
            ->andFilterCompare('offset', $this->offset)
            ->andFilterCompare('count_all_items', $this->count_all_items)
            ->andFilterCompare('count_exported_items', $this->count_exported_items)
            ->andFilterCompare('count_errors', $this->count_errors)
            ->andFilterCompare('file_url', $this->file_url, 'like')
            ->andFilterCompare('exception', $this->exception, 'like')
            ->andFilterCompare('memory_usage', $this->memory_usage)
            ->andFilterCompare('peak_memory_usage', $this->peak_memory_usage)
            ->andFilterCompare('duration_ms', $this->duration_ms)
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('started_at', $this->started_at)
            ->andFilterCompare('finished_at', $this->finished_at);

        return $query;
    }

    public function getS3Folder(): string
    {
        /** @var DataExport $dataExport */
        $dataExport = $this->getDataExport()->one();
        return $dataExport->getS3Folder() . '/parts/' . $this->id;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDataExport()
    {
        return $this->hasOne(DataExport::className(), ['id' => 'data_export_id']);
    }
}
