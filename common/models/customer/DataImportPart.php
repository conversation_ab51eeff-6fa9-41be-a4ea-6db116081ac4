<?php

namespace common\models\customer;

use common\components\core\db\dbManager\DbManager;
use yii\behaviors\TimestampBehavior;/**
 * This is the API model class for table "data_import_part".
 *
 * @OA\Schema(
 * schema="DataImportPart",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="data_import_id",
 *      type="integer",
 *      description="Data Import ID"
 *   ),
 *   @OA\Property(
 *      property="status",
 *      type="string",
 *      description="Status"
 *   ),
 *   @OA\Property(
 *      property="exception",
 *      type="string",
 *      description="Exception description"
 *   ),
 *   @OA\Property(
 *      property="count_all_items",
 *      type="integer",
 *      description="Count All Items"
 *   ),
 *   @OA\Property(
 *      property="count_imported_items",
 *      type="integer",
 *      description="Count Finished Items"
 *   ),
 *   @OA\Property(
 *      property="count_errors",
 *      type="integer",
 *      description="Count Errors"
 *   ),
 *   @OA\Property(
 *      property="file_url",
 *      type="string",
 *      description="File Url"
 *   ),
 *   @OA\Property(
 *      property="created_at",
 *      type="string",
 *      description="Created At"
 *   ),
 *   @OA\Property(
 *      property="started_at",
 *      type="string",
 *      description="Started At"
 *   ),
 *   @OA\Property(
 *      property="finished_at",
 *      type="string",
 *      description="Finished At"
 *   ),
 *
 *   @OA\Property(property="dataImport", type="object", ref="#/components/schemas/DataImport"),
 * )
 * @property int    $id
 * @property int    $data_import_id
 * @property string $status
 * @property int    $count_all_items
 * @property int    $count_imported_items
 * @property int    $count_errors
 * @property string $errors
 * @property int    $offset
 * @property string $exception
 * @property string $log
 * @property int    $part_no
 * @property string $file_url
 * @property string $created_at
 * @property string $started_at
 * @property string $finished_at
 *
 * @property DataImport $dataImport
 */
class DataImportPart extends BaseDataImportExport
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['data_import_id'], 'required'],
            [['data_import_id', 'count_all_items', 'count_imported_items', 'count_errors', 'offset', 'part_no'], 'integer'],
            [['status'], 'string'],
            [['file_url'], 'string', 'max' => 400],
            [['data_import_id'], 'exist', 'skipOnError' => true, 'targetClass' => DataImport::className(), 'targetAttribute' => ['data_import_id' => 'id']],
            [['id', 'data_import_id', 'status', 'count_all_items', 'count_imported_items', 'count_errors', 'file_url', 'created_at', 'started_at', 'finished_at'], 'safe', 'on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DataImportPart::find();

        $this->setScenario('search');

        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('data_import_id', $this->data_import_id)
            ->andFilterCompare('status', $this->status, 'like')
            ->andFilterCompare('count_all_items', $this->count_all_items)
            ->andFilterCompare('count_imported_items', $this->count_imported_items)
            ->andFilterCompare('count_errors', $this->count_errors)
            ->andFilterCompare('file_url', $this->file_url, 'like')
            ->andFilterCompare('created_at', $this->created_at)
            ->andFilterCompare('started_at', $this->started_at)
            ->andFilterCompare('finished_at', $this->finished_at);

        return $query;
    }

    public function getS3Folder(): string
    {
        /** @var DataImport $dataImport */
        $dataImport = $this->getDataImport()->one();
        return $dataImport->getS3Folder() . '/parts/' . $this->id;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDataImport()
    {
        return $this->hasOne(DataImport::class, ['id' => 'data_import_id']);
    }
}
