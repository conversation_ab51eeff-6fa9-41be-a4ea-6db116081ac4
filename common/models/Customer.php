<?php

namespace common\models;

use common\models\traits\SaveOrThrowException;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;

/**
 * @property int $id
 * @property bool $is_demo
 * @property bool $is_vcs_enabled
 * @property string $created_at
 * @property string $updated_at
 * @property bool $is_repricer_sync
 * @property bool $is_sync_default
 */
class Customer extends MainActiveRecord
{
    use SaveOrThrowException;

    public const COMMON_CACHE_TAG = 'model_customer';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            self::COMMON_CACHE_TAG
        );
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            self::COMMON_CACHE_TAG
        );
        parent::afterDelete();
    }
}
