<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;

/**
 * @property int $id
 * @property int $ip
 * @property string $type
 * @property string $status
 * @property string $log
 * @property ?int $customer_id
 * @property string $last_state_check_at
 * @property string $created_at
 * @property string $updated_at
 */
class NodesAvailability extends MainActiveRecord
{
    static bool $ignoreFilterByCustomer = true;

    public const COMMON_CACHE_TAG = 'nodes_availability';

    public const TYPE_CLICKHOUSE_CUSTOMER_DB = 'clickhouse_customer_db';
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            self::getCacheTag($this->type, $this->customer_id)
        );
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            self::getCacheTag($this->type, $this->customer_id)
        );
        parent::afterDelete();
    }

    public function setStatus(string $status, string $message = null): void
    {
        if ($this->status === $status) {
            return;
        }

        $this->status = $status;
        $this->last_state_check_at = date('Y-m-d H:i:s');
        $this->log("Status changed to {$status}. {$message}");
    }

    public function log(string $message): void
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }

    public static function getCacheTag(string $type, int $customerId = null): string
    {
        return self::COMMON_CACHE_TAG . '_' . $type . '_' . (int)$customerId;
    }
}