<?php

namespace common\models;

use common\components\cron\GeneralCronComponent;
use common\components\LinuxCommander;
use common\models\traits\SaveOrThrowException;
use yii\db\ActiveRecord;

/**
 * This is the API model class for table "command".
 * @property int    $id
 * @property string $command
 * @property string $status
 * @property int $retries
 * @property int $max_retries
 * @property string $log
 * @property string $created_at
 * @property string $processed_at
 */
class Command extends MainActiveRecord
{
    use SaveOrThrowException;

    const STATUS_NEW = 'new';
    const STATUS_QUEUED = 'queued';
    const STATUS_IN_PROGRESS = 'in-progress';
    const STATUS_FINISHED = 'finished';
    const STATUS_FAILED = 'failed';

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['command', 'log'], 'string'],
            [['created_at', 'processed_at'], 'safe'],
            [['status'], 'string', 'in', 'range' => [self::STATUS_NEW, self::STATUS_IN_PROGRESS, self::STATUS_FINISHED, self::STATUS_FAILED]],
        ];
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->created_at = date('Y-m-d H:i:s');
            $this->log("Status: " . $this->status . ' (just created)');
        }

        return parent::beforeSave($insert);
    }

    /**
     * @param string $command
     * @return void
     * @throws \Exception
     */
    public static function create(
        string $command,
        int $maxRetries = 1,
        bool $checkAlreadyInProgress = false,
        bool $isInBackground = false
    ) {
        if ($isInBackground) {
            LinuxCommander::safeExecute(
                sprintf("php %s/yii ", \Yii::getAlias('@app') . '/..'),
                sprintf(
                    "command/create '%s' %d %d",
                    $command,
                    $maxRetries,
                    $checkAlreadyInProgress
                ),
                false,
                false
            );
            return;
        }

        if ($checkAlreadyInProgress) {
            $countInProgress = Command::find()
                ->where([
                    'command' => $command,
                    'status' => [
                        self::STATUS_NEW,
                        self::STATUS_IN_PROGRESS,
                        self::STATUS_QUEUED
                    ]
                ])
                ->andWhere([
                    '>=',
                    'created_at',
                    date('Y-m-d H:i:s', time() - 60 * 15)
                ])
                ->noCache()
                ->count()
            ;

            if ($countInProgress > 0) {
                return;
            }
        }

        $instance = new self;
        $instance->max_retries = $maxRetries;
        $instance->command = $command;
        $instance->status = self::STATUS_NEW;
        $instance->saveOrThrowException();
        $instance->markQueued();

        /** @var GeneralCronComponent $cronComponent */
        $cronComponent = \Yii::$app->cronComponent;
        $cronComponent->addCommand($instance->getInlineCommand());
    }

    public function markInProgress(string $message = null)
    {
        if ($this->status != self::STATUS_QUEUED) {
            return;
        }

        $this->retries++;
        $this->status = self::STATUS_IN_PROGRESS;
        $this->log("Status: " . $this->status . ($message ? " ($message)" : ''));
        $this->save(false, ['status', 'log', 'retries']);
    }

    public function markFinished()
    {
        if ($this->status != self::STATUS_IN_PROGRESS) {
            return;
        }

        $this->status = self::STATUS_FINISHED;
        $this->processed_at = (new \DateTime())->format('Y-m-d H:i:s');
        $this->log("Status: " . $this->status);
        $this->save(false, ['status', 'processed_at', 'log']);
    }

    public function markQueued(string $message = null)
    {
        if ($this->status === self::STATUS_QUEUED) {
            return;
        }

        $this->status = self::STATUS_QUEUED;
        $this->processed_at = (new \DateTime())->format('Y-m-d H:i:s');
        $this->log(
            "Status: " . $this->status . (
                $message ? " ($message)" : ''
            )
        );
        $this->save(false, ['status', 'processed_at', 'log']);
    }

    /**
     * @param string $log
     */
    public function markFailed(string $message = null)
    {
        if (empty($message)) {
            $message = 'unknown reason';
        }

        $this->status = self::STATUS_FAILED;
        $this->processed_at = (new \DateTime())->format('Y-m-d H:i:s');
        $this->log("Status: " . $this->status . " ($message)");
        $this->save(false, ['status', ' processed_at', 'log']);

        if ($this->retries < $this->max_retries) {
            $this->markQueued(
                sprintf("prepare for retry %d from %d", $this->retries + 1, $this->max_retries)
            );

            /** @var GeneralCronComponent $cronComponent */
            $cronComponent = \Yii::$app->cronComponent;
            $cronComponent->addCommand($this->getInlineCommand());
        }
    }

    /**
     * @return string
     */
    public function getInlineCommand()
    {
        return $this->command . ' --commandId=' . $this->id;
    }

    protected function log(string $message)
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }
}
