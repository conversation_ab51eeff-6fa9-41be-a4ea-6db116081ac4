<?php

namespace common\models;

use common\components\rabbitmq\MessagesSender;
use yii\behaviors\TimestampBehavior;
use yii\db\Query;

/**
 * @property int $id
 * @property int $parent_process_id
 * @property int $customer_id
 * @property string $name
 * @property int $count_all
 * @property int $count_success;
 * @property int $count_failed;
 * @property int $count_attempts;
 * @property int $max_attempts;
 * @property int $success_rate_percents
 * @property string $status
 * @property string $log
 * @property string $next_attempt_after
 * @property int $next_attempt_delay_s
 * @property array $config
 * @property string $started_at
 * @property string $finished_at
 * @property string $created_at
 * @property string $updated_at
 */
class CustomerProcess extends MainActiveRecord
{
    public const STATUS_CREATED = 'created';
    public const STATUS_QUEUED = 'queued';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_IN_PROGRESS_DELAYED = 'in_progress_delayed';
    public const STATUS_FAILED = 'failed';
    public const STATUS_CANCELED = 'canceled';
    public const STATUS_SUCCESS = 'success';

    protected MessagesSender $messagesSender;

    public function __construct($config = [])
    {
        $this->messagesSender = new MessagesSender();
        parent::__construct($config);
    }

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
            'json' => [
                'class' => \laxity7\yii2\behaviors\JsonFieldBehavior::class,
                'fields' => [
                    'config',
                ],
            ],
        ];
    }

    public function setCountSuccessEvents(int $count)
    {
        if ($count === 0) {
            return;
        }

        $this->count_success = $count;
        $this->recalculateStatusBasedOnSuccessRate();
    }

    public function setCountFailedEvents(int $count)
    {
        if ($count === 0) {
            return;
        }

        $this->count_failed = $count;
        $this->recalculateStatusBasedOnSuccessRate();
    }

    public function incrementSuccessEvents(int $incBy = 1): void
    {
        if  ($incBy === 0) {
            return;
        }

        $this->count_success += $incBy;
        $this->recalculateStatusBasedOnSuccessRate();
    }

    public function incrementFailedEvents(int $incBy = 1): void
    {
        if  ($incBy === 0) {
            return;
        }

        $this->count_failed += $incBy;
        $this->recalculateStatusBasedOnSuccessRate();
    }

    protected function recalculateStatusBasedOnSuccessRate()
    {
        $successRatePercents = $this->success_rate_percents;

        if ($this->count_all === 0) {
            $percentSuccess = 100;
            $percentsFailed = 0;
            $this->count_success = 0;
            $this->count_failed = 0;
        } else {
            $percentSuccess = $this->count_success * 100 / $this->count_all;
            $percentsFailed = $this->count_failed * 100 / $this->count_all;
        }

        if ($percentsFailed > (100 - $successRatePercents)) {
            $this->setFailed(sprintf(
                "%d%% success, %d%% failed, %d%% success rate",
                number_format($percentSuccess, 2),
                number_format($percentsFailed, 2),
                number_format($successRatePercents, 2)
            ));
        } else if ($percentSuccess >= $successRatePercents) {
            $this->setSuccess(sprintf(
                "%d%% success, %d%% failed, %d%% success rate",
                number_format($percentSuccess, 2),
                number_format($percentsFailed, 2),
                number_format($successRatePercents, 2)
            ));
        } else {
            $this->save(false);
        }
    }

    public function setQueued(): void
    {
        if ($this->status === self::STATUS_QUEUED) {
            return;
        }

        $this->status = self::STATUS_QUEUED;
        $this->log("Status: " . $this->status);
        $this->save(false);

        /** @var CustomerProcess $parentProcess */
        $parentProcess = $this->getParentProcess()->one();

        if (!empty($parentProcess) && $parentProcess->status !== self::STATUS_IN_PROGRESS) {
            $parentProcess->setInProgress();
            $parentProcess->save(false);
        }
    }

    public function setInProgress(): void
    {
        if ($this->status === self::STATUS_IN_PROGRESS) {
            return;
        }

        $this->started_at = date('Y-m-d H:i:s');
        $this->status = self::STATUS_IN_PROGRESS;
        $this->count_attempts++;
        $this->finished_at = null;
        $this->count_success = 0;
        $this->count_failed = 0;
        $this->log("Status: " . $this->status);
        $this->save(false);

        /** @var CustomerProcess $parentProcess */
        $parentProcess = $this->getParentProcess()->one();

        if (!empty($parentProcess) && $parentProcess->status !== self::STATUS_IN_PROGRESS) {
            $parentProcess->setInProgress();
            $parentProcess->save(false);
        }
    }

    public function setInProgressDelayed()
    {
        if ($this->status === self::STATUS_IN_PROGRESS_DELAYED) {
            return;
        }

        $this->status = self::STATUS_IN_PROGRESS_DELAYED;
        $this->log("Status: " . $this->status);
        $this->save(false);
    }

    public function setSuccess(string $message = null): void
    {
        if ($this->status === self::STATUS_SUCCESS) {
            return;
        }

        $this->finished_at = date('Y-m-d H:i:s');
        $this->status = self::STATUS_SUCCESS;
        $this->log("Status: " . $this->status . (!empty($message) ? " ($message)" : ""));
        $this->save(false);

        /** @var CustomerProcess $parentProcess */
        $parentProcess = $this->getParentProcess()->one();

        if (!empty($parentProcess)) {
            $parentProcess->incrementSuccessEvents();
            $nextProcess = $parentProcess->getNextProcessForInProgress();

            if (!empty($nextProcess)) {
                $this->messagesSender->invokeCustomerProcess($nextProcess);
            }
        }
    }

    public function setCanceled(string $message): void
    {
        if (in_array($this->status, [self::STATUS_CANCELED, self::STATUS_FAILED, self::STATUS_SUCCESS])) {
            return;
        }

        $this->finished_at = date('Y-m-d H:i:s');
        $this->status = self::STATUS_CANCELED;
        $this->log("Status: " . $this->status . " ($message)");
        $this->save(false);

        /** @var CustomerProcess $parentProcess */
        $parentProcess = $this->getParentProcess()->one();

        if (!empty($parentProcess) && $parentProcess->status !== self::STATUS_FAILED) {
            $parentProcess->setCanceled(sprintf("sub process %d were cancelled", $this->id));
        }

        /** @var CustomerProcess[] $getChildrenProcesses */
        $getChildrenProcesses = $this->getChildrenProcesses()->all();
        foreach ($getChildrenProcesses as $childrenProcess) {
            if ($childrenProcess->status !== self::STATUS_SUCCESS) {
                $childrenProcess->count_attempts = $childrenProcess->max_attempts;
                $childrenProcess->setCanceled("cascade from parent process");
            }
        }
    }

    public function setFailed(string $message): void
    {
        if (in_array($this->status, [self::STATUS_CANCELED, self::STATUS_FAILED, self::STATUS_SUCCESS])) {
            return;
        }

        $this->finished_at = date('Y-m-d H:i:s');
        $this->status = self::STATUS_FAILED;
        $this->log("Status: " . $this->status . " ($message)");
        $this->save(false);

        // Prepare for additional attempt
        if ($this->count_attempts < $this->max_attempts) {
            $message = sprintf("prepared for attempt %d from %d", $this->count_attempts + 1, $this->max_attempts);
            $this->next_attempt_after = date('Y-m-d H:i:s', time() + $this->next_attempt_delay_s);
            $this->setCreated($message);
            $this->messagesSender->invokeCustomerProcess($this);
            return;
        }

        /** @var CustomerProcess $parentProcess */
        $parentProcess = $this->getParentProcess()->one();

        if (!empty($parentProcess)) {
            $parentProcess->setFailed(sprintf("unable to success finish sub process %d after %d attempts", $this->id, $this->count_attempts));
        }

        /** @var CustomerProcess[] $getChildrenProcesses */
        $getChildrenProcesses = $this->getChildrenProcesses()->all();
        foreach ($getChildrenProcesses as $childrenProcess) {
            $childrenProcess->count_attempts = $childrenProcess->max_attempts;
            $childrenProcess->setCanceled("parent process has been failed");
        }
    }

    public function setCreated(string $message = null): void
    {
        if ($this->status === self::STATUS_CREATED) {
            return;
        }

        $this->started_at = null;
        $this->finished_at = null;
        $this->status = self::STATUS_CREATED;
        $this->count_success = 0;
        $this->count_failed = 0;
        $this->log("Status: " . $this->status . (!empty($message) ? " ($message)" : ""));
        $this->save(false);

        /** @var CustomerProcess[] $getChildrenProcesses */
        $getChildrenProcesses = $this->getChildrenProcesses()->all();
        foreach ($getChildrenProcesses as $childrenProcess) {
            $childrenProcess->count_attempts = 0;
            $childrenProcess->setCreated("cascade from parent process");
        }
    }

    public function log(string $message): void
    {
        $this->log .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }

    public function getChildrenProcesses(): Query
    {
        return $this->hasMany(CustomerProcess::class, ['parent_process_id' => 'id']);
    }

    public function getParentProcess(): Query
    {
        return $this->hasOne(CustomerProcess::class, ['id' => 'parent_process_id']);
    }

    public function getNextProcessForInProgress(): ?self
    {
        return $this
            ->getChildrenProcesses()
            ->where(['=', 'status', CustomerProcess::STATUS_CREATED])
            ->orderBy('id ASC')
            ->one() ?? null;
    }
}
