<?php

namespace common\models;

use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "feature_flag".
 *
 * @OA\Schema(
 *      schema="FeatureFlag",
 *      @OA\Property(
 *          property="id",
 *          type="integer",
 *          description="Feature flag ID",
 *      ),
 *      @OA\Property(
 *          property="name",
 *          type="string",
 *          description="Feature flag name (should be unique)",
 *      ),
 *      @OA\Property(
 *          property="description",
 *          type="string",
 *          description="Description of the feature flag, or Jira ticket id",
 *      ),
 *      @OA\Property(
 *          property="is_enabled",
 *          type="boolean",
 *          description="Show if feature enabled or not",
 *      ),
 *  )
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string|null $initial_command
 * @property string $created_at
 * @property string $updated_at
 *
 * @property FeatureFlagTarget[] $targets
 *
 */
class FeatureFlag extends MainActiveRecord
{
    public function behaviors(): array
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'feature_flag';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['description'], 'string'],
            [['initial_command', 'created_at', 'updated_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['name'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => \Yii::t('admin', 'ID'),
            'name' => \Yii::t('admin', 'Name'),
            'description' => \Yii::t('admin', 'Description'),
            'initial_command' => \Yii::t('admin', 'Initial Command'),
            'created_at' => \Yii::t('admin', 'Created At'),
            'updated_at' => \Yii::t('admin', 'Updated At'),
        ];
    }
}
