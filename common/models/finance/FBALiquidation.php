<?php

namespace common\models\finance;

use common\components\core\db\dbManager\DbManager;
use common\models\finance\base\AbstractEvent;

/**
 * @property int $id
 * @property string $FinancialEventGroupId
 * @property string $PostedDate
 * @property string $OriginalRemovalOrderId
 * @property array $LiquidationProceedsAmount
 * @property array $LiquidationFeeAmount
 *
 * @package common\models\finance
 */
class FBALiquidation extends AbstractEvent
{
    public static function tableName()
    {
        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_FINANCE) . '.fba_liquidation';
    }
}
