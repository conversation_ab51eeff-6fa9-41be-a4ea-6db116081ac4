<?php

namespace common\models\finance\base;

use common\components\core\db\dbManager\DbManager;

/**
 * Class AbstractEvent
 * @package common\models\finance\base
 */
abstract class AbstractEvent extends AbstractFinanceRecord
{
    public static function tableName()
    {
        $path = explode('\\', static::class);
        $class = array_pop($path);

        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_FINANCE) . '.' . strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $class));
    }
}
