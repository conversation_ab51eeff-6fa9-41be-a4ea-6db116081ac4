<?php

namespace common\models\finance;

use common\models\customer\clickhouse\Transaction;
use common\models\finance\base\AbstractFinanceRecord;

/**
 * @property string $id
 * @property string $start_date
 * @property string $finish_date
 * @property string $loading_status
 * @property string $type
 * @property string $created_at
 * @property string $updated_at
 * @property int $has_transactions;
 * @property int $clickhouse_version
 * @property string $clickhouse_queued_at
 * @property string $clickhouse_moved_at
 * @property string $clickhouse_status
 * @property string $clickhouse_exception
 *
 * @package common\models\finance
 */
class EventPeriod extends AbstractFinanceRecord
{
    const CLICKHOUSE_STATUS_NEW = 'new';
    const CLICKHOUSE_STATUS_QUEUED = 'queued';
    const CLICKHOUSE_STATUS_MOVED = 'moved';
    const CLICKHOUSE_STATUS_ERROR = 'error';
    const CLICKHOUSE_STATUS_WAITING = 'waiting';
    const CLICKHOUSE_STATUS_PROCESSING = 'processing';

    const LOADING_STATUS_NEW = 'NEW';
    const LOADING_STATUS_FINISHED = 'FINISHED';
    const LOADING_STATUS_SKIPPED = 'SKIPPED';
    const LOADING_STATUS_TERMINATED = 'TERMINATED';
    const LOADING_STATUS_PROCESSING = 'PROCESSING';
    const LOADING_STATUS_QUEUED = 'QUEUED';

    const TYPE_INIT = 'INIT';
    const TYPE_REFRESH = 'REFRESH';

    /**
     * Returns array of table names which contain financial events.
     *
     * @return array
     */
    public static function getEventTables(): array
    {
        return [
            Adjustment::tableName(),
            AffordabilityExpense::tableName(),
            AffordabilityExpenseReversal::tableName(),
            Chargeback::tableName(),
            CouponPayment::tableName(),
            DebtRecovery::tableName(),
            FBALiquidation::tableName(),
            GuaranteeClaim::tableName(),
            ImagingServicesFee::tableName(),
            LoanServicing::tableName(),
            NetworkComminglingTransaction::tableName(),
            PayWithAmazon::tableName(),
            ProductAdsPayment::tableName(),
            Refund::tableName(),
            RentalTransaction::tableName(),
            Retrocharge::tableName(),
            SAFETReimbursement::tableName(),
            SellerDealPayment::tableName(),
            SellerReviewEnrollmentPayment::tableName(),
            ServiceFee::tableName(),
            ServiceProviderCredit::tableName(),
            Shipment::tableName(),
            AdhocDisbursement::tableName(),
            CapacityReservationBilling::tableName(),
            ChargeRefund::tableName(),
            FailedAdhocDisbursement::tableName(),
            ShipmentSettle::tableName(),
            TaxWithholding::tableName(),
            TDSReimbursement::tableName(),
            TrialShipment::tableName(),
            ValueAddedServiceCharge::tableName(),
            RemovalShipmentAdjustment::tableName(),
            RemovalShipment::tableName()
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        $date = date('Y-m-d H:i:s');
        if ($insert) {
            $this->created_at = $date;
        }

        $this->updated_at = $date;

        return parent::beforeSave($insert);
    }

    public function isLoadingTerminated(): bool
    {
        return $this->loading_status === self::LOADING_STATUS_TERMINATED;
    }

    public function markAsClickhouseException(string $exceptionMessage)
    {
        $this->logClickhouseException($exceptionMessage);
        $this->clickhouse_status = self::CLICKHOUSE_STATUS_ERROR;
    }

    public function markAsClickhouseOrderNotFoundException(string $message)
    {
        $this->logClickhouseException($message);
        $this->clickhouse_status = self::CLICKHOUSE_STATUS_WAITING;
    }

    public function markAsMovedToClickhouse()
    {
        $this->clickhouse_status = self::CLICKHOUSE_STATUS_MOVED;
        $this->clickhouse_moved_at = date('Y-m-d H:i:s');

        \Yii::$app->fastPersistentCache->set(
            'customer_' . \Yii::$app->dbManager->getCustomerId() . '_cl_data_updated_at_v1',
            date('Y-m-d H:i:s'),
            60 * 60
        );
    }

    public function logClickhouseException(string $message): void
    {
        $message = substr($message, 0, 1000);
        $this->clickhouse_exception .= date("[Y-m-d H:i:s]") .  " {$message}\n";
    }

    public function markAsQueuedToClickhouse()
    {
        $this->clickhouse_status = self::CLICKHOUSE_STATUS_QUEUED;
        $this->clickhouse_queued_at = date('Y-m-d H:i:s');
        $this->clickhouse_moved_at = null;
        $this->clickhouse_exception = null;
    }

    public static function getLastMovedToClickhouseDate(string $amazonOrderId = null): ?string
    {
        $cacheKey = implode('_', array_filter([
            'customer',
            \Yii::$app->dbManager->getCustomerId(),
            ($amazonOrderId ? "order_id_$amazonOrderId" : null),
            'cl_data_updated_at_v1'
        ]));
        $lastUpdatedDate = \Yii::$app->fastPersistentCache->get($cacheKey) ?? null;

        if (!empty($lastUpdatedDate)) {
            return $lastUpdatedDate;
        }

        if (!\Yii::$app->customerComponent->isActive()) {
            return \Yii::$app->customerComponent->getWasActiveUntilDate(true);
        }

        $query = Transaction::find()->select('max(TransactionDate)');

        if (!empty($amazonOrderId)) {
            $query->andWhere(['=', 'AmazonOrderId', $amazonOrderId]);
        }

        $lastUpdatedDate = $query->createCommand()->queryScalar();

        \Yii::$app->fastPersistentCache->set($cacheKey, $lastUpdatedDate, 60 * 60);

        return $lastUpdatedDate;
    }
}
