<?php

namespace common\models\finance;

use common\models\finance\base\AbstractEvent;

/**
 * @property int $id
 * @property string $FinancialEventGroupId
 * @property string $TransactionType
 * @property string $PostedDate
 * @property string $NetCoTransactionID
 * @property string $SwapReason
 * @property string $ASIN
 * @property string $MarketplaceId
 * @property array $TaxExclusiveAmount
 * @property array $TaxAmount
 *
 * @package common\models\finance
 */
class NetworkComminglingTransaction extends AbstractEvent
{
}
