<?php

namespace common\models\finance\clickhouse;

/**
 * This class extends Transaction class with additional fields.
 */
class ExtendedTransaction extends Transaction
{
    public ?string $EAN;

    public ?string $ISBN;

    public ?string $UPC;

    public ?string $Title;

    public ?string $MainImage;

    public ?string $ParentASIN;

    public ?string $Brand;

    public ?string $Model;

    public ?string $ProductType;

    public ?string $Manufacturer;

    public ?string $AgeRange;

    public ?string $AdultProduct;

    public ?string $ProductStockType;

    public ?string $OfferType;

    public ?string $ProductId;

    public null|array|string $TagId;
}
