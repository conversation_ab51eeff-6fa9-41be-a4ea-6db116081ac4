<?php

namespace common\models\finance\clickhouse;

use yii\helpers\Inflector;

/**
 * Class Transaction
 * @package common\models\finance\clickhouse
 */
class Transaction
{
    /** @var string */
    public $PostedDate;

    /** @var string */
    public $TransactionDate;

    /** @var integer */
    public $Amount;

    /** @var integer */
    public $AmountEUR;

    /** @var string */
    public $Currency;

    /** @var string */
    public $EventPeriodId;

    /** @var string */
    public $SellerSKU;

    /** @var string */
    public $ASIN;

    /** @var string */
    public $SellerOrderId;

    /** @var string */
    public $AmazonOrderId;

    /** @var string */
    public $MarketplaceId;

    /** @var int */
    public $CategoryId;

    /** @var int */
    public $COGCategoryId;

    /** @var int */
    public $IndirectCostId;

    /** @var int */
    public $IndirectCostTypeId;

    /** @var string */
    public $Quantity;

    /** @var string */
    public $SellerId;

    /** @var string */
    public $CreatedAt;

    /**
     * Contains count of clickhouse merges. Can be helpful for debugging information.
     *
     * @var int
     */
    public $MergeCounter = 1;

    public function __construct(array $initialValues  = [])
    {
        foreach ($initialValues as $propertyName => $propertyValue) {
            if (property_exists($this, $propertyName)) {
                $this->{$propertyName} = $propertyValue;
                continue;
            }

            // Lower case initial values support
            $propertyName = ucfirst(Inflector::camelize($propertyName));
            if (property_exists($this, $propertyName)) {
                $this->{$propertyName} = $propertyValue;
            }
        }

        if (empty($this->CreatedAt)) {
            $this->CreatedAt = date('Y-m-d H:i:s');
        }
    }
}
