<?php

namespace common\models\finance\clickhouse;

use common\components\featureFlag\FeatureFlagService;
use common\components\processManager\ProcessManager;
use Ramsey\Uuid\Uuid;
use yii\helpers\Inflector;

/**
 * Class Transaction
 * @package common\models\finance\clickhouse
 */
class Transaction
{
    /** @var string */
    public $PostedDate;

    /** @var string */
    public $TransactionDate;

    /** @var integer */
    public $Amount;

    /** @var integer */
    public $AmountEUR;

    /** @var string */
    public $Currency;

    /** @var string */
    public $EventPeriodId;

    /** @var string */
    public $SellerSKU;

    /** @var string */
    public $ASIN;

    /** @var string */
    public $SellerOrderId;

    /** @var string */
    public $AmazonOrderId;

    /** @var string */
    public $MarketplaceId;

    /** @var int */
    public $CategoryId;

    /** @var int */
    public $COGCategoryId;

    /** @var int */
    public $IndirectCostId;

    /** @var int */
    public $IndirectCostTypeId;

    /** @var string */
    public $Quantity;

    /** @var string */
    public $SellerId;

    /** @var string */
    public $CreatedAt;

    /**
     * Contains count of clickhouse merges. Can be helpful for debugging information.
     *
     * @var int
     */
    public $MergeCounter = 1;

    public function __construct(array $initialValues  = [])
    {
        foreach ($initialValues as $propertyName => $propertyValue) {
            if (property_exists($this, $propertyName)) {
                $this->{$propertyName} = $propertyValue;
                continue;
            }

            // Lower case initial values support
            $propertyName = ucfirst(Inflector::camelize($propertyName));
            if (property_exists($this, $propertyName)) {
                $this->{$propertyName} = $propertyValue;
            }
        }

        if (empty($this->CreatedAt)) {
            $this->CreatedAt = date('Y-m-d H:i:s');
        }
    }

    /**
     * Converts array of Transaction objects to array of arrays.
     *
     * @param array $transactionsAsObjects
     * @return array
     */
    public static function convertTransactionsToArray(array $transactionsAsObjects): array
    {
        /** @var FeatureFlagService $featureFlagService */
        $featureFlagService = \Yii::$app->featureFlagService;
        /** @var ProcessManager $processManager */
        $processManager = \Yii::$app->processManager;

        \Yii::info("Json decoding transactions started");
        $transactionsAsArray = json_decode(json_encode($transactionsAsObjects), true);
        \Yii::info("Json decoding transactions finished");

        $isEnabledTransactionsDeduplication = $featureFlagService->isEnabled(
            FeatureFlagService::FLAG_TRANSACTIONS_DEDUPLICATION_IMPROVEMENTS,
            \Yii::$app->dbManager->getCustomerId()
        );

        if (!$isEnabledTransactionsDeduplication) {
            return $transactionsAsArray;
        }

        $usedTransactionUUIDs = [];

        foreach ($transactionsAsArray as &$transactionAsArray) {
            $randomizerIncrement = 0;

            // In scope of one function call, there should be no duplicated transactions.
            // If UUID is already used, we should try to generate next UUID by added some increment integer.
            do {
                $UUID = Uuid::uuid5(
                    Uuid::NAMESPACE_OID,
                    implode('_', [
                        $transactionAsArray['PostedDate'],
                        $transactionAsArray['TransactionDate'],
                        $transactionAsArray['MarketplaceId'],
                        $transactionAsArray['SellerId'],
                        $transactionAsArray['SellerSKU'],
                        $transactionAsArray['CategoryId'],
                        $transactionAsArray['COGCategoryId'],
                        $transactionAsArray['AmazonOrderId'],
                        $transactionAsArray['Currency'],
                        $transactionAsArray['Amount'],
                        $transactionAsArray['Quantity'],
                        $transactionAsArray['IndirectCostId'],
                        $randomizerIncrement++
                    ])
                )->toString();
                $wasUUIDUsedBefore = in_array($UUID, $usedTransactionUUIDs);
                $usedTransactionUUIDs[] = $UUID;
            } while ($wasUUIDUsedBefore);

            // Prepare process trace to insert into clickhouse
            $processTraceAsString = array_map(function ($traceItem) {
                return "'" . str_replace("'", "\\'", $traceItem) . "'";
            }, $processManager->getProcessTrace());
            $processTraceAsString = implode(', ', $processTraceAsString);

            $transactionAsArray['TransactionUUID'] = $UUID;
            $transactionAsArray['ProcessUUID'] = $processManager->getProcessUUID();
            $transactionAsArray['ProcessTrace'] = "[$processTraceAsString]";
        }

        return $transactionsAsArray;
    }

    public static function createOppositeTransaction(
        Transaction|ExtendedTransaction $transaction
    ): Transaction|ExtendedTransaction
    {
        $oppositeTransaction = clone  $transaction;
        $oppositeTransaction->AmountEUR *= -1;
        $oppositeTransaction->Amount *= -1;
        $oppositeTransaction->MergeCounter = 1;
        $oppositeTransaction->CreatedAt = date('Y-m-d H:i:s');

        return $oppositeTransaction;
    }
}
