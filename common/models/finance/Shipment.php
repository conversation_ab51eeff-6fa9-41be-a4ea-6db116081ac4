<?php

namespace common\models\finance;

use common\models\finance\base\AbstractShipment;

/**
 * @property int $id
 * @property string $FinancialEventGroupId
 * @property string $AmazonOrderId
 * @property string $SellerOrderId
 * @property string $MarketplaceName
 * @property int $OrderChargeList
 * @property array $OrderChargeAdjustmentList
 * @property array $ShipmentFeeList
 * @property array $ShipmentFeeAdjustmentList
 * @property array $OrderFeeList
 * @property array $OrderFeeAdjustmentList
 * @property array $DirectPaymentList
 * @property string $PostedDate
 * @property array $ShipmentItemList
 * @property array $ShipmentItemAdjustmentList
 *
 * @package common\models\finance
 */
class Shipment extends AbstractShipment
{
}
