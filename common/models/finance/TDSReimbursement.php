<?php

namespace common\models\finance;

use common\components\core\db\dbManager\DbManager;
use common\models\finance\base\AbstractEvent;

/**
 * @property int $id
 * @property string $PostedDate
 * @property string $TDSOrderId
 * @property array $ReimbursedAmount
 *
 * @package common\models\finance
 */
class TDSReimbursement extends AbstractEvent
{
    public static function tableName()
    {
        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_FINANCE) . '.tds_reimbursement';
    }
}
