<?php

namespace common\models\finance;

use common\components\core\db\dbManager\DbManager;
use common\models\finance\base\AbstractFinanceRecord;

/**
 * @property string $id
 * @property string $FinancialEventGroupId
 * @property string $ProcessingStatus
 * @property string $FundTransferStatus
 * @property array $OriginalTotal
 * @property array $ConvertedTotal
 * @property string $FundTransferDate
 * @property string $TraceId
 * @property string $AccountTail
 * @property array $BeginningBalance
 * @property string $FinancialEventGroupStart
 * @property string $FinancialEventGroupEnd
 * @property string $loading_status
 * @property string $version
 * @property string $created_at
 * @property string $updated_at
 *
 * @package common\models\finance
 */
class EventGroup extends AbstractFinanceRecord
{
    const LOADING_STATUS_NEW = 'NEW';
    const LOADING_STATUS_FINISHED = 'FINISHED';
    const LOADING_STATUS_TERMINATED = 'TERMINATED';
    const LOADING_STATUS_PROCESSING = 'PROCESSING';
    const PROCESSING_STATUS_OPEN = 'OPEN';


    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        $date = date('Y-m-d H:i:s');
        if ($insert) {
            $this->created_at = $date;
        }

        $this->updated_at = $date;

        return parent::beforeSave($insert);
    }
}
