<?php

namespace common\models\finance;

use common\components\core\db\dbManager\DbManager;
use common\models\finance\base\AbstractEvent;

/**
 * @property int $id
 * @property string $FinancialEventGroupId
 * @property string $PostedDate
 * @property string $SAFETClaimId
 * @property array $ReimbursedAmount
 * @property string $ReasonCode
 * @property array $SAFETReimbursementItemList
 *
 * @package common\models\finance
 */
class SAFETReimbursement extends AbstractEvent
{
    public static function tableName()
    {
        return \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_FINANCE) . '.safet_reimbursement';
    }
}
