<?php

namespace common\models;

use common\components\dataImportExport\import\validator\ImportUrlFileStructureValidator;
use common\components\dataImportExport\SupportedHandlers;
use common\models\validators\CronExpressionValidator;
use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * @OA\Schema(
 *     schema="DataImportRecurrent",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Unique identifier"
 *     ),
 *     @OA\Property(
 *         property="customer_id",
 *         type="integer",
 *         description="Customer Id"
 *     ),
 *     @OA\Property(
 *         property="handler_name",
 *         type="string",
 *         description="The name of the handler",
 *     ),
 *     @OA\Property(
 *         property="is_enabled",
 *         type="boolean",
 *         description="Enabled flag"
 *     ),
 *     @OA\Property(
 *         property="url",
 *         type="string",
 *         format="url",
 *         description="URL address"
 *     ),
 *     @OA\Property(
 *         property="auth_login",
 *         type="string",
 *         nullable=true,
 *         description="Authentication login"
 *     ),
 *     @OA\Property(
 *         property="auth_password",
 *         type="string",
 *         nullable=true,
 *         description="Authentication password"
 *     ),
 *     @OA\Property(
 *         property="cron_expr",
 *         type="string",
 *         description="CRON expression"
 *     ),
 *     @OA\Property(
 *         property="auto_export_url",
 *         type="string",
 *         nullable=true,
 *         description="Automatic export URL"
 *     ),
 *     @OA\Property(
 *         property="invoked_at",
 *         type="string",
 *         format="date-time",
 *         nullable=true,
 *         description="Invocation time"
 *     ),
 *     @OA\Property(
 *         property="executed_at",
 *         type="string",
 *         format="date-time",
 *         nullable=true,
 *         description="Execution time"
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Creation time"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Update time"
 *     ),
 *     @OA\Property(
 *         property="is_url_broken",
 *         type="boolean",
 *         description="URL broken flag"
 *     ),
 *     @OA\Property(
 *         property="should_use_auth",
 *         type="boolean",
 *         description="Should use authentication flag"
 *     )
 * )
 */

/**
 * @property int $id
 * @property int $customer_id
 * @property string $handler_name
 * @property string $language_code
 * @property int $is_enabled
 * @property int $is_url_broken
 * @property string $url
 * @property string $cron_expr
 * @property string $auth_login
 * @property string $auth_password
 * @property string $created_at
 * @property string $updated_at
 * @property string $invoked_at
 * @property string $executed_at
 */
class DataImportRecurrent extends MainActiveRecord
{
    public static $dbId = 'db';

    public $should_use_auth = null;

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['url', 'cron_expr', 'handler_name'], 'required'],
            [['is_enabled'], 'integer'],
            [['is_enabled'], 'default', 'value' => 1],
            [['created_at', 'updated_at', 'invoked_at', 'executed_at'], 'safe'],
            [['should_use_auth'], 'boolean'],
            [['should_use_auth'], function () {
                if (null !== $this->should_use_auth && !$this->should_use_auth) {
                    $this->auth_password = null;
                    $this->auth_login = null;
                }
                return true;
            }],
            [['handler_name'], 'in', 'range' => SupportedHandlers::IMPORT_SUPPORTED_HANDLERS],
            [['url', 'auth_login', 'auth_password', 'cron_expr'], 'filter', 'filter' => 'trim'],
            [['url'], 'string', 'max' => 500],
            [['url'], 'url'],
            [['url'],
                ImportUrlFileStructureValidator::class,
                'when' => function (DataImportRecurrent $dataImportRecurrent) {
                    return (bool)$dataImportRecurrent->is_enabled;
                },
            ],
            [['cron_expr'], CronExpressionValidator::class],
            [['cron_expr'], 'unique', 'targetAttribute' => ['customer_id', 'handler_name', 'cron_expr'],
                'message' => Yii::t('admin', 'Such time has been already taken, please choose another time'),
            ],
            [['auth_login'], 'required', 'when' => function (DataImportRecurrent $dataImportRecurrent) {
                return (bool)$dataImportRecurrent->should_use_auth === true
                    || !empty($dataImportRecurrent->auth_password);
            }],
            [['auth_password'], 'required', 'when' => function (DataImportRecurrent $dataImportRecurrent) {
                return (bool)$dataImportRecurrent->should_use_auth === true
                    || !empty($dataImportRecurrent->auth_login);
            }],
            [['cron_expr', 'auth_login', 'auth_password'], 'string', 'max' => 200],
            [['id', 'customer_id', 'handler_name', 'is_enabled', 'url', 'cron_expr', 'auth_login', 'auth_password', 'should_use_auth', 'created_at', 'invoked_at', 'executed_at'], 'safe', 'on'=>'search'],
        ];
    }

    public function search($params)
    {
        $query = DataImportRecurrent::find();
        $this->setScenario('search');
        $this->setAttributes($params);

        // grid filtering conditions
        $query->andFilterCompare('id', $this->id)
            ->andFilterCompare('handler_name', $this->handler_name, 'like')
            ->andFilterCompare('is_enabled', $this->is_enabled)
            ->andFilterCompare('url', $this->url, 'like');

        $query = $this->applyBetweenDateFilter($query, 'created_at', $this->created_at);
        $query = $this->applyBetweenDateFilter($query, 'updated_at', $this->updated_at);
        $query = $this->applyBetweenDateFilter($query, 'invoked_at', $this->invoked_at);
        $query = $this->applyBetweenDateFilter($query, 'executed_at', $this->executed_at);

        return $query;
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'handler_name',
            'should_use_auth',
            'cron_expr',
            'url',
            'auth_login',
            'auth_password',
        ];
        $scenarios['update'] = [
            'cron_expr',
            'should_use_auth',
            'is_enabled',
            'url',
            'auth_login',
            'auth_password',
        ];
        return $scenarios;
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields['should_use_auth'] = function (DataImportRecurrent $model) {
            return !empty($model->auth_password) || !empty($model->auth_login);
        };
        return $fields;
    }

    public function beforeValidate()
    {
        $this->customer_id =Yii::$app->dbManager->getCustomerId();
        return parent::beforeValidate();
    }

    public function beforeSave($insert): bool
    {
        if (null !== $this->should_use_auth && !$this->should_use_auth) {
            $this->auth_login = null;
            $this->auth_password = null;
        }

        $changedFields = $this->getDirtyAttributes();

        if (isset($changedFields['url'])) {
            $this->is_url_broken = false;
        }

        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        $attributeLabels = parent::attributeLabels();
        $attributeLabels['url'] = \Yii::t('admin', 'Import URL');

        return $attributeLabels;
    }
}
