<?php

namespace common\models\ads;

use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property string $name
 * @property string $profile_id
 * @property string $created_at
 * @property string $updated_at
 */
class AmazonAdsCampaign extends AbstractAdsRecord
{
    public const TYPE_SP = 'sponsored_product';
    public const TYPE_SD = 'sponsored_display';
    public const TYPE_SB = 'sponsored_brand';

    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
