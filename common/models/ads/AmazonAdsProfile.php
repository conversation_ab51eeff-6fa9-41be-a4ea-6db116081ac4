<?php

namespace common\models\ads;

use common\components\core\db\dbManager\DbManager;
use common\models\ads\base\AbstractAdsRecord;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;

/**
 * @property int $id
 * @property int $account_id
 * @property string $marketplace_id
 * @property string $profile_id
 * @property string $profile_name
 * @property string $country_code
 * @property string $currency_code
 * @property string $timezone
 * @property string $created_at
 * @property string $updated_at
 * @property string $seller_id
 * @property string $daily_budget
 */
class AmazonAdsProfile extends AbstractAdsRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public static function getCacheTag(int $customerId): string
    {
        return implode('_', [
            'amazon_ads_profile_model_customer',
            $customerId
        ]);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            /** @var DbManager $dbManager */
            $dbManager = Yii::$app->dbManager;
            TagDependency::invalidate(
                \Yii::$app->{self::getDb()->queryCache},
                self::getCacheTag($dbManager->getCustomerId())
            );
        }
    }

    public function afterDelete()
    {
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            [
                self::getCacheTag($dbManager->getCustomerId())
            ]
        );

        parent::afterDelete();
    }

    public function getAmazonAdsAccount(): \yii\db\ActiveQuery
    {
        return $this->hasOne(AmazonAdsAccount::class, ['id' => 'account_id']);
    }

    /**
     * @return AmazonAdsProfile[]
     */
    public static function findActive(): array
    {
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;
        return AmazonAdsProfile::find()
            ->select([
                'marketplace_id',
                'seller_id',
            ])
            ->leftJoin(AmazonAdsAccount::tableName() . ' aac', 'aac.id = amazon_ads_profile.account_id')
            ->where(['aac.is_active' => true])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    self::getCacheTag($dbManager->getCustomerId()),
                    AmazonAdsAccount::getCacheTag($dbManager->getCustomerId())
                ]])
            )
            ->asArray()
            ->all();
    }
}
