<?php

namespace common\models\ads;

use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property string $profile_id
 * @property string $campaign_id
 * @property string $type
 * @property string $name
 * @property string $sku
 * @property string $asin
 * @property string $created_at
 * @property string $updated_at
 */
class AmazonAdsAd extends AbstractAdsRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
