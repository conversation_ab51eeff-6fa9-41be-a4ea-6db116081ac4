<?php

namespace common\models\ads;

use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property int $profile_id
 * @property int $campaign_id
 * @property int $ad_group_id
 * @property string $asin
 * @property string $brand_name
 * @property string $type
 * @property string $created_at
 * @property string $updated_at
 */
class SbAd extends AbstractAdsRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
