<?php

namespace common\models\ads;

use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property int $profile_id
 * @property string $date
 * @property int $campaign_id
 * @property int $ad_id
 * @property int $clicks
 * @property float $cost
 * @property float $spend
 * @property int $impressions
 * @property int $purchases
 * @property int $purchases_clicks
 * @property int $purchases_promoted_clicks
 * @property int $sales
 * @property int $sales_clicks
 * @property int $sales_promoted_clicks
 * @property int $units_sold
 * @property int $units_sold_clicks
 * @property string $currency_code
 * @property string $asin
 * @property string $sku
 * @property string $created_at
 * @property string $updated_at
 * @property string $status_moved_to_clickhouse
 */
class SdAdvertisedProduct extends AbstractAdsRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
