<?php

namespace common\models\ads;

use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;

/**
 * @property int $id
 * @property int $profile_id
 * @property string $date
 * @property int $campaign_id
 * @property int $ad_group_id
 * @property int $clicks
 * @property float $cost
 * @property float $attributed_sales_same_sku_14d
 * @property int $impressions
 * @property string $currency_code
 * @property string $asin
 * @property string $sku
 * @property string $moved_to_clickhouse_at
 * @property string $status_moved_to_clickhouse
 * @property string $created_at
 * @property string $updated_at
 */
class SbAdGroupStatistic extends AbstractAdsRecord
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }
}
