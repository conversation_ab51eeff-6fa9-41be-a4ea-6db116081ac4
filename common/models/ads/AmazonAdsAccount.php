<?php

namespace common\models\ads;

use common\components\core\db\dbManager\DbManager;
use common\components\tokenService\TokenAwareActiveRecordInterface;
use common\models\ads\base\AbstractAdsRecord;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;

/**
 * @property int $id
 * @property bool $is_token_received
 * @property bool $is_active
 * @property string $last_attempt_to_get_token
 */
class AmazonAdsAccount extends AbstractAdsRecord implements TokenAwareActiveRecordInterface
{
    public function behaviors()
    {
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'value' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public static function getCacheTag(int $customerId): string
    {
        return implode('_', [
            'amazon_ads_account_model_customer',
            $customerId
        ]);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert
            || array_key_exists('is_active', $changedAttributes)
        ) {
            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;
            TagDependency::invalidate(
                \Yii::$app->{self::getDb()->queryCache},
                self::getCacheTag($dbManager->getCustomerId())
            );
        }
    }

    public function afterDelete()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            [
                self::getCacheTag($dbManager->getCustomerId())
            ]
        );

        parent::afterDelete();
    }

    public function canMakeRequestToAmazon(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->is_token_received || is_null($this->last_attempt_to_get_token)) {
            return true;
        }

        $currentDate = new \DateTime();
        $lastAttempt = new \DateTime($this->last_attempt_to_get_token);

        // Re-check token from time to time
        return $currentDate > $lastAttempt->modify('+60 minutes');
    }

    public function setIsTokenReceived(bool $isTokenReceived): void
    {
        $this->is_token_received = $isTokenReceived;
        $this->last_attempt_to_get_token = date('Y-m-d H:i:s');
        $this->saveOrThrowException();
    }
}
