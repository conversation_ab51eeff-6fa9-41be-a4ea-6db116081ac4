<?php

namespace common\models\ads\base;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\models\MainActiveRecord;
use common\models\traits\SaveOrThrowException;
use yii\base\InvalidConfigException;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

/**
 * Class AbstractCustomerRecord
 * @package common\models\customer\base
 *
 */
abstract class AbstractAdsRecord extends MainActiveRecord
{
    public const STATUS_CREATED = 'created';
    public const STATUS_MOVED_TO_CLICHOUSE = 'moved_to_clickhouse';

    use SaveOrThrowException;

    public static function tableName(){
        $schemaName = \Yii::$app->dbManager->getSchemaName(DbManager::DB_PREFIX_ADS);
        $tableName = Inflector::camel2id(StringHelper::basename(get_called_class()), '_');
        return $schemaName . '.' . $tableName;
    }

    /**
     * @throws InvalidConfigException
     */
    public static function getDb()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        return $dbManager->getDb(static::$dbId, HelperFactory::TYPE_POSTGRESS_SHARD);
    }
}
