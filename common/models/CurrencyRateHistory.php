<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "currency_rate_history".
 *
 * @property int $id
 * @property string|null $currency_id
 * @property float|null $value
 * @property string|null $date
 */
class CurrencyRateHistory extends MainActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['value'], 'number'],
            [['date'], 'safe'],
            [['currency_id'], 'string', 'max' => 3],
            [['currency_id', 'date'], 'unique', 'targetAttribute' => ['currency_id', 'date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'currency_id' => 'Currency ID',
            'value' => 'Value',
            'date' => 'Date',
        ];
    }
}
