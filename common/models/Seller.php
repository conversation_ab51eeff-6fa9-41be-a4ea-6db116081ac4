<?php

namespace common\models;

use common\components\customerProcess\process\FromDbToClickhouseProcess;
use common\components\customerProcess\ProcessManager;
use common\components\tokenService\TokenAwareActiveRecordInterface;
use common\models\traits\SaveOrThrowException;
use yii\caching\TagDependency;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * Class Seller
 *
 * @package common\models
 *
 * @OA\Schema(
 * schema="Seller",
 *   @OA\Property(
 *      property="id",
 *      type="string",
 *      description="ID"
 *   ),
 *   @OA\Property(
 *      property="region",
 *      type="string",
 *      description="Region"
 *   ),
 *   @OA\Property(
 *      property="is_active",
 *      type="boolean",
 *      description="Is active"
 *   ),
 *   @OA\Property(
 *      property="is_analytic_active",
 *      type="boolean",
 *      description="Is analytic active"
 *   ),
 *   @OA\Property(
 *      property="is_init_periods_created",
 *      type="boolean",
 *      description="Is init periods created"
 *   ),
 *   @OA\Property(
 *      property="is_init_periods_loaded",
 *      type="boolean",
 *      description="Is init periods loaded"
 *   ),
 *   @OA\Property(
 *      property="is_order_init_periods_created",
 *      type="boolean",
 *      description="Is order init periods created"
 *   ),
 *   @OA\Property(
 *      property="is_order_init_periods_loaded",
 *      type="boolean",
 *      description="Is order init periods loaded"
 *   ),
 *   @OA\Property(
 *      property="is_db_created",
 *      type="boolean",
 *      description="Is DB created"
 *   ),
 *   @OA\Property(
 *      property="is_token_received",
 *      type="boolean",
 *      description="Is Amazon token valid"
 *   ),
 *   @OA\Property(
 *      property="customer_id",
 *      type="integer",
 *      description="Customer ID"
 *   ),
 *   @OA\Property(
 *      property="last_attempt_to_get_token",
 *      type="string",
 *      description="Last attempt to get token"
 *   ),
 * )
 *
 * @property string $id
 * @property boolean $is_active
 * @property boolean $is_analytic_active
 * @property boolean $is_init_periods_created
 * @property boolean $is_init_periods_loaded
 * @property boolean $is_order_init_periods_created
 * @property boolean $is_order_init_periods_loaded
 * @property boolean $is_db_created
 * @property boolean $is_vcs_enabled
 * @property boolean $is_demo
 * @property integer $customer_id
 * @property string $eu_amazon_fees_vat
 * @property integer $postgres_db_index
 * @property string $created_at
 * @property boolean $is_token_received
 * @property string $last_attempt_to_get_token
 * @property string $region
 * @property boolean $is_data_migrated
 * @property string $last_waiting_date
 * @property string $was_active_until_date
 */
class Seller extends MainActiveRecord implements TokenAwareActiveRecordInterface
{
    public const DEFAULT_SELLER_ID = 'UNDEFINED';
    public const COMMON_CACHE_TAG = 'model_seller';

    private const UNSUCCESSFUL_TOKEN_GETTING_INTERVAL = 'PT36360S';

    use SaveOrThrowException;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_id', 'postgres_db_index'], 'integer'],
            [['region'], 'string', 'max' => 16],
            [['is_active', 'is_analytic_active','is_order_init_periods_loaded', 'is_order_init_periods_created', 'is_init_periods_loaded', 'is_init_periods_created', 'is_db_created', 'is_token_received', 'is_data_migrated'], 'boolean'],
            [['customer_id', 'is_active', 'is_analytic_active', 'region'], 'required'],
        ];
    }

    public static function findOneUseCache(string $sellerId): ?ActiveRecord
    {
        return Seller::find()
            ->where(['id' => strtoupper($sellerId)])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency([
                    'tags' => Seller::getCacheTag(null, $sellerId)
                ])
            )
            ->one();
    }

    public static function getCacheTag(int $customerId = null, string $sellerId = null): string
    {
        return implode('_', [
            self::COMMON_CACHE_TAG,
            'customer',
            $customerId,
            'seller',
            $sellerId
        ]);
    }

    public function beforeSave($insert)
    {
        if (!$this->is_active && $this->isValueChanged('is_active') && empty($this->was_active_until_date)) {
            $this->was_active_until_date = date('Y-m-d H:i:s');
        }

        if ($this->is_active) {
            $this->was_active_until_date = null;
        }

        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            TagDependency::invalidate(
                \Yii::$app->{self::getDb()->queryCache},
                [
                    self::COMMON_CACHE_TAG,
                    self::getCacheTag($this->customer_id),
                    self::getCacheTag(null, $this->id)
                ]
            );
        }

        if (array_key_exists('is_active', $changedAttributes)
            || array_key_exists('is_analytic_active', $changedAttributes)
            || array_key_exists('is_vcs_enabled', $changedAttributes)
            || array_key_exists('eu_amazon_fees_vat', $changedAttributes)
        ) {
            TagDependency::invalidate(
                \Yii::$app->{self::getDb()->queryCache},
                [
                    self::getCacheTag($this->customer_id),
                    self::getCacheTag(null, $this->customer_id)
                ],
            );
        }
        if (array_key_exists('is_vcs_enabled', $changedAttributes) && $this->is_vcs_enabled) {
            (new ProcessManager())->schedule((new FromDbToClickhouseProcess())->getName(), false, true);
        }
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{self::getDb()->queryCache},
            [
                self::COMMON_CACHE_TAG,
                self::getCacheTag($this->customer_id),
                self::getCacheTag(null, $this->customer_id)
            ]
        );

        parent::afterDelete();
    }


    public static function getEuAmazonFeesVat(string $sellerId): ?float
    {
        return self::find()
            ->select('eu_amazon_fees_vat')
            ->where(['=', 'id', $sellerId])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => self::getCacheTag(null, $sellerId)])
            )
            ->scalar() ?? null;
    }

    /**
     * @param bool $isReceived
     * @return void
     * @throws \Exception
     */
    public function setIsTokenReceived(bool $isReceived): void
    {
        $this->is_token_received = $isReceived;
        $this->last_attempt_to_get_token = date('Y-m-d H:i:s');
        $this->saveOrThrowException();
    }

    /**
     * @return bool
     * @throws \Exception
     */
    public function canMakeRequestToAmazon(): bool
    {
        if ($this->id === self::DEFAULT_SELLER_ID) {
            return false;
        }

        if (!$this->is_active && !$this->is_analytic_active) {
            return false;
        }

        if ($this->is_token_received) {
            return true;
        }

        if (is_null($this->last_attempt_to_get_token)) {
            return true;
        }

        $currentDate = new \DateTime();
        $lastAttempt = new \DateTime($this->last_attempt_to_get_token);

        return $currentDate  > ($lastAttempt->add(new \DateInterval(self::UNSUCCESSFUL_TOKEN_GETTING_INTERVAL)));
    }

    /**
     * Return array of customer ids indexed by seller id
     * <AUTHOR>
     * @return array|null
     */
    public static function getAllCustomersIDsMappedBySellerIds(){
        static $allCustomersIDsMappedBySellerIds = null;

        if (!$allCustomersIDsMappedBySellerIds){
            $allCustomersIDsMappedBySellerIds = ArrayHelper::map(Seller::find()->all(), 'id', 'customer_id');
        }

        return $allCustomersIDsMappedBySellerIds;

    }

}
