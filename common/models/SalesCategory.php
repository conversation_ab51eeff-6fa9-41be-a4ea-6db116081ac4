<?php

namespace common\models;

use yii\caching\TagDependency;
use yii\db\Expression;

/**
 * This is the model class for table "sales_category".
 * @OA\Schema(
 * schema="SalesCategory",
 *   @OA\Property(
 *      property="id",
 *      type="integer",
 *      description="Id"
 *   ),
 *   @OA\Property(
 *      property="name",
 *      type="string",
 *      description="Name"
 *   ),
 *   @OA\Property(
 *      property="parent_id",
 *      type="string",
 *      description="Parent ID"
 *   ),
 *   @OA\Property(
 *      property="depth",
 *      type="integer",
 *      description="Depth"
 *   ),
 * )
 *
 * @property string|null $id
 * @property string $name
 * @property string $parent_id
 * @property integer $depth
 * @property string $sort_order
 * @property array $tags
 * @property string $path
 * @property string $type
 * @property bool $is_visible
 * @property bool $is_exclude_from_calculation
 * @property string $mapping_rule
 * @property int $mapping_rule_order
 * @property string $color_hex
 *
 * @property FinanceEventCategory[] $financeEventCategories
 */
class SalesCategory extends MainActiveRecord
{
    public const TAG_PRODUCT_SALES = 'product_sales';
    public const TAG_MANUAL_NET_PURCHASE_PRICE = 'manual_net_purchase_price';
    public const TAG_MANUAL_COST_OF_GOODS = 'manual_cost_of_goods';
    public const TAG_MANUAL_OTHER_FEES = 'manual_other_fees';
    public const TAG_MANUAL_FBM_SHIPPING_COSTS = 'manual_fbm_shipping_costs';
    public const TAG_MANUAL_VAT = 'manual_vat';
    public const TAG_MANUAL_INDIRECT_COSTS = 'manual_indirect_costs';
    public const TAG_AMAZON_FEES = 'amazon_fees';
    public const TAG_PROMOTION = 'promotion';
    public const TAG_PPC_COSTS = 'ppc_costs';
    public const TAG_REVENUE = 'revenue';

    public const CATEGORY_UNDEFINED = 'undefined';
    public const CATEGORY_EXPENSES_TAXES = 'expenses_taxes';
    public const CATEGORY_EXPENSES_COG = 'cost_of_goods';
    public const CATEGORY_EXPENSES_SHIPPING_COSTS = 'shipping_costs';
    public const CATEGORY_EXPENSES_INDIRECT_COSTS = 'indirect_costs';
    public const CATEGORY_EXPENSES_OTHER_FEES = 'other_fees';
    public const CATEGORY_EXPENSES_AMAZON_FEES = 'amazon_fees';
    public const CATEGORY_REVENUE_VAT = 'revenue_vat';
    public const CATEGORY_REVENUE_TAXES = 'taxes_2';
    public const CATEGORY_REVENUE_COG = 'revenue_cost_of_goods';
    public const CATEGORY_REVENUE_SHIPPING_COSTS = 'revenue_shipping_costs';
    public const CATEGORY_REVENUE_OTHER_FEES = 'revenue_other_fees';

    public const CATEGORY_PROMOTION = 'promotion';

    public const CATEGORY_AMAZON_ADS_SPONSORED_PRODUCTS = 'sponsored_products';
    public const CATEGORY_AMAZON_ADS_SPONSORED_DISPLAY = 'sponsored_display';
    public const CATEGORY_AMAZON_ADS_SPONSORED_BRANDS_STORE_SPOTLIGHT = 'store_spotlight';
    public const CATEGORY_AMAZON_ADS_SPONSORED_BRANDS_VIDEO = 'video';
    public const CATEGORY_AMAZON_ADS_SPONSORED_BRANDS_PRODUCT_COLLECTION = 'product_collection';

    public const COMMON_CACHE_TAG = 'sales-categories';

    public static function tableName()
    {
        return parent::tableName() . '_v1';
    }

    public function search($params)
    {
        $query = self::find();
        $this->setAttributes($params);

        $query
            ->andFilterCompare('depth', $this->depth)
            ->andFilterCompare('parent_id', $this->parent_id)
        ;

        return $query;
    }

    public function getDefaultSort()
    {
        return [
            'sort_order' => SORT_ASC,
        ];
    }

    public static function isUndefinedCategory(?string $salesCategoryId): bool
    {
        return false !== strpos($salesCategoryId, self::CATEGORY_UNDEFINED);
    }

    public static function findByTag(string $tag, string $type): array
    {
        return self::find()
            ->where([
                'AND',
                ['=', 'type', $type],
                new Expression("'$tag' = ANY(tags)")
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->all();
    }

    public static function getExcludedFromCalculationIds(): array
    {
        return self::find()
            ->select('id')
            ->where([
                'is_exclude_from_calculation' => true
            ])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => self::COMMON_CACHE_TAG])
            )
            ->createCommand()
            ->queryColumn();
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['depth'], 'integer'],
            [['depth', 'parent_id', 'path'], 'safe']
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            TagDependency::invalidate(
                \Yii::$app->{SalesCategory::getDb()->queryCache},
                self::COMMON_CACHE_TAG
            );
        }

        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    public function afterDelete()
    {
        TagDependency::invalidate(
            \Yii::$app->{SalesCategory::getDb()->queryCache},
            self::COMMON_CACHE_TAG
        );
        parent::afterDelete(); // TODO: Change the autogenerated stub
    }

    public function fields()
    {
        return [
            'id',
            'name',
            'parent_id',
            'depth',
            'path'
        ];

        return parent::fields(); // TODO: Change the autogenerated stub
    }

    public static function generateHexColor()
    {
        return '#' . substr(md5(rand()), 0, 6);
    }

    /**
     * Gets query for [[FinanceEventCategories]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFinanceEventCategories()
    {
        return $this->hasMany(FinanceEventCategory::className(), ['sales_category_id' => 'id']);
    }
}
