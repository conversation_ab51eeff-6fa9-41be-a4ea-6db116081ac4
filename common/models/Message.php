<?php

namespace common\models;

use common\models\traits\SaveOrThrowException;
use yii\db\ActiveQuery;

/**
 *
 * @OA\Schema(
 *     schema="Message",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="ID",
 *     ),
 *     @OA\Property(
 *         property="category",
 *         type="string",
 *         description="Category",
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Source message",
 *     ),
 *     @OA\Property(
 *         property="is_processed",
 *         type="boolean",
 *         description="Is message processed and sent to staging environment",
 *     ),
 * )
 *
 * @property int                  $id
 * @property string               $category
 * @property string               $message
 * @property bool                 $is_processed
 * @property MessageTranslation[] $messageTranslations
 */
class Message extends MainActiveRecord
{
    use SaveOrThrowException;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['message'], 'string'],
            [['category'], 'string', 'max' => 32],
            [['message'], 'unique', 'targetAttribute' => ['message', 'category']],
            [['is_processed', ], 'boolean'],
            [['id', 'category', 'message', 'is_processed'], 'safe', 'on' => 'search'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['create'] = [
            'handler_name',
            'output_file_format',
            'cron_expr',
            'is_enabled',
        ];
        $scenarios['update'] = [
            'cron_expr',
            'output_file_format',
            'is_enabled',
        ];
        return $scenarios;
    }

    public function search(?array $params): ActiveQuery
    {
        $query = Message::find();

        $this->setScenario(self::SCENARIO_SEARCH);
        $this->setAttributes($params);

        $query->andFilterCompare('message.id', $this->id)
            ->andFilterCompare('message.category', $this->category, 'like')
            ->andFilterCompare('message.message', $this->message, 'like')
            ->andFilterCompare('message.is_processed', $this->is_processed);

        return $query;
    }

    /**
     * @return ActiveQuery
     */
    public function getMessageTranslations(): ActiveQuery
    {
        return $this->hasMany(MessageTranslation::class, ['id' => 'id']);
    }
}
