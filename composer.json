{"name": "develop/profit-dash", "description": "Amazon Api Loader", "homepage": "https://www.sellerlogic.com/", "type": "project", "minimum-stability": "stable", "require": {"php": ">=7.4", "ext-zip": "*", "ext-simplexml": "*", "ext-json": "*", "ext-soap": "*", "ext-libxml": "*", "ext-redis": ">=3", "yiisoft/yii2": "2.0.47", "yiisoft/yii2-redis": "2.0.18", "genxoft/yii2-oas3": "^0.1.0", "yiisoft/yii2-httpclient": "^2.0.12", "guzzlehttp/guzzle": "^7.3", "php-amqplib/php-amqplib": "^3.5", "zircote/swagger-php": "3.1.*", "lcobucci/jwt": "3.3", "mikemadisonweb/yii2-rabbitmq": "^2.5", "smi2/phpclickhouse": "^1.4", "sellerlogic/internal-api": "^2.3", "laxity7/yii2-json-field": "^1.0", "symfony/dotenv": "^5.3", "bashkarev/clickhouse": "^2.0", "pahanini/yii2-consolelog": "^3.0", "fl0v/yii2-rollbar": "^1.0", "codeception/module-phpbrowser": "^1.0.0", "codeception/module-asserts": "^1.0.0", "machour/yii2-prometheus": "^2.0", "promphp/prometheus_client_php": "^2.4", "symfony/expression-language": "^5.4", "phpoffice/phpexcel": "1.8.2", "dragonmantank/cron-expression": "^3.3", "sellerlogic/selling-api-client": "6.1.7", "nuwber/yii2-phpredis": "^2.1", "aws/aws-sdk-php": "^3.270", "symfony/filesystem": "^5.4", "kubrey/amazon-advertising-api-php-sdk": "^2.1", "halaxa/json-machine": "^1.1"}, "require-dev": {"yiisoft/yii2-debug": "2.1.13", "friendsofphp/php-cs-fixer": "^2.18", "yiisoft/yii2-gii": "^2.2", "yiisoft/yii2-codeception": "^2.0"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "autoload": {"psr-4": {"api\\": "api/", "common\\": "common/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"url": "https://gitlab.sl.local/develop/selling-api-client.git", "type": "git"}, {"url": "https://github.com/valkan07/amazon-sp-api-php", "type": "git"}, {"url": "https://gitlab.sl.local/develop/internal-api.git", "type": "git"}, {"type": "composer", "url": "https://asset-packagist.org"}], "scripts": {"check": ["@cs-fix"], "cs-fix": "./vendor/bin/php-cs-fixer fix", "serve-api": "php -S 0.0.0.0:8800 -t api/web"}}