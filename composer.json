{"name": "develop/profit-dash", "description": "Amazon Api Loader", "homepage": "https://www.sellerlogic.com/", "type": "project", "minimum-stability": "stable", "require": {"php": ">=8.3", "ext-zip": "*", "ext-simplexml": "*", "ext-json": "*", "ext-soap": "*", "ext-libxml": "*", "ext-redis": ">=5.0", "yiisoft/yii2": "^2.0.53", "yiisoft/yii2-redis": "^2.0.20", "genxoft/yii2-oas3": "v0.2-stable", "yiisoft/yii2-httpclient": "^2.0.16", "guzzlehttp/guzzle": "^7.0,<7.10", "guzzlehttp/promises": "^1.5", "php-amqplib/php-amqplib": "^3.7.3", "zircote/swagger-php": "^3.3.7", "lcobucci/jwt": "^5.5", "mikemadisonweb/yii2-rabbitmq": "^2.5", "smi2/phpclickhouse": "^1.6", "sellerlogic/internal-api": "^2.3", "laxity7/yii2-json-field": "^1.0.2", "symfony/dotenv": "^5.4", "bashkarev/clickhouse": "^2.0.5", "pahanini/yii2-consolelog": "^3.0", "baibaratsky/yii2-rollbar": "^1.9", "rollbar/rollbar": "^3.1", "codeception/module-phpbrowser": "^3.0", "codeception/module-asserts": "^3.0", "machour/yii2-prometheus": "*", "promphp/prometheus_client_php": "^2.14", "symfony/expression-language": "^5.4", "phpoffice/phpspreadsheet": "1.25.2", "dragonmantank/cron-expression": "^3.3", "sellerlogic/selling-api-client": "v6.1.7", "nuwber/yii2-phpredis": "^2.1", "aws/aws-sdk-php": "3.270.1", "psr/http-message": "^1.1", "symfony/filesystem": "^7.3", "kubrey/amazon-advertising-api-php-sdk": "*", "halaxa/json-machine": "^1.1", "sellerlogic/file-client": "^1.0", "ramsey/uuid": "^4.9"}, "require-dev": {"yiisoft/yii2-debug": "^2.1.27", "php-cs-fixer/shim": "^3.73", "yiisoft/yii2-gii": "^2.2", "yiisoft/yii2-codeception": "^2.0.6", "codeception/codeception": "^5.3", "codeception/module-yii2": "^2.0.4", "codeception/module-filesystem": "^3.0", "codeception/module-cli": "^2.0.1", "codeception/module-db": "^3.1", "codeception/verify": "^3.3", "phpunit/phpunit": "^12.3", "squizlabs/php_codesniffer": "^3.10", "phpmd/phpmd": "^2.15", "phpstan/phpstan": "^1.10", "vimeo/psalm": "^6.12", "rector/rector": "^1.2"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "autoload": {"psr-4": {"api\\": "api/", "common\\": "common/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"url": "https://gitlab.sl.local/develop/selling-api-client.git", "type": "git"}, {"url": "https://github.com/valkan07/amazon-sp-api-php", "type": "git"}, {"url": "https://gitlab.sl.local/develop/internal-api.git", "type": "git"}, {"url": "https://gitlab.sl.local/develop/file-client.git", "type": "git"}, {"type": "composer", "url": "https://asset-packagist.org"}], "scripts": {"check": ["@cs-fix"], "cs-fix": "./vendor/bin/php-cs-fixer fix", "serve-api": "php -S 0.0.0.0:8800 -t api/web"}}