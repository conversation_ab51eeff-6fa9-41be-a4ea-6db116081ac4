<?php

namespace Tests\functional\api\v1;
use Tests\functional\BaseAuthenticatedTest;

class DataExportControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetExportsList()
    {
        codecept_debug('Testing data exports list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-export' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Data exports list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Data exports list retrieved successfully');
    }

    public function testGetExportDetails()
    {
        codecept_debug('Testing data export details endpoint');

        $exportId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-export/{$exportId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Export details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'status']);
            codecept_debug("✅ Export details retrieved for ID: {$exportId}");
        } else {
            codecept_debug("Export {$exportId} not found (expected for test)");
        }
    }
    
    public function testCreateDataExport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export tests');
    }

    public function testUpdateDataExport()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-export/{id} tests');
    }

    public function testDeleteDataExport()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-export/{id} tests');
    }

    public function testDownloadExport()
    {
        $this->markTestSkipped('TODO: Implement GET /v1/data-export/{id}/download tests');
    }
}
