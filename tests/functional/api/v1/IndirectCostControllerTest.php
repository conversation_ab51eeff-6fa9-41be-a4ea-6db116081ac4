<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class IndirectCostControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetIndirectCostsList()
    {
        codecept_debug('Testing indirect costs list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/indirect-cost' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Indirect costs list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Indirect costs list retrieved successfully');
    }
    
    public function testCreateIndirectCost()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/indirect-cost tests');
    }

    public function testUpdateIndirectCost()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/indirect-cost/{id} tests');
    }

    public function testDeleteIndirectCost()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/indirect-cost/{id} tests');
    }

    public function testBulkUpdateIndirectCosts()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/indirect-cost/bulk-update tests');
    }
}
