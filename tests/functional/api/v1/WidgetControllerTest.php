<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class WidgetControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?marketplaceSellerIds=[]&dateStart=2025-08-01&dateEnd=2025-08-19&currencyId=EUR&isTransactionDateMode=0&sales_category_strategy=custom&customerId=19';

    public function testGetOverallStatistics()
    {
        codecept_debug('Testing overall statistics widget endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/widget/overall-statistics' . $this->getParams);
        $this->assertEquals(200, $result['http_code'], 'Overall statistics should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response'], ['metrics']);

        if (isset($data[0])) {
            $this->assertIsNumeric($data[0]['amount'], 'Total Product sales should be numeric');
            codecept_debug('Total Product sales: ' . $data[0]['amount']);
        }

        if (isset($data[1])) {
            $this->assertIsNumeric($data[1]['amount'], 'Total Revenue should be numeric');
            codecept_debug('Total Revenue: ' . $data[1]['amount']);
        }

        if (isset($data[2])) {
            $this->assertIsNumeric($data[2]['amount'], 'Total Expenses should be numeric');
            codecept_debug('Total Expenses: ' . $data[2]['amount']);
        }

        if (isset($data[3])) {
            $this->assertIsNumeric($data[3]['amount'], 'Total Estimated margin should be numeric');
            codecept_debug('Total Estimated margin: ' . $data[3]['amount']);
        }

        if (isset($data[7])) {
            $this->assertIsNumeric($data[7]['amount'], 'Total Order items should be numeric');
            codecept_debug('Total Order items: ' . $data[7]['amount']);
        }

        if (isset($data[8])) {
            $this->assertIsNumeric($data[8]['amount'], 'Total Units should be numeric');
            codecept_debug('Total Units: ' . $data[8]['amount']);
        }


        codecept_debug('✅ Overall statistics retrieved successfully');
    }

    public function testGetKeyPerformance()
    {
        codecept_debug('Testing key performance widget endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/widget/key-performance' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Key performance should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Key performance response should be array/object');

        codecept_debug('✅ Key performance data retrieved successfully');
    }

    public function testGetProfitAndLost()
    {
        codecept_debug('Testing profit and loss widget endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/widget/profit-and-loss' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Profit and loss should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        if (isset($data['profit'])) {
            $this->assertIsNumeric($data['profit'], 'Profit should be numeric');
            codecept_debug('Profit: ' . $data['profit']);
        }

        if (isset($data['loss'])) {
            $this->assertIsNumeric($data['loss'], 'Loss should be numeric');
            codecept_debug('Loss: ' . $data['loss']);
        }

        codecept_debug('✅ Profit and loss data retrieved successfully');
    }

    public function testGetSalesHistory()
    {
        codecept_debug('Testing sales history widget endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/widget/sales-history-v-one' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Sales history should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Sales history response should be array');

        if (!empty($data)) {
            $firstEntry = $data['salesCategories'];
            if (isset($firstEntry['product_sales_4'])) {
                $this->assertIsString($firstEntry['product_sales_4']['name'], 'Category should be string');
                codecept_debug('Category entry : ' . $firstEntry['product_sales_4']['name']);
            }
        }

        codecept_debug('✅ Sales history data retrieved successfully');
    }
}
