<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class MessageTranslationControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?all=1&language=en';

    public function testGetMessageTranslationDetails()
    {
        codecept_debug('Testing message translation details endpoint');

        $translationId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/message-translation/{$translationId}");

        $this->assertContains($result['http_code'], [200, 404], 'Translation details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'language', 'content']);
            codecept_debug("✅ Translation details retrieved for ID: {$translationId}");
        } else {
            codecept_debug("Translation {$translationId} not found (expected for test)");
        }
    }

    public function testGetTranslationsByLanguage()
    {
        codecept_debug('Testing translations by language endpoint');

        $languages = ['en', 'de', 'fr', 'es', 'ru'];

        foreach ($languages as $language) {
            $result = $this->makeAuthenticatedRequest("/v1/message-translation/language/{$language}");
            
            $this->assertContains($result['http_code'], [200, 404], 
                "Translations for {$language} should return 200 or 404");

            if ($result['http_code'] === 200) {
                $data = $this->validateJsonResponse($result['response']);
                codecept_debug("✅ Translations retrieved for language: {$language}");
            } else {
                codecept_debug("No translations found for language: {$language}");
            }
        }
    }
    
    public function testCreateMessageTranslation()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/message-translation tests');
    }

    public function testUpdateMessageTranslation()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/message-translation/{id} tests');
    }

    public function testDeleteMessageTranslation()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/message-translation/{id} tests');
    }

    public function testBulkImportTranslations()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/message-translation/bulk-import tests');
    }

    public function testExportTranslations()
    {
        $this->markTestSkipped('TODO: Implement GET /v1/message-translation/export tests');
    }
}
