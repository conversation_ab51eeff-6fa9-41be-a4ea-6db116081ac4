<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class ProductControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?marketplaceSellerIds=[]&dateStart=2025-08-01&dateEnd=2025-08-19&currencyId=EUR&isTransactionDateMode=0&sales_category_strategy=custom&customerId=7532';

    public function testGetProductsList()
    {
        codecept_debug('Testing products list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Products list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have pagination');

        codecept_debug('✅ Products list retrieved successfully');
    }

    public function testGetProductFilters()
    {
        codecept_debug('Testing product filters endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product/filters' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Product filters should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);


        $this->assertIsArray($data, 'Filters response should be array/object');

        codecept_debug('✅ Product filters retrieved successfully');
    }

    public function testBulkEditProducts()
    {
        codecept_debug('Testing bulk product edit endpoint');

        $bulkData = [
            'products' => [
                ['id' => 1, 'price' => 25.99],
                ['id' => 2, 'price' => 35.99]
            ]
        ];

        $result = $this->makeAuthenticatedRequest('/v1/product/bulk-edit' . $this->getParams, null, 'POST', $bulkData);

        $this->assertContains($result['http_code'], [200, 422],
            'Bulk edit should return 200 or 422');

        if ($result['http_code'] === 200) {
            codecept_debug('✅ Bulk edit completed successfully');
        } else {
            codecept_debug('Bulk edit validation failed (expected for test data)');
        }
    }
}
