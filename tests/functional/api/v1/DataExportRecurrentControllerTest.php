<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class DataExportRecurrentControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetRecurrentExportsList()
    {
        codecept_debug('Testing recurrent data exports list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-export-recurrent' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Recurrent exports list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Recurrent exports list retrieved successfully');
    }

    public function testGetRecurrentExportDetails()
    {
        codecept_debug('Testing recurrent export details endpoint');

        $exportId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-export-recurrent/{$exportId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Recurrent export details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'schedule']);
            codecept_debug("✅ Recurrent export details retrieved for ID: {$exportId}");
        } else {
            codecept_debug("Recurrent export {$exportId} not found (expected for test)");
        }
    }
    
    public function testCreateRecurrentExport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export-recurrent tests');
    }

    public function testUpdateRecurrentExport()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-export-recurrent/{id} tests');
    }

    public function testDeleteRecurrentExport()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-export-recurrent/{id} tests');
    }

    public function testActivateRecurrentExport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export-recurrent/{id}/activate tests');
    }

    public function testDeactivateRecurrentExport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export-recurrent/{id}/deactivate tests');
    }
}
