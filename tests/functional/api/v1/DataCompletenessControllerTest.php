<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class DataCompletenessControllerTest extends BaseAuthenticatedTest
{

    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetDataCompleteness()
    {
        codecept_debug('Testing data completeness endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-completeness/widget' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Data completeness should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Data completeness response should be array/object');

        if (isset($data['completeness_percentage'])) {
            $this->assertIsNumeric($data['completeness_percentage'], 'Completeness percentage should be numeric');
            codecept_debug('Data completeness: ' . $data['completeness_percentage'] . '%');
        }

        codecept_debug('✅ Data completeness retrieved successfully');
    }
    
    public function testCreateDataCompletenessRule()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-completeness tests');
    }

    public function testUpdateDataCompletenessRule()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-completeness/{id} tests');
    }

    public function testDeleteDataCompletenessRule()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-completeness/{id} tests');
    }
}
