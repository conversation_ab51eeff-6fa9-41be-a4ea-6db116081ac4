<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class ProductCostItemControllerTest extends BaseAuthenticatedTest
{

    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetProductCostItemsList()
    {
        codecept_debug('Testing product cost items list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product-cost-item' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Product cost items list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Product cost items list retrieved successfully');
    }

    public function testGetProductCostItemDetails()
    {
        codecept_debug('Testing product cost item details endpoint');

        $itemId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/product-cost-item/{$itemId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Cost item details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'product_id', 'cost']);
            codecept_debug("✅ Cost item details retrieved for ID: {$itemId}");
        } else {
            codecept_debug("Cost item {$itemId} not found (expected for test)");
        }
    }

    public function testCreateProductCostItem()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-item tests');
    }

    public function testUpdateProductCostItem()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/product-cost-item/{id} tests');
    }

    public function testDeleteProductCostItem()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/product-cost-item/{id} tests');
    }

    public function testBulkUpdateProductCostItems()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-item/bulk-update tests');
    }
}
