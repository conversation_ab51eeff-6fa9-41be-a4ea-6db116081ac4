<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class TagColorControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetTagColorsList()
    {
        codecept_debug('Testing tag colors list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/tag-color' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Tag colors list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);
        $this->assertIsArray($data, 'Tag colors should be array');

        if (!empty($data)) {
            codecept_debug('Available tag colors: ' . count($data));
        }

        codecept_debug('✅ Tag colors list retrieved successfully');
    }

    public function testCreateTagColor()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/tag-color tests');
    }

    public function testUpdateTagColor()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/tag-color/{id} tests');
    }

    public function testDeleteTagColor()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/tag-color/{id} tests');
    }

    public function testValidateTagColorHex()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/tag-color/validate-hex tests');
    }
}
