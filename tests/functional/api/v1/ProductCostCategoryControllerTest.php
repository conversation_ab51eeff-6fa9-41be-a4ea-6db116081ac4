<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class ProductCostCategoryControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetProductCostCategoriesList()
    {
        codecept_debug('Testing product cost categories list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product-cost-category' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Product cost categories list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Product cost categories list retrieved successfully');
    }

    public function testGetProductCostCategoryDetails()
    {
        codecept_debug('Testing product cost category details endpoint');

        $categoryId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/product-cost-category/{$categoryId}". $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Cost category details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'name']);
            codecept_debug("✅ Cost category details retrieved for ID: {$categoryId}");
        } else {
            codecept_debug("Cost category {$categoryId} not found (expected for test)");
        }
    }
    
    public function testCreateProductCostCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-category tests');
    }

    public function testUpdateProductCostCategory()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/product-cost-category/{id} tests');
    }

    public function testDeleteProductCostCategory()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/product-cost-category/{id} tests');
    }

    public function testActivateProductCostCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-category/{id}/activate tests');
    }

    public function testDeactivateProductCostCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-category/{id}/deactivate tests');
    }
}
