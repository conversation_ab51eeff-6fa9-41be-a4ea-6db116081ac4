<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class SalesCategoryControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetSalesCategoriesList()
    {
        codecept_debug('Testing sales categories list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/sales-category' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Sales categories list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Sales categories list retrieved successfully');
    }

    public function testCreateSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/sales-category tests');
    }

    public function testUpdateSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/sales-category/{id} tests');
    }

    public function testDeleteSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/sales-category/{id} tests');
    }

    public function testMoveSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/sales-category/{id}/move tests');
    }

    public function testActivateSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/sales-category/{id}/activate tests');
    }

    public function testDeactivateSalesCategory()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/sales-category/{id}/deactivate tests');
    }
}
