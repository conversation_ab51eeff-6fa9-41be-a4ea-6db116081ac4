<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class ProductAggregatedRefundsControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?marketplaceSellerIds=[]&dateStart=2025-08-01&dateEnd=2025-08-19&currencyId=EUR&isTransactionDateMode=0&sales_category_strategy=custom&customerId=7532';

    public function testGetProductAggregatedRefundsList()
    {
        codecept_debug('Testing product aggregated refunds list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product-aggregated-refunds' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Product aggregated refunds list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Product aggregated refunds list retrieved successfully');
    }
}
