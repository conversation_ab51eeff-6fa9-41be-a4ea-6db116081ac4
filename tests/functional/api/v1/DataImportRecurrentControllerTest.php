<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class DataImportRecurrentControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetRecurrentImportsList()
    {
        codecept_debug('Testing recurrent data imports list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-import-recurrent' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Recurrent imports list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Recurrent imports list retrieved successfully');
    }

    public function testGetRecurrentImportDetails()
    {
        codecept_debug('Testing recurrent import details endpoint');

        $importId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-import-recurrent/{$importId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Recurrent import details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'schedule']);
            codecept_debug("✅ Recurrent import details retrieved for ID: {$importId}");
        } else {
            codecept_debug("Recurrent import {$importId} not found (expected for test)");
        }
    }
    
    public function testCreateRecurrentImport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import-recurrent tests');
    }

    public function testUpdateRecurrentImport()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-import-recurrent/{id} tests');
    }

    public function testDeleteRecurrentImport()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-import-recurrent/{id} tests');
    }

    public function testActivateRecurrentImport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import-recurrent/{id}/activate tests');
    }

    public function testDeactivateRecurrentImport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import-recurrent/{id}/deactivate tests');
    }
}
