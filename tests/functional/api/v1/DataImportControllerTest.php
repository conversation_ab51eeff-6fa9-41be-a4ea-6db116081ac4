<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class DataImportControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetImportsList()
    {
        codecept_debug('Testing data imports list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-import' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Data imports list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Data imports list retrieved successfully');
    }

    public function testGetImportDetails()
    {
        codecept_debug('Testing data import details endpoint');

        $importId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-import/{$importId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Import details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'status']);
            codecept_debug("✅ Import details retrieved for ID: {$importId}");
        } else {
            codecept_debug("Import {$importId} not found (expected for test)");
        }
    }

    public function testGetImportStatus()
    {
        codecept_debug('Testing data import status endpoint');

        $importId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-import/{$importId}/status" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Import status should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['status']);
            codecept_debug("Import status: " . ($data['status'] ?? 'N/A'));
        }

        codecept_debug('✅ Import status check completed');
    }
    
    public function testCreateDataImport()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import tests');
    }

    public function testUpdateDataImport()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-import/{id} tests');
    }

    public function testDeleteDataImport()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-import/{id} tests');
    }

    public function testUploadImportFile()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import/upload tests');
    }

    public function testValidateImportFile()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-import/validate tests');
    }
}
