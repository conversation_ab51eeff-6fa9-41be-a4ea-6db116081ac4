<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class IndirectCostTypeControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetIndirectCostTypesList()
    {
        codecept_debug('Testing indirect cost types list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/indirect-cost-type' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Indirect cost types list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Indirect cost types list retrieved successfully');
    }
    
    public function testCreateIndirectCostType()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/indirect-cost-type tests');
    }

    public function testUpdateIndirectCostType()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/indirect-cost-type/{id} tests');
    }

    public function testDeleteIndirectCostType()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/indirect-cost-type/{id} tests');
    }

    public function testActivateIndirectCostType()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/indirect-cost-type/{id}/activate tests');
    }

    public function testDeactivateIndirectCostType()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/indirect-cost-type/{id}/deactivate tests');
    }
}
