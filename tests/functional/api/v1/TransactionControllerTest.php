<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class TransactionControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?marketplaceSellerIds=[]&dateStart=2025-08-01&dateEnd=2025-08-19&currencyId=EUR&isTransactionDateMode=0&sales_category_strategy=custom&customerId=19';

    public function testGetTransactionsList()
    {
        codecept_debug('Testing transactions list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/transaction-v1' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Transactions list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have pagination');

        codecept_debug('✅ Transactions list retrieved successfully');
    }
}
