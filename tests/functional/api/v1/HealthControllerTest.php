<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class HealthControllerTest extends BaseAuthenticatedTest
{
    public function testHealthEndpointReturnsOk()
    {
        codecept_debug('Testing health endpoint basic functionality');

        $result = $this->makeUnauthenticatedRequest('/v1/health');

        $this->assertEquals(200, $result['http_code'], 'Health endpoint should return 200');
        $this->assertEmpty($result['error'], 'Health endpoint should not have cURL errors');

        $data = $this->validateJsonResponse($result['response'], ['checks']);

        codecept_debug('✅ Health endpoint returns OK');
    }

    public function testHealthEndpointWithoutAuth()
    {
        codecept_debug('Testing health endpoint accessibility without authentication');

        $result = $this->makeUnauthenticatedRequest('/v1/health');
        $this->assertEquals(200, $result['http_code'], 'Health endpoint should be accessible without auth');

        codecept_debug('✅ Health endpoint accessible without authentication');
    }
}
