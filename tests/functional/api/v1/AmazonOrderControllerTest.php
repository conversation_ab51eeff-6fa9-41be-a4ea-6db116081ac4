<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class AmazonOrderControllerTest extends BaseAuthenticatedTest
{
    public string $getParams = '?marketplaceSellerIds=[]&dateStart=2025-08-01&dateEnd=2025-08-19&currencyId=EUR&isTransactionDateMode=0&sales_category_strategy=custom&customerId=19';

    public function testGetOrdersList()
    {
        codecept_debug('Testing Amazon orders list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/amazon-order-v1' . $this->getParams);

        $this->assertEquals(200, $result['http_code'], 'Amazon orders list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have pagination');

        codecept_debug('✅ Amazon orders list retrieved successfully');
    }

    public function testGetOrderDetails()
    {
        codecept_debug('Testing Amazon order details endpoint');

        $listResult = $this->makeAuthenticatedRequest('/v1/amazon-order-v1' . $this->getParams);
        $this->assertEquals(200, $listResult['http_code']);

        $listData = json_decode($listResult['response'], true);

        if (isset($listData['data']) && !empty($listData['data'])) {
            $firstOrder = $listData['data'][0];
            $orderId = $firstOrder['id'] ?? $firstOrder['amazon_order_id'] ?? 1;

            $result = $this->makeAuthenticatedRequest("/v1/amazon-order/{$orderId}");

            if ($result['http_code'] === 200) {
                $data = $this->validateJsonResponse($result['response'], ['id']);
                codecept_debug("✅ Order details retrieved for ID: {$orderId}");
            } else {
                codecept_debug("Order {$orderId} not found or access denied (HTTP {$result['http_code']})");
            }
        } else {
            codecept_debug('No orders found in list, testing with default ID');
            $result = $this->makeAuthenticatedRequest('/v1/amazon-order/1');
            $this->assertContains($result['http_code'], [200, 404], 'Order details should return 200 or 404');
        }
    }

    public function testGetOrderStatuses()
    {
        codecept_debug('Testing Amazon order statuses endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/amazon-order/statuses?customerId=19');

        $this->assertEquals(200, $result['http_code'], 'Order statuses should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Statuses response should be array');

        if (!empty($data)) {
            codecept_debug('Available statuses: ' . implode(', ', array_keys($data)));
        }

        codecept_debug('✅ Order statuses retrieved successfully');
    }

    public function testGetAmazonFeesBreakdown()
    {
        codecept_debug('Testing Amazon fees breakdown endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/amazon-order-v1/amazon-fees-breakdown?customerId=1&amazonOrderItemId=52659325873882&currencyId=EUR' );

        $this->assertEquals(200, $result['http_code'], 'Amazon fees breakdown should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Fees breakdown response should be array/object');

        if (isset($data['totalFees'])) {
            $this->assertIsNumeric($data['totalFees'], 'Total fees should be numeric');
            codecept_debug('Total Amazon fees: ' . $data['totalFees']);
        }

        codecept_debug('✅ Amazon fees breakdown retrieved successfully');
    }

    public function testGetExpensesBreakdown()
    {
        codecept_debug('Testing expenses breakdown endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/amazon-order-v1/expenses-breakdown?customerId=1&amazonOrderItemId=52659325873882&currencyId=EUR');

        $this->assertEquals(200, $result['http_code'], 'Expenses breakdown should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);

        $this->assertIsArray($data, 'Expenses breakdown response should be array/object');

        if (isset($data['totalExpenses'])) {
            $this->assertIsNumeric($data['totalExpenses'], 'Total expenses should be numeric');
            codecept_debug('Total expenses: ' . $data['totalExpenses']);
        }

        codecept_debug('✅ Expenses breakdown retrieved successfully');
    }
}
