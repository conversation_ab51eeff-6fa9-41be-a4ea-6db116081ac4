<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class ProductCostPeriodControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?sort=-date_start&customerId=7532&seller_sku=KNÖH166_FBM&marketplace_id=A1PA6795UKMFR9&sales_category_id=cost_of_goods';

    public function testGetProductCostPeriodsList()
    {
        codecept_debug('Testing product cost periods list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/product-cost-period' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Product cost periods list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Product cost periods list retrieved successfully');
    }

    public function testGetProductCostPeriodDetails()
    {
        codecept_debug('Testing product cost period details endpoint');

        $periodId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/product-cost-period/{$periodId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Cost period details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'period_start', 'period_end']);
            codecept_debug("✅ Cost period details retrieved for ID: {$periodId}");
        } else {
            codecept_debug("Cost period {$periodId} not found (expected for test)");
        }
    }
    
    public function testCreateProductCostPeriod()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-period tests');
    }

    public function testUpdateProductCostPeriod()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/product-cost-period/{id} tests');
    }

    public function testDeleteProductCostPeriod()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/product-cost-period/{id} tests');
    }

    public function testCloseProductCostPeriod()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-period/{id}/close tests');
    }

    public function testReopenProductCostPeriod()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/product-cost-period/{id}/reopen tests');
    }
}
