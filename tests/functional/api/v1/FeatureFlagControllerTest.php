<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class FeatureFlagControllerTest extends BaseAuthenticatedTest
{
    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetFeatureFlagsList()
    {
        codecept_debug('Testing feature flags list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/feature-flag' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Feature flags list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $data = $this->validateJsonResponse($result['response']);
        $this->assertIsArray($data, 'Feature flags response should be array/object');

        if (is_array($data) && !empty($data)) {
            codecept_debug('Available feature flags: ' . count($data));
        }

        codecept_debug('✅ Feature flags list retrieved successfully');
    }

    public function testGetFeatureFlagDetails()
    {
        codecept_debug('Testing feature flag details endpoint');

        $flagName = 'test-feature';
        $result = $this->makeAuthenticatedRequest("/v1/feature-flag/{$flagName}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Feature flag details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['name', 'enabled']);
            codecept_debug("✅ Feature flag details retrieved for: {$flagName}");
        } else {
            codecept_debug("Feature flag {$flagName} not found (expected for test)");
        }
    }

    public function testGetFeatureFlagStatus()
    {
        codecept_debug('Testing feature flag status endpoint');

        $flagName = 'test-feature';
        $result = $this->makeAuthenticatedRequest("/v1/feature-flag/{$flagName}/status" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Feature flag status should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['enabled']);
            codecept_debug("Feature flag status: " . ($data['enabled'] ? 'enabled' : 'disabled'));
        }

        codecept_debug('✅ Feature flag status check completed');
    }

    public function testCreateFeatureFlag()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/feature-flag tests');
    }

    public function testUpdateFeatureFlag()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/feature-flag/{name} tests');
    }

    public function testDeleteFeatureFlag()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/feature-flag/{name} tests');
    }

    public function testEnableFeatureFlag()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/feature-flag/{name}/enable tests');
    }

    public function testDisableFeatureFlag()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/feature-flag/{name}/disable tests');
    }
}
