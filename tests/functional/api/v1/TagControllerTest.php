<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class TagControllerTest extends BaseAuthenticatedTest
{

    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetTagsList()
    {
        codecept_debug('Testing tags list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/tag' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Tags list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Tags list retrieved successfully');
    }

    public function testGetTagDetails()
    {
        codecept_debug('Testing tag details endpoint');

        $tagId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/tag/{$tagId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Tag details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'name']);
            codecept_debug("✅ Tag details retrieved for ID: {$tagId}");
        } else {
            codecept_debug("Tag {$tagId} not found (expected for test)");
        }
    }

    public function testCreateTag()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/tag tests');
    }

    public function testUpdateTag()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/tag/{id} tests');
    }

    public function testDeleteTag()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/tag/{id} tests');
    }

    public function testMergeTags()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/tag/merge tests');
    }

    public function testBulkDeleteTags()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/tag/bulk-delete tests');
    }
}
