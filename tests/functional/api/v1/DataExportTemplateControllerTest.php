<?php

namespace Tests\functional\api\v1;

use Tests\functional\BaseAuthenticatedTest;

class DataExportTemplateControllerTest extends BaseAuthenticatedTest
{

    public string $getParamsCustomerId = '?customerId=7532';

    public function testGetExportTemplatesList()
    {
        codecept_debug('Testing export templates list endpoint');

        $result = $this->makeAuthenticatedRequest('/v1/data-export-template' . $this->getParamsCustomerId);

        $this->assertEquals(200, $result['http_code'], 'Export templates list should return 200');
        $this->assertEmpty($result['error'], 'Should not have cURL errors');

        $expectedFields = ['data', 'totalCount'];
        $data = $this->validateJsonResponse($result['response'], $expectedFields);

        $this->assertArrayHasKey('data', $data, 'Response should have data array');
        $this->assertArrayHasKey('totalCount', $data, 'Response should have data array');

        codecept_debug('✅ Export templates list retrieved successfully');
    }

    public function testGetExportTemplateDetails()
    {
        codecept_debug('Testing export template details endpoint');

        $templateId = 1;
        $result = $this->makeAuthenticatedRequest("/v1/data-export-template/{$templateId}" . $this->getParamsCustomerId);

        $this->assertContains($result['http_code'], [200, 404], 'Template details should return 200 or 404');

        if ($result['http_code'] === 200) {
            $data = $this->validateJsonResponse($result['response'], ['id', 'title']);
            codecept_debug("✅ Template details retrieved for ID: {$templateId}");
        } else {
            codecept_debug("Template {$templateId} not found (expected for test)");
        }
    }
    
    public function testCreateExportTemplate()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export-template tests');
    }

    public function testUpdateExportTemplate()
    {
        $this->markTestSkipped('TODO: Implement PUT /v1/data-export-template/{id} tests');
    }

    public function testDeleteExportTemplate()
    {
        $this->markTestSkipped('TODO: Implement DELETE /v1/data-export-template/{id} tests');
    }

    public function testCloneExportTemplate()
    {
        $this->markTestSkipped('TODO: Implement POST /v1/data-export-template/{id}/clone tests');
    }
}
