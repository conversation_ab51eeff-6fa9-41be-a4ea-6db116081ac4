<?php

namespace Tests\functional;

abstract class BaseAuthenticatedTest extends \Codeception\Test\Unit
{
    /**
     * @var \FunctionalTester
     */
    protected $tester;
    private $clientId;
    private $clientSecret;
    private $username;
    private $password;
    private $tokenUrl;

    protected function _before()
    {
        parent::_before();

        $this->clientId = $_ENV['TEST_OAUTH_CLIENT_ID'] ?? 'openapi';
        $this->clientSecret = $_ENV['TEST_OAUTH_CLIENT_SECRET'] ?? 'openapisecret';
        $this->username = $_ENV['TEST_OAUTH_USERNAME'] ?? '';
        $this->password = $_ENV['TEST_OAUTH_PASSWORD'] ?? '';
        $this->tokenUrl = $_ENV['TEST_OAUTH_TOKEN_URL'] ?? 'https://auth-staging.sl.local/oauth2/token';
    }

    private static $cachedToken = null;
    private static $tokenExpiry = null;

    protected function getValidAccessToken()
    {
        $tokenFile = codecept_output_dir() . '/real_access_token.txt';

        if (file_exists($tokenFile)) {
            $token = trim(file_get_contents($tokenFile));

            if ($this->isTokenValid($token)) {
                codecept_debug('Using existing valid token from file');
                self::$cachedToken = $token;
                self::$tokenExpiry = $this->getTokenExpiry($token);
                return $token;
            } else {
                codecept_debug('Existing token is invalid, getting new one');
            }
        }

        if (self::$cachedToken && self::$tokenExpiry && self::$tokenExpiry > time()) {
            codecept_debug('Using cached valid token');
            return self::$cachedToken;
        }

        $newToken = $this->getNewAccessToken();
        if ($newToken) {
            file_put_contents($tokenFile, $newToken);
            self::$cachedToken = $newToken;
            self::$tokenExpiry = $this->getTokenExpiry($newToken);
            codecept_debug('New token obtained and saved');
            return $newToken;
        }

        codecept_debug('Failed to get access token');
        return null;
    }

    protected function isTokenValid($token)
    {
        if (empty($token)) {
            return false;
        }

        $parts = explode('.', $token);
        if (count($parts) === 3) {
            $payload = json_decode(base64_decode($parts[1]), true);
            if (isset($payload['exp']) && $payload['exp'] > (time() + 60)) {
                return true;
            }
        }

        return false;
    }

    protected function getTokenExpiry($token)
    {
        if (empty($token)) {
            return null;
        }

        $parts = explode('.', $token);
        if (count($parts) === 3) {
            $payload = json_decode(base64_decode($parts[1]), true);
            if (isset($payload['exp'])) {
                return $payload['exp'];
            }
        }

        return null;
    }

    protected function getNewAccessToken()
    {
        codecept_debug('Getting new access token via OAuth2');

        $postData = [
            'grant_type' => 'password',
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'username' => $this->username,
            'password' => $this->password
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->tokenUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            codecept_debug("OAuth2 cURL Error: {$error}");
            return null;
        }

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if (isset($data['access_token'])) {
                return $data['access_token'];
            }
        }

        codecept_debug("Failed to get new access token. HTTP: {$httpCode}");
        return null;
    }

    protected function findFieldInArray($array, $field)
    {
        if (is_array($array)) {
            if (array_key_exists($field, $array)) {
                return true;
            }
            foreach ($array as $value) {
                if ($this->findFieldInArray($value, $field)) {
                    return true;
                }
            }
        }
        return false;
    }


    protected function testEndpointAuthentication($endpoint, $method = 'GET')
    {
        codecept_debug("Testing authentication for {$method} {$endpoint}");

        $result = $this->makeUnauthenticatedRequest($endpoint, $method);
        $this->assertEquals(401, $result['http_code'], "Endpoint {$endpoint} should return 401 without token");

        $result = $this->makeAuthenticatedRequest($endpoint, 'invalid_token_123');
        $this->assertEquals(401, $result['http_code'], "Endpoint {$endpoint} should return 401 with invalid token");

        $result = $this->makeAuthenticatedRequest($endpoint);
        $this->assertNotEquals(401, $result['http_code'], "Endpoint {$endpoint} should not return 401 with valid token");
        $this->assertNotEquals(0, $result['http_code'], "Endpoint {$endpoint} should respond");

        codecept_debug("✅ Authentication test passed for {$endpoint}");
    }

    protected function makeUnauthenticatedRequest($endpoint, $method = 'GET')
    {
        $url = 'http://profit-dash-nginx' . $endpoint;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        return [
            'response' => $response,
            'http_code' => $httpCode,
            'error' => $error
        ];
    }

    protected function testEndpointPerformance($endpoint, $maxResponseTime = 2000, $token = null)
    {
        codecept_debug("Testing performance for {$endpoint} (max {$maxResponseTime}ms)");

        $start = microtime(true);
        $result = $this->makeAuthenticatedRequest($endpoint, $token);
        $end = microtime(true);

        $responseTime = ($end - $start) * 1000; // в миллисекундах

        codecept_debug("Response time for {$endpoint}: {$responseTime}ms");

        $this->assertLessThan($maxResponseTime, $responseTime,
            "Endpoint {$endpoint} should respond in less than {$maxResponseTime}ms");

        return $responseTime;
    }

    protected function testEndpointHttpMethods($endpoint, $allowedMethods = ['GET'])
    {
        codecept_debug("Testing HTTP methods for {$endpoint}");

        $allMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        $token = $this->getValidAccessToken();

        foreach ($allMethods as $method) {
            $result = $this->makeAuthenticatedRequest($endpoint, $token, $method);

            if (in_array($method, $allowedMethods)) {
                $this->assertNotEquals(405, $result['http_code'],
                    "Method {$method} should be allowed for {$endpoint}");
                codecept_debug("✅ Method {$method} allowed for {$endpoint}");
            } else {
                $this->assertEquals(405, $result['http_code'],
                    "Method {$method} should return 405 for {$endpoint}");
                codecept_debug("✅ Method {$method} correctly forbidden for {$endpoint}");
            }
        }
    }

    protected function validateJsonResponse($response, $expectedFields = [])
    {
        $data = json_decode($response, true);

        $this->assertNotNull($data, 'Response should be valid JSON');
        $this->assertIsArray($data, 'Response should be JSON object/array');

        foreach ($expectedFields as $field) {
            $this->assertTrue(
                $this->findFieldInArray($data, $field),
                "Expected field '{$field}' not found in response"
            );
        }

        return $data;
    }

    protected function makeAuthenticatedRequest($endpoint, $token = null, $method = 'GET', $data = null)
    {
        if (!$token) {
            $token = $this->getValidAccessToken();
        }

        if (!$token) {
            return [
                'response' => null,
                'http_code' => 0,
                'error' => 'No valid token available'
            ];
        }

        $url = 'http://profit-dash-nginx' . $endpoint;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        return [
            'response' => $response,
            'http_code' => $httpCode,
            'error' => $error
        ];
    }
}
