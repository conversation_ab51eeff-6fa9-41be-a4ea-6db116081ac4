<?php

namespace Tests\unit\common\components\treeStructureHelper;

use common\components\COGSync\PeriodDateChangeCalculator;
use common\components\treeStructureHelper\TreeStructureHelper;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;

class TreeStructureHelperTest extends TestCase
{
    use ReflectionCallMethodTrait;

    private TreeStructureHelper $adjacencyListHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adjacencyListHelper = new TreeStructureHelper();
    }

    public function testConvertTreeToFlatFormat()
    {
        $tree = [
            'category_1',
            'category_2' => [
                'category_2_1',
                'category_2_2' => [
                    'category_2_2_1'
                ]
            ]
        ];

        $expectedParentsMap = [
            'category_1' => [
                'depth' => 0,
                'path' => 'category_1',
                'parent_id' => null,
            ],
            'category_2' => [
                'depth' => 0,
                'path' => 'category_2',
                'parent_id' => null,
            ],
            'category_2_1' =>  [
                'depth' => 1,
                'path' => 'category_2|category_2_1',
                'parent_id' => 'category_2',
            ],
            'category_2_2' => [
                'depth' => 1,
                'path' => 'category_2|category_2_2',
                'parent_id' => 'category_2',
            ],
            'category_2_2_1' => [
                'depth' => 2,
                'path' => 'category_2|category_2_2|category_2_2_1',
                'parent_id' => 'category_2_2'
            ],
        ];

        $actualParentsMap = $this->callMethod($this->adjacencyListHelper, 'convertTreeToFlatFormat', [$tree]);

        $this->assertCount(count($expectedParentsMap), $actualParentsMap);

        foreach ($expectedParentsMap as $itemId => $threeData) {
            $this->assertEquals($threeData, $actualParentsMap[$itemId]);
        }
    }

    public function testConvertFlatFormatToTree()
    {
        $flatTree = [
            [
                'id' => 'c_1',
                'parent_id' => null,
                'path' => 'c_1',
                'sort_order' => 2,
                'name' => 'c_1_name',
                'custom_field' => 'v1'
            ], [
                'id' => 'c_2',
                'parent_id' => null,
                'path' => 'c_2',
                'sort_order' => 1,
                'name' => 'c_2_name',
                'custom_field' => 'v2'
            ], [
                'id' => 'c_2_1',
                'parent_id' => 'c_2',
                'path' => 'c_2|c_2_1',
                'name' => 'c_2_1_name',
                'sort_order' => 1,
                'custom_field' => 'v3'
            ], [
                'id' => 'c_2_1_1',
                'parent_id' => 'c_2_1',
                'path' => 'c_2|c_2_1|c_2_1_1',
                'name' => 'c_2_1_1_name',
                'sort_order' => 2,
                'custom_field' => 'v4'
            ], [
                'id' => 'c_2_1_2',
                'parent_id' => 'c_2_2',
                'path' => 'c_2|c_2_1|c_2_1_2',
                'name' => 'c_2_1_2_name',
                'sort_order' => 1,
                'custom_field' => 'v4'
            ]
        ];

        $expectedTree = [
            'c_2' => [
                'id' => 'c_2',
                'name' => 'c_2_name',
                'custom_field' => 'v2',
                'hasChildren' => true,
                'children' => [
                    'c_2_1' => [
                        'id' => 'c_2_1',
                        'name' => 'c_2_1_name',
                        'custom_field' => 'v3',
                        'hasChildren' => true,
                        'children' => [
                            'c_2_1_2' => [
                                'id' => 'c_2_1_2',
                                'name' => 'c_2_1_2_name',
                                'custom_field' => 'v4',
                                'hasChildren' => false,
                                'children' => []
                            ],
                            'c_2_1_1' => [
                                'id' => 'c_2_1_1',
                                'name' => 'c_2_1_1_name',
                                'custom_field' => 'v4',
                                'hasChildren' => false,
                                'children' => []
                            ]
                        ]
                    ]
                ]
            ],
            'c_1' => [
                'id' => 'c_1',
                'name' => 'c_1_name',
                'custom_field' => 'v1',
                'children' => [],
                'hasChildren' => false,
            ],
        ];

        $actualTree = $this->callMethod($this->adjacencyListHelper, 'convertFlatFormatToTree', [$flatTree]);

        $this->assertEquals($expectedTree, $actualTree);

        $expectedTree = [
            'c_2' => [
                'id' => 'c_2',
                'name' => 'c_2_name',
                'custom_field' => 'v2',
                'hasChildren' => true,
                'children' => []
            ],
            'c_1' => [
                'id' => 'c_1',
                'name' => 'c_1_name',
                'custom_field' => 'v1',
                'hasChildren' => false,
                'children' => []
            ],
        ];
        $actualTree = $this->callMethod($this->adjacencyListHelper, 'convertFlatFormatToTree', [$flatTree, 0]);

        $this->assertEquals($expectedTree, $actualTree);
    }
}
