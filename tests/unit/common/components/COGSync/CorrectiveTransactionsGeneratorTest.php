<?php

namespace Tests\unit\common\components\COGSync;

use common\components\COGSync\COGTransactionsGenerator;
use common\components\currencyRate\CurrencyRateManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\models\finance\clickhouse\Transaction;
use phpDocumentor\Reflection\Types\Self_;
use PHPUnit\Framework\TestCase;

class CorrectiveTransactionsGeneratorTest extends TestCase
{
    protected const FAKE_PRINCIPAL_CATEGORY_ID = 245;
    protected const FAKE_REFUND_CATEGORY_ID = 346;
    protected const FAKE_PRODUCT_COST_CATEGORY_ID = 53;

    protected const FAKE_THB_RATE = 36.6;
    protected const FAKE_USD_RATE = 1.2;

    /**
     * @var CurrencyRateManager
     */
    private $currencyRateManagerMock;
    private $correctiveTransactionsGeneratorMock;

    public function __construct(?string $name = null, array $data = [], $dataName = '')
    {
        /** @var CurrencyRateManager $profitCalculator */
        $this->currencyRateManagerMock = $this
            ->getMockBuilder(CurrencyRateManager::class)
            ->onlyMethods(['getRateValue'])
            ->disableOriginalConstructor()
            ->getMock();
        $this->currencyRateManagerMock
            ->method('getRateValue')
            ->will($this->returnCallback(function(string $currencyCode, \DateTime $date) {
                $fakeRates = [
                    'USD' => self::FAKE_USD_RATE,
                    'THB' => self::FAKE_THB_RATE,
                    'EUR' => 1
                ];
                return $fakeRates[$currencyCode];
            }));

        $this->correctiveTransactionsGeneratorMock = $this
            ->getMockBuilder(COGTransactionsGenerator::class)
            ->onlyMethods(['getOrganicRefundCategoryId'])
            ->setConstructorArgs([$this->currencyRateManagerMock])
            ->getMock();

        parent::__construct($name, $data, $dataName);
    }

    /**
     * @dataProvider generateProvider
     */
    public function testGenerate(
        array $transactions,
        float $amountOld,
        string $currencyCodeOld,
        float $amountNew,
        string $currencyCodeNew,
        array $expectedCorrectiveTransactions
    ) {
        $this->correctiveTransactionsGeneratorMock
            ->method('getOrganicRefundCategoryId')
            ->willReturn(self::FAKE_REFUND_CATEGORY_ID);

        $correctiveTransactions = $this->correctiveTransactionsGeneratorMock->generateByKnownPriceChange(
            $transactions,
            $amountNew,
            $currencyCodeNew,
            $amountOld,
            $currencyCodeOld,
            self::FAKE_PRODUCT_COST_CATEGORY_ID
        );

        $this->assertEquals($expectedCorrectiveTransactions, $correctiveTransactions);
    }

    public function generateProvider(): array
    {
        return [
            [
                [
                    [
                        'PostedDate' => '2020-01-01',
                        'Amount' => 100,
                        'AmountEUR' => 90,
                        'Currency' => 'EUR',
                        'CategoryId' => self::FAKE_PRINCIPAL_CATEGORY_ID,
                        'Quantity' => 1,
                        'MergeCounter' => 1,
                        'Version' => 1
                    ],
                    [
                        'PostedDate' => '2020-02-05',
                        'Amount' => -100,
                        'AmountEUR' => -90,
                        'Currency' => 'EUR',
                        'CategoryId' => self::FAKE_REFUND_CATEGORY_ID,
                        'Quantity' => 1,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]
                ],
                0,
                'EUR',
                110,
                'EUR',
                [
                    new Transaction([
                        'PostedDate' => '2020-01-01',
                        'Amount' =>  110 * 100 * -1,
                        'AmountEUR' => 110 * 100 * -1,
                        'Currency' => 'EUR',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 1,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                    new Transaction([
                        'PostedDate' => '2020-02-05',
                        'Amount' =>  110 * 100,
                        'AmountEUR' => 110 * 100,
                        'Currency' => 'EUR',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 1,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                ]
            ],
            [
                [
                    [
                        'PostedDate' => '2020-01-01',
                        'Amount' => 100,
                        'AmountEUR' => 90,
                        'Currency' => 'EUR',
                        'CategoryId' => self::FAKE_PRINCIPAL_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 1,
                        'Version' => 1
                    ],
                    [
                        'PostedDate' => '2020-02-05',
                        'Amount' => -100,
                        'AmountEUR' => -90,
                        'Currency' => 'EUR',
                        'CategoryId' => self::FAKE_REFUND_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]
                ],
                100,
                'EUR',
                90,
                'EUR',
                [
                    new Transaction([
                        'PostedDate' => '2020-01-01',
                        'Amount' => (100 - 90) * 100 * 2,
                        'AmountEUR' => (100 - 90) * 100 * 2,
                        'Currency' => 'EUR',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                    new Transaction([
                        'PostedDate' => '2020-02-05',
                        'Amount' => (100 - 90) * 100 * 2 * -1,
                        'AmountEUR' => (100 - 90) * 100 * 2 * -1,
                        'Currency' => 'EUR',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                ]
            ],
            [
                [
                    [
                        'PostedDate' => '2020-01-01',
                        'Amount' => 100,
                        'AmountEUR' => 90,
                        'Currency' => 'USD',
                        'CategoryId' => self::FAKE_PRINCIPAL_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 1,
                        'Version' => 1
                    ],
                    [
                        'PostedDate' => '2020-02-05',
                        'Amount' => -100,
                        'AmountEUR' => -90,
                        'Currency' => 'USD',
                        'CategoryId' => self::FAKE_REFUND_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]
                ],
                117,
                'USD',
                215,
                'THB',
                [
                    new Transaction([
                        'PostedDate' => '2020-01-01',
                        'Amount' => (int)((((117 / self::FAKE_USD_RATE) * self::FAKE_THB_RATE) - 215) * 100 * 2),
                        'AmountEUR' => (int)(((((117 / self::FAKE_USD_RATE) * self::FAKE_THB_RATE) - 215) / self::FAKE_THB_RATE) * 100 * 2),
                        'Currency' => 'THB',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                    new Transaction([
                        'PostedDate' => '2020-02-05',
                        'Amount' => (int)(((215 - (117 / self::FAKE_USD_RATE) * self::FAKE_THB_RATE)) * 100 * 2),
                        'AmountEUR' => (int)((((215 - (117 / self::FAKE_USD_RATE) * self::FAKE_THB_RATE)) / self::FAKE_THB_RATE) * 100 * 2),
                        'Currency' => 'THB',
                        'CategoryId' => 0,
                        'COGCategoryId' => self::FAKE_PRODUCT_COST_CATEGORY_ID,
                        'Quantity' => 2,
                        'MergeCounter' => 0,
                        'EventPeriodId' => null,
                        'Version' => 1
                    ]),
                ]
            ],
        ];
    }
}