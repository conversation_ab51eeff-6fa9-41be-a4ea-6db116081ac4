<?php

namespace Tests\unit\common\components\COGSync;

use common\components\COGSync\PeriodDateChangeCalculator;
use common\components\currencyRate\CurrencyRateManager;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;

class PeriodDateChangeCalculatorTest extends TestCase
{
    use ReflectionCallMethodTrait;

    private PeriodDateChangeCalculator $periodDateChangeCalculator;

    public function __construct(?string $name = null, array $data = [], $dataName = '')
    {
        $this->periodDateChangeCalculator = new PeriodDateChangeCalculator();
        parent::__construct($name, $data, $dataName);
    }

    /**
     * @dataProvider getCOGChangesProvider
     */
    public function testGetCOGChanges(
        string $dateTimeStartOld,
        string $dateTimeStartNew,
        string $dateTimeEndOld,
        string $dateTimeEndNew,
        float $amount,
        array $expectedResult
    )
    {
        $actualResult = $this->periodDateChangeCalculator->getCOGChanges(
            $dateTimeStartOld,
            $dateTimeStartNew,
            $dateTimeEndOld,
            $dateTimeEndNew,
            $amount
        );

        $this->assertEquals($expectedResult, $actualResult);
    }

    function getCOGChangesProvider(): array
    {
        return [
            // Moving date start a little left
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2020-05-18 16:54:03',
                '2022-07-14 15:45:53',
                15.34,
                [
                    [
                        'dateStart' => '2020-05-18 16:54:03',
                        'dateEnd' => '2020-05-20 11:34:11',
                        'amountOld' => 0,
                        'amountNew' => 15.34,
                    ]
                ]
            ],
            // Moving date start a little right
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2020-05-22 16:54:03',
                '2022-07-14 15:45:53',
                16.08,
                [
                    [
                        'dateStart' => '2020-05-20 11:34:12',
                        'dateEnd' => '2020-05-22 16:54:02',
                        'amountOld' => 16.08,
                        'amountNew' => 0
                    ]
                ]
            ],
            // Moving date start to the right, outside the date end
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2022-07-18 20:32:12',
                '2022-08-22 12:38:34',
                100.35,
                [
                    [
                        'dateStart' => '2020-05-20 11:34:12',
                        'dateEnd' => '2022-07-14 15:45:53',
                        'amountOld' => 100.35,
                        'amountNew' => 0
                    ],
                    [
                        'dateStart' => '2022-07-18 20:32:12',
                        'dateEnd' => '2022-08-22 12:38:34',
                        'amountOld' => 0,
                        'amountNew' => 100.35
                    ]
                ]
            ],
            // Moving date end a little left
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2020-05-20 11:34:12',
                '2022-07-14 12:45:53',
                100.35,
                [
                    [
                        'dateStart' => '2022-07-14 12:45:54',
                        'dateEnd' => '2022-07-14 15:45:53',
                        'amountOld' => 100.35,
                        'amountNew' => 0
                    ],
                ]
            ],
            // Moving date end a little right
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2020-05-20 11:34:12',
                '2022-07-14 17:45:53',
                100.35,
                [
                    [
                        'dateStart' => '2022-07-14 15:45:53',
                        'dateEnd' => '2022-07-14 17:45:53',
                        'amountOld' => 0,
                        'amountNew' => 100.35
                    ],
                ]
            ],
            // Nothing changed
            [
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                '2020-05-20 11:34:12',
                '2022-07-14 15:45:53',
                322.35,
                []
            ]
        ];
    }
}
