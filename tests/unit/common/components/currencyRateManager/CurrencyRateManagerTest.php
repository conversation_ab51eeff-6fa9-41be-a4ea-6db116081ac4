<?php

namespace Tests\unit\common\components\currencyRateManager;

use common\components\currencyRate\CurrencyRateManager;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;

class CurrencyRateManagerTest extends TestCase
{
    use ReflectionCallMethodTrait;

    /**
     * @dataProvider fillMissingRatesProvider
     */
    public function testFillMissingRates($fromDate, $toDate, $ratesFromApi, $expectedFilledRates)
    {
        $from = new \DateTime($fromDate);
        $to = new \DateTime($toDate);
        /** @var CurrencyRateManager $currencyRatesManagerMock */
        $currencyRatesManagerMock = $this
            ->getMockBuilder(CurrencyRateManager::class)
            ->disableOriginalConstructor()
            ->getMock()
        ;

        $filledRates = $this->callMethod($currencyRatesManagerMock, 'fillMissingRates', [$ratesFromApi, $from, $to]);

        foreach ($expectedFilledRates as $expectedRate) {
            $expectedRateKey = json_encode($expectedRate, JSON_THROW_ON_ERROR);

            foreach ($filledRates as $filledRate) {
                $filledRateKey = json_encode([
                    'date' => $filledRate['date'],
                    'currency_id' => $filledRate['currency_id'],
                    'value' => $filledRate['value']
                ], JSON_THROW_ON_ERROR);

                if ($filledRateKey === $expectedRateKey) {
                    continue 2;
                }
            }

            $this->assertTrue(false, "$expectedRateKey has not been filled");
        }
    }

    function fillMissingRatesProvider(): array
    {
        return [
            [
                (new \DateTime())->modify('-2 days')->format('Y-m-d'),
                (new \DateTime())->format('Y-m-d'),
                [
                    [
                        'date' => (new \DateTime())->modify('-2 days')->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                ],
                [
                    [
                        'date' => (new \DateTime())->modify('-2 days')->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => (new \DateTime())->modify('-1 days')->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => (new \DateTime())->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => (new \DateTime())->modify('+1 day')->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => (new \DateTime())->modify('+2 days')->format('Y-m-d'),
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                ]
            ],
            [
                '2020-05-01',
                '2020-05-06',
                [
                    [
                        'date' => '2020-05-01',
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => '2020-05-02',
                        'currency_id' => 'USD',
                        'value' => 1.2
                    ],
                    // ...
                    [
                        'date' => '2020-05-04',
                        'currency_id' => 'USD',
                        'value' => 1.3
                    ],
                    [
                        'date' => '2020-05-05',
                        'currency_id' => 'USD',
                        'value' => 1.4
                    ],

                    [
                        'date' => '2020-05-01',
                        'currency_id' => 'AUD',
                        'value' => 1.1
                    ],
                    // ...
                    [
                        'date' => '2020-05-03',
                        'currency_id' => 'AUD',
                        'value' => 1.2
                    ],
                    [
                        'date' => '2020-05-04',
                        'currency_id' => 'AUD',
                        'value' => 1.3
                    ],
                    [
                        'date' => '2020-05-05',
                        'currency_id' => 'AUD',
                        'value' => 1.4
                    ]
                ],
                [
                    [
                        'date' => '2020-05-01',
                        'currency_id' => 'USD',
                        'value' => 1.1
                    ],
                    [
                        'date' => '2020-05-02',
                        'currency_id' => 'USD',
                        'value' => 1.2
                    ],
                    [ // Missed rate
                        'date' => '2020-05-03',
                        'currency_id' => 'USD',
                        'value' => 1.2
                    ],
                    [
                        'date' => '2020-05-04',
                        'currency_id' => 'USD',
                        'value' => 1.3
                    ],
                    [
                        'date' => '2020-05-05',
                        'currency_id' => 'USD',
                        'value' => 1.4
                    ],
                    [
                        'date' => '2020-05-06',
                        'currency_id' => 'USD',
                        'value' => 1.4
                    ],

                    [
                        'date' => '2020-05-01',
                        'currency_id' => 'AUD',
                        'value' => 1.1
                    ],
                    [ // Missed rate
                        'date' => '2020-05-02',
                        'currency_id' => 'AUD',
                        'value' => 1.1
                    ],
                    [
                        'date' => '2020-05-03',
                        'currency_id' => 'AUD',
                        'value' => 1.2
                    ],
                    [
                        'date' => '2020-05-04',
                        'currency_id' => 'AUD',
                        'value' => 1.3
                    ],
                    [
                        'date' => '2020-05-05',
                        'currency_id' => 'AUD',
                        'value' => 1.4
                    ],
                    [
                        'date' => '2020-05-06',
                        'currency_id' => 'AUD',
                        'value' => 1.4
                    ],
                ]
            ]
        ];
    }
}
