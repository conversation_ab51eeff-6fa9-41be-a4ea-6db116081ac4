<?php

namespace Tests\unit\common\components\reports\dataProvider;

use common\components\reports\dataProvider\SellingApiProvider;
use common\models\AmazonMarketplace;
use common\models\Seller;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;
use yii\caching\ArrayCache;

class SellingApiProviderTest extends TestCase
{
    use ReflectionCallMethodTrait;

    private SellingApiProvider $provider;
    private ArrayCache $cache;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cache = new ArrayCache();
        $this->provider = $this->getMockBuilder(SellingApiProvider::class)
            ->onlyMethods(['getSellerMarketplaces', 'info'])
            ->getMock();

        // Inject cache
        $reflection = new \ReflectionClass($this->provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($this->provider, $this->cache);
    }

    /**
     * @dataProvider getApplicableMarketplacesProvider
     */
    public function testGetApplicableMarketplaces($sellerId, $sellerRegion, $sellerMarketplaces, $dbMarketplaces, $expectedResult)
    {
        // Create a testable provider that doesn't use real database
        $provider = $this->createTestableProvider($sellerId, $sellerRegion, $sellerMarketplaces, $dbMarketplaces);

        $result = $provider->getApplicableMarketplaces($sellerId);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGetApplicableMarketplacesSellerNotFound()
    {
        $provider = $this->getMockBuilder(SellingApiProvider::class)
            ->onlyMethods(['getSellerMarketplaces', 'info'])
            ->getMock();

        // Inject cache
        $reflection = new \ReflectionClass($provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($provider, $this->cache);

        // Override getApplicableMarketplaces to mock Seller::findOne returning null
        $provider = $this->getMockBuilder(SellingApiProvider::class)
            ->onlyMethods(['getSellerMarketplaces', 'info'])
            ->getMock();

        $reflection = new \ReflectionClass($provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($provider, $this->cache);

        // Create a partial mock that overrides findSeller method
        $provider = $this->getMockBuilder(TestableSellingApiProvider::class)
            ->onlyMethods(['findSeller', 'getSellerMarketplaces', 'info'])
            ->getMock();

        $provider->method('findSeller')->willReturn(null);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Seller not found: nonexistent');

        $provider->getApplicableMarketplaces('nonexistent');
    }

    public function testFilterMarketplacesByRegionWithCache()
    {
        $marketplaceIds = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS', 'ATVPDKIKX0DER'];
        $region = 'eu-west-1';
        $dbMarketplaces = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS'];

        // Pre-populate cache
        $cacheKey = "region_marketplaces_{$region}";
        $this->cache->set($cacheKey, $dbMarketplaces);

        $result = $this->callMethod($this->provider, 'filterMarketplacesByRegion', [$marketplaceIds, $region]);

        $this->assertEquals(['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS'], $result);
    }

    public function testFilterMarketplacesByRegionEmptyInput()
    {
        // Create a mock provider that doesn't call database
        $provider = $this->getMockBuilder(TestableSellingApiProvider::class)
            ->onlyMethods(['getRegionMarketplaces', 'info'])
            ->getMock();
        
        // Inject cache
        $reflection = new \ReflectionClass($provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($provider, $this->cache);
        
        $result = $this->callMethod($provider, 'filterMarketplacesByRegion', [[], 'eu-west-1']);
        
        $this->assertEquals([], $result);
    }

    public function getApplicableMarketplacesProvider(): array
    {
        return [
            'EU seller with matching marketplaces' => [
                'sellerId' => 'seller1',
                'sellerRegion' => 'eu-west-1',
                'sellerMarketplaces' => ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS', 'A13V1IB3VIYZZH'],
                'dbMarketplaces' => ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS', 'A13V1IB3VIYZZH', 'APJ6JRA9NG5V4'],
                'expectedResult' => ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS', 'A13V1IB3VIYZZH']
            ],
            'US seller with partial matching marketplaces' => [
                'sellerId' => 'seller2',
                'sellerRegion' => 'us-east-1',
                'sellerMarketplaces' => ['ATVPDKIKX0DER', 'A2EUQ1WTGCTBG2', 'A1PA6795UKMFR9'],
                'dbMarketplaces' => ['ATVPDKIKX0DER', 'A2EUQ1WTGCTBG2'],
                'expectedResult' => ['ATVPDKIKX0DER', 'A2EUQ1WTGCTBG2']
            ],
            'Seller with no matching marketplaces' => [
                'sellerId' => 'seller3',
                'sellerRegion' => 'ap-southeast-1',
                'sellerMarketplaces' => ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS'],
                'dbMarketplaces' => ['A19VAU5U5O7RUS', 'A21TJRUUN4KGV'],
                'expectedResult' => []
            ],
            'Empty seller marketplaces' => [
                'sellerId' => 'seller4',
                'sellerRegion' => 'eu-west-1',
                'sellerMarketplaces' => [],
                'dbMarketplaces' => ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS'],
                'expectedResult' => []
            ]
        ];
    }

    private function createTestableProvider(string $sellerId, string $sellerRegion, array $sellerMarketplaces, array $dbMarketplaces): SellingApiProvider
    {
        $provider = $this->getMockBuilder(TestableSellingApiProvider::class)
            ->onlyMethods(['findSeller', 'getSellerMarketplaces', 'getRegionMarketplaces', 'info'])
            ->getMock();
        
        // Create a simple object instead of mock
        $seller = new \stdClass();
        $seller->region = $sellerRegion;
        $seller->id = $sellerId;
        
        $provider->method('findSeller')->willReturn($seller);
        $provider->method('getSellerMarketplaces')->willReturn($sellerMarketplaces);
        $provider->method('getRegionMarketplaces')->willReturn($dbMarketplaces);
        
        // Inject cache
        $reflection = new \ReflectionClass($provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($provider, $this->cache);
        
        return $provider;
    }

    public function testCreateReportInAmazonWithInvalidMarketplaceIds()
    {
        // Create EU seller
        $sellerId = 'EU_SELLER';
        $sellerRegion = 'eu-west-1';
        $sellerMarketplaces = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS']; // EU marketplaces
        $dbMarketplaces = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS']; // EU marketplaces in DB

        $provider = $this->createTestableProviderForCreateReport($sellerId, $sellerRegion, $sellerMarketplaces, $dbMarketplaces);

        // Create Amazon report
        $amazonReport = new \stdClass();
        $amazonReport->seller_id = $sellerId;
        $amazonReport->start_date = '2024-01-01';
        $amazonReport->end_date = '2024-01-02';
        $amazonReport->amazon_type = 'GET_FLAT_FILE_ALL_ORDERS_DATA_BY_LAST_UPDATE_GENERAL';

        // Try to pass Mexican marketplace (should be filtered out)
        $config = [
            'marketplaceIds' => ['A1AM78C64UM0Y8'] // Mexican marketplace
        ];

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("No valid marketplaces found for seller {$sellerId} in region {$sellerRegion}");

        $provider->createReportInAmazon($amazonReport, $config);
    }

    public function testCreateReportInAmazonWithMixedMarketplaceIds()
    {
        // Create EU seller
        $sellerId = 'EU_SELLER';
        $sellerRegion = 'eu-west-1';
        $sellerMarketplaces = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS']; // EU marketplaces
        $dbMarketplaces = ['A1PA6795UKMFR9', 'A1RKKUPIHCS9HS']; // EU marketplaces in DB

        $provider = $this->createTestableProviderForCreateReport($sellerId, $sellerRegion, $sellerMarketplaces, $dbMarketplaces);

        // Create Amazon report
        $amazonReport = new \stdClass();
        $amazonReport->seller_id = $sellerId;
        $amazonReport->start_date = '2024-01-01';
        $amazonReport->end_date = '2024-01-02';
        $amazonReport->amazon_type = 'GET_FLAT_FILE_ALL_ORDERS_DATA_BY_LAST_UPDATE_GENERAL';

        // Mix of valid EU and invalid Mexican marketplaces
        $config = [
            'marketplaceIds' => ['A1PA6795UKMFR9', 'A1AM78C64UM0Y8'] // EU + Mexican
        ];

        // Should filter and use only valid EU marketplace
        $result = $provider->createReportInAmazon($amazonReport, $config);

        $this->assertEquals('test-report-id', $result);
    }

    private function createTestableProviderForCreateReport(string $sellerId, string $sellerRegion, array $sellerMarketplaces, array $dbMarketplaces): TestableSellingApiProvider
    {
        $provider = $this->getMockBuilder(TestableSellingApiProvider::class)
            ->onlyMethods(['findSeller', 'getSellerMarketplaces', 'getRegionMarketplaces', 'info', 'createReportsApi'])
            ->getMock();
        
        // Create seller object
        $seller = new \stdClass();
        $seller->region = $sellerRegion;
        $seller->id = $sellerId;
        
        $provider->method('findSeller')->willReturn($seller);
        $provider->method('getSellerMarketplaces')->willReturn($sellerMarketplaces);
        $provider->method('getRegionMarketplaces')->willReturn($dbMarketplaces);

        $reportsApiMock = new class {
            public function createReport(array $payload)
            {
                $result = new \stdClass();
                $result->reportId = 'test-report-id';
                return $result;
            }
        };
        $provider->method('createReportsApi')->willReturn($reportsApiMock);
        
        // Inject cache
        $reflection = new \ReflectionClass($provider);
        $cacheProperty = $reflection->getProperty('cache');
        $cacheProperty->setAccessible(true);
        $cacheProperty->setValue($provider, $this->cache);
        
        return $provider;
    }
}

// Testable version that allows mocking database calls
class TestableSellingApiProvider extends SellingApiProvider
{
    public function findSeller(string $sellerId)
    {
        return Seller::findOne($sellerId);
    }

    public function getRegionMarketplaces(string $region): array
    {
        return AmazonMarketplace::find()
            ->select('id')
            ->where([
                'is_active' => true,
                'region' => $region
            ])
            ->column();
    }

    public function createReportsApi($sellerId, $region)
    {
        // This will be mocked in tests
        return null;
    }
    
    public function createReportInAmazon($amazonReport, array $config = []): string
    {
        $seller = $this->findSeller($amazonReport->seller_id);

        $marketplaceIds = $config['marketplaceIds'] ?? $this->getApplicableMarketplaces($seller->id);

        // Filter provided marketplaceIds to only include applicable ones
        if (!empty($config['marketplaceIds'])) {
            $applicableMarketplaces = $this->getApplicableMarketplaces($seller->id);
            $marketplaceIds = array_intersect($config['marketplaceIds'], $applicableMarketplaces);

            if (empty($marketplaceIds)) {
                throw new \InvalidArgumentException(
                    "No valid marketplaces found for seller {$seller->id} in region {$seller->region}"
                );
            }
        }

        $reportType = $config['reportType'] ?? $amazonReport->amazon_type;

        $reportsApi = $this->createReportsApi($seller->id, $seller->region);
        $result = $reportsApi->createReport([
            'reportType' => $reportType,
            'dataStartTime' => (new \DateTime($amazonReport->start_date))->format('c'),
            'dataEndTime' => (new \DateTime($amazonReport->end_date))->format('c'),
            'marketplaceIds' => $marketplaceIds
        ]);

        return $result->reportId;
    }

    public function getApplicableMarketplaces(string $sellerId): array
    {
        $seller = $this->findSeller($sellerId);
        if (!$seller) {
            throw new \Exception("Seller not found: {$sellerId}");
        }

        $allMarketplaces = $this->getSellerMarketplaces($sellerId);
        $this->info("All seller marketplaces: " . implode(', ', $allMarketplaces));

        $regionCompatibleMarketplaces = $this->filterMarketplacesByRegion($allMarketplaces, $seller->region);
        $this->info("Region compatible marketplaces for {$seller->region}: " . implode(', ', $regionCompatibleMarketplaces));

        return $regionCompatibleMarketplaces;
    }

    protected function filterMarketplacesByRegion(array $marketplaceIds, string $region): array
    {
        if (empty($marketplaceIds)) {
            return [];
        }

        $cacheKey = "region_marketplaces_{$region}";
        $regionMarketplaceIds = $this->cache->get($cacheKey);

        if ($regionMarketplaceIds !== false) {
            return array_intersect($marketplaceIds, $regionMarketplaceIds);
        }

        $regionMarketplaceIds = $this->getRegionMarketplaces($region);
        $this->cache->set($cacheKey, $regionMarketplaceIds, 60 * 60);

        return array_intersect($marketplaceIds, $regionMarketplaceIds);
    }
}
