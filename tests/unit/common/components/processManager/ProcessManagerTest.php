<?php

namespace Tests\unit\common\components\processManager;

use common\components\processManager\ProcessManager;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;
use yii\caching\ArrayCache;

class ProcessManagerTest extends TestCase
{
    use ReflectionCallMethodTrait;

    public function testCalc()
    {
        // $processManager = new ProcessManager();
        // $processManager->setCache(new ArrayCache());
        // $processManager->register('process_1');
        // $processManager->register('process_2');
        // $processManager->register('process_3');
        //
        // $processes = $processManager->getAll();
        //
        // $this->assertCount(3, $processes);
        // $this->assertEquals('process_1', $processes['process_1']['name']);
        // $this->assertEquals('process_3', $processes['process_3']['name']);
        //
        // $processManager->release('process_1');
        // $processes = $processManager->getAll();
        //
        // $this->assertCount(2, $processes);
        // $this->assertArrayNotHasKey('process_1', $processes);
        // $this->assertEquals('process_2', $processes['process_2']['name']);
        // $this->assertEquals('process_3', $processes['process_3']['name']);
    }
}
