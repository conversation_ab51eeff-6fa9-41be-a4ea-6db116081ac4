<?php

namespace Tests\unit\common\components\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\widget\TransactionQueryExecutor;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;

class TransactionQueryModifierTest extends TestCase
{
    use ReflectionCallMethodTrait;

    private TransactionQueryExecutor $transactionQueryExecutor;

    public function setUp(): void
    {
        $this->transactionQueryExecutor = $this
            ->getMockBuilder(TransactionQueryExecutor::class)
            ->disableOriginalConstructor()
            ->getMock()
        ;

        parent::setUp();
    }

    /**
     * @dataProvider applyFiltersDataProvider
     */
    public function testApplyFilters(string $rawQuery, array $filters, $expectedResult): void
    {
        $filtersForm = new FiltersForm();
        $filtersForm->load($filters, '');

        $actualResult = $this->callMethod($this->transactionQueryExecutor, 'applyFilters', [$rawQuery, $filtersForm]);
        $this->assertArrayHasKey('query', $actualResult);
        $this->assertArrayHasKey('params', $actualResult);

        $actualResult['query'] = preg_replace("/\s{1,}/m", " ", $actualResult['query']);
        $expectedResult['query'] = preg_replace("/\s{1,}/m", " ", $expectedResult['query']);

        $this->assertEquals($expectedResult['query'] , $actualResult['query']);
        $this->assertEquals($expectedResult['params'] , $actualResult['params']);

    }

    public function testApplyFiltersWithoutPlaceholder(): void
    {
        // Placeholder with filters is missing
        $this->expectException(\Throwable::class);
        $this->callMethod($this->transactionQueryExecutor, 'applyFilters', ["SELECT * FROM transaction", new FiltersForm()]);
    }

    public function applyFiltersDataProvider(): array
    {
        return [
            'common case' => [
                "SELECT * FROM transaction WHERE 1 = 1 [FILTERS]",
                [
                    'dateStart' => '2020-01-01',
                    'dateEnd' => '2020-01-01',
                    'marketplaceSellerIds' => '[{"marketplaceId":"M1","sellerId":"S1"},{"marketplaceId":"M2","sellerId":"S2"}]',
                    'sellerId' => 'S1,S2',
                    'marketplaceId' => 'M1',
                    'sellerSku' => 'SKU1,SKU2,SKU3'
                ],
                [
                    'query' => "SELECT * FROM transaction 
                        WHERE 1 = 1
                        AND (PostedDate BETWEEN toDateTime(:date_start) AND toDateTime(:date_end))
                        AND ((SellerId = :seller_id_0 AND MarketplaceId = :marketplace_id_0)
                            OR 
                            (SellerId = :seller_id_1 AND MarketplaceId = :marketplace_id_1))
                        AND MarketplaceId IN(:marketplace_id_single_0)      
                        AND SellerId IN(:seller_id_single_0,:seller_id_single_1)
                        AND SellerSKU IN(:seller_sku_0,:seller_sku_1,:seller_sku_2)",
                    'params' => [
                        ':date_start' => '2020-01-01 00:00:00',
                        ':date_end' => '2020-01-01 23:59:59',
                        ':seller_id_0' => 'S1',
                        ':marketplace_id_0' => 'M1',
                        ':seller_id_1' => 'S2',
                        ':marketplace_id_1' => 'M2',
                        ':marketplace_id_single_0' => 'M1',
                        ':seller_id_single_0' => 'S1',
                        ':seller_id_single_1' => 'S2',
                        ':seller_sku_0' => 'SKU1',
                        ':seller_sku_1' => 'SKU2',
                        ':seller_sku_2' => 'SKU3',
                    ]
                ],
            ],
            'without non required filters' => [
                "SELECT * FROM transaction WHERE 1 = 1 [FILTERS]
                ",
                [
                    'dateStart' => '2022-01-01 02:00:00',
                    'dateEnd' => '2019-01-01 00:00:00',
                ],
                [
                    'query' => "SELECT * FROM transaction
                        WHERE 1 = 1
                        AND (PostedDate BETWEEN toDateTime(:date_start) AND toDateTime(:date_end))",
                    'params' => [
                        ':date_start' => '2022-01-01 02:00:00',
                        ':date_end' => '2019-01-01 23:59:59',
                    ]
                ]
            ],
        ];
    }
}