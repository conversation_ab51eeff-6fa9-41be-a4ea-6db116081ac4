<?php

namespace Tests\unit\common\components\profitCalculator;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\models\SalesCategory;
use PHPUnit\Framework\TestCase;
use Tests\_trait\ReflectionCallMethodTrait;

class ProfitCalculatorTest extends TestCase
{
    use ReflectionCallMethodTrait;

    public function testCalc()
    {
        /** @var ProfitCalculator $profitCalculator */
        $profitCalculator = $this
            ->getMockBuilder(ProfitCalculator::class)
            ->setMethods(['getDataSeries', 'getSalesCategoriesFatTreeData'])
            ->disableOriginalConstructor()
            ->getMock();

        $mockDataSeries = [
            [
                'sales_category_id' => 'c_1',
                'date' => '2021-01-02',
                'amount' => 400
            ],
            [
                'sales_category_id' => 'c_2',
                'date' => '2020-01-02',
                'amount' => -50
            ],
            [
                'sales_category_id' => 'c_2_1',
                'date' => '2020-01-02',
                'amount' => -100
            ],
            [
                'sales_category_id' => 'c_2_1_1',
                'date' => '2020-01-02',
                'amount' => -200
            ],
            [
                'sales_category_id' => 'c_2_1_2',
                'date' => '2020-01-02',
                'amount' => -30
            ],
        ];
        $profitCalculator
            ->method('getDataSeries')
            ->willReturn($mockDataSeries);

        $mockFlatTree = [
            [
                'id' => 'c_1',
                'name' => 'c_1_name',
                'parent_id' => null,
                'path' => 'c_1'
            ],
            [
                'id' => 'c_2',
                'name' => 'c_2_name',
                'parent_id' => null,
                'path' => 'c_2'
            ],
            [
                'id' => 'c_2_1',
                'name' => 'c_2_1_name',
                'parent_id' => 'c_2',
                'path' => 'c_2|c_2_1'
            ],
            [
                'id' => 'c_2_1_1',
                'name' => 'c_2_1_1_name',
                'parent_id' => 'c_2_1',
                'path' => 'c_2|c_2_1|c_2_1_1'
            ],
            [
                'id' => 'c_2_1_2',
                'name' => 'c_2_1_2_name',
                'parent_id' => 'c_2_1',
                'path' => 'c_2|c_2_1|c_2_1_2'
            ],
        ];
        $profitCalculator
            ->method('getSalesCategoriesFatTreeData')
            ->willReturn($mockFlatTree);

        $filtersForm = new FiltersForm();
        $filtersForm->currencyId = 'USD';
        $profitCalculator->setFiltersForm($filtersForm);

        $profitResult = $profitCalculator->calc();

        $expectedSalesCategories = [
            'c_1' => [
                'id' => 'c_1',
                'name' => 'c_1_name',
                'amount' => 400,
                'hasChildren' => false,
                'children' => []
            ],
            'c_2' => [
                'id' => 'c_2',
                'name' => 'c_2_name',
                'amount' => -380,
                'hasChildren' => true,
                'children' => [
                    'c_2_1' => [
                        'id' => 'c_2_1',
                        'name' => 'c_2_1_name',
                        'amount' => -330,
                        'hasChildren' => true,
                        'children' => [
                            'c_2_1_1' => [
                                'id' => 'c_2_1_1',
                                'name' => 'c_2_1_1_name',
                                'amount' => -200,
                                'hasChildren' => false,
                                'children' => []
                            ],
                            'c_2_1_2' => [
                                'id' => 'c_2_1_2',
                                'name' => 'c_2_1_2_name',
                                'amount' => -30,
                                'hasChildren' => false,
                                'children' => []
                            ],
                        ]
                    ],
                ]
            ]
        ];
        $actualSalesCategories = $profitResult->salesCategories;
        $this->assertEquals($expectedSalesCategories, $actualSalesCategories);
    }

    public function testCollapseDataSeries()
    {
        /** @var ProfitCalculator $profitCalculator */
        $profitCalculator = $this
            ->getMockBuilder(ProfitCalculator::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockDataSeries = [
            [
                'sales_category_id' => 'c_1',
                'date' => '2021-01-02',
                'amount' => 400
            ],
            [
                'sales_category_id' => 'c_1_1',
                'date' => '2021-01-03',
                'amount' => 15
            ],
            [
                'sales_category_id' => 'c_2',
                'date' => '2020-01-02',
                'amount' => -50
            ],
            [
                'sales_category_id' => 'c_2_1',
                'date' => '2020-01-02',
                'amount' => -100
            ],
            [
                'sales_category_id' => 'c_2_1_1',
                'date' => '2020-01-02',
                'amount' => -200
            ],
            [
                'sales_category_id' => 'c_2_1_2',
                'date' => '2020-01-02',
                'amount' => -30
            ],
        ];

        $mockFlatTree = [
            [
                'id' => 'c_1',
                'depth' => 0,
                'path' => 'c_1'
            ],
            [
                'id' => 'c_1_1',
                'depth' => 1,
                'path' => 'c_1|c_1_1'
            ],
            [
                'id' => 'c_2',
                'depth' => 0,
                'path' => 'c_2'
            ],
            [
                'id' => 'c_2_1',
                'depth' => 1,
                'path' => 'c_2|c_2_1'
            ],
            [
                'id' => 'c_2_1_1',
                'depth' => 2,
                'path' => 'c_2|c_2_1|c_2_1_1'
            ],
            [
                'id' => 'c_2_1_2',
                'depth' => 2,
                'path' => 'c_2|c_2_1|c_2_1_2'
            ],
        ];

        $expectedResult = [
            [
                'sales_category_id' => 'c_1',
                'date' => '2021-01-02',
                'amount' => 400
            ],
            [
                'sales_category_id' => 'c_1_1',
                'date' => '2021-01-03',
                'amount' => 15
            ],
            [
                'sales_category_id' => 'c_2',
                'date' => '2020-01-02',
                'amount' => -50
            ],
            [
                'sales_category_id' => 'c_2_1',
                'date' => '2020-01-02',
                'amount' => -330
            ],
        ];

        $actualResult = $this->callMethod($profitCalculator, 'collapseDataSeries', [
            $mockDataSeries,
            $mockFlatTree,
            1
        ]);

        $this->assertEquals($expectedResult, $actualResult);
    }
}