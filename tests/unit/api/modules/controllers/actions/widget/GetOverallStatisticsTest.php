<?php

namespace Tests\unit\api\modules\controllers\actions\widget;

use api\modules\v1\controllers\actions\widget\GetOverallStatistics;
use Tests\_trait\ReflectionCallMethodTrait;
use PHPUnit\Framework\TestCase;

class GetOverallStatisticsTest extends TestCase
{
    use ReflectionCallMethodTrait;

    /** @var GetOverallStatistics|\PHPUnit\Framework\MockObject\MockObject */
    private $overallStatisticsMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->overallStatisticsMock = $this
            ->getMockBuilder(GetOverallStatistics::class)
            ->disableOriginalConstructor()
            ->getMock();
    }

    /**
     * @dataProvider compareDiffPercentsProvider
     */
    public function testCompareDiffPercents($currentValue, $prevValue, $expectedResult)
    {
        $result = $this->callMethod($this->overallStatisticsMock, 'compareDiffPercents', [
            $currentValue, $prevValue
        ]);
        $this->assertEquals($expectedResult, $result);
    }

    public function compareDiffPercentsProvider(): array
    {
        return [
            [
                0,
                0,
                0
            ],
            [
                110,
                100,
                10
            ],
            [
                100,
                110,
                -9.09
            ],
            [
                65,
                65,
                0
            ],
            [
                100,
                -100,
                200
            ],
            [
                -100,
                100,
                -200
            ],
            [
                -150,
                0,
                -100
            ],
            [
                0,
                -150,
                100
            ],
            [
                -100,
                -110,
                -9.09
            ],
            [
                -110,
                -100,
                10
            ],
            [
                0,
                -586,
                100
            ],
            [
                -586,
                0,
                -100
            ],
            [
                6,
                3131,
                -99.81
            ],
            [
                -35161.41,
                -517399.7,
                -93.2
            ]
        ];
    }
}
