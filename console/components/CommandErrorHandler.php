<?php

namespace console\components;

use common\models\Command;
use yii\base\ErrorException;
use yii\console\ErrorHandler;
use Yii;

class CommandErrorHandler extends <PERSON>rrorHandler
{
    public function handleException($exception)
    {
        $this->markCommandAsFailed($exception->__toString());
        parent::handleException($exception);
    }

    public function handleFatalError()
    {
        $error = error_get_last();

        if (ErrorException::isFatalError($error)) {
            $exception = new ErrorException($error['message'], $error['type'], $error['type'], $error['file'], $error['line']);
            $this->markCommandAsFailed($exception->__toString());
        }

        parent::handleFatalError();
    }

    public function handleError($code, $message, $file, $line)
    {
        $exception = new ErrorException($message, $code, $code, $file, $line);
        $this->markCommandAsFailed($exception->__toString());
        parent::handleError($code, $message, $file, $line);
    }

    protected function markCommandAsFailed(string $message)
    {
        $command = method_exists(Yii::$app->controller,'getCommandModel') ? Yii::$app->controller->getCommandModel() : null;

        if ($command instanceof Command) {
            $command->markFailed($message);
        }
    }
}
