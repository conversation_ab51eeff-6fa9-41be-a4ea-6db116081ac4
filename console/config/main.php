<?php


declare(strict_types=1);

$params = array_merge(
    require(__DIR__ . '/../../common/config/params.php'),
    require(__DIR__ . '/params.php'),
);

$modules = [];

if (!YII_ENV_PROD) {
//    $modules['gii'] = [
//        'class' => 'yii\gii\Module',
//    ];
}

return [
    'id' => 'app-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log', 'rollbar'],
    'controllerNamespace' => 'console\controllers',
    'modules' => $modules,
    'components' => [
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info'],
                    'categories' => ['cron-scheduler'],
                    'logFile' => '@app/runtime/logs/cron/scheduler.log',
                    'logVars' => [],
                    'exportInterval' => 1
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info'],
                    'categories' => ['cron-executor'],
                    'logFile' => '@app/runtime/logs/cron/executor.log',
                    'logVars' => [],
                    'exportInterval' => 1
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning', 'trace'],
                    'categories' => ['load-data'],
                    'logFile' => '@app/runtime/logs/load-data.log',
                ],
                [
                    'class' => common\components\log\ConsoleTarget::class,
                    'levels' => ['info', 'warning', 'error'],
                    'categories' => ['console'],
                    'exportInterval' => 1,
                    'displayDate' => true,
                ],
            ],
        ],
        'session' => [
            'class' => 'yii\web\Session',
        ],
        'errorHandler' => [
            'class' => \baibaratsky\yii\rollbar\console\ErrorHandler::class,
        ],
    ],
    'controllerMap' => [
        'migrate' => [
            'class' => '\yii\console\controllers\MigrateController',
            'migrationNamespaces' => [
                'console\migrations\common',
            ],
            'migrationTable' => 'public.{{%migration}}',
        ],
        'migrate-customer' => [
            'class' => 'console\controllers\migrate\CustomerMigrateController',
            'migrationNamespaces' => [
                'console\migrations\customer',
            ],
        ],
        'migrate-finance' => [
            'class' => 'console\controllers\migrate\FinanceMigrateController',
            'migrationNamespaces' => [
                'console\migrations\finance',
            ],
        ],
        'migrate-order' => [
            'class' => 'console\controllers\migrate\OrderMigrateController',
            'migrationNamespaces' => [
                'console\migrations\order',
            ],
        ],
        'migrate-ads' => [
            'class' => 'console\controllers\migrate\AdsMigrateController',
            'migrationNamespaces' => [
                'console\migrations\ads',
            ],
        ],
        'migrate-clickhouse' => [
            'class' => 'console\controllers\migrate\ClickhouseMigrateCommonController',
            'migrationNamespaces' => [
                'console\migrations\clickhouse\common',
            ],
        ],
        'migrate-clickhouse-customer' => [
            'class' => 'console\controllers\migrate\ClickhouseMigrateCustomerController',
            'migrationNamespaces' => [
                'console\migrations\clickhouse\customer',
            ],
        ],
        'migrate-clickhouse-customer-related' => [
            'class' => 'console\controllers\migrate\ClickhouseMigrateCustomerRelatedController',
            'migrationNamespaces' => [
                'console\migrations\clickhouse\customerRelated',
            ],
        ],
        'migrate-repricer-event' => [
            'db' => 'repricerEventDb',
            'class' => 'console\controllers\migrate\RepricerEventMigrateController',
            'migrationNamespaces' => [
                'console\migrations\repricerEvent',
            ],
        ],
        'cache' => [
            'class' => 'console\controllers\CacheController',
        ],
    ],
    'params' => $params,
];
