CREATE TABLE order_00000_00000.order_period (
    id serial4 NOT NULL,
    start_date timestamp(0) NOT NULL,
    finish_date timestamp(0) NOT NULL,
    loading_status varchar(32) NOT NULL,
    "type" varchar(32) NOT NULL,
    created_at timestamp(0) NULL,
    updated_at timestamp(0) NULL,
    CONSTRAINT order_period_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-order_period_finish_date" ON order_00000_00000.order_period USING btree (finish_date);

CREATE TABLE order_00000_00000.unknown_amazon_order (
    amazon_order_id varchar(255) NOT NULL,
    errors_count int4 NOT NULL DEFAULT 0,
    created_at timestamp(0) NOT NULL,
    updated_at timestamp(0) NOT NULL,
    CONSTRAINT unknown_amazon_order_pkey PRIMARY KEY (amazon_order_id)
);
CREATE INDEX "idx-unknown_amazon_order-amazon_order_id" ON order_00000_00000.unknown_amazon_order USING btree (amazon_order_id);

CREATE TABLE order_00000_00000.amazon_order (
    id serial4 NOT NULL,
    order_period_id int4 NULL,
    seller_id varchar(255) NOT NULL,
    amazon_order_id varchar(255) NOT NULL,
    seller_order_id varchar(255) NULL,
    purchase_date timestamp(0) NOT NULL,
    last_update_date timestamp(0) NOT NULL,
    order_status varchar(255) NOT NULL,
    marketplace_id varchar(255) NULL,
    order_type varchar(255) NULL,
    created_at timestamp(0) NOT NULL,
    updated_at timestamp(0) NOT NULL,
    items_loading_status varchar(20) NOT NULL DEFAULT 'NEW'::character varying,
    fulfillment_channel varchar(3) NULL DEFAULT NULL::character varying,
    latest_ship_date timestamp(0) NULL,
    earliest_ship_date timestamp(0) NULL,
    is_business_order bool NULL,
    is_prime bool NULL,
    is_premium_order bool NULL,
    is_global_express_enabled bool NULL,
    is_replacement_order bool NULL,
    is_sold_by_ab bool NULL,
    is_ispu bool NULL,
    is_access_point_order bool NULL,
    has_regulated_items bool NULL,
    shipment_service_level_category varchar(255) NULL,
    ship_service_level varchar(255) NULL,
    payment_method varchar(255) NULL,
    has_transactions bool NOT NULL DEFAULT false,
    order_status_from_report varchar(255) NULL,
    has_manual_shipping_cost bool NOT NULL DEFAULT false,
    CONSTRAINT amazon_order_amazon_order_id_key UNIQUE (amazon_order_id),
    CONSTRAINT amazon_order_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-amazon_order-order_period_id" FOREIGN KEY (order_period_id) REFERENCES order_00000_00000.order_period(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX amazon_order_status_key ON order_00000_00000.amazon_order USING btree (order_status);
CREATE INDEX "idx-amazon_order-amazon_order_id" ON order_00000_00000.amazon_order USING btree (amazon_order_id);
CREATE INDEX "idx-amazon_order-has_transactions" ON order_00000_00000.amazon_order USING btree (has_transactions);
CREATE INDEX "idx-amazon_order-items_loading_status" ON order_00000_00000.amazon_order USING btree (items_loading_status);
CREATE INDEX "idx-amazon_order-order_period_id" ON order_00000_00000.amazon_order USING btree (order_period_id);
CREATE INDEX "idx-amazon_order-order_status" ON order_00000_00000.amazon_order USING btree (order_status);
CREATE INDEX "idx-amazon_order-purchase_date" ON order_00000_00000.amazon_order USING btree (purchase_date);
CREATE INDEX "idx-amazon_order-updated_at" ON order_00000_00000.amazon_order USING btree (updated_at);

CREATE TABLE order_00000_00000.amazon_order_item (
    id serial4 NOT NULL,
    order_id varchar(255) NOT NULL,
    asin varchar(20) NULL DEFAULT NULL::character varying,
    sku varchar(50) NOT NULL,
    order_item_id varchar(50) NOT NULL,
    title varchar(200) NULL DEFAULT NULL::character varying,
    quantity int4 NOT NULL DEFAULT 0,
    quantity_shipped int4 NOT NULL DEFAULT 0,
    item_price numeric(11, 2) NOT NULL DEFAULT 0,
    shipping_price numeric(11, 2) NOT NULL DEFAULT 0,
    item_tax numeric(11, 2) NOT NULL DEFAULT 0,
    shipping_tax numeric(11, 2) NOT NULL DEFAULT 0,
    shipping_discount numeric(11, 2) NOT NULL DEFAULT 0,
    promotion_discount numeric(11, 2) NOT NULL DEFAULT 0,
    cod_fee numeric(11, 2) NOT NULL DEFAULT 0,
    cod_fee_discount numeric(11, 2) NOT NULL DEFAULT 0,
    promotion_id varchar(200) NULL DEFAULT NULL::character varying,
    condition_id varchar(20) NULL DEFAULT NULL::character varying,
    condition_subtype_id varchar(20) NULL DEFAULT NULL::character varying,
    condition_note varchar(200) NULL DEFAULT NULL::character varying,
    scheduled_delivery_start_date timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    scheduled_delivery_end_date timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    order_purchase_date timestamp(0) NOT NULL,
    order_marketplace_id varchar(20) NOT NULL,
    "date" timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    profit numeric(11, 2) NULL DEFAULT NULL::numeric,
    is_gift bool NULL,
    is_transparency bool NULL,
    manual_shipping_cost numeric(10, 2) NULL,
    manual_shipping_cost_currency varchar(3) NULL DEFAULT NULL::character varying,
    CONSTRAINT amazon_order_item_order_item_id_key UNIQUE (order_item_id),
    CONSTRAINT amazon_order_item_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-amazon_order_item-amazon_order_id" FOREIGN KEY (order_id) REFERENCES order_00000_00000.amazon_order(amazon_order_id) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX "idx-amazon_order_item-order_id" ON order_00000_00000.amazon_order_item USING btree (order_id);

CREATE OR REPLACE VIEW order_00000_00000.amazon_order_view
AS SELECT
    amazon_order_item.id,
    amazon_order_item.order_id,
    amazon_order_item.asin,
    amazon_order_item.sku,
    amazon_order_item.order_item_id,
    COALESCE(amazon_order_item.title, ''::character varying) AS title,
    COALESCE(amazon_order_item.quantity, 0) AS quantity,
    COALESCE(amazon_order_item.quantity_shipped, 0)::bigint AS quantity_shipped,
    COALESCE(amazon_order_item.item_price * 100::numeric, 0::numeric)::bigint AS item_price,
    COALESCE(amazon_order_item.shipping_price * 100::numeric, 0::numeric)::bigint AS shipping_price,
    COALESCE(amazon_order_item.item_tax * 100::numeric, 0::numeric)::bigint AS item_tax,
    COALESCE(amazon_order_item.shipping_tax * 100::numeric, 0::numeric)::bigint AS shipping_tax,
    COALESCE(amazon_order_item.shipping_discount * 100::numeric, 0::numeric)::bigint AS shipping_discount,
    COALESCE(amazon_order_item.promotion_discount * 100::numeric, 0::numeric)::bigint AS promotion_discount,
    COALESCE(amazon_order_item.cod_fee * 100::numeric, 0::numeric)::bigint AS cod_fee,
    COALESCE(amazon_order_item.cod_fee_discount * 100::numeric, 0::numeric)::bigint AS cod_fee_discount,
    COALESCE(amazon_order_item.promotion_id, ''::character varying) AS promotion_id,
    COALESCE(amazon_order_item.condition_id, ''::character varying) AS condition_id,
    COALESCE(amazon_order_item.condition_subtype_id, ''::character varying) AS condition_subtype_id,
    COALESCE(amazon_order_item.condition_note, ''::character varying) AS condition_note,
    COALESCE(amazon_order_item.scheduled_delivery_start_date, '1970-01-01 00:00:00'::timestamp without time zone) AS scheduled_delivery_start_date,
    COALESCE(amazon_order_item.scheduled_delivery_end_date, '1970-01-01 00:00:00'::timestamp without time zone) AS scheduled_delivery_end_date,
    amazon_order_item.order_purchase_date,
    COALESCE(amazon_order_item.order_marketplace_id, ''::character varying) AS order_marketplace_id,
    amazon_order_item.date,
    COALESCE(amazon_order_item.profit * 100::numeric, 0::numeric)::bigint AS profit,
    COALESCE(amazon_order.order_period_id, 0) AS order_period_id,
    amazon_order.seller_id,
    COALESCE(amazon_order.seller_order_id, ''::character varying) AS seller_order_id,
    amazon_order.last_update_date,
    COALESCE(amazon_order.order_status, ''::character varying) AS order_status,
    COALESCE(amazon_order.order_type, ''::character varying) AS order_type,
    amazon_order.created_at,
    amazon_order.updated_at,
    COALESCE(amazon_order.fulfillment_channel, ''::character varying) AS fulfillment_channel,
    COALESCE(amazon_order.is_business_order, NULL::boolean) AS is_business_order,
    COALESCE(amazon_marketplace.currency_code, ''::character varying) AS currency_code
FROM order_00000_00000.amazon_order_item amazon_order_item
LEFT JOIN order_00000_00000.amazon_order amazon_order ON amazon_order_item.order_id::text = amazon_order.amazon_order_id::text
LEFT JOIN public.amazon_marketplace amazon_marketplace ON amazon_order.marketplace_id::text = amazon_marketplace.id::text
WHERE amazon_order.items_loading_status::text = 'FINISHED'::text;
