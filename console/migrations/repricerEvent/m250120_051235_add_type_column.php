<?php

namespace console\migrations\repricerEvent;

use yii\db\Migration;

/**
 * Class m250120_051235_add_type_column
 */
class m250120_051235_add_type_column extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('repricer_event_buffer', 'offer_type', $this->string()->defaultValue(''));
        $this->addColumn('repricer_event_storage', 'offer_type', $this->string()->defaultValue(''));

        $this->dropPrimaryKey(
            'pk-repricer_event_buffer',
            'repricer_event_buffer'
        );
        $this->dropPrimaryKey(
            'pk-repricer_event_storage',
            'repricer_event_storage'
        );

        $this->addPrimaryKey(
            'pk-repricer_event_buffer',
            'repricer_event_buffer',
            ['day', 'product_id', 'offer_type']
        );
        $this->addPrimaryKey(
            'pk-repricer_event_storage',
            'repricer_event_storage',
            ['day', 'product_id', 'offer_type']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('repricer_event_buffer', 'offer_type');
        $this->dropColumn('repricer_event_storage', 'offer_type');
    }
}
