<?php

namespace console\migrations\repricerEvent;

use yii\db\Migration;

/**
 * Handles the creation of table repricer_event_buffer.
 */
class m241024_113515_create_repricer_event_buffer_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('repricer_event_buffer', [
            'day' => $this->date()->notNull(),
            'product_id' => $this->integer()->notNull(),
            'sku' => $this->string()->notNull(),
            'marketplace_id' => $this->string()->notNull(),
            'seller_id' => $this->string()->notNull(),
            'amount' => $this->integer()->notNull(),
        ]);

        $this->addPrimaryKey(
            'pk-repricer_event_buffer',
            'repricer_event_buffer',
            ['day', 'product_id']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropPrimaryKey(
            'pk-repricer_event_buffer',
            'repricer_event_buffer'
        );

        $this->dropTable('repricer_event_buffer');
    }
}
