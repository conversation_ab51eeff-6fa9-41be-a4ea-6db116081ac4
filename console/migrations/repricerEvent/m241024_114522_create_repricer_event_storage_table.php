<?php

namespace console\migrations\repricerEvent;

use yii\db\Migration;

/**
 * Handles the creation of table repricer_event_storage.
 */
class m241024_114522_create_repricer_event_storage_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('repricer_event_storage', [
            'day' => $this->date()->notNull(),
            'product_id' => $this->integer()->notNull(),
            'sku' => $this->string()->notNull(),
            'marketplace_id' => $this->string()->notNull(),
            'seller_id' => $this->string()->notNull(),
            'amount' => $this->integer()->notNull(),
        ]);

        $this->addPrimaryKey(
            'pk-repricer_event_storage',
            'repricer_event_storage',
            ['day', 'product_id']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropPrimaryKey(
            'pk-repricer_event_storage',
            'repricer_event_storage'
        );

        $this->dropTable('repricer_event_storage');
    }
}
