CREATE TABLE finance_00000_00000.event_group (
    id varchar(255) NOT NULL,
    "ProcessingStatus" varchar(255) NULL,
    "FundTransferStatus" varchar(255) NULL,
    "OriginalTotal" jsonb NULL,
    "ConvertedTotal" jsonb NULL,
    "FundTransferDate" timestamp(0) NULL,
    "TraceId" varchar(255) NULL,
    "AccountTail" varchar(255) NULL,
    "BeginningBalance" jsonb NULL,
    "FinancialEventGroupStart" timestamp(0) NULL,
    "FinancialEventGroupEnd" timestamp(0) NULL,
    loading_status varchar(255) NULL,
    created_at timestamp(0) NULL,
    updated_at timestamp(0) NULL,
    CONSTRAINT "event-group-id_pk" PRIMARY KEY (id),
    CONSTRAINT event_group_id_key UNIQUE (id)
);

CREATE TABLE finance_00000_00000.event_period (
    id serial4 NOT NULL,
    start_date timestamp(0) NOT NULL,
    finish_date timestamp(0) NOT NULL,
    loading_status varchar(32) NOT NULL,
    "type" varchar(32) NOT NULL,
    created_at timestamp(0) NULL,
    updated_at timestamp(0) NULL,
    clickhouse_version int4 NULL DEFAULT 0,
    clickhouse_queued_at timestamp(0) NULL,
    clickhouse_moved_at timestamp(0) NULL,
    clickhouse_status varchar(20) NOT NULL DEFAULT 'new'::character varying,
    clickhouse_exception text NULL,
    has_transactions bool NULL DEFAULT true,
    time_between_attempts_m int4 NULL,
    attempts_made int4 NULL DEFAULT 0,
    stop_repeating_at timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    count_before_merge int4 NULL,
    CONSTRAINT event_period_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-event_period-clickhouse_status" ON finance_00000_00000.event_period USING btree (clickhouse_status);
CREATE INDEX "idx-event_period-finish_date" ON finance_00000_00000.event_period USING btree (finish_date);
CREATE INDEX "idx-event_period-loading_status-type" ON finance_00000_00000.event_period USING btree (loading_status, type);

CREATE TABLE finance_00000_00000.adhoc_disbursement (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "TransactionType" varchar(255) NULL,
    "TransactionId" varchar(255) NULL,
    "TransactionAmount" jsonb NULL,
    CONSTRAINT adhoc_disbursement_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-adhoc_disbursement-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.adjustment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AdjustmentType" varchar(255) NULL,
    "PostedDate" timestamp(0) NULL,
    "AdjustmentAmount" jsonb NULL,
    "AdjustmentItemList" jsonb NULL,
    CONSTRAINT adjustment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-adjustment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-adjustment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-adjustment-event_period_id" ON finance_00000_00000.adjustment USING btree (event_period_id);
CREATE INDEX "idx-adjustment-financial_event_group_id" ON finance_00000_00000.adjustment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.affordability_expense (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "PostedDate" varchar(255) NULL,
    "MarketplaceId" varchar(255) NULL,
    "TransactionType" varchar(255) NULL,
    "BaseExpense" jsonb NULL,
    "TaxTypeCGST" jsonb NULL,
    "TaxTypeSGST" jsonb NULL,
    "TaxTypeIGST" jsonb NULL,
    "TotalExpense" jsonb NULL,
    CONSTRAINT affordability_expense_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-affordability_expense-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-affordability_expense-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-affordability_expense-event_period_id" ON finance_00000_00000.affordability_expense USING btree (event_period_id);
CREATE INDEX "idx-affordability_expense-financial_event_group_id" ON finance_00000_00000.affordability_expense USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.affordability_expense_reversal (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "PostedDate" varchar(255) NULL,
    "MarketplaceId" varchar(255) NULL,
    "TransactionType" varchar(255) NULL,
    "BaseExpense" jsonb NULL,
    "TaxTypeCGST" jsonb NULL,
    "TaxTypeSGST" jsonb NULL,
    "TaxTypeIGST" jsonb NULL,
    "TotalExpense" jsonb NULL,
    CONSTRAINT affordability_expense_reversal_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-affordability_expense_reversal-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-affordability_expense_reversal-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-affordability_expense_reversal-event_period_id" ON finance_00000_00000.affordability_expense_reversal USING btree (event_period_id);
CREATE INDEX "idx-affordability_expense_reversal-financial_event_group_id" ON finance_00000_00000.affordability_expense_reversal USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.capacity_reservation_billing (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "TransactionType" varchar(255) NULL,
    "Description" text NULL,
    "TransactionAmount" jsonb NULL,
    CONSTRAINT capacity_reservation_billing_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-capacity_reservation_billing-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.charge_refund (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "ReasonCode" varchar(255) NULL,
    "ReasonCodeDescription" text NULL,
    "ChargeRefundTransactions" jsonb NULL,
    CONSTRAINT charge_refund_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-charge_refund-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.chargeback (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceName" varchar(255) NULL,
    "OrderChargeList" jsonb NULL,
    "OrderChargeAdjustmentList" jsonb NULL,
    "ShipmentFeeList" jsonb NULL,
    "ShipmentFeeAdjustmentList" jsonb NULL,
    "OrderFeeList" jsonb NULL,
    "OrderFeeAdjustmentList" jsonb NULL,
    "DirectPaymentList" jsonb NULL,
    "PostedDate" timestamp(0) NULL,
    "ShipmentItemList" jsonb NULL,
    "ShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT chargeback_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-chargeback-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-chargeback-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-chargeback-event_period_id" ON finance_00000_00000.chargeback USING btree (event_period_id);
CREATE INDEX "idx-chargeback-financial_event_group_id" ON finance_00000_00000.chargeback USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.coupon_payment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "PostedDate" timestamp(0) NULL,
    "CouponId" varchar(255) NULL,
    "SellerCouponDescription" varchar(2048) NULL,
    "ClipOrRedemptionCount" int4 NULL,
    "PaymentEventId" varchar(255) NULL,
    "FeeComponent" jsonb NULL,
    "ChargeComponent" jsonb NULL,
    "TotalAmount" jsonb NULL,
    CONSTRAINT coupon_payment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-coupon_payment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-coupon_payment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-coupon_payment-event_period_id" ON finance_00000_00000.coupon_payment USING btree (event_period_id);
CREATE INDEX "idx-coupon_payment-financial_event_group_id" ON finance_00000_00000.coupon_payment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.debt_recovery (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "DebtRecoveryType" varchar(255) NULL,
    "RecoveryAmount" jsonb NULL,
    "OverPaymentCredit" jsonb NULL,
    "DebtRecoveryItemList" jsonb NULL,
    "ChargeInstrumentList" jsonb NULL,
    CONSTRAINT debt_recovery_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-debt_recovery-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-debt_recovery-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-debt_recovery-event_period_id" ON finance_00000_00000.debt_recovery USING btree (event_period_id);
CREATE INDEX "idx-debt_recovery-financial_event_group_id" ON finance_00000_00000.debt_recovery USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.failed_adhoc_disbursement (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "FundsTransfersType" varchar(255) NULL,
    "TransferId" varchar(255) NULL,
    "DisbursementId" varchar(255) NULL,
    "PaymentDisbursementType" varchar(255) NULL,
    "Status" varchar(255) NULL,
    "TransferAmount" jsonb NULL,
    CONSTRAINT failed_adhoc_disbursement_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-failed_adhoc_disbursement-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.fba_liquidation (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "PostedDate" timestamp(0) NULL,
    "OriginalRemovalOrderId" varchar(255) NULL,
    "LiquidationProceedsAmount" jsonb NULL,
    "LiquidationFeeAmount" jsonb NULL,
    CONSTRAINT fba_liquidation_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-fba_liquidation-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-fba_liquidation-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-fba_liquidation-event_period_id" ON finance_00000_00000.fba_liquidation USING btree (event_period_id);
CREATE INDEX "idx-fba_liquidation-financial_event_group_id" ON finance_00000_00000.fba_liquidation USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.guarantee_claim (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceName" varchar(255) NULL,
    "OrderChargeList" jsonb NULL,
    "OrderChargeAdjustmentList" jsonb NULL,
    "ShipmentFeeList" jsonb NULL,
    "ShipmentFeeAdjustmentList" jsonb NULL,
    "OrderFeeList" jsonb NULL,
    "OrderFeeAdjustmentList" jsonb NULL,
    "DirectPaymentList" jsonb NULL,
    "PostedDate" timestamp(0) NULL,
    "ShipmentItemList" jsonb NULL,
    "ShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT guarantee_claim_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-guarantee_claim-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-guarantee_claim-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-guarantee_claim-event_period_id" ON finance_00000_00000.guarantee_claim USING btree (event_period_id);
CREATE INDEX "idx-guarantee_claim-financial_event_group_id" ON finance_00000_00000.guarantee_claim USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.imaging_services_fee (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "ImagingRequestBillingItemID" varchar(255) NULL,
    "ASIN" varchar(255) NULL,
    "PostedDate" timestamp(0) NULL,
    "FeeList" jsonb NULL,
    CONSTRAINT imaging_services_fee_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-imaging_services_fee-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-imaging_services_fee-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-imaging_services_fee-event_period_id" ON finance_00000_00000.imaging_services_fee USING btree (event_period_id);
CREATE INDEX "idx-imaging_services_fee-financial_event_group_id" ON finance_00000_00000.imaging_services_fee USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.loan_servicing (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "LoanAmount" jsonb NULL,
    "SourceBusinessEventType" varchar(255) NULL,
    CONSTRAINT loan_servicing_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-loan_servicing-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-loan_servicing-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-loan_servicing-event_period_id" ON finance_00000_00000.loan_servicing USING btree (event_period_id);
CREATE INDEX "idx-loan_servicing-financial_event_group_id" ON finance_00000_00000.loan_servicing USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.network_commingling_transaction (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "TransactionType" varchar(255) NULL,
    "PostedDate" timestamp(0) NULL,
    "NetCoTransactionID" varchar(255) NULL,
    "SwapReason" varchar(255) NULL,
    "ASIN" varchar(255) NULL,
    "MarketplaceId" varchar(255) NULL,
    "TaxExclusiveAmount" jsonb NULL,
    "TaxAmount" jsonb NULL,
    CONSTRAINT network_commingling_transaction_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-network_commingling_transaction-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-network_commingling_transaction-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-network_commingling_transaction-event_period_id" ON finance_00000_00000.network_commingling_transaction USING btree (event_period_id);
CREATE INDEX "idx-network_commingling_transaction-financial_event_group_id" ON finance_00000_00000.network_commingling_transaction USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.pay_with_amazon (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "SellerOrderId" varchar(255) NULL,
    "TransactionPostedDate" timestamp(0) NULL,
    "BusinessObjectType" varchar(255) NULL,
    "SalesChannel" varchar(255) NULL,
    "Charge" jsonb NULL,
    "FeeList" jsonb NULL,
    "PaymentAmountType" varchar(255) NULL,
    "AmountDescription" varchar(255) NULL,
    "FulfillmentChannel" varchar(255) NULL,
    "StoreName" varchar(255) NULL,
    CONSTRAINT pay_with_amazon_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-pay_with_amazon-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-pay_with_amazon-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-pay_with_amazon-event_period_id" ON finance_00000_00000.pay_with_amazon USING btree (event_period_id);
CREATE INDEX "idx-pay_with_amazon-financial_event_group_id" ON finance_00000_00000.pay_with_amazon USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.product_ads_payment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "postedDate" timestamp(0) NULL,
    "transactionType" varchar(255) NULL,
    "invoiceId" varchar(255) NULL,
    "baseValue" jsonb NULL,
    "taxValue" jsonb NULL,
    "transactionValue" jsonb NULL,
    CONSTRAINT product_ads_payment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-product_ads_payment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-product_ads_payment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-product_ads_payment-event_period_id" ON finance_00000_00000.product_ads_payment USING btree (event_period_id);
CREATE INDEX "idx-product_ads_payment-financial_event_group_id" ON finance_00000_00000.product_ads_payment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.refund (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceName" varchar(255) NULL,
    "OrderChargeList" jsonb NULL,
    "OrderChargeAdjustmentList" jsonb NULL,
    "ShipmentFeeList" jsonb NULL,
    "ShipmentFeeAdjustmentList" jsonb NULL,
    "OrderFeeList" jsonb NULL,
    "OrderFeeAdjustmentList" jsonb NULL,
    "DirectPaymentList" jsonb NULL,
    "PostedDate" timestamp(0) NULL,
    "ShipmentItemList" jsonb NULL,
    "ShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT refund_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-refund-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-refund-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-refund-event_period_id" ON finance_00000_00000.refund USING btree (event_period_id);
CREATE INDEX "idx-refund-financial_event_group_id" ON finance_00000_00000.refund USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.removal_shipment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "MerchantOrderId" varchar(255) NULL,
    "OrderId" varchar(255) NULL,
    "TransactionType" varchar(255) NULL,
    "TransactionAmount" jsonb NULL,
    "RemovalShipmentItemList" jsonb NULL,
    CONSTRAINT removal_shipment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-removal_shipment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.removal_shipment_adjustment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "AdjustmentEventId" varchar(255) NULL,
    "MerchantOrderId" varchar(255) NULL,
    "OrderId" varchar(255) NULL,
    "TransactionType" varchar(255) NULL,
    "TransactionAmount" jsonb NULL,
    "RemovalShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT removal_shipment_adjustment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-removal_shipment_adjustment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.rental_transaction (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "RentalEventType" varchar(255) NULL,
    "ExtensionLength" int4 NULL,
    "PostedDate" timestamp(0) NULL,
    "RentalChargeList" jsonb NULL,
    "RentalFeeList" jsonb NULL,
    "MarketplaceName" varchar(255) NULL,
    "RentalInitialValue" jsonb NULL,
    "RentalReimbursement" jsonb NULL,
    "RentalTaxWithheldList" jsonb NULL,
    CONSTRAINT rental_transaction_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-rental_transaction-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-rental_transaction-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-rental_transaction-event_period_id" ON finance_00000_00000.rental_transaction USING btree (event_period_id);
CREATE INDEX "idx-rental_transaction-financial_event_group_id" ON finance_00000_00000.rental_transaction USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.retrocharge (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "RetrochargeEventType" varchar(255) NULL,
    "AmazonOrderId" varchar(255) NULL,
    "PostedDate" timestamp(0) NULL,
    "BaseTax" jsonb NULL,
    "ShippingTax" jsonb NULL,
    "MarketplaceName" varchar(255) NULL,
    "RetrochargeTaxWithheldList" jsonb NULL,
    CONSTRAINT retrocharge_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-retrocharge-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-retrocharge-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-retrocharge-event_period_id" ON finance_00000_00000.retrocharge USING btree (event_period_id);
CREATE INDEX "idx-retrocharge-financial_event_group_id" ON finance_00000_00000.retrocharge USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.safet_reimbursement (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "PostedDate" timestamp(0) NULL,
    "SAFETClaimId" varchar(255) NULL,
    "ReimbursedAmount" jsonb NULL,
    "ReasonCode" varchar(255) NULL,
    "SAFETReimbursementItemList" jsonb NULL,
    CONSTRAINT safet_reimbursement_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-safet_reimbursement-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-safet_reimbursement-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-safet_reimbursement-event_period_id" ON finance_00000_00000.safet_reimbursement USING btree (event_period_id);
CREATE INDEX "idx-safet_reimbursement-financial_event_group_id" ON finance_00000_00000.safet_reimbursement USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.seller_deal_payment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "postedDate" timestamp(0) NULL,
    "dealId" varchar(255) NULL,
    "dealDescription" varchar(255) NULL,
    "eventType" varchar(255) NULL,
    "feeType" varchar(255) NULL,
    "feeAmount" jsonb NULL,
    "taxAmount" jsonb NULL,
    "totalAmount" jsonb NULL,
    CONSTRAINT seller_deal_payment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-seller_deal_payment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-seller_deal_payment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-seller_deal_payment-event_period_id" ON finance_00000_00000.seller_deal_payment USING btree (event_period_id);
CREATE INDEX "idx-seller_deal_payment-financial_event_group_id" ON finance_00000_00000.seller_deal_payment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.seller_review_enrollment_payment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "PostedDate" timestamp(0) NULL,
    "EnrollmentId" varchar(255) NULL,
    "ParentASIN" varchar(255) NULL,
    "FeeComponent" jsonb NULL,
    "ChargeComponent" jsonb NULL,
    "TotalAmount" jsonb NULL,
    CONSTRAINT seller_review_enrollment_payment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-seller_review_enrollment_payment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-seller_review_enrollment_payment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-seller_review_enrollment_payment-event_period_id" ON finance_00000_00000.seller_review_enrollment_payment USING btree (event_period_id);
CREATE INDEX "idx-seller_review_enrollment_payment-financial_event_group_id" ON finance_00000_00000.seller_review_enrollment_payment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.service_fee (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "FeeReason" varchar(255) NULL,
    "FeeList" jsonb NULL,
    "SellerSKU" varchar(255) NULL,
    "FnSKU" varchar(255) NULL,
    "FeeDescription" varchar(2048) NULL,
    "ASIN" varchar(255) NULL,
    CONSTRAINT service_fee_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-service_fee-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-service_fee-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-service_fee-event_period_id" ON finance_00000_00000.service_fee USING btree (event_period_id);
CREATE INDEX "idx-service_fee-financial_event_group_id" ON finance_00000_00000.service_fee USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.service_provider_credit (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "ProviderTransactionType" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceId" varchar(255) NULL,
    "MarketplaceCountryCode" varchar(255) NULL,
    "SellerId" varchar(255) NULL,
    "SellerStoreName" varchar(255) NULL,
    "ProviderId" varchar(255) NULL,
    "ProviderStoreName" varchar(255) NULL,
    "TransactionAmount" jsonb NULL,
    "TransactionCreationDate" timestamp(0) NULL,
    CONSTRAINT service_provider_credit_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-service_provider_credit-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-service_provider_credit-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-service_provider_credit-event_period_id" ON finance_00000_00000.service_provider_credit USING btree (event_period_id);
CREATE INDEX "idx-service_provider_credit-financial_event_group_id" ON finance_00000_00000.service_provider_credit USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.shipment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "FinancialEventGroupId" varchar(255) NULL DEFAULT NULL::character varying,
    "AmazonOrderId" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceName" varchar(255) NULL,
    "OrderChargeList" jsonb NULL,
    "OrderChargeAdjustmentList" jsonb NULL,
    "ShipmentFeeList" jsonb NULL,
    "ShipmentFeeAdjustmentList" jsonb NULL,
    "OrderFeeList" jsonb NULL,
    "OrderFeeAdjustmentList" jsonb NULL,
    "DirectPaymentList" jsonb NULL,
    "PostedDate" timestamp(0) NULL,
    "ShipmentItemList" jsonb NULL,
    "ShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT shipment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-shipment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "fk-shipment-financial_event_group_id" FOREIGN KEY ("FinancialEventGroupId") REFERENCES finance_00000_00000.event_group(id) ON DELETE SET NULL ON UPDATE SET NULL
);
CREATE INDEX "idx-shipment-event_period_id" ON finance_00000_00000.shipment USING btree (event_period_id);
CREATE INDEX "idx-shipment-financial_event_group_id" ON finance_00000_00000.shipment USING btree ("FinancialEventGroupId");

CREATE TABLE finance_00000_00000.shipment_settle (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "FinancialEventGroupId" varchar(255) NULL,
    "AmazonOrderId" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "MarketplaceName" varchar(255) NULL,
    "OrderChargeList" jsonb NULL,
    "OrderChargeAdjustmentList" jsonb NULL,
    "ShipmentFeeList" jsonb NULL,
    "ShipmentFeeAdjustmentList" jsonb NULL,
    "OrderFeeList" jsonb NULL,
    "OrderFeeAdjustmentList" jsonb NULL,
    "DirectPaymentList" jsonb NULL,
    "ShipmentItemList" jsonb NULL,
    "ShipmentItemAdjustmentList" jsonb NULL,
    CONSTRAINT shipment_settle_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-shipment_settle-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.tax_withholding (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "BaseAmount" jsonb NULL,
    "WithheldAmount" jsonb NULL,
    "FeeList" jsonb NULL,
    "TaxWithholdingPeriod" jsonb NULL,
    CONSTRAINT tax_withholding_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-tax_withholding-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.tds_reimbursement (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "TDSOrderId" varchar(255) NULL,
    "ReimbursedAmount" jsonb NULL,
    CONSTRAINT tds_reimbursement_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-tds_reimbursement-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.trial_shipment (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "AmazonOrderId" varchar(255) NULL,
    "FinancialEventGroupId" varchar(255) NULL,
    "SKU" varchar(255) NULL,
    "FeeList" jsonb NULL,
    CONSTRAINT trial_shipment_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-trial_shipment-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE finance_00000_00000.value_added_service_charge (
    id serial4 NOT NULL,
    event_period_id int4 NOT NULL,
    "PostedDate" timestamp(0) NULL,
    "TransactionType" varchar(255) NULL,
    "Description" text NULL,
    "TransactionAmount" jsonb NULL,
    CONSTRAINT value_added_service_charge_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-value_added_service_charge-event_period_id" FOREIGN KEY (event_period_id) REFERENCES finance_00000_00000.event_period(id) ON DELETE CASCADE ON UPDATE CASCADE
);