<?php

namespace console\migrations\finance;

use yii\db\Migration;

/**
 * Class m210415_074450_create_base_finance_tables
 */
class m210415_074450_create_base_finance_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = file_get_contents(__DIR__ . '/init_finance.sql');
        $sql = str_replace('finance_00000_00000', \Yii::$app->dbManager->getSchemaName('finance'), $sql);
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
    }
}
