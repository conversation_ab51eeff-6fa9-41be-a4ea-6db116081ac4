<?php

namespace console\migrations\common;

use common\components\salesCategoryMapper\SalesCategoryMapper;
use common\models\FinanceEventCategory;
use yii\db\Migration;

/**
 * Class m210906_084957_categories_mapping
 */
class m210906_084957_categories_mapping extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable(
            'sales_category',
            [
                'id' => $this->string()->unique(),
                'name' => $this->string(250)->notNull(),
                'is_visible' => $this->boolean()->defaultValue('f')
            ]
        );
        $this->addColumn('finance_event_category', 'sales_category_id', $this->string());
        $this->addForeignKey(
            'fk-finance_event_category-sales_category_id',
            'finance_event_category',
            'sales_category_id',
            'sales_category',
            'id',
            'SET NULL'
        );

        $this->db->createCommand()->batchInsert('sales_category', ['id', 'name', 'is_visible'], [
            ['promo_sales', 'Promo Sales', 1],
            ['organic_sales', 'Organic Sales', 1],
            ['promotions', 'Promotions', 1],
            ['cost_of_goods', 'Cost of Goods', 1],
            ['amazon_fees', 'Amazon Fees', 1],
            ['taxes', 'Taxes', 1],
            ['ppc_costs', 'PPC Costs', 1],
            ['operation_expenses', 'Operating Expenses', 1],
            ['shipping_cost', 'Shipping Cost', 1],
            ['miscellaneous_cost', 'Miscellaneous Cost', 1],
            ['ignored', 'Ignored', 0],
            ['undefined', 'Undefined', 0],
        ])->execute();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-finance_event_category-sales_category_id', 'finance_event_category');
        $this->dropColumn('finance_event_category', 'sales_category_id');
        $this->dropTable('sales_category');
    }
}
