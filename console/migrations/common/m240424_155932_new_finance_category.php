<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m230118_095545_add_is_data_migrated_to_seller_table
 */
class m240424_155932_new_finance_category extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->execute("INSERT INTO finance_event_category (id, path, sales_category_id) VALUES 
            (
                (SELECT max(id) FROM finance_event_category) + 1,
                'Custom.ShippingTaxes.transactionValue_MINUS',
                'undefined_1'
            ), 
            (
                (SELECT max(id) FROM finance_event_category) + 2,
                'Custom.ShippingTaxes.transactionValue_PLUS',
                'undefined'
            ),
            (
                (SELECT max(id) FROM finance_event_category) + 3,
                'Custom.GiftWrapTaxes.transactionValue_MINUS',
                'undefined_1'
            ), 
            (
                (SELECT max(id) FROM finance_event_category) + 4,
                'Custom.GiftWrapTaxes.transactionValue_PLUS',
                'undefined'
            ) 
            ON CONFLICT DO NOTHING
        ");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('finance_event_category', [
            'path' => ['Custom.ProductTaxes.transactionValue_MINUS','Custom.ProductTaxes.transactionValue_PLUS'],
        ]);
    }
}
