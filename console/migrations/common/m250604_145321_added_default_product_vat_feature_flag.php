<?php

namespace console\migrations\common;

use common\components\featureFlag\FeatureFlagService;
use yii\db\Migration;

/**
 * Class m250604_145321_added_default_product_vat_feature_flag
 */
class m250604_145321_added_default_product_vat_feature_flag extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->insert('feature_flag', [
            'name' => FeatureFlagService::FLAG_DEFAULT_VAT_FOR_PRODUCTS,
            'description' => 'BAS-2319 | Set default VAT for all products in case when VAT is null',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
    }
}
