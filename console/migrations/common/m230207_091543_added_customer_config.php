<?php

namespace console\migrations\common;

use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m230207_091543_added_customer_config
 */
class m230207_091543_added_customer_config extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('customer_config', [
            'id' => $this->primaryKey(),
            'customer_id' => $this->integer()->notNull(),
            'parameter' => $this->string()->notNull(),
            'value' => $this->string()->notNull(),
            'created_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull(),
            'updated_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull()
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('customer_config');
    }
}
