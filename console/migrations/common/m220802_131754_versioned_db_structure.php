<?php

namespace console\migrations\common;

use common\models\DbStructure;
use common\models\DbStructureTable;
use common\models\Seller;
use yii\db\Expression;
use yii\db\Migration;
use yii\db\Query;

/**
 * Class m220802_131754_versioned_db_structure
 */
class m220802_131754_versioned_db_structure extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable(
            'db_structure',
            [
                'id' => $this->primaryKey(),
                'customer_id' => $this->integer()->notNull(),
                'type' => $this->string()->notNull()->defaultValue('blue'), // blue|green
                'description' => $this->text(),
                'created_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
                'updated_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
            ]
        );
        $this->createIndex(
            'idx-db_structure-customer_id-type',
            'db_structure',
            [
                'customer_id',
                'type'
            ],
            true
        );

        $this->createTable(
            'db_structure_table',
            [
                'id' => $this->primaryKey(),
                'db_structure_id' => $this->integer()->notNull(),
                'database_type' => $this->string()->notNull(), // postgress|clickhouse
                'table_name' => $this->string()->notNull(),
                'version' => $this->integer()->notNull()->defaultValue(0),
                'created_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
                'updated_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
            ]
        );
        $this->addForeignKey(
            'fk-db_structure_table-db_structure_id',
            'db_structure_table',
            'db_structure_id',
            'db_structure',
            'id',
            'CASCADE'
        );
        $this->createIndex(
            'idx-db_structure_table-database_type-table_name',
            'db_structure_table',
            [
                'database_type',
                'table_name'
            ]
        );
    }

    public function safeDown()
    {
        $this->dropTable('db_structure_table');
        $this->dropTable('db_structure');
    }
}
