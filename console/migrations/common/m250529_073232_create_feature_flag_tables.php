<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250529_073232_create_feature_flag_tables
 */
class m250529_073232_create_feature_flag_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('feature_flag', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string()->unique()->notNull(),
            'description' => $this->text()->null(),
            'initial_command' => $this->text()->null(),
            'created_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);
        $this->createTable('feature_flag_target', [
            'id' => $this->bigPrimaryKey(),
            'feature_id' => $this->integer()->notNull(),
            'customer_id' => $this->integer()->null(),
            'initial_params' => $this->string()->null(),
            'initial_status' => $this->string()->null(),
            'log' => $this->text()->null(),
            'is_enabled' => $this->boolean()->notNull()->defaultValue(false),
            'created_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-feature_flag_target-feature_id',
            'feature_flag_target',
            'feature_id',
            'feature_flag',
            'id',
            'CASCADE'
        );
        $this->createIndex('idx-feature_flag_target-feature_id', 'feature_flag_target', 'feature_id');
        $this->createIndex('idx-feature_flag_target-customer_id', 'feature_flag_target', 'customer_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('feature_flag');
        $this->dropTable('feature_flag_target');
    }
}
