<?php

namespace console\migrations\common;

use yii\db\Migration;

class m240913_125312_customer extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('customer', [
            'id' => $this->primaryKey(),
            'is_demo' => $this->boolean()->notNull()->defaultValue(false),
            'is_vcs_enabled' => $this->boolean(),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);
        $this->addColumn('seller', 'eu_amazon_fees_vat', $this->decimal(5,2));

        $this->execute("
            INSERT INTO customer (id, is_demo, is_vcs_enabled, created_at)
            SELECT s.* 
            FROM (
                SELECT 
                    customer_id, 
                    bool_or(is_demo) as is_demo, 
                    bool_or(is_vcs_enabled) as is_vcs_enabled,
                    min(created_at) as created_at
                FROM seller
                GROUP BY customer_id
            ) s
        ");

        // Remove configs without customers to prevent foreign key constraint violation
        $this->execute("
            DELETE FROM customer_config
            WHERE customer_id NOT IN (SELECT DISTINCT customer_id FROM seller)
        ");

        $this->addForeignKey(
            'fk_seller-customer_id',
            'seller',
            'customer_id',
            'customer',
            'id',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk_customer_config-customer_id',
            'customer_config',
            'customer_id',
            'customer',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_seller-customer_id', 'seller');
        $this->dropForeignKey('fk_customer_config-customer_id', 'customer_config');
        $this->dropColumn('seller', 'eu_amazon_fees_vat');
        $this->dropTable('customer');
    }
}
