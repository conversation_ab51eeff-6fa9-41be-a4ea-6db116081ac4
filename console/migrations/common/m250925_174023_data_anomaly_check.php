<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250924_174023_data_anomaly_check
 */
class m250925_174023_data_anomaly_check extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('public.data_anomaly_check', [
            'id' => $this->primaryKey(),
            'case_id' => $this->string()->notNull(),
            'count_parts' => $this->integer()->notNull()->defaultValue(1),
            'count_processed_parts' => $this->integer()->notNull()->defaultValue(0),
            'status' => $this->string()->notNull()->defaultValue('created'),
            'found_on_customer_ids' => $this->json(),
            'fixed_automatically_on_customer_ids' => $this->json(),
            'log' => $this->text(),
            'started_at' => $this->dateTime(),
            'finished_at' => $this->dateTime(),
            'created_at' => $this->dateTime()->notNull()->defaultValue(new \yii\db\Expression('CURRENT_TIMESTAMP')),
            'updated_at' => $this->dateTime()->notNull()->defaultValue(new \yii\db\Expression('CURRENT_TIMESTAMP')),
        ]);

        $this->createTable('public.data_anomaly_check_part', [
            'id' => $this->primaryKey(),
            'data_anomaly_check_id' => $this->integer()->notNull(),
            'customer_id_from' => $this->integer(),
            'customer_id_to' => $this->integer(),
            'status' => $this->string()->notNull()->defaultValue('created'),
            'found_on_customer_ids' => $this->json(),
            'fixed_automatically_on_customer_ids' => $this->json(),
            'log' => $this->text(),
            'started_at' => $this->dateTime(),
            'finished_at' => $this->dateTime(),
            'created_at' => $this->dateTime()->notNull()->defaultValue(new \yii\db\Expression('CURRENT_TIMESTAMP')),
            'updated_at' => $this->dateTime()->notNull()->defaultValue(new \yii\db\Expression('CURRENT_TIMESTAMP')),
        ]);

        $this->addForeignKey(
            'fk-data_anomaly_check_part-data_anomaly_check_id',
            'data_anomaly_check_part',
            'data_anomaly_check_id',
            'data_anomaly_check',
            'id',
            'CASCADE'
        );
    }
}
