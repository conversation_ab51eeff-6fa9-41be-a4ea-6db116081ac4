<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m211115_095545_add_non_amazon_marketplace
 */
class m211115_095545_add_non_amazon_marketplace extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->insert('amazon_marketplace', [
            'amazon_id' => '',
            'amazon_zone_id' => '',
            'amazon_mws_endpoint' => '',
            'title' => 'Non-Amazon',
            'country_code' => '',
            'currency_code' => '',
            'is_active' => true,
            'ordering' => 20,
            'sales_channel' => 'non-amazon',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('amazon_marketplace', ['amazon_id' => '', 'title' => 'Non-Amazon', 'sales_channel' => 'non-amazon',]);
    }
}
