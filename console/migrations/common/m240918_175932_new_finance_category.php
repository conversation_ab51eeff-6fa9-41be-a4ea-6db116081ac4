<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m240918_175932_new_finance_category
 */
class m240918_175932_new_finance_category extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->execute("INSERT INTO finance_event_category (id, path, sales_category_id) VALUES 
            (
                (SELECT max(id) FROM finance_event_category) + 4,
                'Custom.AmazonFees.EU_VAT.transactionValue_PLUS',
                'undefined'
            ) 
            ON CONFLICT DO NOTHING
        ");
    }
}
