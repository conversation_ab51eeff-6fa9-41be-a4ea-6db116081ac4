<?php

namespace console\migrations\common;

use common\models\AmazonMarketplace;
use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m211103_095545_added_amazon_marketplace_table
 */
class m211117_123451_changed_primary_key_of_marketplace extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->dropTable(AmazonMarketplace::tableName());
        $this->createTable(AmazonMarketplace::tableName(), [
            'id' =>  $this->string(20),
            'amazon_zone_id' => $this->string(20)->notNull(),
            'amazon_mws_endpoint' => $this->string(50)->notNull(),
            'title' => $this->string(100)->notNull(),
            'country_code' => $this->string(2)->notNull(),
            'currency_code' => $this->string(3)->notNull(),
            'is_active' => $this->boolean()->notNull()->defaultValue('t'),
            'ordering' => $this->integer()->notNull()->defaultValue(0),
            'sales_channel' => $this->string(100)->notNull(),
            'created_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
            'updated_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
        ]);
        $this->addPrimaryKey('pk-id', AmazonMarketplace::tableName(), ['id']);
        $this->insert(AmazonMarketplace::tableName(), [
            'id' => 'Non-Amazon',
            'amazon_zone_id' => '',
            'amazon_mws_endpoint' => '',
            'title' => 'Non-Amazon',
            'country_code' => '',
            'currency_code' => '',
            'is_active' => true,
            'ordering' => 20,
            'sales_channel' => 'non-amazon',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable(AmazonMarketplace::tableName());
        $this->createTable(AmazonMarketplace::tableName(), [
            'id' => $this->primaryKey(),
            'amazon_id' => $this->string(20)->notNull(),
            'amazon_zone_id' => $this->string(20)->notNull(),
            'amazon_mws_endpoint' => $this->string(50)->notNull(),
            'title' => $this->string(100)->notNull(),
            'country_code' => $this->string(2)->notNull(),
            'currency_code' => $this->string(3)->notNull(),
            'is_active' => $this->boolean()->notNull()->defaultValue('t'),
            'ordering' => $this->integer()->notNull()->defaultValue(0),
            'sales_channel' => $this->string(100)->notNull(),
            'created_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
            'updated_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
        ]);
        $this->createIndex('uk-amazon_id', AmazonMarketplace::tableName(), ['amazon_id'], true);
        $this->insert('amazon_marketplace', [
            'amazon_id' => '',
            'amazon_zone_id' => '',
            'amazon_mws_endpoint' => '',
            'title' => 'Non-Amazon',
            'country_code' => '',
            'currency_code' => '',
            'is_active' => true,
            'ordering' => 20,
            'sales_channel' => 'non-amazon',
        ]);
    }
}
