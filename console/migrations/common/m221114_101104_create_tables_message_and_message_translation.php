<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m221114_101104_create_tables_message_and_message_translation.
 */
class m221114_101104_create_tables_message_and_message_translation extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('message', [
            'id' => $this->primaryKey(),
            'category' => $this->string(32)->null(),
            // original MySQL field `text collate utf8_bin null`
            'message' => $this->text()->null(),
            'is_processed' => $this->tinyInteger(1)->defaultValue(0)->null(),
        ]);

        $this->createIndex(
            'idx_message_is_processed',
            'message',
            'is_processed',
            false
        );

        $this->createTable('message_translation', [
            'id' => $this->integer()->defaultValue(0)->notNull(),
            'language' => $this->string(2)->defaultValue('')->notNull(),
            'translation' => $this->text()->null(),
            'translated' => $this->tinyInteger(1)->defaultValue(0)->notNull(),
            'is_auto_translated' => $this->tinyInteger(1)->unsigned()->defaultValue(0)->notNull(),
            'comments' => $this->text()->null(),
            // original MySQL field `ENUM ('PENDING', 'TRANSLATED', 'APPROVED') default 'PENDING' null`
            'status' => $this->string(32)->notNull()->defaultValue('PENDING'),
            'admin_bar_ignore_till' => $this->dateTime()->null(),
            'translated_by_user_id' => $this->integer()->null(),
            'approved_by_user_id' => $this->integer()->null(),
            'PRIMARY KEY (id, language)',
        ]);

        $this->addForeignKey(
            'fk-message_translation-id',
            'message_translation',
            'id',
            'message',
            'id',
            'CASCADE',
            'CASCADE',
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-message_translation-id', 'message_translation');
        $this->dropTable('message_translation');

        $this->dropIndex('idx_message_is_processed', 'message');
        $this->dropTable('message');
    }
}
