<?php

namespace console\migrations\common;

use common\components\salesCategoryMapper\SalesCategoryBuilder;
use common\components\salesCategoryMapper\SalesCategoryMapper;
use yii\db\Migration;

class m220412_144532_added_mapping_rule_order extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('sales_category', 'mapping_rule_order', $this->integer()->defaultValue(1));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('sales_category', 'mapping_rule_order');
    }
}
