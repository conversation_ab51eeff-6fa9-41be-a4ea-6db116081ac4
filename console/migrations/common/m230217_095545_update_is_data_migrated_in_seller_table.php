<?php

namespace console\migrations\common;

use yii\db\Migration;

class m230217_095545_update_is_data_migrated_in_seller_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->update('seller', ['is_data_migrated' => false]);
        $this->alterColumn('seller', 'is_data_migrated', $this->boolean()->notNull()->defaultValue(true),);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('seller', 'is_data_migrated', $this->boolean()->notNull()->defaultValue(false),);
    }
}
