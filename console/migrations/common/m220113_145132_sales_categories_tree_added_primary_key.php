<?php

namespace console\migrations\common;

use common\components\salesCategoryMapper\SalesCategoryBuilder;
use common\components\salesCategoryMapper\SalesCategoryMapper;
use yii\db\Migration;

/**
 * Class m220113_145132_sales_categories_tree_added_primary_key
 */
class m220113_145132_sales_categories_tree_added_primary_key extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addPrimaryKey('pk-sales_category', 'sales_category', 'id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('pk-sales_category', 'sales_category');
    }
}
