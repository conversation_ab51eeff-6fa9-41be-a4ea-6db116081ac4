<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m210929_114804_currency_rate_history_table
 */
class m210929_114804_currency_rate_history_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('currency_rate_history', [
            'id' => $this->primaryKey(),
            'currency_id' => $this->string(3),
            'value' => $this->decimal(16, 8),
            'date' => $this->date()
        ]);
        $this->createIndex('uk-currency_id-date', 'currency_rate_history', [
            'currency_id', 'date'
        ], true);
    }

    public function safeDown()
    {
        $this->dropIndex('uk-currency_id-date', 'currency_rate_history');
        $this->dropTable('currency_rate_history');
    }
}
