<?php

namespace console\migrations\common;

use common\components\salesCategoryMapper\SalesCategoryMapper;
use common\models\FinanceEventCategory;
use yii\db\Migration;

class m240720_094957_refund_description_mapping extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable(
            'refund_reason',
            [
                'id' => $this->string()->unique(),
                'name' => $this->string(250)->notNull(),
                'description' => $this->text()->notNull()
            ]
        );

        $this->db->createCommand()->batchInsert('refund_reason', ['id', 'name', 'description'], [
            ['AMZ-PG-APP-TOO-LARGE', 'Apparel Size Too Large', 'The received apparel item is larger than expected and does not fit properly.'],
            ['APPAREL_TOO_LARGE', 'Apparel Size Too Large', 'The received apparel item is larger than expected and does not fit properly.'],
            ['CR-EXTRA_ITEM', 'Received Extra Item', 'An additional item was delivered that was not part of the original order.'],
            ['EXTRA_ITEM', 'Received Extra Item', 'An additional item was delivered that was not part of the original order.'],
            ['DID_NOT_LIKE_FABRIC', 'Fabric Not to Liking', 'The fabric of the received item is not as expected or is not preferred by the customer.'],
            ['CR-SWITCHEROO', 'Incorrect Item Delivered', 'The item received is different from what was originally ordered, possibly due to a mix-up.'],
            ['SWITCHEROO', 'Incorrect Item Delivered', 'The item received is different from what was originally ordered, possibly due to a mix-up.'],
            ['CR-NO_REASON_GIVEN', 'No Reason Provided', 'The customer did not specify a reason for the return.'],
            ['NO_REASON_GIVEN', 'No Reason Provided', 'The customer did not specify a reason for the return.'],
            ['CR-MISSED_ESTIMATED_DELIVERY', 'Missed Delivery Estimate', 'The item was not delivered within the estimated time frame provided during the purchase.'],
            ['MISSED_ESTIMATED_DELIVERY', 'Missed Delivery Estimate', 'The item was not delivered within the estimated time frame provided during the purchase.'],
            ['CR-DAMAGED_BY_FC', 'Damaged by Fulfillment Center', 'The item was damaged while being handled at the fulfillment center before shipping.'],
            ['DAMAGED_BY_FC', 'Damaged by Fulfillment Center', 'The item was damaged while being handled at the fulfillment center before shipping.'],
            ['AMZ-PG-BAD-DESC', 'Description Mismatch', 'The received item does not match the description provided on the website.'],
            ['NOT_AS_DESCRIBED', 'Description Mismatch', 'The received item does not match the description provided on the website.'],
            ['NEVER_ARRIVED', 'Item Never Arrived', 'The ordered item never reached the customer’s delivery address.'],
            ['AMZ-PG-MISORDERED', 'Incorrect Order Placed', 'The customer mistakenly ordered the wrong item.'],
            ['MISORDERED', 'Incorrect Order Placed', 'The customer mistakenly ordered the wrong item.'],
            ['CR-MISSING_PARTS', 'Missing Components', 'The received item is missing one or more parts that are essential for its use.'],
            ['CR-DAMAGED_BY_CARRIER', 'Damaged in Transit', 'The item was damaged during shipping by the carrier.'],
            ['DAMAGED_BY_CARRIER', 'Damaged in Transit', 'The item was damaged during shipping by the carrier.'],
            ['DID_NOT_LIKE_COLOR', 'Color Not Preferred', 'The color of the item is not as expected or is not liked by the customer.'],
            ['CR-QUALITY_UNACCEPTABLE', 'Unacceptable Quality', 'The quality of the item does not meet the customer’s standards or expectations.'],
            ['QUALITY_UNACCEPTABLE', 'Unacceptable Quality', 'The quality of the item does not meet the customer’s standards or expectations.'],
            ['AMZ-PG-APP-TOO-SMALL', 'Apparel Size Too Small', 'The received apparel item is smaller than expected and does not fit properly.'],
            ['APPAREL_TOO_SMALL', 'Apparel Size Too Small', 'The received apparel item is smaller than expected and does not fit properly.'],
            ['CR-ORDERED_WRONG_ITEM', 'Wrong Item Ordered', 'The customer ordered the wrong item, possibly by mistake.'],
            ['ORDERED_WRONG_ITEM', 'Wrong Item Ordered', 'The customer ordered the wrong item, possibly by mistake.'],
            ['CR-DEFECTIVE', 'Defective Product', 'The item is faulty or not functioning as it should.'],
            ['DEFECTIVE', 'Defective Product', 'The item is faulty or not functioning as it should.'],
            ['CR-UNWANTED_ITEM', 'Unwanted Purchase', 'The customer no longer wants the item.'],
            ['UNWANTED_ITEM', 'Unwanted Purchase', 'The customer no longer wants the item.'],
            ['CR-FOUND_BETTER_PRICE', 'Found a Better Price', 'The customer found a similar item at a lower price elsewhere.'],
            ['FOUND_BETTER_PRICE', 'Found a Better Price', 'The customer found a similar item at a lower price elsewhere.'],
            ['CR-UNAUTHORIZED_PURCHASE', 'Unauthorized Purchase', 'The purchase was made without the account holder’s authorization.'],
            ['UNAUTHORIZED_PURCHASE', 'Unauthorized Purchase', 'The purchase was made without the account holder’s authorization.'],
            ['CR-NOT_COMPATIBLE', 'Incompatible Product', 'The item is not compatible with the customer’s existing equipment or needs.'],
            ['NOT_COMPATIBLE', 'Incompatible Product', 'The item is not compatible with the customer’s existing equipment or needs.'],
            ['AMZ-PG-APP-STYLE', 'Unwanted Apparel Style', 'The style of the apparel item is not as expected or not liked by the customer.'],
            ['APPAREL_STYLE', 'Unwanted Apparel Style', 'The style of the apparel item is not as expected or not liked by the customer.'],
            ['EXCESSIVE_INSTALLATION', 'Excessive Installation', 'The item requires more installation effort than expected.'],
            ['JEWELRY_BAD_CLASP', 'Jewelry with Bad Clasp', 'The clasp of the jewelry item is faulty or does not function properly.'],
            ['JEWELRY_BATTERY', 'Jewelry Battery Issue', 'The battery in the jewelry item is not working or is dead.'],
            ['JEWELRY_LOOSE_STONE', 'Jewelry with Loose Stone', 'The jewelry item has a loose stone.'],
            ['JEWELRY_NO_CERT', 'Jewelry Missing Certification', 'The jewelry item is missing its promised certification.'],
            ['JEWELRY_NO_DOCS', 'Jewelry Missing Documentation', 'The jewelry item is missing its necessary documentation.'],
            ['JEWELRY_TARNISHED', 'Tarnished Jewelry', 'The jewelry item is tarnished or in unacceptable condition.'],
            ['JEWELRY_TOO_LARGE', 'Jewelry Size Too Large', 'The received jewelry item is larger than expected.'],
            ['JEWELRY_TOO_SMALL', 'Jewelry Size Too Small', 'The received jewelry item is smaller than expected.'],
            ['PART_NOT_COMPATIBLE', 'Part Not Compatible', 'The part is not compatible with the existing system or product.'],
            ['PRODUCT_NOT_ITALIAN', 'Product Not in Italian', 'The product or manual is not in Italian as expected.'],
            ['PRODUCT_NOT_SPANISH', 'Product Not in Spanish', 'The product or manual is not in Spanish as expected.'],
            ['UND-UNKNOWN', 'Undeliverable - Unknown Reason', 'The item could not be delivered for an unspecified reason.'],
            ['UNDELIVERABLE_CARRIER_MISS_SORTED', 'Carrier Mis-sorted Package', 'The package was mis-sorted by the carrier, causing delivery issues.'],
            ['UNDELIVERABLE_FAILED_DELIVERY_ATTEMPTS', 'Failed Delivery Attempts', 'The item could not be delivered after multiple attempts.'],
            ['UNDELIVERABLE_INSUFFICIENT_ADDRESS', 'Insufficient Address', 'The item could not be delivered due to an incomplete or incorrect address.'],
            ['UNDELIVERABLE_MISSING_LABEL', 'Missing Shipping Label', 'The item could not be delivered because the shipping label was missing.'],
            ['UNDELIVERABLE_REFUSED', 'Delivery Refused', 'The delivery of the item was refused by the recipient.'],
            ['UNDELIVERABLE_UNCLAIMED', 'Unclaimed Delivery', 'The item was not claimed by the recipient and returned.'],
            ['WRONG_SIZE', 'Wrong Size', 'The item received is of an incorrect size.']
        ])->execute();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('refund_reason');
    }
}
