<?php

namespace console\migrations\common;

use yii\db\Migration;

class m230518_134421_command_retries extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('command', 'retries', $this->integer()->defaultValue(0)->notNull());
        $this->addColumn('command', 'max_retries', $this->integer()->defaultValue(1)->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('command', 'retries');
        $this->dropColumn('command', 'max_retries');
    }
}
