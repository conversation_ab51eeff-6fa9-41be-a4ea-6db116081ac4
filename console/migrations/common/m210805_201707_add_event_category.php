<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m210805_201707_add_event_category
 */
class m210805_201707_add_event_category extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('finance_event_category', [
            'id' => $this->primaryKey(),
            'path' => $this->string(250)->notNull()
        ]);

        $this->createIndex(
            'idx-finance_event_category-path',
            'finance_event_category',
            'path',
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-finance_event_category-path', 'finance_event_category');
        $this->dropTable('finance_event_category');
    }
}
