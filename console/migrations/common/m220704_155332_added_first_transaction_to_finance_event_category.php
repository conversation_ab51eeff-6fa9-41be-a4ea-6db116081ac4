<?php

namespace console\migrations\common;

use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m220704_155332_added_first_transaction_to_finance_event_category
 */
class m220704_155332_added_first_transaction_to_finance_event_category extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('finance_event_category', 'created_at', $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')));
        $this->addColumn('finance_event_category', 'updated_at', $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')));
        $this->addColumn('finance_event_category', 'first_transaction', $this->json());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('finance_event_category', 'created_at');
        $this->dropColumn('finance_event_category', 'updated_at');
        $this->dropColumn('finance_event_category', 'first_transaction');
    }
}
