<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m310710_111422_data_completeness_factor
 */
class m241223_111234_data_completeness_factor extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('data_completeness_factor', [
            'id' => $this->string(),
            'title' => $this->string()->notNull(),
            'weight' => $this->integer()->notNull(),
            'sort_order' => $this->string()->notNull()->defaultValue(1),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')
        ]);
        $this->createIndex('uk-data_completeness_factor-id', 'data_completeness_factor', ['id'], true);

        $this->batchInsert('data_completeness_factor', [
            'id', 'title', 'weight', 'sort_order'
        ], [
            ['no_cost_of_goods', 'Cost of goods is not imported', 84, 0],
            ['no_vat', 'VAT is not imported', 84, 10],
            ['no_fbm_shipping_costs', 'FBM shipping costs are not imported', 84, 20],
            ['no_other_fees', 'Other fees are not imported', 84, 30],
            ['data_reassembly', 'Data reassembly', 84, 40],
            ['historical_data_upload', 'Historical data load', 84, 50],

            ['ppc_not_connected', 'Amazon Ads account is not connected', 50, 60],
            ['cog_not_synchronized', 'Manual costs import is not synchronized with Repricer', 50, 70],
            ['missing_products_in_repricer', 'Product costs are no longer present in Repricer', 50, 80],

            ['no_indirect_costs', 'Indirect costs are not imported', 17, 90],

            ['referral_fee_changes', 'Referral fee changes', 0, 100],
            ['fba_fulfillment_fee_changes', 'FBA fulfillment fee changes', 0, 110],
            ['adjustments_fee', 'Adjustments to fees', 0, 120],
        ]);

        \Yii::$app->db1->createCommand("
            IMPORT FOREIGN SCHEMA public
                LIMIT TO (data_completeness_factor)
                FROM SERVER db0_replication_tables
                INTO public;
        ")->execute();
        \Yii::$app->db->createCommand("SELECT 1");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('data_completeness_factor');
    }
}
