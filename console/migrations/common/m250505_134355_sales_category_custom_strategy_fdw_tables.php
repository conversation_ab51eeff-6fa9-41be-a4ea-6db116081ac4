<?php

namespace console\migrations\common;

use common\models\SalesCategory;
use yii\db\Migration;

/**
 * Class m240918_175932_new_finance_category
 */
class m250505_134355_sales_category_custom_strategy_fdw_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            \Yii::$app->db1->createCommand("
                ALTER FOREIGN TABLE public.finance_event_category ADD COLUMN sales_category_id_custom VARCHAR(255) DEFAULT NULL"
            )->execute();
            \Yii::$app->db1->createCommand("
                ALTER FOREIGN TABLE public.sales_category ADD COLUMN tags VARCHAR[] DEFAULT NULL"
            )->execute();
            \Yii::$app->db1->createCommand("
                ALTER FOREIGN TABLE public.sales_category ADD COLUMN type VARCHAR(255)"
            )->execute();
            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        \Yii::$app->db->createCommand("SELECT 1");
    }
}
