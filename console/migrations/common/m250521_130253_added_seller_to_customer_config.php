<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250521_130253_added_seller_to_customer_config
 */
class m250521_130253_added_seller_to_customer_config extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('public.customer_config', 'seller_id', $this->string());

        $this->createIndex(
            'idx-customer_config-customer_id-seller_id-parameter',
            'public.customer_config',
            [
                'customer_id',
                'seller_id',
                'parameter'
            ],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-customer_config-customer_id-seller_id-parameter', 'public.customer_config');
        $this->dropColumn('public.customer_config', 'seller_id');
    }
}
