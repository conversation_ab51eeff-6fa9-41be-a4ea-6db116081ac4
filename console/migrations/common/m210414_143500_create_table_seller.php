<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m210414_143500_create_table_seller
 */
class m210414_143500_create_table_seller extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('seller', [
            'id' => $this->string(50)->unique()->notNull(),
            'is_active' => $this->boolean()->notNull()->defaultValue(false),
            'is_analytic_active' => $this->boolean()->notNull()->defaultValue(false),
            'is_init_periods_created' => $this->boolean()->notNull()->defaultValue(false),
            'region' => $this->string(32)->notNull(),
            'customer_id' => $this->integer()->notNull(),
            'created_at' => $this->dateTime()->notNull()
        ]);
        $this->createIndex(
            'idx-seller_customer-id',
            'seller',
            'customer_id'
        );

        $this->addPrimaryKey('seller_pkey', 'seller', ['id']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropPrimaryKey('seller_pkey', 'seller');
        $this->dropIndex('idx-seller_customer-id','seller');
        $this->dropTable('seller');
    }
}
