<?php

namespace console\migrations\common;

use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m221211_191303_data_import_export_recurrent_tables
 */
class m221211_191303_data_import_export_recurrent_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('data_export_recurrent', [
            'id' => $this->primaryKey(),
            'customer_id' => $this->integer()->notNull(),
            'handler_name' => $this->string(100)->notNull(),
            'output_file_format' => $this->string()->notNull()->defaultValue('csv'),
            'is_enabled' => $this->boolean()->notNull()->defaultValue(true),
            'cron_expr' => $this->string(100)->notNull(),
            'auto_export_url' => $this->string(500),
            'invoked_at' => $this->dateTime(),
            'executed_at' => $this->dateTime(),
            'created_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull(),
            'updated_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull()
        ]);

        $this->createTable('data_import_recurrent', [
            'id' => $this->primaryKey(),
            'customer_id' => $this->integer()->notNull(),
            'handler_name' => $this->string(100)->notNull(),
            'is_enabled' => $this->boolean()->notNull()->defaultValue(true),
            'url' => $this->string(500)->notNull(),
            'auth_login' => $this->string(200),
            'auth_password' => $this->string(200),
            'cron_expr' => $this->string(100)->notNull(),
            'auto_export_url' => $this->string(500),
            'invoked_at' => $this->dateTime(),
            'executed_at' => $this->dateTime(),
            'created_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull(),
            'updated_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull()
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('data_import_recurrent');
        $this->dropTable('data_export_recurrent');
    }
}
