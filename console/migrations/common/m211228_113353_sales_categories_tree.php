<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m211228_113353_sales_categories_tree
 */
class m211228_113353_sales_categories_tree extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('sales_category', 'parent_id', $this->string(250));
        $this->addColumn('sales_category', 'depth', $this->integer()->defaultValue(0));
        $this->addColumn('sales_category', 'path', $this->string());
        $this->addForeignKey(
            'fk-sales_category-parent_id',
            'sales_category',
            'parent_id',
            'sales_category',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-sales_category-parent_id', 'sales_category');
        $this->dropColumn('sales_category', 'parent_id');
        $this->dropColumn('sales_category', 'depth');
        $this->dropColumn('sales_category', 'path');
    }
}
