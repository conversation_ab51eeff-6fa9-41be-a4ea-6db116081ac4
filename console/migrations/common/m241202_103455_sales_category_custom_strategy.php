<?php

namespace console\migrations\common;

use common\models\SalesCategory;
use yii\db\Migration;

/**
 * Class m240918_175932_new_finance_category
 */
class m241202_103455_sales_category_custom_strategy extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('finance_event_category', 'sales_category_id_custom', $this->string());
        $this->addForeignKey(
            'fk-finance_event_category-sales_category_id_custom',
            'finance_event_category',
            'sales_category_id_custom',
            'sales_category',
            'id',
            'SET NULL'
        );
        $this->alterColumn('sales_category', 'mapping_rule', $this->text());

        $this->addColumn('sales_category', 'tags', 'VARCHAR[]');
        $this->addColumn('sales_category', 'type', $this->string()->defaultValue('revenue_expenses'));

        $this->createIndex(
            'idx-sales_category-type',
            'sales_category',
            'type'
        );
    }
}
