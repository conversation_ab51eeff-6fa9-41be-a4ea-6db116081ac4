<?php

namespace console\migrations\common;

use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m210331_101104_create_table_user_token.
 */
class m210331_101104_create_table_user_token extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('user_token', [
            'id' => $this->primaryKey(),
            'access_token' => $this->string(1023)->notNull()->unique(),
            'user_id' => $this->integer()->notNull(),
            'customer_id' => $this->integer()->null(),
            'created_at' => $this->dateTime()->notNull()
        ]);
        $this->createTable(
            'customer_process',
            [
                'id' => $this->primaryKey(),
                'parent_process_id' => $this->integer(),
                'customer_id' => $this->integer()->notNull(),
                'name' => $this->string()->notNull(),
                'count_all' => $this->integer()->defaultValue(1)->notNull(),
                'count_success' => $this->integer()->defaultValue(0)->notNull(),
                'count_failed' => $this->integer()->defaultValue(0)->notNull(),
                'count_attempts' => $this->integer()->defaultValue(0)->notNull(),
                'max_attempts' => $this->integer()->defaultValue(1)->notNull(),
                'status' => $this->string()->defaultValue('created')->notNull(),
                'log' => $this->text(),
                'success_rate_percents' => $this->integer()->notNull()->defaultValue(100),
                'next_attempt_delay_s' => $this->integer()->notNull()->defaultValue(60),
                'next_attempt_after' => $this->dateTime(),
                'started_at' => $this->dateTime(),
                'finished_at' => $this->dateTime(),
                'created_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull(),
                'updated_at' => $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP'))->notNull(),
            ]
        );

        $this->addForeignKey(
            'fk-customer_process_parent_process_id',
            'customer_process',
            'parent_process_id',
            'customer_process',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('user_token');
    }
}
