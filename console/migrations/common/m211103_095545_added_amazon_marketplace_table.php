<?php

namespace console\migrations\common;

use common\models\AmazonMarketplace;
use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m211103_095545_added_amazon_marketplace_table
 */
class m211103_095545_added_amazon_marketplace_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable(AmazonMarketplace::tableName(), [
            'id' => $this->primaryKey(),
            'amazon_id' => $this->string(20)->notNull(),
            'amazon_zone_id' => $this->string(20)->notNull(),
            'amazon_mws_endpoint' => $this->string(50)->notNull(),
            'title' => $this->string(100)->notNull(),
            'country_code' => $this->string(2)->notNull(),
            'currency_code' => $this->string(3)->notNull(),
            'is_active' => $this->boolean()->notNull()->defaultValue('t'),
            'ordering' => $this->integer()->notNull()->defaultValue(0),
            'sales_channel' => $this->string(100)->notNull(),
            'created_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
            'updated_at' => $this->dateTime()->notNull()->defaultValue(new Expression('CURRENT_TIMESTAMP')),
        ]);
        $this->createIndex('uk-amazon_id', AmazonMarketplace::tableName(), ['amazon_id'], true);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('uk-amazon_id', AmazonMarketplace::tableName());
        $this->dropTable('amazon_marketplace');
    }
}
