<?php

namespace console\migrations\common;

use common\components\salesCategoryMapper\SalesCategoryBuilder;
use common\components\salesCategoryMapper\SalesCategoryMapper;
use yii\db\Migration;

/**
 * Class m220421_132939_added_sort_order_to_sales_categories
 */
class m220421_132939_added_sort_order_to_sales_categories extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('sales_category', 'sort_order', $this->integer()->after('depth')->defaultValue(0));

        $salesCategoryBuilder = new SalesCategoryBuilder();
        $salesCategoryBuilder->rebuild();

        $salesCategoriesMapper = new SalesCategoryMapper();
        $salesCategoriesMapper->remap();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('sales_category', 'sort_order');
    }
}
