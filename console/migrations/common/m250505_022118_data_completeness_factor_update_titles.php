<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250505_022118_data_completeness_factor_update_titles
 */
class m250505_022118_data_completeness_factor_update_titles extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->update('data_completeness_factor', ['title' => 'Cost of goods is not found'], ['id' => 'no_cost_of_goods']);
        $this->update('data_completeness_factor', ['title' => 'VAT is not found'], ['id' => 'no_vat']);
        $this->update('data_completeness_factor', ['title' => 'FBM shipping costs are not found'], ['id' => 'no_fbm_shipping_costs']);
        $this->update('data_completeness_factor', ['title' => 'Indirect costs are not found'], ['id' => 'no_indirect_costs']);
        $this->update('data_completeness_factor', ['title' => 'Other fees are not found'], ['id' => 'no_other_fees']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
    }
}
