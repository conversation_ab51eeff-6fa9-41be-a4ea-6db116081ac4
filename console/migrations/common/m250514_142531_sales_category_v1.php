<?php

namespace console\migrations\common;

use common\models\SalesCategory;
use yii\db\Migration;

/**
 * Class m240918_175932_new_finance_category
 */
class m250514_142531_sales_category_v1 extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $newTableName = SalesCategory::tableName();
        $oldTableName = str_replace('_v1', '', $newTableName);

        $this->execute("CREATE TABLE $newTableName (like $oldTableName including all)");
    }
}
