<?php

namespace console\migrations\common;

use common\components\featureFlag\FeatureFlagService;
use yii\db\Migration;

/**
 * Class m250529_073232_create_feature_flag_tables
 */
class m250606_124321_feature_flag_for_vat_calculation_based_on_vcs extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->insert('public.feature_flag', [
            'name' => FeatureFlagService::FLAG_VAT_CALCULATION_BASED_ON_VCS_REPORT,
            'description' => 'BAS-2486 | apply reverse VAT calculation logic on B2C orders too (previously we used to add it only on B2B orders - that was a mistake as we realized)',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('feature_flag', ['name' => FeatureFlagService::FLAG_VAT_CALCULATION_BASED_ON_VCS_REPORT]);
    }
}
