<?php

namespace console\migrations\common;

use yii\db\Migration;
use common\models\RefundReason;
use api\modules\v1\enums\RefundReasons;

class m250922_174009_alter_refund_reason_table_fill_color_column extends Migration
{
    public function safeUp()
    {
        $pendingColor = $this->generateColor();
        $used = [$pendingColor];

        $this->insert(RefundReason::tableName(), [
            'id' => RefundReasons::PENDING,
            'name' => 'Pending',
            'description' => 'Pending',
            'color' => $pendingColor
        ]);

        foreach (RefundReason::find()->all() as $reason) {
            do {
                $color = $this->generateColor();
            } while (in_array($color, $used));

            $used[] = $color;

            $this->update(RefundReason::tableName(), ['color' => $color], ['id' => $reason->id]);
        }
    }

    public function safeDown()
    {
        echo "m250922_174009_alter_refund_reason_table_fill_color_column cannot be reverted.\n";

        return false;
    }

    private function generateColor(): string
    {
        return '#' . substr(md5(rand(0, 10000)), 0, 6);
    }
}
