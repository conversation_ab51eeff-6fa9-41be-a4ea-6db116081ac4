<?php

namespace console\migrations\common;

use yii\db\Migration;

class m211213_143500_create_table_command extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('command', [
            'id' => $this->primaryKey(),
            'command' => $this->text()->notNull(),
            'status' => $this->string(32)->notNull(),
            'log' => $this->text()->null(),
            'created_at' => $this->dateTime()->notNull(),
            'processed_at' => $this->dateTime()->null(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('command');
    }
}
