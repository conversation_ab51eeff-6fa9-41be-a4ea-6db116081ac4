<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m211112_095545_add_column_is_init_periods_loaded_to_seller_table
 */
class m211112_095545_add_column_is_init_periods_loaded_to_seller_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('seller', 'is_init_periods_loaded', $this->boolean()->notNull()->defaultValue(false),);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('seller', 'is_init_periods_loaded');
    }
}
