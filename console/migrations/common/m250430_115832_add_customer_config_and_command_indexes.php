<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250430_115832_add_customer_config_and_command_indexes
 */
class m250430_115832_add_customer_config_and_command_indexes extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            'idx-customer_config-customer_id-parameter',
            'customer_config',
            [
                'customer_id',
                'parameter',
            ]
        );

        $this->createIndex('idx-command-status', 'command', 'status');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-customer_config-customer_id-parameter', 'customer_config');
        $this->dropIndex('idx-command-status', 'command');
    }
}
