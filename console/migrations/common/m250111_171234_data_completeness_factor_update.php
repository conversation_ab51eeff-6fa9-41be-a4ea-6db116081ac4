<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250111_171234_data_completeness_factor_update
 */
class m250111_171234_data_completeness_factor_update extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->update('data_completeness_factor', ['weight' => 0], ['id' => 'no_other_fees']);

        $this->delete('data_completeness_factor', ['id' => 'data_reassembly']);
        $this->delete('data_completeness_factor', ['id' => 'historical_data_upload']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
    }
}
