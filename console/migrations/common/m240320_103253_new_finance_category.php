<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m230118_095545_add_is_data_migrated_to_seller_table
 */
class m240320_103253_new_finance_category extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->execute("INSERT INTO finance_event_category (id, path, sales_category_id) VALUES 
            (
                (SELECT max(id) FROM finance_event_category) + 1,
                'Custom.ProductTaxes.transactionValue_MINUS',
                'product_taxes_2'
            ), 
            (
                (SELECT max(id) FROM finance_event_category) + 2,
                'Custom.ProductTaxes.transactionValue_PLUS',
                'product_taxes'
            ) 
            ON CONFLICT DO NOTHING
        ");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('finance_event_category', [
            'path' => ['Custom.ProductTaxes.transactionValue_MINUS','Custom.ProductTaxes.transactionValue_PLUS'],
        ]);
    }
}
