<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m221216_095545_add_columns_to_seller_table
 */
class m221216_095545_add_columns_to_seller_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('seller', 'is_order_init_periods_loaded', $this->boolean()->notNull()->defaultValue(false),);
        $this->addColumn('seller', 'is_order_init_periods_created', $this->boolean()->notNull()->defaultValue(false),);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('seller', 'is_order_init_periods_loaded');
        $this->dropColumn('seller', 'is_order_init_periods_created');
    }
}
