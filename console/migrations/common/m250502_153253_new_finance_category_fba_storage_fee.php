<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m250502_153253_new_finance_category_fba_storage_fee
 */
class m250502_153253_new_finance_category_fba_storage_fee extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->execute("INSERT INTO finance_event_category (id, path, sales_category_id) VALUES 
            (
                (SELECT max(id) FROM finance_event_category) + 1,
                'Custom.FBAStorageFee.transactionValue',
                'fba_storage_fee'
            ), 
            (
                (SELECT max(id) FROM finance_event_category) + 2,
                'Custom.FBALongTermStorageFee.transactionValue',
                'fba_long_term_storage_fee'
            ) 
            ON CONFLICT DO NOTHING
        ");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('finance_event_category', [
            'path' => ['Custom.FBAStorageFee.transactionValue','Custom.FBALongTermStorageFee.transactionValue'],
        ]);
    }
}
