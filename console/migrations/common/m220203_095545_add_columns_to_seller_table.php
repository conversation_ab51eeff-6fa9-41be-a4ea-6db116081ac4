<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 *
 */
class m220203_095545_add_columns_to_seller_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('seller', 'is_token_received', $this->boolean()->notNull()->defaultValue(true));
        $this->addColumn('seller', 'last_attempt_to_get_token', $this->dateTime()->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('seller', 'is_token_received');
        $this->dropColumn('seller', 'last_attempt_to_get_token');
    }
}
