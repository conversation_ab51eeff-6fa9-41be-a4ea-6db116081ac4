<?php

namespace console\migrations\common;

use yii\db\Migration;

class m220217_125333_add_column_mapping_rule extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('sales_category', 'mapping_rule', $this->string());
        $this->addColumn('sales_category', 'is_exclude_from_calculation', $this->boolean()->defaultValue('f'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('sales_category', 'mapping_rule');
        $this->dropColumn('sales_category', 'is_exclude_from_calculation');
    }
}
