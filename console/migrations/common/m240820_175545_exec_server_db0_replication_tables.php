<?php

namespace console\migrations\common;

use yii\db\Migration;

class m240820_175545_exec_server_db0_replication_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $dbHost = getenv('APP_DB_HOST');
        $dbPort = getenv('APP_DB_PORT');
        $dbUser = getenv('APP_DB_USERNAME');
        $db1User = getenv('APP_DB_1_USERNAME');
        $db1UserSlave = getenv('APP_DB_1_USERNAME_SLAVE');
        $dbPassword = getenv('APP_DB_PASSWORD');

        \Yii::$app->db1->createCommand("
            CREATE EXTENSION IF NOT EXISTS postgres_fdw;
            CREATE SERVER db0_replication_tables
                FOREIGN DATA WRAPPER postgres_fdw
                OPTIONS (host '$dbHost', port '$dbPort', dbname 'profit_dash_db');
        
        
            CREATE USER MAPPING FOR $db1User
                SERVER db0_replication_tables
                OPTIONS (user '$dbUser', password '$dbPassword');
        
            CREATE USER MAPPING FOR $db1UserSlave
                SERVER db0_replication_tables
                OPTIONS (user '$dbUser', password '$dbPassword');
        
            IMPORT FOREIGN SCHEMA public
                LIMIT TO (sales_category)
                FROM SERVER db0_replication_tables
                INTO public;
        
            IMPORT FOREIGN SCHEMA public
                LIMIT TO (amazon_marketplace)
                FROM SERVER db0_replication_tables
                INTO public;
        
            IMPORT FOREIGN SCHEMA public
                LIMIT TO (finance_event_category)
                FROM SERVER db0_replication_tables
                INTO public;
        ")->execute();

        \Yii::$app->db->createCommand("SELECT 1");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        \Yii::$app->db1->createCommand("DROP SERVER db0_replication_tables CASCADE")->execute();
        \Yii::$app->db->createCommand("SELECT 1");
    }
}
