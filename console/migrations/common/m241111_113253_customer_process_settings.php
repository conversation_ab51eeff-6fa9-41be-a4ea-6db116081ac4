<?php

namespace console\migrations\common;

use yii\db\Migration;

/**
 * Class m240918_175932_new_finance_category
 */
class m241111_113253_customer_process_settings extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('customer_process', 'config', $this->json());

        $this->createTable('nodes_availability', [
            'id' => $this->primaryKey(),
            'ip' => $this->string()->notNull(),
            'type' => $this->string()->notNull(),
            'status' => $this->string()->notNull(),
            'customer_id' => $this->integer(),
            'log' => $this->text(),
            'last_state_check_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')
        ]);
        $this->createIndex('idx-nodes_availability-status-state', 'nodes_availability', [
            'ip',
            'type',
            'status',
            'customer_id',
        ]);
        $this->createIndex('uk-nodes_availability-ip-type-customer_id', 'nodes_availability', [
            'ip',
            'type',
            'customer_id',
        ], true);

        $this->execute("
            INSERT INTO seller (id, customer_id, is_active, is_analytic_active, region, created_at)
            VALUES ('UNDEFINED', 0, 't', 't', 'eu-west-1', NOW())
            ON CONFLICT DO NOTHING;
        ");
    }
}
