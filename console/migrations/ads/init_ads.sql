CREATE TABLE ads_00000.amazon_ads_account (
    id serial4 NOT NULL,
    is_token_received bool NOT NULL DEFAULT true,
    last_attempt_to_get_token timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active bool NOT NULL DEFAULT true,
    CONSTRAINT amazon_ads_account_pkey PRIMARY KEY (id)
);

CREATE TABLE ads_00000.amazon_ads_campaign (
    id bigserial NOT NULL,
    "name" varchar(255) NOT NULL,
    profile_id int8 NULL,
    created_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT amazon_ads_campaign_pkey PRIMARY KEY (id)
);

CREATE TABLE ads_00000.sb_ad (
    id serial4 NOT NULL,
    profile_id int8 NOT NULL,
    campaign_id int8 NULL,
    ad_group_id int8 NULL,
    asin varchar(255) NULL,
    brand_name varchar(255) NULL,
    "type" varchar(255) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT sb_ad_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-sb_ad-ad_group_id" ON ads_00000.sb_ad USING btree (ad_group_id);
CREATE INDEX "idx-sb_ad-profile_id" ON ads_00000.sb_ad USING btree (profile_id);
CREATE UNIQUE INDEX "uk-sb_ad" ON ads_00000.sb_ad USING btree (profile_id, ad_group_id, asin);

CREATE TABLE ads_00000.sb_ad_group_statistic (
    id serial4 NOT NULL,
    profile_id int8 NOT NULL,
    "date" date NULL,
    campaign_id int8 NULL,
    ad_group_id int8 NULL,
    clicks int4 NULL,
    "cost" numeric(12, 2) NULL,
    impressions int4 NULL,
    currency_code varchar(255) NULL,
    moved_to_clickhouse_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status_moved_to_clickhouse varchar(255) NOT NULL DEFAULT 'created'::character varying,
    CONSTRAINT sb_ad_group_statistic_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-sb_ad_group_statistic-ad_group_id" ON ads_00000.sb_ad_group_statistic USING btree (ad_group_id);
CREATE INDEX "idx-sb_ad_group_statistic-created_at" ON ads_00000.sb_ad_group_statistic USING btree (created_at);
CREATE INDEX "idx-sb_ad_group_statistic-date" ON ads_00000.sb_ad_group_statistic USING btree (date);
CREATE INDEX "idx-sb_ad_group_statistic-moved_to_clickhouse_at" ON ads_00000.sb_ad_group_statistic USING btree (moved_to_clickhouse_at);
CREATE INDEX "idx-sb_ad_group_statistic-profile_id" ON ads_00000.sb_ad_group_statistic USING btree (profile_id);
CREATE INDEX "idx-sb_ad_group_statistic-status_moved_to_clickhouse" ON ads_00000.sb_ad_group_statistic USING btree (status_moved_to_clickhouse);
CREATE INDEX idx_sb_ad_group_statistic_date_cost_status_moved_to_clickhouse ON ads_00000.sb_ad_group_statistic USING btree (cost, date, status_moved_to_clickhouse);
CREATE UNIQUE INDEX "uk-sb_ad_group_statistic" ON ads_00000.sb_ad_group_statistic USING btree (date, profile_id, ad_group_id);

CREATE TABLE ads_00000.sd_advertised_product (
    id serial4 NOT NULL,
    profile_id int8 NOT NULL,
    "date" date NULL,
    campaign_id int8 NULL,
    ad_id int8 NULL,
    clicks int4 NULL,
    "cost" numeric(12, 2) NULL,
    spend numeric(12, 2) NULL,
    impressions int4 NULL,
    purchases int4 NULL,
    purchases_clicks int4 NULL,
    purchases_promoted_clicks int4 NULL,
    sales numeric(12, 2) NULL,
    sales_clicks int4 NULL,
    sales_promoted_clicks int4 NULL,
    units_sold int4 NULL,
    units_sold_clicks int4 NULL,
    currency_code varchar(255) NULL,
    asin varchar(255) NULL,
    sku varchar(255) NULL,
    moved_to_clickhouse_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status_moved_to_clickhouse varchar(255) NOT NULL DEFAULT 'created'::character varying,
    CONSTRAINT sd_advertised_product_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-sd_advertised_product-created_at" ON ads_00000.sd_advertised_product USING btree (created_at);
CREATE INDEX "idx-sd_advertised_product-date" ON ads_00000.sd_advertised_product USING btree (date);
CREATE INDEX "idx-sd_advertised_product-moved_to_clickhouse_at" ON ads_00000.sd_advertised_product USING btree (moved_to_clickhouse_at);
CREATE INDEX "idx-sd_advertised_product-profile_id" ON ads_00000.sd_advertised_product USING btree (profile_id);
CREATE INDEX "idx-sd_advertised_product-status_moved_to_clickhouse" ON ads_00000.sd_advertised_product USING btree (status_moved_to_clickhouse);
CREATE INDEX idx_sd_advertised_product_date_cost_status_moved_to_clickhouse ON ads_00000.sd_advertised_product USING btree (cost, date, status_moved_to_clickhouse);
CREATE UNIQUE INDEX "uk-sd_advertised_product" ON ads_00000.sd_advertised_product USING btree (date, sku, profile_id, ad_id);

CREATE TABLE ads_00000.sp_advertised_product (
    id serial4 NOT NULL,
    profile_id int8 NOT NULL,
    "date" date NULL,
    campaign_id int8 NULL,
    ad_id int8 NULL,
    clicks int4 NULL,
    "cost" numeric(12, 2) NULL,
    spend numeric(12, 2) NULL,
    impressions int4 NULL,
    purchases_1d int4 NULL,
    purchases_same_sku_1d int4 NULL,
    units_sold_clicks_1d int4 NULL,
    sales_1d numeric(12, 2) NULL,
    attributed_sales_same_sku_1d numeric(12, 2) NULL,
    units_sold_same_sku_1d int4 NULL,
    currency_code varchar(255) NULL,
    asin varchar(255) NULL,
    sku varchar(255) NULL,
    moved_to_clickhouse_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status_moved_to_clickhouse varchar(255) NOT NULL DEFAULT 'created'::character varying,
    CONSTRAINT sp_advertised_product_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-sp_advertised_product-created_at" ON ads_00000.sp_advertised_product USING btree (created_at);
CREATE INDEX "idx-sp_advertised_product-date" ON ads_00000.sp_advertised_product USING btree (date);
CREATE INDEX "idx-sp_advertised_product-moved_to_clickhouse_at" ON ads_00000.sp_advertised_product USING btree (moved_to_clickhouse_at);
CREATE INDEX "idx-sp_advertised_product-profile_id" ON ads_00000.sp_advertised_product USING btree (profile_id);
CREATE INDEX "idx-sp_advertised_product-status_moved_to_clickhouse" ON ads_00000.sp_advertised_product USING btree (status_moved_to_clickhouse);
CREATE INDEX idx_sp_advertised_product_date_cost_status_moved_to_clickhouse ON ads_00000.sp_advertised_product USING btree (cost, date, status_moved_to_clickhouse);
CREATE UNIQUE INDEX "uk-sp_advertised_product" ON ads_00000.sp_advertised_product USING btree (date, sku, profile_id, ad_id);

CREATE TABLE ads_00000.amazon_ads_profile (
    id bigserial NOT NULL,
    account_id int4 NOT NULL,
    region varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_id varchar(255) NOT NULL,
    daily_budget numeric(16, 2) NULL DEFAULT NULL::numeric,
    country_code varchar(255) NULL DEFAULT NULL::character varying,
    currency_code varchar(255) NULL DEFAULT NULL::character varying,
    timezone varchar(255) NULL DEFAULT NULL::character varying,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT amazon_ads_profile_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-amazon_ads_profile-account_id" FOREIGN KEY (account_id) REFERENCES ads_00000.amazon_ads_account(id) ON DELETE CASCADE
);