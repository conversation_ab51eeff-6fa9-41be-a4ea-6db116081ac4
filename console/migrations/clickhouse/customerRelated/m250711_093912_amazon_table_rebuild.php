<?php

namespace console\migrations\clickhouse\customerRelated;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyFbaReturn;
use common\components\clickhouse\materializedViews\proxy\ProxyFbmReturn;
use common\components\clickhouse\materializedViews\proxy\ProxyProduct;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\tables\PpcCostsLastFewDaysTable;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\core\db\dbManager\DbManager;

class m250711_093912_amazon_table_rebuild extends Migration
{

    /**
     * @throws \Throwable
     */
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDb = $dbManager->getClickhouseCustomerDb();

        if ($customerDb->dsn !== $this->db->dsn) {
            return true;
        }

        $manager = new DynamicTablesManager(false, false, 3);
        $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
        $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());
    }

    public function down()
    {
    }
}
