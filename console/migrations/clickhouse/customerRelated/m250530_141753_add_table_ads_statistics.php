<?php

namespace console\migrations\clickhouse\customerRelated;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDictV1;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyFbaEstimatedFeeHistory;
use common\components\clickhouse\materializedViews\proxy\ProxyReferralFeePreviewHistory;
use common\components\clickhouse\materializedViews\tables\AdsStatisticsTable;
use common\components\clickhouse\materializedViews\views\AdsStatisticsExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\FbaFeeFactorView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\ReferralFeeFactorView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;

class m250530_141753_add_table_ads_statistics extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes();

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new AdsStatisticsTable(false));
        $manager->rebuildDynamicTable(new AdsStatisticsExtendedView(false));

    }

    public function down()
    {
    }
}
