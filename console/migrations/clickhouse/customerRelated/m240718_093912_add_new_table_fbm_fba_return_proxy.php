<?php

namespace console\migrations\clickhouse\customerRelated;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyFbaReturn;
use common\components\clickhouse\materializedViews\proxy\ProxyFbmReturn;
use common\components\clickhouse\materializedViews\proxy\ProxyProduct;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\tables\PpcCostsLastFewDaysTable;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\core\db\dbManager\DbManager;

class m240718_093912_add_new_table_fbm_fba_return_proxy extends Migration
{

    /**
     * @throws \Throwable
     */
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerDb = $dbManager->getClickhouseCustomerDb();

        if ($customerDb->dsn !== $this->db->dsn) {
            return true;
        }

        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new ProxyFbmReturn());
        $manager->rebuildDynamicTable(new ProxyFbaReturn());
        $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedView());
        $manager->rebuildDynamicTable(new AmazonOrderExtendedView());
    }

    public function down()
    {
    }
}
