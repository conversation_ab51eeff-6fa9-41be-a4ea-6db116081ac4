<?php

namespace console\migrations\clickhouse\customerRelated;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDictV1;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;

class m250519_121753_new_categories_mapping_views6 extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes(false);

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        if ($dbManager->isActive()) {
            return true;
        }

        $manager = new DynamicTablesManager(false, false, 3);
        $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedViewV1());
        $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());
        $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
        $manager->rebuildDynamicTable(new TransactionExtendedViewV1());

    }

    public function down()
    {
    }
}