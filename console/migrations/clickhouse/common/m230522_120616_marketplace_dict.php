<?php

namespace console\migrations\clickhouse\common;

use bash<PERSON><PERSON>\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\AmazonMarketplaceDict;
use common\components\clickhouse\materializedViews\DynamicTablesManager;

class m230522_120616_marketplace_dict extends Migration
{
    public function up()
    {
        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new AmazonMarketplaceDict());
    }

    public function down()
    {
    }
}