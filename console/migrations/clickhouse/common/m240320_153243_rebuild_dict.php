<?php

namespace console\migrations\clickhouse\common;

use bash<PERSON><PERSON>\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\FinanceEventCategoryDict;
use common\components\clickhouse\materializedViews\DynamicTablesManager;

class m240320_153243_rebuild_dict extends Migration
{
    public function up()
    {
        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new FinanceEventCategoryDict());
    }

    public function down()
    {
    }
}