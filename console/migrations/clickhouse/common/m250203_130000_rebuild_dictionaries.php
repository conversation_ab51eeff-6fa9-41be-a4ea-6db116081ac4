<?php

namespace console\migrations\clickhouse\common;

use bash<PERSON><PERSON>\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\FinanceEventCategoryDict;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyProduct;

class m250203_130000_rebuild_dictionaries extends Migration
{
    public function up()
    {
        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new FinanceEventCategoryDict());
    }

    public function down()
    {
    }
}
