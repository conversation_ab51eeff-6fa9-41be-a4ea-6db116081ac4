<?php

namespace console\migrations\clickhouse\common;

use bash<PERSON><PERSON>\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\CurrencyRateDict;
use common\components\clickhouse\materializedViews\DynamicTablesManager;

class m220601_130554_currency_rate_dict extends Migration
{
    public function up()
    {
        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new CurrencyRateDict());
    }

    public function down()
    {
    }
}