<?php

namespace console\migrations\clickhouse\customer;

use bashkarev\clickhouse\Migration;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\clickhouse\TransactionBuffer;
use common\models\customer\clickhouse\TransactionBufferTmp;

class m221026_133416_transaction_dist extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $customerConfig->set(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED, 1);
        $customerConfig->set(CustomerConfig::PARAMETER_USE_REALTIME_ORDER_DATA_UPDATES, 1);

        $dbHelper = new ClickhouseDbHelper();
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes(false);

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $transactionTableName = Transaction::tableName();
        $engine = sprintf(
            "ReplicatedSummingMergeTree('%s', '{replica}', (Quantity, Amount, AmountEUR, MergeCounter))",
            $dbHelper->generateReplicationPath(Transaction::tableName())
        );

        $dbHelper->executeOnMasterNodes("CREATE TABLE IF NOT EXISTS {$transactionTableName}
            (
                `PostedDate` DateTime,
                `SellerId` String,
                `MarketplaceId` String,
                `CategoryId` Int32,
                `EventPeriodId` String,
                `SellerSKU` String,
                `ASIN` String,
                `SellerOrderId` String,
                `AmazonOrderId` String,
                `Quantity` Int32,
                `Amount` Int64,
                `Currency` String,
                `AmountEUR` Int64,
                `MergeCounter` Int32,
                `COGCategoryId` Int32,
                `CreatedAt` DateTime DEFAULT now(),
                `TransactionDate` DateTime,
                `IndirectCostId` Int32,
                `IndirectCostTypeId` Int32,
                INDEX idx_IndirectCostId IndirectCostId TYPE minmax GRANULARITY 8192,
                INDEX idx_IndirectCostTypeId IndirectCostTypeId TYPE minmax GRANULARITY 8192
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(PostedDate)
            PRIMARY KEY (PostedDate, MarketplaceId, SellerId, SellerSKU, SellerOrderId, AmazonOrderId, CategoryId, COGCategoryId, Currency)
            ORDER BY (PostedDate, MarketplaceId, SellerId, SellerSKU, SellerOrderId, AmazonOrderId, CategoryId, COGCategoryId, Currency, IndirectCostId)
            SETTINGS index_granularity = 8192
        ");

        $engine =  sprintf(
            "ReplicatedReplacingMergeTree('%s', '{replica}')",
            $dbHelper->generateReplicationPath(AmazonOrder::tableName())
        );
        $amazonOrderTable = AmazonOrder::tableName();
        $amazonOrderInProgressTable = AmazonOrderInProgress::tableName();
        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderTable}
            (
                {$dbHelper->getAmazonOrderBaseStructure()}
            )
            ENGINE = {$engine}
            PARTITION BY toYYYYMM(order_purchase_date)
            {$dbHelper->getAmazonOrderBaseOrder()}
            {$dbHelper->getAmazonOrderBasePrimaryKey()}
        ";
        $dbHelper->executeOnMasterNodes($sql);

        $engine = sprintf(
            "ReplicatedReplacingMergeTree('%s', '{replica}')",
            $dbHelper->generateReplicationPath(AmazonOrderInProgress::tableName())
        );
        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderInProgressTable}
              (
                {$dbHelper->getAmazonOrderBaseStructure()},
                `version` UInt64
            )
            ENGINE = {$engine}
            PARTITION BY version
            {$dbHelper->getAmazonOrderBaseOrder()}
            {$dbHelper->getAmazonOrderBasePrimaryKey()}
        ";
        $dbHelper->executeOnMasterNodes($sql);

        $transactionBufferTableName = TransactionBuffer::tableName();
        [$database, $destTableName] = explode('.', Transaction::tableName());
        $dbHelper->executeOnMasterNodes("CREATE TABLE IF NOT EXISTS {$transactionBufferTableName}
            (
                `PostedDate` DateTime,
                `SellerId` String,
                `MarketplaceId` String,
                `CategoryId` Int32,
                `EventPeriodId` String,
                `SellerSKU` String,
                `ASIN` String,
                `SellerOrderId` String,
                `AmazonOrderId` String,
                `Quantity` Int32,
                `Amount` Int64,
                `Currency` String,
                `AmountEUR` Int64,
                `MergeCounter` Int32,
                `COGCategoryId` Int32,
                `CreatedAt` DateTime DEFAULT now(),
                `TransactionDate` DateTime,
                `IndirectCostId` Int32,
                `IndirectCostTypeId` Int32
            )
            ENGINE = Buffer($database, $destTableName, 1, 10, 30, 1, 50000000, 50000000, 100000000)
        ");

        $transactionTmpTableName = TransactionBufferTmp::tableName();
        $dbHelper->executeOnMasterNodes("CREATE TABLE IF NOT EXISTS {$transactionTmpTableName}
            (
                `PostedDate` DateTime,
                `SellerId` String,
                `MarketplaceId` String,
                `CategoryId` Int32,
                `EventPeriodId` String,
                `SellerSKU` String,
                `ASIN` String,
                `SellerOrderId` String,
                `AmazonOrderId` String,
                `Quantity` Int32,
                `Amount` Int64,
                `Currency` String,
                `AmountEUR` Int64,
                `MergeCounter` Int32,
                `COGCategoryId` Int32,
                `CreatedAt` DateTime DEFAULT now(),
                `TransactionDate` DateTime,
                `IndirectCostId` Int32,
                `IndirectCostTypeId` Int32,
                `ProcessId` Int64
            )
            ENGINE = Memory()
        ");
    }

    public function down()
    {
    }
}