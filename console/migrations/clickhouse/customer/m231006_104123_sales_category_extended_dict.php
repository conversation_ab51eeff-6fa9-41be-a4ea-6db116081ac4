<?php

namespace console\migrations\clickhouse\customer;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\dictionaries\ProductCostCategoryDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDict;
use common\components\clickhouse\materializedViews\dictionaries\SalesCategoryExtendedDictV1;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyProduct;
use common\components\clickhouse\materializedViews\proxy\ProxyProductTag;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\tables\PpcCostsLastFewDaysTable;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\core\db\dbManager\DbManager;

class m231006_104123_sales_category_extended_dict extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes(false);

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $manager = new DynamicTablesManager(false, false);
        $manager->rebuildDynamicTable(new ProxyProduct());
        $manager->rebuildDynamicTable(new ProxyProductTag());
        $manager->rebuildDynamicTable(new ProductCostCategoryDict());
        $manager->rebuildDynamicTable(new SalesCategoryExtendedDict());
        $manager->rebuildDynamicTable(new SalesCategoryExtendedDictV1());
        $manager->rebuildDynamicTable(new PpcCostsLastFewDaysTable(false));
        $manager->rebuildDynamicTable(new OrderBasedTransaction(false));
        $manager->rebuildDynamicTable(new AmazonOrderExtendedView(false));
        $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedView(false));
        $manager->rebuildDynamicTable(new TransactionExtendedView(false));
        $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedView(false));
        $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedViewV1(false));
        $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1(false));
        $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1(false));
        $manager->rebuildDynamicTable(new TransactionExtendedViewV1(false));
    }

    public function down()
    {
    }
}