<?php

namespace console\migrations\clickhouse\customer;

use bashkarev\clickhouse\Migration;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\models\customer\clickhouse\RepricerEvent;

class m241024_065302_repricer_event extends Migration
{

    public function up()
    {
        //create summing merge tree table
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $customerConfig->set(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED, 1);
        $dbHelper = new ClickhouseDbHelper();
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes(false);

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $repricerEventTableName = RepricerEvent::tableName();
        $engine = $customerConfig->get(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED)
            ? sprintf(
                "ReplicatedSummingMergeTree('%s', '{replica}', (amount))",
                $dbHelper->generateReplicationPath($repricerEventTableName)
            )
            : 'SummingMergeTree(amount)';
        $dbHelper->executeOnMasterNodes("CREATE TABLE IF NOT EXISTS {$repricerEventTableName} (
            day Date,
            product_id Integer,
            sku String,
            marketplace_id String,
            seller_id String,
            amount UInt32,
            offer_type String DEFAULT '',
            updated_at DateTime DEFAULT now(),
        )
        ENGINE = {$engine}
        PARTITION BY toYYYYMM(day)
        PRIMARY KEY (day, product_id, offer_type)
        ORDER BY (day, product_id, offer_type)");

        return true;
    }

    public function down()
    {
    }
}
