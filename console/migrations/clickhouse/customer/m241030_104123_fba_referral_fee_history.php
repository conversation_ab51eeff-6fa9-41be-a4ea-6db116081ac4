<?php

namespace console\migrations\clickhouse\customer;

use bashkarev\clickhouse\Migration;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\proxy\ProxyFbaEstimatedFeeHistory;
use common\components\clickhouse\materializedViews\proxy\ProxyReferralFeePreviewHistory;
use common\components\clickhouse\materializedViews\views\FbaFeeFactorView;
use common\components\clickhouse\materializedViews\views\ReferralFeeFactorView;
use common\components\core\db\dbManager\DbManager;

class m241030_104123_fba_referral_fee_history extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes();

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $manager = new DynamicTablesManager();
        $manager->rebuildDynamicTable(new ProxyFbaEstimatedFeeHistory());
        $manager->rebuildDynamicTable(new ProxyReferralFeePreviewHistory());
        $manager->rebuildDynamicTable(new FbaFeeFactorView(false));
        $manager->rebuildDynamicTable(new ReferralFeeFactorView(false));
    }

    public function down()
    {
    }
}
