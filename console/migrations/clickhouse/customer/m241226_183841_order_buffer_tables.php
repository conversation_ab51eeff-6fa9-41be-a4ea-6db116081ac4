<?php

namespace console\migrations\clickhouse\customer;

use bashkarev\clickhouse\Migration;
use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\components\db\ClickhouseDbHelper;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderBuffer;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\AmazonOrderInProgressBuffer;
use common\models\customer\clickhouse\Transaction;

class m241226_183841_order_buffer_tables extends Migration
{
    public function up()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        $customerConfig->set(CustomerConfig::PARAMETER_IS_CLICKHOUSE_DATA_REPLICATED, 1);

        $dbHelper = new ClickhouseDbHelper();
        $masterNodes = $dbManager->getClickhouseCustomerMasterNodes(false);

        if (false === strpos($this->db->dsn, $masterNodes[0])) {
            return true;
        }

        $amazonOrderBufferTable = AmazonOrderBuffer::tableName();
        $amazonOrderInProgressBufferTable = AmazonOrderInProgressBuffer::tableName();

        [$database, $targetTableName] = explode('.', AmazonOrder::tableName());
        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderBufferTable}
            (
                {$dbHelper->getAmazonOrderBaseStructure()}
            )
            ENGINE = Buffer('$database', '$targetTableName', 1, 1000, 60, 1, 100000, 100000, 100000000)
        ";
        $dbHelper->executeOnMasterNodes($sql);

        [$database, $targetTableName] = explode('.', AmazonOrderInProgress::tableName());
        $sql = "CREATE TABLE IF NOT EXISTS {$amazonOrderInProgressBufferTable}
            (
                {$dbHelper->getAmazonOrderBaseStructure()},
                `version` UInt64
            )
            ENGINE = Buffer('$database', '$targetTableName', 1, 1000, 60, 1, 100000, 100000, 100000000)
        ";
        $dbHelper->executeOnMasterNodes($sql);
    }

    public function down()
    {
    }
}