<?php

declare(strict_types=1);

namespace console\migrations\customer;

use common\models\customer\ProductFilterList;
use yii\db\Migration;

class m250826_183300_fix_product_list_filter_customer_id_type extends Migration
{
    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function safeUp(): void
    {
        $tableName = ProductFilterList::tableName();
        $this->execute("
            ALTER TABLE {$tableName}
            ALTER COLUMN customer_id TYPE integer USING customer_id::integer
        ");
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function safeDown(): void
    {
        $tableName = ProductFilterList::tableName();
        $this->execute("
            ALTER TABLE {$tableName}
            ALTER COLUMN customer_id TYPE varchar USING customer_id::varchar
        ");
    }
}
