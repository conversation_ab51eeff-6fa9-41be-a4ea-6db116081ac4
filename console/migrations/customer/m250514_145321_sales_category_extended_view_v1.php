<?php

namespace console\migrations\customer;

use common\models\customer\ProductCostCategory;
use common\models\SalesCategory;
use yii\db\Migration;

class m250514_145321_sales_category_extended_view_v1 extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $internalCOGPrefix = ProductCostCategory::INTERNAL_COG_PREFIX;
        $internalIndirectCostPrefix = ProductCostCategory::INTERNAL_INDIRECT_COST_PREFIX;

        $schemaName = \Yii::$app->dbManager->getSchemaName('customer');
        $salesCategoryTableName = 'public.sales_category_v1';
        $salesCategoryExtendedViewTableName = $schemaName . '.sales_category_extended_view_v1';
        $productCostCategoryTableName = $schemaName . '.product_cost_category';
        $indirectCostTypeTableName = $schemaName . '.indirect_cost_type';

        $this->execute("DROP VIEW IF EXISTS {$salesCategoryExtendedViewTableName}");

        $sql = "
            CREATE OR REPLACE VIEW {$salesCategoryExtendedViewTableName} AS
            SELECT 
                sc.id,
                sc.name,
                sc.is_visible,
                sc.depth,
                sc.sort_order,
                sc.path,
                sc.type,
                sc.tags,
                false as is_manual
            FROM {$salesCategoryTableName} sc
            UNION
            SELECT 
                concat('$internalCOGPrefix', pcc.id) as id,
                pcc.name,
                true as is_visible,
                sc.depth + 1 as depth,
                sc.sort_order + pcc.id as sort_order,
                concat(sc.path, '|$internalCOGPrefix', pcc.id) as path,
                sc.type,
                sc.tags,
                true as is_manual
            FROM {$productCostCategoryTableName} pcc
            LEFT JOIN {$salesCategoryTableName} sc ON (
                CASE
                    WHEN pcc.sales_category_id = 'expenses_taxes' THEN '" . SalesCategory::TAG_MANUAL_VAT . "' 
                    WHEN pcc.sales_category_id = 'cost_of_goods' THEN '" . SalesCategory::TAG_MANUAL_COST_OF_GOODS . "' 
                    WHEN pcc.sales_category_id = 'shipping_costs' THEN '" . SalesCategory::TAG_MANUAL_FBM_SHIPPING_COSTS . "' 
                    WHEN pcc.sales_category_id = 'other_fees' THEN '" . SalesCategory::TAG_MANUAL_OTHER_FEES . "' 
                END
            ) = ANY(sc.tags)
            UNION
            SELECT 
                concat('$internalIndirectCostPrefix', ict.id) as id,
                ict.name,
                true as is_visible,
                sc.depth + 1 as depth,
                sc.sort_order + ict.id as sort_order,
                concat(sc.path, '|$internalIndirectCostPrefix', ict.id) as path,
                sc.type,
                sc.tags,
                true as is_manual
            FROM {$indirectCostTypeTableName} ict
            LEFT JOIN {$salesCategoryTableName} sc ON '" . SalesCategory::TAG_MANUAL_INDIRECT_COSTS . "' = ANY(sc.tags);
        ";

        $this->execute($sql);
    }
}
