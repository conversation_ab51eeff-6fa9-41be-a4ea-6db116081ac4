CREATE TABLE customer_00000.amazon_report (
    id serial4 NOT NULL,
    amazon_id varchar(255) NULL,
    amazon_type varchar(255) NOT NULL,
    amazon_document_id varchar(255) NULL,
    amazon_processing_status varchar(255) NOT NULL DEFAULT 'UNKNOWN'::character varying,
    seller_id varchar(255) NOT NULL,
    start_date timestamp(0) NOT NULL,
    end_date timestamp(0) NOT NULL,
    status varchar(255) NOT NULL DEFAULT 'WAITING'::character varying,
    "type" varchar(255) NOT NULL,
    log text NULL,
    created_at timestamp(0) NOT NULL DEFAULT now(),
    updated_at timestamp(0) NOT NULL DEFAULT now(),
    data_provider varchar(255) NULL,
    extra_info jsonb NULL,
    finished_at timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    time_between_attempts_m int4 NULL,
    attempts_made int4 NULL DEFAULT 0,
    stop_repeating_at timestamp(0) NULL DEFAULT NULL::timestamp without time zone,
    CONSTRAINT amazon_report_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-amazon_report_data_provider" ON customer_00000.amazon_report USING btree (data_provider);
CREATE INDEX "idx-amazon_report_end_date" ON customer_00000.amazon_report USING btree (end_date);

CREATE TABLE customer_00000.data_completeness (
    id serial4 NOT NULL,
    factor_id varchar(255) NULL,
    count_all int4 NOT NULL DEFAULT 0,
    count_unfilled int4 NOT NULL DEFAULT 0,
    is_ignored bool NOT NULL DEFAULT false,
    checked_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT data_completeness_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "uk-data_completeness_factor-id" ON customer_00000.data_completeness USING btree (factor_id);

CREATE TABLE customer_00000.data_export_template (
    id serial4 NOT NULL,
    title varchar(200) NOT NULL,
    criteria jsonb NULL,
    fields jsonb NULL,
    format varchar(50) NOT NULL DEFAULT 'csv'::character varying,
    is_default bool NOT NULL DEFAULT false,
    handler_name varchar(100) NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT data_export_template_pkey PRIMARY KEY (id)
);

CREATE TABLE customer_00000.data_import (
    id serial4 NOT NULL,
    handler_name varchar(100) NOT NULL,
    status varchar(15) NOT NULL DEFAULT 'new'::character varying,
    count_parts int4 NOT NULL DEFAULT 0,
    count_all_items int4 NOT NULL DEFAULT 0,
    count_imported_items int4 NOT NULL DEFAULT 0,
    count_errors int4 NOT NULL DEFAULT 0,
    file_url varchar(500) NULL,
    "exception" text NULL,
    errors jsonb NULL,
    language_code varchar(2) NOT NULL DEFAULT 'EN'::character varying,
    data_start_line_number int4 NOT NULL DEFAULT 2,
    "type" varchar(255) NOT NULL DEFAULT 'manual'::character varying,
    finished_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    params text NULL,
    last_id int4 NULL,
    started_at timestamp(0) NULL,
    CONSTRAINT data_import_pkey PRIMARY KEY (id)
);

CREATE TABLE customer_00000.fba_estimated_fee (
    id serial4 NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_id varchar(255) NOT NULL,
    sku varchar(255) NOT NULL,
    currency varchar(3) NOT NULL,
    sales_price numeric(10, 2) NULL,
    estimated_referral_fee_per_unit numeric(10, 2) NULL,
    estimated_variable_closing_fee numeric(10, 2) NULL,
    estimated_fixed_closing_fee numeric(10, 2) NULL,
    expected_domestic_fulfilment_fee_per_unit numeric(10, 2) NULL,
    created_at timestamp(0) NOT NULL DEFAULT now(),
    updated_at timestamp(0) NOT NULL DEFAULT now(),
    CONSTRAINT fba_estimated_fee_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "uk-fba_estimated_fee" ON customer_00000.fba_estimated_fee USING btree (marketplace_id, seller_id, sku);

CREATE TABLE customer_00000.fba_estimated_fee_history (
    id serial4 NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_id varchar(255) NOT NULL,
    sku varchar(255) NOT NULL,
    currency varchar(3) NOT NULL,
    sales_price numeric(15, 2) NULL,
    estimated_referral_fee_per_unit numeric(10, 2) NULL,
    estimated_variable_closing_fee numeric(10, 2) NULL,
    estimated_fixed_closing_fee numeric(10, 2) NULL,
    expected_domestic_fulfilment_fee_per_unit numeric(10, 2) NULL,
    created_at timestamp(0) NOT NULL DEFAULT now(),
    "date" date NOT NULL DEFAULT CURRENT_DATE,
    CONSTRAINT fba_estimated_fee_history_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "idx-fba_estimated_fee_history" ON customer_00000.fba_estimated_fee_history USING btree (marketplace_id, seller_id, sku, date);

CREATE TABLE customer_00000.fba_return (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    return_date timestamp(0) NOT NULL,
    order_id varchar(255) NOT NULL,
    sku varchar(255) NOT NULL,
    asin varchar(255) NOT NULL,
    fnsku varchar(255) NOT NULL,
    product_name text NULL,
    quantity int4 NULL,
    fulfillment_center_id varchar(255) NULL,
    detailed_disposition varchar(255) NULL,
    reason varchar(255) NULL,
    status varchar(255) NULL,
    license_plate_number varchar(255) NULL,
    customer_comments text NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    moved_to_clickhouse_at timestamp(0) NULL,
    CONSTRAINT fba_return_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-fba_return-find-not-sellable" ON customer_00000.fba_return USING btree (moved_to_clickhouse_at, detailed_disposition, status);
CREATE INDEX "idx-fba_return-order_id-sku" ON customer_00000.fba_return USING btree (order_id, sku);
CREATE INDEX "idx-fba_return-return_date-status" ON customer_00000.fba_return USING btree (return_date, status);
CREATE INDEX "idx-fba_return-seller_id" ON customer_00000.fba_return USING btree (seller_id);
CREATE INDEX idx_fba_return_return_date_status_filtered ON customer_00000.fba_return USING btree (return_date DESC, id DESC) WHERE ((moved_to_clickhouse_at IS NULL) AND ((detailed_disposition)::text <> 'SELLABLE'::text) AND ((status)::text <> ALL ((ARRAY['Reimbursed'::character varying, 'Repackaged Successfully'::character varying])::text[])));
CREATE UNIQUE INDEX "uk-fba_return" ON customer_00000.fba_return USING btree (order_id, sku, return_date);

CREATE TABLE customer_00000.fbm_return (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    order_id varchar(255) NOT NULL,
    order_date timestamp(0) NOT NULL,
    return_request_date timestamp(0) NOT NULL,
    return_request_status varchar(255) NULL,
    amazon_rma_id varchar(255) NULL,
    label_type varchar(255) NULL,
    label_cost numeric(15, 4) NULL,
    currency_code varchar(255) NULL,
    return_carrier varchar(255) NULL,
    tracking_id varchar(255) NULL,
    label_to_paid_by varchar(255) NULL,
    a_to_z_claim bool NULL,
    is_prime bool NULL,
    asin varchar(255) NULL,
    merchant_sku varchar(255) NULL,
    item_name varchar(255) NULL,
    return_quantity int4 NULL,
    return_reason varchar(255) NULL,
    in_policy bool NULL,
    return_type varchar(255) NULL,
    resolution varchar(255) NULL,
    order_amount numeric(15, 4) NULL,
    order_quantity int4 NULL,
    refund_amount numeric(15, 4) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fbm_return_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-fbm_return-return_request_date-return_request_status" ON customer_00000.fbm_return USING btree (return_request_date, return_request_status);
CREATE INDEX "idx-fbm_return-seller_id" ON customer_00000.fbm_return USING btree (seller_id);
CREATE UNIQUE INDEX "uk-fbm_return" ON customer_00000.fbm_return USING btree (order_id, return_request_date, merchant_sku, amazon_rma_id);

CREATE TABLE customer_00000.indirect_cost_type (
    id serial4 NOT NULL,
    "name" varchar(255) NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_predefined bool NULL DEFAULT false,
    CONSTRAINT indirect_cost_type_pkey PRIMARY KEY (id)
);
CREATE INDEX "uk-indirect_cost_type-name" ON customer_00000.indirect_cost_type USING btree (name);

CREATE TABLE customer_00000.product (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    "source" varchar(255) NOT NULL,
    title text NULL,
    sku varchar(255) NOT NULL,
    asin varchar(255) NULL,
    buying_price numeric(15, 2) NULL,
    other_fees numeric(15, 2) NULL,
    shipping_cost numeric(15, 2) NULL,
    vat numeric(15, 2) NULL,
    stock_type varchar(255) NULL,
    currency_code varchar(3) NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    repricer_id int4 NULL,
    "condition" int4 NULL,
    is_product_cost_periods_linked bool NULL DEFAULT true,
    is_enabled_sync_with_repricer bool NOT NULL DEFAULT true,
    repricer_is_deleted bool NULL,
    is_multiple_stock_type bool NULL DEFAULT false,
    catalog_product_name varchar(500) NULL,
    ean varchar(100) NULL,
    isbn varchar(100) NULL,
    upc varchar(100) NULL,
    main_image varchar(200) NULL,
    parent_asin varchar(100) NULL,
    brand varchar(100) NULL,
    model varchar(100) NULL,
    product_type varchar(100) NULL,
    manufacturer varchar(100) NULL,
    age_range varchar(100) NULL,
    adult_product bool NULL,
    CONSTRAINT product_cost_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-asin" ON customer_00000.product USING btree (asin);
CREATE INDEX "idx-brand" ON customer_00000.product USING btree (adult_product, brand, seller_id);
CREATE INDEX "idx-buying_price-is-null" ON customer_00000.product USING btree (buying_price) WHERE (buying_price IS NULL);
CREATE INDEX "idx-condition" ON customer_00000.product USING btree (condition);
CREATE INDEX "idx-ean" ON customer_00000.product USING btree (ean, seller_id, adult_product);
CREATE INDEX "idx-isbn" ON customer_00000.product USING btree (isbn, seller_id, adult_product);
CREATE INDEX "idx-lower_brand" ON customer_00000.product USING btree (lower((brand)::text));
CREATE INDEX "idx-lower_manufacturer" ON customer_00000.product USING btree (lower((manufacturer)::text));
CREATE INDEX "idx-manufacturer" ON customer_00000.product USING btree (adult_product, manufacturer, seller_id);
CREATE INDEX "idx-marketplace_id" ON customer_00000.product USING btree (marketplace_id);
CREATE INDEX "idx-marketplace_id_seller_id_adult" ON customer_00000.product USING btree (adult_product, seller_id, marketplace_id);
CREATE INDEX "idx-other_fees-is-null" ON customer_00000.product USING btree (other_fees) WHERE (other_fees IS NULL);
CREATE INDEX "idx-parent_asin" ON customer_00000.product USING btree (parent_asin, seller_id, adult_product);
CREATE INDEX "idx-product-is_multiple_stock_type" ON customer_00000.product USING btree (is_multiple_stock_type);
CREATE INDEX "idx-product_type" ON customer_00000.product USING btree (product_type);
CREATE INDEX "idx-product_type_adult" ON customer_00000.product USING btree (product_type, seller_id, adult_product);
CREATE INDEX "idx-repricer_id" ON customer_00000.product USING btree (repricer_id);
CREATE INDEX "idx-seller_id" ON customer_00000.product USING btree (seller_id);
CREATE INDEX "idx-shipping_cost-is-null" ON customer_00000.product USING btree (shipping_cost) WHERE (shipping_cost IS NULL);
CREATE INDEX "idx-sku" ON customer_00000.product USING btree (sku);
CREATE INDEX "idx-stock_type" ON customer_00000.product USING btree (stock_type);
CREATE INDEX "idx-stock_type_adult" ON customer_00000.product USING btree (stock_type, seller_id, adult_product);
CREATE INDEX "idx-title" ON customer_00000.product USING btree (title);
CREATE INDEX "idx-upc" ON customer_00000.product USING btree (upc, seller_id, adult_product);
CREATE INDEX "idx-vat-is-null" ON customer_00000.product USING btree (vat) WHERE (vat IS NULL);
CREATE UNIQUE INDEX "uk-product" ON customer_00000.product USING btree (marketplace_id, seller_id, sku);
CREATE INDEX "uk-product-completeness-factor-calculation-1" ON customer_00000.product USING btree (repricer_id, repricer_is_deleted, is_enabled_sync_with_repricer);
CREATE INDEX "uk-product-completeness-factor-calculation-2" ON customer_00000.product USING btree (stock_type, is_multiple_stock_type);

CREATE TABLE customer_00000.product_bsr (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    sku varchar(255) NOT NULL,
    bsr int4 NOT NULL,
    "date" timestamp(0) NOT NULL,
    created_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT product_bsr_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-product_bsr-date" ON customer_00000.product_bsr USING btree (date);
CREATE INDEX "idx-product_bsr-marketplace_id-seller_id-sku" ON customer_00000.product_bsr USING btree (marketplace_id, seller_id, sku);

CREATE TABLE customer_00000.product_cost_category (
    id serial4 NOT NULL,
    "name" varchar(255) NOT NULL,
    "source" varchar(255) NOT NULL DEFAULT 'manual'::character varying,
    is_default bool NOT NULL DEFAULT false,
    sales_category_id varchar(255) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_editable bool NOT NULL DEFAULT true,
    CONSTRAINT product_cost_category_pkey PRIMARY KEY (id)
);
CREATE INDEX "uk-name-sales_category_id" ON customer_00000.product_cost_category USING btree (name, sales_category_id);

INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('VAT', 'repricer', true, 'expenses_taxes', false);
INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('Default', 'repricer', true, 'shipping_costs', false);
INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('Default', 'repricer', true, 'other_fees', false);
INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('Net purchase price', 'manual', true, 'cost_of_goods', false);
INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('Inbound shipment', 'manual', false, 'cost_of_goods', false);
INSERT INTO customer_00000.product_cost_category ("name", "source", is_default, sales_category_id, is_editable) VALUES('Customs duty', 'manual', false, 'cost_of_goods', false);

CREATE TABLE customer_00000.product_cost_period (
    id serial4 NOT NULL,
    marketplace_id varchar(255) NULL,
    seller_sku varchar(255) NOT NULL,
    date_start timestamp(0) NULL,
    date_end timestamp(0) NULL,
    amount_total numeric(15, 2) NULL DEFAULT 0,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "source" varchar(255) NULL,
    seller_id varchar(255) NULL,
    sales_category_id varchar(255) NOT NULL DEFAULT 'undefined'::character varying,
    CONSTRAINT product_cost_period_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-product_cost_period-created_at" ON customer_00000.product_cost_period USING btree (created_at);
CREATE INDEX "idx-product_cost_period-seller_id" ON customer_00000.product_cost_period USING btree (seller_id);
CREATE UNIQUE INDEX "idx-search-nearest-periods" ON customer_00000.product_cost_period USING btree (marketplace_id, seller_id, seller_sku, sales_category_id, date_start);
CREATE UNIQUE INDEX "idx-search-nearest-periods-null-date-start" ON customer_00000.product_cost_period USING btree (marketplace_id, seller_id, seller_sku, sales_category_id, COALESCE(date_start, '1900-01-01 00:00:00'::timestamp without time zone));
CREATE INDEX "idx-seller_sku" ON customer_00000.product_cost_period USING btree (seller_sku);

CREATE TABLE customer_00000.product_filter_list (
    id serial4 NOT NULL,
    customer_id varchar(255) NOT NULL,
    user_id int4 NOT NULL,
    "name" varchar(255) NOT NULL,
    filters jsonb NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT product_filter_list_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "idx-name_filter_list" ON customer_00000.product_filter_list USING btree (name, user_id, customer_id);

CREATE TABLE customer_00000.referral_fee_preview (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_sku varchar(255) NOT NULL,
    asin varchar(255) NULL,
    price numeric(15, 2) NULL DEFAULT NULL::numeric,
    estimated_referral_fee_per_item numeric(10, 2) NULL,
    currency varchar(3) NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT now(),
    updated_at timestamp(0) NOT NULL DEFAULT now(),
    CONSTRAINT referral_fee_preview_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "uk-referral_fee_preview" ON customer_00000.referral_fee_preview USING btree (seller_id, marketplace_id, seller_sku);

CREATE TABLE customer_00000.referral_fee_preview_history (
    id serial4 NOT NULL,
    seller_id varchar(255) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_sku varchar(255) NOT NULL,
    asin varchar(255) NULL,
    price numeric(15, 2) NULL,
    estimated_referral_fee_per_item numeric(10, 2) NULL,
    currency varchar(3) NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT now(),
    "date" date NOT NULL DEFAULT CURRENT_DATE,
    CONSTRAINT referral_fee_preview_history_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "idx-referral_fee_preview_history" ON customer_00000.referral_fee_preview_history USING btree (seller_id, marketplace_id, seller_sku, date);

CREATE TABLE customer_00000.refund_transaction_without_product_cost (
    id serial4 NOT NULL,
    posted_date timestamp(0) NOT NULL,
    marketplace_id varchar(255) NOT NULL,
    seller_id varchar(255) NOT NULL,
    seller_sku varchar(255) NOT NULL,
    asin varchar(255) NULL,
    category_id int4 NOT NULL,
    amount int4 NOT NULL DEFAULT 0,
    currency varchar(255) NOT NULL,
    quantity int4 NOT NULL DEFAULT 1,
    amazon_order_id varchar(255) NOT NULL,
    seller_order_id varchar(255) NULL,
    event_period_id int4 NOT NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT refund_transaction_without_product_cost_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-created_at" ON customer_00000.refund_transaction_without_product_cost USING btree (created_at);
CREATE INDEX "idx-search-by-sales-transaction" ON customer_00000.refund_transaction_without_product_cost USING btree (marketplace_id, seller_id, seller_sku, amazon_order_id);
CREATE INDEX idx_refund_transaction_amazon_order_id_seller_sku ON customer_00000.refund_transaction_without_product_cost USING btree (amazon_order_id, seller_sku);

CREATE TABLE customer_00000.tag (
    id serial4 NOT NULL,
    title varchar(255) NOT NULL,
    color varchar(8) NOT NULL,
    CONSTRAINT tag_pkey PRIMARY KEY (id)
);

CREATE TABLE customer_00000.tag_color (
    id serial4 NOT NULL,
    tag_color varchar(255) NOT NULL,
    "type" varchar(255) NOT NULL,
    CONSTRAINT tag_color_pkey PRIMARY KEY (id)
);

CREATE TABLE customer_00000.transaction_buffer (
    id serial4 NOT NULL,
    "PostedDate" timestamp(0) NOT NULL,
    "SellerId" varchar(255) NULL,
    "MarketplaceId" varchar(255) NULL,
    "CategoryId" int4 NOT NULL,
    "EventPeriodId" int4 NOT NULL,
    "SellerSKU" varchar(255) NULL,
    "ASIN" varchar(255) NULL,
    "SellerOrderId" varchar(255) NULL,
    "AmazonOrderId" varchar(255) NULL,
    "Quantity" int4 NOT NULL,
    "Amount" int8 NULL,
    "AmountEUR" int8 NULL,
    "Currency" varchar(255) NOT NULL,
    "MergeCounter" int4 NOT NULL DEFAULT 1,
    "COGCategoryId" int4 NULL DEFAULT 0,
    "TransactionDate" timestamp(0) NOT NULL,
    "CreatedAt" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "IndirectCostId" int4 NULL DEFAULT 0,
    "IndirectCostTypeId" int4 NULL DEFAULT 0,
    xmin_copy varchar(255) NULL,
    CONSTRAINT transaction_buffer_pkey PRIMARY KEY (id)
);
CREATE INDEX "idx-transaction_buffer-event_period_id" ON customer_00000.transaction_buffer USING btree ("EventPeriodId");
CREATE INDEX "idx-transaction_buffer-posted_date" ON customer_00000.transaction_buffer USING btree ("PostedDate");
CREATE INDEX "idx-transaction_buffer-xmin_copy" ON customer_00000.transaction_buffer USING btree (xmin_copy);

CREATE TABLE customer_00000.data_export (
    id serial4 NOT NULL,
    handler_name varchar(100) NOT NULL,
    status varchar(50) NOT NULL DEFAULT 'new'::character varying,
    output_format varchar(50) NOT NULL DEFAULT 'csv'::character varying,
    count_parts int4 NOT NULL DEFAULT 0,
    count_all_items int4 NOT NULL DEFAULT 0,
    count_exported_items int4 NOT NULL DEFAULT 0,
    count_errors int4 NOT NULL DEFAULT 0,
    file_url varchar(500) NULL,
    "exception" text NULL,
    started_at timestamp(0) NULL,
    finished_at timestamp(0) NULL,
    language_code varchar(3) NOT NULL,
    "type" varchar(255) NOT NULL DEFAULT 'manual'::character varying,
    recurrent_data_export_id int4 NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    template_id int4 NULL,
    CONSTRAINT data_export_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-data-export_template-id" FOREIGN KEY (template_id) REFERENCES customer_00000.data_export_template(id) ON DELETE SET NULL
);

CREATE TABLE customer_00000.data_export_part (
    id serial4 NOT NULL,
    data_export_id int4 NOT NULL,
    part_no int4 NOT NULL DEFAULT 0,
    status varchar(50) NOT NULL DEFAULT 'new'::character varying,
    "limit" int4 NOT NULL DEFAULT 0,
    "offset" int4 NOT NULL DEFAULT 0,
    count_all_items int4 NOT NULL DEFAULT 0,
    count_exported_items int4 NOT NULL DEFAULT 0,
    count_errors int4 NOT NULL DEFAULT 0,
    file_url varchar(500) NULL,
    "exception" text NULL,
    memory_usage numeric(11, 2) NOT NULL DEFAULT 0,
    peak_memory_usage numeric(11, 2) NOT NULL DEFAULT 0,
    duration_ms numeric(11, 2) NOT NULL DEFAULT 0,
    started_at timestamp(0) NULL,
    finished_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT data_export_part_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-data_export_part-data_export_id" FOREIGN KEY (data_export_id) REFERENCES customer_00000.data_export(id) ON DELETE CASCADE
);

CREATE TABLE customer_00000.data_import_bulk_part (
    id serial4 NOT NULL,
    data_import_id int4 NOT NULL,
    status varchar(15) NOT NULL DEFAULT 'new'::character varying,
    start_id int4 NOT NULL,
    finish_id int4 NOT NULL,
    part_no int4 NOT NULL DEFAULT 0,
    last_processed_id int4 NOT NULL DEFAULT 0,
    count_all_items int4 NOT NULL DEFAULT 0,
    count_imported_items int4 NOT NULL DEFAULT 0,
    count_errors int4 NOT NULL DEFAULT 0,
    "exception" text NULL,
    errors jsonb NULL,
    finished_at timestamp(0) NULL,
    started_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT data_import_bulk_part_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-data_import_bulk_part-data_import_id" FOREIGN KEY (data_import_id) REFERENCES customer_00000.data_import(id) ON DELETE CASCADE
);
CREATE INDEX "idx-data_import_bulk_part-data_import_id" ON customer_00000.data_import_bulk_part USING btree (data_import_id);

CREATE TABLE customer_00000.data_import_part (
    id serial4 NOT NULL,
    data_import_id int4 NOT NULL,
    part_no int4 NOT NULL DEFAULT 0,
    "offset" int4 NOT NULL DEFAULT 0,
    status varchar(15) NOT NULL DEFAULT 'new'::character varying,
    count_parts int4 NOT NULL DEFAULT 0,
    count_all_items int4 NOT NULL DEFAULT 0,
    count_imported_items int4 NOT NULL DEFAULT 0,
    count_errors int4 NOT NULL DEFAULT 0,
    file_url varchar(500) NULL,
    "exception" text NULL,
    errors jsonb NULL,
    language_code varchar(2) NOT NULL DEFAULT 'EN'::character varying,
    data_start_line_number int4 NOT NULL DEFAULT 2,
    "type" varchar(255) NOT NULL DEFAULT 'manual'::character varying,
    finished_at timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at timestamp(0) NULL,
    CONSTRAINT data_import_part_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-data_import_part-data_import_id" FOREIGN KEY (data_import_id) REFERENCES customer_00000.data_import(id) ON DELETE CASCADE
);

CREATE TABLE customer_00000.indirect_cost (
    id serial4 NOT NULL,
    indirect_cost_type_id int4 NOT NULL,
    product_id int4 NULL,
    amount numeric(15, 2) NOT NULL,
    currency_id varchar(255) NOT NULL,
    cron_expr varchar(255) NULL,
    max_iterations int4 NULL,
    date_start timestamp(0) NULL,
    date_end timestamp(0) NULL,
    last_apply_date timestamp(0) NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    seller_id varchar(255) NULL,
    marketplace_id varchar(255) NULL,
    CONSTRAINT indirect_cost_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-indirect_cost-indirect_cost_type_id" FOREIGN KEY (indirect_cost_type_id) REFERENCES customer_00000.indirect_cost_type(id) ON DELETE RESTRICT,
    CONSTRAINT "fk-indirect_cost-product_id" FOREIGN KEY (product_id) REFERENCES customer_00000.product(id) ON DELETE CASCADE
);

CREATE TABLE customer_00000.product_cost_item (
    id serial4 NOT NULL,
    product_cost_period_id int4 NOT NULL,
    product_cost_category_id int4 NOT NULL,
    amount_total numeric(15, 2) NOT NULL DEFAULT 0,
    currency_id varchar(255) NOT NULL,
    amount_per_unit numeric(15, 5) NOT NULL DEFAULT 0,
    units int4 NOT NULL DEFAULT 1,
    note text NULL,
    created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status varchar(255) NULL,
    count_affected_transactions int4 NULL,
    marketplace_currency_id varchar(255) NULL,
    marketplace_currency_rate float8 NOT NULL DEFAULT 1,
    marketplace_amount_per_unit numeric(15, 5) NOT NULL DEFAULT 0,
    is_currency_rate_recalculated bool NOT NULL DEFAULT true,
    CONSTRAINT product_cost_item_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-product_cost_item-product_cost_category_id" FOREIGN KEY (product_cost_category_id) REFERENCES customer_00000.product_cost_category(id) ON DELETE CASCADE
);
CREATE INDEX "idx-is_currency_rate_recalculated" ON customer_00000.product_cost_item USING btree (is_currency_rate_recalculated);
CREATE INDEX "idx-product_cost_item-created_at" ON customer_00000.product_cost_item USING btree (created_at);
CREATE INDEX "idx-product_cost_period_id" ON customer_00000.product_cost_item USING btree (product_cost_period_id);

CREATE TABLE customer_00000.product_tag (
    id serial4 NOT NULL,
    product_id int4 NOT NULL,
    tag_id int4 NOT NULL,
    CONSTRAINT product_tag_pkey PRIMARY KEY (id),
    CONSTRAINT "fk-product_tag-product_id" FOREIGN KEY (product_id) REFERENCES customer_00000.product(id) ON DELETE CASCADE,
    CONSTRAINT "fk-product_tag-tag_id" FOREIGN KEY (tag_id) REFERENCES customer_00000.tag(id) ON DELETE CASCADE
);
CREATE INDEX "idx-product_id" ON customer_00000.product_tag USING btree (product_id);
CREATE INDEX "idx-tag_id" ON customer_00000.product_tag USING btree (tag_id);

CREATE OR REPLACE VIEW customer_00000.sales_category_extended_view
AS SELECT sc.id,
          sc.name,
          sc.is_visible,
          sc.depth,
          sc.sort_order,
          sc.path,
          false AS is_manual
FROM public.sales_category sc
UNION
SELECT concat('internal_cog_', pcc.id) AS id,
      pcc.name,
      true AS is_visible,
      sc.depth + 1 AS depth,
      sc.sort_order + pcc.id AS sort_order,
      concat(sc.path, '|internal_cog_', pcc.id) AS path,
      true AS is_manual
FROM customer_00000.product_cost_category pcc
LEFT JOIN public.sales_category sc ON sc.id::text = pcc.sales_category_id::text
UNION
SELECT concat('internal_cog_', pcc.id, '_refund') AS id,
       pcc.name,
       true AS is_visible,
       sc.depth + 1 AS depth,
       sc.sort_order + pcc.id AS sort_order,
       concat(sc.path, '|internal_cog_', pcc.id, '_refund') AS path,
       true AS is_manual
FROM customer_00000.product_cost_category pcc
LEFT JOIN public.sales_category sc ON sc.id::text =
CASE
    WHEN pcc.sales_category_id::text = 'expenses_taxes'::text THEN 'revenue_vat'::text
    WHEN pcc.sales_category_id::text = 'cost_of_goods'::text THEN 'revenue_cost_of_goods'::text
    WHEN pcc.sales_category_id::text = 'shipping_costs'::text THEN 'revenue_shipping_costs'::text
    WHEN pcc.sales_category_id::text = 'other_fees'::text THEN 'revenue_other_fees'::text
    ELSE NULL::text
END
UNION
SELECT concat('internal_indirect_cost_', ict.id) AS id,
       ict.name,
       true AS is_visible,
       sc.depth + 1 AS depth,
       sc.sort_order + ict.id AS sort_order,
       concat(sc.path, '|internal_indirect_cost_', ict.id) AS path,
       true AS is_manual
FROM customer_00000.indirect_cost_type ict
LEFT JOIN public.sales_category sc ON sc.id::text = 'indirect_costs'::text;
