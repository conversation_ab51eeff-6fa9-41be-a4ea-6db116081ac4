<?php
namespace console\migrations\customer;

use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use yii\db\Migration;

class m250116_094123_add_foreign_key_for_product_cost_period extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        if (\Yii::$app->db->schema->getTableSchema(ProductCostItem::tableName(), true) !== null) {
            ProductCostItem::deleteAll([
                'NOT IN',
                'product_cost_period_id',
                ProductCostPeriod::find()->select('id')
            ]);

            $this->addForeignKey(
                'fk-product_cost_item-product_cost_period_id',
                'product_cost_item',
                'product_cost_period_id',
                'product_cost_period',
                'id',
                'CASCADE'
            );
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-product_cost_item-product_cost_period_id', 'product_cost_item');
    }
}
