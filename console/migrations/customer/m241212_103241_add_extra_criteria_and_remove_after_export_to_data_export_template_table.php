<?php

namespace console\migrations\customer;

use yii\db\Migration;

/**
 * Class m241212_103241_add_extra_criteria_and_remove_after_export_to_data_export_template_table
 */
class m241212_103241_add_extra_criteria_and_remove_after_export_to_data_export_template_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('data_export_template', 'extra_criteria', $this->json());
        $this->addColumn('data_export_template', 'remove_after_export', $this->boolean()->notNull()->defaultValue(false));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('data_export_template', 'extra_criteria');
        $this->dropColumn('data_export_template', 'remove_after_export');
    }
}
