<?php

namespace console\migrations\customer;

use common\models\customer\DataExport;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use common\models\customer\SalesCategoryExtendedView;
use common\models\SalesCategory;
use yii\db\Expression;
use yii\db\Migration;

/**
 * Class m211231_105048_product_costs_table
 */
class m211231_105048_product_costs_table extends Migration
{

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = file_get_contents(__DIR__ . '/init_customer.sql');
        $sql = str_replace('customer_00000', \Yii::$app->dbManager->getSchemaName('customer'), $sql);
        $this->execute($sql);

        // predefined indirect_cost_type
        $this->insert('indirect_cost_type', [
            'name' => 'Office rent',
            'is_predefined' => true
        ]);
        $this->insert('indirect_cost_type', [
            'name' => 'Employee salary',
            'is_predefined' => true
        ]);
        $this->insert('indirect_cost_type', [
            'name' => 'Insurances',
            'is_predefined' => true
        ]);
        $this->insert('indirect_cost_type', [
            'name' => 'Software',
            'is_predefined' => true
        ]);
        $this->insert('indirect_cost_type', [
            'name' => 'Tax adviser',
            'is_predefined' => true
        ]);

        $this->db->createCommand()->batchInsert(\Yii::$app->dbManager->getSchemaName('customer') . '.tag_color', ['tag_color', 'type'], [
            ['#eb2f96', 'DEFAULT'],
            ['#eb5757', 'DEFAULT'],
            ['#fa541c', 'DEFAULT'],
            ['#ea7e00', 'DEFAULT'],
            ['#f8a500', 'DEFAULT'],
            ['#60b400', 'DEFAULT'],
            ['#04d758', 'DEFAULT'],
            ['#13c2c2', 'DEFAULT'],
            ['#1890ff', 'DEFAULT'],
            ['#2f54eb', 'DEFAULT'],
            ['#722ed1', 'DEFAULT'],
            ['#666666', 'DEFAULT'],
        ])->execute();
    }


    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
    }
}
