<?php

namespace console\migrations\customer;

use yii\db\Expression;
use yii\db\Migration;

class m250416_163300_add_is_enabled_sync_with_global_marketplace_to_product extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('product', 'is_enabled_sync_with_global_marketplace', $this->boolean()->notNull()->defaultValue(true));
        $this->addColumn('product', 'sync_with_global_marketplace_enabled_at', $this->dateTime()->defaultValue(new Expression('CURRENT_TIMESTAMP')));
        $this->addColumn('product', 'global_marketplace_sync_version', $this->integer()->notNull()->defaultValue(0));
        $this->addColumn('product', 'synced_with_marketplace_at', $this->dateTime());

        $this->createIndex(
            'idx-product_is_enabled_sync_with_global_marketplace-global_marketplace_sync_version',
            'product',
            ['is_enabled_sync_with_global_marketplace', 'global_marketplace_sync_version']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('product', 'is_enabled_sync_with_global_marketplace');
        $this->dropColumn('product', 'sync_with_global_marketplace_enabled_at');
        $this->dropColumn('product', 'synced_with_marketplace_at');
        $this->dropColumn('product', 'global_marketplace_sync_version');
    }
}