<?php

namespace console\migrations\customer;

use yii\db\Migration;

/**
 * Class m250110_173241_update_data_completeness_count_is_null
 */
class m250110_173241_update_data_completeness_count_is_null extends Migration
{
    public function safeUp()
    {
        $this->alterColumn('data_completeness', 'count_all', $this->integer()->null());
        $this->alterColumn('data_completeness', 'count_unfilled', $this->integer()->null());
    }

    public function safeDown()
    {
        $this->alterColumn('data_completeness', 'count_all', $this->integer()->notNull()->defaultValue(0));
        $this->alterColumn('data_completeness', 'count_unfilled', $this->integer()->notNull()->defaultValue(0));
    }
}
