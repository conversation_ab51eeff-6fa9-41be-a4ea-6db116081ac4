<?php

namespace console\migrations\customer;

use yii\db\Migration;

/**
 * Adds fnsku column to product table
 */
class m250428_170000_add_product_fnsku_column extends Migration
{

    public function safeUp()
    {
        $this->addColumn('product', 'fnsku', $this->string());

        $this->createIndex(
            'idx-product-fnsku',
            'product',
            'fnsku'
        );
    }

    public function safeDown()
    {
        $this->dropIndex(
            'idx-product-fnsku',
            'product'
        );

        $this->dropColumn('product', 'fnsku');
    }
}
