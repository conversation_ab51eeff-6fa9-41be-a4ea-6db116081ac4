<?php

namespace console\migrations\customer;

use yii\db\Migration;

/**
 * Class m250618_220000_modify_fba_storage_fee_primary_index
 * 
 * This migration modifies the primary unique index on the fba_storage_fee table
 * to use asin instead of fnsku in the constraint.
 */
class m250618_220000_modify_fba_storage_fee_primary_index extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->dropIndex('idx-fba_storage_fee-primary', 'fba_storage_fee');

        $this->createIndex(
            'idx-fba_storage_fee-primary',
            'fba_storage_fee',
            ['marketplace_id', 'seller_id', 'asin', 'date', 'report_type'],
            true
        );

        $this->dropIndex('idx-fba_storage_fee_history-primary', 'fba_storage_fee_history');

        $this->createIndex(
            'idx-fba_storage_fee_history-primary',
            'fba_storage_fee_history',
            ['marketplace_id', 'seller_id', 'asin', 'date', 'report_type'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-fba_storage_fee-primary', 'fba_storage_fee');

        $this->createIndex(
            'idx-fba_storage_fee-primary',
            'fba_storage_fee',
            ['marketplace_id', 'seller_id', 'date', 'report_type', 'fnsku'],
            true
        );

        $this->dropIndex('idx-fba_storage_fee_history-primary', 'fba_storage_fee_history');

        $this->createIndex(
            'idx-fba_storage_fee_history-primary',
            'fba_storage_fee_history',
            ['marketplace_id', 'seller_id', 'date', 'report_type', 'fnsku'],
            true
        );
    }
}
