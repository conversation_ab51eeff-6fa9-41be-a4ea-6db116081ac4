<?php

namespace console\migrations\customer;

use yii\db\Migration;

class m250425_170000_create_fba_storage_fee_tables extends Migration
{

    public function safeUp()
    {
        $this->createTable('fba_storage_fee', [
            'id' => $this->primaryKey(),
            'marketplace_id' => $this->string()->notNull(),
            'seller_id' => $this->string()->notNull(),
            'sku' => $this->string(),
            'fnsku' => $this->string()->notNull(),
            'asin' => $this->string(),
            'currency_code' => $this->string(3),
            'date' => $this->date()->notNull(),
            'amount' => $this->decimal(10, 2),
            'report_type' => $this->string()->notNull(),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('NOW()'),
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        $this->createIndex(
            'idx-fba_storage_fee-primary',
            'fba_storage_fee',
            ['marketplace_id', 'seller_id', 'date', 'report_type', 'fnsku'],
            true
        );

        $this->createIndex('idx-fba_storage_fee-sku', 'fba_storage_fee', 'sku');
        $this->createIndex('idx-fba_storage_fee-asin', 'fba_storage_fee', 'asin');
        $this->createIndex('idx-fba_storage_fee-date', 'fba_storage_fee', 'date');

        $this->createTable('fba_storage_fee_history', [
            'id' => $this->primaryKey(),
            'marketplace_id' => $this->string()->notNull(),
            'seller_id' => $this->string()->notNull(),
            'sku' => $this->string(),
            'fnsku' => $this->string()->notNull(),
            'asin' => $this->string(),
            'date' => $this->date()->notNull(),
            'amount' => $this->decimal(10, 2),
            'diff_amount' => $this->decimal(10, 2)->null(),
            'currency_code' => $this->string(3),
            'report_type' => $this->string()->notNull(),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('NOW()'),
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        $this->createIndex(
            'idx-fba_storage_fee_history-primary',
            'fba_storage_fee_history',
            ['marketplace_id', 'seller_id', 'date', 'report_type', 'fnsku'],
            true
        );

        $this->createIndex('idx-fba_storage_fee_history-sku', 'fba_storage_fee_history', 'sku');
        $this->createIndex('idx-fba_storage_fee_history-asin', 'fba_storage_fee_history', 'asin');
        $this->createIndex('idx-fba_storage_fee_history-date', 'fba_storage_fee_history', 'date');

        $this->addColumn('fba_storage_fee', 'status_moved_to_clickhouse', $this->string()->notNull()->defaultValue('created'));
        $this->addColumn('fba_storage_fee', 'moved_to_clickhouse_at', $this->timestamp()->null());

        $this->createIndex('idx-fba_storage_fee-status_moved_to_clickhouse', 'fba_storage_fee', 'status_moved_to_clickhouse');
        $this->createIndex('idx-fba_storage_fee-moved_to_clickhouse_at', 'fba_storage_fee', 'moved_to_clickhouse_at');

        $this->addColumn('fba_storage_fee_history', 'status_moved_to_clickhouse', $this->string()->notNull()->defaultValue('created'));
        $this->addColumn('fba_storage_fee_history', 'moved_to_clickhouse_at', $this->timestamp()->null());

        $this->createIndex('idx-fba_storage_fee_history-status_moved_to_clickhouse', 'fba_storage_fee_history', 'status_moved_to_clickhouse');
        $this->createIndex('idx-fba_storage_fee_history-moved_to_clickhouse_at', 'fba_storage_fee_history', 'moved_to_clickhouse_at');
    }

    public function safeDown()
    {
        $this->dropTable('fba_storage_fee_history');
        $this->dropTable('fba_storage_fee');
    }
}
