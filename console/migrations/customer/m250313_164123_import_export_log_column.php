<?php
namespace console\migrations\customer;

use yii\db\Migration;

class m250313_164123_import_export_log_column extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('data_import', 'log', $this->text());
        $this->addColumn('data_import_part', 'log', $this->text());
        $this->addColumn('data_import_bulk_part', 'log', $this->text());
        $this->addColumn('data_export', 'log', $this->text());
        $this->addColumn('data_export_part', 'log', $this->text());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('data_import', 'log');
        $this->dropColumn('data_import_part', 'log');
        $this->dropColumn('data_import_bulk_part', 'log');
        $this->dropColumn('data_import', 'log');
        $this->dropColumn('data_export_part', 'log');
    }
}
