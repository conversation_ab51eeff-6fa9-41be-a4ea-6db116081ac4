<?php
namespace console\migrations\customer;

use yii\db\Migration;

class m250403_183841_add_index_for_product_cost_period_deduplication extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            'idx_product_cost_period_deduplication',
            'product_cost_period',
            ['marketplace_id', 'seller_id', 'seller_sku', 'sales_category_id']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx_product_cost_period_deduplication', 'product_cost_period');
    }
}
