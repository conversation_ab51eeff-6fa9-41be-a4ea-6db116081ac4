<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\fbaStorageFee\consumers\FbaStorageFeeTransactionConsumer;
use common\components\rabbitmq\message\MessageInterface;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\FbaStorageFee;
use common\models\customer\FbaStorageFeeHistory;
use common\models\Seller;
use yii\console\ExitCode;
use yii\db\Exception;
use yii\helpers\BaseConsole;

class FbaStorageFeeController extends BaseController
{
    protected MessagesSender $messagesSender;

    public function __construct($id, $module, $config = [])
    {
        $this->messagesSender = new MessagesSender();
        parent::__construct($id, $module, $config);
    }

    public function actionCreateTransactions($sellerId): int
    {
        $seller = Seller::findOne($sellerId);
        if (!$seller) {
            $this->stderr("Seller with ID {$sellerId} not found\n", BaseConsole::FG_RED);
            return ExitCode::DATAERR;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($seller->customer_id);
        $dbManager->setSellerId($sellerId);

        $this->stdout("Creating transactions from FBA Storage Fee data for seller {$sellerId}\n", BaseConsole::FG_GREEN);

        $fees = FbaStorageFeeHistory::find()
            ->where(['report_type' => FbaStorageFeeHistory::REPORT_TYPE_STORAGE])
            ->andWhere(['status_moved_to_clickhouse' => FbaStorageFeeHistory::STATUS_CREATED])
            ->all();
            
        $this->stdout("Found " . count($fees) . " FBA Storage Fee records\n", BaseConsole::FG_GREEN);
        
        foreach ($fees as $fee) {
            $message = [
                'seller_id' => $fee->seller_id,
                'marketplace_id' => $fee->marketplace_id,
                'sku' => $fee->sku,
                'fnsku' => $fee->fnsku,
                'asin' => $fee->asin,
                'report_type' => $fee->report_type,
                'date' => $fee->date,
                'amount' => $fee->amount,
                'diff_amount' => $fee->diff_amount,
                'currency_code' => $fee->currency_code,
                'fee_id' => $fee->id,
                'customer_id' => $seller->customer_id
            ];

            $this->stdout("Processing FBA Storage Fee: {$fee->id}, SKU: {$fee->sku}, Date: {$fee->date}\n", BaseConsole::FG_GREEN);

            $routingKey = strtolower('apply');

            $this->messagesSender->publish($message, MessageInterface::EXCHANGE_NAME_FBA_FEE_STORAGE_SYNC, $routingKey);
        }
        
        $this->stdout("Transactions created successfully\n", BaseConsole::FG_GREEN);
        return ExitCode::OK;
    }

    /**
     * @throws SellerNotFoundException
     * @throws Exception
     */
    public function actionCreateLongTermTransactions($sellerId): int
    {
        $seller = Seller::findOne($sellerId);
        if (!$seller) {
            $this->stderr("Seller with ID {$sellerId} not found\n", BaseConsole::FG_RED);
            return ExitCode::DATAERR;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($seller->customer_id);
        $dbManager->setSellerId($sellerId);

        $this->stdout("Creating transactions from FBA Long Term Storage Fee data for seller {$sellerId}\n", BaseConsole::FG_GREEN);
        
        $fees = FbaStorageFeeHistory::find()
            ->where(['report_type' => FbaStorageFeeHistory::REPORT_TYPE_LONGTERM])
            ->andWhere(['status_moved_to_clickhouse' => FbaStorageFeeHistory::STATUS_CREATED])
            ->all();
            
        $this->stdout("Found " . count($fees) . " FBA Long Term Storage Fee records\n", BaseConsole::FG_GREEN);
        
        foreach ($fees as $fee) {
            $this->stdout("Processing FBA Long Term Storage Fee: {$fee->id}, SKU: {$fee->sku}, Date: {$fee->date}\n", BaseConsole::FG_GREEN);

            $message = [
                'seller_id' => $fee->seller_id,
                'marketplace_id' => $fee->marketplace_id,
                'sku' => $fee->sku,
                'fnsku' => $fee->fnsku,
                'asin' => $fee->asin,
                'report_type' => $fee->report_type,
                'date' => $fee->date,
                'amount' => $fee->amount,
                'diff_amount' => $fee->diff_amount,
                'currency_code' => $fee->currency_code,
                'fee_id' => $fee->id,
                'customer_id' => $seller->customer_id
            ];
            $this->stdout("Processing FBA Storage Fee: {$fee->id}, SKU: {$fee->sku}, Date: {$fee->date}\n", BaseConsole::FG_GREEN);

            $routingKey = strtolower('apply');

            $this->messagesSender->publish($message, MessageInterface::EXCHANGE_NAME_FBA_FEE_STORAGE_SYNC, $routingKey);
        }
        
        $this->stdout("Transactions created successfully\n", BaseConsole::FG_GREEN);
        return ExitCode::OK;
    }
}
