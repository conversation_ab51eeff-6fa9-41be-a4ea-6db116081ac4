<?php

namespace console\controllers;

use common\components\rabbitmq\helpers\CreateUnitHelper;
use Yii;
use yii\console\Controller;

class CreateUnitsController extends Controller
{
    public function actionIndex()
    {
        $helper = new CreateUnitHelper(
            [
                'units_dir' => Yii::getAlias('@runtime/units'),
                'work_dir' => Yii::getAlias('@app'),
                'user' => 'root',
                'group' => 'root',
            ]
        );

        $helper->create();
    }
}
