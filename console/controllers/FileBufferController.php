<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\dataBuffer\buffer\BrokenAwareInterface;
use common\components\dataBuffer\buffer\BufferInterface;
use common\components\dataBuffer\exception\FlushBufferedDataException;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use common\models\CustomerProcess;
use yii\httpclient\Exception;

class FileBufferController extends BaseController
{
    use LogToConsoleTrait;

    protected ?int $customerProcessId = null;

    /**
     * Checking buffers interval,
     */
    protected const ITERATION_INTERVAL_S = 5;

    /**
     * {@inheritdoc}
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'customerProcessId'
        ]);
    }

    public function actionForceRemove(int $customerId)
    {
        $customerProcess = null;

        if (!empty($this->customerProcessId)) {
            $customerProcess = CustomerProcess::findOne($this->customerProcessId);
        }

        $dirNames = [
            'csv_buffers',
            'csv_buffers_tmp',
            'csv_buffers_broken',
            'csv_buffers_ready'
        ];

        try {
            foreach ($dirNames as $dirName) {
                $command = sprintf(
                    'find %s -type d -name "*customer_%d*" -exec rm -rf {} ";"',
                    \Yii::getAlias('@runtime') . "/{$dirName}",
                    $customerId
                );
                exec($command);
            }

            if (!empty($customerProcess)) {
                $customerProcess->incrementSuccessEvents();
            }
        } catch (\Throwable $e) {
            if (!empty($customerProcess)) {
                $customerProcess->incrementFailedEvents();
            }

            $this->error($e);
        }
    }

    /**
     * Flushes all (by conditions) collected clickhouse buffers
     */
    public function actionFlushAllIfNeed(
        int $checkInterval = self::ITERATION_INTERVAL_S,
        string $dirName = 'csv_buffers',
        int $customerIdFrom = null,
        int $customerIdTo = null
    ) {
        $processKey = implode('_', [
            $this->route,
            $dirName,
            $customerIdFrom,
            $customerIdTo
        ]);
        /** @var ProcessManager $processManager */
        $processManager = \Yii::$app->processManager;

        while (true) {
            $this->info(str_repeat('-', 50));

            $bufferInstances = array_merge(
                glob(\Yii::getAlias('@runtime') . "/{$dirName}/*/instance"),
                glob(\Yii::getAlias('@runtime') . "/{$dirName}/*/*/instance"),
                glob(\Yii::getAlias('@runtime') . "/{$dirName}/*/*/*/instance")
            );

            $totalBuffersFound = count($bufferInstances);
            $buffersSkipped = 0;
            $buffersProcessed = 0;
            $buffersFlushed = 0;
            $errors = 0;
            $totalRecordsFlushed = 0;

            foreach ($bufferInstances as $bufferInstance) {
                try {
                    if (!empty($customerIdFrom) || !empty($customerId)) {
                        preg_match('/customer_([0-9]+)_/', $bufferInstance, $matches);
                        $customerId = $matches[1];

                        if (!empty($customerIdFrom) && $customerId < $customerIdFrom) {
                            $buffersSkipped++;
                            continue;
                        }

                        if (!empty($customerIdTo) && $customerId >= $customerIdTo) {
                            $buffersSkipped++;
                            continue;
                        }
                    }

                    $this->info("Start file get contents. buffersSkipped = " . $buffersSkipped);

                    $contents = file_get_contents($bufferInstance);

                    $this->info("Start unserialize");

                    /** @var BufferInterface|BrokenAwareInterface $buffer */
                    $buffer = unserialize($contents);
                    $recordsCount = $buffer->getCount();

                    $this->info("Finish unserialize");

                    if (empty($buffer)) {
                        throw new Exception("Unable to deserialize buffer $bufferInstance");
                    }

                    $buffer->setIsFlushEnabled(true);
                    $this->info("Start flushIfNeed");
                    $processManager->register($processKey);
                    $isFlushed = $buffer->flushIfNeed();
                    $processManager->release($processKey);
                    $this->info("Buffer $bufferInstance" . ($isFlushed
                        ? " was flushed ($recordsCount records)"
                        : ' was NOT flushed (not a time)'
                    ));

                    if ($isFlushed) {
                        $totalRecordsFlushed += $recordsCount;
                        $buffersFlushed++;
                    }
                    $buffersProcessed++;
                } catch (FlushBufferedDataException $e) {
                    $processManager->register($processKey);
                    $buffer->markAsBroken($e->getMessage());
                    $processManager->release($processKey);
                    $this->error($e);
                    $errors++;
                } catch (\Throwable $e) {
                    $this->error($e);
                    $errors++;
                }
            }

            $this->info('Finished. Stats:');
            $this->info([
                'totalBuffersFound' => $totalBuffersFound,
                'buffersSkipped' => $buffersSkipped,
                'buffersProcessed' => $buffersProcessed,
                'buffersFlushed' => $buffersFlushed,
                'totalRecordsFlushed' => $totalRecordsFlushed,
                'buffersFailed' => $errors
            ]);

            $this->info("Waiting {$checkInterval} seconds before next check");
            sleep($checkInterval);
        }
    }
}
