<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\core\db\DbPostfixManager;
use common\components\db\ClickhouseDbHelper;
use common\components\db\InitRepricerEventDb;
use common\components\demo\LoadRepricerEvents;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\RepricerEvent;
use common\models\customer\RepricerEventBuffer;
use common\models\customer\RepricerEventStorage;
use common\models\Seller;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

class RepricerEventController extends BaseController
{
    use LogToConsoleTrait;

    /**
     * Synchronizes repricer event data from a buffer table to ClickHouse for specified customer schemas.
     *
     * @param int|null $customerIdFrom The starting customer ID for schema iteration. Optional.
     * @param int|null $customerIdTo The ending customer ID for schema iteration. Optional.
     *
     * @return void Outputs the status of synchronization and inserts.
     *
     * @throws \Exception If there is an issue with the database connection or data handling.
     */
    public function actionSyncBuffer(?int $customerIdFrom = null, ?int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateSchemas(DbManager::DB_PREFIX_REPRICER_EVENT, $customerIdFrom, $customerIdTo, 0, HelperFactory::TYPE_REPRICER_EVENT_POSTGRESS) as $schemaInfo) {

            if (!isset($schemaInfo['schema'])) {
                continue;
            }
            try {
                $this->info(sprintf("Sync repricer events for schema %s", $schemaInfo['schema']));

                $this->transferDataToClickhouse(RepricerEventBuffer::class, $schemaInfo['schema']);
                $this->saveBufferToStorage();
                $this->cleanBuffer();

            } catch (\Exception $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    /**
     * Restores repricer event data from storage to ClickHouse for specified customer schemas.
     *
     * @param int|null $customerIdFrom The starting customer ID for schema iteration. Optional.
     * @param int|null $customerIdTo The ending customer ID for schema iteration. Optional.
     *
     * @return void Outputs the status of restoration and data inserts.
     *
     * @throws \Exception If there is an issue with the database connection or data handling.
     */
    public function actionSyncStorage(?int $customerIdFrom = null, ?int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateSchemas(DbManager::DB_PREFIX_REPRICER_EVENT, $customerIdFrom, $customerIdTo, 0, HelperFactory::TYPE_REPRICER_EVENT_POSTGRESS) as $schemaInfo) {
            if (!isset($schemaInfo['schema'])) {
                continue;
            }
            try {
                $this->info(sprintf("Restore repricer events from storage for schema %s", $schemaInfo['schema']));

                $clickHouseDb = $this->dbManager->getClickhouseCustomerDb();
                $clickHouseDb->createCommand("TRUNCATE TABLE " . RepricerEvent::tableName())->execute();
                $this->transferDataToClickhouse(RepricerEventStorage::class, $schemaInfo['schema']);
            } catch (\Exception $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    private function transferDataToClickhouse(string $fromEntity, string $schema)
    {
        $dbHelper = new ClickhouseDbHelper();
        $clichouseDb = $this->dbManager->getClickhouseCustomerDb();
        $repricerEventTable = RepricerEvent::tableName();
        $fromTable = Inflector::camel2id(StringHelper::basename($fromEntity), '_');
        $sqlFunction = $dbHelper->getPostgresqlFunction($this->dbManager->getRepricerEventPostgressConfig(), $fromTable, $schema);

        $sql = "
                INSERT INTO {$repricerEventTable} 
                SETTINGS max_partitions_per_insert_block = 10000
                SELECT day, product_id, sku, marketplace_id, seller_id, amount, offer_type, NOW() as updated_at
                FROM {$sqlFunction}
            ";
        $clichouseDb->createCommand($sql)->execute();
    }

    private function saveBufferToStorage(): void
    {
        $repricerEventStorageTable = RepricerEventStorage::tableName();
        $repricerEventBufferTable = RepricerEventBuffer::tableName();
        $this->dbManager
            ->getRepricerEventDb()
            ->createCommand("
            INSERT INTO {$repricerEventStorageTable} (day, product_id, sku, marketplace_id, seller_id, amount, offer_type)
            SELECT day, product_id, sku, marketplace_id, seller_id, amount, offer_type
            FROM {$repricerEventBufferTable}
            ON CONFLICT (day, product_id, offer_type)
            DO UPDATE SET amount = {$repricerEventStorageTable}.amount + EXCLUDED.amount
            ")
            ->execute();
    }

    private function cleanBuffer(): void
    {
        $this->dbManager
            ->getRepricerEventDb()
            ->createCommand()
            ->delete(
                RepricerEventBuffer::tableName()
            )->execute();
    }

    /**
     * Initializes the repricer event database for active customers within a specified range.
     *
     * @param int|null $customerIdFrom The starting customer ID for processing. Optional.
     * @param int|null $customerIdTo The ending customer ID for processing. Optional.
     *
     * @return void Outputs the status of database initialization for each customer and logs any errors.
     *
     * @throws \Exception If errors occur during the initialization process for any customer.
     */
    public function actionInitRepricerEventDb(?int $customerIdFrom = null, ?int $customerIdTo = null)
    {
        $countProcessed = 0;

        foreach ($this->dbManager->iterateSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->info(sprintf(
                    "%s - Processing customer %s",
                    $countProcessed + 1,
                    $schemaInfo['customer_id']
                ));
                $this->dbManager->setCustomerId($schemaInfo['customer_id']);
                $dbPostfixManager = new DbPostfixManager(null, $schemaInfo['customer_id']);
                $customerRelatedDbPostfix = $dbPostfixManager->getDbPostfixForCustomerRelatedDbs();

                (new InitRepricerEventDb($customerRelatedDbPostfix, $schemaInfo['customer_id']))->create();
            } catch (\Throwable $e) {
                $this->error($e);
            }
            $countProcessed++;
        }

    }

    public function actionGenerateDemoData(?int $customerIdFrom = null, ?int $customerIdTo = null): void
    {
        ini_set("memory_limit", "512M");
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()
            ->select('customer_id')
            ->distinct()
            ->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_demo' => true])->orderBy('customer_id');
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }
        $customerIds = $query->column();
        foreach ($customerIds as $customerId) {
            try {
                $this->dbManager->setCustomerId($customerId);
                (new LoadRepricerEvents())->loadData('- 1 hour', 1);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionTruncateAllTablesForCustomer(?int $customerIdFrom = null, ?int $customerIdTo = null): void
    {
        $countProcessed = 0;

        if (!$this->confirm(sprintf(
            "Are you sure you want to truncate tables from customerId %s to %s?",
        $customerIdFrom ?? '0',
        $customerIdTo ?? 'the end'
        ))) {
            $this->info('Aborted.');
            return;
        }
        foreach ($this->dbManager->iterateSchemas(DbManager::DB_PREFIX_REPRICER_EVENT, $customerIdFrom, $customerIdTo, 0, HelperFactory::TYPE_REPRICER_EVENT_POSTGRESS) as $schemaInfo) {
            try {
                $this->info(sprintf(
                    "%s - Truncating postgress repricer event tables for customer %s",
                    $countProcessed + 1,
                    $schemaInfo['customer_id']
                ));
                $this->dbManager->setCustomerId($schemaInfo['customer_id']);
                $clickHouseDb = $this->dbManager->getClickhouseCustomerDb();
                $clickHouseDb->createCommand("TRUNCATE TABLE " . RepricerEvent::tableName())->execute();

                $this->dbManager
                    ->getRepricerEventDb()
                    ->createCommand("TRUNCATE TABLE " . RepricerEventBuffer::tableName())
                    ->execute();

                $this->dbManager
                    ->getRepricerEventDb()
                    ->createCommand("TRUNCATE TABLE " . RepricerEventStorage::tableName())
                    ->execute();
            } catch (\Exception $e) {
                $this->error($e);
            }
        }
    }
}
