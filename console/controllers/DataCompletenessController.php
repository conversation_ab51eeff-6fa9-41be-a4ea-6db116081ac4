<?php

namespace console\controllers;

use common\components\LogToConsoleTrait;
use common\models\DataCompletenessFactor;

class DataCompletenessController extends BaseController
{
    use LogToConsoleTrait;

    public function actionCheck(?int $customerIdFrom = null, ?int $customerIdTo = null, string $factorId = null)
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $checker = new \common\components\dataCompleteness\Checker();
            $factorsQuery = DataCompletenessFactor::find();
            if (!empty($factorId)) {
                $factorsQuery->andWhere(['id' => $factorId]);
            }
            /** @var DataCompletenessFactor[] $factors */
            $factors = $factorsQuery->all();

            foreach ($this->dbManager->iterateActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schema) {
                try {
                    $this->info("Started calculation of data completeness for customer " . $this->dbManager->getCustomerId());

                    foreach ($factors as $factor) {
                        $checker->check($factor->id);
                    }

                    $this->info("Finished calculation of data completeness for customer " . $this->dbManager->getCustomerId());
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }
}
