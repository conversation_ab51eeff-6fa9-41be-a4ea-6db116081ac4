<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\models\Command;
use yii\console\Controller;
use console\components\CommandErrorHandler;
use yii\mutex\Mutex;

class BaseController extends Controller
{
    /** @var int|null */
    public $commandId;
    protected ?int $customerProcessId = null;

    /** @var Command|null */
    protected $commandModel;

    protected DbManager $dbManager;

    protected Mutex $mutex;

    public function __construct($id, $module, $config = [])
    {
        $this->mutex = \Yii::$app->mutex;
        $this->dbManager = \Yii::$app->dbManager;
        parent::__construct($id, $module, $config);
    }

    /**
     * @param $actionID
     * @return string[]
     */
    public function options($actionID)
    {
        return ['commandId', 'customerProcessId'];
    }

    public function lock()
    {
        $lockKey = implode('_', $_SERVER['argv']);
        if (false === $this->mutex->acquire($lockKey)) {
            $this->info('Command is already running in another process');
            return false;
        }

        return true;
    }

    public function unlock()
    {
        $lockKey = implode('_', $_SERVER['argv']);
        $this->mutex->release($lockKey);
    }

    public function init()
    {
        parent::init();
        $this->registerCommandErrorHandler();
    }

    protected function registerCommandErrorHandler()
    {
        $handler = new CommandErrorHandler();
        \Yii::$app->set('errorHandler', $handler);
        $handler->register();
    }

    /**
     * @param $action
     * @return bool
     */
    public function beforeAction($action)
    {
        if ($this->commandId) {
            $this->commandModel = Command::findOne($this->commandId);
            $this->commandModel->markInProgress();
        }
        return parent::beforeAction($action);
    }

    /**
     * @param $action
     * @param $result
     * @return mixed
     */
    public function afterAction($action, $result)
    {
        $result = parent::afterAction($action, $result);

        if ($this->commandModel instanceof Command) {
            $this->commandModel->markFinished();
        }

        return $result;
    }

    /**
     * @return Command|null
     */
    public function getCommandModel()
    {
        return $this->commandModel;
    }
}
