<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\ExportManager;
use common\components\dataImportExport\import\ImportManager;
use common\components\dataImportExport\import\processor\DataImportProcessorFactory;
use common\components\dataImportExport\import\validator\ImportUrlFileStructureValidator;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\DataExport;
use common\models\customer\DataExportTemplate;
use common\models\customer\DataImport;
use common\models\customer\DataImportPart;
use common\models\DataExportRecurrent;
use common\models\DataImportRecurrent;
use common\models\Seller;
use Cron\CronExpression;
use Yii;
use yii\base\DynamicModel;
use yii\console\ExitCode;
use yii\db\Expression;
use yii\helpers\Console;
use yii\mutex\Mutex;

class DataImportExportController extends BaseController
{
    use LogToConsoleTrait;

    protected const RECURRENT_BATCH_SIZE = 500;

    protected $customerId;
    protected $dataImportRecurrentId;
    private CustomerComponent $customerComponent;

    public const ACTION_RECURRENT_IMPORT = 'recurrent-import';

    public function __construct($id, $module, $config = [])
    {
        $this->customerComponent = \Yii::$app->customerComponent;
        parent::__construct($id, $module, $config);
    }

    /**
     * @param  string $actionID
     * @return array
     */
    public function options($actionID): array
    {
        switch ($actionID) {
            case self::ACTION_RECURRENT_IMPORT:
                return ['dataImportRecurrentId', 'customerId'];
            default:
                return [];
        }
    }

    public function actionRenewTerminated(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_active' => true]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        $exportManager = new ExportManager();
        $dataImportProcessorFactory = new DataImportProcessorFactory();

        foreach ($dbManager->iterateActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                foreach ($dataImportProcessorFactory->getAllProcessors() as $importProcessor) {
                    try {
                        $importProcessor->renewStuckParts();
                        $importProcessor->enqueuePartProcessing();
                    } catch (\Throwable $e) {
                        $this->error($e);
                    }
                }

                $terminatedExports = DataExport::find()->where([
                    'AND',
                    ['in', 'status', [DataExport::STATUS_IN_PROGRESS, DataExport::STATUS_NEW]],
                    ['!=', 'type', DataExport::TYPE_BULK_EDIT],
                    ['<=', 'updated_at', (new \DateTime())->modify('-4 hour')->format('Y-m-d H:i:s')]
                ])->all();

                foreach ($terminatedExports as $terminatedExport) {
                    $exportManager->renewTerminated($terminatedExport);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionScheduleImportPartProcessing(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;
        $importManager = new ImportManager();

        foreach ($dbManager->iterateActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $importManager->enqueuePartProcessing();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionRecurrentExportAsync(): int
    {
        $currentTime = new \DateTime();
        $this->info("Auto export started");
        $autoExportQuery = DataExportRecurrent::find()->where([
            'is_enabled' => true,
        ])->orderBy('id')->batch(self::RECURRENT_BATCH_SIZE);
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        foreach ($autoExportQuery as $items) {
            /**
             * @var DataExportRecurrent[] $items
             */
            foreach ($items as $item) {
                try {
                    $cronExpr = str_replace('?', '*', (string)$item->cron_expr);
                    $cron = new CronExpression($cronExpr);

                    $activeSeller = Seller::find()
                        ->where([
                            'customer_id' => $item->customer_id,
                            'is_active' => true
                        ])
                        ->limit(1)
                        ->one();
                    $this->info("Item $item->id, customerId $item->customer_id");

                    if (empty($activeSeller)) {
                        $this->info("Customer not found or not active, item skipped");
                        continue;
                    }
                    $dbManager->setCustomerId($item->customer_id);
                    Yii::$app->language = $this->customerComponent->getLanguageCode();
                    $this->info("Cron expression: $item->cron_expr");

                    if (!$cron->isDue($currentTime, $this->customerComponent->getTimezoneName())) {
                        $this->info("Cron expression does not match current time, skipped");
                        continue;
                    }

                    $template = DataExportTemplate::findOne($item->template_id);

                    if (empty($template)) {
                        $defaultTemplateId = DataExportTemplate::find()->select('id')->where([
                            'is_default' => 't'
                        ])->scalar();
                        if (!empty($defaultTemplateId)) {
                            $item->template_id = $defaultTemplateId;
                            $item->save(false, ['template_id']);
                        }
                    }

                    $exportManager = new ExportManager();
                    $dataExport = $exportManager
                        ->export(
                            $item->handler_name,
                            $item->output_file_format,
                            $item->template_id,
                            DataExport::TYPE_AUTO
                        );

                    $dataExport->recurrent_data_export_id = $item->id;
                    $dataExport->save(false);

                    $item->invoked_at = date('Y-m-d H:i:s');
                    $item->executed_at = date('Y-m-d H:i:s');
                    $item->save(false);
                    $this->info("Auto export created, $dataExport->id");
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
        }

        $this->info("Auto export finished");

        return ExitCode::OK;
    }

    public function actionRecurrentImportAsync(): int
    {
        $currentTime = new \DateTime();
        $this->info("Auto import started");
        $query = DataImportRecurrent::find()->where([
            'is_enabled' => true,
        ])->orderBy('id')->batch(self::RECURRENT_BATCH_SIZE);
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        foreach ($query as $items) {
            /**
             * @var DataImportRecurrent[] $items
             */
            foreach ($items as $item) {
                try {
                    $this->info(str_repeat('-', 20));
                    $cronExpr = str_replace('?', '*', $item->cron_expr);
                    $cron = new CronExpression($cronExpr);

                    $activeSeller = Seller::find()
                        ->where([
                            'customer_id' => $item->customer_id,
                            'is_active' => true
                        ])
                        ->limit(1)
                        ->one();
                    $this->info("Item $item->id, customerId $item->customer_id");

                    if (empty($activeSeller)) {
                        $this->info("Customer not found or not active, item skipped");
                        continue;
                    }
                    $dbManager->setCustomerId($item->customer_id);
                    $this->info("Cron expression: $item->cron_expr");

                    if (!$cron->isDue($currentTime, $this->customerComponent->getTimezoneName())) {
                        $this->info("Cron expression does not match current time, skipped");
                        continue;
                    }
                    $item->invoked_at = date('Y-m-d H:i:s');

                    \Yii::$app->cronComponent->addCommand(
                        "data-import-export/recurrent-import --dataImportRecurrentId={$item->id} --customerId={$item->customer_id}",
                        'queueCronUrl'
                    );
                    $this->info("Recurrent import scheduled, $item->id");
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
        }

        $this->info("Auto import finished");

        return ExitCode::OK;
    }

    public function actionRecurrentImport()
    {
        $this->info("Recurrent import creation started");

        $lockName = implode('-', [$this->customerId, $this->dataImportRecurrentId]);
        /** @var Mutex $mutex */
        $mutex = Yii::$app->mutex;

        if (!$mutex->acquire($lockName)) {
            $this->info("Already running");
            return ExitCode::OK;
        }

        Yii::$app->dbManager->setCustomerId($this->customerId);
        Yii::$app->language = $this->customerComponent->getLanguageCode();
        $recurrentImport = DataImportRecurrent::findOne($this->dataImportRecurrentId);

        if (empty($recurrentImport)) {
            Yii::error("Unable to find recurrent data import by id " . json_encode([
                'id' => $this->dataImportRecurrentId,
                'customerId' => $this->customerId,
            ], JSON_THROW_ON_ERROR));
            $mutex->release($lockName);
            return ExitCode::OK;
        }

        $filename = Yii::getAlias('@console/runtime') . '/' . md5(rand() . time());
        $fp = fopen($filename, 'w+');

        $isDownloaded = DataImport::downloadFile(
            $fp,
            $recurrentImport->url,
            (bool)$recurrentImport->auth_login,
            $recurrentImport->auth_login,
            $recurrentImport->auth_password
        );

        $urlValidation = DynamicModel::validateData(
            ['url' => $recurrentImport->url, 'handler_name' => $recurrentImport->handler_name],
            [[['url'], ImportUrlFileStructureValidator::class]]
        );
        $urlValidation->validate();

        if (!$isDownloaded || $urlValidation->hasErrors()) {
            $recurrentImport->is_url_broken = true;
            $recurrentImport->save(false);
            $this->info("Failed to download file");
            $mutex->release($lockName);
            unlink($filename);

            return ExitCode::UNSPECIFIED_ERROR;
        }
        $recurrentImport->is_url_broken = false;
        $recurrentImport->save(false);

        $importManager = new ImportManager();
        $importManager->upload($filename, $recurrentImport->handler_name, DataImport::TYPE_AUTO);
        unlink($filename);
        $recurrentImport->executed_at = date('Y-m-d H:i:s');

        $this->info("Recurrent import creation finished");
        $mutex->release($lockName);

        return ExitCode::OK;
    }
}
