<?php

namespace console\controllers;

use common\components\LogToConsoleTrait;
use common\components\messenger\MessagesSender;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\db\Query;
use yii\mutex\Mutex;

class FinanceEventCategoryController extends BaseController
{
    use LogToConsoleTrait;

    public function actionNotifyUnmapped()
    {
        $lockKey = 'finance-event-category/notify-unmapped';
        if (false === $this->mutex->acquire($lockKey)) {
            $this->info('Command is already running in another process');
            return;
        }

        /** @var FinanceEventCategory[] $unmappedCategories */
        $unmappedCategories = (new Query())
            ->select('fec.id, fec.path, sc.path as sales_category_path')
            ->from(FinanceEventCategory::tableName() . ' fec')
            ->leftJoin(SalesCategory::tableName() . ' sc', 'sc.id = fec.sales_category_id')
            ->where("fec.is_unmapped_notification_sent = 'f'")
            ->andWhere("fec.sales_category_id like 'undefined%'")
            ->andWhere("fec.path not like '%" . FinanceEventCategory::PLUS_ZERO_POSTFIX . "'")
            ->all()
        ;

        $this->info(sprintf("Found %d unmapped finance event categories", count($unmappedCategories)));
        /** @var MessagesSender $messagesSender */
        $messagesSender = \Yii::$app->messagesSender;

        foreach ($unmappedCategories as $unmappedCategory) {
            $result = $messagesSender->sendUnmappedFinanceEventCategory(
                $unmappedCategory['path'],
                $unmappedCategory['sales_category_path']
            );

            if ($result->isSuccess) {
                $financeEventCategory = FinanceEventCategory::findOne([
                    'id' => $unmappedCategory['id']
                ]);
                $financeEventCategory->is_unmapped_notification_sent = true;
                $financeEventCategory->save(false);
            }
        }
        $this->info("Finished");
        $this->mutex->release($lockKey);
    }
}
