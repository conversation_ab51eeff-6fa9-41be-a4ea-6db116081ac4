<?php

namespace console\controllers;

use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\IndirectCost;
use Cron\CronExpression;
use yii\db\Expression;
use yii\helpers\Console;

class IndirectCostController extends BaseController
{
    use LogToConsoleTrait;

    protected MessagesSender $messagesSender;

    public function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config);
        $this->messagesSender = new MessagesSender();
    }

    public function actionApply(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $query = IndirectCost::find()
                    ->where([
                        'AND',
                        ['<=', 'date_start', date('Y-m-d 23:59:59')],
                        [
                            'OR',
                            ['is', 'date_end', new Expression('NULL')],
                            ['>=', 'date_end', date('Y-m-d 00:00:00')],
                        ],
                        [
                            'OR',
                            ['is', 'last_apply_date', new Expression('NULL')],
                            ['<', 'last_apply_date', date('Y-m-d 00:00:00')]
                        ]
                    ]);

                $countAll = $query->count();
                $countProcessed = 0;
                $countSkipped = 0;
                $countSentToQueue = 0;
                Console::startProgress($countProcessed, $countAll, 'Processed');

                /** @var IndirectCost[] $indirectCosts */
                foreach ($query->batch() as $indirectCosts) {
                    foreach ($indirectCosts as $indirectCost) {
                        try {
                            if (empty($indirectCost['cron_expr'])) {
                                $this->messagesSender->indirectCostChanges(
                                    $this->dbManager->getCustomerId(),
                                    $indirectCost->id,
                                    true
                                );
                                $indirectCost->last_apply_date = date('Y-m-d H:i:s');
                                $indirectCost->save(false);
                                $countProcessed++;
                                $countSentToQueue++;
                                Console::updateProgress($countProcessed, $countAll, 'Processed');
                                continue;
                            }
                            $cronExpr = str_replace('?', '*', $indirectCost->cron_expr);
                            $expression = new CronExpression($cronExpr);
                            if (!$expression->isDue(date('Y-m-d 00:00:00'))) {
                                $countProcessed++;
                                $countSkipped++;
                                Console::updateProgress($countProcessed, $countAll, 'Processed');
                                continue;
                            }

                            $this->messagesSender->indirectCostChanges(
                                $this->dbManager->getCustomerId(),
                                $indirectCost->id,
                                true
                            );
                            $indirectCost->last_apply_date = date('Y-m-d H:i:s');
                            $indirectCost->save(false);
                            $countProcessed++;
                            $countSentToQueue++;
                            Console::updateProgress($countProcessed, $countAll, 'Processed');
                        } catch (\Throwable $e) {
                            $this->info($e);
                        }
                    }
                }

                $this->info([
                    'customerId' => $this->dbManager->getCustomerId(),
                    'countAll' => $countAll,
                    'countSkipped' => $countSkipped,
                    'countSentToQueue' => $countSentToQueue
                ]);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionReCalculate(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }
                $query = IndirectCost::find();

                /** @var IndirectCost[] $indirectCosts */
                foreach ($query->batch() as $indirectCosts) {
                    foreach ($indirectCosts as $indirectCost) {
                        $this->messagesSender->indirectCostChanges(
                            $this->dbManager->getCustomerId(),
                            $indirectCost->id
                        );
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }
}
