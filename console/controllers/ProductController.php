<?php

namespace console\controllers;

use common\components\COGSync\GlobalMarketplaceService;
use common\components\customerConfig\CustomerConfig;
use common\components\COGSync\DefaultVATManager;
use common\components\featureFlag\FeatureFlagService;
use common\components\LogToConsoleTrait;
use common\components\productSync\AmountsSync;
use common\components\productSync\ProductSync;
use common\models\AmazonMarketplace;
use common\components\rabbitmq\MessagesSender;
use common\models\Command;
use common\models\Customer;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\order\AmazonOrder;
use common\models\Seller;

use yii\db\Expression;
use yii\helpers\Console;

class ProductController extends BaseController
{
    protected const PRODUCTS_BATCH_SIZE = 5000;
    protected const SELLERS_BATCH_SIZE = 500;

    use LogToConsoleTrait;

    private MessagesSender $messagesSender;

    public function __construct($id, $module, $config = [])
    {
        $this->messagesSender = new MessagesSender();

        parent::__construct($id, $module, $config);
    }

    public function actionCheckAndSetDefaultVat(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $defaultVATManager = new DefaultVATManager();
        /** @var FeatureFlagService $featureService */
        $featureService = \Yii::$app->featureFlagService;

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schema) {
            try {
                $customerId = $this->dbManager->getCustomerId();

                if (!$featureService->isEnabled(FeatureFlagService::FLAG_DEFAULT_VAT_FOR_PRODUCTS, $customerId)) {
                    continue;
                }

                $this->info(sprintf("Set default VAT for customer %d started", $customerId));

                $productsQuery = Product::find()->where([
                    'is', 'vat', new Expression('NULL')
                ])->asArray();

                $productsPerIteration = 500;
                $maxIterationsPerCustomer = 20;
                $iterationsDone = 0;

                foreach ($productsQuery->batch($productsPerIteration) as $productsWithoutVAT) {
                    $this->info([
                        'customerId' => $customerId,
                        'productsToProcess' => count($productsWithoutVAT),
                    ]);

                    if ($iterationsDone >= $maxIterationsPerCustomer) {
                        break;
                    }

                    $defaultVATManager->generateAndSaveDefaultVATForProducts($productsWithoutVAT, false);
                    $iterationsDone++;
                }
                $this->info(sprintf("Set default VAT for customer %d finished", $customerId));
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSync(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

//        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true]);
        $query = Seller::find()->where([
            'AND',
            ['is_db_created' => true],
            [
                'OR',
                ['is_analytic_active' => true],
                ['is_active' => true],
            ],
        ]);

        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }

        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        $countAll = $query->count();
        $synchronizer = new ProductSync();

        $processedCount = 0;
        Console::startProgress($processedCount, $countAll, 'Processed');

        /** @var Seller[] $sellers */
        foreach ($query->batch() as $sellers) {
            foreach ($sellers as $seller) {
                if ($seller->is_demo) {
                    continue;
                }

                try {
                    $synchronizer->sync($seller->id);
                } catch (\Throwable $e) {
                    // Table does not exist error need to track only on production
                    if ($e->getCode() === '42P01') {
                        if (YII_ENV_PROD) {
                            $this->error($e);
                        }
                    } else {
                        $this->error($e);
                    }
                }
                $processedCount++;
                Console::updateProgress($processedCount, $countAll, 'Processed');
            }
        }

        $this->unlock();
    }

    public function actionCheckSyncWithGlobalMarketplace(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        /** @var CustomerConfig $customerConfig */
        $customerConfig = \Yii::$container->get("customerConfig");
        /** @var FeatureFlagService $featureFlagsService */
        $featureFlagsService = \Yii::$app->featureFlagService;
        /** @var GlobalMarketplaceService $globalMarketplaceService */
        $globalMarketplaceService = \Yii::$container->get('globalMarketplaceService');

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('finance', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (!$featureFlagsService->isEnabled(FeatureFlagService::FLAG_PRODUCT_SYNC_WITH_GLOBAL_MARKETPLACE, $this->dbManager->getCustomerId())) {
                    continue;
                }

                $globalMarketplaceId = $globalMarketplaceService->getGlobalMarketplaceId($this->dbManager->getSellerId());

                if (empty($globalMarketplaceId)) {
                    continue;
                }

                $globalMarketplaceSyncVersion = (int)$customerConfig->get(
                    CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION,
                    0,
                    $this->dbManager->getSellerId()
                );

                // Fetching global marketplace products which contains also products that should be synced
                $productsToSyncWithGlobalMarketplace = Product::find()
                    ->from(Product::tableName() . ' p')
                    ->innerJoin(
                        Product::tableName() . ' p1',
                        "p1.marketplace_id != '{$globalMarketplaceId}' 
                        AND p1.seller_id = p.seller_id 
                        AND p1.sku = p.sku
                        AND p1.is_enabled_sync_with_global_marketplace = 't'
                        AND p1.global_marketplace_sync_version != {$globalMarketplaceSyncVersion}
                    ")
                    ->where([
                        'AND',
                        ['=', 'p.marketplace_id', $globalMarketplaceId],
                        ['=', 'p.seller_id', $this->dbManager->getSellerId()]
                    ])
                    ->asArray();

                $batchSize = 10000;
                $maxProductsToSyncPerIteration = 500000;
                $processedCount = 0;

                foreach ($productsToSyncWithGlobalMarketplace->batch($batchSize) as $products) {
                    foreach ($products as $product) {
                        $this->messagesSender->syncProductWithGlobalMarketplace(
                            $product['seller_id'],
                            $product['sku'],
                        );
                    }
                    $processedCount += $batchSize;

                    if ($processedCount >= $maxProductsToSyncPerIteration) {
                        break;
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionForceEnableSyncWithGlobalMarketplace(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('finance', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {

                Product::updateAll(
                    ['is_enabled_sync_with_global_marketplace' => true],
                    ['is_enabled_sync_with_global_marketplace' => false]
                );

            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        Command::create(
            sprintf(
                "product/check-sync-with-global-marketplace %d %d",
                $customerIdFrom,
                $customerIdTo
            ),
            1,
            true,
            true
        );

        $this->unlock();
    }

    public function actionSyncAmounts(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $amountsUpdater = new AmountsSync();
            $amountsUpdater->sync($customerIdFrom, $customerIdTo);
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->unlock();
    }

    public function actionResetCosts(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (!$this->dbManager->getCustomerId()) {
                    continue;
                }
                $this->info("Processing customer " . $this->dbManager->getCustomerId());

                Product::updateAll([
                    'vat' => null,
                    'shipping_cost' => null,
                    'other_fees' => null,
                    'buying_price' => null,
                    'source' => ProductCostCategory::SOURCE_MANUAL,
                ]);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    /**
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @throws \Throwable
     */
    public function actionSyncAll(int $customerIdFrom = null, int $customerIdTo = null)
    {
        $lockKey = implode(',', [$this->route, $customerIdFrom, $customerIdTo]);
        if (false === $this->mutex->acquire($lockKey)) {
            $this->info('Command is already running in another process');
            return;
        }

        foreach ($this->iterateProducts($customerIdFrom, $customerIdTo) as $productInfos) {
            try {
                $this->info(sprintf("Found %s products, saving them", count($productInfos)));
                $insertUpdateSql = $this
                    ->dbManager
                    ->getCustomerDb()
                    ->createCommand()
                    ->batchInsert(
                        Product::tableName(),
                        array_keys(array_values($productInfos)[0]),
                        $productInfos
                    )
                    ->getRawSql();
                $insertUpdateSql .= ' ON CONFLICT (marketplace_id, seller_id, sku) 
                DO UPDATE SET currency_code = EXCLUDED.currency_code';
                $this->dbManager->getCustomerDb()->createCommand($insertUpdateSql)->execute();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    /**
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @return \Iterator
     */
    private function iterateSellers(int $customerIdFrom = null, int $customerIdTo = null): \Iterator
    {
        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true]);

        if ($customerIdFrom)
            $query->andWhere('customer_id >= :from',[':from'=>$customerIdFrom]);

        if ($customerIdTo)
            $query->andWhere('customer_id < :to',[':to'=>$customerIdTo]);

        $query->orderBy('customer_id ASC');

        $countAll = $query->count();
        $countProcessed = 0;

        foreach ($query->batch(self::SELLERS_BATCH_SIZE) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                try {
                    $this->info(sprintf(
                        "Processing seller %s of customer %s (%s from %s sellers in total)",
                        $seller->id,
                        $seller->customer_id,
                        $countProcessed + 1,
                        $countAll,
                    ));
                    $this->dbManager->setSellerId($seller->id);
                    yield $seller;
                } catch (\Throwable $e) {
                    $this->error($e);
                }
                $countProcessed++;
            }
        }
    }

    /**
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @return \Iterator
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    private function iterateProducts(int $customerIdFrom = null, int $customerIdTo = null): \Iterator
    {
        /** @var Connection $repricerMainDb */
        $repricerMainDb = $this->dbManager->getRepricerMainDb();

        /** @var Connection $customerServiceDb */
        $customerServiceDb = \Yii::$app->customerServiceDb;
        $customerServiceDb->createCommand('SET search_path TO product')->execute();

        foreach ($this->iterateSellers($customerIdFrom, $customerIdTo) as $seller) {
            if ($seller->is_demo) {
                continue;
            }

            $accountId = $repricerMainDb
                ->createCommand("
                    SELECT id
                    FROM amazon_customer_account
                    WHERE sellerId = :seller_id
                    LIMIT 1
                ", [
                    'seller_id' => $seller->id,
                ])
                ->cache(60 * 60 * 24 * 2)
                ->queryScalar();

            if (empty($accountId)) {
                $this->info("Unable to find account_id for seller {$seller->id}");
                continue;
            }

            $tableName = "list_{$seller['customer_id']}_{$accountId}";

            $limit = self::PRODUCTS_BATCH_SIZE;
            $offset = 0;
            $countProcessed = 0;

            try {
                $countAllProducts = $customerServiceDb
                    ->createCommand("SELECT count(*) FROM {$tableName}")
                    ->queryScalar();

                Console::startProgress(0, $countAllProducts, "Seller {$seller->id} products processed");

                while (true) {
                    $createdAt = date('Y-m-d H:i:s');

                    $products = $customerServiceDb
                        ->createCommand("
                            SELECT DISTINCT ON (marketplace_id, sku)
                                '{$seller->id}' as seller_id,
                                '" . Product::SOURCE_REPRICER . "' as source,
                                marketplace_id,
                                product_name as title,
                                sku,
                                asin,
                                stock_type,
                                '$createdAt' as created_at,
                                '$createdAt' as updated_at
                            FROM {$tableName}
                            ORDER BY marketplace_id, sku, created_at DESC
                            LIMIT {$limit} 
                            OFFSET {$offset}
                        ")
                        ->queryAll();

                    if (empty($products)) {
                        break;
                    }

                    $offset += $limit;

                    foreach ($products as $k => $product) {
                        $marketplace = AmazonMarketplace::getById($product['marketplace_id']);
                        $products[$k]['currency_code'] = $marketplace->currency_code;
                    }

                    yield $products;
                    $countProcessed += count($products);
                    Console::updateProgress($countProcessed, $countAllProducts, "Seller {$seller->id} products processed");
                }
            } catch (\Throwable $e) {
                // Table does not exist
                if ($e->getCode() === '42P01') {
                    continue;
                }

                throw $e;
            }
        }
    }

    public function actionFillIsMultipleStockType(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('order', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->info("Processing seller " . $this->dbManager->getSellerId() . ' | '. $this->dbManager->getCustomerId());

                $orderTableName = explode('.', AmazonOrder::tableName())[0] . '.amazon_order_view';
                $productTableName = Product::tableName();

                $query = "
                    UPDATE {$productTableName} p
                    SET is_multiple_stock_type = 't'
                    FROM (
                        SELECT 
                            order_marketplace_id as marketplace_id, 
                            sku, 
                            seller_id
                        FROM {$orderTableName}
                        GROUP BY order_marketplace_id, seller_id, sku
                        HAVING array_length(array_agg(DISTINCT fulfillment_channel), 1) > 1
                    ) as o
                    WHERE p.is_multiple_stock_type = 'f'
                    AND p.marketplace_id = o.marketplace_id
                    AND p.seller_id = o.seller_id
                    AND p.sku = o.sku
                ";

                Product::getDb()->createCommand($query)->queryAll();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    /**
     * One time command, will be removed afeter release.
     *
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionSetDefaultIsEnabledSync(
        int $customerIdFrom = null,
        int $customerIdTo = null,
        int $fromId = null
    )
    {
        $sql = "select c.id 
from customer c
where c.id not in (select distinct customer.id
from customer
join customer_account on customer_account.customer_id = customer.id
join amazon_customer_account on amazon_customer_account.customer_account_id = customer_account.id and repricer_module_connected_status <> 'NEW'
where customer.bas_module_started = 1)
and (c.use_lost_module = 1 or c.use_repricer_module = 1 OR c.use_bas_module = 1)";
        $customerIds = $this->dbManager->getRepricerMainDb()
            ->createCommand($sql)
            ->queryColumn()
        ;
        $dbManager = \Yii::$app->dbManager;
        $batchSize = 20000;

        foreach ($customerIds as $customerId) {
            if (null !== $customerIdFrom && $customerId < $customerIdFrom) {
                continue;
            }

            if (null !== $customerIdTo && $customerId >= $customerIdTo) {
                continue;
            }

            $this->info(str_repeat('-', 50));
            $this->info("Processing customer id {$customerId}");
            $dbManager->setCustomerId($customerId);

            $customer = Customer::findOne($customerId);

            if (null !== $customer->is_sync_default) {
                $this->info('Already processed, skipped');
                continue;
            }

            $fromId = $fromId ?? Product::find()->select('MIN(id)')->scalar();
            $maxId = Product::find()->select('MAX(id)')->scalar();

            while ($fromId <= $maxId) {
                $lastId = Product::find()
                    ->select('id')
                    ->where(['>=', 'id', $fromId])
                    ->orderBy(['id' => SORT_ASC])
                    ->limit(1)
                    ->offset($batchSize - 1)
                    ->scalar();

                // if last < 20000
                if (!$lastId) {
                    $lastId = Product::find()
                        ->select('MAX(id)')
                        ->where(['>=', 'id', $fromId])
                        ->scalar();
                }

                $this->info([
                    'customerId' => $customerId,
                    'fromId' => $fromId,
                    'lastId' => $lastId,
                    'maxId' => $maxId,
                ]);

                if (!$lastId) {
                    break;
                }

                Product::updateAll(
                    ['is_enabled_sync_with_repricer' => false],
                    ['AND', ['>=', 'id', $fromId], ['<=', 'id', $lastId]]
                );

                $fromId = $lastId + 1;
            }

            $this->info('Updating customer');
            $customer->is_sync_default = false;
            $customer->save();
        }
    }
}
