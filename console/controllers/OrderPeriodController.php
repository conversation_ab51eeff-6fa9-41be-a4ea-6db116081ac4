<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\services\order\RenewTerminatedPeriodsService;
use common\components\LogToConsoleTrait;
use common\components\services\order\OrderPeriodService;
use common\models\order\AmazonOrder;
use common\models\order\OrderPeriod;
use common\models\Seller;
use yii\console\widgets\Table;
use yii\helpers\Console;


class OrderPeriodController extends BaseController
{
    use LogToConsoleTrait;

    public const BATCH_SIZE = 50;

    /**
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @throws \Exception
     */
    public function actionGenerateInit(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_order_init_periods_created' => false, 'is_db_created' => true, 'is_demo' => false]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }
        foreach ($query->batch(self::BATCH_SIZE, \Yii::$app->db) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                (new OrderPeriodService($seller))->generateInitPeriods();
            }
        }
        $this->unlock();
    }

    /**
     * @param int|null $customerIdFrom
     * @param int|null $customerIdTo
     * @throws \Exception
     */
    public function actionGenerateRefresh(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_order_init_periods_created' => true, 'is_demo' => false]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }
        foreach ($query->batch(self::BATCH_SIZE, \Yii::$app->db) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                (new OrderPeriodService($seller))->generateRefreshPeriod();
            }
        }
        $this->unlock();
    }

    public function actionIsAllInitPeriodsLoaded()
    {
        foreach (Seller::find()->where(['is_analytic_active' => true, 'is_order_init_periods_created' => true])->batch(self::BATCH_SIZE, \Yii::$app->db) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                (new OrderPeriodService($seller))->checkIsAllInitPeriodsLoaded();
            }
        }
    }

    public function actionRenewTerminated()
    {
        if (!$this->lock()) {
            return;
        }
        foreach (Seller::find()->where(['is_analytic_active' => true, 'is_demo' => false])->batch(self::BATCH_SIZE, \Yii::$app->db) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                try {
                    (new RenewTerminatedPeriodsService($seller))->renew();
                } catch (\Throwable $e) {
                    \Yii::error($e);
                }
            }
        }
        $this->unlock();
    }

    public function actionRenewWithTerminaterOrders()
    {
        if (!$this->lock()) {
            return;
        }
        foreach (Seller::find()->where(['is_analytic_active' => true])->batch(self::BATCH_SIZE, \Yii::$app->db) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                try {
                    /** @var DbManager $sellerDbManager */
                    $sellerDbManager = \Yii::$app->get('dbManager');
                    $sellerDbManager->setSellerId($seller->id);

                    $tableName = AmazonOrder::tableName();
                    $list = AmazonOrder::getDb()->createCommand('select order_period_id, count(*), max(created_at) from ' . $tableName . ' orders
WHERE "items_loading_status" = :terminated
GROUP BY order_period_id
having count(*) = (select count(*) from ' . $tableName . ' where order_period_id = orders.order_period_id);',[':terminated'=>AmazonOrder::ITEMS_LOADING_STATUS_TERMINATED])->queryAll();

                    if (!empty($list)){
                        $this->stdout(sprintf("Found for customer %s seller %s", $seller->customer_id, $seller->id), Console::FG_YELLOW);

                        echo Table::widget([
                            'headers' => ['order_period_id', 'count', 'max date'],
                            'rows' => $list,
                        ]);

                        foreach ($list as $item) {
                            $orderPeriod = OrderPeriod::findOne($item['order_period_id']);
                            if ($orderPeriod) {
                                $orderPeriod->loading_status = OrderPeriod::LOADING_STATUS_TERMINATED;
                                $orderPeriod->save(false);
                            }
                        }
                    }

                } catch (\Throwable $e) {
                    \Yii::error($e);
                }
            }
        }
        $this->unlock();
    }
}
