<?php

namespace console\controllers;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use yii\console\Controller;

class ClickhouseController extends BaseController
{
    use LogToConsoleTrait;

    protected ?string $tableName = null;

    /**
     * {@inheritdoc}
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'tableName'
        ]);
    }

    public function actionRebuildCommonByType(string $type)
    {
        $manager = new DynamicTablesManager();
        $manager->rebuildByType($type);
    }

    public function actionRebuildCustomerByType(string $type, int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $manager = new DynamicTablesManager();

        foreach ($dbManager->iterateSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($dbManager->getCustomerId())) {
                    continue;
                }
                $manager->rebuildByType($type, $this->tableName);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
        $this->unlock();
    }

    public function actionReBuildCronAwareTables(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $manager = new DynamicTablesManager();
            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;
            $currTime = new \DateTime();

            foreach ($dbManager->iterateActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
                try {
                    if (empty($dbManager->getCustomerId())) {
                        continue;
                    }

                    // Temporary stub to prevent dev clickhouse servers to be overloaded
                    if (YII_ENV !== 'prod' && !in_array($dbManager->getCustomerId(), [7532, 5899, 1, 19])) {
                        continue;
                    }

                    $this->info("Processing customer {$dbManager->getCustomerId()}");
                    $manager->rebuildCronAwareTables($currTime, $this->tableName);
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
        } catch (\Throwable $e) {
            $this->unlock();
            throw $e;
        }

        $this->unlock();
    }
}
