<?php

namespace console\controllers;

use common\components\dataAnomalyChecker\Checker;
use common\components\LogToConsoleTrait;

class DataAnomalyController extends BaseController
{
    use LogToConsoleTrait;

    protected bool $isAutoMode = false;

    protected bool $isOnlyActiveCustomers = true;

    /**
     * @param  string $actionID
     * @return array
     */
    public function options($actionID): array
    {
        switch ($actionID) {
            case 'check':
                return ['isAutoMode', 'isOnlyActiveCustomers'];
            case 'check-common':
                return ['isAutoMode'];
            default:
                return [];
        }
    }

    public function actionCheck(
        int $fromCustomerId = null,
        int $toCustomerId = null
    )
    {
        $checker = new Checker($this->isAutoMode ?? false);
        $checker->find(
            $fromCustomerId,
            $toCustomerId,
            $this->isOnlyActiveCustomers
        );
    }

    public function actionCheckCommon()
    {
        $checker = new Checker($this->isAutoMode ?? false);
        $checker->findCommon();
    }
}
