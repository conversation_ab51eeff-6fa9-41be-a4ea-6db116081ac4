<?php

namespace console\controllers;

use common\components\cron\GeneralCronComponent;
use common\components\processManager\ProcessManager;
use yii\console\Controller;

class CronController extends Controller
{
    /**
     * Checks which command should be invoked (using cron like expressions).
     * Puts these commands into queue.
     *
     * @throws \yii\base\InvalidConfigException
     */
    public function actionScheduleTasks()
    {
        /** @var ProcessManager $processManager */
        $processManager = \Yii::$app->processManager;

        if ($processManager->isFrozen()) {
            return;
        }

        /** @var GeneralCronComponent $generalCron */
        $generalCron = \Yii::$app->get('cronComponent');
        $generalCron->scheduleTasks();
    }

    /**
     * Checks which command should be invoked (using cron like expressions).
     * Puts these commands into queue.
     *
     * @throws \yii\base\InvalidConfigException
     */
    public function actionForceRun(string $command, int $customerIdStep)
    {
        /** @var GeneralCronComponent $generalCron */
        $generalCron = \Yii::$app->get('cronComponent');
        $commands = $generalCron->generateCustomerIdChunkedCommands($command, $customerIdStep);

        foreach ($commands as $command) {
            $generalCron->executeCommand($command, true);
        }
    }
}
