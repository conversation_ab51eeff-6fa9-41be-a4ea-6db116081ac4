<?php

namespace console\controllers;

use common\components\featureFlag\FeatureFlagService;
use common\components\LogToConsoleTrait;
use common\models\FeatureFlag;
use common\models\FeatureFlagTarget;
use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Manages feature flags from the command line
 */
class FeatureFlagController extends Controller
{
    use LogToConsoleTrait;
    /** @var FeatureFlagService */
    private $featureFlagService;
    
    /**
     * {@inheritdoc}
     */
    public function __construct($id, $module, FeatureFlagService $featureFlagService, $config = [])
    {
        $this->featureFlagService = $featureFlagService;
        parent::__construct($id, $module, $config);
    }
    
    /**
     * List all feature flags
     *
     * @param int|null $customerId Customer ID to filter flags for a specific customer
     * @return int Exit code
     */
    public function actionList(int $customerId = null)
    {
        if ($customerId !== null) {
            $this->info("Listing feature flags for customer ID: {$customerId}\n");
        } else {
            $this->info("Listing all feature flags.\n");
        }

        $flags = $this->featureFlagService->getAllFlags($customerId);
        
        if (empty($flags)) {
            $this->info("No feature flags found.\n");
            return ExitCode::OK;
        }
        
        $this->info("Feature Flags:\n");
        $table = sprintf("%-5s | %-30s | %-40s | %-30s\n", 'ID', 'Name', 'Description', $customerId === null ? 'Global' : 'Enabled');


        foreach ($flags as $flag) {
            $table .= sprintf("%-33s | %-30s | %-40s | %-30s\n",
                $flag['id'],
                $flag['name'],
                mb_substr($flag['description'],0,40),
                $flag['enabled'] ? 'Yes' : 'No',
            );
        }
        $this->info($table);

        return ExitCode::OK;
    }

    /**
     * Enable a feature for specific customers
     * 
     * @param string $name Feature flag name
     * @param int $customerId
     * @return int Exit code
     */
    public function actionEnable(string $name, int $customerId)
    {
        $flag = FeatureFlag::findOne(['name' => $name]);
        
        if (!$flag) {
            $this->error("Feature flag '{$name}' not found.\n");
            return ExitCode::DATAERR;
        }

        if ($this->featureFlagService->enable($name, $customerId)) {
            $this->info("Feature '{$name}' enabled for customer: " . $customerId . "\n");
            return ExitCode::OK;
        } else {
            $this->error("Failed to enable feature for customers.\n");
            return ExitCode::SOFTWARE;
        }
    }
    
    /**
     * Disable a feature for specific customers
     * 
     * @param string $name Feature flag name
     * @param int $customerId
     * @return int Exit code
     */
    public function actionDisable(string $name, int $customerId)
    {
        $flag = FeatureFlag::findOne(['name' => $name]);
        
        if (!$flag) {
            $this->error("Feature flag '{$name}' not found.\n");
            return ExitCode::DATAERR;
        }

        if ($this->featureFlagService->enable($name, $customerId)) {
            $this->info("Feature '{$name}' disabled for customers: " . $customerId . "\n");
            return ExitCode::OK;
        } else {
            $this->error("Failed to disable feature for customers.\n");
            return ExitCode::SOFTWARE;
        }
    }
    
    /**
     * Enable a feature flag globally for all customers
     *
     * @param string $name Flag name
     * @return int Exit code
     */
    public function actionEnableGlobally(string $name)
    {
        $flag = FeatureFlag::findOne(['name' => $name]);

        if (!$flag) {
            $this->error("Feature flag '{$name}' not found.\n");
            return ExitCode::DATAERR;
        }

        if ($this->featureFlagService->changeForAll($name, true)) {
            $this->info("Feature '{$name}' has been enabled globally for all customers.\n");
            return ExitCode::OK;
        } else {
            $this->error("Failed to enable the feature globally.\n");
            return ExitCode::SOFTWARE;
        }
    }
    
    /**
     * Disable a feature flag globally for all customers
     *
     * @param string $name Flag name
     * @return int Exit code
     */
    public function actionDisableGlobally(string $name)
    {
        $flag = FeatureFlag::findOne(['name' => $name]);

        if (!$flag) {
            $this->error("Feature flag '{$name}' not found.\n");
            return ExitCode::DATAERR;
        }

        if ($this->featureFlagService->changeForAll($name, false)) {
            $this->info("Feature '{$name}' has been disabled globally.\n");
            return ExitCode::OK;
        } else {
            $this->error("Failed to disable the feature globally.\n");
            return ExitCode::SOFTWARE;
        }
    }

    /**
     * Create a feature flag on current environment for testing purposes.
     * This command is not intended for production use.
     * Please create feature flags via the migrations.
     *
     * @param string $name Flag name
     * @param string $description Flag description
     * @param string $initial_command initial command
     * @return int Exit code
     */
    public function actionCreate(string $name, string $description = null, string $initial_command = null)
    {
        $flag = new FeatureFlag();
        $flag->name = $name;
        $flag->description = $description;
        $flag->initial_command = $initial_command;

        if ($flag->save()) {
            $this->info("Feature flag '{$name}' created successfully.\n");
            $flatTarget = new FeatureFlagTarget();
            $flatTarget->feature_id = $flag->id;
            $flatTarget->customer_id = null;
            $flatTarget->initial_params = null;
            $flatTarget->initial_status = null;
            $flatTarget->is_enabled = false;
            $flatTarget->save();
            return ExitCode::OK;
        } else {
            $this->error("Failed to create feature flag: " . implode(', ', $flag->getErrorSummary(true)) . "\n");
            return ExitCode::SOFTWARE;
        }
    }

    /**
     * Flush the cache for feature flags
     *
     * @param int|null $customerId Customer ID to flush cache for a specific customer
     */
    public function actionFlushCache(?int $customerId = null)
    {
        if ($customerId === null) {
            $this->info("Flushing cache for all feature flags.\n");
        } else {
            $this->info("Flushing cache for feature flags of customer ID: {$customerId}\n");
        }
        $this->featureFlagService->flushCache($customerId);
        $this->info("Cache flushed successfully.\n");
    }
}
