<?php

namespace console\controllers;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\LogToConsoleTrait;
use common\components\services\order\TransferOrderService;
use common\models\Customer;
use common\models\order\AmazonOrderView;
use common\models\Seller;

class AmazonOrderController extends BaseController
{
    use LogToConsoleTrait;

    public bool $isActiveOnly = false;

    /**
     * {@inheritdoc}
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'isActiveOnly'
        ]);
    }

    public function actionTransfer(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                // Separated command execution ofr active and inactive customers.
                if ($this->dbManager->isActive() !== $this->isActiveOnly) {
                    continue;
                }

                $this->actionTransferForCustomer($dbManager->getCustomerId());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }


    public function actionReInitForNewCustomer(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $customer = Customer::find()
                    ->where([
                        'id' => $dbManager->getCustomerId(),
                    ])
                    ->andWhere(['>', 'created_at', (new \DateTime('-1 month'))->format('Y-m-d H:i:s')])
                    ->one();

                if ($customer === null) {
                    continue;
                }

                $this->info('Process for customer ID: ' . $dbManager->getCustomerId());

                $sellers = Seller::find()->where(['is_analytic_active' => true])->andWhere(['customer_id' => $dbManager->getCustomerId()])->all();

                $countOrderByPostgres = 0;
                $countOrderByClickhouse = \common\models\customer\clickhouse\AmazonOrderExtendedViewV1::find()
                    ->where(['order_status' => 'Shipped'])
                    ->andWhere(['>', 'order_purchase_date', (new \DateTime('-1 month'))->format('Y-m-d H:i:s')])
                    ->count();

                /** @var Seller $seller */
                foreach ($sellers as $seller) {
                    $this->dbManager->setSellerId($seller->id);

                    $countOrderBySeller = AmazonOrderView::find()
                        ->where(['order_status' => 'Shipped'])
                        ->andWhere(['>', 'order_purchase_date', (new \DateTime('-1 month'))->format('Y-m-d H:i:s')])
                        ->count();
                    $countOrderByPostgres += (int)$countOrderBySeller;
                }

                $isDifferent = abs($countOrderByPostgres - $countOrderByClickhouse) > (0.05 * max($countOrderByPostgres, $countOrderByClickhouse));

                if (!$isDifferent) {
                    continue;
                }

                $this->info('Different order count clickhouse: ' . $countOrderByClickhouse . ' order count postgres: ' . $countOrderByPostgres);

                $this->info('Re init for customer ID: ' . $dbManager->getCustomerId());

                \Yii::$app->cronComponent->addCommand(
                    sprintf("amazon-order/re-init %d %d", $dbManager->getCustomerId(), $dbManager->getCustomerId() + 1),
                );
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionReInit(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                (new TransferOrderService($dbManager->getCustomerId()))->reinit();

                $manager = new DynamicTablesManager();
                $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
                $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }


    public function actionTransferForCustomer(string $customerId)
    {
        //temp fix: https://app.rollbar.com/a/sellerlogic/fix/item/profit.dashboard/5741/occurrence/327997113699#detail
        if (in_array($customerId, [1724, 5756, 6412, 4628]))
            return;

        (new TransferOrderService($customerId))->transfer();
    }

    public function actionSwitchTables(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->actionSwitchTablesForCustomer($dbManager->getCustomerId());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSwitchTablesForCustomer(int $customerId)
    {
        (new TransferOrderService($customerId))->switchTables();
    }
}


