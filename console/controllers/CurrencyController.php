<?php

namespace console\controllers;

use common\components\currencyRate\CurrencyRateManager;
use common\components\LogToConsoleTrait;

class CurrencyController extends BaseController
{
    use LogToConsoleTrait;

    /**
     * Load all currencies from 2016-12-01 to today
     */
    public function actionInit(): void
    {
        (new CurrencyRateManager())->loadRatesFromApi(new \DateTime('2016-12-01'), new \DateTime());
    }

    /**
     * Load new currency rates
     */
    public function actionRefresh(): void
    {
        $today = new \DateTime();
        $from = clone $today;
        $from->modify('-2 days');
        (new CurrencyRateManager())->loadRatesFromApi($from, $today);
    }
}
