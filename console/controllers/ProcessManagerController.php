<?php

namespace console\controllers;

use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;

class ProcessManagerController extends BaseController
{
    use LogToConsoleTrait;

    private const WAIT_FOR_RELEASE_ITERATION_TIME_S = 10;

    private ProcessManager $processManager;

    public function __construct($id, $module, $config = [])
    {
        $this->processManager = \Yii::$app->processManager;
        parent::__construct($id, $module, $config);
    }

    public function actionFreeze()
    {
        $this->processManager->freeze();
    }

    public function actionUnfreeze()
    {
        $this->processManager->unfreeze();
    }

    public function actionRelease(string $processName)
    {
        $this->processManager->release($processName);
    }

    public function actionWaitForRelease()
    {
        do {
            $processes = $this->processManager->getAll();

            $this->info("Current process list:");
            $this->info($processes);

            if (count($processes) === 0) {
               break;
            }

            $this->info("Waiting..");
            sleep(self::WAIT_FOR_RELEASE_ITERATION_TIME_S);
        } while (true);

        $this->info("All process has been released");
    }
}
