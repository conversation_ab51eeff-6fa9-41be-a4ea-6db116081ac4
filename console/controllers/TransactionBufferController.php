<?php

namespace console\controllers;

use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use common\components\transactionBuffer\TransactionBufferFlusher;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\TransactionBuffer;
use yii\caching\CacheInterface;
use yii\db\Expression;

class TransactionBufferController extends BaseController
{
    use LogToConsoleTrait;

    /**
     * Checking buffers interval,
     */
    protected const CHECK_INTERVAL_S = 5;

    public function actionForceFlush(int $customerId)
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $this->flush($customerId);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    public function actionFlushAllIfNeed(int $customerIdFrom = null, int $customerIdTo = null, bool $force = false)
    {
        $this->info(str_repeat('-', 50));

        while (true) {
            foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
                try {
                    if (!$this->dbManager->getCustomerId()) {
                        continue;
                    }

                    $this->flush($this->dbManager->getCustomerId());
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }

            $checkInterval = self::CHECK_INTERVAL_S;
            $this->info("Waiting {$checkInterval} seconds before next check");
            sleep($checkInterval);
        }
    }

    protected function flush(int $customerId)
    {
        $this->dbManager->setCustomerId($customerId);
        $transactionFlusher = new TransactionBufferFlusher();
        $transactionFlusher->flush($customerId);
    }
}
