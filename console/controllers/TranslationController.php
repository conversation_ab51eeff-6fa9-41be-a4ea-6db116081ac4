<?php

namespace console\controllers;

use common\components\translation\http\EnvironmentMessageClient;
use common\components\translation\http\EnvironmentMessageTranslationClient;
use common\components\translation\http\ExternalMessageClient;
use common\components\translation\http\MessageClientInterface;
use common\components\translation\http\MessageTranslationClientInterface;
use common\models\Message;
use common\components\translation\http\MessageClient;
use common\components\LogToConsoleTrait;
use common\components\translation\http\ExternalMessageTranslationClient;
use common\components\translation\TranslationService;
use GuzzleHttp\Exception\RequestException;
use yii\base\DynamicModel;
use yii\base\Model;
use yii\console\ExitCode;
use yii\console\widgets\Table;
use yii\helpers\Console;

class TranslationController extends BaseController
{
    use LogToConsoleTrait;

    public const BATCH_MESSAGES_LIMIT = 100;

    private const ACTION_SYNC_EXTERNAL = 'sync-external';

    private const ACTION_SYNC_ENVIRONMENT = 'sync-environment';

    private const ACTION_TRANSFER = 'transfer';

    private const LANGUAGE_CODE_REPLACEMENT_MAP = [
        'cn' => 'zh',
    ];

    private const DEFAULT_SOURCE_LANGUAGE_CODE = 'en';

    public int $batchMessagesLimit = self::BATCH_MESSAGES_LIMIT;

    public int $timeout = 540;

    /**
     * @param  string $actionID
     * @return array
     */
    public function options($actionID): array
    {
        switch ($actionID) {
            case self::ACTION_TRANSFER:
                return ['batchMessagesLimit'];
            case self::ACTION_SYNC_EXTERNAL:
            case self::ACTION_SYNC_ENVIRONMENT:
            default:
                return [];
        }
    }

    protected function stdoutIncorrectUsage(DynamicModel $validation): int
    {
        $this->error('Incorrect usage, argument or option error.');

        $widget = self::getValidationErrorWidget($validation);

        $this->error($widget);

        return ExitCode::USAGE;
    }

    protected static function getValidationErrorWidget(Model $validation): string
    {
        $rows = [];
        foreach ($validation->getErrors() as $key => $message) {
            $rows[] = ['Error', ExitCode::USAGE, $key, $message];
        }

        return Table::widget(
            [
                'headers' => ['Status', 'Code', 'Key', 'Message'],
                'rows' => $rows,
            ]
        );
    }

    /**
     * @return array
     */
    public function optionAliases(): array
    {
        return [
            'l' => 'batchMessagesLimit',
            't' => 'timeout',
        ];
    }


    /**
     * Sync of translations, loading data from rest-api project to profit-dash project.
     */
    public function actionSyncExternal(): int
    {
        $client = new ExternalMessageTranslationClient();
        $languages = $client->getLanguages();
        foreach ($languages as $language) {
            $this->sync($client, $language['code']);
        }

        return ExitCode::OK;
    }

    /**
     * Sync of translations, loading data from staging to rc or\and from rc to production environment on deploying.
     */
    public function actionSyncEnvironment(): int
    {
        $languages = (new ExternalMessageTranslationClient())->getLanguages();
        foreach ($languages as $language) {
            $this->sync((new EnvironmentMessageTranslationClient()), $language['code']);
        }

        return ExitCode::OK;
    }

    protected function sync(MessageTranslationClientInterface $messageTranslationClient, ?string $language = null)
    {
        $languagePrefix = $language ? '[' . $language . '] ' : '';

        try {
            $translations = $messageTranslationClient->getMessageTranslations($language, (new \DateTime('-2 weeks'))->format('Y-m-d'));
        } catch (RequestException|\Exception $exception) {
            $this->error($exception);
            return ExitCode::DATAERR;
        }

        if (!$translations || !\is_array($translations)) {
            $this->error($languagePrefix . 'Unable to retrieve data for translation synchronization, contents is empty.');

            return ExitCode::DATAERR;
        }

        $result = (new TranslationService())->sync($translations);


        $this->info($languagePrefix . sprintf("Updated messages: %s", $result['message_updated']));
        $this->info($languagePrefix . sprintf("Inserted messages: %s", $result['message_inserted']));
        $this->info($languagePrefix . sprintf("Updated translations: %s", $result['message_translation_updated']));
        $this->info($languagePrefix . sprintf("Inserted translations: %s", $result['message_translation_inserted']));

        return ExitCode::OK;
    }


    /**
     * Transfer new untranslated messages from profit-dash to rest-api project.
     */
    public function transferExternal(): int
    {
        $this->info("Transfer to rest-api project.");
        $this->transfer($this->batchMessagesLimit, (new ExternalMessageClient()));

        return ExitCode::OK;
    }

    /**
     * Transfer new untranslated messages from production to rc or\and from rc to staging environment.
     */
    public function transferEnvironment(): int
    {
        $this->info("Transfer between environments.");
        $this->transfer($this->batchMessagesLimit, (new EnvironmentMessageClient()));

        return ExitCode::OK;
    }

    /**
     * Transfer new untranslated messages.
     */
    public function actionTransfer(): int
    {
        $validation = DynamicModel::validateData(
            [
                'batchMessagesLimit' => $this->batchMessagesLimit,
            ],
            [
                [['batchMessagesLimit'], 'integer', 'min' => 1, 'max' => 100],
            ]
        );

        if ($validation->hasErrors()) {
            return self::stdoutIncorrectUsage($validation);
        }

        if (YII_ENV === 'staging') {
            $this->transferExternal();
        } else {
            $this->transferEnvironment();
        }

        return ExitCode::OK;
    }

    /**
     * Executes transferring of messages.
     */
    private function transfer($batchMessagesLimit, MessageClientInterface $messageClient): bool
    {
        /** @var Message[] $messages */
        $messages = Message::find()
            ->where(
                [
                    'is_processed' => 0,
                ]
            )
            ->limit($batchMessagesLimit)
            ->orderBy(['id' => SORT_DESC])
            ->all();

        if (!$messages) {
            $this->info("Empty message queue.");

            return false;
        }

        $this->info(sprintf("Found [%s] messages to be transferred.", count($messages)));

        $transferred = 0;

        foreach ($messages as $message) {
            $messageClient->postMessage([
                'category' => $message->category,
                'message' => $message->message,
            ]);

            $message->is_processed = true;
            $message->saveOrThrowException();

            $transferred++;
        }

        $this->info(sprintf("Total transferred messages: %s.", $transferred));

        return true;
    }
}
