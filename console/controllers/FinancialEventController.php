<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\services\financialEvent\LoadingEventsService;
use common\models\Seller;
use yii\helpers\Console;

class FinancialEventController extends BaseController
{
    use LogToConsoleTrait;
    public const BATCH_SIZE = 50;

    public function actionLoadInit(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_init_periods_created' => true, 'is_init_periods_loaded' => false,  'is_db_created' => true]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                $this->info('Get batch');
                $this->stdout(
                    'Get batch',
                    Console::FG_GREEN
                );
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    if ($seller->is_demo || $seller->canMakeRequestToAmazon()) {
                        $this->info('canMakeRequestToAmazon ' . $seller->id);
                        $service = new LoadingEventsService($seller);
                        $this->info('LoadingEventsService');
                        $service->loadInit();
                        $this->info('loadInit');
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    public function actionLoadRefresh(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    if ($seller->is_demo || $seller->canMakeRequestToAmazon()) {
                        $service = new LoadingEventsService($seller);
                        $service->loadRefresh();
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->unlock();
    }
}
