<?php

namespace console\controllers;

use common\components\clickhouse\materializedViews\dictionaries\FinanceEventCategoryDict;
use common\components\LogToConsoleTrait;
use common\components\salesCategoryMapper\SalesCategoryBuilder;
use common\components\salesCategoryMapper\SalesCategoryMapper;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\helpers\Inflector;

class SalesCategoriesController extends BaseController
{
    use LogToConsoleTrait;

    /**
     * Maps event categories to sales categories (using event category path as starting point).
     */
    public function actionMap(): void
    {
        $salesCategoryBuilder = new SalesCategoryBuilder();
        $salesCategoryBuilder->rebuild();

        $salesCategoriesMapper = new SalesCategoryMapper();
        $salesCategoriesMapper->remap();
    }

    public function actionCheck(string $filePath)
    {
        // Read csv file
        $csvFile = fopen($filePath, 'r');
        $csvData = [];
        while (($row = fgetcsv($csvFile)) !== false) {
            $csvData[] = $row;
        }

        $result = [
            'notFoundInDbPaths' => [],
            'pathsWithWrongName' => [],
            'wrongMappedPaths' => []
        ];
        $financeCategories = FinanceEventCategory::find()
            ->select('sc.path as sales_category_path, finance_event_category.path as finance_event_category_path')
            ->leftJoin(SalesCategory::tableName() . ' sc', 'sc.id = finance_event_category.sales_category_id')
            ->asArray()
            ->all()
        ;

        foreach ($csvData as $k => $csvDatum) {
            if ($k === 0) {
                continue;
            }

            $path = $csvDatum[1];
            $category0 = Inflector::slug($csvDatum[3], '_');
            $category1 = Inflector::slug($csvDatum[4], '_');
            $category2 = Inflector::slug($csvDatum[5], '_');
            $category3 = Inflector::slug($csvDatum[6], '_');

            $path = str_replace(FinanceEventCategory::PLUS_ZERO_POSTFIX, '', $path);
            $path = str_replace(FinanceEventCategory::PLUS_POSTFIX, '', $path);
            $path = str_replace(FinanceEventCategory::MINUS_POSTFIX, '', $path);

            $zeroPlusPath = $path . FinanceEventCategory::PLUS_ZERO_POSTFIX;

            if ($category0 === RevenueExpensesStrategy::CATEGORY_COSTS) {
                $path .= FinanceEventCategory::MINUS_POSTFIX;
            } else if ($category0 === RevenueExpensesStrategy::CATEGORY_REVENUE) {
                $path .= FinanceEventCategory::PLUS_POSTFIX;
            }

            $actualFinanceCategory = null;
            foreach ($financeCategories as $financeCategory) {
                if (false !== strpos($financeCategory['finance_event_category_path'], $path)) {
                    $actualFinanceCategory = $financeCategory;
                    break;
                }
                if (false !== strpos($financeCategory['finance_event_category_path'], $zeroPlusPath)) {
                    $actualFinanceCategory = $financeCategory;
                    break;
                }
            }

            $sanitizeCategoryPathFn = function (string $path) {
                $path = str_replace(['_1', '_2', '_3', '_4', '_5'], '', $path);
                return str_replace(['_pu', '_total'], '', $path);
            };

            $categoryPathExpected = implode('|', array_filter([$category0, $category1, $category2, $category3]));
            $categoryPathExpected = trim($categoryPathExpected);

            if (empty($actualFinanceCategory)) {
                $similarPaths = [];
                $similarPath = $path;

                $similarPath = str_replace(FinanceEventCategory::PLUS_ZERO_POSTFIX, '', $similarPath);
                $similarPath = str_replace(FinanceEventCategory::PLUS_POSTFIX, '', $similarPath);
                $similarPath = str_replace(FinanceEventCategory::MINUS_POSTFIX, '', $similarPath);

                foreach ($financeCategories as $financeCategory) {
                    if (false !== strpos($financeCategory['finance_event_category_path'], $similarPath)) {
                        continue 2;
                    }
                }

                $result['notFoundInDbPaths'][] = [
                    'counter' => count($result['notFoundInDbPaths']) + 1,
                    'path' => $path,
                    'categoryExpected' => $categoryPathExpected,
                    'similarPaths' => $similarPaths,
                ];
                continue;
            }

            $categoryPathActual = $sanitizeCategoryPathFn($actualFinanceCategory['sales_category_path']);
            $categoryPathExpected = $sanitizeCategoryPathFn($categoryPathExpected);
            $isActualZeroPlus = false !== strpos($actualFinanceCategory['finance_event_category_path'], FinanceEventCategory::PLUS_ZERO_POSTFIX);
            $isWrongNamed = str_replace(['s|'], '|', $categoryPathActual) == str_replace(['s|'], '|', $categoryPathExpected);

            if ($categoryPathExpected != $categoryPathActual && !$isActualZeroPlus) {
                if ($isWrongNamed) {
                    $result['pathsWithWrongName'][] = [
                        'path' => $path,
                        'expected' => $categoryPathExpected,
                        'actual' => $categoryPathActual
                    ];
                } else {
                    $result['wrongMappedPaths'][] = [
                        'counter' => count($result['wrongMappedPaths']) + 1,
                        'path' => $path,
                        'categoryExpected' => $categoryPathExpected,
                        'categoryActual' => $categoryPathActual
                    ];
                }
            }
        }

        echo json_encode($result, JSON_PRETTY_PRINT);
    }
}
