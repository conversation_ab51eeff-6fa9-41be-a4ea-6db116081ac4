<?php

namespace console\controllers;

use common\components\COGSync\COGSynchronizer;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\helpers\Console;
use yii\mutex\Mutex;

class ProductCostController extends BaseController
{
    use LogToConsoleTrait;

    protected bool $isFirstSync = false;
    protected bool $shouldSendCOGChanges = true;

    private MessagesSender $rabbitMqMessagesSender;
    private COGSynchronizer $COGSynchronizer;

    public function __construct($id, $module, $config = [])
    {
        $this->rabbitMqMessagesSender = new MessagesSender();
        $this->COGSynchronizer = \Yii::$container->get('COGSynchronizer');

        parent::__construct($id, $module, $config);
    }

    /**
     * {@inheritdoc}
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'isFirstSync',
            'shouldSendCOGChanges'
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function optionAliases()
    {
        return array_merge(parent::optionAliases(), [
            'fs' => 'isFirstSync',
            'sendCOG' => 'shouldSendCOGChanges'
        ]);
    }

    /**
     * Walks through sellers one by one and receives and saves products' costs data.
     *
     * @throws \yii\base\InvalidConfigException
     */
    public function actionSync(int $customerIdFrom = null, int $customerIdTo = null)
    {
        ini_set('memory_limit', '512M');

        if (!$this->lock()) {
            return;
        }

        // Buffer for all chunks. Need it to be able to shuffle them and smooth the load on the system.
        $allChunks = [];
        $messagesSender = new MessagesSender();

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $allChunks = array_merge($this->COGSynchronizer->generateChunks(
                    $this->dbManager->getCustomerId(),
                    $this->isFirstSync,
                    $this->shouldSendCOGChanges
                ), $allChunks);

                $this->info('Count: ' . count($allChunks));

                if (count($allChunks) >= 1000) {
                    $messagesSender->customerCOGSSyncBatch($allChunks);
                    $allChunks = [];
                }
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }
        $messagesSender->customerCOGSSyncBatch($allChunks);

        $this->unlock();
    }

    public function actionCheckRefunds()
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer') as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $this->rabbitMqMessagesSender->customerCOGCheckRefunds($this->dbManager->getCustomerId());
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }

        $this->unlock();
    }

    public function actionRepairPeriods(int $customerIdFrom = null, int $customerIdTo = null)
    {
        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $dbInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $this->info("Processing customer " . $this->dbManager->getCustomerId());
                $manualPeriodsQuery = ProductCostPeriod::find()->where([
//                    'seller_sku' => '...'
                ])->orderBy('marketplace_id, seller_id, seller_sku, sales_category_id, id DESC');

                $previousKey = null;
                $previousPeriod = null;

                $countPeriods = $manualPeriodsQuery->count();
                $countProcessed = 0;
                Console::startProgress($countProcessed, $countPeriods, 'Processed event periods');

                /** @var ProductCostPeriod[] $manualPeriods */
                foreach ($manualPeriodsQuery->batch(500) as $manualPeriods) {
                    foreach ($manualPeriods as $k => $manualPeriod) {
                        $currKey = implode('|', [
                            $manualPeriod->marketplace_id,
                            $manualPeriod->seller_id,
                            $manualPeriod->seller_sku,
                            $manualPeriod->sales_category_id
                        ]);

                        $isFirst = false;
                        if ($currKey !== $previousKey) {
                            // Product changes, previous product is the first one of previous product.
                            // Date start should be infinite.
                            if (!empty($previousPeriod) && $previousPeriod->date_start !== null) {
                                $this->info(sprintf(
                                    "%s => %s | %s | %s | %s",
                                    $previousPeriod->date_start ?? 'infinity',
                                    $previousPeriod->date_end ?? 'infinity',
                                    $previousPeriod->amount_total,
                                    $previousKey,
                                    $previousPeriod->id
                                ));

                                $previousPeriod->date_start = null;
                                $previousPeriod->save(false);
                            }

                            $isFirst = true;
                            $previousPeriod = null;
                        }

                        $previousKey = $currKey;
                        $manualPeriod->shouldSendCOGChanges = false;
                        $manualPeriod->shouldAutoCalculateDateEnd = false;

                        $isSomethingChanged = false;

                        if ($isFirst) {
                            if ($manualPeriod->date_end !== null) {
                                $manualPeriod->date_end = null;
                                $isSomethingChanged = true;
                            }
                        } elseif ($previousPeriod->date_start === null) {
                            $this->info('Deleted period ' . $manualPeriod->id);
                            $manualPeriod->delete();
                            continue;
                        } else {
                            $newDateEnd = (new \DateTime($previousPeriod->date_start))->modify('-1 second')->format('Y-m-d H:i:s');

                            if ($manualPeriod->date_end !== $newDateEnd) {
                                $manualPeriod->date_end = $newDateEnd;
                                $isSomethingChanged = true;
                            }
                        }

                        if ($isSomethingChanged) {
                            $this->info(sprintf(
                                "%s => %s | %s | %s | %s",
                                $manualPeriod->date_start ?? 'infinity',
                                $manualPeriod->date_end ?? 'infinity',
                                $manualPeriod->amount_total,
                                $currKey,
                                $manualPeriod->id
                            ));

                            $manualPeriod->save(false);
                        }

                        $previousPeriod = $manualPeriod;
                    }

                    $countProcessed += count($manualPeriods);
                    Console::updateProgress($countProcessed, $countPeriods, 'Processed event periods');
                }
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }
    }

    public function actionRecalculateCurrencyRate(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            $query = ProductCostItem::find()
                ->leftJoin(ProductCostPeriod::tableName() . ' pcp', 'pcp.id = product_cost_period_id')
                ->where([
                    'AND',
                    ['=', 'is_currency_rate_recalculated', 'f'],
                    ['<', 'pcp.date_start', date('Y-m-d 23:59:59')]
                ])
            ;
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }
                $this->info("Processing customer " . $this->dbManager->getCustomerId());

                /** @var ProductCostItem[] $productCostItems */
                foreach ($query->batch(500, ProductCostItem::getDb()) as $productCostItems) {
                    foreach ($productCostItems as $productCostItem) {
                        // Currency rate will be recalculated in beforeSave action.
                        $productCostItem->marketplace_currency_rate = null;
                        $productCostItem->is_currency_rate_recalculated = true;
                        $productCostItem->save(false);
                    }
                }
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }

        $this->unlock();
    }
}
