<?php

namespace console\controllers;

use common\components\customerProcess\DelayedChecker;
use common\components\customerProcess\ProcessExecutor;
use common\components\LogToConsoleTrait;
use common\components\customerProcess\ProcessManager;
use common\models\CustomerProcess;
use yii\db\Expression;

class CustomerProcessController extends BaseController
{
    public const MAX_IN_PROGRESS_PROCESSES = 5;

    use LogToConsoleTrait;

    private ProcessManager $processManager;

    public function __construct($id, $module, $config = [])
    {
        $this->processManager = new ProcessManager();
        parent::__construct($id, $module, $config);
    }

    public function actionSchedule(string $processName, string $customerIdFrom = null, int $customerIdTo = null)
    {
        if (false !== strpos($customerIdFrom, ',')) {
            $customerIds = explode(',', $customerIdFrom);

            foreach ($customerIds as $customerId) {
                $customerId = (int)$customerId;
                try {
                    $this->dbManager->setCustomerId($customerId, false);
                    $this->processManager->schedule($processName);
                } catch (\Throwable $e) {
                    $this->info($e);
                }
            }
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }
                $this->processManager->schedule($processName);
            } catch (\Throwable $e) {
                $this->info($e);
            }
        }
    }

    public function actionCheckDelayed()
    {
        $delayedChecker = new DelayedChecker();
        $delayedChecker->startLoop();
    }

    public function actionTerminateIfNeed()
    {
        $maxMinutesWithoutUpdates = 60 * 3;
        /** @var CustomerProcess[] $processesToTerminate */
        $processesToTerminate = CustomerProcess::find()
            ->where([
                'AND',
                ['in', 'status', [CustomerProcess::STATUS_IN_PROGRESS, CustomerProcess::STATUS_IN_PROGRESS_DELAYED]],
                ['is', 'parent_process_id', new Expression('NOT NULL')],
                ['<=', 'updated_at', (new \DateTime())->modify("-$maxMinutesWithoutUpdates minutes")->format('Y-m-d H:i:s')]
            ])
            ->limit(100)
            ->all();

        foreach ($processesToTerminate as $customerProcess) {
            $customerProcess
                ->setFailed(
                    "Too much time without any updates (more that $maxMinutesWithoutUpdates minutes), terminated"
                );
        }
    }

    public function actionProcessExecutor()
    {
        $processExecutor = new ProcessExecutor();
        $processExecutor->startLoop();
    }

    public function actionList()
    {
        $processes = $this->processManager->getAllProcesses();
        $names = [];

        foreach ($processes as $process) {
            $names[] = $process->getName();
        }

        $this->info($names);
    }
}
