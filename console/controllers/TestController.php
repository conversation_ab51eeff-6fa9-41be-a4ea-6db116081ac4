<?php

namespace console\controllers;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\COGSync\COGSynchronizer;
use common\components\COGSync\PeriodsMerge;
use common\components\core\db\dbManager\DbManager;
use common\components\core\i18n\I18N;
use common\components\customerConfig\CustomerConfig;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\dataImportExport\SupportedHandlers;
use common\components\dbStructure\TableNameGenerator;
use common\components\exception\SellerNotFoundException;
use common\components\fileDataReader\DataReaderFactory;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\consumers\EventPeriodsFromDbToClickhouseConsumer;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\salesMetricCalculator\UnitsCalculator;
use common\components\sellingApi\apiProxy\FinancesApi;
use common\components\sellingApi\apiProxy\OrdersApi;
use common\components\services\financialEvent\ClickhouseTransactionExtractor;
use common\components\services\order\dataConverter\PostgresAmazonOrderItemToClickhouseOrderBasedConverter;
use common\models\AmazonMarketplace;
use common\models\customer\clickhouse\Transaction;
use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductTag;
use common\models\customer\Tag;
use common\models\DbStructure;
use common\models\finance\EventPeriod;
use common\models\finance\Refund;
use common\models\finance\Shipment;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use PhpAmqpLib\Message\AMQPMessage;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Console;
use yii\helpers\Inflector;

class TestController extends BaseController
{
    use LogToConsoleTrait;

    public function actionDataImport(int $customerId, string $importFilePath)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($customerId);
        $handlerName = SupportedHandlers::HANDLER_PRODUCT_COST_PERIODS;

        $tmpFilePath = $importFilePath;
        $this->info('Source file has been downloaded into ' . $tmpFilePath);

        $importerFactory = new ImporterFactory();
        $dataReaderFactory = new DataReaderFactory();

        $importer = $importerFactory->getImporter($handlerName);
        $dataReader = $dataReaderFactory->getDataReaderByPath($tmpFilePath);

        $this->info('Reading data from file');
        $dataToImport = $dataReader->getData($tmpFilePath, PHP_INT_MAX, 0, $importer->getRequiredFields());

        $this->info([
            'handlerName' => $handlerName,
            'dataReaderClass' => get_class($dataReader),
            'importerClass' => get_class($importer),
            'countDataToImport' => count($dataToImport),
        ]);

        $this->info('Importing data');

        /** @var I18N $i18n */
        $i18n = \Yii::$app->i18n;
        $i18n->isFutureTranslationMode = true;
        $importResult = $importer->import($dataToImport, 999);
        print_r($importResult);

        $i18n->isFutureTranslationMode = false;
    }

    public function actionGenerateTransactionsForOrder(string $sellerId, string $amazonOrderId)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
//        $eventTables = EventPeriod::getEventTables();
        $eventTables = [
            Shipment::tableName(),
            Refund::tableName()
        ];
        $seller = Seller::findOne($sellerId);

        $eventPeriodId = Transaction::find()
            ->select('EventPeriodId')
            ->where([
                'AmazonOrderId' => $amazonOrderId
            ])
            ->limit(1)
            ->scalar();

        if (empty($eventPeriodId)) {
            $this->info("Unable to determine EventPeriodId for order {$amazonOrderId}");

            /** @var AmazonOrderItem[] $orderItems */
            $orderItems = AmazonOrderItem::find()->where([
                'order_id' => $amazonOrderId
            ])->all();
            /** @var AmazonOrder $amazonOrder */
            $amazonOrder = AmazonOrder::find()->where(['amazon_order_id' => $amazonOrderId])->one();
            /** @var Product $product */
            $product = Product::find()->where([
                'AND',
                ['=', 'sku', $orderItems[0]->sku],
                ['=', 'marketplace_id', $amazonOrder->marketplace_id],
                ['=', 'seller_id', $sellerId]
            ])->one();
            $marketplace = AmazonMarketplace::findOne($amazonOrder->marketplace_id);

            $dataConverter = new PostgresAmazonOrderItemToClickhouseOrderBasedConverter();
            $orderBasedOrders = [];

            foreach ($orderItems as $orderItem) {
                $orderBasedOrders[] = $dataConverter->convert(
                    $orderItem->toArray(),
                    $amazonOrder->toArray(),
                    $product->toArray(),
                    $marketplace->toArray()
                );
            }

            $orderBasedTransaction = new OrderBasedTransaction();
            $transactions = $orderBasedTransaction->generateOrderBasedTransactionsForOrders($orderBasedOrders);

            // Leave ony transacitons with COGCategoryId = 3
            $transactions = array_filter($transactions, function ($transaction) {
                return $transaction['COGCategoryId'] == 3;
            });

            print_r($transactions);

            return;
        }

        $eventPeriod = EventPeriod::findOne($eventPeriodId);

        foreach ($eventTables as $tableName) {
            $this->info(str_repeat('-', 30));
            $this->info("Fetching events data from table $tableName");

            try {
                $financialEvents = (new Query())->from($tableName)->where([
                    'event_period_id' => $eventPeriod->id,
                    'AmazonOrderId' => $amazonOrderId
                ])->all();
            } catch (\Throwable $e) {
                if (false !== strpos($e->getMessage(), 'Undefined column')) {
                    continue;
                }
            }

            $this->info("Fetched " . count($financialEvents) . ' financial events');

            foreach ($financialEvents as $eventKey => $financialEvent) {
                foreach ($financialEvent as $propertyName => $propertyValue) {
                    if (is_array($propertyValue)) {
                        continue;
                    }
                    $propertyValue = stripslashes($propertyValue);
                    $propertyValue = trim($propertyValue, '""');
                    $jsonValue = json_decode($propertyValue, true);

                    if (!empty($jsonValue)) {
                        $financialEvents[$eventKey][$propertyName] = $jsonValue;
                    }
                }
            }

            $chTransactionsExtractor = new ClickhouseTransactionExtractor();
            $chTransactionsExtractor->process(
                $financialEvents,
                Inflector::camelize(explode('.', $tableName)[1]),
                [
                    ClickhouseTransactionExtractor::FACT_SELLER_ID => $seller->id,
                    ClickhouseTransactionExtractor::FACT_POSTED_DATE => $eventPeriod->finish_date
                ]
            );

            /** @var Transaction[] $extractedTransactions */
            $extractedTransactions = $chTransactionsExtractor->getAndFlushTransactions($seller);
            print_r($extractedTransactions);
        }
    }

    public function actionPeriodsMerge(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }
        $dbManager = $this->dbManager;

        $this->info('Periods Merge');

        $periodsMerge = new PeriodsMerge();

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $periodsMerge->deleteDuplicatePeriodsAll();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSetWasActiveUntilDate()
    {
        $customerIds = Seller::find()
            ->select('customer_id')
            ->distinct()
            ->orderBy('customer_id')
            ->column();

        foreach ($customerIds as $customerId) {
            $lastActivePlan = $this->dbManager->getRepricerMainDb()
                ->createCommand("
                    SELECT date_finish
                    FROM bas_customer_plan 
                    WHERE customer_id = {$customerId}
                    AND date_start <= now()
                    ORDER BY created_at DESC
                    LIMIT 1
                ")
                ->queryScalar();

            $lastActivePlan = date(
                'Y-m-d H:i:s',
                min(
                    time(),
                    strtotime($lastActivePlan ?? date('Y-m-d H:i:s'))
                )
            );

            Seller::updateAll([
                'was_active_until_date' => $lastActivePlan,
            ], [
                'customer_id' => $customerId,
                'is_active' => false,
            ]);
            $this->info(sprintf(
                "Updated customer %s with was_active_until_date %s",
                $customerId,
                $lastActivePlan
            ));
        }
    }

    public function actionSyncChunk(int $customerId, int $fromId = 0, int $toId = 1000, bool $isFirstSync = false)
    {
        /** @var COGSynchronizer $COGSynchronizer */
        $COGSynchronizer = \Yii::$container->get('COGSynchronizer');
        $COGSynchronizer->synchronizeChunk($customerId, $fromId, $toId, $isFirstSync);
    }

    public function actionDumpOrderItemsFromAmazonApi(string $sellerId, string $amazonOrderId)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $ordersApi = new OrdersApi($seller->id, $seller->region);
        $nexToken = null;

        while (true) {
            $result = $ordersApi->getOrderItems($amazonOrderId, $nexToken);
            $payload = $result->getPayload();
            $orderItems = json_encode($payload->getOrderItems(), JSON_PRETTY_PRINT);
            $nexToken = $payload->getNextToken();
            $this->info([
                'nextToken' => $nexToken,
                'orderItems' => $orderItems,
            ]);
            if (null === $nexToken) {
                break;
            }
        }
    }

    public function actionDumpOrders(string $sellerId, string $last_updated_after, $last_updated_before)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $ordersApi = new OrdersApi($seller->id, $seller->region);
        $nexToken = null;
        $marketplaceIds = AmazonMarketplace::find()->where(['is_active' => true])->select(['id'])->column();

        while (true) {
            $result = $ordersApi->getOrders(
                $marketplaceIds,
                null,
                null,
                $last_updated_after,
                $last_updated_before,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                $nexToken

            );
            $payload = $result->getPayload();
            $orderItems = json_encode($payload->getOrders(), JSON_PRETTY_PRINT);
            $nexToken = $payload->getNextToken();
            $this->info([
                'nextToken' => $nexToken,
                'orderItems' => $orderItems,
            ]);
            if (null === $nexToken) {
                break;
            }

            sleep(2);
        }
    }

    public function actionDumpFinancialEventsFromDb(
        string $sellerId,
        string $amazonOrderId,
        string $dateStart,
        string $dateEnd
    ) {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);

        $eventPeriodsQuery = EventPeriod::find()->where([
            'AND',
            ['>=', 'start_date', (new \DateTime($dateStart))->modify('-2 days')->setTime(0, 0, 0)->format('c')],
            ['<=', 'finish_date', (new \DateTime($dateEnd))->modify('+2 days')->setTime(23, 59, 59)->format('c')],
        ]);
        $eventTables = EventPeriod::getEventTables();
        $countPeriods = $eventPeriodsQuery->count();
        $countProcessed = 0;
        Console::startProgress($countProcessed, $countPeriods, 'Processed event periods');

        /** @var EventPeriod[] $eventPeriods */
        foreach ($eventPeriodsQuery->batch(500) as $eventPeriods) {
            foreach ($eventPeriods as $eventPeriod) {
                foreach ($eventTables as $tableName) {
                    $query2 = (new Query())->from($tableName)->where([
                        'event_period_id' => $eventPeriod->id,
                    ]);

                    foreach ($query2->batch(100, $dbManager->getFinanceDb()) as $financialEvents) {
                        foreach ($financialEvents as $financialEvent) {
                            if (empty($financialEvent['AmazonOrderId'])
                                || $financialEvent['AmazonOrderId'] !== $amazonOrderId
                            ) {
                                continue;
                            }

                            print_r([
                                'eventPeriod' => $eventPeriod->toArray(),
                                'financialEvent' => $financialEvent,
                            ]);
                        }
                    }
                }
                $countProcessed++;
                Console::updateProgress($countProcessed, $countPeriods, 'Processed event periods');
            }
        }
    }

    public function actionAmazonOrderInfo(string $sellerId, string $amazonOrderId)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $ordersApi = new OrdersApi($seller->id, $seller->region);

        $ordersApi->getOrder($amazonOrderId);

        $result =  $ordersApi->getOrder($amazonOrderId);
        $order = json_encode($result->getPayload(), JSON_PRETTY_PRINT);
        $this->info([
            'order' => $order,
        ]);
    }

    /**
     * Used to re-check data from amazon API and compare with our data.
     *
     * @param string $sellerId
     * @param string|null $maxDate
     * @return void
     * @throws SellerNotFoundException
     * @throws \yii\db\Exception
     */
    public function actionReCheckEventPeriods(string $sellerId, string $maxDate = null)
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
        $seller = Seller::find()->where(['id' => $sellerId])->one();
        $financesApi = new FinancesApi($seller->id, $seller->region);
        $eventPeriods = EventPeriod::find()->cache(60)
            ->orderBy('start_date DESC')
            ->limit(500)
            ->asArray();

        if (!empty($maxDate)) {
            $eventPeriods->andWhere(['<=', 'start_date', $maxDate]);
        }
        $eventPeriods = $eventPeriods->all();

        $countPeriods = count($eventPeriods);
        $this->info([
            'countPeriods' => $countPeriods,
        ]);

        foreach ($eventPeriods as $eventPeriod) {
            $this->info(str_repeat('-', 100));
            $this->info([
                'id' => $eventPeriod['id'],
                'type' => $eventPeriod['type'],
                'start_date' => $eventPeriod['start_date'],
                'finish_date' => $eventPeriod['finish_date'],
            ]);
            $countShipment = Shipment::find()->where(['event_period_id' => $eventPeriod['id']])->count();
            $countRefund = Refund::find()->where(['event_period_id' => $eventPeriod['id']])->count();

            $this->info([
                'ourShipment' => $countShipment,
                'ourRefund' => $countRefund,
            ]);

            $this->info('Checking remote');
            $nexToken = null;

            $theirShipment = 0;
            $theirRefund = 0;

            while (true) {
                $result = null;
                while (true) {
                    try {
                        $params = [
                            100,
                            (new \DateTime($eventPeriod['start_date']))->format('c'),
                            (new \DateTime($eventPeriod['finish_date']))->format('c'),
                            $nexToken,
                        ];
                        $this->info("New request");
                        $result = $financesApi->listFinancialEvents(
                            $params[0],
                            $params[1],
                            $params[2],
                            $params[3],
                        );
                        break;
                    } catch (\Throwable $e) {
                        $this->info($e);
                        sleep(10);
                    }
                }

                $payload = $result->getPayload();
                $nexToken = $payload->getNextToken();
                $financialEvents = $payload->getFinancialEvents();
                $financialEvents = json_decode(json_encode($financialEvents), true);

                $theirShipment += count($financialEvents['ShipmentEventList']);
                $theirRefund += count($financialEvents['RefundEventList']);

                $this->info([
                    'ourShipment' => $countShipment,
                    'ourRefund' => $countRefund,
                    'theirShipment' => $theirShipment,
                    'theirRefund' => $theirRefund,
                ]);

                if (null === $nexToken) {
                    sleep(5);
                    break;
                }
                sleep(5);
            }

            $isOk = $countShipment === $theirShipment && $countRefund === $theirRefund;
            $this->info([
                'id' => $eventPeriod['id'],
                'RESULT' => str_repeat('|', 50) . ' ' . ($isOk ? 'OK' : 'INCONSISTENCY'),
                'ourShipment' => $countShipment,
                'ourRefund' => $countRefund,
                'theirShipment' => $theirShipment,
                'theirRefund' => $theirRefund,
            ]);
        }
    }

    /**
     * @uses "test/dump-financial-events-from-amazon-api"
     * @param string $sellerId
     * @param string $amazonOrderId
     * @param string $dateStart
     * @param string $dateEnd
     * @throws \yii\db\Exception
     */
    public function actionDumpFinancialEventsFromAmazonApi(
        string $sellerId,
        string $amazonOrderId,
        string $dateStart,
        string $dateEnd
    ) {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setSellerId($sellerId);
        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $financesApi = new FinancesApi($seller->id, $seller->region);
        $nexToken = null;

        $stats = [
            'shipments' => 0,
            'refunds' => 0,
        ];
        while (true) {
            $result = null;
            while (true) {
                try {
                    $params = [
                        100,
                        (new \DateTime($dateStart))->setTime(0, 0, 0)->format('c'),
                        (new \DateTime($dateEnd))->setTime(23, 59, 59)->format('c'),
                        $nexToken,
                    ];
                    $this->info("New request");
                    $this->info($params);
                    $result = $financesApi->listFinancialEvents(
                        $params[0],
                        $params[1],
                        $params[2],
                        $params[3],
                    );
                    break;
                } catch (\Throwable $e) {
                    $this->info($e);
                    sleep(1);
                }
            }

            $this->info("Next token {$nexToken}");
            $payload = $result->getPayload();
            $nexToken = $payload->getNextToken();
            $financialEvents = $payload->getFinancialEvents();
            $financialEvents = json_decode(json_encode($financialEvents), true);
            $collectedEvents = [];

            $stats['shipments'] += count($financialEvents['ShipmentEventList']);
            $stats['refunds'] += count($financialEvents['RefundEventList']);

//            foreach ($financialEvents as $financialEvent) {
//                if (empty($financialEvent['AmazonOrderId'])) {
//                    continue;
//                }
//
//                if ($financialEvent['AmazonOrderId'] !== $amazonOrderId) {
//                    continue;
//                }
//
//                $collectedEvents[] = $financialEvent;
//
//                if (count($collectedEvents) > 500) {
//                    $dumpCollectedEvents($collectedEvents);
//                    $collectedEvents = [];
//                }
//            }
            $this->info([
                $stats,
                (bool)$nexToken,
            ]);
//            $dumpCollectedEvents($collectedEvents);

            if (null === $nexToken) {
                break;
            }
            sleep(3);
        }
        $this->info('Finished');
    }

    public function actionProfitCalculator(
        int $customerId,
        string $dateStart,
        string $dateEnd,
        string $currencyId = 'EUR',
        string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY,
        int $maxDepth = 1
    ) {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($customerId);

        $filtersForm = new FiltersForm();
        $filtersForm->dateStart = $dateStart;
        $filtersForm->dateEnd = $dateEnd;
        $filtersForm->currencyId = $currencyId;
        $filtersForm->validate();

        $calculator = new ProfitCalculator($filtersForm);
        $res = $calculator->calc($periodType, $maxDepth);
        print_r($res);
    }

    public function actionCompareMetrics(
        int $customerId,
        string $dateStart,
        string $dateEnd,
        string $sellerId,
        string $marketplaceId = null
    ) {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($customerId);

        $filtersForm = new FiltersForm();
        $filtersForm->dateStart = $dateStart;
        $filtersForm->dateEnd = $dateEnd;
        $filtersForm->marketplaceId = $marketplaceId;
        $filtersForm->sellerId = $sellerId;
        $filtersForm->validate();

        $unitsCalculator = new UnitsCalculator($filtersForm);
        $profitCalculator = new ProfitCalculator($filtersForm);

        $unitsResult = $unitsCalculator->calc();
        $profitResult = $profitCalculator->calc();

        $seller = Seller::find()->where(['id' => $sellerId])->one(\Yii::$app->db);
        $ordersApi = new OrdersApi($seller->id, $seller->region);
        $nexToken = null;
        $amazonOrders = 0;
        $amazonOrderItems = 0;
        $amazonRevenue = 0;

        while (true) {
            $result = $ordersApi->getOrders(
                [$marketplaceId],
                (new \DateTime($filtersForm->dateStart))->format('Y-m-d\TH:i:s'),
                (new \DateTime($filtersForm->dateEnd))->format('Y-m-d\TH:i:s'),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                $nexToken

            );
            $payload = $result->getPayload();
            $orders = $payload->getOrders();

            $nexToken = $payload->getNextToken();

            foreach ($orders as $order) {
                $amazonOrders++;
                $amazonOrderItems += $order->getNumberOfItemsShipped() + $order->getNumberOfItemsUnshipped();
                $amazonRevenue += $order->getOrderTotal() ? $order->getOrderTotal()->getAmount() : 0;
            }

            if (null === $nexToken) {
                break;
            }

            $this->info("Next query in 1 second");
            sleep(1);
        }

        $result = [
            'our' => [
                'orders' => $unitsResult->orders,
                'units' => $unitsResult->units,
                'profit' => $profitResult->netProfit,
                'revenue' => $profitResult->revenueAmount,
                'expenses' => $profitResult->expensesAmount,
            ],
            'amazon' => [
                'orders' => $amazonOrders,
                'units' => $amazonOrderItems,
                'profit' => 'N/A',
                'revenue' => 'N/A',
                'expenses' => 'N/A',
            ],
        ];

        print_r($result);
    }

    public function actionUnitsCalculator(
        int $customerId,
        string $dateStart,
        string $dateEnd
    ) {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $dbManager->setCustomerId($customerId);

        $filtersForm = new FiltersForm();
        $filtersForm->dateStart = $dateStart;
        $filtersForm->dateEnd = $dateEnd;
        $filtersForm->validate();

        $calculator = new UnitsCalculator($filtersForm);
        $res = $calculator->calc();
        print_r($res);
    }

    public function actionCheckSellerId(
        $fromMainTable = false,
        $customerId = null
    ) {
        $sellers = Seller::find()->orderBy('customer_id ASC');
        $allCustomersIDsMappedBySellerIds = ArrayHelper::map(Seller::find()->all(), 'id', 'customer_id');

        if (!empty($customerId)) {
            if (false !== strpos($customerId, '-')) {
                $fromIdToId = explode('-', $customerId);
                sort($fromIdToId);

                $sellers->where([
                    'AND',
                    ['>=', 'customer_id', $fromIdToId[0]],
                    ['<=', 'customer_id', $fromIdToId[1]],
                ]);
            } else {
                $sellers->where([
                    'customer_id' => explode(',', $customerId),
                ]);
            }
        }

        $sellers = $sellers->all();
        $countAllSellers = count($sellers);

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        /** @var Seller $seller */
        foreach ($sellers as $k => $seller) {
            try {
                if (($k+1) % 500 === 0) {
                    $this->info(str_repeat('-', 50));
                    $this->info(sprintf(
                        "Check %s from %s sellers in total",
                        $k + 1,
                        $countAllSellers,
                    ));
                }
                if (isset($sellers[$k-1]) && $sellers[$k-1]->customer_id === $seller->customer_id)
                    continue;

                $dbManager->setSellerId($seller->id);

                /** @var TableNameGenerator $tableNameGenerator */
                $tableNameGenerator = \Yii::$container->get('tableNameGenerator');
                $tableName = $tableNameGenerator->generate($fromMainTable ? DbStructure::TABLE_TRANSACTION : DbStructure::TABLE_TRANSACTION_DIST);

                $sellerIds = $dbManager
                    ->getClickhouseCustomerDb()
                    ->createCommand("select distinct SellerId from {$tableName}")
                    ->queryColumn();

                foreach ($sellerIds as $sellerId) {
                    if (isset($allCustomersIDsMappedBySellerIds[$sellerId]) && (int)$allCustomersIDsMappedBySellerIds[$sellerId] !== (int)$seller->customer_id){
                        $this->info(sprintf(
                            "%s: %s from customer %s",
                            $seller->customer_id,
                            $sellerId,
                            $allCustomersIDsMappedBySellerIds[$sellerId],
                        ));
                    }
                }

            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function actionRedisTest() {
        $i = 0;
        $cacheKey = 'i_value';
        do {

            \Yii::$app->cache->set($cacheKey, $i);

            $iCached = (int)\Yii::$app->cache->get($cacheKey);
            if ($i === $iCached){
                $this->info($i.': Read successfully');
            }else{
                $this->info($i.': Read failed, value '.$iCached);
            }

            $i++;
            sleep(1);
        }while($i < 100);
    }
}
