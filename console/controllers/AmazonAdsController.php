<?php

namespace console\controllers;

use AmazonAdvertisingApi\Regions;
use common\components\amazonAds\api\ClientBuilder;
use common\components\amazonAds\CostsApplier;
use common\components\customerConfig\CustomerConfig;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\models\ads\AmazonAdsAccount;
use common\models\ads\AmazonAdsCampaign;
use common\models\ads\AmazonAdsProfile;

class AmazonAdsController extends BaseController
{
    use LogToConsoleTrait;

    public ClientBuilder $clientBuilder;
    public MessagesSender $messagesSender;
    protected CustomerConfig $customerConfig;

    public function __construct($id, $module, $config = [])
    {
        $this->clientBuilder = new ClientBuilder();
        $this->messagesSender = new MessagesSender();
        $this->customerConfig = \Yii::$container->get('customerConfig');
        parent::__construct($id, $module, $config);
    }

    public function actionApplyCosts(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }
        $dbManager = $this->dbManager;

        $this->info('Sending ppc cost customer to queue');

        $costsApplier = new CostsApplier();

        foreach ($dbManager->iterateAnalyticActiveSchemas('ads', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $dates = $costsApplier->getDatesNotMovedClickhouseCosts($dbManager->getCustomerId());

                foreach ($dates as $date) {
                    $this->messagesSender->ppcCostApply($dbManager->getCustomerId(), $date);
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSyncProfiles(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->info("Syncing profiles for customer" . $this->dbManager->getCustomerId());
                /** @var AmazonAdsAccount[] $amazonAdsAccounts */
                $amazonAdsAccounts = AmazonAdsAccount::find()->all();

                foreach ($amazonAdsAccounts as $amazonAdsAccount) {
                    if (!$amazonAdsAccount->canMakeRequestToAmazon()) {
                        continue;
                    }

                    $regions = array_keys((new Regions())->endpoints);

                    foreach ($regions as $region) {
                        try {
                            $this->info("Syncing profiles for account {$amazonAdsAccount->id} in region {$region}");

                            $apiClient = $this->clientBuilder->getApiClient($amazonAdsAccount->id, $region);
                            $response = $apiClient->listProfiles();
                            $profiles = json_decode($response['response'], true);

                            $profilesToSave = [];
                            $profileIds = [];

                            foreach ($profiles as $profile) {
                                // 429 error for example, instead of array
                                if (!is_array($profile)) {
                                    continue;
                                }

                                $profilesToSave[] = [
                                    'id' => $profile['profileId'],
                                    'account_id' => $amazonAdsAccount->id,
                                    'region' => $region,
                                    'marketplace_id' => $profile['accountInfo']['marketplaceStringId'],
                                    'seller_id' => $profile['accountInfo']['id'],
                                    'daily_budget' => (float)($profile['dailyBudget']),
                                    'country_code' => $profile['countryCode'],
                                    'currency_code' => $profile['currencyCode'],
                                    'timezone' => $profile['timezone'],
                                ];
                                $profileIds[] = $profile['profileId'];
                            }

                            if (!empty($profilesToSave)) {
                                $sql = $this
                                    ->dbManager
                                    ->getCustomerDb()
                                    ->createCommand()
                                    ->batchInsert(
                                        AmazonAdsProfile::tableName(),
                                        array_keys(array_values($profilesToSave)[0]),
                                        $profilesToSave
                                    )
                                    ->getRawSql();
                                $sql .= ' ON CONFLICT (id) DO NOTHING';
                                $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
                            }

                            AmazonAdsProfile::deleteAll([
                                'and',
                                ['=', 'region', $region],
                                ['=', 'account_id', $amazonAdsAccount->id],
                                ['not in', 'id', $profileIds]
                            ]);
                        } catch (\Throwable $e) {
                            $this->error($e);
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSyncCampaigns(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $dbManager = $this->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->info("Syncing profiles for customer" . $this->dbManager->getCustomerId());
                /** @var AmazonAdsAccount[] $amazonAdsAccounts */
                $amazonAdsAccounts = AmazonAdsAccount::find()->all();

                foreach ($amazonAdsAccounts as $amazonAdsAccount) {
                    if (!$amazonAdsAccount->canMakeRequestToAmazon()) {
                        continue;
                    }

                    /** @var AmazonAdsProfile[] $profiles */
                    $profiles = AmazonAdsProfile::find()->all();

                    foreach ($profiles as $profile) {
                        try {
                            $this->info("Syncing campaigns for profile {$profile->id} in region {$profile->region}");
                            $apiClient = $this->clientBuilder->getApiClient($amazonAdsAccount->id, $profile->region);
                            $apiClient->profileId = $profile->id;
                            $campaignsToSave = [];

                            $spCampaigns = $apiClient->listSpCampaigns();
                            $sdCampaigns = $apiClient->listSdCampaigns();
                            $sbCampaigns = $apiClient->listSbCampaigns();

                            $campaigns = array_merge($spCampaigns, $sdCampaigns, $sbCampaigns);
                            foreach ($campaigns as $campaign) {
                                $campaignsToSave[] = [
                                    'id' => $campaign['campaignId'],
                                    'name' => $campaign['name'],
                                    'profile_id' => $profile->id,
                                ];
                            }

                            if (empty($campaignsToSave)) {
                                continue;
                            }

                            $sql = $this
                                ->dbManager
                                ->getCustomerDb()
                                ->createCommand()
                                ->batchInsert(
                                    AmazonAdsCampaign::tableName(),
                                    array_keys(array_values($campaignsToSave)[0]),
                                    $campaignsToSave
                                )
                                ->getRawSql()
                            ;
                            $sql .= ' ON CONFLICT (id) DO NOTHING';
                            $this->dbManager->getCustomerDb()->createCommand($sql)->execute();
                        } catch (\Throwable $e) {
                            $this->error($e);
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }
}
