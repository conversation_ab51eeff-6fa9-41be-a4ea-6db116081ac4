<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\dbStructure\BlueGreenBuilder;
use common\components\LogToConsoleTrait;
use common\models\Seller;

class BlueGreenTablesController extends BaseController
{
    use LogToConsoleTrait;

    protected BlueGreenBuilder $blueGreenBuilder;
    protected DbManager $dbManager;

    public function __construct($id, $module, $config = [])
    {
        /** @var BlueGreenBuilder blueGreenBuilder */
        $this->blueGreenBuilder = \Yii::$container->get('blueGreenBuilder');
        $this->dbManager = \Yii::$app->dbManager;

        parent::__construct($id, $module, $config);
    }

    public function actionFlip(string $customerIds  = null)
    {
        foreach ($this->iterateCustomerIds($customerIds) as $customerId) {
            try {
                $this->blueGreenBuilder->flip();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function actionRemoveBlue(string $customerIds  = null)
    {
        foreach ($this->iterateCustomerIds($customerIds) as $customerId) {
            try {
                $this->blueGreenBuilder->removeBlue();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function actionBuildBlue(string $changedTables, string $customerIds  = null)
    {
        $description = $this->prompt('Description:') ?: 'Generated manually from console';
        $changedTables = explode(',', $changedTables);
        $changedTables = array_map('trim', $changedTables);
        foreach ($this->iterateCustomerIds($customerIds) as $customerId) {
            try {
                $this->blueGreenBuilder->buildBlue($changedTables, $description);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    protected function iterateCustomerIds(string $customerIds = null): \Iterator
    {
        $query = Seller::find()->select('customer_id')->distinct();

        if (!empty($customerIds)) {
            $customerIds = explode(',', $customerIds);
            $customerIds = array_map('trim', $customerIds);
            $query->where([
                'in', 'customer_id', $customerIds
            ]);
        }

        /** @var Seller[] $sellers */
        foreach ($query->batch(500) as $sellers) {
            foreach ($sellers as $seller) {
                $this->dbManager->setCustomerId($seller->customer_id);
                yield $seller->customer_id;
            }
        }
    }
}
