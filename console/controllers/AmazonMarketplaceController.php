<?php

namespace console\controllers;

use common\models\AmazonMarketplace;
use SellerLogic\InternalApi\MainApiClient;
use SellerLogic\InternalApi\Message\ResponseTransformer;

class AmazonMarketplaceController extends BaseController
{
    public function actionSync()
    {
        /** @var MainApiClient $internalApiClient */
        $internalApiClient = \Yii::$container->get('internalApiClient');
        $response = $internalApiClient->amazonMarketplace()->index(['all' => 1]);
        $response = (new ResponseTransformer())->transform($response);

        foreach ($response as $marketplace) {
            $amazonMarketplace = AmazonMarketplace::find()->where([
                'id' => $marketplace['id']
            ])->one();

            if (empty($amazonMarketplace)) {
                $amazonMarketplace = new AmazonMarketplace();
                $amazonMarketplace->id = $marketplace['id'];
                $amazonMarketplace->title = $marketplace['title'];
                $amazonMarketplace->is_active = (bool)$marketplace['active'];
                $amazonMarketplace->ordering = $marketplace['ordering'];
            }

            $amazonMarketplace->amazon_mws_endpoint = $marketplace['amazon_mws_endpoint'];
            $amazonMarketplace->amazon_zone_id = $marketplace['amazon_zone_id'];
            $amazonMarketplace->country_code = $marketplace['country'];
            $amazonMarketplace->currency_code = $marketplace['currency'];
            $amazonMarketplace->sales_channel = $marketplace['sales_channel'];
            $amazonMarketplace->save(false);
        }
    }
}
