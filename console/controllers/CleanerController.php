<?php

namespace console\controllers;

use common\components\dataBuffer\BufferFactory;
use common\components\dbCleaner\Cleaner;
use common\components\LogToConsoleTrait;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use common\models\customer\clickhouse\Transaction;
use common\models\Seller;

class CleanerController extends BaseController
{
    use LogToConsoleTrait;

    public function actionClean(int $customerIdFrom = null, int $customerIdTo = null)
    {
        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $this->cleanExpiredTransactionBufferVersions($this->dbManager->getCustomerId());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    protected function cleanExpiredTransactionBufferVersions(int $customerId)
    {
        $this->info("Started cleaning old transaction versions " . $customerId);
        try {
            $dataBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();
            $dataBuffer->clean();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    public function actionCleanDev(int $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (YII_ENV === 'prod') {
            return;
        }

        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId()) || !$this->dbManager->shouldApplyDevRestrictions()) {
                    continue;
                }

                $minDate = (new \DateTime('-6 months'))->format('Y-m-d H:i:s');

                $tablesToClear = [
                    Transaction::tableName() => 'PostedDate',
                    AmazonOrder::tableName() => 'order_purchase_date',
                    AmazonOrderInProgress::tableName() => 'order_purchase_date',
                ];
                $clickhouseCustomerDb = $this->dbManager->getClickhouseCustomerDb();

                foreach ($tablesToClear as $tableName => $dateColumnName) {
                    $this->info(sprintf(
                        "Removing all records from %s table where %s < %s",
                        $tableName,
                        $dateColumnName,
                        $minDate
                    ));
                    $sql = "DELETE FROM $tableName WHERE $dateColumnName <= '$minDate'";
                    $this->info($sql);
                    $clickhouseCustomerDb->createCommand($sql)->execute();
                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $allSellers = Seller::find()
            ->select('id, customer_id, is_active, is_demo')
            ->distinct()
            ->asArray()
            ->all();

        $dbCleaner = new Cleaner();

        // Removing inactive or fake sellers
        foreach ($allSellers as $seller) {
            try {
                $isDemo = $seller['is_demo'];
                $isInactiveDemo = $isDemo && !$seller['is_active'];
                $isFakeSeller = strlen($seller['id']) <= 10 && !$isDemo;

//                if ($isFakeSeller || $isInactiveDemo) {
//                    $dbCleaner->removeSeller($seller['id'], $seller['customer_id']);
//                }
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }
}
