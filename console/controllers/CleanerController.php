<?php

namespace console\controllers;

use common\components\customerConfig\CustomerConfig;
use common\components\dataBuffer\BufferFactory;
use common\components\LogToConsoleTrait;
use common\components\transactionBuffer\TransactionBufferCleaner;

class CleanerController extends BaseController
{
    use LogToConsoleTrait;

    public function actionClean(int $customerIdFrom = null, int $customerIdTo = null)
    {
        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $this->cleanExpiredTransactionBufferVersions($this->dbManager->getCustomerId());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    protected function cleanExpiredTransactionBufferVersions(int $customerId)
    {
        $this->info("Started cleaning old transaction versions " . $customerId);
        try {
            $dataBuffer = (new BufferFactory())->getTransactionsToClickhouseBuffer();
            $dataBuffer->clean();
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }
}
