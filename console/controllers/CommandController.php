<?php

namespace console\controllers;

use common\components\cron\GeneralCronComponent;
use common\components\LogToConsoleTrait;
use common\models\Command;
use yii\console\Controller;
use yii\mutex\Mutex;

class CommandController extends Controller
{
    use LogToConsoleTrait;

    protected Mutex $mutex;

    const COMMAND_BATCH_LIMIT = 1;
    const SLEEP_SECONDS = 5;

    public function behaviors()
    {
        return [
        ];
    }

    public function __construct($id, $module, $config = [])
    {
        $this->mutex = \Yii::$app->mutex;
        parent::__construct($id, $module, $config);
    }

    public function actionCreate(string $cmdCommand, int $maxRetries = 1, bool $checkAlreadyInProgress = false)
    {
        $lockKey = implode('_', $_SERVER['argv']);
        if (false === $this->mutex->acquire($lockKey)) {
            $this->info('Command is already running in another process');
            return false;
        }

        Command::create($cmdCommand, $maxRetries, $checkAlreadyInProgress);

        $this->mutex->release($lockKey);
    }

    public function actionProcess()
    {
        $start = time();
        $duration = 30 * 60 - 10;

        do {
            $toProcessCommands = Command::find()
                ->where(['status' => Command::STATUS_NEW])
                ->limit(self::COMMAND_BATCH_LIMIT)
                ->cache(-1)
                ->all(\Yii::$app->db);

            $countToProcess = count($toProcessCommands);
            $this->info("Found {$countToProcess} commands to process");


            if (0 === $countToProcess) {
                $this->info("Sleep " . self::SLEEP_SECONDS);
                sleep(self::SLEEP_SECONDS);
            }

            foreach ($toProcessCommands as $toProcessCommand) {
                try {
                    $this->processCommand($toProcessCommand);
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
            sleep(1);
        } while (time() - $start < $duration);
    }

    private function processCommand(Command $command)
    {
        $this->info("Processing command " . $command->getInlineCommand());
        $command->markQueued();
        /** @var GeneralCronComponent $cronComponent */
        $cronComponent = \Yii::$app->cronComponent;
        $cronComponent->addCommand($command->getInlineCommand());
    }
}
