<?php

namespace console\controllers\migrate;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use yii\console\controllers\MigrateController;

class RepricerEventMigrateController extends MigrateController
{
    public $db = 'repricerEventDb';
    public $dbType = HelperFactory::TYPE_REPRICER_EVENT_POSTGRESS;
    public $nodeId = 0;

    use MultiSchemaMigrateTrait;
    public function getSchemaPrefix(): string
    {
        return DbManager::DB_PREFIX_REPRICER_EVENT;
    }

}
