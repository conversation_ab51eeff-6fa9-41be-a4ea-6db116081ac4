<?php

namespace console\controllers\migrate;

use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;

class ClickhouseMigrateCustomerController extends BaseClickhouseMigrateController
{
    use MultiDatabaseMigrateTrait;

    public function getDatabaseType(): string
    {
        return HelperFactory::TYPE_CLICKHOUSE;
    }

    public function getDbPrefix(): string
    {
        return DbManager::DB_PREFIX_CUSTOMER;
    }
}
