<?php

namespace console\controllers\migrate;

use common\components\core\db\clickhouse\Command;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\db\ClickhouseDbHelper;
use common\models\CustomerProcess;
use yii\console\ExitCode;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Console;

trait MultiDatabaseMigrateTrait
{
    protected ?int $customerProcessId = null;

    protected DbManager $dbManager;

    abstract protected function getDbPrefix(): string;
    abstract protected function getDatabaseType(): string;

    public function __construct($id, $module, $config = [])
    {
        /** @var DbManager $dbManager */
        $this->dbManager = \Yii::$app->dbManager;
        parent::__construct($id, $module, $config);
    }

    /**
     * @param $actionID
     * @return string[]
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'customerProcessId'
        ]);
    }

    /**
     * {@inheritDoc}
     */
    public function actionUp($limit = 0, int $customerIdFrom = null, int $customerIdTo = null)
    {
        $this->stdout("Multiple database migrations has been started\n", Console::FG_YELLOW);

        if (!empty($customerIdFrom) && empty($customerIdTo)) {
            $customerIdTo = $customerIdFrom + 1;
        }

        if (!empty($this->customerProcessId)) {
            $customerProcess = CustomerProcess::findOne($this->customerProcessId);
        }

        Command::$isNodeChangingEnabled = false;
        try {
            foreach ($this->dbManager->iterateDb($this->getDbPrefix(), $this->getDatabaseType(), $customerIdFrom, $customerIdTo) as $databaseInfo) {
                try {
                    $this->stdout(sprintf(
                        "Applying migrations for database %s on host %s\n",
                        $databaseInfo['dbName'],
                        $databaseInfo['host'],
                    ), Console::FG_YELLOW);

                    $this->migrationTable = $this->dbManager->getClickhouseDbName($this->getDbPrefix()) . '.' . 'migration';
                    $this->db = $this->dbManager->getDb($this->getDbPrefix(), HelperFactory::TYPE_CLICKHOUSE, $databaseInfo['host']);

                    $result = parent::actionUp($limit);

                    if ($result === ExitCode::UNSPECIFIED_ERROR) {
                        throw new \Exception("Failed to migrate");
                    }
                } catch (\Throwable $e) {
                    if (false === strpos($e->getMessage(), ClickhouseDbHelper::UNKNOWN_DATABASE_ERROR_IDENTIFIER)
                        || $this->getDatabaseType() !== HelperFactory::TYPE_CLICKHOUSE
                    ) {
                        throw $e;
                    }

                    $this->stdout("No customer database on this node, skipped\n", Console::FG_RED);
                }
            }

            if (!empty($customerProcess)) {
                $customerProcess->incrementSuccessEvents();
            }
        } catch (\Throwable $e) {
            if (!empty($customerProcess)) {
                $customerProcess->setFailed($e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function getMigrationHistory($limit)
    {
        if (!$this->db->createCommand("exists table {$this->migrationTable}")->queryScalar()) {
            $this->createMigrationHistoryTable();
        }
        $query = (new Query())
            ->select(['version', 'apply_time'])
            ->from($this->migrationTable)
            ->orderBy(['apply_time' => SORT_DESC, 'version' => SORT_DESC]);

        if (empty($this->migrationNamespaces)) {
            $query->limit($limit);
            $rows = $query->all($this->db);
            $history = ArrayHelper::map($rows, 'version', 'apply_time');
            unset($history[self::BASE_MIGRATION]);
            return $history;
        }

        $rows = $query->all($this->db);

        $history = [];
        foreach ($rows as $key => $row) {
            if ($row['version'] === self::BASE_MIGRATION) {
                continue;
            }
            if (preg_match('/m?(\d{6}_?\d{6})(\D.*)?$/is', $row['version'], $matches)) {
                $time = str_replace('_', '', $matches[1]);
                $row['canonicalVersion'] = $time;
            } else {
                $row['canonicalVersion'] = $row['version'];
            }
            $row['apply_time'] = (int) $row['apply_time'];
            $history[] = $row;
        }

        usort($history, function ($a, $b) {
            if ($a['apply_time'] === $b['apply_time']) {
                if (($compareResult = strcasecmp($b['canonicalVersion'], $a['canonicalVersion'])) !== 0) {
                    return $compareResult;
                }

                return strcasecmp($b['version'], $a['version']);
            }

            return ($a['apply_time'] > $b['apply_time']) ? -1 : +1;
        });

        $history = array_slice($history, 0, $limit);

        $history = ArrayHelper::map($history, 'version', 'apply_time');

        return $history;
    }

    public function actionDown($limit = 1, int $customerIdFrom = null, int $customerIdTo = null)
    {
        $this->stdout("Multiple database migrations has been started\n", Console::FG_YELLOW);

        if (!empty($customerIdFrom) && empty($customerIdTo)) {
            $customerIdTo = $customerIdFrom + 1;
        }

        foreach ($this->dbManager->iterateDb($this->getDbPrefix(), $this->getDatabaseType(), $customerIdFrom, $customerIdTo) as $databaseInfo) {
            $this->stdout(sprintf(
                "Reverting migrations for database %s on host %s\n",
                $databaseInfo['dbName'],
                $databaseInfo['host'],
            ), Console::FG_YELLOW);
            $this->migrationTable = $this->dbManager->getClickhouseDbName($this->getDbPrefix()) . '.' . 'migration';
            $this->db = $this->dbManager->getDb($this->getDbPrefix(), HelperFactory::TYPE_CLICKHOUSE, $databaseInfo['host']);

            $result = parent::actionDown($limit);

            if ($result === ExitCode::UNSPECIFIED_ERROR) {
                throw new \Exception("Failed to migrate");
            }
        }
    }
}
