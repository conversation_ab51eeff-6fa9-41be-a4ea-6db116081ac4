<?php

namespace console\controllers\migrate;

use common\components\core\db\Connection;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use yii\console\ExitCode;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Console;

trait MultiSchemaMigrateTrait
{
    protected DbManager $dbManager;

    abstract protected function getSchemaPrefix(): string;

    public function __construct($id, $module, $config = [])
    {
        /** @var DbManager $dbManager */
        $this->dbManager = \Yii::$app->dbManager;
        parent::__construct($id, $module, $config);

        \Yii::$app->db->enableSlaves = false;
    }

    /**
     * {@inheritDoc}
     */
    public function actionUp($limit = 0, int $customerIdFrom = null, int $customerIdTo = null)
    {
        $this->stdout("Multiple schema migrations has been started\n", Console::FG_YELLOW);

        if (!empty($customerIdFrom) && empty($customerIdTo)) {
            $customerIdTo = $customerIdFrom + 1;
        }
        $dbType = property_exists($this, 'dbType') ? $this->dbType : HelperFactory::TYPE_POSTGRESS_SHARD;
        $nodeId = property_exists($this, 'nodeId') ? $this->nodeId : null;
        foreach ($this->dbManager->iterateSchemas($this->getSchemaPrefix(), $customerIdFrom, $customerIdTo, $nodeId, $dbType) as $schemaInfo) {
            $this->stdout(sprintf(
                "Applying migrations for schema %s\n",
                $schemaInfo['schema']
            ), Console::FG_YELLOW);

            /** @var Connection $db */
            $helper = (new HelperFactory())->getHelper($dbType);
            $this->db = $helper->getBaseConnection($schemaInfo['dbIndex']);
            $this->db->schemaName = $schemaInfo['schema'];
            $this->migrationTable = $schemaInfo['schema'].'.'.'migration';
            $result = parent::actionUp($limit);
            $this->db->schemaName = null;

            if ($result === ExitCode::UNSPECIFIED_ERROR) {
                throw new \Exception("Failed to migrate");
            }
        }
    }

    public function actionDown($limit = 1, int $customerIdFrom = null, int $customerIdTo = null)
    {
        $this->stdout("Multiple schema migrations has been started\n", Console::FG_YELLOW);

        $dbType = property_exists($this, 'dbType') ? $this->dbType : HelperFactory::TYPE_POSTGRESS_SHARD;
        $nodeId = property_exists($this, 'nodeId') ? $this->nodeId : null;
        foreach ($this->dbManager->iterateSchemas($this->getSchemaPrefix(), $customerIdFrom, $customerIdTo, $nodeId, $dbType) as $schemaInfo) {
            $this->stdout("Reverting down migrations for database {$schemaInfo['schema']} \n", Console::FG_YELLOW);

            /** @var Connection $db */
            $helper = (new HelperFactory())->getHelper($dbType);
            $this->db = $helper->getBaseConnection($schemaInfo['dbIndex']);
            $this->db->schemaName = $schemaInfo['schema'];
            $this->migrationTable = $schemaInfo['schema'].'.'.'migration';

            $result = parent::actionDown($limit);
            $this->db->schemaName = null;

            if ($result === ExitCode::UNSPECIFIED_ERROR) {
                throw new \Exception("Failed to migrate");
            }
        }
    }

    protected function getMigrationHistory($limit)
    {
        try {
            $this->db->createCommand("SELECT 1 FROM {$this->migrationTable}")->queryScalar();
        } catch (\Throwable $e) {
            if ($e->getCode() === '42P01') {
                $this->createMigrationHistoryTable();
            } else {
                throw $e;
            }
        }

        $query = (new Query())
            ->select(['version', 'apply_time'])
            ->from($this->migrationTable)
            ->orderBy(['apply_time' => SORT_DESC, 'version' => SORT_DESC]);

        if (empty($this->migrationNamespaces)) {
            $query->limit($limit);
            $rows = $query->all($this->db);
            $history = ArrayHelper::map($rows, 'version', 'apply_time');
            unset($history[self::BASE_MIGRATION]);
            return $history;
        }

        $rows = $query->all($this->db);

        $history = [];
        foreach ($rows as $key => $row) {
            if ($row['version'] === self::BASE_MIGRATION) {
                continue;
            }
            if (preg_match('/m?(\d{6}_?\d{6})(\D.*)?$/is', $row['version'], $matches)) {
                $time = str_replace('_', '', $matches[1]);
                $row['canonicalVersion'] = $time;
            } else {
                $row['canonicalVersion'] = $row['version'];
            }
            $row['apply_time'] = (int) $row['apply_time'];
            $history[] = $row;
        }

        usort($history, function ($a, $b) {
            if ($a['apply_time'] === $b['apply_time']) {
                if (($compareResult = strcasecmp($b['canonicalVersion'], $a['canonicalVersion'])) !== 0) {
                    return $compareResult;
                }

                return strcasecmp($b['version'], $a['version']);
            }

            return ($a['apply_time'] > $b['apply_time']) ? -1 : +1;
        });

        $history = array_slice($history, 0, $limit);

        $history = ArrayHelper::map($history, 'version', 'apply_time');

        return $history;
    }
}
