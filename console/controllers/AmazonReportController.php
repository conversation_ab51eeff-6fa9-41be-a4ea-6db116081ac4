<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\exception\SellerNotFoundException;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\dataProvider\DataProviderFactory;
use common\components\reports\ReadyReportsProcessor;
use common\components\reports\ReportsService;
use common\components\reports\ReportsSynchronizer;
use common\models\customer\AmazonReport;
use common\models\Seller;

class AmazonReportController extends BaseController
{
    use LogToConsoleTrait;

    protected ReportsService $reportsService;
    protected ReportsSynchronizer $reportsSynchronizer;
    protected MessagesSender $messagesSender;
    protected DbManager $dbManager;

    public function __construct($id, $module, $config = [])
    {
        $this->reportsService = new ReportsService();
        $this->messagesSender = new MessagesSender();
        $this->reportsSynchronizer = new ReportsSynchronizer();
        $this->dbManager = \Yii::$app->dbManager;
        parent::__construct($id, $module, $config);
    }

    public function actionGetUrl(int $customerId, int $reportId)
    {
        $this->dbManager->setCustomerId($customerId);
        $amazonReport = AmazonReport::findOne($reportId);
        if (empty($amazonReport)) {
            $this->error('Report not found');
            return;
        }

        $providerFactory = new DataProviderFactory();
        $dataProvider = $providerFactory->getDataProvider($amazonReport->data_provider);

        $this->info([
            'url' => $dataProvider->getUrl($amazonReport, $dataProvider)
        ]);
    }

    public function actionCreateReportsInAmazon(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->iterateSellers($customerIdFrom, $customerIdTo) as $seller) {
            try {
                $this->reportsService->createReportsInAmazonToQueue($seller->id);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionRenewTerminated(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        foreach ($dbManager->iterateAnalyticActiveSchemas('customer',$customerIdFrom, $customerIdTo) as $schema) {
            try {
                AmazonReport::updateAll([
                    'status' => AmazonReport::STATUS_WAITING,
                    'updated_at' => date('Y-m-d H:i:s'),
                ], [
                    'AND',
                    ['=', 'status', AmazonReport::STATUS_QUEUED],
                    ['<=', 'updated_at', date('Y-m-d H:i:s', strtotime('-1 hour'))],
                ]);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSendReadyToQueue(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->iterateSellers($customerIdFrom, $customerIdTo) as $seller) {
            try {
                $this->reportsService->sendReadyToQueue($seller->id, AmazonReport::TYPE_REFRESH);
                $this->reportsService->sendReadyToQueue($seller->id, AmazonReport::TYPE_INIT);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionGenerateOngoingPeriods(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }
        $currTime = new \DateTime();

        foreach ($this->iterateSellers($customerIdFrom, $customerIdTo) as $seller) {
            try {
                $this->reportsService->generateOngoingPeriods($seller->id, $currTime);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionReInitToUpdateChangedData(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->reportsService->reInitToUpdateChangedData();
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    public function actionSyncStatuses(string $customerIdFrom = null, int $customerIdTo = null): void
    {
        if (!$this->lock()) {
            return;
        }

        foreach ($this->iterateSellers($customerIdFrom, $customerIdTo) as $seller) {
            try {
                $this->messagesSender->handleSyncAmazonReportStatuses($seller->id);
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    protected function iterateSellers(string $customerIdFrom = null, int $customerIdTo = null): \Iterator
    {
        foreach ($this->dbManager->iterateAnalyticActiveSchemas('finance', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->dbManager->setSellerId($this->dbManager->getSellerId());
            } catch (SellerNotFoundException $e) {
                continue;
            }
            $seller = $this->dbManager->getSeller();

            if (empty($seller) || !$seller->canMakeRequestToAmazon()) {
                continue;
            }
            yield $seller;
        }
    }

    public function actionHandle(int $customerId, int $reportId)
    {
        $this->dbManager->setCustomerId($customerId);
        $amazonReport = AmazonReport::findOne($reportId);
        $this->dbManager->setSellerId($amazonReport->seller_id);

        $readyReportProcessor = new ReadyReportsProcessor();
        $processingResult = $readyReportProcessor->process($amazonReport);
        $this->info($processingResult);
    }
}
