<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\demo\DemoDataManager;
use common\components\LogToConsoleTrait;
use common\components\services\order\OrderItemsLoader;
use common\components\services\order\OrdersLoader;
use common\components\services\order\RenewTerminatedOrdersService;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\Seller;
use yii\helpers\ArrayHelper;
use yii\helpers\Console;

class OrdersController extends BaseController
{
    use LogToConsoleTrait;
    public const BATCH_SIZE = 200;
    public const TIME_LIMIT = 55;

    public const LOAD_ITEMS_SIZE = 30;
    public const SELLERS_BATCH_SIZE = 20;

    public function actionLoadInit(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $start = time();

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_order_init_periods_created' => true, 'is_order_init_periods_loaded' => false,  'is_db_created' => true, 'is_demo' => false]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
//            do{
                foreach ($query->batch(self::BATCH_SIZE) as $items) {
                    $this->info('Get batch');
                    $this->stdout(
                        'Get batch',
                        Console::FG_GREEN
                    );
                    /** @var Seller $seller */
                    foreach ($items as $seller) {
                        if ($seller->canMakeRequestToAmazon()) {
                            $this->info('canMakeRequestToAmazon ' . $seller->id);
                            $service = new OrdersLoader($seller);
                            $this->info('OrdersLoader');
                            $service->loadInit();
                            $this->info('loadInit');
                        }

//                        if (time() - $start >= self::TIME_LIMIT) {
//                            return ;
//                        }
                    }
                }
//            }while( time() - $start < self::TIME_LIMIT );

        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    public function actionLoadRefresh(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_demo' => false]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    if ($seller->canMakeRequestToAmazon()) {
                        $service = new OrdersLoader($seller);
                        $service->loadRefresh();
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->unlock();
    }

    public function actionLoadItems(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_demo' => false]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                foreach ($items as $seller) {
                    $loadItemsSize = strtolower($seller->id) === 'a2n58yccqnsunr' ? 60 : self::LOAD_ITEMS_SIZE;

                    if ($seller->canMakeRequestToAmazon()) {
                        $service = new OrderItemsLoader($seller);
                        $processingCount = $service->getProcessingCount();
                        if ($processingCount >= $loadItemsSize) {
                            continue;
                        }
                        $amazonOrderIds = $service->getLastUpdatedOrdersIds($loadItemsSize - $processingCount);

                        $service = new OrderItemsLoader($seller);
                        $service->loadBatch($amazonOrderIds);
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->unlock();
    }

    public function actionRenewTerminated(int $customerIdFrom = null, int $customerIdTo = null)
    {
        ini_set("memory_limit", "512M");

        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_demo' => false])->orderBy('customer_id');
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        foreach ($query->batch(self::BATCH_SIZE) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                try {
                    (new RenewTerminatedOrdersService($seller))->renew();
                } catch (\Throwable $e) {
                    $this->error($e);
                }
            }
        }
        $this->unlock();
    }


    public function actionDeleteDuplicate($customerIdFrom = null, $customerIdTo = null)
    {
        if (!empty($customerIdFrom) && empty($customerIdTo)) {
            $customerIdTo = $customerIdFrom + 1;
        }

        \Yii::$app->db->enableSlaves = false;

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('order', $customerIdFrom, $customerIdTo) as $schemaInfo) {

            $this->stdout(sprintf("Run commands for schema %s\n", $schemaInfo['schema']), Console::FG_YELLOW);

            $result = AmazonOrder::getDb()->createCommand(
                "SELECT amazon_order_id, COUNT(*) as count, max(updated_at), max(id) as max_id FROM {$schemaInfo['schema']}.amazon_order group by amazon_order_id having count(*) >1"
            )->queryAll();

            if (empty($result)) {
                continue;
            }

            foreach ($result as $double) {
                $this->stdout(sprintf("delete doubles for %s\n", $double['amazon_order_id']));

                AmazonOrder::getDb()->createCommand(
                    "DELETE FROM {$schemaInfo['schema']}.amazon_order WHERE amazon_order_id = :amazonOrderId AND updated_at < :updatedAt OR amazon_order_id = :amazonOrderId AND updated_at = :updatedAt AND id <> :maxId;"
                )->bindValues(['amazonOrderId' => $double['amazon_order_id'], 'updatedAt' =>  $double['max'], 'maxId' =>  $double['max_id']])->execute();
            }
        }
    }

    public function actionMigrateOrderItems(int $customerIdFrom = null, int $customerIdTo = null, $sleepSeconds = 60)
    {
        while (true) {
            $query = Seller::find()->where(['is_db_created' => true, 'is_data_migrated' => false]);

            if ($customerIdTo)
                $query->andWhere('customer_id < :to',[':to'=>$customerIdTo]);

            if ($customerIdFrom)
                $query->andWhere('customer_id >= :from',[':from'=>$customerIdFrom]);

            $query->orderBy('customer_id ASC');

            foreach ($query->batch(self::SELLERS_BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    try {
                        $this->dbManager->setSellerId($seller->id);
                        $clientDb = $this->dbManager->getRepricerCustomerClientDb();

                        $this->info("PROCESSING: customer {$seller->customer_id}; seller {$seller->id}");

                        $countOrderItemsInServiceDb = $clientDb->createCommand("
                                SELECT count(*)
                                FROM amazon_order_product
                            ")->queryScalar();

                        if (!$countOrderItemsInServiceDb){
                            $seller->is_data_migrated = true;
                            $seller->saveOrThrowException();
                            $this->info("Db is empty on the service");
                            continue;
                        }

                        $totalOrdersCount = AmazonOrder::find()
                            ->select(['amazon_order_id', 'marketplace_id'])
                            ->where(['items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_NEW])->count();

                        Console::startProgress(0, $totalOrdersCount);
                        $processed = 0;
                        $success = 0;
                        $empty = 0;

                        while (true) {
                            $orderIdMarketplaceIdMap = ArrayHelper::map(AmazonOrder::find()
                                ->select(['amazon_order_id', 'marketplace_id'])
                                ->where(['items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_NEW])
                                ->limit(self::BATCH_SIZE)
                                ->orderBy('last_update_date ASC')
                                ->asArray()
                                ->all(), 'amazon_order_id', 'marketplace_id');

                            if (empty($orderIdMarketplaceIdMap)) {
                                break;
                            }

                            $orderIds = "'" . implode('\', \'', array_keys($orderIdMarketplaceIdMap)) . "'";

                            $items = $clientDb->createCommand("
                                SELECT 
                                    order_id,
                                    asin,
                                    sku,
                                    order_item_id,
                                    title,
                                    quantity,
                                    quantity_shipped,
                                    gift_message_text,
                                    gift_wrap_level,
                                    item_price,
                                    shipping_price,
                                    gift_wrap_price,
                                    item_tax,
                                    shipping_tax,
                                    cift_wrap_tax,
                                    shipping_discount,
                                    promotion_discount,
                                    amazon_customer_account_id,
                                    cod_fee,
                                    cod_fee_discount,
                                    promotion_id,
                                    condition_id,
                                    condition_subtype_id,
                                    condition_note,
                                    scheduled_delivery_start_date,
                                    scheduled_delivery_end_date,
                                    amazon_product_id,
                                    profit,
                                    order_purchase_date
                                FROM amazon_order_product
                                WHERE order_id in ({$orderIds})
                            ")->queryAll();

                            $result = $this->saveOrdersItems($orderIdMarketplaceIdMap, $items);

                            $success = $success + $result['success'];
                            $empty = $empty + $result['empty'];
                            $processed = $processed + count($orderIdMarketplaceIdMap);
                            Console::updateProgress($processed, $totalOrdersCount);
                        }

                        Console::endProgress(true, PHP_EOL);
                        $seller->is_data_migrated = true;
                        $seller->saveOrThrowException();
                        $this->info("   success: {$success}, empty: {$empty}");
                    } catch (\Throwable $e) {
                        if ($e->getCode() === '42P01') {
                            $this->info('SQLSTATE[42P01]: Undefined table for seller ID ' . $seller->id);
                            continue;
                        }
                        $this->error($e);
                    }
                }
            }

            sleep($sleepSeconds);
        }
    }

    protected function saveOrdersItems(array $orderIdMarketplaceIdMap, array $items)
    {
        $date = date('Y-m-d H:i:s');

        $dataToSave = [];

        $notEmptyOrderIds = [];

        foreach ($items as $item) {
            $notEmptyOrderIds[] = $item['order_id'];


            $orderPurchaseDate = $item['order_purchase_date'];

            if (empty($orderPurchaseDate)) {
                /** @var AmazonOrder $amazonOrder */
                $amazonOrder = AmazonOrder::find()->where(['amazon_order_id' => $item['order_id']])->one();

                if (is_null($amazonOrder)) {
                    throw new \Exception("Amazon order ID {$item['order_id']} is not found");
                }
                $orderPurchaseDate = $amazonOrder->purchase_date;
            }

            $dataToSave[] = [
                'order_id' => $item['order_id'],
                'asin' => $item['asin'],
                'sku' => $item['sku'],
                'order_item_id' => $item['order_item_id'],
                'title' => $item['title'],
                'quantity' => $item['quantity'],
                'quantity_shipped' => $item['quantity_shipped'],
                'item_price' => $item['item_price'],
                'shipping_price' => $item['shipping_price'],
                'item_tax' => $item['item_tax'],
                'shipping_tax' => $item['shipping_tax'],
                'shipping_discount' => $item['shipping_discount'],
                'promotion_discount' => $item['promotion_discount'],
                'cod_fee' => $item['cod_fee'],
                'cod_fee_discount' => $item['cod_fee_discount'],
                'promotion_id' => $item['promotion_id'],
                'condition_id' => $item['condition_id'],
                'condition_subtype_id' => $item['condition_subtype_id'],
                'condition_note' => $item['condition_note'],
                'scheduled_delivery_start_date' => $item['scheduled_delivery_start_date'],
                'scheduled_delivery_end_date' => $item['scheduled_delivery_end_date'],
                'order_purchase_date' => $orderPurchaseDate,
                'order_marketplace_id' => $orderIdMarketplaceIdMap[$item['order_id']],
                'date' => $date,
                'profit' => null,
            ];
        }

        $notEmptyOrderIds = array_values(array_unique($notEmptyOrderIds));
        $emptyOrderIds = array_values(array_diff(array_values(array_keys($orderIdMarketplaceIdMap)), $notEmptyOrderIds));

        $transaction = AmazonOrderItem::getDb()->beginTransaction();

        try {
            if (!empty($dataToSave)) {
                $sql = AmazonOrderItem::getDb()
                    ->createCommand()
                    ->batchInsert(AmazonOrderItem::tableName(), array_keys(array_values($dataToSave)[0]), $dataToSave)
                    ->getRawSql();

                $sql .= ' ON CONFLICT (order_item_id) DO NOTHING';
                AmazonOrderItem::getDb()->createCommand($sql)->execute();
            }

            AmazonOrder::updateAll(['items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_FINISHED], ['amazon_order_id' =>  $notEmptyOrderIds]);
            AmazonOrder::updateAll(['items_loading_status' => AmazonOrder::ITEMS_LOADING_STATUS_TERMINATED], ['amazon_order_id' =>  $emptyOrderIds]);

            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return [
            'success' => count($notEmptyOrderIds),
            'empty' => count($emptyOrderIds)
        ];
    }
}


