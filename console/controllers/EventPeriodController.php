<?php

namespace console\controllers;

use common\components\core\db\dbManager\DbManager;
use common\components\LogToConsoleTrait;
use common\components\processManager\ProcessManager;
use common\components\rabbitmq\MessagesSender;
use common\components\services\financialEvent\EventPeriodService;
use common\components\services\financialEvent\PeriodsMerger;
use common\components\services\financialEvent\RenewTerminatedPeriodsService;
use common\models\finance\EventPeriod;
use common\models\Seller;
use yii\caching\CacheInterface;

class EventPeriodController extends BaseController
{
    use LogToConsoleTrait;

    public const BATCH_SIZE = 50;

    public function actionMergeParts(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $periodsMerger = new PeriodsMerger();

        foreach ($this->dbManager->iterateSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                continue;
                if (!in_array($this->dbManager->getCustomerId(), [700, 1373])) {
                    continue;
                }

                $periodsMerger->merge($this->dbManager->getCustomerId());
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }

        $this->unlock();
    }

    /**
     * @throws \Exception
     */
    public function actionGenerateInit(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true, 'is_init_periods_created' => false, 'is_db_created' => true]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    (new EventPeriodService($seller))->generateInitPeriods();
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    public function actionReExportFinishedToClickhouse(string $sellerId, int $fromId, int $toId)
    {
        /** @var ProcessManager $processManager */
        $processManager = \Yii::$app->processManager;
        $processKey = implode('_', [
            'event-period/re-export-finished-to-clickhouse',
            $sellerId,
            $fromId,
            $toId
        ]);
        $processManager->register($processKey);

        try {
            $this->info("Re-exporting finished event periods to clickhouse started");
            $this->dbManager->setSellerId($sellerId);

            $messagesSender = new MessagesSender();
            $eventPeriodQuery = EventPeriod::find();
            $eventPeriodQuery
                ->andWhere(['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED])
                ->andWhere([
                    'and',
                    ['>', 'id', $fromId],
                    ['<=', 'id', $toId]
                ])
                ->orderBy('id DESC');

            $eventPeriods = $eventPeriodQuery->all();

            if (count($eventPeriods) === 0) {
                return;
            }

            /** @var EventPeriod[] $eventPeriods */
            foreach ($eventPeriods as $eventPeriod) {
                $messagesSender->eventPeriodsFromCacheToClickhouse($eventPeriod, $sellerId);
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $processManager->release($processKey);
    }

    /**
     * @throws \Exception
     */
    public function actionGenerateRefresh(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()
            ->where(['is_analytic_active' => true, 'is_db_created' => true, 'is_init_periods_created' => true])
            ->andWhere(['OR', ['is_token_received' => true], ['is_demo' => true]]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    (new EventPeriodService($seller))->generateRefreshPeriod();
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    public function actionIsAllInitPeriodsLoaded()
    {
        foreach (Seller::find()->where(['is_analytic_active' => true, 'is_init_periods_created' => true])->batch(self::BATCH_SIZE) as $items) {
            /** @var Seller $seller */
            foreach ($items as $seller) {
                $this->info(sprintf('Check seller %s | Started', $seller->id));
                (new EventPeriodService($seller))->checkIsAllInitPeriodsLoaded();
                $this->info(sprintf('Check seller %s | Finished', $seller->id));
            }
        }
    }

    public function actionExportFinishedToClickhouse(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $rabbitMqMessagesSender = new MessagesSender();
        /** @var DbManager $sellerDbManager */
        $sellerDbManager = \Yii::$app->get('dbManager');
        $this->info("Exporting to clickhouse started");

        try {
            /** @var Seller $seller */
            foreach ($sellerDbManager->iterateAnalyticActiveSchemas('finance', $customerIdFrom, $customerIdTo) as $schemaInfo) {
                if (PeriodsMerger::isInProgress($sellerDbManager->getSellerId())) {
                    $this->info("Periods merge is in progress, skipping this seller");
                    continue;
                }

                $seller = Seller::find()
                    ->where(['id' => $sellerDbManager->getSellerId(), 'is_analytic_active' => true])
                    ->cache(5 * 60)
                    ->one(\Yii::$app->db);

                if (null === $seller) {
                    continue;
                }

                $maxMessagesInQueue = 30;

                if ($seller->is_demo) {
                    $maxMessagesInQueue = 1000;
                }

                if (!$seller->is_active && $seller->is_analytic_active) {
                    $maxMessagesInQueue = 5;
                }

                $this->info("Processing seller {$seller->id} | started");

                $messagesInQueue = EventPeriod::find()->where(['clickhouse_status'=>EventPeriod::CLICKHOUSE_STATUS_QUEUED])->count();
                if ($messagesInQueue < $maxMessagesInQueue){
                    $eventPeriodsQuery = EventPeriod::find()->where([
                        'and',
                        ['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED],
                        ['=', 'clickhouse_status', EventPeriod::CLICKHOUSE_STATUS_NEW]
                    ])->orderBy('start_date DESC')->limit($maxMessagesInQueue - $messagesInQueue);

                    $eventPeriods = $eventPeriodsQuery->all();

                    $this->info(sprintf('Sending %d event periods to queue (from cache to clickhouse)', count($eventPeriods)));
                    foreach ($eventPeriods as $eventPeriod) {
                        $rabbitMqMessagesSender->eventPeriodsFromCacheToClickhouse($eventPeriod, $seller->id);
                    }
                }else{
                    $this->info(sprintf('Skipped, %d periods are in status queued', $messagesInQueue));
                }

                /*$waitingEventsPeriodsQuery = EventPeriod::find()->where([
                    'and',
                    ['=', 'loading_status', EventPeriod::LOADING_STATUS_FINISHED],
                    ['=', 'clickhouse_status', EventPeriod::CLICKHOUSE_STATUS_WAITING],
                    ['<', 'updated_at', (new \DateTime())->modify('-1 hour')->format('Y-m-d H:i:s')]
                ])->orderBy('id DESC')->limit(10);

                $waitingEventPeriods = $waitingEventsPeriodsQuery->all();

                $this->info(sprintf('Sending %d waiting event periods to queue (from cache to clickhouse)', count($waitingEventPeriods)));
                foreach ($waitingEventPeriods as $waitingEventPeriod) {
                    $rabbitMqMessagesSender->eventPeriodsFromCacheToClickhouse($waitingEventPeriod, $seller->id);
                }*/

                $this->info("Processing seller {$seller->id} | finished");
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
        $this->info('Finished successfully');
    }

    public function actionRenewTerminated(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        $query = Seller::find()->where(['is_analytic_active' => true]);
        if ($customerIdFrom) {
            $query->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
        }
        if ($customerIdTo) {
            $query->andWhere('customer_id < :to', [':to' => $customerIdTo]);
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var CacheInterface $cache */
        $cache = \Yii::$app->fastPersistentCache;

        try {
            foreach ($query->batch(self::BATCH_SIZE) as $items) {
                /** @var Seller $seller */
                foreach ($items as $seller) {
                    $dbManager->setSellerId($seller->id);
                    $isReExportInProgress = $cache->get("is_re_export_in_progress_" . $this->dbManager->getCustomerId());

                    if ($isReExportInProgress) {
                        $this->info("Re-export is in progress, skipping");
                        continue;
                    }

                    try {
                        $service = new RenewTerminatedPeriodsService($seller);
                        $service->renew();
                        $service->renewClickHouseStatus();
                    } catch (\Throwable $e) {
                        \Yii::error($e);
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->error($e);
        }
        $this->unlock();
    }
}
