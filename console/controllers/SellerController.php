<?php

namespace console\controllers;

use api\modules\v1\controllers\TransactionController;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\materializedViews\tables\OrderBasedTransaction;
use common\components\clickhouse\materializedViews\tables\PpcCostsLastFewDaysTable;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderExtendedView;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1;
use common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedView;
use common\components\clickhouse\materializedViews\views\FbaFeeFactorView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedView;
use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\clickhouse\materializedViews\views\ReferralFeeFactorView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedView;
use common\components\clickhouse\materializedViews\views\TransactionExtendedViewV1;
use common\components\core\db\Connection;
use common\components\core\db\dbManager\DbManager;
use common\components\core\db\dbManager\helper\HelperFactory;
use common\components\customerProcess\process\sub\InitDemoData;
use common\components\dataReloader\Config;
use common\components\dataReloader\DataReLoader;
use common\components\db\DbCreator;
use common\components\dbCleaner\Cleaner;
use common\components\LogToConsoleTrait;
use common\components\rabbitmq\MessagesSender;
use common\components\reports\dataProvider\SellingApiProvider;
use common\components\sellingApi\apiProxy\FinancesApi;
use common\components\sellingApi\exception\AccessTokenInvalidException;
use common\components\services\order\TransferOrderService;
use common\components\tokenService\Exception\NoAccessTokenException;
use common\models\ads\AmazonAdsAccount;
use common\models\AmazonMarketplace;
use common\models\Command;
use common\models\customer\AmazonReport;
use common\models\customer\IndirectCost;
use common\models\CustomerProcess;
use common\models\DataImportRecurrent;
use common\models\Seller;
use SellingPartnerApi\ReportType;
use yii\caching\TagDependency;
use yii\console\ExitCode;
use yii\data\ActiveDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\VarDumper;

class SellerController extends BaseController
{
    use LogToConsoleTrait;

    /**
     * @throws \Exception
     */
    public function actionCreateDb($sellerId = null)
    {
        if (!$this->lock()) {
            return;
        }

        if (!is_null($sellerId)) {
            $sellersQuery = Seller::find()->where(['id' => $sellerId]);
        } else {
            $sellersQuery = Seller::find()->where([
                'is_db_created' => false
            ]);
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        foreach ($sellersQuery->batch() as $sellers) {
            /** @var Seller[] $sellers */
            foreach ($sellers as $seller) {
                try {
                    $dbManager->setSellerId($seller->id, true);
                    $this->info("Creating db for seller {$seller->id}");
                    (new DbCreator($seller))->createDatabases();

                    $seller->is_db_created = true;
                    $seller->saveOrThrowException();
                    if ($seller->is_demo) {
                        try {
                            $initDemoProcess = new InitDemoData();
                            $initDemoProcess->proceed(new CustomerProcess());
                        } catch (\Throwable $e) {
                            $this->error($e);
                        }
                    }
                } catch (\Throwable $e) {
                    if (!empty($sellerId)) {
                        throw $e;
                    }
                    $this->error($e);
                }
            }

            Command::create(sprintf(
                "seller/check-vat-calculation-service-availability %d %d",
                $seller->customer_id,
                $seller->customer_id + 1
            ));
            Command::create(sprintf(
                "product/sync %d %d",
                $dbManager->getCustomerId(),
                $dbManager->getCustomerId() + 1
            ));
            Command::create(sprintf(
                "product-cost/sync %d %d",
                $dbManager->getCustomerId(),
                $dbManager->getCustomerId() + 1
            ));
        }

        if (!empty($this->commandModel)) {
            $this->commandModel->markFinished();
        }

        $this->unlock();
    }

    public function actionCheckVatCalculationServiceAvailability(int $customerIdFrom = null, int $customerIdTo = null)
    {
        $allowedCountryCodes = ['DE', 'BE', 'IT', 'FR', 'ES', 'SE', 'GB', 'PL', 'NL'];
        $allowedMarketplaceIds = AmazonMarketplace::find()
            ->select('id')
            ->where(['country_code' => $allowedCountryCodes])
            ->column();

        $createReportFn = function (SellingApiProvider $sellingApiProvider, Seller $seller) use ($allowedCountryCodes) {
            $amazonReportDTO = new AmazonReport();
            $amazonReportDTO->seller_id = $this->dbManager->getSellerId();
            $amazonReportDTO->start_date = (new \DateTime())->modify('-10 minute')->format('Y-m-d H:i:s');
            $amazonReportDTO->end_date = (new \DateTime())->format('Y-m-d H:i:s');
            $amazonReportDTO->amazon_type = ReportType::GET_FLAT_FILE_VAT_INVOICE_DATA_REPORT['name'];
            $sellerMarketplaceIds = $sellingApiProvider->getSellerMarketplaces($this->dbManager->getSellerId());

            if (empty($sellerMarketplaceIds)) {
                $this->info("No marketplaces (at all) found for seller {$this->dbManager->getSellerId()}");
                return null;
            }

            $marketplaceIds = AmazonMarketplace::find()
                ->select('id')
                ->where([
                    'id' => $sellerMarketplaceIds,
                    'country_code' => $allowedCountryCodes
                ])
                ->limit(3)
                ->column();

            if (empty($marketplaceIds)) {
                $this->info("No suitable marketplaces found for seller {$this->dbManager->getSellerId()}");
                $seller->is_vcs_enabled = false;
                $seller->save(false);
                return null;
            }

            $createdReportId = $sellingApiProvider->createReportInAmazon($amazonReportDTO, [
                'marketplaceIds' => $marketplaceIds
            ]);
            $this->info("Report created with id {$createdReportId}");
        };

        foreach ($this->dbManager->iterateAnalyticActiveSchemas('finance', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                $this->info("Checking VCS for customer {$this->dbManager->getCustomerId()}, seller {$this->dbManager->getSellerId()}");

                $seller = Seller::find()->where([
                    'id' => $this->dbManager->getSellerId()
                ])->one(\Yii::$app->db);
                if (empty($seller)) {
                    continue;
                }

                if (!$seller->canMakeRequestToAmazon()) {
                    $this->info("Seller {$this->dbManager->getSellerId()} can't make request to Amazon");
                    continue;
                }

                $sellingApiProvider =  new SellingApiProvider();
                $reports = $sellingApiProvider->getLatestExternalReports($this->dbManager->getSellerId(), [ReportType::GET_FLAT_FILE_VAT_INVOICE_DATA_REPORT['name']]);

                foreach ($reports as $report) {
                    $marketplaceIds = $report->extraInfo['marketplaceIds'] ?? [];
                    $notAllowedMarketplaceIds = array_diff($marketplaceIds, $allowedMarketplaceIds);

                    if (empty($notAllowedMarketplaceIds)) {
                        $firstReport = $report;
                        break;
                    }
                }

                if (empty($firstReport) || $firstReport->createdAt < (new \DateTime())->modify('-1 day')) {
                    $createReportFn($sellingApiProvider, $seller);
                    continue;
                }
                $this->info(json_decode(json_encode($firstReport), true));

                if ($firstReport->status !== AmazonReport::STATUS_DONE) {
                    $this->info("Report is not ready yet");
                    continue;
                }

                $amazonReportDTO = new AmazonReport();
                $amazonReportDTO->seller_id = $this->dbManager->getSellerId();
                $amazonReportDTO->extra_info = $firstReport->extraInfo;
                $reportUrl = $sellingApiProvider->getUrl($amazonReportDTO);
                $this->info([
                    'url' => $reportUrl
                ]);
                $headers = get_headers($reportUrl, 1);

                if ($headers['Content-Length'] > 5000) {
                    $this->info("VCS is available for this seller, file contains large amount of data");
                    $seller->is_vcs_enabled = true;
                    $seller->save(false);
                    continue;
                }

                $reportFileContent = file_get_contents($reportUrl);

                if (false !== strpos($reportFileContent, 'You must be enrolled')) {
                    $this->info("VCS is NOT available for this seller ($reportFileContent)");
                    continue;
                }

                try {
                    $reportFileContent = gzdecode($reportFileContent);
                } catch (\Throwable $e) {
                    $this->info("Report content is not gzipped");
                }

                if (false !== strpos($reportFileContent, "order-item-id")) {
                    $this->info("VCS is available for this seller");
                    $seller->is_vcs_enabled = true;
                    $seller->save(false);
                    continue;
                }

                if (false !== strpos($reportFileContent, "You must be enrolled")) {
                    $this->info("VCS is NOT available for this seller ($reportFileContent)");
                    $seller->is_vcs_enabled = false;
                    $seller->save(false);
                    continue;
                }

                throw new \Exception("Unable to determine VCS availability by report content");
            } catch (\Throwable $e) {
                $this->error($e);
            }
        }
    }

    public function actionRebuildDynamicTables(int $customerIdFrom = null, int $customerIdTo = null, bool $isAfterReExport = false)
    {
        foreach ($this->dbManager->iterateAnalyticActiveSchemas('customer', $customerIdFrom, $customerIdTo) as $schemaInfo) {
            try {
                if (empty($this->dbManager->getCustomerId())) {
                    continue;
                }

                $this->info("Rebuilding dynamic tables for customer {$this->dbManager->getCustomerId()}");
                try {
//                    if ($shouldReInit) {
//                        (new TransferOrderService($this->dbManager->getCustomerId()))->reinit();
//                    } else {
                        (new TransferOrderService($this->dbManager->getCustomerId()))->transfer();
//                    }
                } catch (\Throwable $e) {
                    $this->error($e);
                }

                $manager = new DynamicTablesManager();

                if ($isAfterReExport) {
                    $manager->rebuildDynamicTable(new TransactionExtendedViewV1());
                    $manager->rebuildDynamicTable(new ReferralFeeFactorView());
                    $manager->rebuildDynamicTable(new FbaFeeFactorView());
                }

                if ($this->dbManager->isActive() || $isAfterReExport) {
                    $manager->rebuildDynamicTable(new PpcCostsLastFewDaysTable());
                    $manager->rebuildDynamicTable(new AmazonOrderExtendedViewV1());
                    $manager->rebuildDynamicTable(new AmazonOrderInProgressExtendedViewV1());
                }

                $manager->rebuildDynamicTable(new OrderBasedTransaction());
                $manager->rebuildDynamicTable(new OrderBasedTransactionExtendedViewV1());
            } catch (\Throwable $e) {
                $this->info($e);
            }
        }
    }

    public function actionReActivateSubscription(int $customerId): void
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $this->dbManager->setCustomerId($customerId);
            $messagesSender = new MessagesSender();

            /** @var IndirectCost[] $indirectCosts */
            foreach (IndirectCost::find()->batch() as $indirectCosts) {
                foreach ($indirectCosts as $indirectCost) {
                    $messagesSender->indirectCostChanges(
                        $this->dbManager->getCustomerId(),
                        $indirectCost->id
                    );
                }
            }

            DataImportRecurrent::updateAll(['is_enabled' => false], ['customer_id' => $customerId]);
        } catch (\Throwable $e) {
            $this->error($e);
        }

        $this->unlock();
    }

    /**
     * @throws \Exception
     */
    public function actionMigrateData(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        \Yii::$app->db->enableSlaves = false;

        try {
            ini_set('memory_limit', '512M');

            $sellersQuery = Seller::find()->where([
                'is_data_migrated' => false,
                'is_db_created' => true,
            ]);

            if ($customerIdFrom) {
                $sellersQuery->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
            }
            if ($customerIdTo) {
                $sellersQuery->andWhere('customer_id < :to', [':to' => $customerIdTo]);
            }

            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;

            $alreadyMigratedCustomers = [];
            foreach ($sellersQuery->batch() as $sellers) {
                /** @var Seller[] $sellers */
                foreach ($sellers as $seller) {

                    $this->info("Customer {$seller->customer_id} | Seller {$seller->id}");

                    $dbManager->setSellerId($seller->id, true);

                    $dbPrefixes = [DbManager::DB_PREFIX_CUSTOMER, DbManager::DB_PREFIX_FINANCE];
                    $newDbPrefixes = [DbManager::DB_PREFIX_CUSTOMER, DbManager::DB_PREFIX_FINANCE];

                    foreach ($newDbPrefixes as $newDbPrefix) {
                        $newDb = $dbManager->getDb($newDbPrefix, HelperFactory::TYPE_POSTGRESS);
                        $newDb->createCommand('SET session_replication_role = replica;')->execute();
                    }

                    foreach ($dbPrefixes as $dbPrefix) {
                        if ($dbPrefix === DbManager::DB_PREFIX_CUSTOMER && in_array($seller->customer_id, $alreadyMigratedCustomers)){
                            continue;
                        }
                        $this->info("DB {$dbPrefix}");
                        /** @var Connection $db */
                        $db = $dbManager->getDb($dbPrefix, HelperFactory::TYPE_POSTGRESS_SPLITTED_BY_DB);

                        $tables = (new Query())
                            ->select('tablename')
                            ->where("schemaname = 'public' and tablename != 'migration'")
                            ->from('pg_catalog.pg_tables')->column($db);

                        foreach ($tables as $table) {

                            $newDbPrefix = in_array($table, ['order_period', 'amazon_order']) ? DbManager::DB_PREFIX_ORDER : $dbPrefix;

                            $newDb = $dbManager->getDb($newDbPrefix, HelperFactory::TYPE_POSTGRESS);
                            $newDb->createCommand('SET session_replication_role = replica;')->execute();

                            $newDbTableName = $dbManager->getSchemaName($newDbPrefix).'.'.$table;
//                            $newDb->createCommand("TRUNCATE TABLE $newDbTableName CASCADE")->execute();

                            $primaryField = $db->createCommand("SELECT c.column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_name)
    JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema
      AND tc.table_name = c.table_name AND ccu.column_name = c.column_name
    WHERE constraint_type = 'PRIMARY KEY' and tc.table_name = '{$table}' limit 1;")->cache(0, new TagDependency(['tags' => 'table-primary-'.$table]))->queryScalar();

                            $maxId = (new Query())
                                ->select("max($primaryField)")
                                ->from($newDbTableName)->scalar($newDb);

                            $query = (new Query())
                                ->select('*')
                                ->from($table)
                                ->orderBy($primaryField);

                            $this->info("MaxID {$maxId}");

                            if ($maxId){
                                $query->where("$primaryField > $maxId");
                            }

                            $countRows = 0;
                            foreach ($query->batch(1000, $db) as $rows) {
                                $sql = $newDb->
                                createCommand()->
                                batchInsert($newDbTableName, array_keys($rows[0]), $rows)->getRawSql();

                                if ($table === 'product'){
                                    $sql .= ' ON CONFLICT (marketplace_id, sku, seller_id) DO NOTHING';
                                }
                                $newDb->createCommand($sql)->execute();
                                $countRows += count($rows);
                            }

                            if ($table !== 'event_group') {
                                $seqName = $table !== 'product' ? "{$newDbTableName}_id_seq" : "{$newDbTableName}_cost_id_seq";
                                $newDb->createCommand("SELECT setval('{$seqName}', COALESCE((SELECT MAX(id) + 1 FROM $newDbTableName), 1), false)")->execute();
                            }

                            $mem_usage = memory_get_usage();
                            $mem_peak = memory_get_peak_usage();

                            $this->info("Mamory " . round($mem_usage / 1024) . 'KB, peak '.round($mem_peak / 1024) . 'KB');
                            $this->info("Table {$table} | Rows {$countRows}");
                        }

                        if ($dbPrefix === DbManager::DB_PREFIX_CUSTOMER){
                            $alreadyMigratedCustomers[] = $seller->customer_id;
                        }
                        $db->close();
                    }

                    foreach ($newDbPrefixes as $newDbPrefix) {
                        $newDb = $dbManager->getDb($newDbPrefix, HelperFactory::TYPE_POSTGRESS);
                        $newDb->createCommand('SET session_replication_role = DEFAULT;')->execute();
                    }

                    $seller->is_data_migrated = true;
                    $seller->update(false, ['is_data_migrated']);

                }
            }

        } catch (\Throwable $e) {
            $this->unlock();
            throw $e;
        }

        $this->unlock();
    }

    /**
     * @throws \Exception
     */
    public function actionReindex(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        \Yii::$app->db->enableSlaves = false;

        try {
            ini_set('memory_limit', '512M');

            $sellersQuery = Seller::find()->where([
//                'is_data_migrated' => false,
                'is_db_created' => true,
            ])->orderBy('customer_id');

            if ($customerIdFrom) {
                $sellersQuery->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
            }
            if ($customerIdTo) {
                $sellersQuery->andWhere('customer_id < :to', [':to' => $customerIdTo]);
            }

            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;

            $alreadyMigratedCustomers = [];
            foreach ($sellersQuery->batch() as $sellers) {
                /** @var Seller[] $sellers */
                foreach ($sellers as $seller) {

                    $this->info("Customer {$seller->customer_id} | Seller {$seller->id}");

                    $dbManager->setSellerId($seller->id, true);

//                    $dbPrefixes = [DbManager::DB_PREFIX_CUSTOMER, DbManager::DB_PREFIX_FINANCE, DbManager::DB_PREFIX_ORDER];

                    $schema = $dbManager->getSchemaName(DbManager::DB_PREFIX_ORDER);
                    $this->info("Reindex schema {$seller->id}");

                    $newDb = $dbManager->getDb(DbManager::DB_PREFIX_FINANCE, HelperFactory::TYPE_POSTGRESS);
                    $newDb->createCommand("REINDEX SCHEMA {$schema}")->execute();

//                    if ($dbPrefix === DbManager::DB_PREFIX_CUSTOMER){
//                        $alreadyMigratedCustomers[] = $seller->customer_id;
//                    }
                }
            }

        } catch (\Throwable $e) {
            $this->unlock();
            throw $e;
        }

        $this->unlock();
    }



    /**
     * @throws \Exception
     */
    public function actionRemoveDb(string $sellerId, int $customerId)
    {
        $lockKey = "seller/remove-db {$sellerId} {$customerId}";
        if (false === $this->mutex->acquire($lockKey)) {
            $this->info('Command is already running in another process');
            return;
        }

        try {
            $dbCleaner = new Cleaner();
            $dbCleaner->removeSeller($sellerId, $customerId);
        } catch (\Throwable $e) {
            $this->mutex->release($lockKey);
            throw $e;
        }
        $this->mutex->release($lockKey);
    }

    public function actionRecheckTokens(int $customerIdFrom = null, int $customerIdTo = null)
    {
        if (!$this->lock()) {
            return;
        }

        try {
            $sellersQuery = Seller::find()
                ->where([
                    'is_analytic_active' => true,
                    'is_db_created' => true,
                    'is_token_received' => false
                ])
                ->andWhere(['<', 'last_attempt_to_get_token', date('Y-m-d H:i:s', time() - 60 * 30)]);
            if ($customerIdFrom) {
                $sellersQuery->andWhere('customer_id >= :from', [':from' => $customerIdFrom]);
            }
            if ($customerIdTo) {
                $sellersQuery->andWhere('customer_id < :to', [':to' => $customerIdTo]);
            }

            foreach ($sellersQuery->batch() as $sellers) {

                /** @var Seller $seller */
                foreach ($sellers as $seller) {
                    try {
                        $apiInstance = new FinancesApi($seller->id, $seller->region);
                        $apiInstance->listFinancialEvents(
                            100,
                            // Any date, we need to check access token. Other does not matter.
                            (new \DateTime())->modify('-60 minutes')->format('c'),
                            (new \DateTime())->modify('-50 minutes')->format('c'),
                        );

                        $seller->last_attempt_to_get_token = date('Y-m-d H:i:s');
                        $seller->is_token_received = true;
                        $seller->save();
                    } catch (AccessTokenInvalidException | NoAccessTokenException $e) {
                        $seller->last_attempt_to_get_token = date('Y-m-d H:i:s');
                        $seller->save();
                    } catch (\Throwable $e) {
                        \Yii::error($e);
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->unlock();
            throw $e;
        }

        $this->unlock();
    }

    public function actionReloadData()
    {
        $this->info('Please, configure reload process');
        $reloadConfig = new Config();
        $reloadConfig->sellerId = $this->prompt('Seller id (all sellers if null):') ?: null;

        if (empty($reloadConfig->sellerId)) {
            $reloadConfig->customerId = ($this->prompt('Customer id (all customers if null):') ?: null);
        }

        if ($this->confirm('Should reset event periods data and reimport from amazon? (clickhouse data will be re-imported too)')) {
            $reloadConfig->isResetPostgress = true;
            $reloadConfig->isResetClickhouse = true;
        } else if ($this->confirm('Should recreate clickhouse databases?')) {
            $reloadConfig->isRecreateClickhouseDatabases = true;
        }

        if ($this->confirm('Should reset COG data and reimport from rest api?')) {
            $reloadConfig->isResetCOGData = true;
        }

        if (!$reloadConfig->isResetClickhouse
            && $this->confirm('Should reset clickhouse data and reimport from db?')
        ) {
            $reloadConfig->isResetClickhouse = true;
            $reloadConfig->eventPeriodExceptionMessageLike =
                $this->prompt('Specify event period exception LIKE if need (all event  periods with this mask will be reloaded to clickhouse):' ?: null);
            if (!empty($reloadConfig->eventPeriodExceptionMessageLike)
                && strlen($reloadConfig->eventPeriodExceptionMessageLike) < 15
            ) {
                $this->info("Exception message like pattern should be more than 15 characters!");
                return ExitCode::OK;
            };
        }
        if ($this->confirm('Force reload data to clickhouse?')) {
            $reloadConfig->forceLoadToClickhouse = true;
        }

        if ($this->confirm('Should reset empty orders periods?')) {
            $reloadConfig->isResetEmptyOrderPeriods = true;
        }

        if (!$reloadConfig->isResetPostgress && $this->confirm('Should fix duplicated events periods?')) {
            $reloadConfig->fixDuplicatesInEventPeriods = true;
        }
        if (!$reloadConfig->isResetPostgress && $this->confirm('Should fix duplicated orders periods?')) {
            $reloadConfig->fixDuplicatesInOrdersPeriods = true;
        }

        if ($this->confirm('Should recreate clickhouse orders tables?')) {
            $reloadConfig->isRecreateClickhouseOrdersTables = true;
        }

        $this->info('Reload config:');
        $this->info(json_decode(json_encode($reloadConfig), true));

        if (!$this->confirm("I've DOUBLE checked reload config and understand what i do (in case of wrong configuration, data can be lost)")) {
            return ExitCode::OK;
        }

        /** @var DataReLoader $dataReLoader */
        $dataReLoader = \Yii::$container->get('dataReLoader');
        $dataReLoader->reload($reloadConfig);

        return ExitCode::OK;
    }

    /**
     * Erase the data for clients with no BAS subscription or expired over 2 months ago
     * @return int
     */
    public function actionRemoveInactive(): int
    {
        if (!$this->lock()) {
            return ExitCode::TEMPFAIL;
        }
        $dbCleaner = new Cleaner();

        $this->info('Searching clients with no BAS subscriptions or expired over 2 months ago');

        $subQuery = Seller::find()
            ->select('customer_id')
            ->distinct()
            ->where(['is_analytic_active' => 't'])
            ->orWhere([
                'or',
                ['and',
                    ['is not', 'was_active_until_date', null],
                    new Expression("was_active_until_date >= NOW() - INTERVAL '2 MONTH'")
                ],
                ['and',
                    ['is', 'was_active_until_date', null],
                    new Expression("created_at > NOW() - INTERVAL '2 MONTH'")
                ]
            ]);

        $sellersQuery = Seller::find()
            ->where(['not in', 'customer_id', $subQuery]);

        $this->info('Inactive clients: ' . $sellersQuery->count());

        $this->info('Running seller/remove-db for inactive clients');
        foreach ($sellersQuery->batch() as $sellers) {
            foreach ($sellers as $seller) {
                try {
                    $dbCleaner->removeSeller($seller->id, $seller->customer_id);
                } catch (\Throwable $e) {
                    $this->unlock();
                    $this->error($e);
                    return ExitCode::DATAERR;
                }
            }
        }
        $this->info('Removing completed');

        $this->unlock();

        return ExitCode::OK;
    }

    /**
     * Compares sellers status with repricer and re-sync them if found inconsistency.
     *
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionReSyncSellers()
    {
        $repricerSellers = (new Query)
            ->select([
                'aca.sellerId as id',
                'ca.customer_id as customer_id',
                new Expression('IF (aca.use_bas_module, 1, 0) as is_active'),
                new Expression('IF ((aca.use_bas_module || aca.use_repricer_module), 1, 0) as is_analytic_active')
            ])
            ->from('repricer_main_db.amazon_customer_account aca')
            ->leftJoin('repricer_main_db.customer_account ca', 'ca.id = customer_account_id')
            ->leftJoin('repricer_main_db.customer c', 'c.id = ca.customer_id')
            ->where([
                'AND',
                [
                    'OR',
                    ['aca.use_bas_module' => 1],
                    ['aca.use_repricer_module' => 1]
                ],
                ['not like', 'aca.sellerId', 'DEMO'],
                ['!=', 'aca.sellerId', 'BASIC'],
                ['=', 'c.status', 'active'],
                ['!=', 'c.repricer_locked_by_empty_payment', 1]
            ])
            ->indexBy('id')
            ->all($this->dbManager->getRepricerMainDb());

        $basSellers = Seller::find()
            ->select([
                'id',
                new Expression('is_active::int'),
                new Expression('is_analytic_active::int'),
            ])
            ->indexBy('id')
            ->asArray()
            ->all();

        $analyticActiveCustomerIds = [];
        $syncCommands = [];

        foreach ($repricerSellers as $repricerSeller) {
            $basSeller = $basSellers[$repricerSeller['id']] ?? null;
            $basIsActive = $basSeller['is_active'] ?? null;
            $basIsAnalyticActive = $basSeller['is_analytic_active'] ?? null;
            $repricerIsActive = (int)$repricerSeller['is_active'];
            $repricerIsAnalyticActive = (int)$repricerSeller['is_analytic_active'];
            $analyticActiveCustomerIds[] = $repricerSeller['customer_id'];

            if ($repricerIsActive === $basIsActive && $repricerIsAnalyticActive === $basIsAnalyticActive) {
                continue;
            }

            $this->sendSellerInconsistencyError([
                'customer_id' => $repricerSeller['customer_id'],
                'seller_id' => $repricerSeller['id'],
                'is_active' => $repricerIsActive,
                'is_analytic_active' => $repricerIsAnalyticActive,
                'bas_is_active' => $basIsActive,
                'bas_is_analytic_active' => $basIsAnalyticActive,
            ]);
            $syncCommands[$repricerSeller['customer_id']] = [
                'command' => 'syncBas',
                'args' => serialize([
                    'customerId' => (int)$repricerSeller['customer_id'],
                    'userId' => null
                ]),
                'status' => 'new',
                'type' => 'action'
            ];
        }
        $analyticActiveCustomerIds = array_unique($analyticActiveCustomerIds);

        $repricerAmazonAdsAccounts = (new Query)
            ->select([
                'aac.id',
                'aac.customer_id',
                'aac.is_deleted',
                'aac.is_ba_active as is_active'
            ])
            ->from('repricer_main_db.amazon_ads_account aac')
            ->leftJoin('repricer_main_db.customer c', 'c.id = aac.customer_id')
            ->where([
                'AND',
                ['in', 'c.id', $analyticActiveCustomerIds],
            ])
            ->orderBy('aac.customer_id')
            ->indexBy('id')
            ->all($this->dbManager->getRepricerMainDb());

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        foreach ($repricerAmazonAdsAccounts as $repricerAmazonAdsAccount) {
            try {
                $dbManager->setCustomerId($repricerAmazonAdsAccount['customer_id']);
            } catch (\Throwable $e) {
                continue;
            }
            $this->info(sprintf('Comparison customer %d amazon ads account %d',
                $dbManager->getCustomerId(),
                $repricerAmazonAdsAccount['id'],
            ));

            try {
                $basAmazonAdsAccount = AmazonAdsAccount::find()->where([
                    'id' => $repricerAmazonAdsAccount['id']
                ])->asArray()->one();
            } catch (\Throwable $e) {
                $this->error($e);
                continue;
            }

            $basIsActive = (int)($basAmazonAdsAccount['is_active'] ?? 0);
            $repricerIsActive = (int)$repricerAmazonAdsAccount['is_active'];
            $basIsDeleted = empty($basAmazonAdsAccount) ? 1 : 0;
            $repricerIsDeleted = (int)$repricerAmazonAdsAccount['is_deleted'];

            if (
                ($basIsDeleted && $repricerIsDeleted)
                || ($basIsDeleted === $repricerIsDeleted && $basIsActive === $repricerIsActive)
            ) {
                $this->info('Ok');
                continue;
            }

            $this->sendSellerInconsistencyError([
                'customer_id' => $this->dbManager->getCustomerId(),
                'amazon_ads_account_id' => $repricerAmazonAdsAccount['id'],
                'is_active' => $repricerIsActive,
                'bas_is_active' => $basIsActive,
                'id_deleted' => $repricerIsDeleted,
                'bas_is_deleted' => $basIsDeleted,
            ]);
            $syncCommands[$this->dbManager->getCustomerId()] = [
                'command' => 'syncBas',
                'args' => serialize([
                    'customerId' => $this->dbManager->getCustomerId(),
                    'userId' => null
                ]),
                'status' => 'new',
                'type' => 'action'
            ];
        }

        if (count($syncCommands) === 0) {
            $this->info("All sellers have correct sync status");
            return;
        }

        $this->info(sprintf("Sending %d sync commands started", count($syncCommands)));
        $syncCommands = array_values($syncCommands);

//        $this->dbManager->getRepricerMainDb()->createCommand()->batchInsert(
//            'repricer_main_db.command',
//            array_keys($syncCommands[0]),
//            $syncCommands
//        )->execute();
        $this->info("Sending sync commands finished");
    }

    /**
     * Used only for rollbar to be able to see inconsistency info for every found occurrence.
     *
     * @param array $inconsistencyInfo
     * @return mixed
     * @throws \Exception
     */
    protected function sendSellerInconsistencyError(array $inconsistencyInfo): void
    {
        $this->info($inconsistencyInfo);
        $this->error(new \Exception("Found sync seller inconsistency"));
    }
}
