<?php

namespace console\controllers;

use \yii\console\controllers\CacheController as BaseCacheController;
use Yii;
use yii\console\ExitCode;

/**
 * Class CacheController.
 *
 * @method \yii\console\controllers\CacheController findCaches()
 * @method \yii\console\controllers\CacheController notifyNoCachesFound()
 * @method \yii\console\controllers\CacheController canBeFlushed($className)
 * @method \yii\console\controllers\CacheController notifyFlushed($caches)
 *
 * @package console\controllers
 */
class CacheController extends BaseCacheController
{
    /**
     * Caches which should be skipped when calling 'flush-all'.
     */
    private const SKIP_ON_FLUSH_ALL = [
        'fastPersistentCache',
        'mutexCache'
    ];

    /**
     * {@inheritdoc}
     */
    public function actionFlushAll()
    {
        $caches = $this->findCaches();
        $cachesInfo = [];

        if (empty($caches)) {
            $this->notifyNoCachesFound();
            return ExitCode::OK;
        }

        foreach ($caches as $name => $class) {
            if (in_array($name, self::SKIP_ON_FLUSH_ALL, true)) {
                continue;
            }

            $cachesInfo[] = [
                'name' => $name,
                'class' => $class,
                'is_flushed' => $this->canBeFlushed($class) ? Yii::$app->get($name)->flush() : false,
            ];
        }

        $this->notifyFlushed($cachesInfo);
    }

    /**
     * Making base controller expandable and flexible.
     *
     * @param  string               $name
     * @param  array                $params
     * @throws \ReflectionException
     * @return mixed
     */
    public function __call($name, $params)
    {
        $method = new \ReflectionMethod(BaseCacheController::className(), $name);
        $method->setAccessible(true);

        return $method->invoke($this, ...$params);
    }
}
