# Run Codecept

```bash
functional tests
docker-compose exec profit-dash-php-fpm ./vendor/bin/codecept run functional tests/functional/api/v1

unit tests
docker-compose exec profit-dash-php-fpm ./vendor/bin/codecept run functional tests/unit
```

# Run Rector
Rector is a CLI tool for automated PHP refactoring and upgrading. Below are the basic commands you will need.
```bash
# Preview changes without applying
vendor/bin/rector process --dry-run
# Apply changes
vendor/bin/rector process
# Run Rector on a specific folder
vendor/bin/rector process src/Some/Module
# Run Rector on a specific file
vendor/bin/rector process src/Foo/Bar.php
# Run in parallel for faster processing
vendor/bin/rector process --parallel
# Tips:
# Always run Rector in dry-run mode first to review what will be changed
# Use git to review diffs (git diff) and commit only what you want
# Combine Rector with tests and static analysis to ensure safe refactoring
```


# Run PHPStan

```bash
vendor/bin/phpstan analyse src --level=8


vendor/bin/phpstan analyse common
vendor/bin/phpstan analyse api
vendor/bin/phpstan analyse console
```
