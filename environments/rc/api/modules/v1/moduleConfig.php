<?php
/**
 * @OA\Info(
 *   version="1.0",
 *   title="Business Analytics API",
 *   description="
<h3>Date and Time Handling</h3>
<p>API always stores and returns <b>datetime</b> values (both date and time specified) in <b>UTC timezone</b>, so you should take care about conversion UTC datetime to user's timezone.<br>
This rule does not apply <b>date</b> values (only date specified), such values always accepts and retrieves date in <b>user's timezone</b> without requirement to be converted to/from UTC.</p>
<b>Examples:</b> (user's timezone is UTC+3)
<ul>
<li>user is lookging for transactions for dates <b>'2024-10-02 00:00:00 - 2024-10-03 00:00:00'</b>, you should convert these dates to UTC and send <b>'2024-10-01 21:00:00 - 2024-10-02 21:00:00'</b> within your request.</li>
<li>API returned <b>'2024-10-02 21:00:00'</b>, you should convert it to user's local timezone and display <b>'2024-10-03 00:00:00'</b>.</li>
<li>user opened dahsboard and wants to see widget data for date range <b>'2024-10-02 - 2024-10-03'</b> (no particular time specified), you can send these date range as it is <b>'2024-10-02 - 2024-10-03'</b> without timezone conversion.
<br>At the same time it's allowed to convert these dates to UTC format and send <b>'2024-10-01 21:00:00 - 2024-10-02 21:00:00'</b> instead (time specified).<br>
</li>
<li>API returned <b>'2024-10-02'</b>, you should not do anything with this date - just display as it is.</li>
</ul>
",
 * ),
 *
 * @OA\SecurityScheme(
 *   securityScheme="oauth2",
 *   type="oauth2",
 *   in="header",
 *   scheme="https",
 *   bearerFormat="bearer",
 *   @OA\Flow(
 *     flow="password",
 *     tokenUrl="{AUTH_TOKEN_URL}",
 *     scopes={
 *       "testscope": "Test scope"
 *     }
 *   ),
 *   @OA\Flow(
 *     flow="clientCredentials",
 *     tokenUrl="{AUTH_TOKEN_URL}",
 *     scopes={
 *       "testscope": "Test scope"
 *     }
 *   )
 * )
 */