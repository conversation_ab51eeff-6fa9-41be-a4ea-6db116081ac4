<?php
/**
 * The manifest of files that are local to specific environment.
 * This file returns a list of environments that the application
 * may be installed under. The returned data must be in the following
 * format:
 *
 * ```php
 * return [
 *     'environment name' => [
 *         'path' => 'directory storing the local files',
 *         'setWritable' => [
 *             // list of directories that should be set writable
 *         ],
 *         'setExecutable' => [
 *             // list of directories that should be set executable
 *         ],
 *         'setCookieValidationKey' => [
 *             // list of config files that need to be inserted with automatically generated cookie validation keys
 *         ],
 *         'createSymlink' => [
 *             // list of symlinks to be created. Keys are symlinks, and values are the targets.
 *         ],
 *     ],
 * ];
 * ```
 */
return [
    'Development' => [
        'path' => 'dev',
        'setWritable' => [
            'api/runtime',
            'console/runtime',
            'api/web/assets',
        ],
        'setReadable' => [
        ],
        'setExecutable' => [
            'yii',
        ],
    ],
    'Staging' => [
        'path' => 'staging',
        'setWritable' => [
            'api/runtime',
            'console/runtime',
            'api/web/assets',
        ],
        'setReadable' => [
        ],
    ],
    'Rc' => [
        'path' => 'rc',
        'setWritable' => [
            'api/runtime',
            'console/runtime',
            'api/web/assets',
        ],
        'setReadable' => [
        ],
    ],
    'Production' => [
        'path' => 'prod',
        'setWritable' => [
            'api/runtime',
            'console/runtime',
            'api/web/assets',
        ],
        'setReadable' => [
        ],
    ],
    'Local' => [
        'path' => 'local',
        'setWritable' => [
            'api/runtime',
            'console/runtime',
            'api/web/assets',
        ],
        'setReadable' => [
        ],
    ],
];
