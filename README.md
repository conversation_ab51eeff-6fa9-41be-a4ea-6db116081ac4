## Profit dashboard service

This service is Profit dashboard

### REQUIREMENTS

- docker 20.10 +
- docker compose 2.10 +
- started in docker [https://gitlab.sl.local/develop/rest-api](https://gitlab.sl.local/develop/rest-api) project

### Recommendations
* `Makefile` support - if you don't have this you need to look `Makefile` file
  to see what each `make` command is doing.

### Installation

After you clone the repository, you have to conduct the following steps to initialize the installed application.
1. Copy config file `cp .env.example .env`
2. Place your environment variables to `.env` file
3. Init project `make init`
4. Run command `make up`

That's all. You just need to wait for completion! After that you can access project locally by URL: [localhost:8080](http://localhost:8080/)

### Commands
* `make init` - init project
* `make up` - run application
* `make down` - stop application
* `make restart` - restart application
* `make service-composer-update` - update composer dependencies
* `make service-composer-install` - install composer dependencies
* `make service-test` - run codeception tests