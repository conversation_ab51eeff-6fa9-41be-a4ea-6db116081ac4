# Docker compose file for prod reserve worker
version: '3.7'
services:
  profit-dash-php-fpm-base-worker: &php-fpm-worker-base
    image: {REGISTRY}/develop/profit-dash/sellerlogic_profit-dash-php-fpm:{IMGTAG}
    restart: always
    volumes:
      - profit-dash-console-runtime-worker:/app/console/runtime
      - profit-dash-api-runtime-worker:/app/api/runtime
    networks:
      - profit-dash-net-worker
    logging:
      options:
        max-size: "50m"
        max-file: "3"

  profit-dash-command:
    <<: *php-fpm-worker-base
    command: php /app/yii command/process

  profit-dash-flush-transactions-buffer-1-2000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 1 2000

  profit-dash-flush-transactions-buffer-2000-4000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 2000 4000

  profit-dash-flush-transactions-buffer-4000-6000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 4000 6000

  profit-dash-flush-transactions-buffer-6000-8000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 6000 8000

  profit-dash-flush-transactions-buffer-8000-10000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 8000 10000

  profit-dash-flush-transactions-buffer-10000:
    <<: *php-fpm-worker-base
    command: php /app/yii transaction-buffer/flush-all-if-need 10000

  customer-process-executor:
    <<: *php-fpm-worker-base
    command: php /app/yii customer-process/process-executor

  customer-process-check-delayed:
    <<: *php-fpm-worker-base
    command: php /app/yii customer-process/check-delayed

volumes:
  profit-dash-console-runtime-worker:
  profit-dash-api-runtime-worker:

networks:
  profit-dash-net-worker:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1500
