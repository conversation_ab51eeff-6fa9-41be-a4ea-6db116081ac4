up: docker-up
down: docker-down
restart: docker-down docker-up

init: docker-down-clear docker-pull	docker-build docker-up	service-composer-install service-init-config service-migration

docker-up:
	docker compose up -d

docker-down:
	docker compose down --remove-orphans

docker-down-clear:
	docker compose down -v --remove-orphans

docker-pull:
	docker compose pull

docker-build:
	docker compose build

service-composer-install:
	docker compose run --rm profit-dash-php-fpm composer install

service-composer-update:
	docker compose run --rm profit-dash-php-fpm composer update

service-init-config:
	docker compose run --rm profit-dash-php-fpm ./profit_dash.sh init_config

service-flush-cache:
	docker compose run --rm profit-dash-php-fpm php yii cache/flush-all --interactive=0

service-scale:
	docker compose scale profit-cons-event-periods-from-cache-to-clickhouse=2

service-migration:
	docker compose run --rm profit-dash-php-fpm /app/yii migrate --interactive=0
	docker compose run --rm profit-dash-php-fpm /app/yii migrate-finance --interactive=0
	docker compose run --rm profit-dash-php-fpm /app/yii migrate-customer --interactive=0
	docker compose run --rm profit-dash-php-fpm /app/yii migrate-order --interactive=0
	docker compose run --rm profit-dash-php-fpm /app/yii migrate-clickhouse-customer --interactive=0
	docker compose run --rm profit-dash-php-fpm /app/yii migrate-clickhouse-customer-related --interactive=0

service-test:
	docker compose run --rm profit-dash-php-fpm codecept run
