# ProfitCalculator Performance Optimization Plan

## 🚨 Critical Performance Issues Identified

### 1. **Database Query Optimization**

#### Current Issues:
- **Multiple separate queries** in `DataSeriesRetriever::getDataSeries()`
- **N+1 query pattern** in category processing
- **Large UNION ALL queries** without proper indexing
- **Inefficient date filtering** with timezone conversions

#### Solutions:

##### A. Consolidate Database Queries
```php
// Instead of 3 separate queries, use single optimized query
public function getDataSeriesOptimized(
    SalesCategoryStrategyInterface $salesCategoryStrategy,
    int $depth = 0,
    string $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY
): array {
    // Single query with CTEs (Common Table Expressions)
    $query = (new Query())
        ->select([
            'date',
            'sales_category_id', 
            'category_id',
            'SUM(amount) as amount'
        ])
        ->from([
            '(' . $this->buildOptimizedUnionQuery($salesCategoryStrategy, $depth, $periodType) . ')'
        ])
        ->groupBy(['date', 'sales_category_id', 'category_id'])
        ->cache(self::EXTENDED_CACHE_TIME_S); // Increase cache time
        
    return $query->all($this->dbManager->getClickhouseCustomerDb());
}
```

##### B. Add Database Indexes
```sql
-- Add composite indexes for better performance
CREATE INDEX idx_transaction_date_category ON transaction_extended_view 
(posted_date, sales_category_id, category_id);

CREATE INDEX idx_order_based_date_category ON order_based_transaction 
(posted_date, category_id);

CREATE INDEX idx_ppc_costs_date ON ppc_costs_last_few_days_transaction 
(posted_date);
```

### 2. **Memory and CPU Optimization**

#### Current Issues:
- **Multiple loops over flatTree** in different methods
- **Redundant array operations** in `collapseDataSeries()`
- **Inefficient array_reduce** in strategy methods
- **DateTime object creation** in loops

#### Solutions:

##### A. Optimize Array Processing
```php
// Replace multiple loops with single pass
public function processDataSeriesOptimized(array $dataSeries, array $flatTree, int $maxDepth): array
{
    $collapsedData = [];
    $categoryLookup = [];
    
    // Build lookup table once
    foreach ($flatTree as $item) {
        $categoryLookup[$item['id']] = $item;
    }
    
    // Single pass processing
    foreach ($dataSeries as $item) {
        $categoryInfo = $categoryLookup[$item['sales_category_id']] ?? null;
        if (!$categoryInfo) continue;
        
        $categoryId = ($maxDepth && $categoryInfo['depth'] > $maxDepth) 
            ? explode('|', $categoryInfo['path'])[$maxDepth]
            : $item['sales_category_id'];
            
        $key = $categoryId . '_' . $item['date'];
        $collapsedData[$key] = ($collapsedData[$key] ?? 0) + $item['amount'];
    }
    
    return $collapsedData;
}
```

##### B. Cache Expensive Operations
```php
class ProfitCalculatorOptimized 
{
    private array $categoryCache = [];
    private array $strategyCache = [];
    
    public function getAmountByFlatTreeDataByTagOptimized(array $flatTreeData, string $tag, bool $shouldInclude = true): float
    {
        $cacheKey = md5($tag . ($shouldInclude ? '1' : '0') . serialize(array_keys($flatTreeData)));
        
        if (isset($this->strategyCache[$cacheKey])) {
            return $this->strategyCache[$cacheKey];
        }
        
        $amount = 0;
        foreach ($flatTreeData as $item) {
            if (empty($item['amount_own'])) continue;
            
            $tags = $item['tags'] ?? [];
            $hasTag = in_array($tag, $tags);
            
            if (($hasTag && $shouldInclude) || (!$hasTag && !$shouldInclude)) {
                $amount += $item['amount_own'];
            }
        }
        
        return $this->strategyCache[$cacheKey] = $amount;
    }
}
```

### 3. **Caching Strategy Improvements**

#### Current Issues:
- **Short cache duration** (15 seconds)
- **No result-level caching**
- **Cache misses** on similar requests

#### Solutions:

##### A. Implement Multi-Level Caching
```php
class CacheManager 
{
    const CACHE_LEVELS = [
        'query_result' => 300,      // 5 minutes for query results
        'processed_data' => 900,    // 15 minutes for processed data
        'final_result' => 1800,     // 30 minutes for final calculations
    ];
    
    public function getCachedResult(string $key, callable $callback, string $level = 'query_result')
    {
        $duration = self::CACHE_LEVELS[$level];
        $cacheKey = $this->buildCacheKey($key, $level);
        
        return \Yii::$app->cache->getOrSet($cacheKey, $callback, $duration);
    }
}
```

##### B. Smart Cache Invalidation
```php
public function calc(string $periodType = 'day', int $maxDepth = 1, string $categoryId = null): ProfitResult 
{
    $cacheKey = $this->buildCacheKey($periodType, $maxDepth, $categoryId, $this->filtersForm);
    
    return $this->cacheManager->getCachedResult($cacheKey, function() use ($periodType, $maxDepth, $categoryId) {
        return $this->calculateProfit($periodType, $maxDepth, $categoryId);
    }, 'final_result');
}
```

### 4. **Algorithm Optimization**

#### Current Issues:
- **O(n²) complexity** in data series processing
- **Redundant calculations** across methods
- **Inefficient tree traversal**

#### Solutions:

##### A. Reduce Algorithmic Complexity
```php
// Replace O(n²) with O(n) using hash maps
public function fillAmountsOptimized(array $dataSeriesStructure, array $dataSeries, array $flatTree): array
{
    // Pre-build lookup structures
    $dateRanges = [];
    foreach ($dataSeriesStructure as $key => $group) {
        $dateRanges[] = [
            'key' => $key,
            'start' => $group['dateTimeStart']->getTimestamp(),
            'end' => $group['dateTimeEnd']->getTimestamp(),
        ];
    }
    
    // Process data series in single pass
    foreach ($dataSeries as $item) {
        $timestamp = strtotime($item['date']);
        
        // Binary search for date range (O(log n) instead of O(n))
        $rangeKey = $this->findDateRange($dateRanges, $timestamp);
        if ($rangeKey) {
            $this->updateDataSeriesStructure($dataSeriesStructure[$rangeKey], $item, $flatTree);
        }
    }
    
    return $dataSeriesStructure;
}
```

### 5. **Parallel Processing**

#### Solutions:

##### A. Async Data Retrieval
```php
public function calcAsync(string $periodType = 'day', int $maxDepth = 1, string $categoryId = null): ProfitResult
{
    // Start multiple operations in parallel
    $promises = [
        'dataSeries' => $this->getDataSeriesAsync($salesCategoryStrategy, $maxDepth, $periodType),
        'flatTree' => $this->getFlatTreeAsync($salesCategoryStrategy, $categoryId),
        'units' => $this->getUnitsAsync($periodType),
    ];
    
    // Wait for all to complete
    $results = $this->awaitAll($promises);
    
    // Continue with processing
    return $this->processResults($results);
}
```

## 📊 Expected Performance Improvements

| Optimization | Current Time | Optimized Time | Improvement |
|--------------|--------------|----------------|-------------|
| Database Queries | 2-5 seconds | 0.5-1 second | **75-80%** |
| Array Processing | 1-2 seconds | 0.2-0.4 seconds | **80-85%** |
| Caching | Cache misses | 90%+ cache hits | **90%+** |
| Overall | 5-10 seconds | 1-2 seconds | **80-85%** |

## 🚀 Implementation Priority

1. **High Priority** (Immediate 50-70% improvement):
   - Consolidate database queries
   - Add proper indexes
   - Implement result caching

2. **Medium Priority** (Additional 15-25% improvement):
   - Optimize array processing
   - Reduce algorithmic complexity
   - Add memory optimization

3. **Low Priority** (Additional 5-10% improvement):
   - Implement async processing
   - Add advanced caching strategies
   - Optimize DateTime operations

## 🔧 Quick Wins (Can implement immediately)

1. **Increase cache duration** from 15s to 5-15 minutes
2. **Add composite database indexes**
3. **Cache final ProfitResult** for identical requests
4. **Pre-build category lookup tables**
5. **Optimize DateTime operations**
