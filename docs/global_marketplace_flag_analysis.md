# 🔍 Анализ мест для добавления флага is_enabled_sync_with_global_marketplace

## 📋 **Текущее использование флага**

Флаг `is_enabled_sync_with_global_marketplace` уже используется в:

1. **ProductToProductSynchronizer.php** - проверка при синхронизации
2. **PeriodsSaver.php** - проверка при сохранении периодов
3. **ProductCostsActionHelperTrait.php** - проверка доступа к управлению
4. **Product.php** - автоматическая синхронизация при изменении флага

## 🚨 **Места, где НУЖНО добавить проверку флага**

### 1. **ProductCostItem Actions - КРИТИЧНО**

#### **A. CreateAction для ProductCostItem**
**Файл:** `api/modules/v1/controllers/actions/productCostItem/CreateAction.php`
**Проблема:** Создание cost items без проверки флага синхронизации

```php
// НУЖНО ДОБАВИТЬ:
public function run()
{
    // Получить product по product_cost_period_id
    $productCostPeriod = ProductCostPeriod::findOne($request['product_cost_period_id']);
    $product = $productCostPeriod->product;
    
    // Проверить флаг синхронизации
    $this->checkIsAllowManageProductOrThrowException($product);
    
    // Продолжить создание...
}
```

#### **B. UpdateAction для ProductCostItem**
**Файл:** `api/modules/v1/controllers/actions/productCostItem/UpdateAction.php`
**Проблема:** Обновление cost items без проверки флага

#### **C. BulkUpdateAction для ProductCostItem**
**Файл:** `api/modules/v1/controllers/actions/productCostItem/BulkUpdateAction.php`
**Проблема:** Массовое обновление без проверки флага

### 2. **ProductCostPeriod Actions - КРИТИЧНО**

#### **A. CreateAction для ProductCostPeriod**
**Файл:** `api/modules/v1/controllers/actions/productCostPeriod/CreateAction.php`
**Проблема:** Создание периодов без проверки флага

#### **B. UpdateAction для ProductCostPeriod**
**Файл:** `api/modules/v1/controllers/actions/productCostPeriod/UpdateAction.php`
**Проблема:** Обновление периодов без проверки флага

### 3. **Product Import/Export - ВЫСОКИЙ ПРИОРИТЕТ**

#### **A. ProductCostPeriods Importer**
**Файл:** `common/components/dataImportExport/import/importer/ProductCostPeriods.php`
**Проблема:** Импорт данных без проверки флага синхронизации

```php
// НУЖНО ДОБАВИТЬ в методе import():
foreach ($dataToImport as $k => $data) {
    // Найти продукт
    $product = Product::find()->where([
        'marketplace_id' => $data['marketplace'],
        'seller_id' => $data['seller_id'],
        'sku' => $data['item_sku']
    ])->one();
    
    // Проверить флаг синхронизации
    if ($this->shouldSkipDueToGlobalMarketplaceSync($product)) {
        continue;
    }
    
    // Продолжить импорт...
}

private function shouldSkipDueToGlobalMarketplaceSync(Product $product): bool
{
    $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);
    
    return !empty($globalMarketplaceId) 
        && $product->marketplace_id !== $globalMarketplaceId
        && $product->is_enabled_sync_with_global_marketplace;
}
```

#### **B. BulkEditPartProcessor**
**Файл:** `common/components/dataImportExport/bulkEdit/product/BulkEditPartProcessor.php`
**Проблема:** Массовое редактирование без проверки флага

### 4. **ProductsSaver - ВЫСОКИЙ ПРИОРИТЕТ**

#### **A. COGSync ProductsSaver**
**Файл:** `common/components/COGSync/ProductsSaver.php`
**Проблема:** При сохранении продуктов нужно устанавливать флаг по умолчанию

```php
// НУЖНО ДОБАВИТЬ в метод fillIsEnabledSyncWithRepricerDefaultValue():
private function fillIsEnabledSyncWithGlobalMarketplaceDefaultValue(array $productInfos): array
{
    foreach ($productInfos as &$productInfo) {
        if (!isset($productInfo['is_enabled_sync_with_global_marketplace'])) {
            $productInfo['is_enabled_sync_with_global_marketplace'] = true; // По умолчанию включено
        }
    }
    return $productInfos;
}
```

#### **B. ProductSync**
**Файл:** `common/components/productSync/ProductSync.php**
**Проблема:** При синхронизации продуктов не устанавливается флаг

### 5. **API Controllers - СРЕДНИЙ ПРИОРИТЕТ**

#### **A. Product BulkEditAction**
**Файл:** `api/modules/v1/controllers/actions/product/BulkEditAction.php`
**Проблема:** Массовое редактирование продуктов без проверки флага

#### **B. Product EditAction**
**Файл:** `api/modules/v1/controllers/actions/product/EditAction.php`
**Проблема:** Редактирование отдельных продуктов без проверки флага

### 6. **Console Commands - СРЕДНИЙ ПРИОРИТЕТ**

#### **A. COGSynchronizer**
**Файл:** `common/components/COGSync/COGSynchronizer.php`
**Проблема:** При синхронизации COG не проверяется флаг

```php
// НУЖНО ДОБАВИТЬ в метод iterateProducts():
foreach ($this->iterateProducts($fromId, $toId, $this->dbManager->isDemo()) as $data) {
    $productInfos = $data['productInfos'];
    
    // Фильтровать продукты по флагу синхронизации
    $productInfos = $this->filterProductsByGlobalMarketplaceSync($productInfos);
    
    // Продолжить обработку...
}

private function filterProductsByGlobalMarketplaceSync(array $productInfos): array
{
    $filtered = [];
    foreach ($productInfos as $productInfo) {
        $product = Product::find()->where([
            'marketplace_id' => $productInfo['marketplace_id'],
            'seller_id' => $productInfo['seller_id'],
            'sku' => $productInfo['sku']
        ])->one();
        
        if (!$this->shouldSkipDueToGlobalMarketplaceSync($product)) {
            $filtered[] = $productInfo;
        }
    }
    return $filtered;
}
```

## 🎯 **Приоритеты реализации**

### **🔴 КРИТИЧНО (Немедленно)**
1. **ProductCostItem Actions** - прямое нарушение бизнес-логики
2. **ProductCostPeriod Actions** - прямое нарушение бизнес-логики
3. **Import/Export операции** - массовые нарушения

### **🟡 ВЫСОКИЙ ПРИОРИТЕТ (1-2 недели)**
1. **ProductsSaver** - установка флага по умолчанию
2. **BulkEdit операции** - массовые изменения
3. **ProductSync** - синхронизация продуктов

### **🟢 СРЕДНИЙ ПРИОРИТЕТ (1 месяц)**
1. **API Controllers** - пользовательские операции
2. **Console Commands** - фоновые процессы
3. **Дополнительные проверки** - edge cases

## 🛠️ **Рекомендуемый подход**

### **1. Создать общий trait для проверок**
```php
// common/components/traits/GlobalMarketplaceSyncTrait.php
trait GlobalMarketplaceSyncTrait
{
    protected function checkIsAllowManageProductOrThrowException(Product $product = null): void
    {
        if (empty($product)) {
            return;
        }

        $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);

        if (!empty($globalMarketplaceId)
            && $product->marketplace_id !== $globalMarketplaceId
            && $product->is_enabled_sync_with_global_marketplace
        ) {
            throw new \Exception(
                \Yii::t('admin', "Only periods for global marketplace can be modified when synchronization with global marketplace enabled.")
            );
        }
    }
    
    protected function shouldSkipDueToGlobalMarketplaceSync(Product $product): bool
    {
        $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);
        
        return !empty($globalMarketplaceId) 
            && $product->marketplace_id !== $globalMarketplaceId
            && $product->is_enabled_sync_with_global_marketplace;
    }
}
```

### **2. Добавить проверки в базовые классы**
```php
// В базовый Action класс
abstract class BaseProductAction extends Action
{
    use GlobalMarketplaceSyncTrait;
    
    protected function beforeRun(): bool
    {
        // Автоматическая проверка для всех product-related actions
        return parent::beforeRun();
    }
}
```

### **3. Обновить миграции**
```php
// Добавить флаг во все существующие продукты
UPDATE product 
SET is_enabled_sync_with_global_marketplace = true 
WHERE is_enabled_sync_with_global_marketplace IS NULL;
```
