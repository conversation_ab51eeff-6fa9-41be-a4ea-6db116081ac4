services:
  profit-dash-php-fpm-base: &php-fpm-base
    build:
      context: .
      dockerfile: docker/php-fpm/Dockerfile
      args:
        GITLAB_COMPOSER_USERNAME: ${GITLAB_COMPOSER_USERNAME}
        GITLAB_COMPOSER_TOKEN: ${GITLAB_COMPOSER_TOKEN}
        GITHUB_TOKEN: ${GITHUB_TOKEN}
        DEPLOY_ENV: ${DEPLOY_ENV}
        AUTH_TOKEN_URL: ${AUTH_TOKEN_URL}
      target: dev
    restart: always
    volumes:
      - ./:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"
#    depends_on:
#      - profit-dash-redis
#      - profit-dash-db
#      - profit-dash-rabbitmq

  profit-dash-php-fpm:
    <<: *php-fpm-base
    container_name: profit-dash-php-fpm

  #######
  # Uncomment only if you need to run these services locally.
  # In most cases it's not needed, you can run all of them separately.
  #######
  #    profit-dash-cron:
  #        <<: *php-fpm-base
  #        container_name: profit-dash-cron
  #        command: >
  #            bash -c "crontab /app/console/cron/crontab
  #            && cron && tail -f /dev/null"
  #        privileged: true
  #
  #    profit-dash-command:
  #        <<: *php-fpm-base
  #        container_name: profit-dash-command
  #        command: php /app/yii command/process
  #
  #    ### Consumers START ###
  #    consumer-event-periods-from-cache-to-clickhouse:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE.CONSUMER -l 32
  #
  #    consumer-financial-event-init_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.INIT.CONSUMER.0 -l 32
  #    consumer-financial-event-init_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.INIT.CONSUMER.1 -l 32
  #
  #    consumer-financial-event-refresh_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.REFRESH.CONSUMER.0 -l 32
  #    consumer-financial-event-refresh_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.REFRESH.CONSUMER.1 -l 32
  #
  #    consumer-cog-chagnes_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.0 -l 32
  #
  #    consumer-cog-chagnes_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.1 -l 32
  #
  #    consumer-cog-chagnes_2:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.2 -l 32
  #
  #    consumer-cog-chagnes_3:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.3 -l 32
  #
  #    consumer-cog-chagnes_4:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.4 -l 32
  #
  #    consumer-cron:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume CRON.CONSUMER -l 32
  #
  #    consumer-load-orders-init_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDERS.INIT.CONSUMER.0 -l 32
  #    consumer-load-orders-init_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDERS.INIT.CONSUMER.1 -l 32
  #
  #    consumer-load-order_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.0 -l 32
  #    consumer-load-order_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.1 -l 32
  #
  #    consumer-load-orders-refresh_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDERS.REFRESH.CONSUMER.0 -l 32
  #    consumer-load-orders-refresh_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDERS.REFRESH.CONSUMER.1 -l 32
  #
  #    consumer-load-order_items_0:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.0
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_1:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.1
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_2:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.2
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_3:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.3
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_4:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.4
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_5:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.5
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_6:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.6
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_7:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.7
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_8:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.8
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_9:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.9
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_10:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.10
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_11:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.11
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_12:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.12
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_13:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.13
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_14:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.14
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_15:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.15
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_16:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.16
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_17:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.17
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_18:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.18
  #        deploy:
  #            replicas: 1
  #
  #    consumer-load-order_items_19:
  #        <<: *php-fpm-base
  #        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.19
  #        deploy:
  #            replicas: 1
  #    ### Consumers END ###
  #
  #    profit-dash-flush-buffers:
  #        <<: *php-fpm-base
  #        command: php /app/yii file-buffer/flush-all-if-need 10 csv_buffers
  #
  #    profit-dash-re-flush-broken-buffers:
  #        <<: *php-fpm-base
  #        command: php /app/yii file-buffer/flush-all-if-need 60 csv_buffers_broken
  #
  #    migrate-orders-items:
  #        <<: *php-fpm-base
  #        command: php /app/yii orders/migrate-order-items

  profit-dash-nginx:
    container_name: profit-dash-nginx
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
      args:
        PROMETHEUS_BASIC_AUTH_USER: ${PROMETHEUS_BASIC_AUTH_USER}
        PROMETHEUS_BASIC_AUTH_PASSWORD: ${PROMETHEUS_BASIC_AUTH_PASSWORD}
    volumes:
      - ./:/app
    depends_on:
      - profit-dash-php-fpm
    ports:
      - 8080:80
      - 443:443
      - 9090:9090

#  profit-dash-redis:
#    image: harbor.sl.local/proxy-cache/redis:5.0-alpine
#    container_name: profit-dash-redis
#    volumes:
#      - profit-dash-redis:/data
#    command: redis-server --requirepass secret
#
#  profit-dash-rabbitmq:
#    build:
#      context: docker/rabbitmq
#    container_name: profit-dash-rabbitmq
#    environment:
#      RABBITMQ_ERLANG_COOKIE: djhdjflksdjw
#      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
#      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
#    volumes:
#      - profit-dash-rabbitmq:/etc/rabbitmq/
#      - profit-dash-rabbitmq:/var/lib/rabbitmq/
#      - profit-dash-rabbitmq:/var/log/rabbitmq/
#    ports:
#      - 5672:5672
#      - 15672:15672
#
#  profit-dash-clickhouse:
#    container_name: profit-dash-clickhouse
#    image: harbor.sl.local/proxy-cache/yandex/clickhouse-server:latest
#    ports:
#      - 8123:8123
#      - 9000:9000
#    volumes:
#      - profit-dash-clickhouse:/var/lib/clickhouse
#      - ./docker/clickhouse/config.xml:/etc/clickhouse-server/config.d/profit_cluster.xml
#
#  profit-dash-clickhouse-client:
#    container_name: profit-dash-clickhouse-client
#    image: harbor.sl.local/proxy-cache/yandex/clickhouse-client:latest
#
#  profit-dash-db:
#    container_name: profit-dash-db
#    image: harbor.sl.local/proxy-cache/postgres:13.2-alpine
#    volumes:
#      - profit-dash-db:/var/lib/postgresql/data
#      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
#    environment:
#      POSTGRES_USER: ${APP_DB_USERNAME}
#      POSTGRES_PASSWORD: ${APP_DB_PASSWORD}
#      POSTGRES_DB: ${APP_DB_HOST}
#    ports:
#      - 5432:5432
#
#  repricer-event-db:
#      container_name: repricer-event-db
#      image: harbor.sl.local/proxy-cache/postgres:13.2-alpine
#      volumes:
#          - repricer-event-db:/var/lib/postgresql/data
#          - ./docker/postgres/repricer_event_init.sql:/docker-entrypoint-initdb.d/init.sql
#      environment:
#          POSTGRES_DB: 'repricer_event'
#          POSTGRES_USER: ${REPRICER_EVENT_DB_USERNAME}
#          POSTGRES_PASSWORD: ${REPRICER_EVENT_DB_PASSWORD}
#      ports:
#          - 5433:5432

#volumes:
#  profit-dash-db:
#  repricer-event-db:
#  profit-dash-redis:
#  profit-dash-rabbitmq:
#  profit-dash-clickhouse:
#  profit-dash-clickhouse-1:

networks:
  default:
    name: sellerlogic
    external: true
