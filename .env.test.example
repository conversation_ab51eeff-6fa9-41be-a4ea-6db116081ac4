TEST_OAUTH_CLIENT_ID=openapi
TEST_OAUTH_CLIENT_SECRET=openapisecret
TEST_OAUTH_USERNAME=
TEST_OAUTH_PASSWORD=
TEST_OAUTH_TOKEN_URL=https://auth-staging.sl.local/oauth2/token
# composer
GITLAB_COMPOSER_USERNAME=gitlab-ci-token
GITLAB_COMPOSER_TOKEN=
GITHUB_TOKEN=

# auth
MAIN_API_URL=http://repricer-app:8800
AUTH_TOKEN_URL=http://localhost:8888/oauth2/token

# DB
APP_DB_HOST=profit-dash-db
APP_DB_SLAVE_HOST=profit-dash-db
APP_DB_PORT=5432
APP_DB_SLAVE_PORT=5432
APP_DB_USERNAME=root
APP_DB_PASSWORD=root
APP_DB_USERNAME_SLAVE=root
APP_DB_PASSWORD_SLAVE=root

# Repricer Event DB
REPRICER_EVENT_DB_HOST=repricer-event-db
REPRICER_EVENT_DB_PORT=5432
REPRICER_EVENT_DB_USERNAME=root
REPRICER_EVENT_DB_PASSWORD=root
REPRICER_EVENT_DB_NAME=repricer_event

# Repricer DB
REPRICER_DB_HOST=repricer-db
REPRICER_DB_HOST_1=repricer-second-db
REPRICER_DB_USERNAME=root
REPRICER_DB_PASSWORD=root

# Customer service DB
CUSTOMER_SERVICE_DB_HOST=
CUSTOMER_SERVICE_DB_PORT=
CUSTOMER_SERVICE_DB_USERNAME=
CUSTOMER_SERVICE_DB_PASSWORD=
CUSTOMER_SERVICE_DB_NAME=

# ClickHouse
CLICKHOUSE_HOST=profit-dash-clickhouse
CLICKHOUSE_PORT=8123
CLICKHOUSE_DB_NAME=default
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_CLUSTER_NAME=profit_cluster

# redis
APP_REDIS_HOST=profit-dash-redis
APP_REDIS_PORT=6379
APP_REDIS_STANDARD_CACHE_DB=1
APP_REDIS_MUTEX_CACHE_DB=6
APP_REDIS_SCHEMA_CACHE_DB=2
APP_REDIS_PASSWORD=secret

# rabbitmq
# 0|1
RABBITMQ_SSL=0
RABBITMQ_HOST=profit-dash-rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_VHOST=/
RABBITMQ_DEFAULT_USER=guest
RABBITMQ_DEFAULT_PASS=guest

# Development|Staging|Production|Local
DEPLOY_ENV=Local
# local|dev|rc|prod
YII_ENV=local
# 0|1
YII_DEBUG=1

PROFIT_DASH_DB_NAME=profit_dash_db
FINANCE_DB_NAME_PREFIX=finance

ROLLBAR_ACCESS_TOKEN=11111111111111111111111111111111

TOKEN_SERVICE_API_URL=http://tokens-dev.sellerlogic.com:8080

# Selling API credentials
SELLING_API_ACCESS_KEY=
SELLING_API_ACCESS_SECRET=
SELLING_API_ROLE_ARN=

# Internal API
INTERNAL_API_CLIENT_ID=
INTERNAL_API_CLIENT_SECRET=
INTERNAL_API_GRANT_TYPE=client_credentials

# BAS API client
BAS_API_URL=http://profit-dash-nginx
BAS_AUTH_TOKEN_URL=http://repricer-auth:8888
BAS_INTERNAL_API_CLIENT_ID=1
BAS_INTERNAL_API_CLIENT_SECRET=1
BAS_INTERNAL_API_GRANT_TYPE=client_credentials

APP_HOST=

PROMETHEUS_BASIC_AUTH_USER=prom
PROMETHEUS_BASIC_AUTH_PASSWORD=53123edg33535
PROMETHEUS_REDIS_HOST=profit-dash-redis
PROMETHEUS_REDIS_PORT=6379
PROMETHEUS_REDIS_PASSWORD=secret

SLACK_WEB_HOOK_METRICS_MONITOR=
SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR=

# AWS
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
AWS_REGION=
AWS_VERSION=
AWS_BUCKET=
AWS_PUBLIC_BUCKET=

# File Client Configuration
FILE_CLIENT_USE_CURL_FOR_HTTP=false

# SFTPgo S3 Configuration (for sftp.sellerlogic.com)
SFTPGO_S3_BUCKET=
SFTPGO_S3_REGION=
SFTPGO_S3_ACCESS_KEY=
SFTPGO_S3_ACCESS_SECRET=
SFTPGO_S3_ENDPOINT=
