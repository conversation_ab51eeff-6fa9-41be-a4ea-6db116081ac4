default:
  image: harbor.sl.local/develop/base_images/base-ci:latest

stages:
  - merge
  - build_and_push
  - pull_and_prepare
  - migrate
  - deploy_api
  - stop_workers
  - deploy_workers
  - translation_sync

.kaniko_auth: &kaniko_auth
  before_script:
    - mkdir -p /kaniko/.docker
    - mkdir -p /kaniko/.docker/certs
    - cat $CA_CERT >> /kaniko/.docker/certs/ca-certificates.crt
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json

.ssh_key: &ssh_key
  before_script:
    - eval $(ssh-agent -s)
    - echo "$SSH_ADMIN_DEPLOY_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa

variables:
  ENVIRONMENT: 'Staging'
  WORKER_FILE_POSTIFX: '.develop'
  SKIP_MIGRATIONS: 'false'
  MERGE_BRANCH: master # default value just for to be sure nothing will be broken


workflow:
  rules:
    - if: $CI_COMMIT_REF_NAME == 'rc'
      variables:
        ENVIRONMENT: 'Rc'
        MERGE_BRANCH: 'release'
    - if: $CI_COMMIT_REF_NAME == 'master'
      variables:
        ENVIRONMENT: 'Production'
        WORKER_FILE_POSTIFX: ''
        MERGE_BRANCH: 'rc'
    ## do not run pipeline on master after merge
    - if: $CI_COMMIT_REF_NAME != "master" || $CI_PIPELINE_SOURCE != 'push'

## Merge and run migrations
MergeAndRunMigrations:
    stage: merge
    environment:
      name: $ENVIRONMENT
    tags:
        - profit
    only:
      refs:
        - master
        - rc
      variables:
        - $MERGE == "true"
    <<: *ssh_key
    script:
      - |-
        merge_requests=$(curl -k --request GET --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/merge_requests?source_branch=$MERGE_BRANCH&target_branch=$CI_COMMIT_REF_NAME&state=opened")
        merge_iid=$(echo "$merge_requests" | jq -r '.[0].iid')
        if [ "$merge_iid" = "null" ]; then
          response=$(curl -k --request POST --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/merge_requests?source_branch=$MERGE_BRANCH&target_branch=$CI_COMMIT_REF_NAME&title=Automatically%20created%20MR")
          echo "$response"
          merge_iid=$(echo "$response" | jq -r ".iid")
          if [ -z "$merge_iid" ]; then echo "Error: Failed to create merge request"; exit 1; fi
        fi
        for i in {1..5}; do
          response=$(curl -k --request GET --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/merge_requests/$merge_iid")
          changes_count=$(echo "$response" | jq -r '.changes_count')
          if [ "changes_count" != "null" ]; then
            echo "Attempt $i: Waiting for the merge request to be prepared..."
            sleep 2
          else
            echo "Merge request is prepared"
            break
          fi
        done
        if [ "$changes_count" = "null" ] || [ $changes_count -le 0 ]; then
          echo "Merge request has no changes. Skip merging" 
          exit 0
        fi
        for i in {1..5}; do
          merge_response=$(curl -k --request PUT --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" --header "Content-Type: application/json" --data "{\"ref\": \"$CI_COMMIT_REF_NAME\", \"variables\": [{\"key\": \"MIGRATIONS_ONLY\", \"value\": \"true\"}]}" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/merge_requests/$merge_iid/merge");
          if [ $? -eq 0 ]; then
            echo "$merge_response"
            break
          else
            if [ $i -eq 5 ]; then
              echo "Error: Failed to merge merge request after $i attempts"
              exit 1
            fi
            echo "Attempt $i: Failed to merge. Retrying in 2 seconds..."
            sleep 2
          fi
        done
        # Wait for the downstream pipeline to finish
        while true; do
          pipeline=$(curl -k --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/pipelines/latest?ref=$CI_COMMIT_REF_NAME")
          status=$(echo "$pipeline" | jq -r '.status')
          pipeline_id=$(echo "$pipeline" | jq -r '.id')
          if [ "$status" = "running" ]; then
            job_id=$(curl -k --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/pipelines/$pipeline_id/jobs" | jq -r '.[] | select(.name == "deploy_api").id')
            if [ -n "$job_id" ]; then
              job_status=$(curl -k --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/jobs/$job_id" | jq -r '.status')
              if [ "$job_status" != "canceled" ]; then
                  echo "Job ID for deploy_api: $job_id"
                  # Cancel the job
                  curl -k --request POST --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "https://gitlab.sl.local/api/v4/projects/$CI_PROJECT_ID/jobs/$job_id/cancel"
              fi
            else
              echo "Job deploy_api not found in the running pipeline."
            fi
          fi
        
          if [ "$status" != "success" ] && [ "$status" != "failed" ] && [ "$status" != "canceled" ]; then
            echo "Downstream pipeline status: $status. Waiting for it to finish..."
            sleep 30
          else
            echo "Downstream pipeline status: $status"
            if [ "$status" != "success" ] && [ "$status" != "canceled" ]; then
              echo "Error: Downstream pipeline did not succeed"
              exit 1
            fi
            break
          fi
        done

## Build
build:
  stage: build_and_push
  environment:
    name: $ENVIRONMENT
  tags:
    - profit
  only:
    - staging
    - rc
    - master
  except:
    variables:
      - $MERGE == "true"
  image:
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  <<: *kaniko_auth
  script:
    - cp .env.ci .env
    - sed -i "s/{GITLAB_COMPOSER_TOKEN}/$GITLAB_COMPOSER_TOKEN/g" .env
    - sed -i "s/{GITHUB_TOKEN}/$GITHUB_TOKEN/g" .env
    - sed -i "s|{MAIN_API_URL}|$MAIN_API_URL|g" .env
    - sed -i "s|{AUTH_TOKEN_URL}|$AUTH_TOKEN_URL|g" .env
    - sed -i "s/{APP_DB_HOST}/$APP_DB_HOST/g" .env
    - sed -i "s/{APP_DB_PORT}/$APP_DB_PORT/g" .env
    - sed -i "s|{APP_HOST}|$APP_HOST|g" .env
    - sed -i "s/{APP_DB_USERNAME}/$APP_DB_USERNAME/g" .env
    - sed -i "s/{APP_DB_PASSWORD}/$APP_DB_PASSWORD/g" .env
    - sed -i "s/{APP_DB_USERNAME_SLAVE}/$APP_DB_USERNAME_SLAVE/g" .env
    - sed -i "s/{APP_DB_PASSWORD_SLAVE}/$APP_DB_PASSWORD_SLAVE/g" .env
    - sed -i "s/{APP_DB_SLAVE_HOST}/$APP_DB_SLAVE_HOST/g" .env
    - sed -i "s/{APP_DB_SLAVE_PORT}/$APP_DB_SLAVE_PORT/g" .env
    - sed -i "s/{APP_DB_1_HOST}/$APP_DB_1_HOST/g" .env
    - sed -i "s/{APP_DB_1_PORT}/$APP_DB_1_PORT/g" .env
    - sed -i "s/{APP_DB_1_USERNAME}/$APP_DB_1_USERNAME/g" .env
    - sed -i "s/{APP_DB_1_PASSWORD}/$APP_DB_1_PASSWORD/g" .env
    - sed -i "s/{APP_DB_1_USERNAME_SLAVE}/$APP_DB_1_USERNAME_SLAVE/g" .env
    - sed -i "s/{APP_DB_1_PASSWORD_SLAVE}/$APP_DB_1_PASSWORD_SLAVE/g" .env
    - sed -i "s/{APP_DB_1_SLAVE_HOST}/$APP_DB_1_SLAVE_HOST/g" .env
    - sed -i "s/{APP_DB_1_SLAVE_PORT}/$APP_DB_1_SLAVE_PORT/g" .env
    - sed -i "s/{CLICKHOUSE_HOST}/$CLICKHOUSE_HOST/g" .env
    - sed -i "s/{CLICKHOUSE_DB_NAME}/$CLICKHOUSE_DB_NAME/g" .env
    - sed -i "s/{CLICKHOUSE_USERNAME}/$CLICKHOUSE_USERNAME/g" .env
    - sed -i "s/{CLICKHOUSE_PASSWORD}/$CLICKHOUSE_PASSWORD/g" .env
    - sed -i "s/{APP_REDIS_STANDARD_CACHE_DB}/$APP_REDIS_STANDARD_CACHE_DB/g" .env
    - sed -i "s/{APP_REDIS_SCHEMA_CACHE_DB}/$APP_REDIS_SCHEMA_CACHE_DB/g" .env
    - sed -i "s/{APP_REDIS_FAST_PERSISTENT_CACHE_DB}/$APP_REDIS_FAST_PERSISTENT_CACHE_DB/g" .env
    - sed -i "s/{APP_REDIS_MUTEX_CACHE_DB}/$APP_REDIS_MUTEX_CACHE_DB/g" .env
    - sed -i "s/{APP_REDIS_PROMETHEUS_DB}/$APP_REDIS_PROMETHEUS_DB/g" .env
    - sed -i "s/{APP_REDIS_HOST}/$APP_REDIS_HOST/g" .env
    - sed -i "s/{APP_REDIS_PORT}/$APP_REDIS_PORT/g" .env
    - sed -i "s/{APP_REDIS_PASSWORD}/$APP_REDIS_PASSWORD/g" .env
    - sed -i "s/{RABBITMQ_SSL}/$RABBITMQ_SSL/g" .env
    - sed -i "s/{RABBITMQ_HOST}/$RABBITMQ_HOST/g" .env
    - sed -i "s/{RABBITMQ_VHOST}/$RABBITMQ_VHOST/g" .env
    - sed -i "s/{RABBITMQ_DEFAULT_USER}/$RABBITMQ_DEFAULT_USER/g" .env
    - sed -i "s/{RABBITMQ_DEFAULT_PASS}/$RABBITMQ_DEFAULT_PASS/g" .env
    - sed -i "s/{DEPLOY_ENV}/$DEPLOY_ENV/g" .env
    - sed -i "s/{YII_ENV}/$YII_ENV/g" .env
    - sed -i "s/{YII_DEBUG}/$YII_DEBUG/g" .env
    - sed -i "s/{ROLLBAR_ACCESS_TOKEN}/$ROLLBAR_ACCESS_TOKEN/g" .env
    - sed -i "s|{TOKEN_SERVICE_API_URL}|$TOKEN_SERVICE_API_URL|g" .env
    - sed -i "s/{SELLING_API_ACCESS_KEY}/$SELLING_API_ACCESS_KEY/g" .env
    - sed -i "s|{SELLING_API_ACCESS_SECRET}|$SELLING_API_ACCESS_SECRET|g" .env
    - sed -i "s|{SELLING_API_ROLE_ARN}|$SELLING_API_ROLE_ARN|g" .env
    - sed -i "s|{AMAZON_ADS_API_CLIENT_ID}|$AMAZON_ADS_API_CLIENT_ID|g" .env
    - sed -i "s|{AMAZON_ADS_API_CLIENT_SECRET}|$AMAZON_ADS_API_CLIENT_SECRET|g" .env
    - sed -i "s|{INTERNAL_API_CLIENT_ID}|$INTERNAL_API_CLIENT_ID|g" .env
    - sed -i "s|{INTERNAL_API_CLIENT_SECRET}|$INTERNAL_API_CLIENT_SECRET|g" .env
    - sed -i "s|{BAS_API_URL}|$BAS_API_URL|g" .env
    - sed -i "s|{BAS_AUTH_TOKEN_URL}|$BAS_AUTH_TOKEN_URL|g" .env
    - sed -i "s|{BAS_INTERNAL_API_CLIENT_ID}|$BAS_INTERNAL_API_CLIENT_ID|g" .env
    - sed -i "s|{BAS_INTERNAL_API_CLIENT_SECRET}|$BAS_INTERNAL_API_CLIENT_SECRET|g" .env
    - sed -i "s|{BAS_INTERNAL_API_GRANT_TYPE}|$BAS_INTERNAL_API_GRANT_TYPE|g" .env
    - sed -i "s|{REPRICER_DB_HOST}|$REPRICER_DB_HOST|g" .env
    - sed -i "s|{REPRICER_DB_HOST_1}|$REPRICER_DB_HOST_1|g" .env
    - sed -i "s|{REPRICER_DB_USERNAME}|$REPRICER_DB_USERNAME|g" .env
    - sed -i "s|{REPRICER_DB_PASSWORD}|$REPRICER_DB_PASSWORD|g" .env
    - sed -i "s|{REPRICER_EVENT_DB_HOST}|$REPRICER_EVENT_DB_HOST|g" .env
    - sed -i "s|{REPRICER_EVENT_DB_USERNAME}|$REPRICER_EVENT_DB_USERNAME|g" .env
    - sed -i "s|{REPRICER_EVENT_DB_PASSWORD}|$REPRICER_EVENT_DB_PASSWORD|g" .env
    - sed -i "s|{REPRICER_EVENT_DB_NAME}|$REPRICER_EVENT_DB_NAME|g" .env
    - sed -i "s|{REPRICER_EVENT_DB_PORT}|$REPRICER_EVENT_DB_PORT|g" .env
    - sed -i "s|{CUSTOMER_SERVICE_DB_HOST}|$CUSTOMER_SERVICE_DB_HOST|g" .env
    - sed -i "s|{CUSTOMER_SERVICE_DB_PORT}|$CUSTOMER_SERVICE_DB_PORT|g" .env
    - sed -i "s|{CUSTOMER_SERVICE_DB_USERNAME}|$CUSTOMER_SERVICE_DB_USERNAME|g" .env
    - sed -i "s|{CUSTOMER_SERVICE_DB_PASSWORD}|$CUSTOMER_SERVICE_DB_PASSWORD|g" .env
    - sed -i "s|{CUSTOMER_SERVICE_DB_NAME}|$CUSTOMER_SERVICE_DB_NAME|g" .env
    - sed -i "s|{PROMETHEUS_REDIS_HOST}|$PROMETHEUS_REDIS_HOST|g" .env
    - sed -i "s|{PROMETHEUS_REDIS_PORT}|$PROMETHEUS_REDIS_PORT|g" .env
    - sed -i "s|{PROMETHEUS_REDIS_PASSWORD}|$PROMETHEUS_REDIS_PASSWORD|g" .env
    - sed -i "s|{CLICKHOUSE_MASTER_NODES}|$CLICKHOUSE_MASTER_NODES|g" .env
    - sed -i "s|{CLICKHOUSE_MASTER_NODES_MAP}|$CLICKHOUSE_MASTER_NODES_MAP|g" .env
    - sed -i "s|{CLICKHOUSE_CLUSTER_NAME}|$CLICKHOUSE_CLUSTER_NAME|g" .env
    - sed -i "s|{SLACK_WEB_HOOK_METRICS_MONITOR}|$SLACK_WEB_HOOK_METRICS_MONITOR|g" .env
    - sed -i "s|{SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR}|$SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR|g" .env
    - sed -i "s|{AWS_ACCESS_KEY}|$AWS_ACCESS_KEY|g" .env
    - sed -i "s|{AWS_SECRET_KEY}|$AWS_SECRET_KEY|g" .env
    - sed -i "s|{AWS_REGION}|$AWS_REGION|g" .env
    - sed -i "s|{AWS_VERSION}|$AWS_VERSION|g" .env
    - sed -i "s|{AWS_BUCKET}|$AWS_BUCKET|g" .env
    - sed -i "s|{AWS_PUBLIC_BUCKET}|$AWS_PUBLIC_BUCKET|g" .env
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - /kaniko/executor
      --context .
      --dockerfile docker/php-fpm/Dockerfile
      --destination $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG
      --cache=true
      --cache-ttl=24h
      --registry-certificate harbor.sl.local=/kaniko/.docker/certs/ca-certificates.crt
      --build-arg GITLAB_COMPOSER_USERNAME=${GITLAB_COMPOSER_USERNAME}
      --build-arg GITLAB_COMPOSER_TOKEN=${GITLAB_COMPOSER_TOKEN}
      --build-arg GITHUB_TOKEN=${GITHUB_TOKEN}
      --build-arg AUTH_TOKEN_URL=${AUTH_TOKEN_URL}
      --build-arg DEPLOY_ENV=${DEPLOY_ENV}
      --build-arg NEW_RELIC_AGENT_VERSION=${NEW_RELIC_AGENT_VERSION}
      --build-arg NEW_RELIC_LICENSE_KEY=${NEW_RELIC_LICENSE_KEY}
      --build-arg NEW_RELIC_APPNAME=profit-dash-${DEPLOY_ENV}
    - /kaniko/executor
      --context .
      --dockerfile docker/nginx/Dockerfile
      --destination $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-nginx:$IMGTAG
      --destination $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-nginx:latest
      --cache=true
      --cache-ttl=24h
      --registry-certificate harbor.sl.local=/kaniko/.docker/certs/ca-certificates.crt
      --build-arg PROMETHEUS_BASIC_AUTH_USER=${PROMETHEUS_BASIC_AUTH_USER}
      --build-arg PROMETHEUS_BASIC_AUTH_PASSWORD=${PROMETHEUS_BASIC_AUTH_PASSWORD}


## Pull docker images
pull_and_prepare:
  tags:
    - profit
  environment:
    name: $ENVIRONMENT
  stage: pull_and_prepare
  only:
    - staging
    - rc
    - master
  except:
    variables:
      - $MERGE == "true"
  <<: *ssh_key
  script:
    - |
      export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
      sed -i "s/{IMGTAG}/$IMGTAG/g" docker-compose.ci.common.yml
      sed -i "s/{REGISTRY}/$CI_REGISTRY/g" docker-compose.ci.common.yml
      sed -i "s/{NEWRELIC_LICENSE_KEY}/$NEW_RELIC_LICENSE_KEY/g" docker-compose.ci.common.yml
      sed -i "s/{IMGTAG}/$IMGTAG/g" docker-compose.ci.worker${WORKER_FILE_POSTIFX}.yml
      sed -i "s/{REGISTRY}/$CI_REGISTRY/g" docker-compose.ci.worker${WORKER_FILE_POSTIFX}.yml
      sed -i "s/{IMGTAG}/$IMGTAG/g" docker-compose.ci.worker${WORKER_FILE_POSTIFX}.reserve.yml
      sed -i "s/{REGISTRY}/$CI_REGISTRY/g" docker-compose.ci.worker${WORKER_FILE_POSTIFX}.reserve.yml
      scp -i ~/.ssh/id_rsa -P2202 -o StrictHostKeyChecking=no ./docker-compose.ci.common.yml admin@"$DEPLOY_SERVER_IP":/deploy/docker-compose.yml
      ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker image prune -a -f
      cd /deploy &&
      docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY &&
      docker pull $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG && echo php-fpm pull ok &&
      docker pull $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-nginx && echo nginx pull ok"
    - |
      DEPLOY_SERVER_IPS=$(echo $DEPLOY_WORKER_SERVER_IP | tr "," "\n")
      for WORKER_SERVER_IP in $DEPLOY_SERVER_IPS
      do
        echo "Pull and prepare on worker server ${WORKER_SERVER_IP} | started"
        scp -i ~/.ssh/id_rsa -P2202 -o StrictHostKeyChecking=no ./docker-compose.ci.worker${WORKER_FILE_POSTIFX}.yml admin@"$WORKER_SERVER_IP":/deploy/docker-compose.yml
        scp -i ~/.ssh/id_rsa -P2202 -o StrictHostKeyChecking=no ./docker-compose.ci.worker${WORKER_FILE_POSTIFX}.reserve.yml admin@"$WORKER_SERVER_IP":/deploy/docker-compose.reserve.yml
        ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$WORKER_SERVER_IP" -p 2202 "
        cd /deploy &&
        docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY &&
        docker pull $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG && echo php-fpm pull ok"
        echo "Pull and prepare on worker server ${WORKER_SERVER_IP} | done"
      done

## Migrate
migrate:
  tags:
    - profit
  environment:
    name: $ENVIRONMENT
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/common/*
      - console/migrations/clickhouse/common/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  <<: *ssh_key
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate --interactive=0 &&
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse --interactive=0"

## Migrate finance
.migrate_finance: &migrate_finance
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/finance/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_finance_1_3000:
  <<: *migrate_finance
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-finance 0 0 3000 --interactive=0"

migrate_finance_3000_6000:
  <<: *migrate_finance
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-finance 0 3000 6000 --interactive=0"

migrate_finance_6000_9000:
  <<: *migrate_finance
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-finance 0 6000 9000 --interactive=0"

migrate_finance_9000_12000:
  <<: *migrate_finance
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-finance 0 9000 12000 --interactive=0"

migrate_finance_12000_N:
  <<: *migrate_finance
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-finance 0 12000 20000  --interactive=0"

## Migrate orders
.migrate_orders: &migrate_orders
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/order/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_orders_1_3000:
  <<: *migrate_orders
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-order 0 0 3000 --interactive=0"

migrate_orders_3000_6000:
  <<: *migrate_orders
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-order 0 3000 6000 --interactive=0"

migrate_orders_6000_9000:
  <<: *migrate_orders
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-order 0 6000 9000 --interactive=0"

migrate_orders_9000_12000:
  <<: *migrate_orders
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-order 0 9000 12000 --interactive=0"

migrate_orders_12000_N:
  <<: *migrate_orders
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-order 0 12000 20000  --interactive=0"

## Migrate ads
.migrate_ads: &migrate_ads
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/ads/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_ads_1_3000:
  <<: *migrate_ads
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-ads 0 0 3000 --interactive=0"

migrate_ads_3000_6000:
  <<: *migrate_ads
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-ads 0 3000 6000 --interactive=0"

migrate_ads_6000_9000:
  <<: *migrate_ads
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-ads 0 6000 9000 --interactive=0"

migrate_ads_9000_12000:
  <<: *migrate_ads
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-ads 0 9000 12000 --interactive=0"

migrate_ads_12000_N:
  <<: *migrate_ads
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-ads 0 12000 20000  --interactive=0"

## Migrate customer
.migrate_customer:  &migrate_customer
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/customer/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_customer_1_3000:
  <<: *migrate_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-customer 0 0 3000 --interactive=0"

migrate_customer_3000_6000:
  <<: *migrate_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-customer 0 3000 6000 --interactive=0"

migrate_customer_6000_9000:
  <<: *migrate_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-customer 0 6000 9000 --interactive=0"

migrate_customer_9000_12000:
  <<: *migrate_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-customer 0 9000 12000 --interactive=0"

migrate_customer_12000_N:
  <<: *migrate_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-customer 0 12000 20000  --interactive=0"


## Migrate clickhouse
.migrate_clickhouse_customer: &migrate_clickhouse_customer
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/clickhouse/customer/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_clickhouse_customer_1_3000:
  <<: *migrate_clickhouse_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer 0 0 3000 --interactive=0 "

migrate_clickhouse_customer_3000_6000:
  <<: *migrate_clickhouse_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer 0 3000 6000 --interactive=0 "

migrate_clickhouse_customer_6000_9000:
  <<: *migrate_clickhouse_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer 0 6000 9000 --interactive=0 "

migrate_clickhouse_customer_9000_12000:
  <<: *migrate_clickhouse_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer 0 9000 12000 --interactive=0 "

migrate_clickhouse_customer_12000_N:
  <<: *migrate_clickhouse_customer
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer 0 12000 20000 --interactive=0 "


## Migrate clickhouse
.migrate_clickhouse_customer_related:  &migrate_clickhouse_customer_related
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/clickhouse/customerRelated/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  environment:
    name: $ENVIRONMENT
  <<: *ssh_key

migrate_clickhouse_customer_related_1_3000:
  <<: *migrate_clickhouse_customer_related
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer-related 0 0 3000 --interactive=0"

migrate_clickhouse_customer_related_3000_6000:
  <<: *migrate_clickhouse_customer_related
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer-related 0 3000 6000 --interactive=0"

migrate_clickhouse_customer_related_6000_9000:
  <<: *migrate_clickhouse_customer_related
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer-related 0 6000 9000 --interactive=0"

migrate_clickhouse_customer_related_9000_12000:
  <<: *migrate_clickhouse_customer_related
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer-related 0 9000 12000 --interactive=0"

migrate_clickhouse_customer_related_12000_N:
  <<: *migrate_clickhouse_customer_related
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-clickhouse-customer-related 0 12000 20000  --interactive=0"

## Migrate repricer event
migrate_repricer_event:
  tags:
    - profit
  stage: migrate
  only:
    refs:
      - staging
      - rc
      - master
    changes:
      - console/migrations/repricerEvent/*
  except:
    variables:
      - $SKIP_MIGRATIONS == "true"
      - $MERGE == "true"
  <<: *ssh_key
  environment:
    name: $ENVIRONMENT
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii migrate-repricer-event --interactive=0"

## Deployment api
deploy_api:
  tags:
    - profit
  stage: deploy_api
  only:
    - staging
    - rc
    - master
  environment:
    name: $ENVIRONMENT
  except:
    variables:
      - $MERGE == "true"
      - $MIGRATIONS_ONLY == "true"
  <<: *ssh_key
  script:
    - |
      ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      cd /deploy
      service_name=profit-dash-php-fpm
      old_container_id=\$(docker ps -f name=\$service_name -q | tail -n1)
      if [[ -z \$old_container_id ]]; then
        docker compose up -d --remove-orphans
      else
        docker compose up -d --no-deps --scale \$service_name=2 --no-recreate \$service_name
        docker exec profit-dash-nginx /usr/sbin/nginx -s reload
        docker stop \$old_container_id
        docker rm \$old_container_id
        docker compose up -d --no-deps --scale \$service_name=1 --no-recreate \$service_name
        docker exec profit-dash-nginx /usr/sbin/nginx -s reload
        docker compose up -d profit-dash-cron
        docker compose -f docker-compose.yml up -d newrelic-php-daemon newrelic-infra
      fi
      docker system prune -a -f
      curl 127.0.0.1
      sleep 2
      docker cp \$(docker ps -f name=profit-dash-php-fpm -q | tail -n1):/app/api/web ./web
      docker cp ./web \$(docker ps -f name=profit-dash-nginx -q | tail -n1):/app/api
      rm -rf ./web
      docker compose -f docker-compose.yml run --rm profit-dash-php-fpm /app/yii cache/flush-all --interactive=0
      docker compose -f docker-compose.yml run --rm profit-dash-php-fpm /app/yii sales-categories/map
      docker compose -f docker-compose.yml run --rm profit-dash-php-fpm /app/yii cache/flush-all --interactive=0"

stop_workers:
  tags:
    - profit
  stage: stop_workers
  only:
    - staging
    - rc
    - master
  environment:
    name: $ENVIRONMENT
  except:
    variables:
      - $MERGE == "true"
  <<: *ssh_key
  script:
    - |
      ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      cd /deploy &&
      docker compose -f docker-compose.yml run --rm profit-dash-php-fpm /app/yii process-manager/freeze &&
      docker compose -f docker-compose.yml run --rm profit-dash-php-fpm /app/yii process-manager/wait-for-release"

      DEPLOY_SERVER_IPS=$(echo $DEPLOY_WORKER_SERVER_IP | tr "," "\n")
      INDEX=0
      for WORKER_SERVER_IP in $DEPLOY_SERVER_IPS
      do
        echo "Stop containers on worker server ${WORKER_SERVER_IP} | started"
        if [[ "$INDEX" -eq "0" ]]; then
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$WORKER_SERVER_IP" -p 2202 "
          cd /deploy &&
          COMPOSE_HTTP_TIMEOUT=200 docker compose -f docker-compose.yml -f docker-compose.reserve.yml rm -f -s" &
        else
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$WORKER_SERVER_IP" -p 2202 "
          cd /deploy &&
          COMPOSE_HTTP_TIMEOUT=200 docker compose rm -f -s" &
        fi
        INDEX=$((INDEX+1))
      done
      wait
      echo "All worker servers stopped."
      ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      cd /deploy && docker compose run --rm profit-dash-php-fpm /app/yii cache/flush-all --interactive=0"

deploy_workers:
  tags:
    - profit
  stage: deploy_workers
  only:
    - staging
    - rc
    - master
  environment:
    name: $ENVIRONMENT
  except:
    variables:
      - $MERGE == "true"
  <<: *ssh_key
  script:
    - |
      DEPLOY_SERVER_IPS=$(echo $DEPLOY_WORKER_SERVER_IP | tr "," "\n")
      INDEX=0
      for WORKER_SERVER_IP in $DEPLOY_SERVER_IPS
      do
        echo "Deploying on worker server ${WORKER_SERVER_IP} | started"
        if [[ "$INDEX" -eq "0" ]]; then
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$WORKER_SERVER_IP" -p 2202 "
          cd /deploy &&
          COMPOSE_HTTP_TIMEOUT=200 docker compose -f docker-compose.yml -f docker-compose.reserve.yml up -d --remove-orphans &&
          docker system prune -a -f" &
        else
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$WORKER_SERVER_IP" -p 2202 "
          cd /deploy &&
          COMPOSE_HTTP_TIMEOUT=200 docker compose up -d --remove-orphans &&
          docker system prune -a -f" &
        fi
        INDEX=$((INDEX+1))
      done
      wait
      echo "All worker servers deployed."
      ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      cd /deploy &&
      docker compose run --rm profit-dash-php-fpm /app/yii cache/flush mutexCache --interactive=0 &&
      docker compose run --rm profit-dash-php-fpm /app/yii process-manager/unfreeze &&
      docker compose run --rm profit-dash-php-fpm /app/yii cache/flush-all --interactive=0"

translation_sync:
  tags:
    - profit
  stage: translation_sync
  only:
    - rc
    - master
  environment:
    name: $ENVIRONMENT
  except:
    variables:
      - $MERGE == "true"
  <<: *ssh_key
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii translation/sync-environment "

translation_sync_staging:
  tags:
    - profit
  stage: translation_sync
  <<: *ssh_key
  script:
    - export IMGTAG=`date +%Y-%m-%d`-$CI_PIPELINE_ID
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no admin@"$DEPLOY_SERVER_IP" -p 2202 "
      docker run --rm $CI_REGISTRY/develop/profit-dash/sellerlogic_profit-dash-php-fpm:$IMGTAG /app/yii translation/sync-external "
  only:
    - staging
  environment:
    name: $ENVIRONMENT
