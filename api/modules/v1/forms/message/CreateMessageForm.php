<?php

namespace api\modules\v1\forms\message;

use common\models\Message;
use yii\base\Model;


class CreateMessageForm extends Model
{
    public $category;
    public $message;

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['category'], 'required'],
            [['category'], 'string', 'max' => 32],

            [['message'], 'required'],
            [['message'], 'string'],
        ];
    }

    public function create(): Message
    {
        /** @var Message $message */
        if ($message = Message::find()->where(['category' => $this->category, 'message' => $this->message])->limit(1)->one()) {
            return $message;
        }
        $message = new Message();
        $message->message = $this->message;
        $message->category = $this->category;
        $message->is_processed = false;
        $message->saveOrThrowException();

        return $message;
    }

}
