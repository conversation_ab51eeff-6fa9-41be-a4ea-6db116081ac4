<?php

namespace api\modules\v1\forms\product;

use yii\base\Model;
use yii\validators\DateValidator;
use api\modules\v1\enums\RefundReasons;

class GetRefundCommentsForm extends Model
{
    public $seller_sku;
    public $marketplace_id;
    public $seller_id;
    public $refund_date;
    public $return_reason;

    public function rules(): array
    {
        return [
            [['seller_sku', 'marketplace_id', 'seller_id',], 'required'],
            ['return_reason', 'in', 'range' => RefundReasons::getAll()],
            ['refund_date', 'validateRefundDate', 'skipOnEmpty' => true],
        ];
    }

    public function formName(): string
    {
        return '';
    }

    public function getReturnReason(): string
    {
        return $this->return_reason ?? '';
    }

    public function validateRefundDate($attribute)
    {
        $value = trim((string)$this->$attribute);

        foreach (['yyyy-MM-dd', 'yyyy-MM-dd - yyyy-MM-dd'] as $format) {
            $validator = new DateValidator(['format' => $format]);
            if ($validator->validate($value)) {
                return;
            }
        }

        $this->addError($attribute, 'Use YYYY-MM-DD or YYYY-MM-DD - YYYY-MM-DD.');
    }
}
