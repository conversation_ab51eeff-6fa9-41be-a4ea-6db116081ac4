<?php

namespace api\modules\v1\forms\product;

use common\components\dataImportExport\bulkEdit\product\ProductBulkEditPartCreator;
use common\components\dataImportExport\SupportedHandlers;
use common\components\LinuxCommander;
use common\models\customer\DataImport;
use Yii;
use yii\base\Model;
use yii\helpers\Json;

/**
 * This is the API model class for "ProductBulkEditForm".
 *
 * @OA\Schema(schema="ProductBulkEditForm"),
 * @OA\Property(property="side", type="string", enum={"selected","all"}),
 * @OA\Property(property="ids", type="array", @OA\Items(type="integer")),
 * @OA\Property(property="tags_add", type="array", @OA\Items(type="integer")),
 * @OA\Property(property="tags_remove", type="array", @OA\Items(type="integer")),
 * @OA\Property(
 *      property="is_enabled_sync_with_repricer",
 *      type="boolean",
 *      description="Is enabled sync with repricer"
 *   )
 */
class ProductBulkEditForm extends Model
{
    public const EMPTY_VALUE = '_empty_';

    public $side;
    public $ids;
    public $data;
    public $query;

    public $is_enabled_sync_with_repricer;
    public $buying_price;
    public $shipping_cost;
    public $other_fees;
    public $vat;
    public $tags_add;
    public $tags_remove;

    /**
     * GroupActionForm constructor.
     * @param array $data
     * @param array $query
     * @param array $config
     */
    public function __construct(array $data, array $query = [], $config = [])
    {
        $this->data = $data;
        $this->query = $query;
        parent::__construct($config);
    }

    public function attributeLabels()
    {
        return [
            'is_enabled_sync_with_repricer' => Yii::t( 'admin', 'Sync with repricer'),
            'other_fees' => \Yii::t('admin', 'Other Fees'),
            'shipping_cost' => \Yii::t('admin', 'FBM shipping Costs'),
            'buying_price' => \Yii::t('admin', 'Cost Of Goods'),
            'vat' => \Yii::t('admin', 'VAT'),
            'tags_add' => \Yii::t('admin', 'Tags Add'),
            'tags_remove' => \Yii::t('admin', 'Tags Remove'),
        ];
    }

    /**
     * @return string[]
     */
    private function getAllowedFields(): array
    {
        return [
            'is_enabled_sync_with_repricer',
            'buying_price',
            'shipping_cost',
            'other_fees',
            'vat',
            'tags_add',
            'tags_remove'
       ];
    }

    public function beforeSave($insert)
    {
        if (\Yii::$app->dbManager->isRepricerSync() === false) {
            $this->is_enabled_sync_with_repricer = $this->getOldAttribute('is_enabled_sync_with_repricer');
        }

        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['is_enabled_sync_with_repricer'], 'boolean'],
            [['buying_price', 'other_fees', 'shipping_cost', 'vat'], 'default', 'value' => null],
            [['tags_add', 'tags_remove'], 'default', 'value' => []],
            [['tags_add', 'tags_remove'], 'each', 'rule' => ['integer']],
            [['buying_price', 'other_fees', 'shipping_cost', 'vat'], 'number'],
            [['buying_price', 'other_fees', 'shipping_cost', 'vat'],
                'compare',
                'compareValue' => 0,
                'operator' => '>=',
                'skipOnEmpty' => true
            ],

            // Service fields
            [['side'], 'required'],
            [['side'], 'in', 'range' => $this->getAllowedSides()],
            [['ids'], 'required', 'when' => function (ProductBulkEditForm $model) {
                 return $model->side === DataImport::SIDE_SELECTED;
            }],

        ];
    }

    /**
     * @return array
     */
    public function getPreparedFields(): array
    {
        $fields = [];
        foreach ($this->getAllowedFields() as $field) {
            if ($field === 'tags_add' && is_array($this->tags_add)) {
                $fields[$field] = empty($this->tags_add) ? [] : $this->tags_add;
            } elseif ($field === 'tags_remove' && is_array($this->tags_remove)) {
                $fields[$field] = empty($this->tags_remove) ? [] : $this->tags_remove;
            } elseif (!is_null($this->$field)) {
                $fields[$field] = $this->$field === self::EMPTY_VALUE ? null : $this->$field;
            }
        }

        return $fields;
    }

    /**
     * @return array
     */
    public function getAllowedSides()
    {
        return [
            DataImport::SIDE_ALL,
            DataImport::SIDE_SELECTED,
        ];
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @return array
     */
    public function getParams(): array
    {
        return $this->query;
    }
}
