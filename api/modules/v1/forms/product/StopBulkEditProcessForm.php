<?php

namespace api\modules\v1\forms\product;

use common\models\customer\DataImport;
use yii\base\Model;

class StopBulkEditProcessForm extends Model
{
    /** @var DataImport */
    private $model;
    public $id;

    public function __construct(DataImport $model, $config = [])
    {
        $this->model = $model;
        parent::__construct($config);
    }

    /**
     * @return array[]
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id'], 'integer'],
            [['id'], function () {
                if ($this->model->type !== DataImport::TYPE_BULK_EDIT) {
                    $this->addError('id', \Yii::t('admin', "Can't stop process. Incorrect type"));
                }
            }, ],
            [['id'], function () {
                if (!$this->model->canStopProcess()) {
                    $this->addError('id', \Yii::t('admin', "Can't stop process. Process is not active"));
                }
            }, ],
        ];
    }

    public function stop()
    {
        $this->model->status = DataImport::STATUS_TERMINATED;
        $this->model->update(false, ['status']);

        return $this->model;
    }
}
