<?php

namespace api\modules\v1\forms\order;

use yii\base\Model;

class OrderEditAmountForm extends Model
{

    public $amount;

    public $product_title;

    public $product_asin;

    public $marketplace_id;

    public $seller_id;

    public $seller_sku;

    public function rules(): array
    {
        return [
            [['amount'], 'required'],
            ['amount', 'number', 'min' => 0, 'max' => 100000000],
            [['product_title', 'product_asin', 'marketplace_id', 'seller_id', 'seller_sku'], 'safe'],
        ];
    }
}
