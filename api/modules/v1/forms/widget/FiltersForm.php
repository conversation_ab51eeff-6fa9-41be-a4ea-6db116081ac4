<?php

namespace api\modules\v1\forms\widget;

use common\components\core\db\dbManager\DbManager;
use common\components\currencyRate\CurrencyRateManager;
use common\components\CustomerComponent;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\models\customer\Product;
use common\models\order\AmazonOrder;
use yii\base\Model;
use yii\db\Query;
use yii\helpers\Inflector;

class FiltersForm extends Model
{
    public ?string $dateStart = '';
    public ?string $currencyId = '';
    public ?string $dateEnd = '';
    public string $marketplaceSellerIds = '';
    public string $sellerSku = '';
    public string $amazonOrderId = '';
    public string $marketplaceId = '';
    public string $sellerId = '';
    public string $stockType = '';
    public string $sku = '';
    public string $title = '';
    public string $ean = '';
    public string $upc = '';
    public string $isbn = '';
    public string $brand = '';
    public string $productType = '';
    public string $parentAsin = '';
    public string $ageRange = '';
    public string $manufacturer = '';
    public ?bool $adultProduct = null;
    public string $asin = '';
    public string $offerType = '';
    public string $tagId = '';
    public bool $isTransactionDateMode = false;
    public ?string $salesCategoryStrategy = SalesCategoryStrategyFactory::DEFAULT_STRATEGY;

    public function load($data, $formName = null): bool
    {
        // Support for snake_case keys
        foreach ($data as $k => $value) {
            $data[lcfirst(Inflector::camelize($k))] = $value;
        }

        if (isset($data['adultProduct'])) {
            $data['adultProduct'] = filter_var($data['adultProduct'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;
        }

        if (isset($data['isTransactionDateMode'])) {
            $data['isTransactionDateMode'] = filter_var($data['isTransactionDateMode'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;
        }

        $res = parent::load($data, $formName);
        $this->fillSellerSkuIfNeed();

        return $res;
    }

    private function fillSellerSkuIfNeed(): void
    {
        if (empty($this->asin)) {
            return;
        }
        $sellerSku = (new Query())->select('sku')->from(Product::tableName())->where([
            'asin' => $this->asin
        ])->createCommand(Product::getDb())->queryColumn();

        $this->sellerSku = implode(',', $sellerSku);
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['currencyId', 'salesCategoryStrategy', 'marketplaceSellerIds', 'sellerId', 'marketplaceId', 'stockType', 'sku', 'asin', 'title', 'ean', 'upc', 'isbn', 'brand', 'productType', 'manufacturer', 'parentAsin', 'ageRange', 'tagId'], 'string'],
            [['adultProduct', 'isTransactionDateMode'], 'boolean'],
            [['salesCategoryStrategy'], function ($field) {
                $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
                $supportedStrategies = $salesCategoryStrategyFactory->getSupportedStrategyTypes();

                if (empty($this->{$field}) || !in_array($this->{$field}, $supportedStrategies)) {
                    $this->{$field} = SalesCategoryStrategyFactory::DEFAULT_STRATEGY;
                }
            }, 'skipOnEmpty' => false],
            [['dateStart', 'dateEnd'], function($field) {
                try {
                    $isTimeSpecified = false !== strpos($this->{$field}, ':');
                    $isInUTCFormat = $isTimeSpecified;

                    // If time specified, we assume that it's already in UTC format and do not convert it
                    // from user timezone to UTC
                    if ($isInUTCFormat) {
                        $timezone = new \DateTimeZone(CustomerComponent::UTC_TIMEZONE);
                        $dateTime = (new \DateTime($this->{$field}, $timezone));
                    } else {
                        $customerComponent = \Yii::$app->customerComponent;
                        $timezone = new \DateTimeZone($customerComponent->getUserTimezoneName());
                        $dateTime = (new \DateTime($this->{$field}, $timezone));

                        if ($field === 'dateEnd') {
                            $dateTime->setTime(23,59,59);
                        }
                        $dateTime->setTimezone(new \DateTimeZone(CustomerComponent::UTC_TIMEZONE));
                    }

                    if ($field === 'dateStart') {
                        /** @var DbManager $dbManager */
                        $dbManager = \Yii::$app->dbManager;
                        $minTransactionDate = $dbManager->getMinimumStatisticDate();

                        if ($dateTime < $minTransactionDate) {
                            $dateTime = $minTransactionDate;
                        }
                    }
                    $this->{$field} = $dateTime->format('Y-m-d H:i:s');
                } catch (\Throwable $e) {
                    // Nothing to do here - just mute, next validator will catch wrong format
                }
            }],
            [['offerType'], function($field) {
                $this->{$field} = strtoupper($this->{$field});
            }, 'skipOnEmpty' => true],
            [['tagId'], 'filter', 'filter' => function ($value) {
                $tags = array_filter(array_map(function ($item) {
                    return preg_replace('/\D/', '', $item);
                }, explode(',', $value)), function ($item) {
                    return $item !== '';
                });

                return implode(',', $tags);
            }],
            [['offerType'], 'validateOfferType', 'skipOnEmpty' => true],
            [['dateStart', 'dateEnd'], 'datetime', 'format' => 'php:Y-m-d H:i:s'],
            [['dateStart', 'dateEnd', 'asin', 'sellerSku', 'sellerId', 'stockType', 'sku', 'title', 'ean', 'upc', 'isbn', 'brand', 'productType', 'manufacturer', 'marketplaceId', 'isTransactionDateMode', 'adultProduct', 'parentAsin', 'offerType', 'salesCategoryStrategy', 'tagId'], 'safe'],
            [['currencyId'], 'default', 'value' => CurrencyRateManager::BASE_CURRENCY]
        ];
    }

    public function validateOfferType($attribute)
    {
        $validOfferTypes = [AmazonOrder::OFFER_TYPE_B2B, AmazonOrder::OFFER_TYPE_B2C];
        $offerTypes = explode(',', $this->$attribute);

        foreach ($offerTypes as $offerType) {
            $offerType = trim($offerType);
            if (!in_array($offerType, $validOfferTypes)) {
                $this->addError($attribute, 'Invalid offer type: ' . $offerType);
                return;
            }
        }
    }
}
