<?php

namespace api\modules\v1\forms\export;

use common\components\dataImportExport\SupportedHandlers;
use yii\base\Model;


class ExportTemplateForm extends Model
{
    public $handler_name;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['handler_name'], 'required'],
            [['handler_name'], 'in', 'range' => SupportedHandlers::EXPORT_SUPPORTED_HANDLERS]
        ];
    }
}
