<?php

namespace api\modules\v1\forms\seller;

use common\components\core\db\dbManager\DbManager;
use common\components\customerConfig\CustomerConfig;
use common\models\AmazonMarketplace;
use common\models\Command;
use common\models\Seller;
use yii\base\Model;

class GlobalMarketplaceSettingsForm extends Model
{
    protected CustomerConfig $customerConfig;
    protected DbManager $dbManager;

    public ?string $seller_id = null;

    public ?string $global_marketplace_id = null;

    public bool $is_enabled_cost_of_goods_sync = false;

    public bool $is_enabled_other_fees_sync = false;

    public bool $is_enabled_fbm_shipping_cost_sync = false;

    public bool $should_relink_products = false;

    public function __construct($config = [])
    {
        $this->customerConfig = \Yii::$container->get('customerConfig');
        $this->dbManager = \Yii::$app->dbManager;

        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['seller_id'], 'required'],
            [['global_marketplace_id', 'seller_id'], 'string', 'max' => 20],
            [['global_marketplace_id'], 'exist', 'targetClass' => AmazonMarketplace::class, 'targetAttribute' => 'id'],
            [['seller_id'], 'exist', 'targetClass' => Seller::class, 'targetAttribute' => 'id'],
            [
                ['is_enabled_cost_of_goods_sync', 'is_enabled_other_fees_sync', 'is_enabled_fbm_shipping_cost_sync', 'should_relink_products'],
                'boolean'
            ],
            [
                ['is_enabled_cost_of_goods_sync', 'is_enabled_other_fees_sync', 'is_enabled_fbm_shipping_cost_sync', 'should_relink_products'],
                'default',
                'value' => false
            ],
        ];
    }

    /**
     * Saves global marketplace settings and creates command to check for products to be synchronized.
     *
     * @return void
     * @throws \Exception
     */
    public function save(): void
    {
        $formBeforeSave = $this->getFormBeforeSave();

        $this->customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            $this->global_marketplace_id,
            $this->seller_id
        );

        $this->customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            $this->is_enabled_cost_of_goods_sync,
            $this->seller_id
        );

        $this->customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            $this->is_enabled_other_fees_sync,
            $this->seller_id
        );

        $this->customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            $this->is_enabled_fbm_shipping_cost_sync,
            $this->seller_id
        );

        $isSomethingChanged = $formBeforeSave->global_marketplace_id != $this->global_marketplace_id
            || $formBeforeSave->is_enabled_cost_of_goods_sync != $this->is_enabled_cost_of_goods_sync
            || $formBeforeSave->is_enabled_other_fees_sync != $this->is_enabled_other_fees_sync
            || $formBeforeSave->is_enabled_fbm_shipping_cost_sync != $this->is_enabled_fbm_shipping_cost_sync;

        // When something changed, we need to check for products to be synchronized
        // and increase sync version to start sync process
        if ($isSomethingChanged) {
            $currentSyncVersion = (int)$this->customerConfig->get(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION,
                0,
                $this->seller_id
            );

            $this->customerConfig->set(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_SYNC_VERSION,
                $currentSyncVersion + 1,
                $this->seller_id
            );
            if ($this->should_relink_products) {
                Command::create(
                    sprintf(
                        "product/force-enable-sync-with-global-marketplace %d %d",
                        $this->dbManager->getCustomerId(),
                        $this->dbManager->getCustomerId() + 1
                    ),
                    1,
                    true,
                    true
                );
                return;
            }

            Command::create(
                sprintf(
                    "product/check-sync-with-global-marketplace %d %d",
                    $this->dbManager->getCustomerId(),
                    $this->dbManager->getCustomerId() + 1
                ),
                1,
                true,
                true
            );
        }
    }

    /**
     * Returns settings state before state to be able to find changes
     *
     * @return GlobalMarketplaceSettingsForm
     */
    protected function getFormBeforeSave(): GlobalMarketplaceSettingsForm
    {
        $form = new GlobalMarketplaceSettingsForm();
        $form->seller_id = $this->seller_id;

        $form->global_marketplace_id = $this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            null,
            $this->seller_id
        );
        $form->is_enabled_cost_of_goods_sync = $this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            false,
            $this->seller_id
        );
        $form->is_enabled_other_fees_sync = $this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            false,
            $this->seller_id
        );
        $form->is_enabled_fbm_shipping_cost_sync = $this->customerConfig->get(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            false,
            $this->seller_id
        );

        $form->should_relink_products = $this->should_relink_products;

        return $form;
    }
}
