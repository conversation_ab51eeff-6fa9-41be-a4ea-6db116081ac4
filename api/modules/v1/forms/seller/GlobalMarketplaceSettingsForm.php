<?php

namespace api\modules\v1\forms\seller;

use common\models\AmazonMarketplace;
use common\models\Seller;
use yii\base\Model;

class GlobalMarketplaceSettingsForm extends Model
{

    public ?string $global_marketplace_id;

    public bool $is_enabled_cost_of_goods_sync = false;

    public bool $is_enabled_other_fees_sync = false;

    public bool $is_enabled_fbm_shipping_cost_sync = false;

    public function __construct($config = [])
    {
        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['global_marketplace_id'], 'string', 'max' => 20],
            [['global_marketplace_id'], 'validateMarketplaceId'],
            [
                ['is_enabled_cost_of_goods_sync', 'is_enabled_other_fees_sync', 'is_enabled_fbm_shipping_cost_sync'],
                'boolean'
            ],
            [
                ['is_enabled_cost_of_goods_sync', 'is_enabled_other_fees_sync', 'is_enabled_fbm_shipping_cost_sync'],
                'default',
                'value' => false
            ],
        ];
    }

    public function validateMarketplaceId($attribute, $params)
    {
        if (empty($this->$attribute)) {
            return;
        }

        $exists = AmazonMarketplace::find()->where(['id' => $this->$attribute])->exists();
        if (!$exists) {
            $this->addError($attribute, \Yii::t('admin', 'Marketplace does not exist'));
        }
    }
}
