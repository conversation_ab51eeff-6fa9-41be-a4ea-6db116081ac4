<?php

namespace api\modules\v1\forms\seller;

use SellerLogic\SellerApi\Configurator;
use yii\base\Model;


class SyncSeller extends Model
{
    private $index;

    public $sellerId;
    public $active;
    public $isAnalyticActive;
    public $productCostsGlobalMarketplaceId;
    public $lastActivePlanDateFinish;
    public $euAmazonFeesVat;
    public $region;
    public $isDeleted;
    public $isDemo;

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['active'], 'required'],
            [['euAmazonFeesVat'], 'number', 'min' => 0, 'max' => 100],
            [['active', 'isAnalyticActive'], 'boolean'],
            [['lastActivePlanDateFinish'], 'string'],
            [['isDeleted'], 'boolean'],
            [['isDemo'], 'boolean'],
            [['sellerId'], 'required'],
            [['sellerId'], 'string', 'max' => 50],
            [['sellerId'], 'match', 'not' => true, 'pattern' => '/[^A-Za-z0-9]/', 'message' => 'SellerId should contain letters or numbers.'],
            ['sellerId', 'filter', 'filter' => 'strtoupper'],
            [['region'], 'required'],
            [['region'], 'string', 'max' => 32],
            [['region'], 'in', 'range' => [Configurator::REGION_EU, Configurator::REGION_FE, Configurator::REGION_US]],
        ];
    }

    /**
     * @return mixed
     */
    public function getIndex()
    {
        return $this->index;
    }

    /**
     * @param mixed $index
     */
    public function setIndex($index): void
    {
        $this->index = $index;
    }
}
