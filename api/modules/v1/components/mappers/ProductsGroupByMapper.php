<?php

namespace api\modules\v1\components\mappers;

class ProductsGroupByMapper implements DataMapperInterface
{
    public function getAttributeByInput(string $input): ?string
    {
        return $this->getMapping()[$input] ?? null;
    }

    public function getMapping(): array
    {
        return [
            'asin' => 'product_asin',
            'marketplace' => 'marketplace_id',
            'brand' => 'product_brand',
            'manufacturer' => 'product_manufacturer',
            'product_type' => 'product_type',
            'fulfillment_method' => 'product_stock_type',
            'product_parent_asin' => 'product_parent_asin',
        ];
    }

    public function availableKeys(): array
    {
        return array_keys($this->getMapping());
    }
}
