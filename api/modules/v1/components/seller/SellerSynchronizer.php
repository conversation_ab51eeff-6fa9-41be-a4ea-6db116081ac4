<?php

namespace api\modules\v1\components\seller;

use api\modules\v1\components\seller\dto\SyncResult;
use api\modules\v1\forms\seller\SyncSeller;
use common\components\core\db\dbManager\DbManager;
use common\components\customerProcess\process\FromDbToClickhouseProcess;
use common\components\customerProcess\ProcessManager;
use common\models\Command;
use common\models\Customer;
use common\models\Seller;
use yii\base\Model;
use yii\mutex\Mutex;

class SellerSynchronizer
{
    private $customerId;

    /** @var SyncSeller[]  */
    private array $models = [];
    protected Mutex $mutex;
    protected ProcessManager $processManager;
    protected DbManager $dbManager;

    /**
     * @param int $customerId
     */
    public function __construct(int $customerId)
    {
        $this->customerId = $customerId;
        $this->mutex = \Yii::$app->mutex;
        $this->processManager = new ProcessManager();
        $this->dbManager = \Yii::$app->dbManager;
    }

    /**
     * @param array $data
     */
    public function loadModels(array $data)
    {
        $this->models = $this->loadMultipleModel($data);
    }

    /**
     * @param array $data
     * @return array
     */
    protected function loadMultipleModel(array $data)
    {
        $models = [];

        if (!empty($data)) {
            foreach ($data as $index => $item) {
                $model = new SyncSeller();
                $model->load($item, '');
                $model->setIndex($index);
                $models[] = $model;
            }
        }

        return $models;
    }

    /**
     * @return bool
     */
    public function validate()
    {
        return Model::validateMultiple($this->models);
    }

    /**
     * @return array
     */
    public function getValidationErrors()
    {
        $result = [];
        foreach ($this->models as $model) {
            if ($model->hasErrors()) {
                $item['index'] = $model->getIndex();
                $item['errors'] = $model->getErrors();
                $result[] = $item;
            }
        }

        return $result;
    }

    public function syncAll()
    {
        $this->dbManager->setCustomerId($this->customerId);
        $countActiveBefore = Seller::find()
            ->where([
                'customer_id' => $this->customerId,
                'is_active' => true
            ])
            ->noCache()
            ->count();
        $wasActiveUntilDate = \Yii::$app->customerComponent->getWasActiveUntilDate(true);
        $countReactivated = 0;
        $countCreated = 0;
        $isEuAmazonFeesVatChanged = false;

        foreach ($this->models as $model) {
            try {
                $syncResult = $this->syncSeller($model);

                if ($syncResult->isReActivated) {
                    $countReactivated++;
                }

                if ($syncResult->isCreated) {
                    $countCreated++;
                }

                if ($syncResult->isEuAmazonFeesVatChanged) {
                    $isEuAmazonFeesVatChanged = true;
                }
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }

        Seller::updateAll([
            'is_active' => false
        ], [
            'AND',
            ['=', 'customer_id', $this->customerId],
            ['NOT IN', 'id', array_column($this->models, 'sellerId')]
        ]);
        $isSubscriptionReActivated = $countActiveBefore === 0
            && $countReactivated > 0
            && time() - strtotime($wasActiveUntilDate) > 60 * 30
        ;

        if ($countCreated > 0) {
            Command::create('seller/create-db', 10);
        }

        // Subscription prolonged (re-activated)
        if ($isSubscriptionReActivated) {
            Command::create("seller/re-activate-subscription $this->customerId", 1, true);
        }

        if ($isEuAmazonFeesVatChanged) {
            $this->processManager->schedule((new FromDbToClickhouseProcess())->getName(), false, true);
        }
    }

    /**
     * @param SyncSeller $model
     * @throws \Exception
     */
    private function syncSeller(SyncSeller $model): SyncResult
    {
        $lockKey = 'sync_seller_' . implode('_', [$model->sellerId, $this->customerId]);

        $syncResult = new SyncResult();

        if (!$this->mutex->acquire($lockKey)) {
            return $syncResult;
        }

        try {
            Seller::$ignoreFilterByCustomer = true;
            /** @var Seller|null $seller */
            $seller = Seller::find()
                ->where([
                    'id' => $model->sellerId,
                    'customer_id' => $this->customerId
                ])
                ->limit(1)
                ->one(\Yii::$app->db);

            if (is_null($seller)) {
                if ($model->isDeleted) {
                    $this->mutex->release($lockKey);
                    return $syncResult;
                }
                if ($model->active || $model->isAnalyticActive) {
//                    $this->deleteSameSellers($model->sellerId, $this->customerId);
                    $this->createSeller($model);
                    $this->mutex->release($lockKey);
                    $syncResult->isCreated = true;
                    return $syncResult;
                }
            }

            if (!is_null($seller)) {
                if ($model->isDeleted) {
                    // Temporary restrict data cleanup
                    // Wee need to preserve some data for infinity tine (repricer events data)
//                    Command::create("seller/remove-db {$seller->id} {$seller->customer_id}");
//                    $this->mutex->release($lockKey);
//                    $syncResult->isDeleted = true;
//                    return $syncResult;
                    $model->active = false;
                    $model->isAnalyticActive = false;
                }

                $isActive = (bool)$model->active;
                $euAmazonFeesVat = $model->euAmazonFeesVat;

                if (!$seller->is_active && $isActive) {
                    $syncResult->isReActivated = true;
                }

                if ((float)$seller->eu_amazon_fees_vat != (float)$euAmazonFeesVat) {
                    $syncResult->isEuAmazonFeesVatChanged = true;
                }

                $seller->is_active = $isActive;
                $seller->eu_amazon_fees_vat = $euAmazonFeesVat;
                $seller->is_analytic_active = $seller->is_active || $model->isAnalyticActive;
                $seller->saveOrThrowException();
            }

        } catch (\Throwable $e) {
            /*if (false === strpos($e->getMessage(), 'Unique violation')) {
                $this->mutex->release($lockKey);
                throw $e;
            }*/
            $this->mutex->release($lockKey);
            throw $e;
        }
        $this->mutex->release($lockKey);

        return $syncResult;
    }

    protected function createSeller(SyncSeller $model): Seller
    {
        $customer = Customer::find()
            ->where([
                'id' => $this->customerId
            ])
            ->noCache()
            ->one();

        if (empty($customer)) {
            try {
                $customer = new Customer();
                $customer->id = $this->customerId;
                $customer->is_sync_default = false; // For all new customers, we decided to not activate sync by default
                $customer->is_demo = $model->isDemo;
                $customer->saveOrThrowException();
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }

        $postgresDbIndex = $this->getPosgresDbIndexByCustomerId($this->customerId);
        //new customer to new server shard
        $postgresDbIndex = $postgresDbIndex ?? 1;

        $seller = new Seller();
        $seller->id = $model->sellerId;
        $seller->customer_id = $this->customerId;
        $seller->is_demo = $model->isDemo;
        $seller->region = $model->region;
        $seller->created_at = date('Y-m-d H:i:s');
        $seller->last_attempt_to_get_token = null;
        $seller->is_token_received = true;
        $seller->is_active = $model->active;
        $seller->is_analytic_active = $model->active || $model->isAnalyticActive;
        $seller->is_init_periods_created = false;
        $seller->is_init_periods_loaded = false;
        $seller->is_order_init_periods_created = false;
        $seller->is_order_init_periods_loaded = false;
        $seller->eu_amazon_fees_vat = $model->euAmazonFeesVat;
        $seller->is_db_created = false;
        $seller->postgres_db_index = $postgresDbIndex;
        $seller->saveOrThrowException();

        return $seller;
    }

    protected function deleteSameSellers($sellerId, $customerId)
    {
        $sameSellers = Seller::find()->where(['seller.id' => $sellerId, 'seller.is_active' => false])->andWhere(['!=', 'seller.customer_id', $customerId])->all(\Yii::$app->db);

        $idsForeDeleting = [];
        /** @var Seller $sameSeller */
        foreach ($sameSellers as $sameSeller) {
            Command::create("seller/remove-db {$sameSeller->id} {$sameSeller->customer_id}");
            $idsForeDeleting[] = $sameSeller->id;
        }

        Seller::deleteAll(['id' => $idsForeDeleting]);
    }

    protected function getPosgresDbIndexByCustomerId($customerId): ?int
    {
        return Seller::find()->select('max(postgres_db_index)')->andWhere(['=', 'seller.customer_id', $customerId])->scalar(\Yii::$app->db);
    }

    /**
     * @return SyncSeller[]
     */
    public function getModels(): array
    {
        return $this->models;
    }
}
