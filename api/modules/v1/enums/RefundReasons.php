<?php

namespace api\modules\v1\enums;

use Exception;
use yii\db\Expression;

/**
 * Class describes fba/fbm reasons types
 */
class RefundReasons
{
    public const UNWANTED_ITEM = 'UNWANTED_ITEM';
    public const APPAREL_TOO_LARGE = 'APPAREL_TOO_LARGE';
    public const EXTRA_ITEM = 'EXTRA_ITEM';
    public const SWITCHEROO = 'SWITCHEROO';
    public const NO_REASON_GIVEN = 'NO_REASON_GIVEN';
    public const MISSED_ESTIMATED_DELIVERY = 'MISSED_ESTIMATED_DELIVERY';
    public const DAMAGED_BY_FC = 'DAMAGED_BY_FC';
    public const NOT_AS_DESCRIBED = 'NOT_AS_DESCRIBED';
    public const MISORDERED = 'MISORDERED';
    public const MISSING_PARTS = 'MISSING_PARTS';
    public const DAMAGED_BY_CARRIER = 'DAMAGED_BY_CARRIER';
    public const QUALITY_UNACCEPTABLE = 'QUALITY_UNACCEPTABLE';
    public const APPAREL_TOO_SMALL = 'APPAREL_TOO_SMALL';
    public const ORDERED_WRONG_ITEM = 'ORDERED_WRONG_ITEM';
    public const DEFECTIVE = 'DEFECTIVE';
    public const FOUND_BETTER_PRICE = 'FOUND_BETTER_PRICE';
    public const UNAUTHORIZED_PURCHASE = 'UNAUTHORIZED_PURCHASE';
    public const NOT_COMPATIBLE = 'NOT_COMPATIBLE';
    public const APPAREL_STYLE = 'APPAREL_STYLE';

    private static array $aliases = [
        self::UNWANTED_ITEM => ['CR-UNWANTED_ITEM'],
        self::APPAREL_TOO_LARGE => ['AMZ-PG-APP-TOO-LARGE'],
        self::EXTRA_ITEM => ['CR-EXTRA_ITEM'],
        self::SWITCHEROO => ['SWITCHEROO'],
        self::NO_REASON_GIVEN => ['CR-NO_REASON_GIVEN'],
        self::MISSED_ESTIMATED_DELIVERY => ['CR-MISSED_ESTIMATED_DELIVERY'],
        self::DAMAGED_BY_FC => ['CR-DAMAGED_BY_FC'],
        self::NOT_AS_DESCRIBED => ['AMZ-PG-BAD-DESC'],
        self::MISORDERED => ['AMZ-PG-MISORDERED'],
        self::MISSING_PARTS => ['CR-MISSING_PARTS'],
        self::DAMAGED_BY_CARRIER => ['CR-DAMAGED_BY_CARRIER'],
        self::QUALITY_UNACCEPTABLE => ['CR-QUALITY_UNACCEPTABLE'],
        self::APPAREL_TOO_SMALL => ['AMZ-PG-APP-TOO-SMALL'],
        self::ORDERED_WRONG_ITEM => ['CR-ORDERED_WRONG_ITEM'],
        self::DEFECTIVE => ['CR-DEFECTIVE'],
        self::FOUND_BETTER_PRICE => ['CR-FOUND_BETTER_PRICE'],
        self::UNAUTHORIZED_PURCHASE => ['CR-UNAUTHORIZED_PURCHASE'],
        self::NOT_COMPATIBLE => ['CR-NOT_COMPATIBLE'],
        self::APPAREL_STYLE => ['AMZ-PG-APP-STYLE'],
    ];

    /**
     * @throws Exception
     */
    public static function withAliases(...$reasons): array
    {
        $aliases = array_reduce(
            $reasons,
            fn($aliases, $item) => array_merge($aliases ?? [], self::getAliases($item))
        );

        return array_merge($reasons, $aliases);
    }

    /**
     * @throws Exception
     */
    public static function getAliases(string $reason): array
    {
        if (!isset(self::$aliases[$reason])) {
            throw new Exception(
                sprintf('Unknown reason %s', $reason)
            );
        }

        return self::$aliases[$reason];
    }

    public static function getAll(): array
    {
        return array_keys(self::$aliases);
    }

    public static function clickhouseNormalizeExpr(string $column = 'reason'): Expression
    {
        $from = $to = [];
        foreach (self::$aliases as $canon => $raws) {
            foreach (array_unique(array_merge([$canon], $raws)) as $raw) {
                $from[] = $raw;
                $to[]   = $canon;
            }
        }

        $sql = sprintf(
            'transform(%s, %s, %s, %s)',
            $column,
            self::arrayLiteral($from),
            self::arrayLiteral($to),
            $column
        );

        return new Expression($sql);
    }

    private static function arrayLiteral(array $vals): string
    {
        $q = static fn(string $s) => "'" . str_replace(["\\", "'"], ["\\\\", "\\'"], $s) . "'";

        return '[' . implode(',', array_map($q, $vals)) . ']';
    }
}
