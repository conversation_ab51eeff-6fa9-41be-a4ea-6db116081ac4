<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\amazonOrder\GetAmazonFeesBreakdownV1;
use api\modules\v1\controllers\actions\amazonOrder\GetDetailsV1;
use api\modules\v1\controllers\actions\amazonOrder\GetExpensesBreakdownV1;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use api\modules\v1\controllers\filters\ValidPlanGuard;
use common\components\Permissions;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use OpenApi\Annotations as OA;
use yii\filters\AccessControl;

/**
 * @OA\Get(
 *     path="/v1/amazon-order-v1/amazon-fees-breakdown",
 *     summary="Returns data needed for amazon fees modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderItemId",
 *         in="query",
 *         description="Amazon order item id",
 *         required=true,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order-v1/expenses-breakdown",
 *     summary="Returns data needed for expenses modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderItemId",
 *         in="query",
 *         description="Amazon order item id",
 *         required=true,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order-v1/details",
 *     summary="Data needed for order details modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderId",
 *         in="query",
 *         description="Amazon order id",
 *         required=true,
 *         example="303-6829218-2536344",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the details of the order",
 *         @OA\JsonContent(ref="#/components/schemas/OrderDetailsResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 *
/**
 * @OA\Get(
 *     path="/v1/amazon-order-v1",
 *     summary="Retrieves the collection of AmazonOrder resources.",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(
 *         name="order_item_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Order item identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Order identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_status",
 *         in="query",
 *         description="Order status (multisearch)",
 *         required=false,
 *         @OA\Schema(
 *            type="string",
 *            enum={"Shipped", "Canceled", "Pending", "Unshipped", "PartiallyShipped", "Unfulfillable", "InvoiceUnconfirmed", "PendingAvailability"}
 *         )
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Seller identifier"
 *     ),
 *     @OA\Parameter(
 *         name="seller_sku",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Seller SKU"
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Marketplace identifier"
 *     ),
 *     @OA\Parameter(
 *         name="item_price",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Item price"
 *     ),
 *     @OA\Parameter(
 *         name="quantity",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Quantity"
 *     ),
 *     @OA\Parameter(
 *         name="quantity_refunded",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Quantity refunded"
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *         description="Offer type"
 *     ),
 *     @OA\Parameter(
 *         name="amazon_fees_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Amazon fees amount"
 *     ),
 *     @OA\Parameter(
 *         name="expenses_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Expenses amount"
 *     ),
 *     @OA\Parameter(
 *         name="revenue_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Revenue amount"
 *     ),
 *     @OA\Parameter(
 *         name="estimated_profit_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Estimated profit amount"
 *     ),
 *     @OA\Parameter(
 *         name="promotion_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Promotion amount"
 *     ),
 *     @OA\Parameter(
 *         name="product_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Product identifier"
 *     ),
 *     @OA\Parameter(
 *         name="product_asin",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product ASIN"
 *     ),
 *     @OA\Parameter(
 *         name="product_title",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product title"
 *     ),
 *      @OA\Parameter(
 *          name="product_ean",
 *          in="query",
 *          description="Product EAN",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_upc",
 *          in="query",
 *          description="Product UPC",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_isbn",
 *          in="query",
 *          description="Product ISBN",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_brand",
 *          in="query",
 *          description="Product Brand",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_type",
 *          in="query",
 *          description="Product Type",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_manufacturer",
 *          in="query",
 *          description="Product Manufacturer",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_parent_asin",
 *          in="query",
 *          description="Parent Asin",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="adult_product",
 *          in="query",
 *          description="Product Adult",
 *          required=false,
 *          @OA\Schema(type="integer", enum={1,0})
 *      ),
 *     @OA\Parameter(
 *         name="product_stock_type",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product stock type"
 *     ),
 *     @OA\Parameter(
 *         name="product_condition",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product condition"
 *     ),
 *     @OA\Parameter(
 *         name="currency_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Currency identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_purchase_date",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Order purchase date"
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of resource.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                     @OA\Schema(
 *                         type="object",
 *                         @OA\Property(
 *                             property="data",
 *                             type="array",
 *                             @OA\Items(ref="#/components/schemas/AmazonOrderExtendedViewItem")
 *                         )
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */
class AmazonOrderV1Controller extends Controller
{
    public $modelClass = AmazonOrderExtendedViewV1::class;
    protected bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => [
                        'index',
                        'details',
                        'amazon-fees-breakdown',
                        'expenses-breakdown'
                    ],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::ORDER_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        unset($actions['create']);
        unset($actions['update']);
        unset($actions['delete']);

        $actions['details'] = [
            'class' => GetDetailsV1::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['amazon-fees-breakdown'] = [
            'class' => GetAmazonFeesBreakdownV1::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['expenses-breakdown'] = [
            'class' => GetExpensesBreakdownV1::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
