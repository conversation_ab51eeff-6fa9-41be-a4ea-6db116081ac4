<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\healthCheck\CheckAction;
use yii\base\Model;
use yii\filters\AccessControl;
use yii\rest\ActiveController;

/**
 * @OA\Get(path="/v1/health",
 *   summary="Retrieves the collection of HealthCheck resources.",
 *   tags={"HealthCheck"},
 *   @OA\Response(
 *     response=200,
 *     description="Health check",
 *          @OA\JsonContent(
 *             type="object",
 *              @OA\Property(
 *                property="checks",
 *                type="object",
 *                description="Statuses of various services",
 *                  @OA\Property(
 *                     property="postgres0Master",
 *                     type="string",
 *                     description="postgres0Master"
 *                  ),
 *                  @OA\Property(
 *                     property="postgres1Master",
 *                     type="string",
 *                     description="postgres1Master"
 *                  ),
 *                  @OA\Property(
 *                     property="postgres0Slave",
 *                     type="string",
 *                     description="postgres0Slave"
 *                  ),
 *                  @OA\Property(
 *                     property="postgres1Slave",
 *                     type="string",
 *                     description="postgres1Slave"
 *                  ),
 *                  @OA\Property(
 *                     property="clickhouse1",
 *                     type="string",
 *                     description="clickhouse1"
 *                  ),
 *                  @OA\Property(
 *                     property="clickhouse2",
 *                     type="string",
 *                     description="clickhouse2"
 *                  ),
 *                  @OA\Property(
 *                     property="clickhouse3",
 *                     type="string",
 *                     description="clickhouse3"
 *                  ),
 *                  @OA\Property(
 *                     property="clickhouse4",
 *                     type="string",
 *                     description="clickhouse4"
 *                  ),
 *                  @OA\Property(
 *                     property="redis",
 *                     type="string",
 *                     description="redis"
 *                  ),
 *                  @OA\Property(
 *                     property="rabbitMQ",
 *                     type="string",
 *                     description="rabbitMQ"
 *                  ),
 *              ),
 *              @OA\Property(
 *                  property="date",
 *                  type="string",
 *                  description="date"
 *              )
 *         ),
 *   ),
 *   @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *   ),
 *   @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *   )
 * ),
 *
 */
class HealthController extends Controller
{
    public $modelClass = Model::class;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index'],
                    'roles' => ['@'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

        /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        unset($actions['update']);
        unset($actions['delete']);
        unset($actions['create']);
        unset($actions['view']);
        unset($actions['options']);

        $actions['index'] = [
            'class' => CheckAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
