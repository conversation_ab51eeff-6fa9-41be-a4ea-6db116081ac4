<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use common\models\MessageTranslation;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use yii\rest\Action;

/**
 * MessageTranslationController implements the REST actions for MessageTranslation model.
 * @OA\Get(path="/v1/message-translation",
 *   summary="Retrieves the collection of MessageTranslation resources. This is localization service. Providing language as a search parameter all translations will be fetched",
 *   tags={"MessageTranslation"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="pageSize",
 *         in="query",
 *         description="Page size [1,100]",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="all",
 *         in="query",
 *         description="Show all records with pager [1,0]",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="id",
 *         in="query",
 *         description="Id",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="language",
 *         in="query",
 *         description="Language",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="translation",
 *         in="query",
 *         description="Translation",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="translated",
 *         in="query",
 *         description="Translated",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="status",
 *         in="query",
 *         description="Status ['APPROVED','PENDING','TRANSLATED']",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           enum={
 *             "APPROVED",
 *             "PENDING",
 *             "TRANSLATED",
 *           },
 *         ),
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Retrieves the collection of MessageTranslation resources.",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/MessageTranslation"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * ),
 */
class MessageTranslationController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = MessageTranslation::class;

    protected bool $isCustomerRelated = false;


    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index'],
                    'matchCallback' => [$this, 'isAllowed'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        unset($actions['update']);
        unset($actions['delete']);
        unset($actions['create']);
        unset($actions['view']);

        return $actions;
    }

    /**
     * @param AccessRule $rule
     * @param Action $action
     * @return bool
     */
    public function isAllowed(AccessRule $rule, Action $action): bool
    {
        return \Yii::$app->user->identity->isInternalClient();
    }
}
