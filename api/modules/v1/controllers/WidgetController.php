<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\widget\GetKeyPerformance;
use api\modules\v1\controllers\actions\widget\GetOverallStatistics;
use api\modules\v1\controllers\actions\widget\GetRefunds;
use api\modules\v1\controllers\actions\widget\GetProfitAndLost;
use api\modules\v1\controllers\actions\widget\GetSalesHistory;
use api\modules\v1\controllers\actions\widget\GetSalesHistoryV1;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use yii\base\Model;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * @OA\Get(path="/v1/widget/sales-history",
 *   summary="Sales history chart",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="Title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="maxDepth",
 *       in="query",
 *       description="Maximum depth of sales categories tree structure and data series",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Root sales category id, only data related to this category will be returned",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="periodType",
 *       in="query",
 *       description="day|week|month|year",
 *       required=false,
 *       @OA\Schema(type = "string", example="day")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/widget/sales-history-v-one",
 *   summary="Sales history chart",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="Title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="maxDepth",
 *       in="query",
 *       description="Maximum depth of sales categories tree structure and data series",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Root sales category id, only data related to this category will be returned",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="periodType",
 *       in="query",
 *       description="day|week|month|year",
 *       required=false,
 *       @OA\Schema(type = "string", example="day")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *    @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *    @OA\Response(response=405, description = "Method Not Allowed"),
 *    @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse"),
 *   @OA\Response(
 *      response=200,
 *      description="Success response",
 *      @OA\JsonContent(
 *        type="object",
 *        @OA\Property(property="updatedAt", type="string", example="2025-06-22 21:51:11"),
 *        @OA\Property(property="userTimezone", type="string", example="Europe/London"),
 *        @OA\Property(property="currency", type="string", example="EUR"),
 *        @OA\Property(
 *          property="salesCategories",
 *          type="object",
 *          @OA\Property(
 *            property="default",
 *            type="object",
 *            @OA\Property(property="id", type="string", nullable=true),
 *            @OA\Property(property="name", type="string", nullable=true),
 *            @OA\Property(property="depth", type="integer", example=1),
 *            @OA\Property(property="color_hex", type="string", nullable=true),
 *            @OA\Property(property="tags", type="array", @OA\Items(type="string")),
 *            @OA\Property(property="count_transactions", type="integer", nullable=true),
 *            @OA\Property(property="is_default", type="boolean", example=true),
 *            @OA\Property(property="amount", type="number", nullable=true),
 *            @OA\Property(property="type", type="string", example="money"),
 *            @OA\Property(property="hasChildren", type="boolean", example=true),
 *            @OA\Property(property="children", type="array", @OA\Items(type="object"))
 *          ),
 *          @OA\Property(
 *            property="revenue",
 *            type="object",
 *            @OA\Property(property="id", type="string", example="revenue"),
 *            @OA\Property(property="name", type="string", example="Revenue"),
 *            @OA\Property(property="depth", type="integer", example=0),
 *            @OA\Property(property="amount", type="number", example=1817.4),
 *            @OA\Property(property="color_hex", type="string", nullable=true),
 *            @OA\Property(property="type", type="string", example="money"),
 *            @OA\Property(property="is_default", type="boolean", example=true),
 *            @OA\Property(property="children", type="array", @OA\Items(type="object")),
 *            @OA\Property(property="hasChildren", type="boolean", example=false)
 *          ),
 *          @OA\Property(
 *            property="estimatedProfit",
 *            type="object",
 *            @OA\Property(property="id", type="string", example="estimated_profit"),
 *            @OA\Property(property="name", type="string", example="Estimated margin"),
 *            @OA\Property(property="depth", type="integer", example=0),
 *            @OA\Property(property="amount", type="number", example=-15479.88),
 *            @OA\Property(property="color_hex", type="string", nullable=true),
 *            @OA\Property(property="type", type="string", example="money"),
 *            @OA\Property(property="is_default", type="boolean", example=true),
 *            @OA\Property(property="children", type="array", @OA\Items(type="object")),
 *            @OA\Property(property="hasChildren", type="boolean", example=false)
 *          )
 *        ),
 *        @OA\Property(
 *          property="dataSeries",
 *          type="object",
 *          @OA\AdditionalProperties(
 *            type="object",
 *            @OA\Property(property="date", type="string", example="2024-12-04"),
 *            @OA\Property(property="dateStart", type="string", example="2024-12-04"),
 *            @OA\Property(property="dateEnd", type="string", example="2024-12-04"),
 *            @OA\Property(property="salesCategories", type="object"),
 *            @OA\Property(property="units", type="object"),
 *            @OA\Property(property="estimatedProfit", type="number", example=-1792.14),
 *            @OA\Property(property="expenses", type="number", example=-1926.67),
 *            @OA\Property(property="revenue", type="number", example=134.53)
 *          )
 *        ),
 *        @OA\Property(
 *          property="ppcMetrics",
 *          type="array",
 *          @OA\Items(
 *            type="object",
 *            @OA\Property(property="id", type="string", example="sponsored_brands"),
 *            @OA\Property(property="name", type="string", example="Sponsored brands"),
 *            @OA\Property(property="type", type="string", example="money"),
 *            @OA\Property(property="amount", type="number", example=0),
 *            @OA\Property(property="prevAmount", type="number", nullable=true),
 *            @OA\Property(property="prevComparePercents", type="number", nullable=true),
 *            @OA\Property(
 *              property="subMetrics",
 *              type="array",
 *              @OA\Items(
 *                type="object",
 *                @OA\Property(property="id", type="string"),
 *                @OA\Property(property="name", type="string"),
 *                @OA\Property(property="type", type="string"),
 *                @OA\Property(property="amount", type="number")
 *              ),
 *              nullable=true
 *            )
 *          )
 *        )
 *      )
 *    )
 * ),
 * @OA\Get(path="/v1/widget/profit-and-loss",
 *    summary="Profit and lost widget",
 *    tags={"Widget"},
 *    security={{"oauth2":{}}},
 *      @OA\Parameter(
 *          name="customerId",
 *          in="query",
 *          description="Customer Id - required to admin user or internal client",
 *          required=false,
 *          @OA\Schema(
 *            type="string",
 *            default="1"
 *          ),
 *      ),
 *    @OA\Parameter(
 *        name="dateStart",
 *        in="query",
 *        description="Event period start date",
 *        required=true,
 *        @OA\Schema(type = "string", format = "date YYY-M-D")
 *    ),
 *    @OA\Parameter(
 *        name="dateEnd",
 *        in="query",
 *        description="Event period end date",
 *        required=true,
 *        @OA\Schema(type = "string", format = "date YYY-M-D")
 *    ),
 *    @OA\Parameter(
 *        name="isTransactionDateMode",
 *        in="query",
 *        description="Use transaction date for all calcualtions or no (order date is using by default)",
 *        required=false,
 *        @OA\Schema(type="boolean")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *    ),
 *    @OA\Parameter(
 *        name="sellerId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="asin",
 *        in="query",
 *        description="Asin",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="ean",
 *        in="query",
 *        description="EAN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="isbn",
 *        in="query",
 *        description="ISBN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="upc",
 *        in="query",
 *        description="UPC",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="title",
 *        in="query",
 *        description="Title",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="mainImage",
 *        in="query",
 *        description="Main image",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="parentAsin",
 *        in="query",
 *        description="Parent ASIN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="brand",
 *        in="query",
 *        description="Brand",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="model",
 *        in="query",
 *        description="Model",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="productType",
 *        in="query",
 *        description="Product Type",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *          name="stockType",
 *          in="query",
 *          description="Stock type",
 *          required=false,
 *          @OA\Schema(type="string"),
 *    ),
 *    @OA\Parameter(
 *        name="manufacturer",
 *        in="query",
 *        description="Manufacturer",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="adultProduct",
 *        in="query",
 *        description="Adult Product",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="offerType",
 *        in="query",
 *        description="Offer Type",
 *        required=false,
 *        @OA\Schema(type="string", enum={"B2B", "B2C"})
 *    ),
 *    @OA\Parameter(
 *        name="sellerSku",
 *        in="query",
 *        description="Seller SKU (use coma as separator to send several values)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="maxDepth",
 *        in="query",
 *        description="Maximum depth of sales categories tree structure and data series",
 *        required=false,
 *        @OA\Schema(type = "integer")
 *    ),
 *    @OA\Parameter(
 *        name="salesCategoryId",
 *        in="query",
 *        description="Root sales category id, only data related to this category will be returned",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="periodType",
 *        in="query",
 *        description="day|week|month|year",
 *        required=false,
 *        @OA\Schema(type = "string", example="day")
 *    ),
 *    @OA\Parameter(
 *        name="tagId",
 *        in="query",
 *        description="Tag id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string", example = "1,2")
 *    ),
 *    @OA\Response(
 *        response=200,
 *        description="Successful response",
 *        @OA\JsonContent(ref="#/components/schemas/PivotTable")
 *    ),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *    @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *  ),
 * @OA\Get(path="/v1/widget/key-performance",
 *   summary="Key perfromance widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(
 *        response=200,
 *        description="Key performance widget data",
 *        @OA\JsonContent(
 *            example={
 *                "updatedAt": "2025-06-06 17:13:10",
 *                "amounts": {
 *                    "ordered_product_sales": 264.25,
 *                    "revenue": 265.69,
 *                    "net_profit": -16.21,
 *                    "margin": -6.1,
 *                    "roi": -5.75,
 *                    "ppc": -80.17
 *                },
 *                "units": {
 *                    "units": 74,
 *                    "orders": 61,
 *                    "ordersCanceled": 1,
 *                    "refunds": 0,
 *                    "promo": 0
 *                },
 *                "left_side": {
 *                    {"id": "ordered_product_sales", "name": "Product sales", "type": "money", "amount": 264.25},
 *                    {"id": "revenue", "name": "Revenue", "type": "money", "amount": 265.69},
 *                    {"id": "net_profit", "name": "Estimated margin", "type": "money", "amount": -16.21},
 *                    {"id": "ppc", "name": "Ads (PPC)", "type": "count", "amount": -80.17}
 *                },
 *                "right_side": {
 *                    {"id": "orders", "name": "Order items", "type": "count", "amount": 61},
 *                    {"id": "units", "name": "Units", "type": "count", "amount": 74},
 *                    {"id": "promo", "name": "Promo", "type": "count", "amount": 0},
 *                    {"id": "refunds", "name": "Refunds", "type": "count", "amount": 0},
 *                    {"id": "margin", "name": "Margin", "type": "pct", "amount": -6.1},
 *                    {"id": "roi", "name": "ROI", "type": "pct", "amount": -5.75},
 *                    {"id": "markup", "name": "Markup", "type": "pct", "amount": -37.44}
 *                },
 *                "profit_breakdown": {
 *                    {"id": "product_sales_4", "name": "Product sales", "type": "money", "amount": 264.25, "style": "regular"},
 *                    {"id": "shipping_charges_1", "name": "Shipping charges", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "gift_wrap_charges_1", "name": "Gift wrap charges", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "reimbursements_1", "name": "Reimbursements", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "taxes_vat", "name": "Taxes & VAT", "type": "money", "amount": -40.8837, "style": "regular"},
 *                    {"id": "refunds_and_chargebacks_2", "name": "Refunds and chargebacks", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "fba_inbound_fee_3", "name": "FBA inbound fee", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "fba_outbound_fee_3", "name": "FBA outbound fee", "type": "money", "amount": -68.69, "style": "regular"},
 *                    {"id": "service_fee_1", "name": "Service fee", "type": "money", "amount": -38.8085, "style": "regular"},
 *                    {"id": "technology_fee_1", "name": "Technology fee", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "postage_fee_2", "name": "Postage fee", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "miscellaneous_fees", "name": "Miscellaneous fees", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "other_fees_1", "name": "Other fees", "type": "money", "amount": -8.61, "style": "regular"},
 *                    {"id": "cost_of_goods_1", "name": "Cost of goods", "type": "money", "amount": -43.3, "style": "regular"},
 *                    {"id": "fbm_shipping_costs", "name": "FBM Shipping costs", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "guarantee_claim_2", "name": "Guarantee claim", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "promotion_1", "name": "Promotion", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "ads_ppc", "name": "Ads (PPC)", "type": "money", "amount": -80.17, "style": "regular"},
 *                    {"id": "indirect_costs_1", "name": "Indirect costs", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "other_costs_1", "name": "Other costs", "type": "money", "amount": 0, "style": "regular"},
 *                    {"id": "wholesale_liquidation_8", "name": "Wholesale liquidation", "type": "money", "amount": 0, "style": "regular"},
 *                    {
 *                        "id": null,
 *                        "title": null,
 *                        "children": {
 *                            {"id": "estimated_profit", "name": "Estimated margin", "type": "money", "amount": -16.21, "style": "total_2"},
 *                            {"id": "margin", "name": "Margin", "type": "pct", "amount": -6.1, "style": "regular"},
 *                            {"id": "roi", "name": "ROI", "type": "pct", "amount": -5.75, "style": "regular"},
 *                            {"id": "markup", "name": "Markup", "type": "pct", "amount": -37.44, "style": "regular"},
 *                            {"id": "acos", "name": "ACoS", "type": "pct", "amount": 391.5772135790812, "style": "regular"}
 *                        }
 *                    }
 *                }
 *            }
 *        )
 *   ),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *    @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *    @OA\Response(response=405, description = "Method Not Allowed"),
 *    @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse"),
 * ),
 * @OA\Get(path="/v1/widget/overall-statistics",
 *   summary="Overal statistics widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *    ),
 *    @OA\Parameter(
 *        name="sellerId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="asin",
 *        in="query",
 *        description="Asin",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="ean",
 *        in="query",
 *        description="EAN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="isbn",
 *        in="query",
 *        description="ISBN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="upc",
 *        in="query",
 *        description="UPC",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="title",
 *        in="query",
 *        description="Catalog product name",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="mainImage",
 *        in="query",
 *        description="Main image",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="parentAsin",
 *        in="query",
 *        description="Parent ASIN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="brand",
 *        in="query",
 *        description="Brand",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="model",
 *        in="query",
 *        description="Model",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="productType",
 *        in="query",
 *        description="Product Type",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *    ),
 *    @OA\Parameter(
 *        name="manufacturer",
 *        in="query",
 *        description="Manufacturer",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="adultProduct",
 *        in="query",
 *        description="Adult Product",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *    @OA\Parameter(
 *        name="sellerSku",
 *        in="query",
 *        description="Seller SKU (use coma as separator to send several values)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *   @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *   @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *   @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse"),
 *   @OA\Response(
 *        response=200,
 *        description="Overall statistics widget data",
 *        @OA\JsonContent(
 *            example={
 *                "metrics": {
 *                    {
 *                        "id": "ordered_product_sales",
 *                        "name": "Product sales",
 *                        "type": "money",
 *                        "amount": 3738.84,
 *                        "prevAmount": 2166.83,
 *                        "prevComparePercents": 72.55
 *                    },
 *                    {
 *                        "id": "revenue",
 *                        "name": "Revenue",
 *                        "type": "money",
 *                        "amount": 3865.58,
 *                        "prevAmount": 2224.63,
 *                        "prevComparePercents": 73.76
 *                    },
 *                    {
 *                        "id": "expenses",
 *                        "name": "Expenses",
 *                        "type": "money",
 *                        "amount": -4462.7,
 *                        "prevAmount": -4293.77,
 *                        "prevComparePercents": 3.93
 *                    },
 *                    {
 *                        "id": "estimated_profit",
 *                        "name": "Estimated margin",
 *                        "type": "money",
 *                        "amount": -597.12,
 *                        "prevAmount": -2069.14,
 *                        "prevComparePercents": -71.14
 *                    },
 *                    {
 *                        "id": "margin",
 *                        "name": "Estimated margin %",
 *                        "type": "pct",
 *                        "amount": -15.45,
 *                        "prevAmount": -93.01,
 *                        "prevComparePercents": 77.56
 *                    },
 *                    {
 *                        "id": "roi",
 *                        "name": "ROI",
 *                        "type": "pct",
 *                        "amount": -13.38,
 *                        "prevAmount": -48.19,
 *                        "prevComparePercents": 34.81
 *                    },
 *                    {
 *                        "id": "orders",
 *                        "name": "Order items",
 *                        "type": "count",
 *                        "amount": 141,
 *                        "prevAmount": 170,
 *                        "prevComparePercents": -17.06
 *                    },
 *                    {
 *                        "id": "units",
 *                        "name": "Units",
 *                        "type": "count",
 *                        "amount": 158,
 *                        "prevAmount": 198,
 *                        "prevComparePercents": -20.2
 *                    },
 *                    {
 *                        "id": "promotion_amount",
 *                        "name": "Promotion",
 *                        "type": "count",
 *                        "amount": 14,
 *                        "prevAmount": 16,
 *                        "prevComparePercents": -12.5
 *                    },
 *                    {
 *                        "id": "refunds",
 *                        "name": "Refunds",
 *                        "type": "count",
 *                        "amount": 11,
 *                        "prevAmount": 8,
 *                        "prevComparePercents": 37.5
 *                    },
 *                    {
 *                        "id": "total_acos",
 *                        "name": "Total ACoS",
 *                        "type": "pct",
 *                        "amount": 2.88,
 *                        "prevAmount": 15.09,
 *                        "prevComparePercents": -80.91
 *                    },
 *                    {
 *                        "id": "acos",
 *                        "name": "ACoS",
 *                        "type": "pct",
 *                        "amount": 289.17,
 *                        "prevAmount": 0,
 *                        "prevComparePercents": 100
 *                    }
 *                }
 *            }
 *        )
 *   ),
 * ),
 * @OA\Get(path="/v1/widget/refunds",
 *   summary="Refunds widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="catalogProductName",
 *       in="query",
 *       description="Catalog product name",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(response=200, description="Successful response", @OA\JsonContent(
 *       type="array",
 *       @OA\Items(
 *           type="object",
 *           @OA\Property(
 *               property="refund_reason",
 *               type="string",
 *               description="Reason for the refund",
 *               example="APPAREL_TOO_LARGE"
 *           ),
 *           @OA\Property(
 *               property="refunds",
 *               type="integer",
 *               description="Number of refunds",
 *               example=5236
 *           ),
 *           @OA\Property(
 *               property="name",
 *               type="string",
 *               description="Name of the refund reason",
 *               example="Name"
 *           ),
 *           @OA\Property(
 *               property="description",
 *               type="string",
 *               description="Description of the refund reason",
 *               example="Description"
 *           )
 *       ),
 *       example={{
 *               "refund_reason": "APPAREL_TOO_LARGE",
 *               "refunds": 5236,
 *               "name": "text",
 *               "description": ""
 *       }, {
 *               "refund_reason": "AMZ-PG-APP-TOO-SMALL",
 *               "refunds": 33148,
 *               "name": "text",
 *               "description": ""
 *       }}
 *   )),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *    @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *    @OA\Response(response=405, description = "Method Not Allowed"),
 *    @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse"),
 * )
 */
class WidgetController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['sales-history', 'sales-history-v-one', 'profit-and-loss'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['key-performance', 'overall-statistics', 'refunds'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['sales-history'] = [
            'class' => GetSalesHistory::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['sales-history-v-one'] = [
            'class' => GetSalesHistoryV1::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['key-performance'] = [
            'class' => GetKeyPerformance::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['overall-statistics'] = [
            'class' => GetOverallStatistics::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['refunds'] = [
            'class' => GetRefunds::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['profit-and-loss'] = [
            'class' => GetProfitAndLost::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['refunds'] = [
            'class' => GetRefunds::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    protected function verbs(): array
    {
        $verbs = parent::verbs();

        $verbs['sales-history'] = ['GET'];
        $verbs['sales-history-v-one'] = ['GET'];
        $verbs['key-performance'] = ['GET'];
        $verbs['overall-statistics'] = ['GET'];
        $verbs['refunds'] = ['GET'];

        return $verbs;
    }
}
