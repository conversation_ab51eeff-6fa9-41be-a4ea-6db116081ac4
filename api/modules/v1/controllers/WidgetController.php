<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\widget\GetKeyPerformance;
use api\modules\v1\controllers\actions\widget\GetOverallStatistics;
use api\modules\v1\controllers\actions\widget\GetProfitAndLost;
use api\modules\v1\controllers\actions\widget\GetSalesHistory;
use api\modules\v1\controllers\actions\widget\GetSalesHistoryV1;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use yii\base\Model;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * @OA\Get(path="/v1/widget/sales-history",
 *   summary="Sales history chart",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="Title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="maxDepth",
 *       in="query",
 *       description="Maximum depth of sales categories tree structure and data series",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Root sales category id, only data related to this category will be returned",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="periodType",
 *       in="query",
 *       description="day|week|month|year",
 *       required=false,
 *       @OA\Schema(type = "string", example="day")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/widget/sales-history-v-one",
 *   summary="Sales history chart",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="Title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="maxDepth",
 *       in="query",
 *       description="Maximum depth of sales categories tree structure and data series",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Root sales category id, only data related to this category will be returned",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="periodType",
 *       in="query",
 *       description="day|week|month|year",
 *       required=false,
 *       @OA\Schema(type = "string", example="day")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/widget/profit-and-lost",
 *    summary="Profit and lost widget",
 *    tags={"Widget"},
 *    security={{"oauth2":{}}},
 *      @OA\Parameter(
 *          name="customerId",
 *          in="query",
 *          description="Customer Id - required to admin user or internal client",
 *          required=false,
 *          @OA\Schema(
 *            type="string",
 *            default="1"
 *          ),
 *      ),
 *    @OA\Parameter(
 *        name="dateStart",
 *        in="query",
 *        description="Event period start date",
 *        required=true,
 *        @OA\Schema(type = "string", format = "date YYY-M-D")
 *    ),
 *    @OA\Parameter(
 *        name="dateEnd",
 *        in="query",
 *        description="Event period end date",
 *        required=true,
 *        @OA\Schema(type = "string", format = "date YYY-M-D")
 *    ),
 *    @OA\Parameter(
 *        name="isTransactionDateMode",
 *        in="query",
 *        description="Use transaction date for all calcualtions or no (order date is using by default)",
 *        required=false,
 *        @OA\Schema(type="boolean")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *    ),
 *    @OA\Parameter(
 *        name="sellerId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="asin",
 *        in="query",
 *        description="Asin",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="ean",
 *        in="query",
 *        description="EAN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="isbn",
 *        in="query",
 *        description="ISBN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="upc",
 *        in="query",
 *        description="UPC",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="title",
 *        in="query",
 *        description="Title",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="mainImage",
 *        in="query",
 *        description="Main image",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="parentAsin",
 *        in="query",
 *        description="Parent ASIN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="brand",
 *        in="query",
 *        description="Brand",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="model",
 *        in="query",
 *        description="Model",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="productType",
 *        in="query",
 *        description="Product Type",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *          name="stockType",
 *          in="query",
 *          description="Stock type",
 *          required=false,
 *          @OA\Schema(type="string"),
 *    ),
 *    @OA\Parameter(
 *        name="manufacturer",
 *        in="query",
 *        description="Manufacturer",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="adultProduct",
 *        in="query",
 *        description="Adult Product",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="offerType",
 *        in="query",
 *        description="Offer Type",
 *        required=false,
 *        @OA\Schema(type="string", enum={"B2B", "B2C"})
 *    ),
 *    @OA\Parameter(
 *        name="sellerSku",
 *        in="query",
 *        description="Seller SKU (use coma as separator to send several values)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="maxDepth",
 *        in="query",
 *        description="Maximum depth of sales categories tree structure and data series",
 *        required=false,
 *        @OA\Schema(type = "integer")
 *    ),
 *    @OA\Parameter(
 *        name="salesCategoryId",
 *        in="query",
 *        description="Root sales category id, only data related to this category will be returned",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="periodType",
 *        in="query",
 *        description="day|week|month|year",
 *        required=false,
 *        @OA\Schema(type = "string", example="day")
 *    ),
 *    @OA\Parameter(
 *        name="tagId",
 *        in="query",
 *        description="Tag id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string", example = "1,2")
 *    ),
 *    @OA\Response(response=400, description = "Bad Request"),
 *    @OA\Response(response=401, description = "Invalid token supplied"),
 *    @OA\Response(response=404, description = "Not found"),
 *    @OA\Response(response=405, description = "Method Not Allowed"),
 *    @OA\Response(response=422, description = "Data Validation Failed"),
 *  ),
 * @OA\Get(path="/v1/widget/key-performance",
 *   summary="Key perfromance widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="title",
 *       in="query",
 *       description="title",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/widget/overall-statistics",
 *   summary="Overal statistics widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *    ),
 *    @OA\Parameter(
 *        name="sellerId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="marketplaceId",
 *        in="query",
 *        description="Seller id (comma separated values can be sent)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="currencyId",
 *        in="query",
 *        description="Currency code",
 *        required=false,
 *        @OA\Schema(type = "string", example = "EN")
 *    ),
 *    @OA\Parameter(
 *        name="asin",
 *        in="query",
 *        description="Asin",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="ean",
 *        in="query",
 *        description="EAN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="isbn",
 *        in="query",
 *        description="ISBN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="upc",
 *        in="query",
 *        description="UPC",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="title",
 *        in="query",
 *        description="Catalog product name",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="mainImage",
 *        in="query",
 *        description="Main image",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="parentAsin",
 *        in="query",
 *        description="Parent ASIN",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="brand",
 *        in="query",
 *        description="Brand",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="model",
 *        in="query",
 *        description="Model",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="productType",
 *        in="query",
 *        description="Product Type",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *         name="stockType",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *    ),
 *    @OA\Parameter(
 *        name="manufacturer",
 *        in="query",
 *        description="Manufacturer",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *    @OA\Parameter(
 *        name="adultProduct",
 *        in="query",
 *        description="Adult Product",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *   @OA\Parameter(
 *       name="offerType",
 *       in="query",
 *       description="Offer Type",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"})
 *   ),
 *    @OA\Parameter(
 *        name="sellerSku",
 *        in="query",
 *        description="Seller SKU (use coma as separator to send several values)",
 *        required=false,
 *        @OA\Schema(type = "string")
 *    ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/widget/refunds",
 *   summary="Refunds widget",
 *   tags={"Widget"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Parameter(
 *       name="dateStart",
 *       in="query",
 *       description="Event period start date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="dateEnd",
 *       in="query",
 *       description="Event period end date",
 *       required=true,
 *       @OA\Schema(type = "string", format = "date YYY-M-D")
 *   ),
 *   @OA\Parameter(
 *       name="isTransactionDateMode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="boolean")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceSellerIds",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="sellerId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplaceId",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="catalogProductName",
 *       in="query",
 *       description="Catalog product name",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="mainImage",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parentAsin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="productType",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adultProduct",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="sellerSku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currencyId",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="tagId",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Response(response=200, description="Successful response", @OA\JsonContent(
 *       type="array",
 *       @OA\Items(
 *           type="object",
 *           @OA\Property(
 *               property="refund_reason",
 *               type="string",
 *               description="Reason for the refund",
 *               example="APPAREL_TOO_LARGE"
 *           ),
 *           @OA\Property(
 *               property="refunds",
 *               type="integer",
 *               description="Number of refunds",
 *               example=5236
 *           ),
 *           @OA\Property(
 *               property="name",
 *               type="string",
 *               description="Name of the refund reason",
 *               example="Name"
 *           ),
 *           @OA\Property(
 *               property="description",
 *               type="string",
 *               description="Description of the refund reason",
 *               example="Description"
 *           )
 *       ),
 *       example={{
 *               "refund_reason": "APPAREL_TOO_LARGE",
 *               "refunds": 5236,
 *               "name": "text",
 *               "description": ""
 *       }, {
 *               "refund_reason": "AMZ-PG-APP-TOO-SMALL",
 *               "refunds": 33148,
 *               "name": "text",
 *               "description": ""
 *       }}
 *   )),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * )
 */
class WidgetController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['sales-history', 'sales-history-v-one', 'profit-and-lost'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['key-performance', 'overall-statistics'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['sales-history'] = [
            'class' => GetSalesHistory::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['sales-history-v-one'] = [
            'class' => GetSalesHistoryV1::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['key-performance'] = [
            'class' => GetKeyPerformance::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['overall-statistics'] = [
            'class' => GetOverallStatistics::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['profit-and-lost'] = [
            'class' => GetProfitAndLost::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    protected function verbs(): array
    {
        $verbs = parent::verbs();

        $verbs['sales-history'] = ['GET'];
        $verbs['sales-history-v-one'] = ['GET'];
        $verbs['key-performance'] = ['GET'];
        $verbs['overall-statistics'] = ['GET'];

        return $verbs;
    }
}
