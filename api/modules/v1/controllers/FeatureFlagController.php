<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\featureFlag\GetFeatureFlagGlobalAction;
use api\modules\v1\controllers\actions\featureFlag\GetFeatureFlagAction;
use common\components\featureFlag\FeatureFlagService;
use common\models\FeatureFlag;
use OpenApi\Annotations as OA;
use Yii;
use yii\base\Model;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\NotFoundHttpException;

/**
 * FeatureFlag APIv1
 * 
 * @OA\Get(path="/v1/feature-flag",
 *   summary="Get all feature flag list",
 *   tags={"Feature Flags"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *     name="customerId",
 *     in="query",
 *     description="",
 *     required=false,
 *     @OA\Schema(type="integer")
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Retrieves the collection of feature flags for customer.",
 *     @OA\MediaType(
 *        mediaType="application/json",
 *        @OA\Schema(
 *           type="array",
 *           @OA\Items(ref="#/components/schemas/FeatureFlag"),
 *        ),
 *     ),
 *   ),
 *   @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *   @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *   @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *   @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *   @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 * @OA\Get(path="/v1/feature-flag/global-status",
 *    summary="Show if feature enabled for all customers",
 *    tags={"Feature Flags"},
 *    security={{"oauth2":{}}},
 *    @OA\Response(
 *      response=200,
 *      description="Retrieves the collection global feature flags (applicable on all customers)",
 *      @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(
 *            type="array",
 *            @OA\Items(ref="#/components/schemas/FeatureFlag"),
 *         ),
 *      ),
 *    ),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *    @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *    @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 *  )
 */
class FeatureFlagController extends Controller
{
    public $modelClass = Model::class;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'roles' => ['@'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];
        
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'index' => ['GET'],
                'view' => ['GET'],
                'global-status' => ['GET'],
            ],
        ];
        
        return $behaviors;
    }

    public function actions(): array
    {
        $actions = parent::actions();
        unset(
            $actions['index'],
            $actions['update'],
            $actions['create'],
            $actions['delete'],
            $actions['view'],
        );

        $actions['index'] = [
            'class' => GetFeatureFlagAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['global-status'] = [
            'class' => GetFeatureFlagGlobalAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

}
