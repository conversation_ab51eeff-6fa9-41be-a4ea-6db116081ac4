<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\transaction\CategoryFilters;
use common\components\Permissions;
use common\models\customer\TransactionExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use yii\filters\AccessControl;

/**
 * MessageController implements the REST actions for Message model.
 * @OA\Get(path="/v1/transaction-v1",
 *   summary="Retrieves the collection of Transaction resources.",
 *   tags={"Transaction"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(
 *         name="marketplace_seller_ids",
 *         in="query",
 *         description="Pairs of marketplace and seller ids in json format. Pass 'all' as sellerId to see transactions on sellers level, pass 'global' or nothing to see transactions on global level",
 *         required=false,
 *         @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *     @OA\Parameter(
 *         name="currency_id",
 *         in="query",
 *         description="Currency Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="amazon_order_id",
 *         in="query",
 *         description="Amazon order id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller ID",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sku",
 *         in="query",
 *         description="Product SKU",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="asin",
 *         in="query",
 *         description="Product ASIN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *      @OA\Parameter(
 *          name="product_asin",
 *          in="query",
 *          required=false,
 *          @OA\Schema(type="string"),
 *          description="Product ASIN"
 *      ),
 *      @OA\Parameter(
 *          name="seller_sku",
 *          in="query",
 *          required=false,
 *          @OA\Schema(type="string"),
 *          description="Seller SKU"
 *      ),
 *       @OA\Parameter(
 *           name="product_ean",
 *           in="query",
 *           description="Product EAN",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *       @OA\Parameter(
 *           name="product_upc",
 *           in="query",
 *           description="Product UPC",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *       @OA\Parameter(
 *           name="product_isbn",
 *           in="query",
 *           description="Product ISBN",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *       @OA\Parameter(
 *           name="product_brand",
 *           in="query",
 *           description="Product Brand",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *       @OA\Parameter(
 *           name="factor",
 *           in="query",
 *           description="Factor data completeness",
 *           required=false,
 *           @OA\Schema(type="string", enum={"adjustments_fee"}),
 *       ),
 *       @OA\Parameter(
 *           name="product_type",
 *           in="query",
 *           description="Product Type",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *       @OA\Parameter(
 *           name="product_manufacturer",
 *           in="query",
 *           description="Product Manufacturer",
 *           required=false,
 *           @OA\Schema(type="string"),
 *       ),
 *     @OA\Parameter(
 *         name="product_parent_asin",
 *         in="query",
 *         description="Parent Asin",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="adult_product",
 *         in="query",
 *         description="Product Adult",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,0})
 *     ),
 *     @OA\Parameter(
 *         name="title",
 *         in="query",
 *         description="Product title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace ID",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="product_id",
 *         in="query",
 *         description="Product ID",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *     ),
 *     @OA\Parameter(
 *         name="condition",
 *         in="query",
 *         description="Condition",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *     ),
 *     @OA\Parameter(
 *         name="stock_type",
 *         in="query",
 *         description="Product stock type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"FBM", "FBA"}),
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Order Offer type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *     ),
 *     @OA\Parameter(
 *         name="sales_category_depth_1",
 *         in="query",
 *         description="Id of sales category (depth 1)",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sales_category_depth_2",
 *         in="query",
 *         description="Id of sales category (depth 2)",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="transaction_type",
 *         in="query",
 *         description="Transaction type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"adjustment", "retrocharge", "standard", "estimated"}),
 *     ),
 *     @OA\Parameter(
 *         name="transaction_level",
 *         in="query",
 *         description="Transaction level",
 *         required=false,
 *         @OA\Schema(type="string", enum={"account", "marketplace", "order", "product", "global"}),
 *     ),
 *     @OA\Parameter(
 *         name="amount",
 *         in="query",
 *         description="Amount",
 *         required=false,
 *         @OA\Schema(type="number"),
 *     ),
 *     @OA\Parameter(
 *         name="transaction_date",
 *         in="query",
 *         description="Transaction date",
 *         required=false,
 *         @OA\Schema(type="string", format="datetime"),
 *     ),
 *     @OA\Parameter(
 *         name="posted_date",
 *         in="query",
 *         description="Order purchase date",
 *         required=false,
 *         @OA\Schema(type="string", format="datetime"),
 *     ),
 *     @OA\Parameter(
 *       name="tag_id",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Returns date of the last synchronization",
 *     @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/Transaction"),
 *             ),
 *     ),
 *   ),
 *   @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *   @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *   @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */
class TransactionV1Controller extends Controller
{
    use AllRecordsTrait;

    public $modelClass = TransactionExtendedViewV1::class;

    protected bool $isCustomerRelated = true;


    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index'],
                    'roles' => [Permissions::TRANSACTION_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        unset($actions['create']);
        unset($actions['update']);
        unset($actions['delete']);

        return $actions;
    }
}
