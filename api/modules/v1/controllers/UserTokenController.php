<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\userToken\MyAction;
use yii\base\Model;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * StaffController implements the REST actions for UserToken model.
 * @OA\Get(path="/v1/user-token/my",
 *   summary="View the current user token info",
 *   tags={"UserToken"},
 *   security={{"oauth2":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Returns current UserToken instance",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/UserToken"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 */
class UserTokenController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = false;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['my'],
                    'verbs' => ['GET'],
                    'roles' => ['@'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['my'] = [
            'class' => MyAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
