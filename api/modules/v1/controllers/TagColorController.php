<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\modules\v1\controllers\actions\product\BulkEditAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use common\models\customer\TagColor;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * @OA\Get(
 *     path="/v1/tag-color",
 *     summary="Retrieves the collection of TagColor resources.",
 *     tags={"TagColor"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Response(
 *         response=200,
 *         description="TagColor resource is updated",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/TagColor"),
 *             ),
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
* )
 */

/**
 * @OA\Delete(
 *     path="/v1/tag-color/{id}",
 *     summary="Deletes TagColor resources.",
 *     tags={"TagColor"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *      @OA\Parameter(
 *        name="id",
 *        in="path",
 *        description="Id",
 *        required=true,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *      ),
 *     @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/tag-color",
 *     summary="Create TagColor resource",
 *     tags={"TagColor"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *         request="TagColorRequest",
 *         required=true,
 *         @OA\JsonContent(
 *             @OA\Property(property="tag_color", type="string", example="#999999")
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="TagColor resource",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/TagColor")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */
class TagColorController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = TagColor::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions(): array
    {
        return parent::actions();
    }
}
