<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use common\components\dataImportExport\SupportedHandlers;
use common\components\Permissions;

/**
 * DataExportRecurrentController implements the REST actions for DataExportRecurrent model.
 *
 * @OA\Get(
 *     path="/v1/data-export-recurrent",
 *     summary="Retrieves the collection of DataExportRecurrent resources.",
 *     tags={"DataExportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Parameter(
 *         name="template_id",
 *         in="query",
 *         description="Template id (only for amazon_product handler)",
 *         required=false,
 *         @OA\Schema(
 *             type="integer"
 *         )
 *     ),
 *     @OA\Parameter(
 *         name="is_enabled",
 *         in="query",
 *         description="Is Enabled",
 *         required=false,
 *         @OA\Schema(
 *             type="integer",
 *             enum={1, 0}
 *         )
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created At",
 *         required=false,
 *         @OA\Schema(
 *             type="string"
 *         )
 *     ),
 *       @OA\Response(
 *          response=200,
 *          description="Retrieves the collection of resource.",
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  allOf={
 *                      @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                      @OA\Schema(
 *                          type="object",
 *                          @OA\Property(
 *                              property="data",
 *                              type="array",
 *                              @OA\Items(ref="#/components/schemas/DataExportRecurrent")
 *                          )
 *                      )
 *                  }
 *              )
 *          )
 *      ),
 *      @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *      @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *      @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/data-export-recurrent",
 *     summary="Create a DataExportRecurrent resource",
 *     tags={"DataExportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *           @OA\JsonContent(
 *               required={"handler_name", "cron_expr"},
 *               @OA\Property(
 *                   property="handler_name",
 *                   type="string",
 *                   description="The name of the handler",
 *                   example="product_cost_periods",
 *                   enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost"}
 *               ),
 *               @OA\Property(
 *                   property="template_id",
 *                   type="integer",
 *                   description="The ID of the template used for the export task",
 *                   example=112
 *               ),
 *               @OA\Property(
 *                   property="cron_expr",
 *                   type="string",
 *                   description="The cron expression defining the schedule",
 *                   example="3-59/5 * 1,13,15 * ?"
 *               ),
 *               @OA\Property(
 *                   property="is_enabled",
 *                   type="boolean",
 *                   description="Indicates if the task is enabled",
 *                   example=true,
 *               )
 *           )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Created DataExportRecurrent resource",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportRecurrent")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/data-export-recurrent/{id}",
 *     summary="Get a DataExportRecurrent resource",
 *     tags={"DataExportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportRecurrent resource response",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportRecurrent")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 * )
 */

/**
 * @OA\Put(
 *     path="/v1/data-export-recurrent/{id}",
 *     summary="Update a DataExportRecurrent resource",
 *     tags={"DataExportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\RequestBody(
 *          required=true,
 *          @OA\JsonContent(
 *              @OA\Property(
 *                  property="template_id",
 *                  type="integer",
 *                  description="The ID of the template used for the export task",
 *                  example=112
 *              ),
 *              @OA\Property(
 *                  property="cron_expr",
 *                  type="string",
 *                  description="The cron expression defining the schedule",
 *                  example="3-59/5 * 1,13,15 * ?"
 *              ),
 *              @OA\Property(
 *                  property="is_enabled",
 *                  type="boolean",
 *                  description="Indicates if the task is enabled",
 *                  example=true
 *              )
 *          )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Updated DataExportRecurrent resource",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportRecurrent")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Delete(
 *     path="/v1/data-export-recurrent/{id}",
 *     summary="Delete DataExportRecurrent resource",
 *     tags={"DataExportRecurrent"},
 *     security={{"oauth2":{}}},
 *        @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */
class DataExportRecurrentController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = 'common\models\DataExportRecurrent';

    public $updateScenario = 'update';

    protected bool $isCustomerRelated =  true;
    protected $handlerName = null;

    public function __construct($id, $module, $config = [])
    {
        $requestParams = array_merge(\Yii::$app->request->get(), \Yii::$app->request->post());
        $this->handlerName = $requestParams['handler_name'] ?? null;
        parent::__construct($id, $module, $config);
    }

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        if ($this->handlerName === SupportedHandlers::HANDLER_PRODUCT_COST_PERIODS ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_FBM_COST ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_ITEM_FBM_COST
        ) {
            $behaviors['access'] = Permissions::allowBasicPermissionsWith([
                'GET' => [Permissions::PRODUCT_COST_AUTO_EXPORT_VIEW],
                'POST' => [Permissions::PRODUCT_COST_AUTO_EXPORT_MANAGE],
                'PUT' => [Permissions::PRODUCT_COST_AUTO_EXPORT_MANAGE],
                'DELETE' => [Permissions::PRODUCT_COST_AUTO_EXPORT_MANAGE],
            ]);
        }

        return $behaviors;
    }
}
