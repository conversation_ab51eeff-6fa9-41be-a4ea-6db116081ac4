<?php

namespace api\modules\v1\controllers\filters;

use common\components\core\db\dbManager\DbManager;
use common\components\core\Exception\ModuleIsNotEnabledException;
use common\models\Seller;
use yii\caching\TagDependency;

class SynchronizedAccountGuard
{
    public function __invoke($rule, $action)
    {
        if (\Yii::$app->request->isOptions) {
            return;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var Seller $seller */
        $seller = Seller::find()
            ->where(['is_db_created' => true])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => Seller::getCacheTag($dbManager->getCustomerId())])
            )
            ->one();

        $accountIsNotActivatedMsg = 'Your account is being activated. Please wait for SELLERLOGIC to synchronize with Amazon.';
        if (empty($seller) || $seller->id === Seller::DEFAULT_SELLER_ID) {
            throw new ModuleIsNotEnabledException(
                403,
                \Yii::t('admin', $accountIsNotActivatedMsg)
            );
        }
    }
}
