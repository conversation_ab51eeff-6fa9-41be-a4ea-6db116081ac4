<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\message\CreateAction;
use common\models\Message;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use yii\rest\Action;

/**
 * MessageController implements the REST actions for Message model.
 * @OA\Get(path="/v1/message",
 *   summary="Retrieves the collection of Message resources.",
 *   tags={"Message"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="pageSize",
 *         in="query",
 *         description="Page size [1,100]",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="all",
 *         in="query",
 *         description="Show all records with pager [1,0]",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="id",
 *         in="query",
 *         description="Id",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="language",
 *         in="query",
 *         description="Language code",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="category",
 *         in="query",
 *         description="Category",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="message",
 *         in="query",
 *         description="Message",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *
 *   @OA\Response(
 *     response=200,
 *     description="Retrieves the collection of Message resources.",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/Message"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * ),
 *
 * @OA\Post(path="/v1/message",
 *   summary="Create Message resource",
 *   tags={"Message"},
 *   security={{"oauth2":{}}},
 *     @OA\RequestBody(
 *			@OA\JsonContent(
 *              type="object",
 *	 	            @OA\Property(property="category", type="string"),
 *	 	            @OA\Property(property="message", type="string"),
 *          )
 *        ),
 *
 *
 *   @OA\Response(
 *     response=200,
 *     description="Message resource is created",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/Message"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 *
 */
class MessageController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = Message::class;

    protected bool $isCustomerRelated = false;


    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index'],
                    'matchCallback' => [$this, 'isAllowed'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['POST'],
                    'actions' => ['create'],
                    'matchCallback' => [$this, 'isAllowed'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        unset($actions['update']);
        unset($actions['delete']);
        unset($actions['create']);
        unset($actions['view']);

        $actions['create'] = [
            'class' => CreateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    /**
     * @param AccessRule $rule
     * @param Action $action
     * @return bool
     */
    public function isAllowed(AccessRule $rule, Action $action): bool
    {
        return \Yii::$app->user->identity->isInternalClient();
    }
}
