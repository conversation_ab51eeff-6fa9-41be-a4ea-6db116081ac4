<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\dataExportTemplate\GetFilters;
use api\modules\v1\controllers\actions\product\BulkEditAction;
use api\modules\v1\controllers\actions\product\GetAmazonFeesBreakdown;
use api\modules\v1\controllers\actions\product\GetExpensesBreakdown;
use api\modules\v1\controllers\actions\product\EditAction;
use api\modules\v1\controllers\actions\product\GetProductFiltersAction;
use api\modules\v1\controllers\actions\product\GetRevenueBreakdown;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use common\models\customer\Product;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * @OA\Get(path="/v1/product",
 *   summary="View product cost data",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="search",
 *         in="query",
 *         description="Keyword to search by asin, sku, title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="source",
 *         in="query",
 *         description="Source (manual|repricer)",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="title",
 *         in="query",
 *         description="Title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sku",
 *         in="query",
 *         description="Sku",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="asin",
 *         in="query",
 *         description="ASIN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="ean",
 *         in="query",
 *         description="Product EAN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="upc",
 *         in="query",
 *         description="Product UPC",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="isbn",
 *         in="query",
 *         description="Product ISBN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="brand",
 *         in="query",
 *         description="Product Brand",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="factor",
 *         in="query",
 *         description="Factor data completeness",
 *         required=false,
 *         @OA\Schema(type="string", enum={"no_cost_of_goods", "no_vat", "no_fbm_shipping_costs", "no_other_fees", "cog_not_synchronized", "missing_products_in_repricer"}),
 *     ),
 *     @OA\Parameter(
 *         name="product_type",
 *         in="query",
 *         description="Product Type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="manufacturer",
 *         in="query",
 *         description="Product Manufacturer",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="parent_asin",
 *         in="query",
 *         description="Parent Asin",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="age_range",
 *         in="query",
 *         description="Age Range",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="adult_product",
 *         in="query",
 *         description="Product Adult",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,0})
 *     ),
 *     @OA\Parameter(
 *         name="buying_price",
 *         in="query",
 *         description="Buying price",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="other_fees",
 *         in="query",
 *         description="Other fees",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="shipping_cost",
 *         in="query",
 *         description="Shipping cost",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="is_enabled_sync_with_repricer",
 *         in="query",
 *         description="Is sync with repricer enabled",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,2}),
 *     ),
 *     @OA\Parameter(
 *         name="is_enabled_sync_with_global_marketplace",
 *         in="query",
 *         description="Is sync with global marketplace",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,2}),
 *     ),
 *     @OA\Parameter(
 *         name="repricer_id",
 *         in="query",
 *         description="Rpericer id",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,2}),
 *     ),
 *     @OA\Parameter(
 *         name="vat",
 *         in="query",
 *         description="Vat",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="stock_type",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="date_start",
 *         in="query",
 *         description="Date start",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="date_end",
 *         in="query",
 *         description="Date end",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="updated_at",
 *         in="query",
 *         description="Updated at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *     @OA\Parameter(
 *       name="tag_id",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of products.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/Product"),
 *             ),
 *         ),
 *     ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Get(path="/v1/product/filters",
 *   summary="View filter product",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="source",
 *         in="query",
 *         description="Source (manual|repricer)",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="title",
 *         in="query",
 *         description="Title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sku",
 *         in="query",
 *         description="Sku",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="asin",
 *         in="query",
 *         description="ASIN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="ean",
 *         in="query",
 *         description="Product EAN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="upc",
 *         in="query",
 *         description="Product UPC",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="isbn",
 *         in="query",
 *         description="Product ISBN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="brand",
 *         in="query",
 *         description="Product Brand",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="product_type",
 *         in="query",
 *         description="Product Type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="manufacturer",
 *         in="query",
 *         description="Product Manufacturer",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="parent_asin",
 *         in="query",
 *         description="Parent Asin",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="age_range",
 *         in="query",
 *         description="Age Range",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="adult_product",
 *         in="query",
 *         description="Product Adult",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,0})
 *     ),
 *     @OA\Parameter(
 *         name="buying_price",
 *         in="query",
 *         description="Buying price",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="other_fees",
 *         in="query",
 *         description="Other fees",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="shipping_cost",
 *         in="query",
 *         description="Shipping cost",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="is_enabled_sync_with_repricer",
 *         in="query",
 *         description="Is sync with repricer enabled",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,2}),
 *     ),
 *     @OA\Parameter(
 *         name="repricer_id",
 *         in="query",
 *         description="Rpericer id",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,2}),
 *     ),
 *     @OA\Parameter(
 *         name="vat",
 *         in="query",
 *         description="Vat",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="stock_type",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Parameter(
 *         name="date_start",
 *         in="query",
 *         description="Date start",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="date_end",
 *         in="query",
 *         description="Date end",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *     @OA\Parameter(
 *       name="tag_id",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *     ),
 *     @OA\Parameter(
 *         name="updated_at",
 *         in="query",
 *         description="Updated at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of products.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="object",
 *                  @OA\Property(
 *                      property="seller_ids",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="marketplace_ids",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="stock_types",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="currency_codes",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="brands",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="manufacturers",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="product_types",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  ),
 *                  @OA\Property(
 *                      property="offer_types",
 *                      type="object",
 *                      @OA\AdditionalProperties(
 *                          type="object",
 *                          @OA\Property(property="key", type="string"),
 *                          @OA\Property(property="value", type="string")
 *                      )
 *                  )
 *             )
 *         ),
 *     ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Put(path="/v1/product/{id}",
 *   summary="Update Product resource",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *   @OA\RequestBody(
 *	    @OA\JsonContent(
 *          type="object",
 *	 	    @OA\Property(property="is_enabled_sync_with_repricer", type="boolean"),
 *          @OA\Property(property="is_enabled_sync_with_global_marketplace", type="boolean"),
 *          @OA\Property(
 *              property="tag_id",
 *              type="array",
 *              @OA\Items(type="integer")
 *          )
 *      )
 *   ),
 *   @OA\Parameter(
 *        name="id",
 *        in="path",
 *        description="Id",
 *        required=true,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *   ),
 *   @OA\Parameter(
 *      name="customerId",
 *      in="query",
 *      description="Customer Id - required to admin user",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          default="1"
 *      ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Updated Product resource",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/Product"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * )
 * @OA\Post(path="/v1/product/bulk-edit",
 *   summary="Create the product bulk edit resource",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="search",
 *         in="query",
 *         description="Keyword to search by asin, sku, title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller Id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="source",
 *         in="query",
 *         description="Source (manual|repricer)",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="title",
 *         in="query",
 *         description="Title",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sku",
 *         in="query",
 *         description="Sku",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="asin",
 *         in="query",
 *         description="ASIN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="ean",
 *         in="query",
 *         description="Product EAN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="upc",
 *         in="query",
 *         description="Product UPC",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="isbn",
 *         in="query",
 *         description="Product ISBN",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="brand",
 *         in="query",
 *         description="Product Brand",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="product_type",
 *         in="query",
 *         description="Product Type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="manufacturer",
 *         in="query",
 *         description="Product Manufacturer",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="parent_asin",
 *         in="query",
 *         description="Parent Asin",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="age_range",
 *         in="query",
 *         description="Age Range",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="adult_product",
 *         in="query",
 *         description="Product Adult",
 *         required=false,
 *         @OA\Schema(type="integer", enum={1,0})
 *     ),
 *     @OA\Parameter(
 *         name="buying_price",
 *         in="query",
 *         description="Buying price",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="other_fees",
 *         in="query",
 *         description="Other fees",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="shipping_cost",
 *         in="query",
 *         description="Shipping cost",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="vat",
 *         in="query",
 *         description="Vat",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="stock_type",
 *         in="query",
 *         description="Stock type",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="date_start",
 *         in="query",
 *         description="Date start",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="date_end",
 *         in="query",
 *         description="Date end",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="updated_at",
 *         in="query",
 *         description="Updated at",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *   @OA\Parameter(
 *        name="marketplaceSellerIds",
 *        in="query",
 *        description="Pairs of marketplace and seller ids in json format",
 *        required=false,
 *        @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *    ),
 *    @OA\RequestBody(
 *      @OA\MediaType(
 *          mediaType="application/json",
 *			@OA\Schema(ref="#/components/schemas/ProductBulkEditForm"),
 *      )
 *    ),
 *
 *   @OA\Response(
 *     response=200,
 *     description="Product bulk edit has been created",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/DataImport"),
 *     ),
 *   ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 *
 * @OA\Get(
 *     path="/v1/product/amazon-fees-breakdown",
 *     summary="Returns data needed for amazon fees modal",
 *     tags={"Product"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="sellerSku",
 *         in="query",
 *         description="Seller sku",
 *         required=true,
 *     ),
 *     @OA\Parameter(
 *         name="marketplaceId",
 *         in="query",
 *         description="Marketplace Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sellerId",
 *         in="query",
 *         description="Seller Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * ),
 * @OA\Get(
 *     path="/v1/product/expenses-breakdown",
 *     summary="Returns data needed for expenses modal",
 *     tags={"Product"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="sellerSku",
 *         in="query",
 *         description="Seller sku",
 *         required=true,
 *     ),
 *     @OA\Parameter(
 *         name="marketplaceId",
 *         in="query",
 *         description="Marketplace Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sellerId",
 *         in="query",
 *         description="Seller Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * ),
 * @OA\Get(
 *     path="/v1/product/revenue-breakdown",
 *     summary="Returns data needed for revenue modal",
 *     tags={"Product"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="sellerSku",
 *         in="query",
 *         description="Seller sku",
 *         required=true,
 *     ),
 *     @OA\Parameter(
 *         name="marketplaceId",
 *         in="query",
 *         description="Marketplace Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sellerId",
 *         in="query",
 *         description="Seller Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of product revenue",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * ),
 */
class ProductController extends Controller
{
    public $modelClass = Product::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => [
                        'index',
                        'amazon-fees-breakdown',
                        'expenses-breakdown',
                        'revenue-breakdown'
                    ],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['bulk-edit'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['filters'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['bulk-edit'] = [
            'class' => BulkEditAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['update'] = [
            'class' => EditAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['filters'] = [
            'class' => GetProductFiltersAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['amazon-fees-breakdown'] = [
            'class' => GetAmazonFeesBreakdown::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['expenses-breakdown'] = [
            'class' => GetExpensesBreakdown::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['revenue-breakdown'] = [
            'class' => GetRevenueBreakdown::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
