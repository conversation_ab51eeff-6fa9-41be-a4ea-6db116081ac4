<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\indirectCost\BulkDeleteAction;
use api\modules\v1\controllers\actions\indirectCost\CreateAction;
use api\modules\v1\controllers\actions\indirectCost\UpdateAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use api\components\controllers\Controller;
use common\models\customer\IndirectCost;
use yii\filters\AccessControl;

/**
 * IndirectCostController implements the REST actions for IndirectCost model.
* @OA\Get(path="/v1/indirect-cost",
*   summary="Retrieves the collection of IndirectCost resources.",
*   tags={"IndirectCost"},
*   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="indirect_cost_type_id",
*         in="query",
*         description="Indirect Cost Type Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="product_id",
*         in="query",
*         description="Product Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="marketplace_id",
*         in="query",
*         description="Marketplace Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="seller_id",
*         in="query",
*         description="Seller Ids",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="amount",
*         in="query",
*         description="Amount",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="currency_id",
*         in="query",
*         description="Currency Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="cron_expr",
*         in="query",
*         description="Cron Expr",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
 *     @OA\Parameter(
 *         name="frequency",
 *         in="query",
 *         description="Frequency (syntax sugar for cron_expr)",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           enum={"one_time", "recurring"}
 *         ),
 *     ),
*     @OA\Parameter(
*         name="max_iterations",
*         in="query",
*         description="Max Iterations",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="date_start",
*         in="query",
*         description="Date Start",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="date_end",
*         in="query",
*         description="Date End",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="last_apply_date",
*         in="query",
*         description="Last Apply Date",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="created_at",
*         in="query",
*         description="Created At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="updated_at",
*         in="query",
*         description="Updated At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of IndirectCost resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCost"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Post(path="/v1/indirect-cost",
*   summary="Create IndirectCost resource",
*   tags={"IndirectCost"},
*   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
        *	 	            @OA\Property(property="indirect_cost_type_id", type="integer"),
    *	 	            @OA\Property(property="product_id", type="integer"),
    *	 	            @OA\Property(property="marketplace_id", type="string"),
    *	 	            @OA\Property(property="seller_id", type="string"),
    *	 	            @OA\Property(property="amount", type="decimal"),
    *	 	            @OA\Property(property="currency_id", type="string"),
    *	 	            @OA\Property(property="cron_expr", type="string"),
    *	 	            @OA\Property(property="max_iterations", type="integer"),
    *	 	            @OA\Property(property="date_start", type="timestamp"),
    *	 	            @OA\Property(property="date_end", type="timestamp")
*          )
*        ),
*
*
*   @OA\Response(
*     response=200,
*     description="IndirectCost resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCost"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="/v1/indirect-cost",
*   summary="Update the IndirectCost resource",
*   tags={"IndirectCost"},
*   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
		*	 	       @OA\Property(property="indirect_cost_type_id", type="integer"),
	*	 	       @OA\Property(property="product_id", type="integer"),
	*	 	       @OA\Property(property="marketplace_id", type="string"),
	*	 	       @OA\Property(property="seller_id", type="string"),
	*	 	       @OA\Property(property="amount", type="decimal"),
	*	 	       @OA\Property(property="currency_id", type="string"),
	*	 	       @OA\Property(property="cron_expr", type="string"),
	*	 	       @OA\Property(property="max_iterations", type="integer"),
	*	 	       @OA\Property(property="date_start", type="timestamp"),
	*	 	       @OA\Property(property="date_end", type="timestamp")
*          )
*        ),
*
*   @OA\Response(
*     response=200,
*     description="IndirectCost resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCost"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="/v1/indirect-cost",
*   summary="Delete IndirectCost resource",
*   tags={"IndirectCost"},
*   security={{"oauth2":{}}},
 *        @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
* )
 * @OA\Post(path="/v1/indirect-cost/bulk-delete",
 *   summary="Bulk delete IndirectCost resource",
 *   tags={"IndirectCost"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
 *     @OA\RequestBody(
 *       @OA\JsonContent(
 *           type="array",
 *           @OA\Items
 *           (
 *              type="object",
 *	 	            @OA\Property(property="ids", type="array", @OA\Items(type="integer")),
 *	 	            @OA\Property(property="side", type="string")
 *           )
 *       )
 *     ),
 *   @OA\Response(
 *     response=204,
 *     description="IndirectCost resource is deleted."
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * )
*/
class IndirectCostController extends Controller
{
    public $modelClass = IndirectCost::class;

    public bool $isCustomerRelated = true;


    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::INDIRECT_COSTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create', 'bulk-delete'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['bulk-delete'] = [
            'class' => BulkDeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['create'] = [
            'class' => CreateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['update'] = [
            'class' => UpdateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
