<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\productFilterList\CreateAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use common\models\customer\ProductFilterList;
use yii\filters\AccessControl;
use api\components\controllers\Controller;

/**
 * @OA\Get(path="/v1/product-filter-list",
 *   summary="View product filter list",
 *   tags={"ProductFilterList"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="name",
 *         in="query",
 *         description="Name filter list",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of products filter lists.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/ProductFilterList"),
 *             ),
 *         ),
 *     ),
 *   @OA\Response(response=400, description = "Bad Request"),
 *   @OA\Response(response=401, description = "Invalid token supplied"),
 *   @OA\Response(response=404, description = "Not found"),
 *   @OA\Response(response=405, description = "Method Not Allowed"),
 *   @OA\Response(response=422, description = "Data Validation Failed"),
 * ),
 * @OA\Put(path="/v1/product-filter-list/{id}",
 *   summary="Update Product filter list resource",
 *   tags={"ProductFilterList"},
 *   security={{"oauth2":{}}},
 *     @OA\RequestBody(
 *     @OA\JsonContent(
 *         type="object",
 *         @OA\Property(
 *             property="name",
 *             type="string",
 *             description="Name",
 *             example="test343"
 *         ),
 *         @OA\Property(
 *             property="filters",
 *             type="object",
 *             description="Filters data in JSON format",
 *             @OA\Property(
 *                 property="marketplaceId",
 *                 type="array",
 *                 @OA\Items(type="integer"),
 *                 description="List of marketplace IDs",
 *                 example={1, 2, 3}
 *             ),
 *             @OA\Property(
 *                 property="adult_product",
 *                 type="boolean",
 *                 description="Whether the product is for adults",
 *                 example=false
 *             )
 *         )
 *     )
 *     ),
 *   @OA\Parameter(
 *        name="id",
 *        in="path",
 *        description="Id",
 *        required=true,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *   ),
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer Id - required to admin user",
 *       required=false,
 *       @OA\Schema(
 *           type="string",
 *           default="1"
 *       ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Updated Product filter list resource",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/ProductFilterList"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Post(path="/v1/product-filter-list",
 *   summary="Create Product Filter List resource",
 *   tags={"ProductFilterList"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *     @OA\RequestBody(
 *     @OA\JsonContent(
 *         type="object",
 *         @OA\Property(
 *             property="name",
 *             type="string",
 *             description="Name",
 *             example="test343"
 *         ),
 *         @OA\Property(
 *             property="filters",
 *             type="object",
 *             description="Filters data in JSON format",
 *             @OA\Property(
 *                 property="marketplaceId",
 *                 type="array",
 *                 @OA\Items(type="integer"),
 *                 description="List of marketplace IDs",
 *                 example={1, 2, 3}
 *             ),
 *             @OA\Property(
 *                 property="adult_product",
 *                 type="boolean",
 *                 description="Whether the product is for adults",
 *                 example=false
 *             )
 *         )
 *     )
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Product Filter List resource is created",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/ProductFilterList"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Delete(path="/v1/product-filter-list/{id}",
 *   summary="Delete Product Filter list resource",
 *   tags={"ProductFilterList"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *      @OA\Parameter(
 *        name="id",
 *        in="path",
 *        description="Id",
 *        required=true,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *      ),
 *   @OA\Response(
 *     response=204,
 *     description="Product Filter List resource is deleted."
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * )
 */
class ProductFilterListController extends Controller
{
    public $modelClass = ProductFilterList::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['create'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['create'] = [
            'class' => CreateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
