<?php

namespace api\modules\v1\controllers;

use yii\filters\AccessControl;
use api\components\controllers\Controller;
use api\components\controllers\AllRecordsTrait;
use common\models\customer\clickhouse\ProductAggregatedRefund;
use api\modules\v1\controllers\actions\productAggregatedRefunds\GetCustomerComments;

/**
 * @OA\Get(path="/v1/product-aggregated-refunds",
 *   summary="Refunds Product widget",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *   @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *   @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *   @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *   @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *   @OA\Parameter(
 *       name="refund_date",
 *       in="query",
 *       description="Order Refund date range",
 *       required=false,
 *       @OA\Schema(type = "string", example="2025-07-01 23:00:00 - 2025-08-01 23:00:00")
 *   ),
 *   @OA\Parameter(
 *       name="marketplace_seller_ids",
 *       in="query",
 *       description="Pairs of marketplace and seller ids in json format",
 *       required=false,
 *       @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *   ),
 *   @OA\Parameter(
 *       name="seller_id",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplace_id",
 *       in="query",
 *       description="Seller id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currency_id",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="asin",
 *       in="query",
 *       description="Asin",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="ean",
 *       in="query",
 *       description="EAN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="isbn",
 *       in="query",
 *       description="ISBN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="upc",
 *       in="query",
 *       description="UPC",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="catalog_product_name",
 *       in="query",
 *       description="Catalog product name",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="main_image",
 *       in="query",
 *       description="Main image",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="parent_asin",
 *       in="query",
 *       description="Parent ASIN",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="brand",
 *       in="query",
 *       description="Brand",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="model",
 *       in="query",
 *       description="Model",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="product_type",
 *       in="query",
 *       description="Product Type",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="offer_type",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *       description="Offer type"
 *   ),
 *   @OA\Parameter(
 *       name="manufacturer",
 *       in="query",
 *       description="Manufacturer",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="adult_product",
 *       in="query",
 *       description="Adult Product",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="seller_sku",
 *       in="query",
 *       description="Seller SKU (use coma as separator to send several values)",
 *       required=false,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="currency_id",
 *       in="query",
 *       description="Currency code",
 *       required=false,
 *       @OA\Schema(type = "string", example = "EN")
 *   ),
 *   @OA\Parameter(
 *       name="return_reason",
 *       in="query",
 *       description="Reason Refund",
 *       required=false,
 *       @OA\Schema(type = "string", example = "APPAREL_STYLE,PENDING")
 *   ),
 *   @OA\Parameter(
 *       name="tag_id",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *   @OA\Parameter(
 *       name="revenue_amount",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = ">15.5")
 *   ),
 *   @OA\Parameter(
 *       name="expenses_amount",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "15.5")
 *   ),
 *   @OA\Parameter(
 *       name="estimated_profit_amount",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "<15.5")
 *   ),
 *   @OA\Parameter(
 *       name="units",
 *       description="Total count of purchased items",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "10")
 *   ),
 *   @OA\Parameter(
 *       name="promotion_amount",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "15.5")
 *   ),
 *   @OA\Parameter(
 *       name="refunds",
 *       description="Count of the refunded units",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "10")
 *   ),
 *   @OA\Parameter(
 *       name="margin",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "15.5")
 *   ),
 *   @OA\Parameter(
 *       name="roi",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "15.5")
 *   ),
 *   @OA\Parameter(
 *       name="orders",
 *       in="query",
 *       required=false,
 *       @OA\Schema(type = "string", example = "15")
 *   ),
 *   @OA\Response(
 *          response=200,
 *          description="Successful response.",
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  allOf={
 *                      @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                      @OA\Schema(
 *                          type="object",
 *                          @OA\Property(
 *                              property="data",
 *                              type="array",
 *                              @OA\Items(ref="#/components/schemas/ProductAggregatedRefund")
 *                          )
 *                      )
 *                  }
 *              )
 *          )
 *   ),
 *   @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *   @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *   @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *   @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *   @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * ),
 * @OA\Get(path="/v1/product-aggregated-refunds/customer-comments",
 *   summary="Get customer comments for refunded products",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *   @OA\Parameter(
 *       name="seller_sku",
 *       in="query",
 *       description="Seller SKU",
 *       required=true,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="marketplace_id",
 *       in="query",
 *       description="Marketplace ID",
 *       required=true,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="seller_id",
 *       in="query",
 *       description="Seller ID",
 *       required=true,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="refund_date",
 *       in="query",
 *       description="Order Refund date range",
 *       required=false,
 *       @OA\Schema(type = "string", example="2025-07-01 23:00:00 - 2025-08-01 23:00:00")
 *   ),
 *   @OA\Parameter(
 *       name="return_reason",
 *       in="query",
 *       description="Return reason filter",
 *       required=false,
 *       @OA\Schema(type = "string", example="NOT_COMPATIBLE")
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="List of reasons with aggregated customer comments",
 *     @OA\JsonContent(
 *       type="array",
 *       @OA\Items(
 *         type="object",
 *         required={"reason","comments"},
 *         @OA\Property(
 *           property="reason",
 *           type="string",
 *         ),
 *         @OA\Property(
 *           property="comments",
 *           type="array",
 *           description="Comments related to this reason.",
 *           @OA\Items(type="string")
 *         )
 *       ),
 *       example={
 *         {"reason":"APPAREL_STYLE","comments":{"Passt nicht gut"}}
 *       }
 *     )
 *   )
 * )
 */
class ProductAggregatedRefundsController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = ProductAggregatedRefund::class;

    protected bool $isCustomerRelated = false;


    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index', 'customer-comments'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function actions(): array
    {
        $actions = parent::actions();

        unset($actions['create']);
        unset($actions['update']);
        unset($actions['delete']);

        $actions['customer-comments'] = [
            'class' => GetCustomerComments::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    protected function verbs(): array
    {
        $verbs = parent::verbs();

        return $verbs;
    }
}
