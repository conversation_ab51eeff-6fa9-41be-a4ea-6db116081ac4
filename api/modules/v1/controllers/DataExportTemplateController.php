<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\dataExportTemplate\DeleteAction;
use api\modules\v1\controllers\actions\dataExportTemplate\GetFilters;
use api\modules\v1\controllers\actions\dataExportTemplate\GetFiltersGroups;
use common\components\Permissions;
use yii\filters\AccessControl;
use yii\rbac\Permission;

/**
 * DataExportTemplateController implements the REST actions for DataExportTemplate model.
 *
 * @OA\Get(
 *     path="/v1/data-export-template",
 *     summary="Retrieves the collection of DataExportTemplate resources.",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Parameter(
 *         name="title",
 *         in="query",
 *         description="Title",
 *         required=false,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of resource.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                     @OA\Schema(
 *                         type="object",
 *                         @OA\Property(
 *                             property="data",
 *                             type="array",
 *                             @OA\Items(ref="#/components/schemas/DataExportTemplate")
 *                         )
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/data-export-template/{id}",
 *     summary="Retrieves DataExportTemplate resources.",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves DataExportTemplate resources.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportTemplate")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Delete(
 *     path="/v1/data-export-template/{id}",
 *     summary="Deletes DataExportTemplate resources.",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/data-export-template",
 *     summary="Create DataExportTemplate resource",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *         request="DataExportRequest",
 *         required=true,
 *         @OA\JsonContent(
 *             @OA\Property(property="handler_name", type="string", enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost", "orders", "orders_v1"}),
 *             @OA\Property(property="title", type="string", example="My template title"),
 *             @OA\Property(property="format", type="string", example="txt", enum={"txt", "csv"}),
 *             @OA\Property(
 *                 property="criteria",
 *                 type="object",
 *                 ref="#/components/schemas/ExportTemplateCriteria"
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportTemplate resource is created",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportTemplate")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Put(
 *     path="/v1/data-export-template/{id}",
 *     summary="Update DataExportTemplate resource",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             @OA\Property(property="title", type="string", example="My template title"),
 *             @OA\Property(property="format", type="string", example="txt", enum={"txt", "csv"}),
 *             @OA\Property(
 *                 property="criteria",
 *                 type="object",
 *                 ref="#/components/schemas/ExportTemplateCriteria"
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportTemplate resource is updated",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExportTemplate")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/data-export-template/field-filters",
 *     summary="Returns possible fields and their filters (options)",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportTemplate resource is updated",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(
 *                     property="active",
 *                     type="integer",
 *                     description="Indicates if the field is active",
 *                     example=1
 *                 ),
 *                 @OA\Property(
 *                     property="field",
 *                     type="string",
 *                     description="Name of the field",
 *                     example="item_sku"
 *                 ),
 *                 @OA\Property(
 *                     property="options",
 *                     type="object",
 *                     description="Options for the field",
 *                     example={}
 *                 ),
 *                 @OA\Property(
 *                     property="position",
 *                     type="integer",
 *                     description="Position of the field",
 *                     example=100
 *                 ),
 *                 @OA\Property(
 *                     property="title",
 *                     type="string",
 *                     description="Title of the field",
 *                     example="SKU (Stock Keeping Unit)"
 *                 ),
 *                 @OA\Property(
 *                     property="type",
 *                     type="string",
 *                     description="Type of the field",
 *                     example="string",
 *                     enum={"checkboxList", "select", "float", "string"}
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\POST(
 *     path="/v1/data-export-template/field-filters",
 *     summary="Returns possible fields and their filters (options)",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="fields",
 *                 type="array",
 *                 @OA\Items(type="string"),
 *                 example={"order_id", "status", "quantity"}
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportTemplate resource is updated",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(
 *                     property="active",
 *                     type="integer",
 *                     description="Indicates if the field is active",
 *                     example=1
 *                 ),
 *                 @OA\Property(
 *                     property="field",
 *                     type="string",
 *                     description="Name of the field",
 *                     example="item_sku"
 *                 ),
 *                 @OA\Property(
 *                     property="options",
 *                     type="object",
 *                     description="Options for the field",
 *                     example={}
 *                 ),
 *                 @OA\Property(
 *                     property="position",
 *                     type="integer",
 *                     description="Position of the field",
 *                     example=100
 *                 ),
 *                 @OA\Property(
 *                     property="title",
 *                     type="string",
 *                     description="Title of the field",
 *                     example="SKU (Stock Keeping Unit)"
 *                 ),
 *                 @OA\Property(
 *                     property="type",
 *                     type="string",
 *                     description="Type of the field",
 *                     example="string",
 *                     enum={"checkboxList", "select", "float", "string"}
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/data-export-template/export-field-group",
 *     summary="Returns possible fields and their filters (options)",
 *     tags={"DataExportTemplate"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Response(
 *         response=200,
 *         description="DataExportTemplate resource is updated",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(
 *                     property="active",
 *                     type="integer",
 *                     description="Indicates if the field is active",
 *                     example=1
 *                 ),
 *                 @OA\Property(
 *                     property="field",
 *                     type="string",
 *                     description="Name of the field",
 *                     example="item_sku"
 *                 ),
 *                 @OA\Property(
 *                     property="position",
 *                     type="integer",
 *                     description="Position of the field",
 *                     example=100
 *                 ),
 *                 @OA\Property(
 *                     property="title",
 *                     type="string",
 *                     description="Title of the field",
 *                     example="SKU (Stock Keeping Unit)"
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */
class DataExportTemplateController extends Controller
{
    public $modelClass = 'common\models\customer\DataExportTemplate';
    public $updateScenario = 'update';
    public $createScenario = 'create';

    protected bool $isCustomerRelated =  true;
    protected $handlerName = null;

    public function __construct($id, $module, $config = [])
    {
        $requestParams = array_merge(\Yii::$app->request->get(), \Yii::$app->request->post());
        $this->handlerName = $requestParams['handler_name'] ?? null;
        parent::__construct($id, $module, $config);
    }

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [
                        Permissions::PRODUCT_EXPORT_TEMPLATE_LIST,
                        Permissions::PRODUCT_COST_EXPORT_LIST,
                        Permissions::PRODUCT_COST_AUTO_EXPORT_VIEW
                    ],
                ],
                [
                    'allow' => true,
                    'actions' => ['field-filters'],
                    'verbs' => ['GET', 'POST'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_LIST],
                ],
                [
                    'allow' => true,
                    'actions' => ['export-field-group'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_LIST],
                ],
                [
                    'allow' => true,
                    'actions' => ['view'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_DELETE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::PRODUCT_EXPORT_TEMPLATE_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['field-filters'] = [
            'class' => GetFilters::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['export-field-group'] = [
            'class' => GetFiltersGroups::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
