<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\amazonOrder\GetAmazonFeesBreakdown;
use api\modules\v1\controllers\actions\amazonOrder\GetAmountCost;
use api\modules\v1\controllers\actions\amazonOrder\GetDetails;
use api\modules\v1\controllers\actions\amazonOrder\GetExpensesBreakdown;
use api\modules\v1\controllers\actions\amazonOrder\GetLastUpdateDate;
use api\modules\v1\controllers\actions\amazonOrder\GetStatusesAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use api\modules\v1\controllers\actions\amazonOrder\UpdateAmountCost;
use common\components\Permissions;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use api\components\controllers\Controller;
use yii\filters\AccessControl;

/**
 * @OA\Get(
 *     path="/v1/amazon-order/statuses",
 *     summary="Retrieves available amazon order statuses",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Response(
 *         response=200,
 *         description="List of order statuses",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="Pending",
 *                 type="string",
 *                 example="Pending"
 *             ),
 *             @OA\Property(
 *                 property="Unshipped",
 *                 type="string",
 *                 example="Unshipped"
 *             ),
 *             @OA\Property(
 *                 property="PartiallyShipped",
 *                 type="string",
 *                 example="PartiallyShipped"
 *             ),
 *             @OA\Property(
 *                 property="Shipped",
 *                 type="string",
 *                 example="Shipped"
 *             ),
 *             @OA\Property(
 *                 property="Canceled",
 *                 type="string",
 *                 example="Canceled"
 *             ),
 *             @OA\Property(
 *                 property="Unfulfillable",
 *                 type="string",
 *                 example="Unfulfillable"
 *             ),
 *             @OA\Property(
 *                 property="InvoiceUnconfirmed",
 *                 type="string",
 *                 example="InvoiceUnconfirmed"
 *             ),
 *             @OA\Property(
 *                 property="PendingAvailability",
 *                 type="string",
 *                 example="PendingAvailability"
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order/last-update-date",
 *     summary="Returns last update date",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the updated timestamp",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="updatedAt",
 *                 type="string",
 *                 format="date-time",
 *                 example="2024-06-13 10:34:20"
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order/amazon-fees-breakdown",
 *     summary="Returns data needed for amazon fees modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderItemId",
 *         in="query",
 *         description="Amazon order item id",
 *         required=true,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order/expenses-breakdown",
 *     summary="Returns data needed for expenses modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderItemId",
 *         in="query",
 *         description="Amazon order item id",
 *         required=true,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the breakdown of order fees",
 *         @OA\JsonContent(ref="#/components/schemas/ProfitBreakdownResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order/details",
 *     summary="Data needed for order details modal",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="amazonOrderId",
 *         in="query",
 *         description="Amazon order id",
 *         required=true,
 *         example="303-6829218-2536344",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="currencyId",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         example="EUR",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Response containing the details of the order",
 *         @OA\JsonContent(ref="#/components/schemas/OrderDetailsResponse")
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/amazon-order",
 *     summary="Retrieves the collection of AmazonOrder resources.",
 *     tags={"AmazonOrder"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(
 *         name="order_item_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Order item identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Order identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_status",
 *         in="query",
 *         description="Order status (multisearch)",
 *         required=false,
 *         @OA\Schema(
 *            type="string",
 *            enum={"Shipped", "Canceled", "Pending", "Unshipped", "PartiallyShipped", "Unfulfillable", "InvoiceUnconfirmed", "PendingAvailability"}
 *         )
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Seller identifier"
 *     ),
 *     @OA\Parameter(
 *         name="seller_sku",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Seller SKU"
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Marketplace identifier"
 *     ),
 *     @OA\Parameter(
 *         name="item_price",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Item price"
 *     ),
 *     @OA\Parameter(
 *         name="quantity",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Quantity"
 *     ),
 *     @OA\Parameter(
 *         name="quantity_refunded",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Quantity refunded"
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *         description="Offer type"
 *     ),
 *     @OA\Parameter(
 *         name="amazon_fees_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Amazon fees amount"
 *     ),
 *     @OA\Parameter(
 *         name="expenses_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Expenses amount"
 *     ),
 *     @OA\Parameter(
 *         name="revenue_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Revenue amount"
 *     ),
 *     @OA\Parameter(
 *         name="estimated_profit_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Estimated profit amount"
 *     ),
 *     @OA\Parameter(
 *         name="promotion_amount",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="number", format="float"),
 *         description="Promotion amount"
 *     ),
 *     @OA\Parameter(
 *         name="product_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Product identifier"
 *     ),
 *     @OA\Parameter(
 *         name="product_asin",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product ASIN"
 *     ),
 *     @OA\Parameter(
 *         name="product_title",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product title"
 *     ),
 *      @OA\Parameter(
 *          name="product_ean",
 *          in="query",
 *          description="Product EAN",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_upc",
 *          in="query",
 *          description="Product UPC",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_isbn",
 *          in="query",
 *          description="Product ISBN",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_brand",
 *          in="query",
 *          description="Product Brand",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_type",
 *          in="query",
 *          description="Product Type",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_manufacturer",
 *          in="query",
 *          description="Product Manufacturer",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="product_parent_asin",
 *          in="query",
 *          description="Parent Asin",
 *          required=false,
 *          @OA\Schema(type="string"),
 *      ),
 *      @OA\Parameter(
 *          name="adult_product",
 *          in="query",
 *          description="Product Adult",
 *          required=false,
 *          @OA\Schema(type="integer", enum={1,0})
 *      ),
 *     @OA\Parameter(
 *         name="product_stock_type",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product stock type"
 *     ),
 *     @OA\Parameter(
 *         name="product_condition",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Product condition"
 *     ),
 *     @OA\Parameter(
 *         name="currency_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Currency identifier"
 *     ),
 *     @OA\Parameter(
 *         name="order_purchase_date",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Order purchase date"
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of resource.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                     @OA\Schema(
 *                         type="object",
 *                         @OA\Property(
 *                             property="data",
 *                             type="array",
 *                             @OA\Items(ref="#/components/schemas/AmazonOrderExtendedViewItem")
 *                         )
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * ),
 * @OA\Get(path="/v1/amazon-order/amount-cost",
 *   summary="Get amount cost amazon order items",
 *   tags={"AmazonOrder"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=true,
 *       @OA\Schema(type = "integer", format = "int64")
 *   ),
 *   @OA\Parameter(
 *       name="amazonOrderId",
 *       in="query",
 *       description="Amazon order id",
 *       required=true,
 *       @OA\Schema(type = "string")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Sales Category id",
 *       required=false,
 *       @OA\Schema(type = "string", example = "shipping_costs")
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Retrieves available amazon order statuses",
 *     @OA\JsonContent(
 *         type="object",
 *         @OA\Property(
 *             property="currency_id",
 *             type="string",
 *             example="EUR"
 *         ),
 *           @OA\Property(
 *              property="items",
 *              type="array",
 *              @OA\Items(
 *                  type="object",
 *                      @OA\Property(
 *                          property="amount",
 *                          type="number",
 *                          format="float",
 *                          example=0.50
 *                      ),
 *                      @OA\Property(
 *                          property="product_title",
 *                          type="string",
 *                          example="Palado Damen Pantoletten"
 *                      ),
 *                      @OA\Property(
 *                          property="product_asin",
 *                          type="string",
 *                          example="B08VN2"
 *                      ),
 *                      @OA\Property(
 *                          property="marketplace_id",
 *                          type="string",
 *                          example="A1PA6795U"
 *                      ),
 *                      @OA\Property(
 *                          property="seller_id",
 *                          type="string",
 *                          example="A2WY0S4JX2"
 *                      ),
 *                      @OA\Property(
 *                          property="seller_sku",
 *                          type="string",
 *                          example="fba1670"
 *                      ),
 *                      @OA\Property(
 *                          property="order_item_id",
 *                          type="string",
 *                          example="416823224"
 *                      )
 *                 )
 *            )
 *        )
 * ),
 *   @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *   ),
 *   @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *   ),
 *   @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *   )
 * ),
 * @OA\Post(
 *   path="/v1/amazon-order/update-amount-cost",
 *   summary="Edit FBM shipping cost for order item",
 *   tags={"AmazonOrder"},
 *   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer Id - required to admin user",
 *       required=false,
 *       @OA\Schema(
 *         type="string",
 *         default="1"
 *       )
 *   ),
 *   @OA\Parameter(
 *       name="amazonOrderId",
 *       in="query",
 *       description="Amazon order id",
 *       required=true,
 *       @OA\Schema(type="string")
 *   ),
 *   @OA\Parameter(
 *       name="salesCategoryId",
 *       in="query",
 *       description="Sales Category id",
 *       required=false,
 *       @OA\Schema(type="string", example="shipping_costs")
 *   ),
 *   @OA\RequestBody(
 *       @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="currency_id",
 *                 type="string",
 *                 example="EUR"
 *             ),
 *             @OA\Property(
 *                 property="items",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="amount",
 *                         type="number",
 *                         format="float",
 *                         example=10.8
 *                     ),
 *                     @OA\Property(
 *                         property="product_title",
 *                         type="string",
 *                         example="Product title"
 *                     ),
 *                     @OA\Property(
 *                         property="product_asin",
 *                         type="string",
 *                         example="GDFDGTERT"
 *                     ),
 *                     @OA\Property(
 *                         property="marketplace_id",
 *                         type="string",
 *                         example="KFDFDT3434"
 *                     ),
 *                     @OA\Property(
 *                         property="seller_id",
 *                         type="string",
 *                         example="FDF5545FD55"
 *                     ),
 *                     @OA\Property(
 *                         property="seller_sku",
 *                         type="string",
 *                         example="KA08FBM"
 *                     ),
 *                     @OA\Property(
 *                         property="order_item_id",
 *                         type="string",
 *                         example="REPORT_656602e2d7c0f"
 *                     )
 *                 )
 *             )
 *       )
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Operation successful.",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(
 *             type="object",
 *             @OA\Property(
 *                 property="result",
 *                 type="string",
 *                 example="successful"
 *             )
 *         )
 *     )
 *   ),
 *   @OA\Response(
 *     response=400,
 *     description="Bad Request"
 *   ),
 *   @OA\Response(
 *     response=401,
 *     description="Invalid token supplied"
 *   ),
 *   @OA\Response(
 *     response=405,
 *     description="Method Not Allowed"
 *   )
 * )
 */
class AmazonOrderController extends Controller
{
    public $modelClass = AmazonOrderExtendedView::class;
    protected bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => [
                        'index',
                        'statuses',
                        'last-update-date',
                        'amazon-fees-breakdown',
                        'expenses-breakdown',
                        'details',
                        'amount-cost'
                    ],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::ORDER_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update-amount-cost'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::ORDER_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        unset($actions['create']);
        unset($actions['update']);
        unset($actions['delete']);

        $actions['statuses'] = [
            'class' => GetStatusesAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['last-update-date'] = [
            'class' => GetLastUpdateDate::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['amazon-fees-breakdown'] = [
            'class' => GetAmazonFeesBreakdown::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['expenses-breakdown'] = [
            'class' => GetExpensesBreakdown::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['update-amount-cost'] = [
            'class' => UpdateAmountCost::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['amount-cost'] = [
            'class' => GetAmountCost::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['details'] = [
            'class' => GetDetails::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
