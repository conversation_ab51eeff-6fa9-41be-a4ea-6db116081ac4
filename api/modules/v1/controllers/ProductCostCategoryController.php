<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\productCostCategory\DeleteAction;
use common\components\Permissions;
use Yii;
use api\components\controllers\Controller;
use yii\filters\AccessControl;


/**
 * ProductCostCategoryController implements the REST actions for ProductCostCategory model.
* @OA\Get(path="/v1/product-cost-category",
*   summary="Retrieves the collection of ProductCostCategory resources.",
*   tags={"ProductCostCategory"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="query",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="name",
*         in="query",
*         description="Name",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="source",
*         in="query",
*         description="Source",
*         required=false,
*         @OA\Schema(
*           type="string",
*           enum={"manual", "repricer"}
*         ),
*     ),
 *     @OA\Parameter(
 *         name="sales_category_id",
 *         in="query",
 *         description="Sales Category Id",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           enum={"cost_of_goods", "shipping_costs", "other_fees"}
 *         ),
 *     ),
*     @OA\Parameter(
*         name="created_at",
*         in="query",
*         description="Created At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="updated_at",
*         in="query",
*         description="Updated At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of ProductCostCategory resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostCategory"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Get(path="/v1/product-cost-category/{id}",
*   summary="View the ProductCostCategory resource",
*   tags={"ProductCostCategory"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="path",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*   @OA\Response(
*     response=200,
*     description="View the ProductCostCategory resource",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostCategory"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="/v1/product-cost-category",
*   summary="Create ProductCostCategory resource",
*   tags={"ProductCostCategory"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="query",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
        *	 	            @OA\Property(property="name", type="string"),
    *	 	            @OA\Property(property="source", type="string"),
    *	 	            @OA\Property(property="sales_category_id", type="string"),
    *	 	            @OA\Property(property="created_at", type="string"),
    *	 	            @OA\Property(property="updated_at", type="string"),
*          )
*        ),
*
*
*   @OA\Response(
*     response=200,
*     description="ProductCostCategory resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostCategory"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="/v1/product-cost-category",
*   summary="Update the ProductCostCategory resource",
*   tags={"ProductCostCategory"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="query",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
		*	 	       @OA\Property(property="name", type="string"),
	*	 	       @OA\Property(property="source", type="string"),
	*	 	       @OA\Property(property="sales_category_id", type="string"),
	*	 	       @OA\Property(property="created_at", type="string"),
	*	 	       @OA\Property(property="updated_at", type="string"),
*          )
*        ),
*
*   @OA\Response(
*     response=200,
*     description="ProductCostCategory resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostCategory"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="/v1/product-cost-category/{id}",
*   summary="Delete ProductCostCategory resource",
*   tags={"ProductCostCategory"},
*   security={{"oauth2":{}}},
 *            @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
* )
*/
class ProductCostCategoryController extends Controller
{
    public $updateScenario = 'update';
    public $createScenario = 'create';

    protected bool $isCustomerRelated =  true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [                [
                    'allow' => true,
                    'actions' => ['index', 'view'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    public $modelClass = 'common\models\customer\ProductCostCategory';
}
