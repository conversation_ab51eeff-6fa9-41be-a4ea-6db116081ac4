<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\dataImportExport\ExportAction;
use common\components\dataImportExport\SupportedHandlers;
use common\components\Permissions;
use yii\filters\AccessControl;
use yii\web\UnauthorizedHttpException;

/**
 * @OA\Get(
 *     path="/v1/data-export",
 *     summary="Retrieves the collection of DataExport resources.",
 *     tags={"DataExport"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Parameter(ref="#/components/parameters/importExportStatus"),
 *     @OA\Parameter(
 *         name="output_format",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", enum={"csv", "txt"}),
 *         description="Output format"
 *     ),
 *     @OA\Parameter(
 *         name="count_parts",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Count parts"
 *     ),
 *     @OA\Parameter(
 *         name="count_all_items",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Count all items"
 *     ),
 *     @OA\Parameter(
 *         name="count_exported_items",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Count exported items"
 *     ),
 *     @OA\Parameter(
 *         name="count_errors",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Count errors"
 *     ),
 *     @OA\Parameter(
 *         name="file_url",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="File URL"
 *     ),
 *     @OA\Parameter(
 *         name="template_id",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *         description="Template ID"
 *     ),
 *     @OA\Parameter(
 *         name="exception",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string"),
 *         description="Exception"
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Created at"
 *     ),
 *     @OA\Parameter(
 *         name="started_at",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Started at"
 *     ),
 *     @OA\Parameter(
 *         name="finished_at",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Finished at"
 *     ),
 *     @OA\Parameter(
 *         name="updated_at",
 *         in="query",
 *         required=false,
 *         @OA\Schema(type="string", format="date-time"),
 *         description="Updated at"
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of resource.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                     @OA\Schema(
 *                         type="object",
 *                         @OA\Property(
 *                             property="data",
 *                             type="array",
 *                             @OA\Items(ref="#/components/schemas/DataExport")
 *                         )
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/data-export/{id}",
 *     summary="View the DataExport resource",
 *     tags={"DataExport"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="id",
 *         in="path",
 *         required=true,
 *         @OA\Schema(type="integer"),
 *         description="Resource Id"
 *      ),
 *     @OA\Response(
 *         response=200,
 *         description="View the DataExport resource",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExport")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/data-export/export",
 *     summary="Exports data",
 *     tags={"DataExport"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             required={"handler_name"},
 *             type="object",
 *             @OA\Property(
 *                 property="handler_name",
 *                 type="string",
 *                 description="Handler name",
 *                 enum={"product_cost_periods", "orders", "orders_v1"}
 *             ),
 *             @OA\Property(
 *                 property="template_id",
 *                 type="integer",
 *                 description="Template id",
 *                 default=1
 *             ),
 *             @OA\Property(
 *                 property="output_file_format",
 *                 type="string",
 *                 description="Output file format",
 *                 enum={"csv", "txt"}
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="View the DataExport resource",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataExport")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Delete(
 *     path="/v1/data-export/{id}",
 *     summary="Delete the DataExport resource",
 *     tags={"DataExport"},
 *     security={{"oauth2":{}}},
 *        @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(
 *            name="id",
 *            in="path",
 *            required=true,
 *            @OA\Schema(type="integer"),
 *            description="Resource Id"
 *        ),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */
class DataExportController extends Controller
{
    public $modelClass = 'common\models\customer\DataExport';

    protected bool $isCustomerRelated =  true;
    protected $handlerName = null;

    public function __construct($id, $module, $config = [])
    {
        $requestParams = array_merge(\Yii::$app->request->get(), \Yii::$app->request->post());
        $this->handlerName = $requestParams['handler_name'] ?? null;
        parent::__construct($id, $module, $config);
    }

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        if ($this->handlerName === SupportedHandlers::HANDLER_PRODUCT_COST_PERIODS ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_FBM_COST ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_ITEM_FBM_COST
            ) {
            $behaviors['access'] = [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view'],
                        'verbs' => ['GET'],
                        'roles' => [Permissions::PRODUCT_COST_EXPORT_LIST],
                    ],
                    [
                        'allow' => true,
                        'actions' => ['delete'],
                        'verbs' => ['DELETE'],
                        'roles' => [Permissions::PRODUCT_COST_EXPORT_CREATE],
                    ],
                    [
                        'allow' => true,
                        'actions' => ['export'],
                        'verbs' => ['POST'],
                        'roles' => [Permissions::PRODUCT_COST_EXPORT_CREATE],
                    ],
                    [
                        'allow' => true,
                        'verbs' => ['OPTIONS'],
                    ],
                ],
            ];
        }

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        unset(
            $actions['update'],
            $actions['create']
        );

        $actions['export'] = [
            'class' => ExportAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
