<?php

namespace api\modules\v1\controllers\actions\seller;

use common\components\customerConfig\CustomerConfig;
use common\models\Seller;
use yii\rest\Action;

class GlobalMarketplaceSettingsAction extends Action
{

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $customerId = \Yii::$app->dbManager->getCustomerId();
        $customerConfig = \Yii::$container->get('customerConfig');

        $sellers = Seller::find()
            ->select([
                'id as seller_id',
            ])
            ->where(['customer_id' => $customerId])
            ->indexBy('seller_id')
            ->asArray()
            ->all();

        $globalMarketplaceSettings = [];

        foreach ($sellers as $seller) {
            $sellerId = $seller['seller_id'];
            $globalMarketplaceIdSetting = $customerConfig->getAsObject(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
                null,
                $sellerId
            );
            $isEnabledCostOfGoodsSyncSetting = $customerConfig->getAsObject(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
                false,
                $sellerId
            );
            $isEnabledOtherFeesSyncSetting = $customerConfig->getAsObject(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
                false,
                $sellerId
            );
            $isEnableFBMShippingCostSyncSetting = $customerConfig->getAsObject(
                CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
                false,
                $sellerId
            );

            $globalMarketplaceId = $globalMarketplaceIdSetting->value ?? null;
            $isEnabledCostOfGoodsSync = (bool)($isEnabledCostOfGoodsSyncSetting->value ?? null);
            $isEnabledOtherFeesSync = (bool)($isEnabledOtherFeesSyncSetting->value ?? null);
            $isEnabledFBMShippingCostSync = (bool)($isEnableFBMShippingCostSyncSetting->value ?? null);

            $globalMarketplaceSettings[$sellerId] = [
                'seller_id' => $sellerId,
                'global_marketplace_id' => $globalMarketplaceId,
                'is_enabled_cost_of_goods_sync' => $isEnabledCostOfGoodsSync,
                'is_enabled_other_fees_sync' => $isEnabledOtherFeesSync,
                'is_enabled_fbm_shipping_cost_sync' => $isEnabledFBMShippingCostSync
            ];
        }

        return $globalMarketplaceSettings;
    }
}
