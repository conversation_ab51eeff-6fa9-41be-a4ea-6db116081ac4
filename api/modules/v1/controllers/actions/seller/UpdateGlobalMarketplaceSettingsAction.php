<?php

namespace api\modules\v1\controllers\actions\seller;

use api\modules\v1\forms\seller\GlobalMarketplaceSettingsForm;
use common\components\customerConfig\CustomerConfig;
use common\models\Seller;
use yii\rest\Action;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class UpdateGlobalMarketplaceSettingsAction extends Action
{
    public function run($sellerId)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var Seller $seller */
        $seller = Seller::find()
            ->where([
                'id' => $sellerId,
                'customer_id' => \Yii::$app->dbManager->getCustomerId()
            ])
            ->limit(1)
            ->one(\Yii::$app->db);

        if (!$seller) {
            throw new NotFoundHttpException("Seller with ID {$sellerId} not found");
        }

        $form = new GlobalMarketplaceSettingsForm();
        $form->load(\Yii::$app->request->getBodyParams(), '');

        if (!$form->validate()) {
            return $form;
        }

        $customerConfig = \Yii::$container->get('customerConfig');

        $customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            $form->global_marketplace_id,
            $seller->id
        );

        $customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            (bool)$form->is_enabled_cost_of_goods_sync,
            $seller->id
        );

        $customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            (bool)$form->is_enabled_other_fees_sync,
            $seller->id
        );

        $customerConfig->set(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            (bool)$form->is_enabled_fbm_shipping_cost_sync,
            $seller->id
        );

        $globalMarketplaceIdSetting = $customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_ID,
            null,
            $seller->id
        );
        $isEnabledCostOfGoodsSyncSetting = $customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_COST_OF_GOODS_SYNC,
            false,
            $seller->id
        );
        $isEnabledOtherFeesSyncSetting = $customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_OTHER_FEES_SYNC,
            false,
            $seller->id
        );
        $isEnableFBMShippingCostSyncSetting = $customerConfig->getAsObject(
            CustomerConfig::PARAMETER_PRODUCT_COSTS_GLOBAL_MARKETPLACE_IS_ENABLED_FBA_SHIPPING_COST_SYNC,
            false,
            $seller->id
        );

        $globalMarketplaceId = $globalMarketplaceIdSetting->value ?? null;
        $isEnabledCostOfGoodsSync = (bool)($isEnabledCostOfGoodsSyncSetting->value ?? null);
        $isEnabledOtherFeesSync = (bool)($isEnabledOtherFeesSyncSetting->value ?? null);
        $isEnabledFBMShippingCostSync = (bool)($isEnableFBMShippingCostSyncSetting->value ?? null);

        return [
            'seller_id' => $seller->id,
            'global_marketplace_id' => $globalMarketplaceId,
            'is_enabled_cost_of_goods_sync' => $isEnabledCostOfGoodsSync,
            'is_enabled_other_fees_sync' => $isEnabledOtherFeesSync,
            'is_enabled_fbm_shipping_cost_sync' => $isEnabledFBMShippingCostSync
        ];
    }
}
