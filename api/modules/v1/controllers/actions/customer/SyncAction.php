<?php

namespace api\modules\v1\controllers\actions\customer;

use api\modules\v1\components\seller\SellerSynchronizer;
use common\components\dataCompleteness\Checker;
use common\components\dataCompleteness\factor\FactorFactory;
use common\models\ads\AmazonAdsAccount;
use common\models\Command;
use common\models\Customer;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\Seller;
use yii\rest\Action;

class SyncAction extends Action
{
    public function run(int $customerId)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $postData = \Yii::$app->request->post();
        $repricerToBASync = $postData['repricerToBASync'] ?? null;
        $adsAccountsData = $postData['adsAccounts'] ?? [];
        $sellersData = $postData['sellers'] ?? [];

        \Yii::$app->dbManager->setCustomerId($customerId);

         try {
            $this->syncSellers($sellersData, $customerId);

             $customer = Customer::find()->where(['id' => $customerId])->noCache()->one();

            if ($customer !== null) {
                $isSync = false;

                if ($repricerToBASync !== null && $customer->is_repricer_sync !== (bool)$repricerToBASync) {
                    $customer->is_repricer_sync = (bool)$repricerToBASync;
                    $customer->save();
                    $isSync = true;
                }

                if ($isSync) {
                    Command::create(sprintf(
                        "product-cost/sync %d %d",
                        $customerId,
                        $customerId + 1
                    ));
                }
            }

            $seller = Seller::find()->where(['customer_id' => $customerId])->noCache()->one(\Yii::$app->db);

            // Checking if at least one seller left after prev step sync
            if (!empty($seller)) {
                $this->syncAdsAccounts($adsAccountsData);
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return [
            'result' => 'successful'
        ];
    }

    protected function syncSellers(array $sellersData, int $customerId): void
    {
        $service = new SellerSynchronizer($customerId);
        $service->loadModels($sellersData);

        if (!$service->validate()) {
            throw new \Exception('Invalid sellers data format');
        }

        $service->syncAll();
    }

    protected function syncAdsAccounts(array $adsAccountsData): void
    {
        $isNewAdded = false;
        $existingIds = [];
        foreach ($adsAccountsData as $adsAccountsDatum) {
            try {
                $isActive = $adsAccountsDatum['is_active'] ?? true;
                $isDeleted = $adsAccountsDatum['is_deleted'] ?? false;

                $amazonAdAccount = AmazonAdsAccount::findOne(['id' => $adsAccountsDatum['id']]);
                if (empty($amazonAdAccount)) {
                    if ($isDeleted) {
                        continue;
                    }
                    $amazonAdAccount = new AmazonAdsAccount();
                    $amazonAdAccount->id = $adsAccountsDatum['id'];
                    $isNewAdded = true;
                }

                if ($isDeleted) {
                    $amazonAdAccount->delete();
                    continue;
                }

                $amazonAdAccount->is_active = $isActive;
                $amazonAdAccount->saveOrThrowException();
                $existingIds[] = $amazonAdAccount->id;
            } catch (\Throwable $e) {
                \Yii::error($e);
            }
        }

        AmazonAdsAccount::deleteAll([
            'and',
            ['not in', 'id', $existingIds],
        ]);

        if ($isNewAdded) {
            $customerId = \Yii::$app->dbManager->getCustomerId();
            \Yii::$app->cronComponent->addCommand(
                sprintf("amazon-ads/sync-profiles %d %d", $customerId, $customerId + 1),
            );
        }
        (new Checker())->check(FactorFactory::FACTOR_PPC_NOT_CONNECTED);
    }
}
