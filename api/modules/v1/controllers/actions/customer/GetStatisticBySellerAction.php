<?php

namespace api\modules\v1\controllers\actions\customer;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use common\components\salesMetricCalculator\UnitsCalculator;
use yii\rest\Action;

class GetStatisticBySellerAction extends Action
{
    public function run(int $customerId, string $sellerId, string $dateStart, string $dateEnd)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        try {
            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;
            $dbManager->setCustomerId($customerId);

            $filtersForm = new FiltersForm();
            $filtersForm->sellerId = $sellerId;
            $filtersForm->dateStart = $dateStart;
            $filtersForm->dateEnd = $dateEnd;
            if (!$filtersForm->validate()) {
                return $filtersForm;
            }
            $unitsCalculator = new UnitsCalculator($filtersForm);
            $units = $unitsCalculator->calc();

            return [
                'orders' => $units->orders
            ];
        } catch (\Throwable $e) {
            \Yii::error($e);
            return [
                'orders' => 0
            ];
        }
    }
}
