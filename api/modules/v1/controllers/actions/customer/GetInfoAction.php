<?php

namespace api\modules\v1\controllers\actions\customer;

use common\models\customer\Product;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;

class GetInfoAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $result = [
            'has_manual_costs' => false
        ];

        $manualCost = ProductCostPeriod::find()->where([
            'source' => Product::SOURCE_MANUAL
        ])->limit(1)->one();

        if (!empty($manualCost)) {
            $result['has_manual_costs'] = true;
        }

        return $result;
    }
}
