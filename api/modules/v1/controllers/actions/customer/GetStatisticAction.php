<?php

namespace api\modules\v1\controllers\actions\customer;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\dto\UnitsResult;
use common\components\salesMetricCalculator\UnitsCalculator;
use common\models\finance\EventPeriod;
use yii\rest\Action;

class GetStatisticAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $info = new UnitsResult();

        try {
            $filtersForm = new FiltersForm();
            $filtersForm->load(\Yii::$app->request->getQueryParams(), '');

            if (!$filtersForm->validate()) {
                return $filtersForm;
            }
            $info = (new UnitsCalculator($filtersForm))->calc();
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        $info = (array)($info);
        $info['updatedAt'] = EventPeriod::getLastMovedToClickhouseDate();

        return $info;
    }
}
