<?php

namespace api\modules\v1\controllers\actions\customerDashboard;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\DataSanitizer;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use yii\rest\Action;

class GetKeyPerformance extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $cachedData = $this->getFromCache();
        if (false !== $cachedData) {
            return $cachedData;
        }

        DataSanitizer::$isEnabled = false;
        $params = \Yii::$app->request->getQueryParams();
        $params['date_start'] = $params['date_start'] ?? date('Y-m-d', strtotime('-14 days'));
        $params['date_end'] = $params['date_end'] ?? date('Y-m-d', strtotime('-1 day'));

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $profitCalculator = new ProfitCalculator($filtersForm);
        $profitResult = $profitCalculator->calc(DataSeriesStructureManager::PERIOD_TYPE_YEAR);

        $prevPeriodFiltersForm = $this->getPreviousPeriodFiltersForm($filtersForm);
        $prevPeriodProfitResult = (new ProfitCalculator($prevPeriodFiltersForm))->calc(DataSeriesStructureManager::PERIOD_TYPE_YEAR);

        $avgSales = $profitResult->orders > 0
            ? $profitResult->orderedProductSalesAmount / $profitResult->orders
            : 0;
        $avgSalesPrev = $prevPeriodProfitResult->orders > 0
            ? $prevPeriodProfitResult->orderedProductSalesAmount / $prevPeriodProfitResult->orders
            : 0;

        $response = [
            'orders' => [
                'count_orders_dyn' => $this->compareDiffPercents($profitResult->orders, $prevPeriodProfitResult->orders),
                'count_orders' => $profitResult->orders,
            ],
            'selling_price' => [
                'avg_sales_dyn' => $this->compareDiffPercents($avgSales, $avgSalesPrev),
                'avg_sales' => round($avgSales, 2),
            ],
            'profit' => [
                'sum_profit_dyn' => $this->compareDiffPercents($profitResult->netProfit, $prevPeriodProfitResult->netProfit),
                'sum_profit' => $profitResult->netProfit,
            ],
            "turnover" => [
                "sum_sales_dyn" => $this->compareDiffPercents($profitResult->revenueAmount, $prevPeriodProfitResult->revenueAmount),
                "sum_sales" => $profitResult->revenueAmount
            ],
        ];

        $this->saveToCache($response, 60 * 15);

        return $response;
    }

    protected function getPreviousPeriodFiltersForm(FiltersForm $currPeriodFiltersForm): FiltersForm
    {
        $filtersForm = clone $currPeriodFiltersForm;
        $currStartDate = new \DateTime($currPeriodFiltersForm->dateStart);
        $currEndDate = new \DateTime($currPeriodFiltersForm->dateEnd);

        $diffDays = $currStartDate->diff($currEndDate)->days;
        $diffDays += 1;

        $filtersForm->dateEnd = $currEndDate->modify("-$diffDays days")->format('Y-m-d');
        $filtersForm->dateStart = $currStartDate->modify("-$diffDays days")->format('Y-m-d');
        $filtersForm->validate();

        return $filtersForm;
    }

    protected function compareDiffPercents($currentValue, $prevValue): float
    {
        if ($prevValue == 0 && $currentValue == 0) {
            return 0;
        }

        if ($prevValue == 0) {
            return ($currentValue > 0) ? 100 : -100;
        }

        $percents = (($currentValue - $prevValue) / $prevValue) * 100;

        if ($currentValue >= 0 && $prevValue < 0 || $prevValue < 0 && $currentValue >= 0) {
            $percents *= -1;
        }

        return round($percents, 2);
    }
}
