<?php

namespace api\modules\v1\controllers\actions\customerDashboard;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use api\modules\v1\forms\widget\FiltersForm;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Query;
use yii\rest\Action;

class GetOrdersMarketplaces extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $cachedData = $this->getFromCache();
        if (false !== $cachedData) {
            return $cachedData;
        }

        $params = \Yii::$app->request->getQueryParams();
        $params['date_start'] = $params['date_start'] ?? date('Y-m-d', strtotime('-14 days'));
        $params['date_end'] = $params['date_end'] ?? date('Y-m-d', strtotime('-1 day'));

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $query1 = (new AmazonOrder())
            ->search($params)
            ->select([
                "order_marketplace_id",
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED]
            ]);
        $query2 = (new AmazonOrderInProgress())
            ->search($params)
            ->select([
                "order_marketplace_id",
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED],
                [
                    '=',
                    'version',
                    AmazonOrderInProgress::find()->select('max(version)')
                ]
            ]);

        $response = (new Query())
            ->select([
                'count(t1.order_marketplace_id) as value',
                "dictGetOrNull(
                    default.amazon_marketplace_dict, 
                    'title', 
                    t1.order_marketplace_id
                ) as label",
            ])
            ->from(['t1' => $query1->union($query2, true)])
            ->groupBy(['t1.order_marketplace_id'])
            ->orderBy([
                'value' => SORT_DESC,
            ])
            ->all(AmazonOrder::getDb())
        ;

        foreach ($response as $key => $value) {
            $response[$key]['value'] = (int) $value['value'];
        }
        $this->saveToCache($response, 60 * 15);

        return $response;
    }
}