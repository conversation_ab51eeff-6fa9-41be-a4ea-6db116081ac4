<?php

namespace api\modules\v1\controllers\actions\customerDashboard;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use api\modules\v1\forms\widget\FiltersForm;
use common\components\CustomerComponent;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Query;
use yii\rest\Action;

class GetOrderHistory extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $cachedData = $this->getFromCache();
        if (false !== $cachedData) {
            return $cachedData;
        }

        $params = \Yii::$app->request->getQueryParams();
        $params['date_start'] = $params['date_start'] ?? date('Y-m-d', strtotime('-14 days'));
        $params['date_end'] = $params['date_end'] ?? date('Y-m-d', strtotime('-1 days'));

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $customerComponent = new CustomerComponent();
        $customerTimezone = new \DateTimeZone($customerComponent->getUserTimezoneName());
        $rawData = $this->getRawData($filtersForm, $params);

        $labels = $this->getLabels($rawData);
        $periods = $this->getPeriods(
            (new \DateTime($filtersForm->dateStart))->setTimezone($customerTimezone),
            (new \DateTime($filtersForm->dateEnd))->setTimezone($customerTimezone)
        );

        $response = [
            'labels' => $this->getLabels($rawData),
            'period' => $periods,
            'from' => date('Y-m-d', strtotime($filtersForm->dateStart)),
            'to' => date('Y-m-d', strtotime($filtersForm->dateEnd)),
            'data' => $this->getData($rawData, $labels, $periods)
        ];

        $this->saveToCache($response, 60 * 15);

        return $response;
    }

    public function getData(array $rawData, array $labels, array $periods): array
    {
        $data = [];
        foreach ($rawData as $rawDatum) {
            $uniqueKey = $rawDatum['date'] . $rawDatum['marketplace_id'];
            $data[$uniqueKey] = [
                'date' => $rawDatum['date'],
                'marketplace_id' => $rawDatum['marketplace_id'],
                'orders' => (int)$rawDatum['orders']
            ];
        }

        // Fill missing dates and marketplaces with 0
        foreach ($periods as $date) {
            foreach ($labels as $label) {
                $uniqueKey = $date . $label['marketplace_id'];
                if (!array_key_exists($uniqueKey, $data)) {
                    $data[$uniqueKey] = [
                        'date' => $date,
                        'marketplace_id' => $label['marketplace_id'],
                        'orders' => 0
                    ];
                }
            }
        }

        // Sort by date
        uasort($data, function ($a, $b) {
            return $a['date'] <=> $b['date'];
        });

        return array_values($data);
    }

    public function getPeriods(\DateTime $dateStart, \DateTime $dateEnd): array
    {
        $periods = [];
        $interval = new \DateInterval('P1D');
        $dateEnd->add($interval);
        $dateRange = new \DatePeriod($dateStart, $interval, $dateEnd);
        foreach ($dateRange as $date) {
            $periods[] = $date->format('Y-m-d');
        }
        array_pop($periods);
        return $periods;
    }

    protected function getLabels(array $rawData): array
    {
        $labels = [];
        foreach ($rawData as $data) {
            if (!array_key_exists($data['marketplace_id'], $labels)) {
                $labels[$data['marketplace_id']] = [
                    'marketplace_id' => $data['marketplace_id'],
                    'title' => $data['marketplace_title']
                ];
            }
        }
        return array_values($labels);
    }

    protected function getRawData(FiltersForm $filtersForm, array $requestParams): array
    {
        /** @var CustomerComponent $customerComponent */
        $customerComponent = \Yii::$app->customerComponent;
        $timezone = $customerComponent->getUserTimezoneName();

        $query1 = (new AmazonOrder())
            ->search($requestParams)
            ->select([
                "toDate(order_purchase_date, '$timezone') as date",
                'order_marketplace_id',
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED]
            ]);

        $query2 = (new AmazonOrderInProgress())
            ->search($requestParams)
            ->select([
                "toDate(order_purchase_date, '$timezone') as date",
                'order_marketplace_id',
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED],
                [
                    '=',
                    'version',
                    AmazonOrderInProgress::find()->select('max(version)')
                ]
            ]);

        $result = (new Query())
            ->select([
                'count(t1.date) as orders',
                "t1.date as date",
                't1.order_marketplace_id as marketplace_id',
                "dictGetOrNull(
                    default.amazon_marketplace_dict,
                    'title',
                    t1.order_marketplace_id
                ) as marketplace_title",
            ])
            ->from(['t1' => $query1->union($query2, true)])
            ->groupBy(["t1.date", 't1.order_marketplace_id'])
            ->all(AmazonOrder::getDb())
        ;

        return $result;
    }
}