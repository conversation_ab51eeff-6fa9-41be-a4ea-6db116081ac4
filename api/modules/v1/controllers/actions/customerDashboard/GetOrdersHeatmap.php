<?php

namespace api\modules\v1\controllers\actions\customerDashboard;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use api\modules\v1\forms\widget\FiltersForm;
use common\components\CustomerComponent;
use common\models\customer\clickhouse\AmazonOrder;
use common\models\customer\clickhouse\AmazonOrderInProgress;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Query;
use yii\rest\Action;

class GetOrdersHeatmap extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $cachedData = $this->getFromCache();
        if (false !== $cachedData) {
            return $cachedData;
        }

        $params = \Yii::$app->request->getQueryParams();
        $params['date_start'] = $params['date_start'] ?? date('Y-m-d', strtotime('-7 days'));
        $params['date_end'] = $params['date_end'] ?? date('Y-m-d', strtotime('-1 day'));

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $rows = $this->getRawHeatmap($filtersForm, $params);
        $heatmap = $this->prepareHeatmapStructure($filtersForm);

        foreach ($rows as $row) {
            if (empty($heatmap[$row['day']])) {
                continue;
            }

            $heatmap[$row['day']]['hours'][$row['hour']] = [
                'hour' => (int)$row['hour'],
                'orders' => (int)$row['count']
            ];
        }
        $heatmap = array_values($heatmap);

        $this->saveToCache($heatmap, 60 * 15);
        return $heatmap;
    }

    protected function prepareHeatmapStructure(FiltersForm $filtersForm): array
    {
        /** @var CustomerComponent $customerComponent */
        $customerComponent = \Yii::$app->customerComponent;
        $timezone = $customerComponent->getUserTimezoneName();

        $hours = array_map(function ($v) {
            return ['hour' => $v, 'orders' => 0];
        }, range(0, 23));

        $response = [];

        $utcTimezone = new \DateTimeZone('UTC');
        $time = (new \DateTime($filtersForm->dateStart, $utcTimezone))
            ->setTimezone(new \DateTimeZone($timezone));
        $timeTo = (new \DateTime($filtersForm->dateEnd, $utcTimezone))
            ->setTimezone(new \DateTimeZone($timezone));

        do {
            $day = $time->format('Y-m-d');
            $response[$day] = [
                'date' => $day,
                'hours' => $hours,
            ];
            $time->modify('+1 day');
        } while ($time < $timeTo);

        return $response;
    }

    protected function getRawHeatmap(FiltersForm $filtersForm, array $requestParams): array
    {
        /** @var CustomerComponent $customerComponent */
        $customerComponent = \Yii::$app->customerComponent;
        $timezone = $customerComponent->getUserTimezoneName();

        $query1 = (new AmazonOrder())
            ->search($requestParams)
            ->select([
                "toDate(order_purchase_date, '$timezone') as day",
                "toHour(order_purchase_date, '$timezone') as hour",
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED]
            ]);

        $query2 = (new AmazonOrderInProgress())
            ->search($requestParams)
            ->select([
                "toDate(order_purchase_date, '$timezone') as day",
                "toHour(order_purchase_date, '$timezone') as hour",
            ])
            ->andWhere([
                'AND',
                ['>=', 'order_purchase_date', $filtersForm->dateStart],
                ['<=', 'order_purchase_date', $filtersForm->dateEnd],
                ['!=', 'order_status', Order::ORDER_STATUS_CANCELED],
                [
                    '=',
                    'version',
                    AmazonOrderInProgress::find()->select('max(version)')
                ]
            ]);

        $result = (new Query())
            ->select([
                'count(t1.day) as count',
                "t1.day as day",
                "t1.hour as hour",
            ])
            ->from(['t1' => $query1->union($query2, true)])
            ->groupBy(["t1.day", "t1.hour"])
            ->orderBy([
                "t1.day" => SORT_ASC,
            ])
            ->all(AmazonOrder::getDb())
        ;

        return $result;
    }
}