<?php

namespace api\modules\v1\controllers\actions\customerDashboard;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use yii\rest\Action;

class GetOrdersAndSales extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $cachedData = $this->getFromCache();
        if (false !== $cachedData) {
            return $cachedData;
        }

        $params = \Yii::$app->request->getQueryParams();
        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');
        $filtersForm->validate();

        $periodType = $params['period_type'] ?? DataSeriesStructureManager::PERIOD_TYPE_DAY;

        $response = [
            'currency_id' => $filtersForm->currencyId,
            'data_series' => [],
        ];

        try {
            $profitResult = (new ProfitCalculator($filtersForm))->calc($periodType);

            foreach ($profitResult->dataSeries as $date => $dataSery) {
                $date = date('Y-m-d H:i:s', strtotime($date));

                $response['data_series'][$date] = $response['data_series'][$date] ?? [
                    'orders' => 0,
                    'product_sales' => 0,
                ];

                $response['data_series'][$date]['product_sales'] = round($dataSery['orderedProductSalesAmount'], 2);
                $response['data_series'][$date]['orders'] = $dataSery['units']['orders'];
            }
        } catch (\Throwable $e) {
            \Yii::error($e);
        }

        return $response;
    }
}
