<?php

namespace api\modules\v1\controllers\actions\indirectCost;

use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\IndirectCost;
use yii\rest\Action;

class UpdateAction extends \yii\rest\UpdateAction
{
    public function run($id)
    {
        /** @var IndirectCost $model */
        $model = parent::run($id);
        if (!$model->hasErrors()) {
            $model = $model->search([])->where(['indirect_cost.id' => $model->id])->one();
        }
        return $model;
    }
}
