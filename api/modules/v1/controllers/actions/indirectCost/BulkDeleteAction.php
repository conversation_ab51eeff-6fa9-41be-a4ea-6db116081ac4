<?php

namespace api\modules\v1\controllers\actions\indirectCost;

use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\IndirectCost;
use yii\rest\Action;

class BulkDeleteAction extends Action
{
    protected MessagesSender $messagesSender;
    protected DbManager $dbManager;

    public function __construct($id, $controller, $config = [])
    {
        $this->messagesSender = new MessagesSender();
        $this->dbManager = \Yii::$app->dbManager;
        parent::__construct($id, $controller, $config);
    }

    public function run()
    {
        $params = \Yii::$app->request->getBodyParams();
        $side = $params['side'] ?? null;

        if ($side === 'all') {
            $query = IndirectCost::find()->select('id');
            foreach ($query->batch() as $indirectCosts) {
                $ids = [];
                foreach ($indirectCosts as $indirectCost) {
                    $ids[] = $indirectCost['id'];
                }
                $this->deleteByIds($ids);
            }
        } else {
            $ids = $params['ids'] ?? [];
            IndirectCost::deleteAll(['in', 'id', $ids]);
            $this->messagesSender->indirectCostChanges(
                $this->dbManager->getCustomerId(),
                implode(',', $ids),
                false,
                true
            );
        }

        \Yii::$app->getResponse()->setStatusCode(204);
    }

    protected function deleteByIds(array $ids): void
    {
        IndirectCost::deleteAll(['in', 'id', $ids]);
        $this->messagesSender->indirectCostChanges(
            $this->dbManager->getCustomerId(),
            implode(',', $ids),
            false,
            true
        );
    }
}
