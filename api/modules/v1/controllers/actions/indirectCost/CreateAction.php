<?php

namespace api\modules\v1\controllers\actions\indirectCost;

use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\MessagesSender;
use common\models\customer\IndirectCost;
use yii\rest\Action;

class CreateAction extends \yii\rest\CreateAction
{
    public function run()
    {
        /** @var IndirectCost $model */
        $model = parent::run();
        if (!$model->hasErrors()) {
            $model = $model->search([])->where(['indirect_cost.id' => $model->id])->one();
        }
        return $model;
    }
}
