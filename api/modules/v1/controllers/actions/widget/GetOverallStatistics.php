<?php

namespace api\modules\v1\controllers\actions\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\salesMetricCalculator\UnitsCalculator;
use yii\rest\Action;

class GetOverallStatistics extends Action
{
    public function run()
    {
        \Yii::$app->session->close();

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $params = \Yii::$app->request->getQueryParams();

        if (isset($params['marketplace_seller_ids'])) {
            $params['marketplaceSellerIds'] = $params['marketplace_seller_ids'];
            unset($params['marketplace_seller_ids']);
        }
        if (isset($params['marketplace_id'])) {
            $params['marketplaceId'] = $params['marketplace_id'];
            unset($params['marketplace_id']);
        }

        if (!array_key_exists('isTransactionDateMode', $params)) {
            $params['isTransactionDateMode'] = true;
        }

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $unitsResult = (new UnitsCalculator($filtersForm))->calc();
        $profitResult = (new ProfitCalculator($filtersForm))->calc();

        $prevPeriodFiltersForm = $this->getPreviousPeriodFiltersForm($filtersForm);
        $prevPeriodUnitsResult = (new UnitsCalculator($prevPeriodFiltersForm))->calc();
        $prevPeriodProfitResult = (new ProfitCalculator($prevPeriodFiltersForm))->calc();

        $metrics  = [
            [
                'id' => 'ordered_product_sales',
                'name' => 'Product sales',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->orderedProductSalesAmount,
                'prevAmount' => $prevPeriodProfitResult->orderedProductSalesAmount,
                'prevComparePercents' => $this->compareDiffPercents(
                    $profitResult->orderedProductSalesAmount,
                    $prevPeriodProfitResult->orderedProductSalesAmount
                )
            ], [
                'id' => 'revenue',
                'name' => 'Revenue',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->revenueAmount,
                'prevAmount' => $prevPeriodProfitResult->revenueAmount,
                'prevComparePercents' => $this->compareDiffPercents(
                    $profitResult->revenueAmount,
                    $prevPeriodProfitResult->revenueAmount
                )
            ], [
                'id' => 'expenses',
                'name' => 'Expenses',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->expensesAmount,
                'prevAmount' => $prevPeriodProfitResult->expensesAmount,
                'prevComparePercents' => $this->compareDiffPercents(
                    $profitResult->expensesAmount,
                    $prevPeriodProfitResult->expensesAmount
                )
            ], [
                'id' => 'estimated_profit',
                'name' => 'Estimated margin',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->netProfit,
                'prevAmount' => $prevPeriodProfitResult->netProfit,
                'prevComparePercents' => $this->compareDiffPercents(
                    $profitResult->netProfit,
                    $prevPeriodProfitResult->netProfit
                )
            ], [
                'id' => 'margin',
                'name' => 'Margin',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->margin,
                'prevAmount' => $prevPeriodProfitResult->margin,
                'prevComparePercents' => round($profitResult->margin - $prevPeriodProfitResult->margin, 2)
            ], [
                'id' => 'roi',
                'name' => 'ROI',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->roi,
                'prevAmount' => $prevPeriodProfitResult->roi,
                'prevComparePercents' => round($profitResult->roi - $prevPeriodProfitResult->roi, 2)
            ], [
                'id' => 'orders',
                'name' => 'Order items',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $unitsResult->orders,
                'prevAmount' => $prevPeriodUnitsResult->orders,
                'prevComparePercents' => $this->compareDiffPercents(
                    $unitsResult->orders,
                    $prevPeriodUnitsResult->orders
                )
            ], [
                'id' => 'units',
                'name' => 'Units',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $unitsResult->units,
                'prevAmount' => $prevPeriodUnitsResult->units,
                'prevComparePercents' => $this->compareDiffPercents(
                    $unitsResult->units,
                    $prevPeriodUnitsResult->units
                )
            ], [
                'id' => 'promotion_amount',
                'name' => 'Promotion',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $unitsResult->promo,
                'prevAmount' => $prevPeriodUnitsResult->promo,
                'prevComparePercents' => $this->compareDiffPercents(
                    $unitsResult->promo,
                    $prevPeriodUnitsResult->promo
                )
            ], [
                'id' => 'refunds',
                'name' => 'Refunds',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $unitsResult->refunds,
                'prevAmount' => $prevPeriodUnitsResult->refunds,
                'prevComparePercents' => $this->compareDiffPercents(
                    $unitsResult->refunds,
                    $prevPeriodUnitsResult->refunds
                )
            ], [
                'id' => 'total_acos',
                'name' => 'Total ACoS',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->totalACOS,
                'prevAmount' => $profitResult->totalACOS,
                'prevComparePercents' => $this->compareDiffPercents(
                    $profitResult->totalACOS,
                    $prevPeriodProfitResult->totalACOS
                )
            ]
            // , [
            //     'id' => 'estimated_payout',
            //     'name' => 'Estimated payout',
            //     'type' => CategoriesStructureManager::TYPE_MONEY,
            //     'amount' => $profitResult->estimatedPayout,
            //     'prevAmount' => $prevPeriodProfitResult->estimatedPayout,
            //     'prevComparePercents' => $this->compareDiffPercents(
            //         $profitResult->estimatedPayout,
            //         $prevPeriodProfitResult->estimatedPayout
            //     )
            // ]
        ];

        return [
            'metrics' => $metrics,
        ];
    }

    protected function getPreviousPeriodFiltersForm(FiltersForm $currPeriodFiltersForm): FiltersForm
    {
        $filtersForm = clone $currPeriodFiltersForm;
        $currStartDate = new \DateTime($currPeriodFiltersForm->dateStart);
        $currEndDate = new \DateTime($currPeriodFiltersForm->dateEnd);

        $diffDays = $currStartDate->diff($currEndDate)->days;
        $diffDays += 1;

        $filtersForm->dateEnd = $currEndDate->modify("-$diffDays days")->format('Y-m-d');
        $filtersForm->dateStart = $currStartDate->modify("-$diffDays days")->format('Y-m-d');
        $filtersForm->validate();

        return $filtersForm;
    }

    protected function compareDiffPercents($currentValue, $prevValue): float
    {
        if ($prevValue == 0 && $currentValue == 0) {
            return 0;
        }

        if ($prevValue == 0) {
            return ($currentValue > 0) ? 100 : -100;
        }

        $percents = (($currentValue - $prevValue) / $prevValue) * 100;

        if ($currentValue >= 0 && $prevValue < 0 || $prevValue < 0 && $currentValue >= 0) {
            $percents *= -1;
        }

        return round($percents, 2);
    }
}
