<?php

namespace api\modules\v1\controllers\actions\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\CustomerComponent;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\PPCCostsCalculator;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\salesMetricCalculator\UnitsCalculator;
use common\models\finance\EventPeriod;
use yii\rest\Action;

class GetSalesHistory extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $filtersForm = new FiltersForm();
        $filtersForm->load(\Yii::$app->request->getQueryParams(), '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $maxDepth = \Yii::$app->request->get('maxDepth');
        $categoryId = \Yii::$app->request->get('salesCategoryId');
        $periodType = \Yii::$app->request->get('periodType') ?? DataSeriesStructureManager::PERIOD_TYPE_DAY;

        if (YII_ENV !== 'prod' && $periodType === 'day_debug') {
            $periodType = DataSeriesStructureManager::PERIOD_TYPE_DAY;
            $maxDepth = 10;
        }

        if (null === $maxDepth) {
            $maxDepth = 1;
        } else {
            $maxDepth--;
        }

        $profitResult = (new ProfitCalculator($filtersForm))->calc($periodType, $maxDepth, $categoryId);
        $unitsResult = (new UnitsCalculator($filtersForm))->calc();
        $units = [];

        $unitColors = [
            'units' => '#8bc34a',
            'orders' => '#0755cc',
            'ordersCanceled' => '#85baff',
            'refunds' => '#eb5757',
            'promo' => '#feaa02'
        ];

        // Order of fields is necessary on frontend (according to design)
        $unitsResultArr = [
            'orders' => $unitsResult->orders,
            'ordersCanceled' => $unitsResult->ordersCanceled,
            'units' => $unitsResult->units,
            'promo' => $unitsResult->promo,
            'refunds' => $unitsResult->refunds,
        ];

        foreach ($unitsResultArr as $k => $v) {
            $name = ucfirst($k);

            if ($k === 'ordersCanceled') {
                $name = 'Cancelled order items';
            }

            if ($k === 'orders') {
                $name = 'Order items';
            }

            $units[] = [
                'id' => $k,
                'name' => $name,
                'depth' => 1,
                'type' => 'count',
                'is_default' => true,
                'amount' => $v,
                'color_hex' => $unitColors[$k],
                'children' => [],
                'hasChildren' => false
            ];
        }

        $salesCategories = $profitResult->salesCategories;

        $salesCategories['estimatedProfit'] = [
            'id' => 'estimated_profit',
            'name' => 'Estimated margin',
            'depth' => 0,
            'amount' => $profitResult->netProfit,
            'color_hex' => null,
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'is_default' => true,
            'children' => [],
            'hasChildren' => false
        ];

        $salesCategories['units'] = [
            'id' => 'units',
            'name' => 'Products',
            'depth' => 0,
            'amount' => 0,
            'color_hex' => $unitColors['units'],
            'type' => CategoriesStructureManager::TYPE_COUNT,
            'is_default' => true,
            'children' => $units,
            'hasChildren' => true
        ];

        $ppcCosts = (new PPCCostsCalculator())->calc($filtersForm);

        $ppcMetrics = [
            [
                'id' => 'sponsored_brands',
                'name' => 'Sponsored brands',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => round(
                    $ppcCosts['sponsored_brands_store_spotlight_amount']
                    + $ppcCosts['sponsored_brands_product_collection_amount']
                    + $ppcCosts['sponsored_brands_video_amount'],
                    2
                ),
                'prevAmount' => null,
                'prevComparePercents' => null,
                'subMetrics' => [[
                    'id' => 'sponsored_brands_product_collection',
                    'name' => 'Product collection',
                    'type' => CategoriesStructureManager::TYPE_MONEY,
                    'amount' => $ppcCosts['sponsored_brands_product_collection_amount'],
                ], [
                    'id' => 'sponsored_brands_store_spotlight',
                    'name' => 'Store spotlight',
                    'type' => CategoriesStructureManager::TYPE_MONEY,
                    'amount' => $ppcCosts['sponsored_brands_store_spotlight_amount'],
                ], [
                    'id' => 'sponsored_brands_video',
                    'name' => 'Video',
                    'type' => CategoriesStructureManager::TYPE_MONEY,
                    'amount' => $ppcCosts['sponsored_brands_video_amount'],
                ]]
            ], [
                'id' => 'sponsored_products',
                'name' => 'Sponsored products',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $ppcCosts['sponsored_products_amount'],
                'prevAmount' => null,
                'prevComparePercents' => null,
            ], [
                'id' => 'sponsored_display',
                'name' => 'Sponsored display',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $ppcCosts['sponsored_display_amount'],
                'prevAmount' => null,
                'prevComparePercents' => null,
            ], [
                'id' => 'total_acos',
                'name' => 'Total ACoS',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->totalACOS,
                'prevAmount' => null,
                'prevComparePercents' => null,
            ]
        ];

        return [
            'updatedAt' => EventPeriod::getLastMovedToClickhouseDate(),
            'userTimezone' => \Yii::$app->customerComponent->getUserTimezoneName(),
            'currency' => $filtersForm->currencyId,
            'salesCategories' => $salesCategories,
            'dataSeries' => $profitResult->dataSeries,
            'ppcMetrics' => $ppcMetrics
        ];
    }
}
