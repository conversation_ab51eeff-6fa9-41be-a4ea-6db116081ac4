<?php

namespace api\modules\v1\controllers\actions\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesMetricCalculator\ACoSCalculator;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\dto\AcosResult;
use common\components\salesMetricCalculator\dto\ProfitResult;
use common\components\salesMetricCalculator\PPCCostsCalculator;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\salesMetricCalculator\UnitsCalculator;
use common\models\finance\EventPeriod;
use common\models\SalesCategory;
use yii\rest\Action;

class GetKeyPerformance extends Action
{
    private const STYLE_TOTAL_1 = 'total_1';
    private const STYLE_TOTAL_2 = 'total_2';
    private const STYLE_REGULAR = 'regular';
    
    public function run()
    {
        \Yii::$app->session->close();

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $params = \Yii::$app->request->get();
        $filtersForm = new FiltersForm();
        $filtersForm->load(\Yii::$app->request->getQueryParams(), '');

        $salesCategoryStrategy = $params['sales_category_strategy'] ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY;

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $unitsResult = (new UnitsCalculator($filtersForm))->calc();
        $profitResult = (new ProfitCalculator($filtersForm))->calc();
        $ACoSResult = (new ACoSCalculator())->calc($filtersForm);

        $amounts = [
            'ordered_product_sales' => $profitResult->orderedProductSalesAmount,
            'revenue' => $profitResult->revenueAmount,
            'net_profit' => $profitResult->netProfit,
            'margin' => $profitResult->margin,
            'roi' => $profitResult->roi,
            'ppc' => $profitResult->PPCCostsAmount
        ];
        $units = json_decode(json_encode($unitsResult), true);

        $leftSide  = [
            [
                'id' => 'ordered_product_sales',
                'name' => 'Product sales',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->orderedProductSalesAmount
            ], [
                'id' => 'revenue',
                'name' => 'Revenue',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->revenueAmount
            ], [
                'id' => 'net_profit',
                'name' => 'Estimated margin',
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $profitResult->netProfit
            ], [
                'id' => 'ppc',
                'name' => 'Ads (PPC)',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $profitResult->PPCCostsAmount
            ]
        ];
        $rightSide = [
            [
                'id' => 'orders',
                'name' => 'Order items',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $units['orders']
            ], [
                'id' => 'units',
                'name' => 'Units',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $units['units']
            ], [
                'id' => 'promo',
                'name' => 'Promo',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $units['promo']
            ], [
                'id' => 'refunds',
                'name' => 'Refunds',
                'type' => CategoriesStructureManager::TYPE_COUNT,
                'amount' => $units['refunds']
            ], [
                'id' => 'margin',
                'name' => 'Margin',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->margin
            ], [
                'id' => 'roi',
                'name' => 'ROI',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->roi
            ], [
                'id' => 'markup',
                'name' => 'Markup',
                'type' => CategoriesStructureManager::TYPE_PERCENTS,
                'amount' => $profitResult->markup
            ]
        ];

        return [
            'updatedAt' => EventPeriod::getLastMovedToClickhouseDate(),
            'amounts' => $amounts,
            'units' => $units,
            'left_side' => $leftSide,
            'right_side' => $rightSide,
            'profit_breakdown' => $salesCategoryStrategy == SalesCategoryStrategyFactory::STRATEGY_CUSTOM ? $this->generateProfitBreakdownV1($profitResult) : $this->generateProfitBreakdown($profitResult, $ACoSResult)
        ];
    }

    private function generateProfitBreakdown(ProfitResult $profitResult, AcosResult $ACoSResult): array
    {
        $salesCategories = $profitResult->salesCategories;

        $otherIncomeParts = [];

        foreach ($salesCategories[RevenueExpensesStrategy::CATEGORY_REVENUE]['children'] as $salesCategory) {
            // Product sales already included into root category
            if ($salesCategory['id'] === 'product_sales') {
                continue;
            }

            $otherIncomeParts[] = [
                'id' => $salesCategory['id'],
                'name' => $salesCategory['name'],
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $salesCategory['amount'],
                'style' => self::STYLE_REGULAR,
            ];
        }

        $otherIncomeParts[] = [
            'id' => $salesCategories[RevenueExpensesStrategy::CATEGORY_REVENUE]['id'],
            'name' => $salesCategories[RevenueExpensesStrategy::CATEGORY_REVENUE]['name'],
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'amount' => $salesCategories[RevenueExpensesStrategy::CATEGORY_REVENUE]['amount'],
            'style' => self::STYLE_TOTAL_1,
        ];

        $expensesParts = [];

        foreach ($salesCategories[RevenueExpensesStrategy::CATEGORY_COSTS]['children'] as $salesCategory) {
            $expensesParts[] = [
                'id' => $salesCategory['id'],
                'name' => $salesCategory['name'],
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $salesCategory['amount'],
                'style' => self::STYLE_REGULAR,
            ];
        }

        $expensesParts[] = [
            'id' => 'total_expenses',
            'name' => 'Total expenses',
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'amount' => $profitResult->expensesAmount,
            'style' => self::STYLE_TOTAL_1,
        ];

        $profitBreakdown = [
            [
                'id' => 'product_sales',
                'title' => 'Product sales',
                'children' => [
                    [
                        'id' => 'shipped_orders',
                        'name' => 'Shipped orders',
                        'type' => CategoriesStructureManager::TYPE_MONEY,
                        'amount' => $profitResult->orderedProductSalesAmount,
                        'style' => self::STYLE_REGULAR,
                    ]
                ],
            ],
            [
                'id' => 'other_income',
                'title' => 'Other income',
                'children' => $otherIncomeParts,
            ],
            [
                'id' => $salesCategories[RevenueExpensesStrategy::CATEGORY_COSTS]['id'],
                'title' => $salesCategories[RevenueExpensesStrategy::CATEGORY_COSTS]['name'],
                'children' => $expensesParts
            ],
            [
                'id' => null,
                'title' => null,
                'children' => [
                    [
                        'id' => 'estimated_profit',
                        'name' => 'Estimated margin',
                        'type' => CategoriesStructureManager::TYPE_MONEY,
                        'amount' => $profitResult->netProfit,
                        'style' => self::STYLE_TOTAL_2,
                    ],
                    // [
                    //     'id' => 'estimated_payout',
                    //     'name' => 'Estimated payout',
                    //     'type' => CategoriesStructureManager::TYPE_MONEY,
                    //     'amount' => $profitResult->estimatedPayout,
                    //     'style' => self::STYLE_REGULAR,
                    // ],
                    [
                        'id' => 'margin',
                        'name' => 'Margin',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->margin,
                        'style' => self::STYLE_REGULAR,
                    ],
                    [
                        'id' => 'roi',
                        'name' => 'ROI',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->roi,
                        'style' => self::STYLE_REGULAR,
                    ],
                    [
                        'id' => 'markup',
                        'name' => 'Markup',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->markup,
                        'style' => self::STYLE_REGULAR,
                    ],
                    [
                        'id' => 'acos',
                        'name' => 'ACoS',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $ACoSResult->acos,
                        'style' => self::STYLE_REGULAR,
                    ],
                ]
            ]
        ];

        return $profitBreakdown;
    }

    private function generateProfitBreakdownV1(ProfitResult $profitResult): array
    {
        $salesCategories = $profitResult->salesCategories;

        $profitBreakdown = [];

        foreach ($salesCategories as $salesCategory) {
            $profitBreakdown[] = [
                'id' => $salesCategory['id'],
                'name' => $salesCategory['name'],
                'type' => CategoriesStructureManager::TYPE_MONEY,
                'amount' => $salesCategory['amount'],
                'style' => self::STYLE_REGULAR,
            ];
        }

        $profitBreakdown = array_merge($profitBreakdown, [
            [
                'id' => null,
                'title' => null,
                'children' => [
                    [
                        'id' => 'estimated_profit',
                        'name' => 'Estimated margin',
                        'type' => CategoriesStructureManager::TYPE_MONEY,
                        'amount' => $profitResult->netProfit,
                        'style' => self::STYLE_TOTAL_2,
                    ],
                    // [
                    //     'id' => 'estimated_payout',
                    //     'name' => 'Estimated payout',
                    //     'type' => CategoriesStructureManager::TYPE_MONEY,
                    //     'amount' => $profitResult->estimatedPayout,
                    //     'style' => self::STYLE_REGULAR,
                    // ],
                    [
                        'id' => 'margin',
                        'name' => 'Margin',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->margin,
                        'style' => self::STYLE_REGULAR,
                    ],
                    [
                        'id' => 'roi',
                        'name' => 'ROI',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->roi,
                        'style' => self::STYLE_REGULAR,
                    ],
                    [
                        'id' => 'markup',
                        'name' => 'Markup',
                        'type' => CategoriesStructureManager::TYPE_PERCENTS,
                        'amount' => $profitResult->markup,
                        'style' => self::STYLE_REGULAR,
                    ],
                ]
            ]
        ]);

        return $profitBreakdown;
    }
}
