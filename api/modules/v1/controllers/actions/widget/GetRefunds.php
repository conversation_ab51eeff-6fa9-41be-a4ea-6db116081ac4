<?php

namespace api\modules\v1\controllers\actions\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesMetricCalculator\RefundsCalculator;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\FbaReturn;
use common\models\customer\FbmReturn;
use common\models\customer\Product;
use common\models\RefundReason;
use yii\db\Query;
use yii\rest\Action;

class GetRefunds extends Action
{
    use ExtraFiltersTrait;

    public function run()
    {
        \Yii::$app->session->close();

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $params = \Yii::$app->request->getQueryParams();

        if (isset($params['marketplace_seller_ids'])) {
            $params['marketplaceSellerIds'] = $params['marketplace_seller_ids'];
            unset($params['marketplace_seller_ids']);
        }
        if (isset($params['marketplace_id'])) {
            $params['marketplaceId'] = $params['marketplace_id'];
            unset($params['marketplace_id']);
        }

        if (!array_key_exists('isTransactionDateMode', $params)) {
            $params['isTransactionDateMode'] = true;
        }

        if (!array_key_exists('salesCategoryStrategy', $params)) {
            $params['salesCategoryStrategy'] = SalesCategoryStrategyFactory::DEFAULT_STRATEGY;
        }

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $refundsWithCount = $this->calc($params);
        $refundsReason = RefundReason::find()->indexBy('id')->asArray()->all();

        foreach ($refundsWithCount as $key => $refundWithCount) {
            if (isset($refundsReason[$refundWithCount['return_reason']])) {
                $refundsWithCount[$key]['name'] = $refundsReason[$refundWithCount['return_reason']]['name'];
                $refundsWithCount[$key]['description'] = $refundsReason[$refundWithCount['return_reason']]['description'];
            }
        }
        return $refundsWithCount;
    }

    public function calc(array $params): array
    {
        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($params['salesCategoryStrategy'] ?? SalesCategoryStrategyFactory::STRATEGY_REVENUE_EXPENSES);

        $query = $salesCategoryStrategy->getAmazonOrderExtendedViewQuery();
        
        $query = $query
            ->select([
                'return_reason',
                'COUNT(order_id) AS refunds'])
            ->groupBy('return_reason')
            ->orderBy('refunds DESC');

        $query->andWhere(['AND', ['!=', 'return_reason', ''], ['IS NOT', 'return_reason', null]]);

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplaceSellerIds'] ?? '',
            $params['marketplaceId'] ?? '',
            $params['sellerId'] ?? '',
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query, 'seller_id');
        $this->applyDateStartDateEndFilter(
            $query,
            $params['dateStart'] ?? null,
            $params['dateEnd'] ?? null,
            'order_purchase_date'
        );
        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'ean' => 'product_ean',
                'upc' => 'product_upc',
                'sku' => 'seller_sku',
                'asin' => 'product_asin',
                'isbn' => 'product_isbn',
                'brand' => 'product_brand',
                'manufacturer' => 'product_manufacturer',
                'productType' => 'product_type',
                'parentAsin' => 'product_parent_asin',
                'title' => 'product_title',
                'stockType' => 'product_stock_type',
                'adultProduct' => 'adult_product',
                'tagId' => 'tag_id',
            ]
        );

        return $query->asArray()->all();
    }
}
