<?php

namespace api\modules\v1\controllers\actions\widget;

use yii\rest\Action;
use common\models\RefundReason;
use api\modules\v1\enums\RefundReasons;
use api\modules\v1\forms\widget\FiltersForm;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;

class GetRefunds extends Action
{
    use ExtraFiltersTrait;

    public function run()
    {
        \Yii::$app->session->close();

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $params = \Yii::$app->request->getQueryParams();

        if (isset($params['marketplace_seller_ids'])) {
            $params['marketplaceSellerIds'] = $params['marketplace_seller_ids'];
            unset($params['marketplace_seller_ids']);
        }
        if (isset($params['marketplace_id'])) {
            $params['marketplaceId'] = $params['marketplace_id'];
            unset($params['marketplace_id']);
        }

        if (!array_key_exists('isTransactionDateMode', $params)) {
            $params['isTransactionDateMode'] = true;
        }

        if (!array_key_exists('salesCategoryStrategy', $params)) {
            $params['salesCategoryStrategy'] = SalesCategoryStrategyFactory::DEFAULT_STRATEGY;
        }

        $filtersForm = new FiltersForm();
        $filtersForm->load($params, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $refundsWithCount = $this->calc($params);
        $refundsReason = RefundReason::find()->indexBy('id')->asArray()->all();

        foreach ($refundsWithCount as $key => $refundWithCount) {
            if (isset($refundsReason[$refundWithCount['return_reason']])) {
                $refundsWithCount[$key]['name'] = $refundsReason[$refundWithCount['return_reason']]['name'];
                $refundsWithCount[$key]['description'] = $refundsReason[$refundWithCount['return_reason']]['description'];
                $refundsWithCount[$key]['color'] = $refundsReason[$refundWithCount['return_reason']]['color'];
            }
        }
        return $refundsWithCount;
    }

    public function calc(array $params): array
    {
        $salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();
        $salesCategoryStrategy = $salesCategoryStrategyFactory->getStrategyByType($params['salesCategoryStrategy'] ?? SalesCategoryStrategyFactory::STRATEGY_REVENUE_EXPENSES);

        $query = $salesCategoryStrategy->getAmazonOrderExtendedViewQuery();

        $query = $query
            ->select([
                'return_reason' => RefundReasons::clickhouseNormalizeExpr('return_reason'),
                'COUNT(order_id) AS refunds'
            ])
            ->groupBy('return_reason')
            ->orderBy('refunds DESC');

        $query->andWhere(['IS NOT', 'refund_date', null]);

        $this->applyMarketplaceSellerGroupsFilter(
            $query,
            $params['marketplaceSellerIds'] ?? '',
            $params['marketplaceId'] ?? '',
            $params['sellerId'] ?? '',
        );
        $this->applyExpiredOrInactiveSubscriptionLogic($query);
        $this->applyDateStartDateEndFilter(
            $query,
            'order_purchase_date',
            $params['dateStart'] ?? null,
            $params['dateEnd'] ?? null,
        );
        $this->applySegmentationFilters(
            $query,
            $params,
            [
                'ean' => 'product_ean',
                'upc' => 'product_upc',
                'sku' => 'seller_sku',
                'asin' => 'product_asin',
                'isbn' => 'product_isbn',
                'brand' => 'product_brand',
                'manufacturer' => 'product_manufacturer',
                'productType' => 'product_type',
                'offerType' => 'offer_type',
                'parentAsin' => 'product_parent_asin',
                'title' => 'product_title',
                'stockType' => 'product_stock_type',
                'adultProduct' => 'adult_product',
                'tagId' => 'tag_id',
            ]
        );

        return $query->asArray()->all();
    }
}
