<?php

namespace api\modules\v1\controllers\actions\widget;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitAndLostCalculator;
use yii\rest\Action;

class GetProfitAndLost extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $filtersForm = new FiltersForm();
        $filtersForm->salesCategoryStrategy = SalesCategoryStrategyFactory::STRATEGY_CUSTOM;
        $filtersForm->load(\Yii::$app->request->getQueryParams(), '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        return (new ProfitAndLostCalculator($filtersForm))
            ->calc(
                \Yii::$app->request->get('periodType') ?? DataSeriesStructureManager::PERIOD_TYPE_DAY
            );
    }
}
