<?php

namespace api\modules\v1\controllers\actions\productCostItem;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\components\core\db\dbManager\DbManager;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\mutex\Mutex;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class BulkUpdateAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $data = \Yii::$app->request->post();
        $productCostPeriodId = \Yii::$app->request->get('product_cost_period_id');

        if (empty($productCostPeriodId)) {
            throw new BadRequestHttpException("Missing required parameter 'product_cost_period_id'");
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        /** @var Mutex $mutex */
        $mutex = \Yii::$app->mutex;
        $mutexKey = implode('_', [
            'bulk_edit_mutex',
            $dbManager->getCustomerId(),
        ]);
        $isAlreadyInProgress = !$mutex->acquire($mutexKey);

        if ($isAlreadyInProgress) {
            $mutex->acquire($mutexKey, 30);
            $mutex->release($mutexKey);
            return ProductCostItem::find()
                ->noCache()
                ->where(['=', 'product_cost_period_id', $productCostPeriodId])->all();
        }

        try {
            /** @var ProductCostPeriod $period */
            $period = ProductCostPeriod::findOne($productCostPeriodId);
            /** @var Product $product */
            $product = $period->getProduct()->one();

            $this->checkIsAllowManageProductOrThrowException($product, $period->sales_category_id);
            $this->checkIsAllowManagePeriodOrThrowException($period, $product);

            if ($product->stock_type === Product::STOCK_TYPE_FBA
                && !$product->is_multiple_stock_type
                && in_array(
                    $period->sales_category_id,
                    [SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS]
                )
            ) {
                throw new \Exception(\Yii::t('admin', "Can not be managed for FBA products"));
            }

            $itemsToSave = [];
            $foundErrors = [];

            foreach ($data as $index => $productCostItemAsArray) {
                if (empty($productCostItemAsArray['id'])) {
                    $productCostItem = new ProductCostItem();
                    $productCostItem->product_cost_period_id = $productCostPeriodId;
                } else {
                    $productCostItem = ProductCostItem::findOne($productCostItemAsArray['id']);
                }

                $productCostItem->load($productCostItemAsArray, '');
                $productCostItem->validate();
                $errors = $productCostItem->getErrors();
                if (count($errors) > 0) {
                    $foundErrors[] = [
                        'index' => $index,
                        'errors' => $errors
                    ];
                }
                $itemsToSave[] = $productCostItem;
            }

            if (count($foundErrors) > 0) {
                \Yii::$app->getResponse()->setStatusCode(422);
                return $foundErrors;
            }

            $actualIds = [];

            foreach ($itemsToSave as $productCostItem) {
                $productCostItem->save(false);
                $actualIds[] = $productCostItem['id'];
            }

            /** @var ProductCostItem[] $itemsToDelete */
            $itemsToDelete = ProductCostItem::find()->where([
                'and',
                ['=', 'product_cost_period_id', $productCostPeriodId],
                ['not in', 'id', $actualIds]
            ])->all();

            foreach ($itemsToDelete as $productCostItem) {
                $productCostItem->delete();
            }
            /** @var ProductCostPeriod $period */
            $period = ProductCostPeriod::findOne($productCostPeriodId);
            $period->source = ProductCostCategory::SOURCE_MANUAL;
            $period->save(false);
            $period->recalculateFBMShippingCostsIfNeed();
            $mutex->release($mutexKey);

            $this->synchroniseGlobalMarketplaceIfNeed($product);

            return ProductCostItem::find()->where(['=', 'product_cost_period_id', $productCostPeriodId])->all();
        } catch (\Throwable $e) {
            $mutex->release($mutexKey);
            throw $e;
        }
    }
}
