<?php

namespace api\modules\v1\controllers\actions\productCostItem;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;
use yii\web\ServerErrorHttpException;

class DeleteAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run($id)
    {
        /** @var ProductCostItem $model */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }

        /** @var ProductCostPeriod $period */
        $period = $model->getProductCostPeriod()->one();
        /** @var Product $product */
        $product = $period->getProduct()->one();

        $this->checkIsAllowManageProductOrThrowException($product, $period->sales_category_id);
        $this->checkIsAllowManagePeriodOrThrowException($period, $product);

        if ($model->delete() === false) {
            throw new ServerErrorHttpException(\Yii::t('admin', 'Failed to delete the object for unknown reason.'));
        }

        $this->synchroniseGlobalMarketplaceIfNeed($product);

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
