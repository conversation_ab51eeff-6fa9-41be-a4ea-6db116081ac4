<?php

namespace api\modules\v1\controllers\actions\productCostItem;

use common\models\customer\Product;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\rest\Action;
use yii\web\ServerErrorHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        /** @var ProductCostItem $model */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->isAllowManageOrThrowException();

        if ($model->delete() === false) {
            throw new ServerErrorHttpException(\Yii::t('admin', 'Failed to delete the object for unknown reason.'));
        }

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
