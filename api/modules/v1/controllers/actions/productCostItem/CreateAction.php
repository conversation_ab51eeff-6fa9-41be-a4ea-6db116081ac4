<?php

namespace api\modules\v1\controllers\actions\productCostItem;

use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\rest\Action;

class CreateAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /* @var $model ProductCostItem */
        $model = new ProductCostItem();
        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
        $model->isAllowManageOrThrowException();

        /** @var ProductCostPeriod $period */
        $period = $model->getProductCostPeriod()->one();
        /** @var Product $product */
        $product = $period->getProduct()->one();

        if ($product->stock_type === Product::STOCK_TYPE_FBA
            && !$product->is_multiple_stock_type
            && in_array(
                $period->sales_category_id,
                [SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS]
            )
        ) {
            throw new \Exception(\Yii::t('admin', "Can not be created for FBA products"));
        }

        // Allow only one item for VAT due to UI limitation
        if ($period->sales_category_id === SalesCategory::CATEGORY_EXPENSES_TAXES) {
            $existingItem = $period->getItems()->noCache()->one();

            if (!empty($existingItem)) {
                $model = $existingItem;
                $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
            }
        }

        if ($model->save()) {
            /** @var ProductCostPeriod $period */
            $period = $model->getProductCostPeriod()->one();
            $period->source = ProductCostCategory::SOURCE_MANUAL;
            $period->save(false);

            $response = \Yii::$app->getResponse();
            $response->setStatusCode(201);
        }

        return $model;
    }
}
