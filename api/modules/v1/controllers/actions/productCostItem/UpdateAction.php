<?php

namespace api\modules\v1\controllers\actions\productCostItem;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\rest\Action;

class UpdateAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run($id)
    {
        /* @var $model ProductCostItem */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');

        /** @var ProductCostPeriod $period */
        $period = $model->getProductCostPeriod()->one();
        /** @var Product $product */
        $product = $period->getProduct()->one();

        $this->checkIsAllowManageProductOrThrowException($product);
        $this->checkIsAllowManagePeriodOrThrowException($period, $product);

        if ($product->stock_type === Product::STOCK_TYPE_FBA
            && !$product->is_multiple_stock_type
            && in_array(
                $period->sales_category_id,
                [SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS]
            )
        ) {
            throw new \Exception(\Yii::t('admin', "Can not be edited for FBA products"));
        }

        if ($model->save()) {
            /** @var ProductCostPeriod $period */
            $period = $model->getProductCostPeriod()->one();
            $period->source = ProductCostCategory::SOURCE_MANUAL;
            $period->save(false);

            $this->synchroniseGlobalMarketplaceIfNeed($product);
        }

        return $model;
    }
}
