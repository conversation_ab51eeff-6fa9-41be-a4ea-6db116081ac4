<?php

namespace api\modules\v1\controllers\actions\productFilterList;

use common\models\customer\ProductFilterList;
use Yii;
use yii\base\InvalidConfigException;
use yii\rest\Action;

class CreateAction extends Action
{

    /**
     * @throws \Throwable
     * @throws InvalidConfigException
     */
    public function run(): ProductFilterList
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $form = new ProductFilterList();
        $form->load(Yii::$app->request->getBodyParams(), '');

        $customerId = \Yii::$app->dbManager->getCustomerId();
        $form->customer_id = $customerId;
        $identity = Yii::$app->user->identity;
        $form->user_id = $identity->user_id;

        if ($form->validate()) {
            $form->save();
        }
        return $form;
    }
}
