<?php

namespace api\modules\v1\controllers\actions\productAggregatedRefunds;

use api\modules\v1\controllers\actions\FullPageCacheTrait;
use common\models\customer\clickhouse\ProxyFbaReturn;
use common\models\customer\clickhouse\ProxyFbmReturn;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use Yii;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class GetCustomerComments extends Action
{
    use FullPageCacheTrait;

    public function run()
    {
        $request = Yii::$app->request;

        $params = \Yii::$app->request->queryParams;

        $sellerSku = $params['seller_sku'];
        $marketplaceId = $params['marketplace_id'];
        $sellerId = $params['seller_id'];
        $orderRefundDate = $params['refund_date'];
        $returnReason = $params['return_reason'] ?? '';

        if (!$sellerSku || !$marketplaceId || !$sellerId) {
            throw new BadRequestHttpException('seller_sku, marketplace_id and seller_id are required');
        }

        $fbmClass = new ProxyFbmReturn();

        $fbaQuery = ProxyFbaReturn::find()
            ->select(['customer_comments', 'return_date', 'order_id'])
            ->where([
                'sku' => $sellerSku,
                'marketplace_id' => $marketplaceId,
                'seller_id' => $sellerId
            ])
            ->andWhere(['!=', 'customer_comments', ''])
            ->andWhere(['IS NOT', 'customer_comments', null]);

        $fbmClass->applyBetweenDateFilter($fbaQuery, 'return_date', $orderRefundDate);

        $fbaQuery->andFilterWhere(['=', 'reason', $returnReason]);

        $fbaComments = $fbaQuery->orderBy(['return_date' => SORT_DESC])
            ->asArray()
            ->all();

        // Sort by return_date descending
        usort($fbaComments, function($a, $b) {
            return strtotime($b['return_date']) - strtotime($a['return_date']);
        });

        return $fbaComments;
    }
}
