<?php

namespace api\modules\v1\controllers\actions\product;

use api\modules\v1\forms\product\ProductBulkEditForm;
use common\components\dataImportExport\import\ImportManager;
use common\components\dataImportExport\SupportedHandlers;
use common\models\customer\DataImport;
use yii\helpers\Json;
use yii\rest\Action;

class BulkEditAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $data = \Yii::$app->request->getBodyParams();
        $form = new ProductBulkEditForm($data, \Yii::$app->request->get());
        $form->load($data, '');

        if (!$form->validate()) {
            return $form;
        }

        $importManager = new ImportManager();

        if ($form->side === DataImport::SIDE_ALL) {
            $form->ids = [];
        }

        $params = [
            'settings' => [
                'side' => $form->side,
                'ids' => $form->ids,
            ],
            'query' => $form->query,
            'fields' => $form->getPreparedFields(),
        ];
        $dataImport = new DataImport();
        $dataImport->status = DataImport::STATUS_NEW;
        $dataImport->log('Created');
        $dataImport->handler_name = SupportedHandlers::HANDLER_PRODUCT;
        $dataImport->created_at = date('Y-m-d H:i:s');
        $dataImport->type = DataImport::TYPE_BULK_EDIT;
        $dataImport->last_id = 0;
        $dataImport->params = Json::encode($params);
        $dataImport->save(false);

        $importManager->enqueueSplittingIntoParts($dataImport);

        return $dataImport;
    }
}
