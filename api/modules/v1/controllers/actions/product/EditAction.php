<?php
namespace api\modules\v1\controllers\actions\product;

use common\models\customer\ProductTag;
use common\models\customer\Tag;
use yii\rest\Action;
use Yii;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class EditAction extends Action
{
    /**
     * @throws NotFoundHttpException
     * @throws BadRequestHttpException
     */
    public function run($id)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $model = $this->findModel($id);

        $params = Yii::$app->request->post();
        $model->load($params, '');

        if (!$model->validate()) {
            return $model;
        }

        if (isset($params['tag_id']) && is_array($params['tag_id'])) {
            ProductTag::deleteAll(['product_id' => $id]);
            $validTags = Tag::find()
                ->select('id')
                ->where(['id' => $params['tag_id']])
                ->column();
            foreach ($validTags as $tagId) {
                $productTag = new ProductTag();
                $productTag->product_id = $id;
                $productTag->tag_id = $tagId;
                $productTag->save();
            }
        }

        if (!$model->save()) {
            throw new BadRequestHttpException('Failed to update product.');
        }

        $model->refresh();

        return $model;
    }
}
