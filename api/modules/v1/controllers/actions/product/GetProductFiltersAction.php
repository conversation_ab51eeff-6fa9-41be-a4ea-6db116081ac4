<?php

namespace api\modules\v1\controllers\actions\product;

use api\modules\v1\forms\widget\FiltersForm;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\customer\Product;
use common\models\customer\TransactionExtendedView;
use common\models\order\AmazonOrder;
use common\models\customer\ProductTag;
use Exception;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\Inflector;
use yii\rest\Action;

class GetProductFiltersAction extends Action
{
    use ExtraFiltersTrait;

    /**
     * @throws Exception
     */
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $filtersForm = new FiltersForm();
        $params = \Yii::$app->request->getQueryParams();

        $convertedParams = [];
        foreach ($params as $key => $value) {
            $camelCaseKey = lcfirst(Inflector::camelize($key));
            $convertedParams[$camelCaseKey] = $value;
        }

        $filtersForm->load($convertedParams, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $filterArr = [
            'marketplace_id' => 'marketplace_id',
            'product_stock_type' => 'stock_type',
            'product_sku' => 'sku',
            'product_asin' => 'asin',
            'product_title' => 'title',
            'product_ean' => 'ean',
            'product_upc' => 'upc',
            'product_brand' => 'brand',
            'product_seller_id' => 'seller_id',
            'product_isbn' => 'isbn',
            'product_type' => 'product_type',
            'parent_asin' => 'parent_asin',
            'product_age_range' => 'age_range',
            'product_manufacturer' => 'manufacturer',
            'product_adult' => 'adult_product',
            'product_parent_asin' => 'parent_asin',
            'tag_id' => 'tag_id'
        ];

        $filterFields = [
            'product_brand' => 'brand',
            'product_stock_type' => 'stock_type',
            'product_manufacturer' => 'manufacturer',
            'product_type' => 'product_type'
        ];
        $query = Product::find();

        $results = [];
        foreach ($filterFields as $field) {
            $fieldQuery = clone $query;
            $fieldQuery = $this->applyFiltersExcept($filtersForm, $fieldQuery, $filterArr, $field);

            $this->applyMarketplaceSellerGroupsFilter(
                $fieldQuery,
                $params['marketplaceSellerIds'] ?? '',
                $filtersForm->marketplaceId,
                $filtersForm->sellerId
            );
            $queryUnionFilter = $this->queryAddFilter($filtersForm, $query, $field, true)->select($field)->distinct()->andWhere("$field is not null");
            $results[$field] = $fieldQuery->union($queryUnionFilter)->select($field)->distinct()->andWhere("$field is not null")->cache(60*5)->column();
        }

        $results['offer_type'] = [AmazonOrder::OFFER_TYPE_B2B, AmazonOrder::OFFER_TYPE_B2C];

        return [
            'marketplace_ids' => [],
            'stock_types' => $this->transformArray($results['stock_type']),
            'brands' => $this->transformArray($results['brand']),
            'manufacturers' => $this->transformArray($results['manufacturer']),
            'product_types' => $this->transformArray($results['product_type'], true),
            'offer_types' => $this->transformArray($results['offer_type'])
        ];
    }

    private function queryAddFilter(FiltersForm $filtersForm, ActiveQuery $query, string $filterName, bool $isExclude = false): ActiveQuery
    {
        $filterNameUrl = lcfirst(str_replace('_', '', ucwords($filterName, '_')));

        $filter = $filtersForm->$filterNameUrl;

        if ($filterName == 'brand' || $filterName == 'manufacturer') {
            if (strpos((string)$filter, ',') !== false) {
                $conditions = ['or'];
                foreach (explode(',', $filter) as $value) {
                    $conditions[] = ['ilike', new Expression("$filterName"), trim($value)];
                }
                if (!$isExclude) {
                    $query->orFilterWhere($conditions);
                }
                return $query->andFilterWhere($conditions);
            } else {
                if (!$isExclude) {
                    $query->orFilterWhere(['ilike', new Expression("$filterName"), $filter]);
                }
                return $query->andFilterWhere(['ilike', new Expression("$filterName"), $filter]);
            }
        }
        if (!$isExclude) {
            if ($filterName == 'tag_id') {
                if (!empty($filter)) {
                    $query->joinWith('tags');
                    $tagsArray = explode(',', $filter);

                    return $query->andWhere([$filterName => $tagsArray]);
                }
            }

            if (strpos((string)$filter, ',') !== false) {
                $query->andFilterWhere(['IN', $filterName, explode(',', $filter)]);
            } else {
                if ($filterName == 'adult_product') {
                    if ($filter === true) {
                        $query->andFilterWhere([$filterName => true]);
                    }
                } else {
                    $query->andFilterWhere(['=', $filterName, $filter]);
                }
            }
        }
        return $query;
    }

    private function applyFiltersExcept($filtersForm, $query, $filterArr, $excludeFilter) {

        foreach ($filterArr as $filter) {
            if ($filter !== $excludeFilter) {
                $query = $this->queryAddFilter($filtersForm, $query, $filter);
            }
        }

        return $query;
    }

    private function transformArray($array, $isUrlString = false): array
    {
        $mappedArray = [];
        foreach ($array as $item) {
            if (!empty($item)) {
                $slug = mb_strtolower($item);
                $slug = str_replace(',', '-', $slug);
                if (isset($mappedArray[$slug])) {
                    if (strcasecmp($item, $mappedArray[$slug]) < 0) {
                    }
                } else {
                    $mappedArray[$slug] = $item;
                }
            }
        }
        ksort($mappedArray);

        return array_map(function($item) use ($isUrlString) {
            return [
                'key' => ucwords($item),
                'value' => $isUrlString ? ucwords(str_replace('_', ' ', strtolower($item))) : ucwords($item)
            ];

        }, $mappedArray);
    }
}
