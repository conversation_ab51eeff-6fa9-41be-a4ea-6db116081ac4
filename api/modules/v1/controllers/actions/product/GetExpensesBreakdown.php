<?php

namespace api\modules\v1\controllers\actions\product;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\treeStructureHelper\breakdown\BreakdownBuilder;
use common\components\treeStructureHelper\breakdown\BreakdownConfig;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\Product;
use common\models\SalesCategory;
use yii\db\Query;
use yii\rest\Action;
use yii\web\NotFoundHttpException;

class GetExpensesBreakdown extends Action
{
    public function run(
        string $currencyId,
        string $marketplaceId,
        string $sellerId,
        string $sellerSku
    ) {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $filtersForm = new FiltersForm();
        $filtersForm->sellerId = $sellerId;
        $filtersForm->sellerSku = $sellerSku;
        $filtersForm->marketplaceId = $marketplaceId;
        $filtersForm->currencyId = $currencyId;

        $profitResult = (new ProfitCalculator($filtersForm))->calc(
            DataSeriesStructureManager::PERIOD_TYPE_ALL,
            5
        );

        $expenses = $profitResult->salesCategories[SalesCategory::CATEGORY_COSTS]['children'];

        $config = new BreakdownConfig();
        $config->maxDepth = 5;
        $config->onlyLastElements = true;
        $config->skipEmptyAmounts = true;
        $config->allowEmptyAmountsOnDepth = 1;
        $config->skipIds = [SalesCategory::CATEGORY_EXPENSES_AMAZON_FEES, SalesCategory::CATEGORY_PROMOTION];

        $builder = new BreakdownBuilder();

        $breakdown = $builder->build($expenses, $config);

        $total = 0;

        foreach ($breakdown as $category) {
            $total += $category['amount'];
        }

        $breakdown[] = [
            'id' => 'total',
            'name' => 'Total',
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'amount' => round($total, 2),
            'children' => [],
            'is_default' => true,
            'hasChildren' => false
        ];

        $response = [
            'currency_id' => $currencyId,
            'breakdown' => $breakdown,
            'order_id' => null,
            'is_approximate_amounts_calculation' => false
        ];

        return $response;
    }
}
