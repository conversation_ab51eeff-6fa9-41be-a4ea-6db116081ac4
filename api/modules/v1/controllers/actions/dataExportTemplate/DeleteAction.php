<?php

namespace api\modules\v1\controllers\actions\dataExportTemplate;

use common\models\customer\DataExportTemplate;
use common\models\DataExportRecurrent;
use yii\rest\Action;
use yii\web\MethodNotAllowedHttpException;
use yii\web\ServerErrorHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var DataExportTemplate $model */
        $model = $this->findModel($id);
        $countAutoExports = DataExportRecurrent::find()->where(['=', 'template_id', $id])->count();

        if ($countAutoExports > 0) {
            throw new MethodNotAllowedHttpException(
                \Yii::t('admin', "This template is used by one or more auto-exports. Please remove the template from the auto-exports to be able to delete it.")
            );
        }

        if ($model->delete() === false) {
            throw new ServerErrorHttpException(\Yii::t('admin', 'Failed to delete the object for unknown reason.'));
        }

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
