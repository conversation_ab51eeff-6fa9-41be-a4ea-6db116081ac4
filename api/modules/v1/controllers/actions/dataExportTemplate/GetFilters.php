<?php

namespace api\modules\v1\controllers\actions\dataExportTemplate;

use common\components\dataImportExport\export\exporter\ExporterFactory;
use yii\rest\Action;

class GetFilters extends Action
{
    public function run(): array
    {
        $handlerName = \Yii::$app->request->get('handler_name');
        $allowedFilters = \Yii::$app->request->post('fields');
        if (empty($handlerName)) {
            throw new \Exception('Handler name is required');
        }

        $factory = new ExporterFactory();
        $exporter = $factory->getExporter($handlerName);
        $templateStructure = $exporter->getTemplateStructure();

        $response = [];
        $required = [];

        $groupIds = array_combine(array_keys($templateStructure['groups']),
            range(1, count(array_keys($templateStructure['groups']))));

        $i = 1;
        foreach ($templateStructure['columns'] as $columnId => $column) {

            if (isset($column['is_required']) && $column['is_required'] === true) {
                $required[] = [
                    'id' => $i,
                    'active' => 1,
                    'field' => $columnId,
                    'options' => $column['options'],
                    'position' => $i * 100,
                    'title' => $column['title'],
                    'group' => $column['group'] ?? '',
                    'field_group_id' => $groupIds[$column['group']] ?? '',
                    'type' => $column['type'] ?? '',
                    'is_required' => $column['is_required']
                ];
                $i++;
                continue;
            }

            if (empty($column['type']) || empty($column['group'])) {
                continue;
            }

            $response[] = [
                'id' => $i,
                'active' => 1,
                'field' => $columnId,
                'options' => $column['options'],
                'position' => $i * 100,
                'title' => $column['title'],
                'group' => $column['group'],
                'field_group_id' => $groupIds[$column['group']] ?? '',
                'type' => $column['type'],
                'is_required' => $column['is_required'] ?? false
            ];
            $i++;
        }

        if (!empty($allowedFilters)) {
            $response = array_filter($response, fn($field) => in_array($field['field'], $allowedFilters) || $field['is_required']);
        }

        $response = array_merge($response, $required);

        return $response;
    }
}
