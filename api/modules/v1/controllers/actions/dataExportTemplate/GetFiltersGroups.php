<?php

namespace api\modules\v1\controllers\actions\dataExportTemplate;

use common\components\dataImportExport\export\exporter\ExporterFactory;
use yii\rest\Action;

class GetFiltersGroups extends Action
{
    /**
     * @throws \Exception
     */
    public function run(): array
    {
        $handlerName = \Yii::$app->request->get('handler_name');

        if (empty($handlerName)) {
            throw new \Exception('Handler name is required');
        }

        $factory = new ExporterFactory();
        $exporter = $factory->getExporter($handlerName);
        $templateStructure = $exporter->getTemplateStructure();

        $response = [];
        $groupId = 1;

        foreach ($templateStructure['groups'] as $groupKey => $groupData) {
            $groupFields = [];

            $i = 1;
            foreach ($templateStructure['columns'] as $columnId => $column) {
                if (empty($column['type']) || $column['group'] !== $groupKey) {
                    $i++;
                    continue;
                }

                $groupFields[] = [
                    'id' => $i,
                    'active' => 1,
                    'field' => $columnId,
                    'position' => $i * 100,
                    'title' => $column['title'],
                    'field_group_id' => $groupId
                ];
                $i++;
            }

            if (!empty($groupFields)) {
                $response[] = [
                    'id' => $groupId,
                    'title' => $groupData['title'],
                    'position' => $groupId * 100,
                    'exportFields' => $groupFields,
                    'active' => 1,
                ];
            }

            $groupId++;
        }

        return $response;
    }
}
