<?php

namespace api\modules\v1\controllers\actions\dataCompleteness;

use common\components\dataCompleteness\Calculator;
use common\models\customer\DataCompleteness;
use yii\rest\Action;

class WidgetAction extends Action
{
    public function run()
    {
        $calculator = new Calculator();
        /** @var DataCompleteness[] $factors */
        $factors = (new DataCompleteness)->search()->all();

        foreach ($factors as $factor) {
            $factor->setScenario('search');
        }

        return [
            'fill_percentage' => $calculator->getCompletenessPercents(),
            'completeness' => $factors
        ];
    }
}
