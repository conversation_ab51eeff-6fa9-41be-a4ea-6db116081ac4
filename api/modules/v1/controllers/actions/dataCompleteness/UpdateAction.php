<?php

namespace api\modules\v1\controllers\actions\dataCompleteness;

use common\components\dataCompleteness\Calculator;
use common\models\customer\DataCompleteness;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;

class UpdateAction extends Action
{
    public function run($id)
    {
        /* @var $model DataCompleteness */
        $model = $this->findModel($id);

        $model->setScenario('update');
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
        $model->save();

        return $model;
    }
}
