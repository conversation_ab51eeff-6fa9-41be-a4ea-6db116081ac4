<?php

namespace api\modules\v1\controllers\actions\productAggregatedSalesInfo;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\exporter\AggregatedSalesInfo;
use common\components\dataImportExport\export\ExportManager;
use common\components\dataImportExport\SupportedHandlers;
use common\models\customer\DataExport;
use common\models\customer\DataExportTemplate;
use yii\helpers\Inflector;
use yii\rest\Action;

class ExportAction extends Action
{
    public function run()
    {
        $filtersForm = new FiltersForm();
        $params = \Yii::$app->request->getQueryParams();

        $convertedParams = [];
        foreach ($params as $key => $value) {
            $camelCaseKey = lcfirst(Inflector::camelize($key));
            $convertedParams[$camelCaseKey] = $value;
        }

        $filtersForm->load($convertedParams, '');

        if (!$filtersForm->validate()) {
            return $filtersForm;
        }

        $exportTemplate = new DataExportTemplate();
        $exportTemplate->title = "Aggregated Sales Info " . date('d.m.Y H:i:s');
        $exportTemplate->fields = (new AggregatedSalesInfo())->getWhiteListedFields(new ExportConfig());
        $exportTemplate->criteria = [];
        $exportTemplate->extra_criteria = $params;
        $exportTemplate->format = 'csv';
        $exportTemplate->remove_after_export = true;
        $exportTemplate->handler_name = SupportedHandlers::HANDLER_AGGREGATED_SALES;
        $exportTemplate->save(false);

        $form = new DataExport();
        $form->load([
            'template_id' => $exportTemplate->id,
            'handler_name' => $exportTemplate->handler_name,
            'output_file_format' => $exportTemplate->format
        ], '');

        $exportManager = new ExportManager();

        $dataExport = $exportManager
            ->export(
                $form->handler_name,
                $form->output_file_format,
                $form->template_id
            );

        return $dataExport;
    }
}
