<?php

namespace api\modules\v1\controllers\actions\productAggregatedSalesInfo;

use yii\db\Query;
use yii\rest\Action;
use yii\db\Expression;
use api\modules\v1\controllers\actions\GroupedByTrait;
use api\modules\v1\components\mappers\DataMapperInterface;
use api\modules\v1\components\mappers\ProductsGroupByMapper;
use common\models\customer\clickhouse\ProductAggregatedSalesInfo;
use common\models\customer\clickhouse\ProxyProduct;
use common\components\core\db\dbManager\DbManager;

class GetGroupedByValue extends Action
{
    use GroupedByTrait;

    /**
     * Fields that is grouping by max() condition and should appear only depend on group by value.
     */
    protected const NON_DISPLAYABLE_IN_GROUP_VIEW_FIELDS = [
        'default' => [
            'product_title',
            'product_asin',
            'seller_sku',
            'marketplace_id',
            'seller_id',
            'product_manufacturer',
            'product_brand',
            'product_type',
            'product_stock_type',
            'product_parent_asin',
            'product_upc',
            'product_ean',
            'bsr_avg_curr',
        ],
        'product_parent_asin' => [
            'seller_sku',
            'marketplace_id',
            'product_manufacturer',
            'product_brand',
            'product_type',
            'product_stock_type',
            'product_parent_asin',
            'product_upc',
            'product_ean',
            'bsr_avg_curr',
        ]
    ];

    const SORT_ATTRIBUTES = [
        'group_value',
        'revenue_amount',
        'expenses_amount',
        'total_income',
        'total_expenses',
        'estimated_profit_amount',
        'orders',
        'units',
        'refunds',
        'margin',
        'roi',
        'markup',
        'ppc_costs',
        'amazon_fees',
        'expenses_amount_without_fees',
        'net_purchase_price',
        'promo',
        'group_items_count',
        'product_title',
        'product_stock_type',
        'product_asin',
        'product_ean',
        'product_upc',
        'product_isbn',
        'product_brand',
        'product_manufacturer',
        'product_type',
        'product_parent_asin',
        'tag_id',
        'offer_type',
        'product_adult',
        'bsr_avg_curr',
        'bsr_avg_prev_compare_percents'
    ];

    protected function modifyQueryForGrouping($baseQuery, string $groupByField, string $groupByName): Query
    {
        $baseSql = $baseQuery->createCommand()->getRawSql();

        $query = new Query();
        $query->from(['base' => "($baseSql)"])
            ->select([
                new Expression("$groupByField as group_value"),
                new Expression("'$groupByName' as group_by"),
                $groupByField === 'product_parent_asin'
                    ? "CASE WHEN max(pp.title) != '' AND max(pp.title) IS NOT NULL THEN max(pp.title) ELSE max(product_title) END as product_title"
                    : "max(product_title) as product_title",
                "max(seller_sku) as seller_sku",
                $groupByField === 'product_parent_asin'
                    ? "CASE WHEN max(pp.seller_id) != '' AND max(pp.seller_id) IS NOT NULL THEN max(pp.seller_id) ELSE max(seller_id) END as seller_id"
                    : "max(seller_id) as seller_id",
                'any(product_ean) as product_ean',
                'any(product_upc) as product_upc',
                'any(product_isbn) as product_isbn',
                'any(product_parent_asin) as product_parent_asin',
                'any(product_adult) as product_adult',
                $groupByField === 'product_stock_type'
                    ? 'product_stock_type'
                    : "max(product_stock_type) as product_stock_type",
                $groupByField === 'product_brand'
                    ? 'product_brand'
                    : "max(product_brand) as product_brand",
                $groupByField === 'product_asin'
                    ? 'product_asin'
                    : ($groupByField === 'product_parent_asin'
                        ? 'product_parent_asin as product_asin'
                        : "max(product_asin) as product_asin"),
                $groupByField === 'product_type'
                    ? 'product_type'
                    : "max(product_type) as product_type",
                $groupByField === 'product_manufacturer'
                    ? 'product_manufacturer'
                    : "max(product_manufacturer) as product_manufacturer",
                $groupByField === 'marketplace_id'
                    ? 'marketplace_id'
                    : "max(marketplace_id) as marketplace_id",
                $groupByField === 'product_parent_asin'
                    ? 'product_parent_asin'
                    : "max(product_parent_asin) as product_parent_asin",
                "toInt32(count(*)) as group_items_count",
                "toInt32(SUM(base.orders)) as orders", // Order Items N
                "toInt32(SUM(base.units)) as units", // Units N
                "round(SUM(base.revenue_amount), 2) as revenue_amount", // Revenue N
                "round(SUM(base.estimated_profit_amount), 2) as estimated_profit_amount", //Estimated margin N
                "round(SUM(base.expenses_amount), 2) as expenses_amount", // Expenses N
                "round(SUM(base.total_income), 2) as total_income", // total_income N
                "round(SUM(base.total_expenses), 2) as total_expenses", // total_expenses N
                "round(SUM(base.ppc_costs), 2) as ppc_costs", // Ads (PPC) N
                "round(SUM(base.amazon_fees), 2) as amazon_fees", // Amazon fees N
                "round(AVG(base.margin), 2) as margin", // Margin %
                "round(AVG(base.roi), 2) as roi", // ROI %
                "round(AVG(base.markup), 2) as markup", // Markup %
                "toInt32(SUM(base.refunds)) as refunds", // Refunds N
                "any(base.expenses_amount_without_fees) as expenses_amount_without_fees", // expenses_amount_without_fees N
                "any(base.net_purchase_price) as net_purchase_price", // net_purchase_price N
                "any(base.promo) as promo", // promo N
                "any(base.bsr_avg_curr) as bsr_avg_curr", // Refunds N
                "any(base.bsr_avg_prev_compare_percents) as bsr_avg_prev_compare_percents", // Refunds N
            ])
            ->groupBy([$groupByField]);

        // Add LEFT JOIN to proxy_product when grouping by product_parent_asin
        if ($groupByField === 'product_parent_asin') {
            /** @var DbManager $dbManager */
            $dbManager = \Yii::$app->dbManager;
            $proxyProductTableName = $dbManager->getClickhouseDbName(DbManager::DB_PREFIX_CUSTOMER) . '.proxy_product';

            $query->leftJoin(
                ['pp' => $proxyProductTableName],
                'pp.asin = base.product_parent_asin'
            );
        }

        return $query;
    }

    protected function getBaseSearchQuery(array $params): Query
    {
        return (new ProductAggregatedSalesInfo())->search($params);
    }

    protected function getMapper(): DataMapperInterface
    {
        return new ProductsGroupByMapper;
    }
}
