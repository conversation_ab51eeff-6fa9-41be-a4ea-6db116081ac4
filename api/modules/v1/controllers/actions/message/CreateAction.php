<?php

namespace api\modules\v1\controllers\actions\message;

use api\modules\v1\forms\message\CreateMessageForm;
use yii\rest\Action;

class CreateAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $form = new CreateMessageForm();
        $form->load(\Yii::$app->request->getBodyParams(), '');

        if ($form->validate()) {
            return $form->create();
        }

        return $form;
    }
}
