<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class DeleteAll extends Action
{
    use ProductCostsActionHelperTrait;

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $marketplaceId = \Yii::$app->request->getQueryParam('marketplace_id');
        $sellerId = \Yii::$app->request->getQueryParam('seller_id');
        $sellerSku = \Yii::$app->request->getQueryParam('seller_sku');
        $salesCategoryId = \Yii::$app->request->getQueryParam('sales_category_id');

        if (empty($marketplaceId) || empty($sellerId) || empty($sellerSku)) {
            throw new BadRequestHttpException("Missing required fields");
        }

        /** @var Product $product */
        $product = Product::find()->where([
            'marketplace_id' => $marketplaceId,
            'sku' => $sellerSku,
            'seller_id' => $sellerId
        ])->one();
        $this->checkIsAllowManageProductOrThrowException($product, $salesCategoryId);

        $productCostPeriods = ProductCostPeriod::find()
            ->where([
                'AND',
                ['=', 'marketplace_id', $marketplaceId],
                ['=', 'seller_id', $sellerId],
                ['=', 'seller_sku', $sellerSku],
                ['=', 'sales_category_id', $salesCategoryId]
            ])
        ;

        /** @var ProductCostPeriod[] $periods */
        foreach ($productCostPeriods->batch() as $periods) {
            foreach ($periods as $period) {
                $period->delete();
            }
        }

        $this->synchroniseGlobalMarketplaceIfNeed($product);

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
