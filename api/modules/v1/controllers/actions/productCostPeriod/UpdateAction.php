<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use common\models\customer\ProductCostPeriod;
use yii\db\ActiveRecord;
use yii\rest\Action;
use yii\web\ServerErrorHttpException;

class UpdateAction extends Action
{
    public function run($id)
    {
        /* @var $model ProductCostPeriod */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
        $model->isAllowManageOrThrowException();
        $model->save();

        return $model;
    }
}
