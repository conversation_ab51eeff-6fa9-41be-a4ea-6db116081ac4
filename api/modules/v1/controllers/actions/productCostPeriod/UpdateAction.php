<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\ProductCostPeriod;
use yii\db\ActiveRecord;
use yii\rest\Action;
use yii\web\ServerErrorHttpException;

class UpdateAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run($id)
    {
        /* @var $model ProductCostPeriod */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
        $product = $model->getProduct()->one();

        $this->checkIsAllowManageProductOrThrowException($product, $model->sales_category_id);
        $this->checkIsAllowManagePeriodOrThrowException($model, $product);

        $model->save();

        $this->synchroniseGlobalMarketplaceIfNeed($product);

        return $model;
    }
}
