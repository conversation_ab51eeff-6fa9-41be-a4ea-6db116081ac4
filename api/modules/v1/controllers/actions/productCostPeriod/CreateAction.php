<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class CreateAction extends Action
{
    public function run()
    {
        $productCostPeriod = new ProductCostPeriod();
        $productCostPeriod->load(\Yii::$app->request->post(), '');
        $productCostPeriod->source = ProductCostCategory::SOURCE_MANUAL;

        if (!$productCostPeriod->validate()) {
            return $productCostPeriod;
        }
        $productCostPeriod->isAllowManageOrThrowException();

        $transaction = ProductCostPeriod::getDb()->beginTransaction();

        try {
            $productCostPeriod->save();
            $leftPeriod = $productCostPeriod->getLeftPeriod($productCostPeriod->date_start);

            if (!empty($leftPeriod)) {
                /** @var ProductCostItem[] $items */
                $items = $leftPeriod['items'];

                foreach ($items as $itemToClone) {
                    $item = $itemToClone->makeDuplicate();
                    $item->product_cost_period_id = $productCostPeriod->id;
                    $item->save(false);
                }

                if ($leftPeriod->source === ProductCostCategory::SOURCE_REPRICER
                    && empty($leftPeriod->date_start)
                ) {
                    $leftPeriod->source = ProductCostCategory::SOURCE_MANUAL;
                    $productCostPeriod->source = ProductCostCategory::SOURCE_REPRICER;
                    $leftPeriod->save(false);
                    $productCostPeriod->save(false);
                }

                $leftPeriod->recalculateTotalAmount();
            }

            $productCostPeriod->recalculateTotalAmount();

            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return ProductCostPeriod::findOne($productCostPeriod->id);
    }
}
