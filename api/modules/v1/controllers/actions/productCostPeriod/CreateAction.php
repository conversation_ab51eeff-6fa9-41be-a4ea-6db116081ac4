<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;

class CreateAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run()
    {
        $productCostPeriod = new ProductCostPeriod();
        $productCostPeriod->load(\Yii::$app->request->post(), '');
        $productCostPeriod->source = ProductCostCategory::SOURCE_MANUAL;

        if (!$productCostPeriod->validate()) {
            return $productCostPeriod;
        }

        /** @var Product $product */
        $product = Product::find()->where([
            'marketplace_id' => $productCostPeriod->marketplace_id,
            'seller_id' => $productCostPeriod->seller_id,
            'sku' => $productCostPeriod->seller_sku
        ])->one();

        $this->checkIsAllowManageProductOrThrowException($product);

        $transaction = ProductCostPeriod::getDb()->beginTransaction();

        try {
            $productCostPeriod->save();
            $leftPeriod = $productCostPeriod->getLeftPeriod($productCostPeriod->date_start);

            if (!empty($leftPeriod)) {
                /** @var ProductCostItem[] $items */
                $items = $leftPeriod['items'];

                foreach ($items as $itemToClone) {
                    $item = $itemToClone->makeDuplicate();
                    $item->product_cost_period_id = $productCostPeriod->id;
                    $item->save(false);
                }

                if ($leftPeriod->source === ProductCostCategory::SOURCE_REPRICER
                    && empty($leftPeriod->date_start)
                ) {
                    $leftPeriod->source = ProductCostCategory::SOURCE_MANUAL;
                    $productCostPeriod->source = ProductCostCategory::SOURCE_REPRICER;
                    $leftPeriod->save(false);
                    $productCostPeriod->save(false);
                }

                $leftPeriod->recalculateTotalAmount();
            }

            $productCostPeriod->recalculateTotalAmount();

            $transaction->commit();

            $this->synchroniseGlobalMarketplaceIfNeed($product);
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return ProductCostPeriod::findOne($productCostPeriod->id);
    }
}
