<?php

namespace api\modules\v1\controllers\actions\productCostPeriod\traits;

use common\components\LogToConsoleTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostPeriod;
use common\models\Seller;

trait ProductCostsActionHelperTrait
{
    use LogToConsoleTrait;

    public function synchroniseGlobalMarketplaceIfNeed(Product $globalMarketplaceProduct = null): void
    {
        if (empty($globalMarketplaceProduct)) {
            return;
        }

        try {
            $productToProductSynchronizer = new \common\components\COGSync\ProductToProductSynchronizer();
            $productToProductSynchronizer->ensureGlobalMarketplaceAndSyncAllProductsWithIt(
                $globalMarketplaceProduct->seller_id,
                $globalMarketplaceProduct->sku,
                $globalMarketplaceProduct->marketplace_id
            );
        } catch (\Throwable $e) {
            $this->error($e);
        }
    }

    public function checkIsAllowManageProductOrThrowException(Product $product = null): void
    {
        if (empty($product)) {
            return;
        }

        $globalMarketplaceId = Seller::getProductCostsGlobalMarketplaceId($product->seller_id);

        if (!empty($globalMarketplaceId)
            && $product->marketplace_id !== $globalMarketplaceId
            && $product->is_enabled_sync_with_global_marketplace
        ) {
            throw new \Exception(
                \Yii::t('admin', "Only periods for global marketplace can be modified when synchronization with global marketplace enabled.")
            );
        }
    }

    public function checkIsAllowManagePeriodOrThrowException(
        ProductCostPeriod $productCostPeriod = null,
        Product $product = null
    ): void
    {
        if (empty($productCostPeriod)) {
            return;
        }

        if (empty($product)) {
            /** @var Product $product */
            $product = Product::find()
                ->where([
                    'marketplace_id' => $productCostPeriod->marketplace_id,
                    'seller_id' => $productCostPeriod->seller_id,
                    'sku' => $productCostPeriod->seller_sku
                ])
                ->one();
        }

        if (!$this->isAllowManageWhenEnabledSyncWithRepricer($product, $productCostPeriod)) {
            throw new \Exception(
                \Yii::t('admin', "Only historical periods can be modified when synchronization enabled.")
            );
        }
    }

    /**
     * Checks whether period can be managed based on enabled or disabled sync with repricer.
     *
     * @param Product $product
     * @param ProductCostPeriod $productCostPeriod
     * @return bool
     * @throws \DateMalformedStringException
     */
    protected function isAllowManageWhenEnabledSyncWithRepricer(Product $product, ProductCostPeriod $productCostPeriod): bool
    {
        if (!$product->is_enabled_sync_with_repricer || $product->repricer_is_deleted) {
            return true;
        }
        $currentPeriod = $productCostPeriod->getCurrentActivePeriod(ProductCostCategory::SOURCE_REPRICER);

        if (empty($currentPeriod)) {
            return true;
        }

        if ($productCostPeriod->id === $currentPeriod->id) {
            return false;
        }

        if (empty($currentPeriod->date_start)) {
            return true;
        }

        if (strtotime($productCostPeriod->date_start) >= (new \DateTime($currentPeriod->date_start))->setTime(0,0)->getTimestamp()) {
            return false;
        }

        return true;
    }
}