<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use common\models\customer\ProductCostPeriod;
use yii\rest\Action;
use yii\web\BadRequestHttpException;
use yii\web\ServerErrorHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        /** @var ProductCostPeriod $model */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }
        $model->isAllowManageOrThrowException();

        if ($model->delete() === false) {
            throw new ServerErrorHttpException(\Yii::t('admin', 'Failed to delete the object for unknown reason.'));
        }

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
