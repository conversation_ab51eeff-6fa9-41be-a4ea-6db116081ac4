<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\models\customer\Product;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;
use yii\web\ServerErrorHttpException;

class DeleteAction extends Action
{
    use ProductCostsActionHelperTrait;

    public function run($id)
    {
        /** @var ProductCostPeriod $model */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }

        /** @var Product $product */
        $product = $model->getProduct()->one();
        $this->checkIsAllowManageProductOrThrowException($product, $model->sales_category_id);
        $this->checkIsAllowManagePeriodOrThrowException($model, $product);

        if ($model->delete() === false) {
            throw new ServerErrorHttpException(\Yii::t('admin', 'Failed to delete the object for unknown reason.'));
        }

        $this->synchroniseGlobalMarketplaceIfNeed($product);

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
