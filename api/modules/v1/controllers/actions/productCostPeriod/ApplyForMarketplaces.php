<?php

namespace api\modules\v1\controllers\actions\productCostPeriod;

use api\modules\v1\controllers\actions\productCostPeriod\traits\ProductCostsActionHelperTrait;
use common\components\COGSync\GlobalMarketplaceService;
use common\models\customer\Product;
use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\customer\ProductCostPeriod;
use common\models\SalesCategory;
use common\models\Seller;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class ApplyForMarketplaces extends Action
{
    use ProductCostsActionHelperTrait;

    protected GlobalMarketplaceService $globalMarketplaceService;

    public function __construct($id, $controller, $config = [])
    {
        $this->globalMarketplaceService = \Yii::$container->get('globalMarketplaceService');
        parent::__construct($id, $controller, $config);
    }

    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $marketplaceId = \Yii::$app->request->getQueryParam('marketplace_id');
        $sellerId = \Yii::$app->request->getQueryParam('seller_id');
        $sellerSku = \Yii::$app->request->getQueryParam('seller_sku');
        $marketplaceIds = \Yii::$app->request->getQueryParam('target_marketplace_ids');
        $salesCategoryId = \Yii::$app->request->getQueryParam('sales_category_id');

        if (empty($marketplaceId) || empty($sellerId) || empty($sellerSku) || empty($marketplaceIds) || empty($salesCategoryId)) {
            throw new BadRequestHttpException("Missing required fields");
        }

        /** @var ProductCostPeriod[] $productCostPeriods */
        $productCostPeriods = ProductCostPeriod::find()->where([
            'marketplace_id' => $marketplaceId,
            'seller_id' => $sellerId,
            'seller_sku' => $sellerSku,
            'sales_category_id' => $salesCategoryId
        ])->all();

        /** @var Product $product */
        $product = Product::find()->where([
            'marketplace_id' => $marketplaceId,
            'sku' => $sellerSku,
            'seller_id' => $sellerId
        ])->one();

        $this->checkIsAllowManageProductOrThrowException($product, $salesCategoryId);

        $marketplaceIds = explode(',', $marketplaceIds);
        $marketplaceIds = $this->filterMarketplaceIds($marketplaceIds, $sellerId, $sellerSku);

        if (empty($marketplaceIds)) {
            return \Yii::$app->getResponse()->setStatusCode(200);
        }

        $this->removeExistingPeriods($sellerId, $sellerSku, $marketplaceIds, $salesCategoryId);
        $this->createDuplicates($productCostPeriods, $marketplaceIds);

        return \Yii::$app->getResponse()->setStatusCode(200);
    }

    private function removeExistingPeriods(string $sellerId, string $sellerSku, array $marketplaceIds, string $salesCategoryId): void
    {
        foreach ($marketplaceIds as $marketplaceId) {
            /** @var ProductCostPeriod[] $periodsToRemove */
            $periodsToRemove = ProductCostPeriod::find()->where([
                'marketplace_id' => $marketplaceId,
                'seller_id' => $sellerId,
                'seller_sku' => $sellerSku,
                'sales_category_id' => $salesCategoryId
            ])->all();
            if (empty($periodsToRemove)) {
                continue;
            }

            /** @var Product $product */
            $product = $periodsToRemove[0]->getProduct()->one();
            if ($product->stock_type === Product::STOCK_TYPE_FBA
                && !$product->is_multiple_stock_type
                && in_array($salesCategoryId, [SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS])
            ) {
                continue;
            }

            foreach ($periodsToRemove as $periodToRemove) {
                $periodToRemove->delete();
            }

            if (!empty($product)) {
                if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_TAXES) {
                    $product->vat = null;
                }
                if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS) {
                    $product->shipping_cost = null;
                }
                if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_OTHER_FEES) {
                    $product->other_fees = null;
                }
                if ($salesCategoryId === SalesCategory::CATEGORY_EXPENSES_COG) {
                    $product->buying_price = null;
                }
                $product->save(false);
            }
        }
    }

    private function createDuplicates(array $productCostPeriods, array $marketplaceIds) :void
    {
        // Need to sort by date_start, period with null date_start should be first to prevent error
        // when doing beforeSave action.
        usort($productCostPeriods, function ($a, $b) {
            return $a['date_start'] <=> $b['date_start'];
        });

        foreach ($marketplaceIds as $marketplaceId) {
            /** @var ProductCostPeriod[] $productCostPeriods */
            foreach ($productCostPeriods as $productCostPeriod) {
                /** @var Product $product */
                $product = Product::find()->where([
                    'marketplace_id' => $marketplaceId,
                    'sku' => $productCostPeriod->seller_sku,
                    'seller_id' => $productCostPeriod->seller_id
                ])->one();

                if ($product->stock_type === Product::STOCK_TYPE_FBA
                    && !$product->is_multiple_stock_type
                    && in_array($productCostPeriod->sales_category_id, [
                        SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
                    ])
                ) {
                    continue;
                }

                $periodDuplicate = $productCostPeriod->makeDuplicate();
                $periodDuplicate->marketplace_id = $marketplaceId;
                $periodDuplicate->source = ProductCostCategory::SOURCE_MANUAL;
                $periodDuplicate->save(false);

                /** @var ProductCostItem[] $productCostItems */
                $productCostItems = $productCostPeriod->getItems()->all();

                foreach ($productCostItems as $productCostItem) {
                    $itemDuplicate = $productCostItem->makeDuplicate();
                    $itemDuplicate->product_cost_period_id = $periodDuplicate->id;
                    $itemDuplicate->marketplace_currency_rate = null;
                    $itemDuplicate->marketplace_currency_id = null;
                    $itemDuplicate->save(false);
                }
            }
        }
    }

    /**
     * Filters marketplace ids by seller id and seller sku, removes those of them which can not be modified.
     *
     * @param array $marketplaceIds
     * @param string $sellerId
     * @param string $sellerSku
     * @return array
     */
    private function filterMarketplaceIds(array $marketplaceIds, string $sellerId, string $sellerSku): array
    {
        $globalMarketplaceId = $this->globalMarketplaceService->getGlobalMarketplaceId($sellerId, $sellerSku);

        return array_filter($marketplaceIds, function ($marketplaceId) use ($globalMarketplaceId, $sellerId, $sellerSku) {
            if ($marketplaceId === $globalMarketplaceId) {
                return false;
            }

            /** @var Product $product */
            $product = Product::find()
                ->where([
                    'marketplace_id' => $marketplaceId,
                    'seller_id' => $sellerId,
                    'sku' => $sellerSku
                ])
                ->one();

            if (empty($product)) {
                return false;
            }

            return !$product->is_enabled_sync_with_global_marketplace;
        });
    }
}
