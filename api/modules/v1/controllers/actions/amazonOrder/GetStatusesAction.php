<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use Selling<PERSON><PERSON><PERSON><PERSON><PERSON>\Model\OrdersV0\Order;
use yii\rest\Action;

class GetStatusesAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        return [
            Order::ORDER_STATUS_PENDING => Order::ORDER_STATUS_PENDING,
            Order::ORDER_STATUS_UNSHIPPED => Order::ORDER_STATUS_UNSHIPPED,
            Order::ORDER_STATUS_PARTIALLY_SHIPPED => Order::ORDER_STATUS_PARTIALLY_SHIPPED,
            Order::ORDER_STATUS_SHIPPED => Order::ORDER_STATUS_SHIPPED,
            Order::ORDER_STATUS_CANCELED => Order::ORDER_STATUS_CANCELED,
            Order::ORDER_STATUS_UNFULFILLABLE => Order::ORDER_STATUS_UNFULFILLABLE,
            Order::ORDER_STATUS_INVOICE_UNCONFIRMED => Order::ORDER_STATUS_INVOICE_UNCONFIRMED,
            Order::ORDER_STATUS_PENDING_AVAILABILITY => Order::ORDER_STATUS_PENDING_AVAILABILITY,
        ];
    }
}