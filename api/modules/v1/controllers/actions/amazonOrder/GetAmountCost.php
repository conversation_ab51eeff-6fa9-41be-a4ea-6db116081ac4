<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\ShippingCostAmount;
use common\components\currencyRate\CurrencyRateManager;
use common\components\services\order\TransferOrderService;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\TransactionExtendedView;
use common\models\SalesCategory;
use yii\db\Exception;
use yii\db\Query;
use yii\rest\Action;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class GetAmountCost extends Action
{
    /**
     * @throws NotFoundHttpException
     * @throws Exception|\Throwable
     */
    public function run(
        string $amazonOrderId,
        ?string $salesCategoryId = SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
    )
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $orderItems = AmazonOrderExtendedView::findAll(['order_id' => $amazonOrderId]);

        if (empty($orderItems)) {
            $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());
            $orderItems = AmazonOrderInProgressExtendedView::findAll([
                'order_id' => $amazonOrderId,
                'version' => $lastVersion
            ]);
        }

        if (empty($orderItems)) {
            throw new NotFoundHttpException();
        }

        switch ($salesCategoryId){
            case SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS:
                $costAmount = new ShippingCostAmount($salesCategoryId);
                break;
            default:
                $costAmount = new ShippingCostAmount($salesCategoryId);
        }

        [$costProductsAmount, $currencyId] = $costAmount->getCostAmounts($amazonOrderId, $orderItems);

        $response = [
            'currency_id' => $currencyId,
            'items' => $costProductsAmount
        ];

        return $response;
    }
}
