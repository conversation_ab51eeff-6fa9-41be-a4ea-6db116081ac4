<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\salesCategoryMapper\strategy\RevenueExpensesStrategy;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\services\order\TransferOrderService;
use common\components\treeStructureHelper\breakdown\BreakdownBuilder;
use common\components\treeStructureHelper\breakdown\BreakdownConfig;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\TransactionExtendedView;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\TransactionExtendedViewV1;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\Expression;
use yii\db\Query;
use yii\rest\Action;
use yii\web\NotFoundHttpException;

class GetExpensesBreakdownV1 extends Action
{
    public function run(
        string $amazonOrderItemId,
        string $currencyId
    ) {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $orderItem = AmazonOrderExtendedView::findOne(['order_item_id' => $amazonOrderItemId]);

        if (empty($orderItem)) {
            $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());
            $orderItem = AmazonOrderInProgressExtendedView::findOne([
                'order_item_id' => $amazonOrderItemId,
                'version' => $lastVersion
            ]);
        }

        if (empty($orderItem)) {
            throw new NotFoundHttpException();
        }

        $filtersForm = new FiltersForm();
        $filtersForm->amazonOrderId = $orderItem['order_id'];
        $filtersForm->sellerId = $orderItem['seller_id'];
        $filtersForm->sellerSku = $orderItem['seller_sku'];
        $filtersForm->currencyId = $currencyId;
        $filtersForm->salesCategoryStrategy = SalesCategoryStrategyFactory::STRATEGY_CUSTOM;


        $categoriesTransaction = $this->getCategoriesTransaction($orderItem['order_id'], $orderItem['seller_sku']);

        $profitResult = (new ProfitCalculator($filtersForm))->calc(
            DataSeriesStructureManager::PERIOD_TYPE_ALL,
            5,
            implode(TreeStructureHelper::PATH_SEPARATOR, $categoriesTransaction)
        );

        $expenses = $profitResult->salesCategories;

        $config = new BreakdownConfig();
        $config->maxDepth = 5;
        $config->onlyLastElements = true;
        $config->skipEmptyAmounts = true;
        $config->isExpensive = true;
        $config->skipTagIds = [SalesCategory::TAG_AMAZON_FEES, SalesCategory::TAG_PROMOTION, SalesCategory::TAG_REVENUE];
        $config->allowEmptyAmountsOnDepth = 1;
        $builder = new BreakdownBuilder();

        $breakdown = $builder->build($expenses, $config);

        $total = 0;

        foreach ($breakdown as $breakdownItem) {
            $total += $breakdownItem['amount'];
        }

        $breakdown[] = [
            'id' => 'total',
            'name' => 'Total',
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'amount' => round($total, 2),
            'children' => [],
            'is_default' => true,
            'hasChildren' => false
        ];

        $response = [
            'currency_id' => $currencyId,
            'is_approximate_amounts_calculation' => (bool)($orderItem['is_approximate_amounts_calculation'] ?? false),
            'order_id' => $orderItem['order_id'],
            'breakdown' => $breakdown
        ];

        return $response;
    }

    public function getCategoriesTransaction(string $amazonOrderId, string $sku) {
        $flatTree = TransactionExtendedViewV1::find()
            ->andWhere([
                'amazon_order_id' => $amazonOrderId,
                'seller_sku' => $sku
            ])
            ->andWhere(['<', 'amount', 0])
            ->asArray()
            ->all();

        $allCategories = [];
        foreach ($flatTree as $row) {
            $allCategories[] = $row['sales_category_depth_1'];
            $allCategories[] = $row['sales_category_depth_2'];
        }

        $allCategories = array_filter($allCategories);
        $allCategories = array_unique($allCategories);
        $allCategories = array_values($allCategories);

        return $allCategories;
    }
}
