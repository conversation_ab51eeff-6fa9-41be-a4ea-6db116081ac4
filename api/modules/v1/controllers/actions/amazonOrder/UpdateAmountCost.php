<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use api\modules\v1\forms\order\OrderEditAmountForm;
use common\components\clickhouse\materializedViews\DynamicTablesManager;
use common\components\clickhouse\ShippingCostAmount;
use common\components\services\order\TransferOrderService;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedViewV1;
use common\models\customer\TransactionExtendedViewV1;
use common\models\order\AmazonOrder;
use common\models\order\AmazonOrderItem;
use common\models\SalesCategory;
use Yii;
use yii\db\Exception;
use yii\db\Query;
use yii\rest\Action;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class UpdateAmountCost extends Action
{
    /**
     * @throws NotFoundHttpException
     * @throws Exception|\Throwable
     */
    public function run(
        string $amazonOrderId,
        ?string $salesCategoryId = SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS
    )
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        switch ($salesCategoryId){
            case SalesCategory::CATEGORY_EXPENSES_SHIPPING_COSTS:
                $costAmount = new ShippingCostAmount($salesCategoryId);
                break;
            default:
                $costAmount = new ShippingCostAmount($salesCategoryId);
        }

        $orderItemsCost = [];
        $dataOrderItemsCost = Yii::$app->request->post();

        $foundErrors = [];

        $currencyId = $dataOrderItemsCost['currency_id'];

        if (empty($currencyId)) {
            throw new BadRequestHttpException('Currency_id is required');
        }

        foreach ($dataOrderItemsCost['items'] as $index => $dataOrderItemCost) {
            $productCostItem = new OrderEditAmountForm();
            $productCostItem->load($dataOrderItemCost, '');
            $productCostItem->validate();

            $amazonOrderItemId = $dataOrderItemCost['order_item_id'];
            if ($productCostItem->hasErrors()) {
                $foundErrors[] = [
                    'index' => $index,
                    'errors' => $productCostItem->getErrors()
                ];
            }
            $amount = $dataOrderItemCost['amount'] ?? 0.00;
            if (!empty($amazonOrderItemId)) {
                $orderItemsCost[$amazonOrderItemId] = $amount;
            }
        }

        if (!empty($foundErrors)) {
            \Yii::$app->getResponse()->setStatusCode(422);
            return [
                'status' => 'error',
                'errors' => $foundErrors
            ];
        }

        if (empty($orderItemsCost)) {
            throw new \Exception('amazon_order_item_id is required');
        }

        $orderItem = AmazonOrderExtendedViewV1::findOne(['order_id' => $amazonOrderId]);
        $isAmazonOrderExtendedView = true;

        if (empty($orderItem)) {
            $isAmazonOrderExtendedView = false;
            $lastVersion = TransferOrderService::getLatestVersion(\Yii::$app->dbManager->getCustomerId());
            $orderItem = AmazonOrderInProgressExtendedViewV1::findOne([
                'order_id' => $amazonOrderId,
                'version' => $lastVersion
            ]);
        }

        if (empty($orderItem)) {
            throw new NotFoundHttpException();
        }

        [$transactions, $transactionOneBySKU] = $costAmount->generateOppositeTransactions($amazonOrderId);

        $newAmount = $orderItemsCost[$orderItem['order_item_id']];

        $transactionForCopy = $transactionOneBySKU[$orderItem['seller_sku']] ?? null;
        $transactions[] = $costAmount->generateNewCostTransactions(
            $orderItem,
            $currencyId,
            $newAmount,
            $transactionForCopy
        );

        Yii::$app->dbManager->setSellerId($orderItem['seller_id']);
        AmazonOrderItem::updateAll([
            'manual_shipping_cost' => $newAmount,
            'manual_shipping_cost_currency' => $currencyId,
        ], [
            'order_item_id' => $orderItem['order_item_id']
        ]);

        $costAmount->apply($transactions);

        AmazonOrder::updateAll([
            'has_manual_shipping_cost' => true,
        ], [
            'amazon_order_id' => $amazonOrderId
        ]);

        $dateOrderPurchase = (new \DateTime($orderItem['order_purchase_date']));
        $dateStart = $dateOrderPurchase->setTime(0, 0);
        $dateEnd = $dateOrderPurchase->setTime(23, 59, 59);

        $manager = new DynamicTablesManager();

        if($isAmazonOrderExtendedView) {
            $manager->rebuildPartialDynamicTable(new \common\components\clickhouse\materializedViews\views\AmazonOrderExtendedViewV1(true, $dateStart, $dateEnd), $dateOrderPurchase);
        } else {
            $manager->rebuildPartialDynamicTable(new \common\components\clickhouse\materializedViews\views\AmazonOrderInProgressExtendedViewV1(true, $dateStart, $dateEnd), $dateOrderPurchase);
        }

        $this->optimizeTable();

        return [
            'result' => 'successful'
        ];
    }

    /**
     * @throws \Exception
     */
    public function optimizeTable(): void
    {
        $dataBaseTableName = TransactionExtendedViewV1::tableName();
        $sql = "OPTIMIZE TABLE {$dataBaseTableName} FINAL";

        Yii::$app->dbManager
            ->getClickhouseCustomerDb()
            ->createCommand($sql)->execute();
    }
}
