<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use yii\db\Query;
use yii\rest\Action;
use yii\db\Expression;
use yii\db\ActiveQuery;
use api\modules\v1\controllers\actions\GroupedByTrait;
use api\modules\v1\components\mappers\OrdersGroupByMapper;
use api\modules\v1\components\mappers\DataMapperInterface;
use common\models\customer\clickhouse\AmazonOrderExtendedViewV1;

class GetGroupedByValue extends Action
{
    use GroupedByTrait;

    protected const NON_DISPLAYABLE_IN_GROUP_VIEW_FIELDS = [
        'default' => [
            'offer_type',
            'tag_id',
            'order_id',
            'seller_id',
            'seller_sku',
            'product_ean',
            'product_upc',
            'product_asin',
            'product_type',
            'order_item_id',
            'product_title',
            'product_brand',
            'marketplace_id',
            'product_stock_type',
            'product_parent_asin',
            'product_manufacturer',
            'currency_id',
            'order_status',
        ]
    ];

    protected const SORT_ATTRIBUTES = [
        'quantity',
        'group_value',
        'product_upc',
        'product_ean',
        'product_asin',
        'product_isbn',
        'product_type',
        'total_income',
        'product_adult',
        'product_title',
        'product_brand',
        'revenue_amount',
        'total_expenses',
        'expenses_amount',
        'promotion_amount',
        'quantity_refunded',
        'product_condition',
        'product_stock_type',
        'amazon_fees_amount',
        'product_parent_asin',
        'product_manufacturer',
        'estimated_profit_amount',
    ];

    protected function modifyQueryForGrouping(
        ActiveQuery $query,
        string $groupByField,
        string $groupByName
    ): Query {
        $baseSql = $query->createCommand()->getRawSql();

        $query = new Query();
        $query
            ->from(['base' => "($baseSql)"])
            ->select([
                new Expression("$groupByField as group_value"),
                new Expression("'$groupByName' as group_by"),
                'max(product_title) as product_title',
                'max(currency_id) as currency_id',
                'max(seller_sku) as seller_sku',
                'max(offer_type) as offer_type',
                'max(seller_id) as seller_id',
                'max(order_id) as order_id',
                'max(order_item_id) as order_item_id',
                'any(product_ean) as product_ean',
                'any(product_upc) as product_upc',
                'any(product_isbn) as product_isbn',
                'any(product_parent_asin) as product_parent_asin',
                'any(product_adult) as product_adult',
                $groupByField === 'order_status'
                    ? 'order_status'
                    : 'max(order_status) as order_status',
                $groupByField === 'product_stock_type'
                    ? 'product_stock_type'
                    : 'max(product_stock_type) as product_stock_type',
                $groupByField === 'product_brand'
                    ? 'product_brand'
                    : 'max(product_brand) as product_brand',
                $groupByField === 'product_asin'
                    ? 'product_asin'
                    : 'max(product_asin) as product_asin',
                $groupByField === 'product_type'
                    ? 'product_type'
                    : 'max(product_type) as product_type',
                $groupByField === 'product_manufacturer'
                    ? 'product_manufacturer'
                    : 'max(product_manufacturer) as product_manufacturer',
                $groupByField === 'marketplace_id'
                    ? 'marketplace_id'
                    : 'max(marketplace_id) as marketplace_id',
                'toInt32(count(*)) as group_items_count',
                'round(SUM(base.revenue_amount), 2) as revenue_amount',
                'round(SUM(base.estimated_profit_amount), 2) as estimated_profit_amount',
                'round(SUM(base.expenses_amount), 2) as expenses_amount',
                'round(SUM(base.total_income), 2) as total_income',
                'round(SUM(base.total_expenses), 2) as total_expenses',
                'round(SUM(base.amazon_fees_amount), 2) as amazon_fees_amount',
                'toInt32(SUM(base.quantity)) as quantity',
                'toInt32(SUM(base.quantity_refunded)) as quantity_refunded',
                'round(SUM(base.item_price), 2) as item_price',
                'round(SUM(base.promotion_amount), 2) as promotion_amount',
            ])
            ->groupBy([$groupByField]);

        return $query;
    }

    protected function getBaseSearchQuery(array $params): ActiveQuery
    {
        return (new AmazonOrderExtendedViewV1)->search($params);
    }

    protected function getMapper(): DataMapperInterface
    {
        return new OrdersGroupByMapper;
    }
}
