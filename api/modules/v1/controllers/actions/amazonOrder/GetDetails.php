<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use api\modules\v1\forms\widget\FiltersForm;
use common\components\currencyRate\CurrencyRateManager;
use common\components\salesMetricCalculator\CategoriesStructureManager;
use common\components\salesMetricCalculator\DataSeriesStructureManager;
use common\components\salesMetricCalculator\ProfitCalculator;
use common\components\services\order\TransferOrderService;
use common\components\treeStructureHelper\breakdown\BreakdownBuilder;
use common\components\treeStructureHelper\breakdown\BreakdownConfig;
use common\models\customer\clickhouse\AmazonOrderExtendedView;
use common\models\customer\clickhouse\AmazonOrderInProgressExtendedView;
use common\models\customer\clickhouse\traits\ExtraFiltersTrait;
use common\models\finance\EventPeriod;
use SellingPartnerApi\Model\OrdersV0\Order;
use yii\db\Query;
use yii\rest\Action;
use yii\web\NotFoundHttpException;

class GetDetails extends Action
{
    protected CurrencyRateManager $currencyRateManager;

    use ExtraFiltersTrait;

    public function __construct($id, $controller, $config = [])
    {
        $this->currencyRateManager = new CurrencyRateManager();
        parent::__construct($id, $controller, $config);
    }

    public function run(string $amazonOrderId, string $currencyId)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $query = AmazonOrderExtendedView::find()->where(['order_id' => $amazonOrderId]);
        $query2 = clone $query;
        $query2->from(AmazonOrderInProgressExtendedView::tableName());
        $this->applyClickhouseLatestVersion($query2);

        $amazonOrderItems = AmazonOrderExtendedView::find()
            ->from(['o' => $query->union($query2, true)])
            ->asArray()
            ->all()
        ;

        if (count($amazonOrderItems) === 0) {
            throw new NotFoundHttpException();
        }

        $updatedAt1 = strtotime($amazonOrderItems[0]['last_update_date']);
        $updatedAt2 = strtotime(EventPeriod::getLastMovedToClickhouseDate($amazonOrderId));
        $updatedAt = max($updatedAt1, $updatedAt2);

        $isApproximateAmounts = false;

        foreach ($amazonOrderItems as $amazonOrderItem) {
            if ($amazonOrderItem['is_approximate_amounts_calculation']) {
                $isApproximateAmounts = true;
                break;
            }
        }

        $response = [
            'currency_id' => $currencyId,
            'is_approximate_amounts_calculation' => $isApproximateAmounts,
            'updated_at' => date('Y-m-d H:i:s', $updatedAt),
            'summary' => $this->buildSummaryBlock($amazonOrderItems, $currencyId),
            'products' => $this->buildProductsBlock($amazonOrderItems, $currencyId),
            'breakdown' => $this->buildBreakdownBlock($amazonOrderItems, $currencyId)
        ];

        return $response;
    }

    protected function buildProductsBlock(array $amazonOrderItems, string $currencyId): array
    {
        $products = [];

        foreach ($amazonOrderItems as $amazonOrderItem) {
            $quantity = $amazonOrderItem['quantity'];

            $unitPrice = null;

            if ($amazonOrderItem['order_status'] !== Order::ORDER_STATUS_CANCELED) {
                $itemPrice = $this->currencyRateManager->convert(
                    $amazonOrderItem['item_price'],
                    $amazonOrderItem['currency_id'],
                    $currencyId,
                    new \DateTime($amazonOrderItem['order_purchase_date'])
                );
                $itemPrice -= $this->currencyRateManager->convert(
                    $amazonOrderItem['promotion_amount_eur'],
                    CurrencyRateManager::BASE_CURRENCY,
                    $currencyId,
                    new \DateTime($amazonOrderItem['order_purchase_date'])
                );

                $unitPrice = ($quantity === 0)
                    ? round($itemPrice, 2)
                    : round($itemPrice / $quantity, 2);
            }

            $products[] = [
                'title' => $amazonOrderItem['product_title'],
                'asin' => $amazonOrderItem['product_asin'],
                'sku' => $amazonOrderItem['seller_sku'],
                'unit_price' => $unitPrice,
                'quantity' => $quantity > 0 ? $quantity : null,
                'currency_id' => $amazonOrderItem['currency_id'],
                'stock_type' => $amazonOrderItem['product_stock_type'] ?: null,
                'status' => $amazonOrderItem['order_status'],
            ];
        }
        return $products;
    }

    protected function buildSummaryBlock(array $amazonOrderItems, string $currencyId): array
    {
        $salesTotal = 0;

        foreach ($amazonOrderItems as $amazonOrderItem) {
            if ($amazonOrderItem['order_status'] === Order::ORDER_STATUS_CANCELED) {
                continue;
            }

            $salesTotal += $amazonOrderItem['item_price'];
            $salesTotal -= $this->currencyRateManager->convert(
                $amazonOrderItem['promotion_amount_eur'],
                CurrencyRateManager::BASE_CURRENCY,
                $amazonOrderItem['currency_id'],
                new \DateTime($amazonOrderItem['order_purchase_date'])
            );
        }

        $salesTotalBlock = null;

        if ($salesTotal > 0) {
            $salesTotalBlock = [
                'original' => [
                    'currency_id' => $amazonOrderItems[0]['currency_id'],
                    'amount' => round($salesTotal, 2)
                ],
                'chosen' => [
                    'currency_id' => $currencyId,
                    'amount' => round($this->currencyRateManager->convert(
                        $salesTotal,
                        $amazonOrderItems[0]['currency_id'],
                        $currencyId,
                        new \DateTime($amazonOrderItems[0]['order_purchase_date'])
                    ), 2)
                ],
            ];
        }

        return [
            'seller_id' => $amazonOrderItems[0]['seller_id'],
            'order_id' => $amazonOrderItems[0]['order_id'],
            'offer_type' => $amazonOrderItems[0]['offer_type'],
            'purchase_date' => $amazonOrderItems[0]['order_purchase_date'],
            'status' => $amazonOrderItems[0]['order_status'],
            'marketplace_id' => $amazonOrderItems[0]['marketplace_id'],
            'sales_total' => $salesTotalBlock
        ];
    }

    protected function buildBreakdownBlock(array $amazonOrderItems, string $currencyId): array
    {
        $isCanceled = $amazonOrderItems[0]['order_status'] === Order::ORDER_STATUS_CANCELED;
        $filtersForm = new FiltersForm();

        // No need to retrieve eny results in case of canceled order status.
        if ($isCanceled) {
            $filtersForm->amazonOrderId = 'not_existing_order_id';
        } else {
            $filtersForm->amazonOrderId = $amazonOrderItems[0]['order_id'];
        }
        $filtersForm->currencyId = $currencyId;
        $profitResult = (new ProfitCalculator($filtersForm))->calc(
            DataSeriesStructureManager::PERIOD_TYPE_ALL,
            5,
        );

        $config = new BreakdownConfig();
        $config->maxDepth = 2;
        $config->onlyLastElements = true;
        $config->skipEmptyAmounts = true;
        $config->allowEmptyAmountsOnDepth = 1;
        $builder = new BreakdownBuilder();

        $breakdown = $builder->build($profitResult->salesCategories, $config);
        $breakdown['estimated_profit'] = [
            'id' => 'estimated_profit',
            'name' => 'Estimated margin',
            'is_default' => true,
            'type' => CategoriesStructureManager::TYPE_MONEY,
            'amount' => $profitResult->netProfit,
            'children' => [],
            "hasChildren" => false
        ];
        $breakdown['margin'] = [
            'id' => 'margin',
            'name' => 'Margin',
            'is_default' => true,
            'type' => CategoriesStructureManager::TYPE_PERCENTS,
            'amount' => $profitResult->margin,
            'children' => [],
            "hasChildren" => false
        ];
        $breakdown['roi'] = [
            'id' => 'roi',
            'name' => 'ROI',
            'is_default' => true,
            'type' => CategoriesStructureManager::TYPE_PERCENTS,
            'amount' => $profitResult->roi,
            'children' => [],
            "hasChildren" => false
        ];

        return $breakdown;
    }
}
