<?php

namespace api\modules\v1\controllers\actions\amazonOrder;

use common\models\customer\clickhouse\AmazonOrder;
use yii\db\Expression;
use yii\rest\Action;

class GetLastUpdateDate extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        return [
            'updatedAt' => AmazonOrder::find()->select(new Expression('max(last_update_date)'))->scalar()
        ];
    }
}