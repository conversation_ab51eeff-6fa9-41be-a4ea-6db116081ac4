<?php

namespace api\modules\v1\controllers\actions\userToken;

use common\models\UserToken;
use yii\rest\Action;

class MyAction extends Action
{
    public function run()
    {
        /** @var UserToken $identity */
        $identity = \Yii::$app->user->identity;

        return [
            'access_token' => $identity->access_token,
            'authUser' => $identity->getAuthUser(),
            'customer_id' => $identity->customer_id,
            'user_id' => $identity->user_id,
            'language' => $identity->getLanguage(),
        ];
    }
}
