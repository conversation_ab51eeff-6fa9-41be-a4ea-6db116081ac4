<?php

namespace api\modules\v1\controllers\actions\healthCheck;

use common\components\core\db\dbManager\DbManager;
use common\components\rabbitmq\MessagesSender;
use common\components\ServerHealthChecker;
use common\models\customer\IndirectCost;
use yii\rest\Action;
use yii\web\Response;

class CheckAction extends Action
{
    protected MessagesSender $messagesSender;
    protected DbManager $dbManager;

    public function __construct($id, $controller, $config = [])
    {
        parent::__construct($id, $controller, $config);
    }

    public function run()
    {
        $checker = new ServerHealthChecker();

        $result = $checker->getReport();

        \Yii::$app->response->format = Response::FORMAT_JSON;

        return $result;
    }

}
