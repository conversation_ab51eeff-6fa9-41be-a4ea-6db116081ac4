<?php

namespace api\modules\v1\controllers\actions\indirectCostType;

use common\models\customer\IndirectCostType;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        /** @var IndirectCostType $model */
        $model = $this->findModel($id);

        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id, $model);
        }

        $countCosts = $model->getIndirectCosts()->count();

        if ($countCosts > 0) {
            throw new BadRequestHttpException(
                \Yii::t('admin', 'Cost type has been already in use and can not be deleted')
            );
        }

        $model->delete();
        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
