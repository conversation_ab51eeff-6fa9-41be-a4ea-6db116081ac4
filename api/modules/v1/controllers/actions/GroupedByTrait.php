<?php

namespace api\modules\v1\controllers\actions;

use Yii;
use yii\db\Query;
use yii\db\ActiveQuery;
use yii\data\ActiveDataProvider;
use api\modules\v1\forms\widget\GroupByForm;
use api\modules\v1\forms\widget\FiltersForm;
use common\components\core\db\dbManager\DbManager;
use api\modules\v1\components\mappers\DataMapperInterface;

trait GroupedByTrait
{
    private DataMapperInterface $mapper;

    public function beforeRun(): bool
    {
        $this->initMapper();

        return true;
    }

    public function run()
    {
        $params = $this->getInputParams();

        $groupByForm = (new GroupByForm())
            ->setAvailableAttributes(
                $this->mapper->availableKeys()
            );

        foreach ([$groupByForm, new FiltersForm()] as $form) {
            $form->load($params, '');
            if (!$form->validate()) {
                return $form;
            }
        }

        $query = $this->getBaseSearchQuery($params);

        $groupByField = $this->mapper->getAttributeByInput($groupByForm->groupBy);

        $query = $this->modifyQueryForGrouping(
            $query,
            $groupByField,
            $groupByForm->groupBy,
        );

        $dataProvider = $this->prepareDataProvider($query, $params);

        $data = $dataProvider->getModels();

        $this->hideNonDisplayable($data, $groupByField);

        return [
            'totalCount' => $dataProvider->getTotalCount(),
            'pageCount' => $dataProvider->pagination->getPageCount(),
            'currentPage' => $dataProvider->pagination->getPage() + 1,
            'pageSize' => $dataProvider->pagination->getPageSize(),
            'data' => $data,
        ];
    }

    private function getInputParams(): array
    {
        return Yii::$app->request->getQueryParams();
    }

    public function prepareDataProvider(Query $query, array $params): ActiveDataProvider
    {
        /** @var DbManager $dbManager */
        $dbManager = Yii::$app->dbManager;

        return new ActiveDataProvider([
            'query' => $query,
            'db' => $dbManager->getClickhouseCustomerDb(),
            'pagination' => [
                'params' => $params,
                'pageSizeLimit' => [10, 100],
                'pageSizeParam' => 'pageSize',
                'defaultPageSize' => 25,
            ],
            'sort' => [
                'defaultOrder' => [
                    'group_value' => SORT_ASC,
                ],
                'params' => $params,
                'attributes' => static::SORT_ATTRIBUTES,
            ],
        ]);
    }

    private function hideNonDisplayable(array &$data, string $groupByField)
    {
        // Setting null to those fields that can not be displayed in grouped view when there is at least 2 items in such group.
        foreach ($data as &$item) {
            if ($item['group_items_count'] <= 1) {
                continue;
            }

            $fieldsFilters = self::NON_DISPLAYABLE_IN_GROUP_VIEW_FIELDS[$groupByField]
                ?? self::NON_DISPLAYABLE_IN_GROUP_VIEW_FIELDS['default'];


            foreach ($fieldsFilters as $field) {
                if ($field == $groupByField ) {
                    continue;
                }

                $item[$field] = null;
            }
        }
    }

    public function initMapper(): DataMapperInterface
    {
        return $this->mapper = $this->getMapper();
    }

    abstract function getBaseSearchQuery(array $params): Query;

    abstract function modifyQueryForGrouping(ActiveQuery $query, string $groupByField, string $groupByName): Query;

    abstract function getMapper(): DataMapperInterface;
}
