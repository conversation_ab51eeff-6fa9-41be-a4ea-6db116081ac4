<?php

namespace api\modules\v1\controllers\actions\productCostCategory;

use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostItem;
use common\models\UserToken;
use yii\rest\Action;
use yii\web\BadRequestHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var ProductCostCategory $model */
        $model = $this->findModel($id);

        if (!$model->isDeletable()) {
            throw new \Exception(\Yii::t('admin', 'This cost type can not be deleted'));
        }

        $countItems = ProductCostItem::find()->where([
            'product_cost_category_id' => $model->id
        ])->count();

        if ($countItems > 0) {
            throw new BadRequestHttpException(
                \Yii::t('admin', 'Cost type has been already in use and can not be deleted')
            );
        }

        $model->delete();
        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
