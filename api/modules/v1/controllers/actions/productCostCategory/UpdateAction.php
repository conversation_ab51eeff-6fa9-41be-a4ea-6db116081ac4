<?php

namespace api\modules\v1\controllers\actions\productCostCategory;

use common\models\customer\ProductCostCategory;
use common\models\customer\ProductCostPeriod;
use yii\rest\Action;

class UpdateAction extends Action
{
    public function run($id)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var ProductCostCategory $model */
        $model = $this->findModel($id);

        if (!$model->isEditable()) {
            throw new \Exception(\Yii::t('admin', 'This cost type can not be modified'));
        }

        $model->load(\Yii::$app->getRequest()->getBodyParams(), '');
        $model->save();

        return $model;
    }
}
