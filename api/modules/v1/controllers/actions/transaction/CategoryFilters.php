<?php

namespace api\modules\v1\controllers\actions\transaction;

use common\components\clickhouse\materializedViews\views\OrderBasedTransactionExtendedViewV1;
use common\components\core\db\dbManager\DbManager;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyFactory;
use common\components\salesCategoryMapper\strategy\SalesCategoryStrategyInterface;
use common\components\treeStructureHelper\TreeStructureHelper;
use common\models\customer\IndirectCostType;
use common\models\customer\ProductCostCategory;
use common\models\customer\SalesCategoryExtendedView;
use common\models\FinanceEventCategory;
use common\models\SalesCategory;
use yii\caching\TagDependency;
use yii\db\Expression;
use yii\rest\Action;

class CategoryFilters extends Action
{
    protected const TRANSACTION_TYPE_DEPTH = 51;

    protected SalesCategoryStrategyFactory $salesCategoryStrategyFactory;

    public function __construct($id, $controller, $config = [])
    {
        $this->salesCategoryStrategyFactory = new SalesCategoryStrategyFactory();

        parent::__construct($id, $controller, $config);
    }

    public function run()
    {
        $params = \Yii::$app->request->get();
        $salesCategoryStrategyType = $params['sales_category_strategy'] ?? SalesCategoryStrategyFactory::DEFAULT_STRATEGY;
        $strategy = $this->salesCategoryStrategyFactory->getStrategyByType($salesCategoryStrategyType);

        $salesCategories = $this->getAllSalesCategories($strategy);
        $salesCategories = $this->groupByName($salesCategories);
        $chosenIds = [
            $params['sales_category_depth_1'] ?? [],
            $params['sales_category_depth_2'] ?? [],
            $params['sales_category_depth_3'] ?? [],
            $params['sales_category_depth_4'] ?? [],
            $params['transaction_type'] ?? []
        ];
        $salesCategories = $this->fillIsSelectable($salesCategories, $chosenIds, $strategy);
        usort($salesCategories, function ($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });
        $groupedByDepth = $this->groupByDepth($salesCategories);
        $groupedByDepth['transaction_type'] = $groupedByDepth['sales_category_depth_' . self::TRANSACTION_TYPE_DEPTH] ?? [];
        unset($groupedByDepth['sales_category_depth_' . self::TRANSACTION_TYPE_DEPTH]);

        return $groupedByDepth;
    }

    protected function fillIsSelectable(array $salesCategories, array $chosenIdsGroups): array
    {
        $maxChosenDepth = 0;
        $chosenPathGroups = [];

        foreach ($chosenIdsGroups as $k => $chosenIds) {
            if (empty($chosenIds)) {
                continue;
            }
            $paths = [];
            $chosenDepth = null;

            foreach ($chosenIds as $chosenId) {
                $path = $salesCategories[$chosenId]['path'] ?? null;
                if (empty($path)) {
                    continue;
                }
                $paths = array_merge($paths, explode(',', $path));
                $chosenDepth = $salesCategories[$chosenId]['depth'];
                $maxChosenDepth = max($maxChosenDepth, $chosenDepth);
            }

            $chosenPathGroups[$chosenDepth] = $paths;
        }

        foreach ($salesCategories as $k => &$salesCategory) {
            $canBeSelected = true;
            $canBeDisplayed = true;

            foreach ($chosenPathGroups as $chosenPathDepth => $chosenPaths) {
                $isMatched = false;

                foreach ($chosenPaths as $chosenPath) {
                    $salesCategoryPaths = explode(',', $salesCategory['path']);

                    foreach ($salesCategoryPaths as $salesCategoryPath) {
                        if ($this->isPathIncludesAnotherPath($chosenPath, $salesCategoryPath)) {
                            $isMatched = true;
                            break 2;
                        }
                    }
                }

                if (!$isMatched && $salesCategory['depth'] != $maxChosenDepth) {
                    $canBeSelected = false;
                }

                if ($salesCategory['depth'] > $chosenPathDepth && !$isMatched) {
                    $canBeDisplayed = false;
                    break;
                }
            }

            $salesCategory['can_be_selected'] = $canBeSelected;
            if (!$canBeDisplayed) {
                unset($salesCategories[$k]);
            }
        }

        return $salesCategories;
    }

    protected function isPathIncludesAnotherPath(string $path1, string $path2): bool
    {
        return false !== strpos($path1, $path2) || false !== strpos($path2, $path1);
    }

    private function getPathsByTransactionType(string $transactionType, SalesCategoryStrategyInterface $strategy): array
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        static $result = [];

        if (array_key_exists($transactionType, $result)) {
            return $result[$transactionType];
        }

        $plusPostfix = FinanceEventCategory::PLUS_POSTFIX;
        $zeroPlusPostfix = FinanceEventCategory::PLUS_ZERO_POSTFIX;

        $query = FinanceEventCategory::find()
            ->from(FinanceEventCategory::tableName() . ' fec')
            ->select([
                'sc.path',
                new Expression("
                    CASE 
                    WHEN (
                            fec.path like 'Adjustment.%'
                            OR (
                                fec.path LIKE '%{$plusPostfix}'
                                AND
                                fec.path NOT LIKE '%{$zeroPlusPostfix}'
                                AND 
                                fec.path LIKE '%.FeeAmount%'
                                AND
                                fec.path NOT LIKE 'Refund.%'
                                AND   
                                fec.path NOT LIKE 'Chargeback.%'
                                AND   
                                fec.path NOT LIKE 'GuaranteeClaim.%'              
                            )
                        )
                        THEN 'adjustment_type'
                    WHEN fec.path like 'Retrocharge.%'
                        THEN 'retrocharge_type'
                    WHEN fec.path like 'Custom.%'
                        THEN 'manual_type'
                    ELSE 'standard_type'
                    END as transaction_type
                ")
            ])
            ->distinct()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->leftJoin(SalesCategory::tableName() . ' sc', 'sc.id = ' . $strategy->getSalesCategoryIdColumnName());

        $query2 = SalesCategoryExtendedView::find()
            ->select(
                [
                    'path',
                    new Expression("'manual_type' as transaction_type")
                ]
            )
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->where([
                'AND',
                ['=', 'is_manual', 't'],
                ['=', 'type', $strategy->getType()]
            ])
        ;
        $estimatedQuery = \common\models\customer\OrderBasedTransactionExtendedViewV1::find()
            ->distinct()
            ->select([
                'sales_category_path as path',
                'transaction_type'
            ])
            ->where(['transaction_type' => $transactionType])
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )->asArray()->all();

        $records = $query->union($query2)->asArray()->all($dbManager->getCustomerDb());
        $records = array_merge($records, $estimatedQuery);
        $result = [
            'manual_type' => []
        ];

        foreach ($records as $record) {
            if (empty($result[$record['transaction_type']])) {
                $result[$record['transaction_type']] = [];
            }

            $result[$record['transaction_type']][] = $record['path'] . '|transaction_type';
        }

        return $result[$transactionType];
    }

    private function groupByName(array $salesCategories): array
    {
        $groupedByName = [];

        foreach ($salesCategories as $salesCategory) {
            $key = implode('_', [$salesCategory['name'], $salesCategory['depth']]);
            if (empty($groupedByName[$key])) {
                $groupedByName[$key] = $salesCategory;
                continue;
            }

            $groupedByName[$key]['id'] .= '-' . $salesCategory['id'];
            $groupedByName[$key]['path'] .= ',' . $salesCategory['path'];
        }

        foreach ($groupedByName as $k => $salesCategory) {
            $groupedByName[$salesCategory['id']] = $salesCategory;
            unset($groupedByName[$k]);
        }

        return $groupedByName;
    }

    private function groupByDepth(array $salesCategories): array
    {
        $groupedByDepth = [];

        foreach ($salesCategories as $salesCategory) {
            $key = 'sales_category_depth_' . $salesCategory['depth'];
            unset($salesCategory['depth']);
            unset($salesCategory['path']);
            $groupedByDepth[$key][] = $salesCategory;
        }
        ksort($groupedByDepth);

        return $groupedByDepth;
    }

    private function getAllSalesCategories(SalesCategoryStrategyInterface $strategy): array
    {
        static $result = null;

        if (null !== $result) {
            return $result;
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;


        $salesCategories = SalesCategoryExtendedView::find()
            ->select([
                'id',
                'name',
                'depth',
                'path'
            ])
            ->where([
                'AND',
                ['=', 'type', $strategy->getType()],
                ['=', 'is_visible', 't'],
                ['<=', 'depth', 4]
            ])
            ->orderBy('sort_order')
            ->asArray()
            ->cache(
                \Yii::$app->params['tagDependencyCacheDuration'],
                new TagDependency(['tags' => [
                    SalesCategory::COMMON_CACHE_TAG,
                    ProductCostCategory::COMMON_CACHE_TAG,
                    IndirectCostType::COMMON_CACHE_TAG
                ]])
            )
            ->all($dbManager->getCustomerDb());

        $salesCategories[] = [
            'id' => 'adjustment_type',
            'name' => 'Adjustment',
            'can_be_selected' => true,
            'path' => implode(',', $this->getPathsByTransactionType('adjustment_type', $strategy)),
            'depth' => self::TRANSACTION_TYPE_DEPTH
        ];
        $salesCategories[] = [
            'id' => 'retrocharge_type',
            'name' => 'Retrocharge',
            'can_be_selected' => true,
            'path' => implode(',', $this->getPathsByTransactionType('retrocharge_type', $strategy)),
            'depth' => self::TRANSACTION_TYPE_DEPTH
        ];
        $salesCategories[] = [
            'id' => 'standard_type',
            'name' => 'Standard',
            'can_be_selected' => true,
            'path' => implode(',', $this->getPathsByTransactionType('standard_type', $strategy)),
            'depth' => self::TRANSACTION_TYPE_DEPTH
        ];
        $salesCategories[] = [
            'id' => 'manual_type',
            'name' => 'Manual',
            'can_be_selected' => true,
            'path' => implode(',', $this->getPathsByTransactionType('manual_type', $strategy)),
            'depth' => self::TRANSACTION_TYPE_DEPTH
        ];
        $salesCategories[] = [
            'id' => 'estimated',
            'name' => 'Estimated',
            'can_be_selected' => true,
            'path' => implode(',', $this->getPathsByTransactionType('estimated', $strategy)),
            'depth' => self::TRANSACTION_TYPE_DEPTH
        ];

        // Converting to usable array with id as a key.
        foreach ($salesCategories as $k => $salesCategory) {
            if (!$salesCategory['is_manual']) {
                $salesCategory['name'] = \Yii::t('admin', $salesCategory['name']);
            }
            $salesCategories[$salesCategory['id']] = $salesCategory;
            unset($salesCategories[$k]);
        }

        return $salesCategories;
    }
}
