<?php

namespace api\modules\v1\controllers\actions\dataImportExport;

use common\components\dataImportExport\export\ExportConfig;
use common\components\dataImportExport\export\ExportManager;
use common\models\customer\DataExport;
use yii\rest\Action;

class ExportAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $form = new DataExport();
        $form->load(\Yii::$app->request->getBodyParams(), '');

        if (!$form->validate()) {
            return $form;
        }

//        $notFinishedExport = DataExport::find()
//            ->where([
//                'not in',
//                'status', [
//                    DataExport::STATUS_FINISHED,
//                    DataExport::STATUS_NO_ITEMS,
//                ],
//            ])
//            ->andWhere([
//                'handler_name' => $form->handler_name,
//            ])
//            ->one();
//
//        if (null !== $notFinishedExport) {
//            return $notFinishedExport;
//        }

        $exportManager = new ExportManager();
        $dataExport = $exportManager
            ->export(
                $form->handler_name,
                $form->output_file_format,
                $form->template_id
            );

        return $dataExport;
    }
}
