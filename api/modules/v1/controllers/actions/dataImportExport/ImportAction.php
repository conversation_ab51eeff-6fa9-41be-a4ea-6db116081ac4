<?php

namespace api\modules\v1\controllers\actions\dataImportExport;

use common\components\dataImportExport\import\ImportManager;
use common\models\customer\DataImport;
use yii\rest\Action;
use yii\web\UploadedFile;

class ImportAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $dataImport = new DataImport();
        $dataImport->setScenario('create');
        $dataImport->load(\Yii::$app->request->getBodyParams(), '');
        $dataImport->file = UploadedFile::getInstanceByName('file');

        if (!$dataImport->validate()) {
            return $dataImport;
        }

        $importManager = new ImportManager();
        if (!empty($dataImport->file->tempName)) {
            $dataImport = $importManager->upload($dataImport->file->tempName, $dataImport->handler_name);
        } else {
            $dataImport = $importManager->save($dataImport->file_url, $dataImport->handler_name);
        }

        $dataImport->setScenario('search');

        return $dataImport;
    }
}
