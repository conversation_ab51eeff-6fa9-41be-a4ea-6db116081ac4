<?php

namespace api\modules\v1\controllers\actions\dataImportExport;

use api\modules\v1\forms\export\ExportTemplateForm;
use common\components\dataImportExport\import\importer\ImporterFactory;
use common\components\dataImportExport\import\templateGenerator\TemplateGenerator;
use Yii;
use yii\rest\Action;

class DownloadTemplateAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        $importerFactory = new ImporterFactory();

        $form = new ExportTemplateForm();

        $form->load(\Yii::$app->request->get(), '');

        if (!$form->validate()) {
            return $form;
        }

        $dataImporter = $importerFactory->getImporter(\Yii::$app->request->get('handler_name'));

        $templateData = $dataImporter->getExampleData();
        $templateGenerator = new TemplateGenerator(
            $templateData['columns'] ?? [],
            $templateData['data'] ?? [],
            $templateData['groups'] ?? []
        );
        $tmpFile = Yii::getAlias('@runtime') . '/' . md5(time() . rand(0, PHP_INT_MAX)) . ".xlsx";
        $xls = $templateGenerator->generate();
        $templateGenerator->saveAsXlsx($xls, $tmpFile);

        Yii::$app->response->headers->add('Access-Control-Expose-Headers', 'Content-Disposition');
        Yii::$app->response->sendFile($tmpFile, 'template-' . date('Ymd-His') . '.xlsx');

        unlink($tmpFile);
    }
}
