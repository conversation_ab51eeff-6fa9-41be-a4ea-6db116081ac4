<?php

namespace api\modules\v1\controllers\actions\dataImportExport;

use common\models\customer\BaseDataImportExport;
use yii\rest\Action;
use yii\web\MethodNotAllowedHttpException;

class DeleteAction extends Action
{
    public function run($id)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /* @var $model BaseDataImportExport */
        $model = $this->findModel($id);

        if ($model->status !== BaseDataImportExport::STATUS_NEW) {
            throw new MethodNotAllowedHttpException(\Yii::t('admin', 'You can delete object only with status NEW'));
        }
        $model->delete();

        \Yii::$app->getResponse()->setStatusCode(204);
    }
}
