<?php

namespace api\modules\v1\controllers\actions\dataImportExport;

use api\modules\v1\forms\product\StopBulkEditProcessForm;
use common\models\customer\DataImport;
use yii\rest\Action;

class StopBulkEditProcessAction extends Action
{
    public function run()
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var DataImport $model */
        $model = $this->findModel(\Yii::$app->request->getBodyParam('id'));
        $form = new StopBulkEditProcessForm($model);
        $form->load(\Yii::$app->request->getBodyParams(), '');

        if ($form->validate()) {
            return $form->stop();
        }

        return $form;
    }
}
