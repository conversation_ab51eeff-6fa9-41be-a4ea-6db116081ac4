<?php

namespace api\modules\v1\controllers\actions\repricerEvent;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\clickhouse\RepricerEvent;
use yii\rest\Action;

class GetGroupedByDate extends Action
{
    public function run(int $customerId, string $dateStart, string $dateEnd, string $period = 'month')
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $params = \Yii::$app->request->getQueryParams();

        $calcType = $params['calc_type'] ?? 'distinct';
        $groupDayExpr = "formatDateTime(day, '%Y-%m-%d')";

        if ($calcType === 'amount') {
            $select = [
                "$groupDayExpr AS date",
                "SUMIf(amount, offer_type='B2B') AS b2b",
                "SUMIf(amount, offer_type='B2C') AS b2c",
                "SUM(amount) AS total",
            ];
        } else {
            // calcType = 'distinct'
            $select = [
                "$groupDayExpr AS date",
                "uniqExactIf(product_id, offer_type='B2B') AS b2b",
                "uniqExactIf(product_id, offer_type='B2C') AS b2c",
                "uniqExactIf(product_id, offer_type='B2B') + uniqExactIf(product_id, offer_type='B2C') AS total",
            ];
        }

        try {
            $eventsQuery = RepricerEvent::find()
                ->select($select)
                ->groupBy([$groupDayExpr])
                ->where([
                    'AND',
                    ['>=', 'day', (new \DateTime($dateStart))->format('Y-m-d')],
                    ['<=', 'day', (new \DateTime($dateEnd))->format('Y-m-d')],
                ]);

            if (!empty($params['offer_type'])) {
                $eventsQuery->andWhere(['offer_type' => $params['offer_type']]);
            }

            $dailyData = $eventsQuery->asArray()->all($dbManager->getClickhouseCustomerDb());
        } catch (\Exception $e) {
            $dailyData = [];
            \Yii::error($e);
        }

        if ($period === 'day') {
            return array_map(function ($row) {
                $row['b2b']   = (int)$row['b2b'];
                $row['b2c']   = (int)$row['b2c'];
                $row['total'] = (int)$row['total'];
                return $row;
            }, $dailyData);
        }

        $groupedByMonth = [];

        foreach ($dailyData as $row) {
            $row['b2b']   = (int)$row['b2b'];
            $row['b2c']   = (int)$row['b2c'];
            $row['total'] = (int)$row['total'];

            $monthKey = substr($row['date'], 0, 7); // "2025-02"

            if (!isset($groupedByMonth[$monthKey])) {
                $groupedByMonth[$monthKey] = [
                    'date'  => $monthKey,
                    'b2b'   => 0,
                    'b2c'   => 0,
                    'total' => 0,
                ];
            }

            $groupedByMonth[$monthKey]['b2b']   += $row['b2b'];
            $groupedByMonth[$monthKey]['b2c']   += $row['b2c'];
            $groupedByMonth[$monthKey]['total'] += $row['total'];
        }

        return array_values($groupedByMonth);
    }
}
