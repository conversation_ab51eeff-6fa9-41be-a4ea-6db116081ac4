<?php

namespace api\modules\v1\controllers\actions\repricerEvent;

use common\components\core\db\dbManager\DbManager;
use common\models\customer\clickhouse\RepricerEvent;
use yii\rest\Action;

class GetTotal extends Action
{
    public function run(int $customerId, string $dateStart, string $dateEnd)
    {
        if ($this->checkAccess) {
            call_user_func($this->checkAccess, $this->id);
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        $params = \Yii::$app->request->getQueryParams();

        $calcType = $params['calc_type'] ?? 'distinct';
        $groupDayExpr = "formatDateTime(day, '%Y-%m-%d')";

        if ($calcType === 'amount') {
            $select = [
                "$groupDayExpr AS day_date",
                "seller_id",
                "SUMIf(amount, offer_type='B2B') AS b2b",
                "SUMIf(amount, offer_type='B2C') AS b2c",
                "SUM(amount) AS total",
            ];
        } else {
            $select = [
                "$groupDayExpr AS day_date",
                "seller_id",
                "uniqExactIf(product_id, offer_type='B2B') AS b2b",
                "uniqExactIf(product_id, offer_type='B2C') AS b2c",
                "uniqExactIf(product_id, offer_type='B2B') + uniqExactIf(product_id, offer_type='B2C') AS total",
            ];
        }

        try {
            $eventsQuery = RepricerEvent::find()
                ->select($select)
                ->groupBy([
                    "$groupDayExpr",
                    "seller_id",
                ])
                ->where([
                    'AND',
                    ['>=', 'day', (new \DateTime($dateStart))->format('Y-m-d')],
                    ['<=', 'day', (new \DateTime($dateEnd))->format('Y-m-d')],
                ]);

            if (!empty($params['offer_type'])) {
                $eventsQuery->andWhere(['offer_type' => $params['offer_type']]);
            }

            $rows = $eventsQuery
                ->asArray()
                ->all($dbManager->getClickhouseCustomerDb());
        } catch (\Exception $e) {
            $rows = [];
            \Yii::error($e);
        }

        $sellerStats = [];

        foreach ($rows as $row) {
            $sellerId = $row['seller_id'];
            $b2b = (int)$row['b2b'];
            $b2c = (int)$row['b2c'];
            $total = (int)$row['total'];

            if (!isset($sellerStats[$sellerId])) {
                $sellerStats[$sellerId] = [
                    'seller_id' => $sellerId,
                    'b2b' => 0,
                    'b2c' => 0,
                    'total' => 0,
                ];
            }

            $sellerStats[$sellerId]['b2b'] += $b2b;
            $sellerStats[$sellerId]['b2c'] += $b2c;
            $sellerStats[$sellerId]['total'] += $total;
        }

        $total = [
            'b2b' => 0,
            'b2c' => 0,
            'total' => 0,
        ];

        foreach ($sellerStats as $stat) {
            $total['b2b'] += $stat['b2b'];
            $total['b2c'] += $stat['b2c'];
            $total['total'] += $stat['total'];
        }

        $sellers = array_values($sellerStats);

        return [
            'total' => $total,
            'sellers' => $sellers,
        ];
    }
}
