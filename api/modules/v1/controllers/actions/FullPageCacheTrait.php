<?php

namespace api\modules\v1\controllers\actions;

use common\components\core\db\dbManager\DbManager;
use common\components\CustomerComponent;
use yii\caching\TagDependency;

trait FullPageCacheTrait
{
    protected function getFromCache()
    {
        $cacheKey = $this->getCacheKey();
        $cache = \Yii::$app->cache;
        return $cache->get($cacheKey);
    }

    protected function saveToCache($data, int $duration = null): void
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $cacheKey = $this->getCacheKey();
        $cache = \Yii::$app->cache;
        $cache->set(
            $cacheKey,
            $data,
            $duration,
            new TagDependency(['tags' => [
                'full_page_cache',
                'full_page_cache_customer_' . $dbManager->getCustomerId(),
                'full_page_cache_customer_' . $dbManager->getCustomerId() . '_' . static::class
            ]])
        );
    }

    protected function getCacheKey(): string
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $params = \Yii::$app->request->getQueryParams();
        /** @var CustomerComponent $customerComponent */
        $customerComponent = \Yii::$app->customerComponent;
        $params['user_timezone'] = $customerComponent->getUserTimezoneName();

        return implode('_', [
            'full_page_cache',
            'customer_' . $dbManager->getCustomerId(),
            static::class,
            md5(serialize($params))
        ]);
    }
}