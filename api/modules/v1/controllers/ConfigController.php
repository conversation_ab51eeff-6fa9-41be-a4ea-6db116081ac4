<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\modules\v1\controllers\actions\seller\GlobalMarketplaceSettingsAction;
use api\modules\v1\controllers\actions\seller\UpdateGlobalMarketplaceSettingsAction;
use common\components\Permissions;
use common\models\CustomerConfig;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use api\components\controllers\Controller;
use yii\rest\Action;

/**
 * @OA\Get(
 *     path="/v1/config/global-marketplace-setting",
 *     summary="Retrieves global marketplace settings for all sellers of a customer",
 *     tags={"Seller"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Response(
 *          response=200,
 *          description="Global marketplace settings",
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                   type="array",
 *                   @OA\Items(
 *                       @OA\Property(property="seller_id", type="string"),
 *                       @OA\Property(property="global_marketplace_id", type="string", nullable=true, description="ID global marketplace"),
 *                       @OA\Property(property="is_enabled_cost_of_goods_sync", type="boolean", description="Enabled cost of goods sync"),
 *                       @OA\Property(property="is_enabled_other_fees_sync", type="boolean", description="Enabled other fees sync"),
 *                       @OA\Property(property="is_enabled_fbm_shipping_cost_sync", type="boolean", description="Enabled FBM shipping cost sync")
 *                   )
 *              ),
 *          )
 *      ),
 *      @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *      @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *      @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\POST(
 *     path="/v1/config/update-global-marketplace-setting",
 *     summary="Update global marketplace settings for a seller",
 *     tags={"Seller"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *        name="seller_id",
 *        in="path",
 *        description="Seller Id",
 *        required=true,
 *        @OA\Schema(
 *          type="string",
 *        ),
 *     ),
 *     @OA\RequestBody(
 *          @OA\JsonContent(
 *              @OA\Property(property="global_marketplace_id", type="string", nullable=true, description="Id global marketplace"),
 *              @OA\Property(property="is_enabled_cost_of_goods_sync", type="boolean", description="Enabled cost of goods sync"),
 *              @OA\Property(property="is_enabled_other_fees_sync", type="boolean", description="Enabled other fees sync"),
 *              @OA\Property(property="is_enabled_fbm_shipping_cost_sync", type="boolean", description="Enabled FBM shipping cost sync")
 *          )
 *      ),
 *      @OA\Response(
 *          response=200,
 *          description="Updated seller settings",
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  @OA\Property(property="seller_id", type="string"),
 *                  @OA\Property(property="global_marketplace_id", type="string", nullable=true, description="ID global marketplace"),
 *                  @OA\Property(property="is_enabled_cost_of_goods_sync", type="boolean", description="Enabled cost of goods sync"),
 *                  @OA\Property(property="is_enabled_other_fees_sync", type="boolean", description="Enabled other fees sync"),
 *                  @OA\Property(property="is_enabled_fbm_shipping_cost_sync", type="boolean", description="Enabled FBM shipping cost sync")
 *              )
 *          )
 *      ),
 *      @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *      @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *      @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *      @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */
class ConfigController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = CustomerConfig::class;

    public bool $isCustomerRelated = true;

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['global-marketplace-setting'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['update-global-marketplace-setting'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions(): array
    {
        $actions = parent::actions();
        unset(
            $actions['update'],
            $actions['delete'],
            $actions['view'],
            $actions['create']
        );

        $actions['global-marketplace-setting'] = [
            'class' => GlobalMarketplaceSettingsAction::class,
            'modelClass' => $this->modelClass,
        ];

        $actions['update-global-marketplace-setting'] = [
            'class' => UpdateGlobalMarketplaceSettingsAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
