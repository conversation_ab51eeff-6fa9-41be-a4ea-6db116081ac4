<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\customerDashboard\GetKeyPerformance;
use api\modules\v1\controllers\actions\customerDashboard\GetOrderHistory;
use api\modules\v1\controllers\actions\customerDashboard\GetOrdersAndSales;
use api\modules\v1\controllers\actions\customerDashboard\GetOrdersHeatmap;
use api\modules\v1\controllers\actions\customerDashboard\GetOrdersMarketplaces;
use common\components\Permissions;
use yii\base\Model;
use yii\filters\AccessControl;

/**
 * @OA\Get(
 *     path="/v1/bas-customer-dashboard/orders-and-sales",
 *     summary="Retrieves the collection of orders and sales data.",
 *     tags={"CustomerDashboard"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *        name="customerId",
 *        in="query",
 *        description="Customer ID",
 *        required=true,
 *        @OA\Schema(type="integer", format="int64")
 *     ),
 *     @OA\Parameter(
 *         name="date_start",
 *         in="query",
 *         description="Start date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Parameter(
 *         name="date_end",
 *         in="query",
 *         description="End date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Parameter(
 *         name="currency_id",
 *         in="query",
 *         description="Currency id",
 *         required=true,
 *         @OA\Schema(type="string", example="EUR")
 *     ),
 *     @OA\Parameter(
 *         name="period_type",
 *         in="query",
 *         description="Group by period type",
 *         required=true,
 *         @OA\Schema(type="string", enum={"hour", "day", "week", "month", "year"})
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         example="B2B",
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace id (comma separated values can be sent)",
 *         required=false,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller id (comma separated values can be sent)",
 *         required=false,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="seller_sku",
 *         in="query",
 *         description="Seller SKU (use coma as separator to send several values)",
 *         required=false,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="currency_id",
 *                 type="string",
 *                 example="EUR"
 *             ),
 *             @OA\Property(
 *                 property="data_series",
 *                 type="object",
 *                 @OA\AdditionalProperties(
 *                     type="object",
 *                     @OA\Property(
 *                         property="orders",
 *                         type="integer",
 *                         example=1078
 *                     ),
 *                     @OA\Property(
 *                         property="product_sales",
 *                         type="number",
 *                         format="float",
 *                         example=15698.89
 *                     )
 *                 )
 *             ),
 *             example={
 *                 "currency_id": "EUR",
 *                 "data_series": {
 *                     "2025-08-10 00:00:00": {
 *                         "orders": 384,
 *                         "product_sales": 5991.63
 *                     },
 *                     "2025-08-11 01:00:00": {
 *                         "orders": 371,
 *                         "product_sales": 5872.7
 *                     },
 *                     "2025-08-12 02:00:00": {
 *                         "orders": 331,
 *                         "product_sales": 5596.59
 *                     }
 *                 }
 *             }
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

 /**
 * @OA\Get(
 *     path="/v1/bas-customer-dashboard/orders-heatmap",
 *     summary="Retrieves the collection of orders heatmap data.",
 *     tags={"CustomerDashboard"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer ID",
 *         required=true,
 *         @OA\Schema(type="integer", format="int64")
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         example="B2B",
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(
 *                     property="date",
 *                     type="string",
 *                     format="date",
 *                     description="Date of the orders",
 *                     example="2024-07-22"
 *                 ),
 *                 @OA\Property(
 *                     property="hours",
 *                     type="array",
 *                     description="List of hourly orders",
 *                     @OA\Items(
 *                         type="object",
 *                         @OA\Property(
 *                             property="hour",
 *                             type="integer",
 *                             description="Hour of the day",
 *                             example=2
 *                         ),
 *                         @OA\Property(
 *                             property="orders",
 *                             type="integer",
 *                             description="Number of orders",
 *                             example=532
 *                         )
 *                     )
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/bas-customer-dashboard/orders-marketplaces",
 *     summary="Retrieves the collection of orders on marketplaces data.",
 *     tags={"CustomerDashboard"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer ID",
 *         required=true,
 *         @OA\Schema(type="integer", format="int64")
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(
 *                     property="value",
 *                     type="integer",
 *                     example="17"
 *                 ),
 *                 @OA\Property(
 *                     property="label",
 *                     type="string",
 *                     example="Amazon DE"
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/bas-customer-dashboard/order-history",
 *     summary="Retrieves the collection of order history data.",
 *     tags={"CustomerDashboard"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer ID",
 *         required=true,
 *         @OA\Schema(type="integer", format="int64")
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="labels",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="marketplace_id",
 *                         type="string",
 *                         example="A13V1IB3VIYZZH"
 *                     ),
 *                     @OA\Property(
 *                         property="title",
 *                         type="string",
 *                         example="Amazon FR"
 *                     )
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="period",
 *                 type="array",
 *                 @OA\Items(
 *                     type="string",
 *                     format="date",
 *                     example="2024-05-30"
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="from",
 *                 type="string",
 *                 format="date",
 *                 example="2024-05-30"
 *             ),
 *             @OA\Property(
 *                 property="to",
 *                 type="string",
 *                 format="date",
 *                 example="2024-06-12"
 *             ),
 *             @OA\Property(
 *                 property="data",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="date",
 *                         type="string",
 *                         format="date",
 *                         example="2024-05-30"
 *                     ),
 *                     @OA\Property(
 *                         property="marketplace_id",
 *                         type="string",
 *                         example="A13V1IB3VIYZZH"
 *                     ),
 *                     @OA\Property(
 *                         property="orders",
 *                         type="integer",
 *                         example=0
 *                     )
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/bas-customer-dashboard/dynamic",
 *     summary="Retrieves the collection of dynamic data.",
 *     tags={"CustomerDashboard"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer ID",
 *         required=true,
 *         @OA\Schema(type="integer", format="int64")
 *     ),
 *     @OA\Parameter(
 *         name="offer_type",
 *         in="query",
 *         description="Offer type",
 *         required=false,
 *         @OA\Schema(type="string", enum={"B2B", "B2C"})
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="orders",
 *                 type="object",
 *                 @OA\Property(
 *                     property="count_orders_dyn",
 *                     type="number",
 *                     format="float",
 *                     example=9.44
 *                 ),
 *                 @OA\Property(
 *                     property="count_orders",
 *                     type="integer",
 *                     example=1078
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="selling_price",
 *                 type="object",
 *                 @OA\Property(
 *                     property="avg_sales_dyn",
 *                     type="number",
 *                     format="float",
 *                     example=-13.32
 *                 ),
 *                 @OA\Property(
 *                     property="avg_sales",
 *                     type="number",
 *                     format="float",
 *                     example=16.5
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="profit",
 *                 type="object",
 *                 @OA\Property(
 *                     property="sum_profit_dyn",
 *                     type="number",
 *                     format="float",
 *                     example=-53.16
 *                 ),
 *                 @OA\Property(
 *                     property="sum_profit",
 *                     type="number",
 *                     format="float",
 *                     example=-9289.38
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="turnover",
 *                 type="object",
 *                 @OA\Property(
 *                     property="sum_sales_dyn",
 *                     type="number",
 *                     format="float",
 *                     example=11
 *                 ),
 *                 @OA\Property(
 *                     property="sum_sales",
 *                     type="number",
 *                     format="float",
 *                     example=43235
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */
class BasCustomerDashboardController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['orders-heatmap', 'orders-marketplaces', 'order-history', 'dynamic'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::REPRICER_DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['orders-and-sales'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::REPRICER_DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['orders-heatmap'] = [
            'class' => GetOrdersHeatmap::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['orders-marketplaces'] = [
            'class' => GetOrdersMarketplaces::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['order-history'] = [
            'class' => GetOrderHistory::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['dynamic'] = [
            'class' => GetKeyPerformance::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['orders-and-sales'] = [
            'class' => GetOrdersAndSales::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        return $actions;
    }
}
