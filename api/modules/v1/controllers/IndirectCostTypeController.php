<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\modules\v1\controllers\actions\indirectCostType\DeleteAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use api\components\controllers\Controller;
use common\models\customer\IndirectCostType;
use yii\filters\AccessControl;


/**
 * IndirectCostTypeController implements the REST actions for IndirectCostType model.
* @OA\Get(path="/v1/indirect-cost-type",
*   summary="Retrieves the collection of IndirectCostType resources.",
*   tags={"IndirectCostType"},
*   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="name",
*         in="query",
*         description="Name",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="created_at",
*         in="query",
*         description="Created At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="updated_at",
*         in="query",
*         description="Updated At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of IndirectCostType resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCostType"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Post(path="/v1/indirect-cost-type",
*   summary="Create IndirectCostType resource",
*   tags={"IndirectCostType"},
*   security={{"oauth2":{}}},
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
        *	 	            @OA\Property(property="name", type="string")
*          )
*        ),
*
*
*   @OA\Response(
*     response=200,
*     description="IndirectCostType resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCostType"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="/v1/indirect-cost-type",
*   summary="Update the IndirectCostType resource",
*   tags={"IndirectCostType"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
 *   @OA\Parameter(
 *       name="customerId",
 *       in="query",
 *       description="Customer ID",
 *       required=false,
 *       @OA\Schema(type = "integer")
 *   ),
*   @OA\Response(
*     response=200,
*     description="IndirectCostType resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/IndirectCostType"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="/v1/indirect-cost-type/{id}",
*   summary="Delete IndirectCostType resource",
*   tags={"IndirectCostType"},
*   security={{"oauth2":{}}},
 *            @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
* )
*/
class IndirectCostTypeController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = IndirectCostType::class;
    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::INDIRECT_COSTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::INDIRECT_COSTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
