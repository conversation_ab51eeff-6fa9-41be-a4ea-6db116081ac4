<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\productAggregatedSalesInfo\ExportAction;
use common\components\Permissions;
use api\components\controllers\Controller;
use common\models\customer\clickhouse\ProductAggregatedSalesInfo;
use OpenApi\Annotations as OA;
use yii\filters\AccessControl;

/**
 * @OA\Get(path="/v1/product-aggregated-sales-info",
 *   summary="Retrieves the collection of aggregated product info",
 *   tags={"Product"},
 *   security={{"oauth2":{}}},
 *      @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *      @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *      @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *      @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *      @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *      @OA\Parameter(
 *          name="date_start",
 *          in="query",
 *          description="Start date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *          name="date_end",
 *          in="query",
 *          description="End date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *     @OA\Parameter(
 *         name="marketplace_seller_ids",
 *         in="query",
 *         description="Pairs of marketplace and seller ids in json format. Pass 'all' as sellerId to see transactions on sellers level, pass 'global' or nothing to see transactions on global level",
 *         required=false,
 *         @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *    @OA\Parameter(
 *       name="currency_id",
 *       in="query",
 *       required=false,
 *       @OA\Schema(
 *           type="string",
 *           example="EUR"
 *       ),
 *       description="Currency of all money related columns"
 *   ),
 * @OA\Parameter(
 *      name="product_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=11623107
 *      ),
 *      description="Unique identifier of the product"
 *  ),
 *  @OA\Parameter(
 *      name="marketplace_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="A1PA6795UKMFR9"
 *      ),
 *      description="Marketplace ID"
 *  ),
 *  @OA\Parameter(
 *      name="seller_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="A15GEJR91SS7OC"
 *      ),
 *      description="Seller ID"
 *  ),
 *  @OA\Parameter(
 *      name="seller_sku",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="NAYPE_2.3_6.16_0407_B0010V5MBA"
 *      ),
 *      description="Seller Stock Keeping Unit (SKU)"
 *  ),
 *  @OA\Parameter(
 *      name="currency_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="EUR"
 *      ),
 *      description="Currency ID (e.g., EUR, USD)"
 *  ),
 *  @OA\Parameter(
 *      name="product_asin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="B0010V5MBA"
 *      ),
 *      description="Amazon Standard Identification Number (ASIN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_brand",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="adidas"
 *      ),
 *      description="Brand of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_ean",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="0885730845370"
 *      ),
 *      description="European Article Number (EAN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_upc",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="885730845370"
 *      ),
 *      description="Universal Product Code (UPC)"
 *  ),
 *  @OA\Parameter(
 *      name="product_isbn",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example=null
 *      ),
 *      description="International Standard Book Number (ISBN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_title",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="adidas Victory League Eau de Toilette – Erfrischend-zitrisches Herren Parfüm für den selbstbewussten, sportlichen Mann – 1 x 50 ml"
 *      ),
 *      description="Title of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_manufacturer",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="Coty Beauty Germany GmbH"
 *      ),
 *      description="Manufacturer of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_type",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="PERSONAL_FRAGRANCE"
 *      ),
 *      description="Type of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_parent_asin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="APTUIPPE36G",
 *          example=null
 *      ),
 *      description="Parent ASIN"
 *  ),
 *  @OA\Parameter(
 *      name="product_stock_type",
 *      in="query",
 *      required=false,
 *      @OA\Schema(type="string", enum={"FBM", "FBA"}),
 *      description="Stock type"
 *  ),
 *  @OA\Parameter(
 *      name="product_adult",
 *      in="query",
 *      description="Product Adult",
 *      required=false,
 *      @OA\Schema(type="integer", enum={1,0})
 *  ),
 *  @OA\Parameter(
 *       name="tag_id",
 *       in="query",
 *       description="Tag id (comma separated values can be sent)",
 *       required=false,
 *       @OA\Schema(type = "string", example = "1,2")
 *  ),
 *  @OA\Parameter(
 *       name="estimated_profit_amount",
 *       in="query",
 *       description="Estimated Profit Amount",
 *       required=false,
 *       @OA\Schema(type="number", format="float", example="<0")
 *   ),
 *  @OA\Parameter(
 *      name="refunds",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=2
 *      ),
 *      description="Number of refunds"
 *  ),
 *  @OA\Parameter(
 *      name="markup",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=15.00
 *      ),
 *      description="Markup percentage"
 *  ),
 *  @OA\Parameter(
 *      name="roi",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=20.00
 *      ),
 *      description="Return on investment percentage"
 *  ),
 *  @OA\Parameter(
 *      name="margin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=10.00
 *      ),
 *      description="Profit margin percentage"
 *  ),
 *  @OA\Parameter(
 *      name="amazon_fees",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=5.99
 *      ),
 *      description="Amazon fees in USD"
 *  ),
 *  @OA\Parameter(
 *      name="expenses_amount_without_fees",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=30.00
 *      ),
 *      description="Expenses amount excluding fees in USD"
 *  ),
 *  @OA\Parameter(
 *      name="revenue_amount",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=100.00
 *      ),
 *      description="Revenue amount in USD"
 *  ),
 *  @OA\Parameter(
 *      name="units",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=10
 *      ),
 *      description="Number of units sold"
 *  ),
 *  @OA\Parameter(
 *      name="orders",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=5
 *      ),
 *      description="Number of orders"
 *  ),
 *   @OA\Parameter(
 *       name="is_transaction_date_mode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="integer", enum={1,0})
 *   ),
 *   @OA\Response(
 *        response=200,
 *        description="Retrieves the collection of resource.",
 *        @OA\MediaType(
 *            mediaType="application/json",
 *            @OA\Schema(
 *                allOf={
 *                    @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                    @OA\Schema(
 *                        type="object",
 *                        @OA\Property(
 *                            property="data",
 *                            type="array",
 *                            @OA\Items(ref="#/components/schemas/ProductAggregatedSalesInfo")
 *                        )
 *                    )
 *                }
 *            )
 *        )
 *    ),
 *    @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *    @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *    @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * ),
 * @OA\POST(path="/v1/product-aggregated-sales-info/export",
 *   summary="Create export aggregated product info",
 *   tags={"DataExport"},
 *   security={{"oauth2":{}}},
 *      @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *      @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *      @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *      @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *      @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *      @OA\Parameter(
 *          name="date_start",
 *          in="query",
 *          description="Start date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *          name="date_end",
 *          in="query",
 *          description="End date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *     @OA\Parameter(
 *         name="marketplace_seller_ids",
 *         in="query",
 *         description="Pairs of marketplace and seller ids in json format. Pass 'all' as sellerId to see transactions on sellers level, pass 'global' or nothing to see transactions on global level",
 *         required=false,
 *         @OA\Schema(type = "string", example="[{'marketplaceId':'A13V1IB3VIYZZH','sellerId':'A2N58YCCQNSUNR'},{'marketplaceId':'A1PA6795UKMFR9','sellerId':'A2N58YCCQNSUNR'}]")
 *     ),
 *    @OA\Parameter(
 *       name="currency_id",
 *       in="query",
 *       required=false,
 *       @OA\Schema(
 *           type="string",
 *           example="EUR"
 *       ),
 *       description="Currency of all money related columns"
 *   ),
 * @OA\Parameter(
 *      name="product_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=11623107
 *      ),
 *      description="Unique identifier of the product"
 *  ),
 *  @OA\Parameter(
 *      name="marketplace_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="A1PA6795UKMFR9"
 *      ),
 *      description="Marketplace ID"
 *  ),
 *  @OA\Parameter(
 *      name="seller_id",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="A15GEJR91SS7OC"
 *      ),
 *      description="Seller ID"
 *  ),
 *  @OA\Parameter(
 *      name="seller_sku",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="NAYPE_2.3_6.16_0407_B0010V5MBA"
 *      ),
 *      description="Seller Stock Keeping Unit (SKU)"
 *  ),
 *  @OA\Parameter(
 *      name="product_asin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="B0010V5MBA"
 *      ),
 *      description="Amazon Standard Identification Number (ASIN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_brand",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="adidas"
 *      ),
 *      description="Brand of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_ean",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="0885730845370"
 *      ),
 *      description="European Article Number (EAN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_upc",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="885730845370"
 *      ),
 *      description="Universal Product Code (UPC)"
 *  ),
 *  @OA\Parameter(
 *      name="product_isbn",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example=null
 *      ),
 *      description="International Standard Book Number (ISBN)"
 *  ),
 *  @OA\Parameter(
 *      name="product_title",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="adidas Victory League Eau de Toilette – Erfrischend-zitrisches Herren Parfüm für den selbstbewussten, sportlichen Mann – 1 x 50 ml"
 *      ),
 *      description="Title of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_manufacturer",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="Coty Beauty Germany GmbH"
 *      ),
 *      description="Manufacturer of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_type",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="string",
 *          example="PERSONAL_FRAGRANCE"
 *      ),
 *      description="Type of the product"
 *  ),
 *  @OA\Parameter(
 *      name="product_parent_asin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="APTUIPPE36G",
 *          example=null
 *      ),
 *      description="Parent ASIN"
 *  ),
 *  @OA\Parameter(
 *      name="product_stock_type",
 *      in="query",
 *      required=false,
 *      @OA\Schema(type="string", enum={"FBM", "FBA"}),
 *      description="Stock type"
 *  ),
 *  @OA\Parameter(
 *      name="product_adult",
 *      in="query",
 *      description="Product Adult",
 *      required=false,
 *      @OA\Schema(type="integer", enum={1,0})
 *  ),
 *  @OA\Parameter(
 *       name="estimated_profit_amount",
 *       in="query",
 *       description="Estimated Profit Amount",
 *       required=false,
 *       @OA\Schema(type="string", example="<0")
 *   ),
 *  @OA\Parameter(
 *      name="refunds",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=2
 *      ),
 *      description="Number of refunds"
 *  ),
 *  @OA\Parameter(
 *      name="markup",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=15.00
 *      ),
 *      description="Markup percentage"
 *  ),
 *  @OA\Parameter(
 *      name="roi",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=20.00
 *      ),
 *      description="Return on investment percentage"
 *  ),
 *  @OA\Parameter(
 *      name="margin",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=10.00
 *      ),
 *      description="Profit margin percentage"
 *  ),
 *  @OA\Parameter(
 *      name="amazon_fees",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=5.99
 *      ),
 *      description="Amazon fees in USD"
 *  ),
 *  @OA\Parameter(
 *      name="expenses_amount_without_fees",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=30.00
 *      ),
 *      description="Expenses amount excluding fees in USD"
 *  ),
 *  @OA\Parameter(
 *      name="revenue_amount",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="number",
 *          format="float",
 *          example=100.00
 *      ),
 *      description="Revenue amount in USD"
 *  ),
 *  @OA\Parameter(
 *      name="units",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=10
 *      ),
 *      description="Number of units sold"
 *  ),
 *  @OA\Parameter(
 *      name="orders",
 *      in="query",
 *      required=false,
 *      @OA\Schema(
 *          type="integer",
 *          example=5
 *      ),
 *      description="Number of orders"
 *  ),
 *   @OA\Parameter(
 *       name="is_transaction_date_mode",
 *       in="query",
 *       description="Use transaction date for all calcualtions or no (order date is using by default)",
 *       required=false,
 *       @OA\Schema(type="integer", enum={1,0})
 *   ),
 *   @OA\Parameter(
 *       name="sales_category_strategy",
 *       in="query",
 *       description="Sales category strategy (structure) used for calculation",
 *       required=false,
 *       @OA\Schema(enum={"revenue_expenses", "custom"}, default="revenue_expenses")
 *   ),
 *     @OA\Response(
 *          response=200,
 *          description="View the DataExport resource",
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(ref="#/components/schemas/DataExport")
 *          )
 *      ),
 *      @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *      @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *      @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *      @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * ),
 */
class ProductAggregatedSalesInfoController extends Controller
{
    protected bool $isCustomerRelated =  true;

    public $modelClass = ProductAggregatedSalesInfo::class;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [[
                    'allow' => true,
                    'actions' => ['index'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],[
                    'allow' => true,
                    'actions' => ['export'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        unset($actions['view']);
        unset($actions['create']);
        unset($actions['delete']);
        unset($actions['update']);

        $actions['export'] = [
            'class' => ExportAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
