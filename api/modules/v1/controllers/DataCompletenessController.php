<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\dataCompleteness\FactorAction;
use api\modules\v1\controllers\actions\dataCompleteness\UpdateAction;
use api\modules\v1\controllers\actions\dataCompleteness\WidgetAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use common\components\Permissions;
use common\models\customer\clickhouse\AdjustmentFactorView;
use common\models\customer\clickhouse\FbaFeeFactorView;
use common\models\customer\clickhouse\ReferralFeeFactorView;
use common\models\customer\DataCompleteness;
use yii\filters\AccessControl;

/**
 * @OA\Get(
 *     path="/v1/data-completeness/widget",
 *     summary="Retrieves list of data completeness and factors factors",
 *     tags={"DataCompleteness"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Response(
 *         response="200",
 *         description="Completeness response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="fill_percentage",
 *                 type="integer",
 *                 description="Overall fill percentage",
 *                 example=56
 *             ),
 *             @OA\Property(
 *                 property="completeness",
 *                 type="array",
 *                 description="List of completeness items",
 *                 @OA\Items(ref="#/components/schemas/DataCompleteness")
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 *
 * * @OA\Get(
 *     path="/v1/data-completeness/adjustment-to-fees",
 *     summary="Retrieves list of data completeness adjustment",
 *     tags={"DataCompleteness"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Response(
 *         response=200,
 *         description="Data completeness adjustment to fees",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/AdjustmentFactorViewItem"),
 *             ),
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 *
 * @OA\Get(
 *     path="/v1/data-completeness/referral-fee-changes",
 *     summary="Retrieves list of data completeness referral fee changes",
 *     tags={"DataCompleteness"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Response(
 *         response=200,
 *         description="Data completeness referral fee changes",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/ReferralFeeFactorViewItem"),
 *             ),
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 *
 * @OA\Get(
 *     path="/v1/data-completeness/fba-fulfillment-fee-changes",
 *     summary="Retrieves list of data completeness fba fulfillment fee changes",
 *     tags={"DataCompleteness"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Response(
 *         response=200,
 *         description="Data completeness fba fulfillment fee changes",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/FbaFeeFactorViewItem"),
 *             ),
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 *
 * @OA\Put(
 *     path="/v1/data-completeness/{id}",
 *     summary="Update data completeness",
 *     tags={"DataCompleteness"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *         request="IgnoreStatusRequest",
 *         description="Request to update ignore status",
 *         required=true,
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="is_ignored",
 *                 type="boolean",
 *                 description="Indicates if this factor should be ignored",
 *                 example=true
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Data completeness updated successfully",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataCompleteness")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */
class DataCompletenessController extends Controller
{
    public $modelClass = DataCompleteness::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['widget'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['referral-fee-changes'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['adjustment-to-fees'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['fba-fulfillment-fee-changes'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::DASHBOARD_VIEW],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        unset($actions['index']);
        unset($actions['delete']);
        unset($actions['create']);

        $actions['widget'] = [
            'class' => WidgetAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['update'] = [
            'class' => UpdateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['referral-fee-changes'] = [
            'class' => FactorAction::class,
            'modelClass' => ReferralFeeFactorView::class,
            'checkAccess' => [$this, 'checkAccess']
        ];

        $actions['adjustment-to-fees'] = [
            'class' => FactorAction::class,
            'modelClass' => AdjustmentFactorView::class,
            'checkAccess' => [$this, 'checkAccess']
        ];

        $actions['fba-fulfillment-fee-changes'] = [
            'class' => FactorAction::class,
            'modelClass' => FbaFeeFactorView::class,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
