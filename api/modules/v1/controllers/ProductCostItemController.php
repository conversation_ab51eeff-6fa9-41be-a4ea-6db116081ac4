<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\productCostItem\BulkUpdateAction;
use api\modules\v1\controllers\actions\productCostItem\CreateAction;
use api\modules\v1\controllers\actions\productCostItem\DeleteAction;
use api\modules\v1\controllers\actions\productCostItem\UpdateAction;
use common\components\Permissions;
use Yii;
use api\components\controllers\Controller;
use yii\filters\AccessControl;


/**
 * ProductCostItemController implements the REST actions for ProductCostItem model.
* @OA\Get(path="/v1/product-cost-item",
*   summary="Retrieves the collection of ProductCostItem resources.",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="product_cost_period_id",
*         in="query",
*         description="Product Cost Period Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="product_cost_category_id",
*         in="query",
*         description="Product Cost Category Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="amount_total",
*         in="query",
*         description="Amount Total",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="currency_id",
*         in="query",
*         description="Currency Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="amount_per_unit",
*         in="query",
*         description="Amount Per Unit",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="units",
*         in="query",
*         description="Units",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="note",
*         in="query",
*         description="Note",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="created_at",
*         in="query",
*         description="Created At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="updated_at",
*         in="query",
*         description="Updated At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of ProductCostItem resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostItem"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Get(path="/v1/product-cost-item/{id}",
*   summary="View the ProductCostItem resource",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="path",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*   @OA\Response(
*     response=200,
*     description="View the ProductCostItem resource",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostItem"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="/v1/product-cost-item",
*   summary="Create ProductCostItem resource",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="query",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
*	 	            @OA\Property(property="product_cost_period_id", type="integer"),
*	 	            @OA\Property(property="product_cost_category_id", type="integer"),
*	 	            @OA\Property(property="amount_total", type="number"),
*	 	            @OA\Property(property="currency_id", type="stgring"),
*	 	            @OA\Property(property="marketplace_currency_rate", type="decimal"),
*	 	            @OA\Property(property="units", type="integer"),
*	 	            @OA\Property(property="note", type="string")
*          )
*        ),
*   @OA\Response(
*     response=200,
*     description="ProductCostItem resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostItem"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="/v1/product-cost-item/bulk-update",
*   summary="Update ProductCostItem resources",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="path",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*     @OA\Parameter(
*         name="product_cost_period_id",
*         in="query",
*         description="Product cost period id",
*         required=true,
*         @OA\Schema(
*           type="int",
*         ),
*     ),
*     @OA\RequestBody(
*       @OA\JsonContent(
*           type="array",
*           @OA\Items
*           (
*              type="object",
*	 	            @OA\Property(property="product_cost_period_id", type="integer"),
*	 	            @OA\Property(property="product_cost_category_id", type="integer"),
*	 	            @OA\Property(property="amount_total", type="number"),
*	 	            @OA\Property(property="currency_id", type="number"),
*	 	            @OA\Property(property="marketplace_currency_rate", type="decimal"),
*	 	            @OA\Property(property="units", type="integer"),
*	 	            @OA\Property(property="note", type="string")
*           )
*       )
*     ),
*   @OA\Response(
*     response=200,
*     description="ProductCostItem resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostItem"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="/v1/product-cost-item",
*   summary="Update the ProductCostItem resource",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="customerId",
*         in="query",
*         description="Customer Id - required to admin user",
*         required=false,
*         @OA\Schema(
*           type="string",
*           default="c-1"
*         ),
*     ),
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
		*	 	       @OA\Property(property="product_cost_period_id", type="integer"),
	*	 	       @OA\Property(property="product_cost_category_id", type="integer"),
	*	 	       @OA\Property(property="amount_total", type="number"),
	*	 	       @OA\Property(property="currency_id", type="number"),
	*	 	       @OA\Property(property="amount_per_unit", type="number"),
    *	 	       @OA\Property(property="marketplace_currency_rate", type="number"),
	*	 	       @OA\Property(property="units", type="integer"),
	*	 	       @OA\Property(property="note", type="string"),
	*	 	       @OA\Property(property="created_at", type="string"),
	*	 	       @OA\Property(property="updated_at", type="string"),
*          )
*        ),
*
*   @OA\Response(
*     response=200,
*     description="ProductCostItem resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostItem"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="/v1/product-cost-item/{id}",
*   summary="Delete ProductCostItem resource",
*   tags={"ProductCostItem"},
*   security={{"oauth2":{}}},
 *            @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
* )
*/
class ProductCostItemController extends Controller
{
    protected bool $isCustomerRelated =  true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [                [
                    'allow' => true,
                    'actions' => ['index', 'view'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create', 'bulk-update'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        $actions['create'] = [
            'class' => CreateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['update'] = [
            'class' => UpdateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['bulk-update'] = [
            'class' => BulkUpdateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        return $actions;
    }

    public $modelClass = 'common\models\customer\ProductCostItem';
}
