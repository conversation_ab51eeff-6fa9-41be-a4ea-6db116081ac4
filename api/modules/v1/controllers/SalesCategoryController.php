<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use common\models\SalesCategory;
use yii\filters\AccessControl;

/**
 * MessageController implements the REST actions for Message model.
 * @OA\Get(path="/v1/sales-category",
 *   summary="Retrieves the collection of Transaction resources.",
 *   tags={"SalesCategory"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *     ),
 *     @OA\Parameter(
 *         name="pageSize",
 *         in="query",
 *         description="Page size [1,100]",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *     ),
 *     @OA\Parameter(
 *         name="id",
 *         in="query",
 *         description="Id",
 *         required=false,
 *         @OA\Schema(type="strinng"),
 *     ),
 *     @OA\Parameter(
 *         name="depth",
 *         in="query",
 *         description="Depth",
 *         required=false,
 *         @OA\Schema(type="integer"),
 *     ),
 *     @OA\Parameter(
 *         name="parent_id",
 *         in="query",
 *         description="Parent id",
 *         required=false,
 *         @OA\Schema(type="string"),
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Returns list of sales categories",
 *     @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/SalesCategory"),
 *             ),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * )
 */
class SalesCategoryController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = SalesCategory::class;

    protected bool $isCustomerRelated = false;


    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'verbs' => ['GET'],
                    'actions' => ['index'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        unset($actions['create']);
        unset($actions['update']);
        unset($actions['delete']);

        return $actions;
    }
}
