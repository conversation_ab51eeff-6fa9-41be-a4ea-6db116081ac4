<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\customer\GetInfoAction;
use api\modules\v1\controllers\actions\customer\GetStatisticAction;
use api\modules\v1\controllers\actions\customer\GetStatisticBySellerAction;
use api\modules\v1\controllers\actions\customer\SyncAction;
use yii\base\Model;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use yii\rest\Action;

/**
 * @OA\Get(
 *     path="/v1/customer/statistic",
 *     summary="Internal API. Returns sales statistic for customer (units)",
 *     tags={"Customer"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="dateStart",
 *         in="query",
 *         description="Event period start date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Parameter(
 *         name="dateEnd",
 *         in="query",
 *         description="Event period end date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="units",
 *                 type="integer",
 *                 example=226134
 *             ),
 *             @OA\Property(
 *                 property="orders",
 *                 type="integer",
 *                 example=211990
 *             ),
 *             @OA\Property(
 *                 property="ordersCanceled",
 *                 type="integer",
 *                 example=12545
 *             ),
 *             @OA\Property(
 *                 property="refunds",
 *                 type="integer",
 *                 example=45897
 *             ),
 *             @OA\Property(
 *                 property="promo",
 *                 type="integer",
 *                 example=24901
 *             ),
 *             @OA\Property(
 *                 property="updatedAt",
 *                 type="string",
 *                 format="date-time",
 *                 example="2024-06-14 00:24:09"
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/customer/statistic-by-seller",
 *     summary="Internal API. Returns sales statistic by seller (units)",
 *     tags={"Customer"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="sellerId",
 *         in="query",
 *         description="Seller ID",
 *         required=true,
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="dateStart",
 *         in="query",
 *         description="Event period start date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Parameter(
 *         name="dateEnd",
 *         in="query",
 *         description="Event period end date",
 *         required=true,
 *         @OA\Schema(type="string", format="date YYY-M-D")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="orders",
 *                 type="integer",
 *                 example=156
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/customer/info",
 *     summary="Common information about customer",
 *     tags={"Customer"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="has_manual_costs",
 *                 type="boolean",
 *                 example=true
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/customer/sync",
 *     summary="Internal API. Synchronize sellers",
 *     tags={"Customer"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\RequestBody(
 *         required=true,
 *         description="Request body containing ads accounts and seller details",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                  property="defaultIsSyncActive",
 *                  type="boolean",
 *                  example=true
 *             ),
 *             @OA\Property(
 *                  property="repricerToBASync",
 *                  type="boolean",
 *                  example=true
 *             ),
 *             @OA\Property(
 *                 property="adsAccounts",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="id",
 *                         type="integer",
 *                         example=14
 *                     ),
 *                     @OA\Property(
 *                         property="is_active",
 *                         type="boolean",
 *                         example=true
 *                     ),
 *                     @OA\Property(
 *                         property="is_deleted",
 *                         type="boolean",
 *                         example=false
 *                     )
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="sellers",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="region",
 *                         type="string",
 *                         example="eu-west-1"
 *                     ),
 *                     @OA\Property(
 *                         property="isAnalyticActive",
 *                         type="boolean",
 *                         example=false
 *                     ),
 *                     @OA\Property(
 *                         property="isDemo",
 *                         type="boolean",
 *                         example=false
 *                     ),
 *                     @OA\Property(
 *                         property="lastActivePlanDateFinish",
 *                         type="string",
 *                         format="date",
 *                         example="2022-05-04"
 *                     ),
 *                     @OA\Property(
 *                         property="active",
 *                         type="boolean",
 *                         example=false
 *                     ),
 *                     @OA\Property(
 *                         property="sellerId",
 *                         type="string",
 *                         example="544941fcfa"
 *                     ),
 *                     @OA\Property(
 *                         property="customerId",
 *                         type="integer",
 *                         example=5888
 *                     ),
 *                     @OA\Property(
 *                         property="isDeleted",
 *                         type="boolean",
 *                         example=false
 *                     )
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */
class CustomerController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['statistic', 'statistic-by-seller', 'info'],
                    'verbs' => ['GET'],
                    'matchCallback' => [$this, 'isAllowed'],
                ],
                [
                    'allow' => true,
                    'actions' => ['sync'],
                    'verbs' => ['POST'],
                    'matchCallback' => [$this, 'isAllowed'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['statistic'] = [
            'class' => GetStatisticAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['info'] = [
            'class' => GetInfoAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['statistic-by-seller'] = [
            'class' => GetStatisticBySellerAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['sync'] = [
            'class' => SyncAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    /**
     * @param AccessRule $rule
     * @param Action $action
     * @return bool
     */
    public function isAllowed(AccessRule $rule, Action $action): bool
    {
        return \Yii::$app->user->identity->isInternalClient();
    }
}
