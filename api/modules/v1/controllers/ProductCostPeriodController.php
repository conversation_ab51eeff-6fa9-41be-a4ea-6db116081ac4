<?php

namespace api\modules\v1\controllers;

use api\modules\v1\controllers\actions\productCostPeriod\CreateAction;
use api\modules\v1\controllers\actions\productCostPeriod\ApplyForMarketplaces;
use api\modules\v1\controllers\actions\productCostPeriod\DeleteAction;
use api\modules\v1\controllers\actions\productCostPeriod\DeleteAll;
use api\modules\v1\controllers\actions\productCostPeriod\UpdateAction;
use common\components\Permissions;
use api\components\controllers\Controller;
use yii\filters\AccessControl;


/**
 * ProductCostPeriodController implements the REST actions for ProductCostPeriod model.
* @OA\Get(path="/v1/product-cost-period",
*   summary="Retrieves the collection of ProductCostPeriod resources.",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
*     @OA\Parameter(
*         name="page",
*         in="query",
*         description="Page number",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*     @OA\Parameter(
*         name="sort",
*         in="query",
*         description="Sort by column [{column}, -{column}]",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="pageSize",
*         in="query",
*         description="Page size [1,100]",
*         required=false,
*         @OA\Schema(
*           type="integer",
*         ),
*     ),
*
*     @OA\Parameter(
*         name="id",
*         in="query",
*         description="Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="marketplace_id",
*         in="query",
*         description="Marketplace Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="seller_id",
*         in="query",
*         description="Seller Id",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="seller_sku",
*         in="query",
*         description="Seller Sku",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="sales_category_id",
*         in="query",
*         description="Sales category id",
*         required=false,
*         @OA\Schema(
*           type="string",
*           enum={"cost_of_goods", "shipping_costs", "other_fees"}
*         ),
*     ),
*     @OA\Parameter(
*         name="source",
*         in="query",
*         description="Source",
*         required=false,
*         @OA\Schema(
*           type="string",
*           enum={"manual", "repricer"}
*         ),
*     ),
*     @OA\Parameter(
*         name="date_start",
*         in="query",
*         description="Date Start",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="amount_total",
*         in="query",
*         description="Amount Total",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="created_at",
*         in="query",
*         description="Created At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*     @OA\Parameter(
*         name="updated_at",
*         in="query",
*         description="Updated At",
*         required=false,
*         @OA\Schema(
*           type="string",
*         ),
*     ),
*
*   @OA\Response(
*     response=200,
*     description="Retrieves the collection of ProductCostPeriod resources.",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostPeriod"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     )
* ),
* @OA\Get(path="/v1/product-cost-period/{id}",
*   summary="View the ProductCostPeriod resource",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="path",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*   @OA\Response(
*     response=200,
*     description="View the ProductCostPeriod resource",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostPeriod"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="/v1/product-cost-period",
*   summary="Create ProductCostPeriod resource",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
*	 	            @OA\Property(property="marketplace_id", type="string"),
*	 	            @OA\Property(property="seller_id", type="string"),
*	 	            @OA\Property(property="seller_sku", type="string"),
*	 	            @OA\Property(property="sales_category_id", type="string"),
*	 	            @OA\Property(property="date_start", type="string"),
*          )
*        ),
*   @OA\Response(
*     response=200,
*     description="ProductCostPeriod resource is created",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostPeriod"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Post(path="/v1/product-cost-period/apply-for-marketplaces",
*   summary="Applies product cost periods ",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="c-1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace Id to copy from",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller Id to copy from",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_sku",
 *         in="query",
 *         description="Seller SKU to copy from",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="target_marketplace_ids",
 *         in="query",
 *         description="Marketplace Ids to copy to",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="sales_category_id",
 *         in="query",
 *         description="Sales category id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
*   @OA\Response(
*     response=200,
*     description="Product cost periods has been moved",
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Put(path="/v1/product-cost-period",
*   summary="Update the ProductCostPeriod resource",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
*    @OA\Parameter(
*        name="id",
*        in="query",
*        description="Id",
*        required=true,
*        @OA\Schema(
*          type="integer",
*        ),
*    ),
*     @OA\RequestBody(
*			@OA\JsonContent(
*              type="object",
*   	 	       @OA\Property(property="date_start", type="string"),
*          )
*        ),
*
*   @OA\Response(
*     response=200,
*     description="ProductCostPeriod resource is updated",
*     @OA\MediaType(
*         mediaType="application/json",
*         @OA\Schema(ref="#/components/schemas/ProductCostPeriod"),
*     ),
*   ),
*     @OA\Response(
*         response=400,
*         description="Bad Request"
*     ),
*     @OA\Response(
*         response=401,
*         description="Invalid token supplied"
*     ),
*     @OA\Response(
*         response=404,
*         description="Not found"
*     ),
*     @OA\Response(
*         response=405,
*         description="Method Not Allowed"
*     ),
*     @OA\Response(
*         response=422,
*         description="Data Validation Failed"
*     ),
* ),
* @OA\Delete(path="/v1/product-cost-period/{id}",
*   summary="Delete ProductCostPeriod resource",
*   tags={"ProductCostPeriod"},
*   security={{"oauth2":{}}},
 *            @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
* ),
 * @OA\Delete(path="/v1/product-cost-period/delete-all",
 *   summary="Delete all product cost periods for current product",
 *   tags={"ProductCostPeriod"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *         name="marketplace_id",
 *         in="query",
 *         description="Marketplace Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_id",
 *         in="query",
 *         description="Seller Id",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *     @OA\Parameter(
 *         name="seller_sku",
 *         in="query",
 *         description="Seller SKU",
 *         required=true,
 *         @OA\Schema(type="string"),
 *     ),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
*/
class ProductCostPeriodController extends Controller
{
    protected bool $isCustomerRelated =  true;

    public $modelClass = 'common\models\customer\ProductCostPeriod';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [[
                    'allow' => true,
                    'actions' => ['index', 'view'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::MY_PRODUCTS_VIEW],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete', 'delete-all'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['update'],
                    'verbs' => ['PUT'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'actions' => ['create', 'apply-for-marketplaces'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::MY_PRODUCTS_MANAGE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['create'] = [
            'class' => CreateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['update'] = [
            'class' => UpdateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['delete-all'] = [
            'class' => DeleteAll::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['apply-for-marketplaces'] = [
            'class' => ApplyForMarketplaces::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
