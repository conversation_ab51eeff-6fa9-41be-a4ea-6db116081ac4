<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\information\GetLoadInfoAction;
use api\modules\v1\controllers\actions\information\GetMinimumStatisticDateAction;
use api\modules\v1\controllers\filters\SynchronizedAccountGuard;
use yii\base\Model;
use yii\filters\AccessControl;

/**
 * @OA\Get(path="/v1/information/load-info",
 *   summary="Date of the last synchronization",
 *   tags={"Information"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Returns date of the last synchronization",
 *     @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                  type="array",
 *                  @OA\Items(ref="#/components/schemas/LoadInfo"),
 *             ),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Get(path="/v1/information/minimum-statistic-date",
 *   summary="Minimum statistic date",
 *   tags={"Information"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user or internal client",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="1"
 *         ),
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="Returns information about the minimum statistic date",
 *     @OA\MediaType(
 *             mediaType="application/json",
 *               @OA\Schema(
 *                  type="object",
 *                  @OA\Property(property="date", type="string"),
 *              ),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 */
class InformationController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'matchCallback' => (new SynchronizedAccountGuard()),
                ],
                [
                    'allow' => true,
                    'actions' => ['load-info'],
                    'verbs' => ['GET'],
                    'roles' => ['@'],
                ],
                [
                    'allow' => true,
                    'actions' => ['minimum-statistic-date'],
                    'verbs' => ['GET'],
                    'roles' => ['@'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();

        $actions['load-info'] = [
            'class' => GetLoadInfoAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['minimum-statistic-date'] = [
            'class' => GetMinimumStatisticDateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }

    protected function verbs(): array
    {
        $verbs = parent::verbs();

        $verbs['load-info'] = ['GET'];
        $verbs['minimum-statistic-date'] = ['GET'];

        return $verbs;
    }
}
