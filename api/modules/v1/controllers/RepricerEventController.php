<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\repricerEvent\GetGroupedByDate;
use api\modules\v1\controllers\actions\repricerEvent\GetTotal;
use yii\base\Model;
use yii\filters\AccessControl;
use yii\filters\AccessRule;
use yii\rest\Action;

/**
 * @OA\Get(
 *     path="/v1/repricer-event/total",
 *     summary="Total repricer events count",
 *     tags={"RepricerEvent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *          name="dateStart",
 *          in="query",
 *          description="Event period start date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *          name="dateEnd",
 *          in="query",
 *          description="Event period end date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *           name="offer_type",
 *           in="query",
 *           description="Event Offer type",
 *           required=false,
 *           @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *      ),
 *      @OA\Parameter(
 *           name="calc_type",
 *           in="query",
 *           description="Calculate events count by distinct product_id per day as 1 event (default) or by real events amount",
 *           required=false,
 *           @OA\Schema(type="string", enum={"amount", "distinct"}),
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(
 *                 property="total",
 *                 type="object",
 *                 @OA\Property(property="b2b", type="integer", example=12),
 *                 @OA\Property(property="b2c", type="integer", example=24),
 *                 @OA\Property(property="total", type="integer", example=36)
 *             ),
 *             @OA\Property(
 *                 property="sellers",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(property="seller_id", type="string", example="A15GEJR91SS7OC"),
 *                     @OA\Property(property="b2b", type="integer", example=0),
 *                     @OA\Property(property="b2c", type="integer", example=8),
 *                     @OA\Property(property="total", type="integer", example=8),
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Get(
 *     path="/v1/repricer-event/grouped-by-date",
 *     summary="Detailed repricer events count per day",
 *     tags={"RepricerEvent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(
 *          name="dateStart",
 *          in="query",
 *          description="Event period start date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *          name="dateEnd",
 *          in="query",
 *          description="Event period end date",
 *          required=true,
 *          @OA\Schema(type="string", format="date YYY-M-D")
 *      ),
 *      @OA\Parameter(
 *          name="offer_type",
 *          in="query",
 *          description="Event Offer type",
 *          required=false,
 *          @OA\Schema(type="string", enum={"B2B", "B2C"}),
 *      ),
 *      @OA\Parameter(
 *          name="period",
 *          in="query",
 *          description="Date period response type",
 *          required=false,
 *          @OA\Schema(type="string", enum={"day", "month"}),
 *      ),
 *      @OA\Parameter(
 *          name="calc_type",
 *          in="query",
 *          description="Calculate events count by distinct product_id per day as 1 event (default) or by real events amount",
 *          required=false,
 *          @OA\Schema(type="string", enum={"amount", "distinct"}),
 *      ),
 *     @OA\Response(
 *         response=200,
 *         description="Successful response",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(property="date", type="string", format="date", example="2024-01-01"),
 *                 @OA\Property(property="b2b", type="integer", example=100),
 *                 @OA\Property(property="b2c", type="integer", example=50),
 *                 @OA\Property(property="total", type="integer", example=150)
 *             ),
 *             @OA\Examples(
 *                 example="daily",
 *                 summary="Daily grouping example",
 *                 value={
 *                     {"date":"2024-01-01","total":150,"b2b":100,"b2c":50},
 *                     {"date":"2024-01-02","total":200,"b2b":120,"b2c":80}
 *                 }
 *             ),
 *             @OA\Examples(
 *                 example="monthly",
 *                 summary="Monthly grouping example",
 *                 value={
 *                     {"date":"2024-01","total":5000,"b2b":3000,"b2c":2000},
 *                     {"date":"2024-02","total":4000,"b2b":2500,"b2c":1500}
 *                 }
 *             )
 *         ),
 *     ),
 *      @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *      @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *      @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

class RepricerEventController extends Controller
{
    public $modelClass = Model::class;

    public bool $isCustomerRelated = true;

    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['grouped-by-date', 'total'],
                    'verbs' => ['GET'],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions(): array
    {
        $actions = parent::actions();
        unset(
            $actions['update'],
            $actions['delete'],
            $actions['view'],
            $actions['create']
        );

        $actions['grouped-by-date'] = [
            'class' => GetGroupedByDate::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['total'] = [
            'class' => GetTotal::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];


        return $actions;
    }
}
