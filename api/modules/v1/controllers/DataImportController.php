<?php

namespace api\modules\v1\controllers;

use api\components\controllers\Controller;
use api\modules\v1\controllers\actions\dataImportExport\DeleteAction;
use api\modules\v1\controllers\actions\dataImportExport\DownloadTemplateAction;
use api\modules\v1\controllers\actions\dataImportExport\ImportAction;
use api\modules\v1\controllers\actions\dataImportExport\StopBulkEditProcessAction;
use common\components\Permissions;
use yii\filters\AccessControl;

/**
 * DataImportController implements the REST actions for DataImport model.
 * @OA\Get(path="/v1/data-import",
 *   summary="Retrieves the collection of DataImport resources.",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="c-1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="sort",
 *         in="query",
 *         description="Sort by column [{column}, -{column}]",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="pageSize",
 *         in="query",
 *         description="Page size [1,100]",
 *         required=false,
 *         @OA\Schema(
 *           type="integer",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="id",
 *         in="query",
 *         description="Id",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="handler_name",
 *         in="query",
 *         description="Handler Name",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost"}
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="status",
 *         in="query",
 *         description="Status",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           enum={"new", "done", "in_progress", "no_items"}
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="count_parts",
 *         in="query",
 *         description="Count Parts",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="count_all_items",
 *         in="query",
 *         description="Count All Items",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="count_imported_items",
 *         in="query",
 *         description="Count Imported Items",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="count_errors",
 *         in="query",
 *         description="Count Errors",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="file_url",
 *         in="query",
 *         description="File Url",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="exception",
 *         in="query",
 *         description="Exception",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="errors",
 *         in="query",
 *         description="Errors",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created At",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="started_at",
 *         in="query",
 *         description="Started At",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="finished_at",
 *         in="query",
 *         description="Finished At",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *
 *   @OA\Response(
 *     response=200,
 *     description="Retrieves the collection of DataImport resources.",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/DataImport"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     )
 * ),
 * @OA\Get(path="/v1/data-import/{id}",
 *   summary="View the DataImport resource",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="c-1"
 *         ),
 *     ),
 *    @OA\Parameter(
 *        name="id",
 *        in="path",
 *        description="Id",
 *        required=true,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *    ),
 *   @OA\Response(
 *     response=200,
 *     description="View the DataImport resource",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/DataImport"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Get(path="/v1/data-import/template",
 *   summary="Download import data template file",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="c-1"
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="handler_name",
 *         in="query",
 *         description="Handler name",
 *         required=true,
 *         @OA\Schema(
 *           type="string",
 *           enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost"}
 *         ),
 *     ),
 *   @OA\Response(
 *     response=200,
 *     description="File with example import data",
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Post(path="/v1/data-import/import",
 *   summary="Imports data",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *           type="string",
 *           default="c-1"
 *         ),
 *     ),
 *     @OA\RequestBody(
 *			@OA\MediaType(
 *     			mediaType="multipart/form-data",
 *               @OA\Schema(
 *                  @OA\Property(
 *                      property="file",
 *                      type="string",
 *                      format="binary",
 *                      description="File",
 * 					),
 *                  @OA\Property(
 *                      property="file_url",
 *                      type="string",
 *                      description="URL of file uploadded to our amazon s3 bucket",
 *                  ),
 *                  @OA\Property(
 *                      property="handler_name",
 *                      type="string",
 *                      description="Handler name",
 *                      enum={"product_cost_periods", "order_fbm_cost", "order_item_fbm_cost"},
 * 					)
 *              )
 *        ),
 *	),
 *   @OA\Response(
 *     response=200,
 *     description="Import has been started",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/DataImport"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Not found"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 * @OA\Delete(path="/v1/data-import/{id}",
 *   summary="Delete the DataImport resource",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *        @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * ),
 * @OA\Post(path="/v1/data-import/stop-bulk-edit-process",
 *   summary="Stop bulk edit process",
 *   tags={"DataImport"},
 *   security={{"oauth2":{}}},
 *    @OA\Parameter(
 *        name="customerId",
 *        in="query",
 *        description="Customer ID required to admin user",
 *        required=false,
 *        @OA\Schema(
 *          type="integer",
 *        ),
 *    ),
 *     @OA\RequestBody(
 *		@OA\JsonContent(
 *           type="object",
 *	 	    @OA\Property(property="id", type="integer"),
 *       )
 *    ),
 *   @OA\Response(
 *     response=200,
 *     description="Bulk edit process is terminated",
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/DataImport"),
 *     ),
 *   ),
 *     @OA\Response(
 *         response=400,
 *         description="Bad Request"
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Invalid token supplied"
 *     ),
 *     @OA\Response(
 *         response=405,
 *         description="Method Not Allowed"
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Data Validation Failed"
 *     ),
 * ),
 */
class DataImportController extends Controller
{
    protected bool $isCustomerRelated =  true;

    public $modelClass = 'common\models\customer\DataImport';

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['index', 'view'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::PRODUCT_COST_IMPORT_LIST],
                ],
                [
                    'allow' => true,
                    'actions' => ['delete'],
                    'verbs' => ['DELETE'],
                    'roles' => [Permissions::PRODUCT_COST_IMPORT_CREATE],
                ],
                [
                    'allow' => true,
                    'actions' => ['import'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::PRODUCT_COST_IMPORT_CREATE],
                ],
                [
                    'allow' => true,
                    'actions' => ['template'],
                    'verbs' => ['GET'],
                    'roles' => [Permissions::PRODUCT_COST_IMPORT_LIST],
                ],
                [
                    'allow' => true,
                    'actions' => ['stop-bulk-edit-process'],
                    'verbs' => ['POST'],
                    'roles' => [Permissions::PRODUCT_COST_IMPORT_CREATE],
                ],
                [
                    'allow' => true,
                    'verbs' => ['OPTIONS'],
                ],
            ],
        ];

        return $behaviors;
    }

    public function actions()
    {
        $actions = parent::actions();
        unset(
            $actions['create'],
            $actions['update'],
        );

        $actions['import'] = [
            'class' => ImportAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['template'] = [
            'class' => DownloadTemplateAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];
        $actions['delete'] = [
            'class' => DeleteAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        $actions['stop-bulk-edit-process'] = [
            'class' => StopBulkEditProcessAction::class,
            'modelClass' => $this->modelClass,
            'checkAccess' => [$this, 'checkAccess'],
        ];

        return $actions;
    }
}
