<?php

namespace api\modules\v1\controllers;

use api\components\controllers\AllRecordsTrait;
use api\components\controllers\Controller;
use common\components\dataImportExport\SupportedHandlers;
use common\components\Permissions;

/**
 * @OA\Get(
 *     path="/v1/data-import-recurrent",
 *     summary="Retrieves the collection of DataImportRecurrent resources.",
 *     tags={"DataImportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPage"),
 *     @OA\Parameter(ref="#/components/parameters/listViewSort"),
 *     @OA\Parameter(ref="#/components/parameters/listViewPageSize"),
 *     @OA\Parameter(ref="#/components/parameters/listViewAllRecords"),
 *     @OA\Parameter(ref="#/components/parameters/listViewResourceId"),
 *     @OA\Parameter(ref="#/components/parameters/importExportHandlerName"),
 *     @OA\Parameter(
 *         name="is_enabled",
 *         in="query",
 *         description="Is Enabled",
 *         required=false,
 *         @OA\Schema(
 *             type="integer",
 *             enum={1, 0}
 *         )
 *     ),
 *     @OA\Parameter(
 *         name="created_at",
 *         in="query",
 *         description="Created At",
 *         required=false,
 *         @OA\Schema(
 *             type="string"
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Retrieves the collection of resource.",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/AbstractPaginatedResponse"),
 *                     @OA\Schema(
 *                         type="object",
 *                         @OA\Property(
 *                             property="data",
 *                             type="array",
 *                             @OA\Items(ref="#/components/schemas/DataImportRecurrent")
 *                         )
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse")
 * )
 */

/**
 * @OA\Post(
 *     path="/v1/data-import-recurrent",
 *     summary="Create DataImportRecurrent resource",
 *     tags={"DataImportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(
 *         name="customerId",
 *         in="query",
 *         description="Customer Id - required to admin user",
 *         required=false,
 *         @OA\Schema(
 *             type="string",
 *             default="c-1"
 *         )
 *     ),
 *     @OA\RequestBody(
 *         request="CreateDataImportRecurrent",
 *         required=true,
 *         @OA\JsonContent(
 *             required={"handler_name", "cron_expr", "url"},
 *             @OA\Property(property="handler_name", type="string", example="example_handler"),
 *             @OA\Property(property="should_use_auth", type="boolean", example=true),
 *             @OA\Property(property="cron_expr", type="string", example="0 0 * * *"),
 *             @OA\Property(property="url", type="string", format="url", example="http://example.com"),
 *             @OA\Property(property="auth_login", type="string", nullable=true, example="user"),
 *             @OA\Property(property="auth_password", type="string", nullable=true, example="password")
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="DataImportRecurrent resource is created",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataImportRecurrent")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Put(
 *     path="/v1/data-import-recurrent/{id}",
 *     summary="Update the DataImportRecurrent resource",
 *     tags={"DataImportRecurrent"},
 *     security={{"oauth2":{}}},
 *     @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *     @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *     @OA\RequestBody(
 *         request="UpdateDataImportRecurrent",
 *         required=true,
 *         @OA\JsonContent(
 *             @OA\Property(property="cron_expr", type="string", example="0 0 * * *"),
 *             @OA\Property(property="should_use_auth", type="boolean", example=true),
 *             @OA\Property(property="is_enabled", type="boolean", example=true),
 *             @OA\Property(property="url", type="string", format="url", example="http://example.com"),
 *             @OA\Property(property="auth_login", type="string", nullable=true, example="user"),
 *             @OA\Property(property="auth_password", type="string", nullable=true, example="password")
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="DataImportRecurrent resource is updated",
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(ref="#/components/schemas/DataImportRecurrent")
 *         )
 *     ),
 *     @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *     @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *     @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *     @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse"),
 *     @OA\Response(response=422, ref="#/components/responses/FormValidationErrorResponse")
 * )
 */

/**
 * @OA\Delete(
 *     path="/v1/data-import-recurrent/{id}",
 *     summary="Delete DataImportRecurrent resource",
 *     tags={"DataImportRecurrent"},
 *     security={{"oauth2":{}}},
 *        @OA\Parameter(ref="#/components/parameters/requestCustomerId"),
 *        @OA\Parameter(ref="#/components/parameters/requestResourceId"),
 *        @OA\Response(response=204, ref="#/components/responses/ResourceDeletedResponse"),
 *        @OA\Response(response=400, ref="#/components/responses/CommonErrorResponse"),
 *        @OA\Response(response=401, ref="#/components/responses/UnauthorizedErrorResponse"),
 *        @OA\Response(response=403, ref="#/components/responses/ForbiddenErrorResponse"),
 *        @OA\Response(response=404, ref="#/components/responses/NotFoundErrorResponse")
 * )
 */
class DataImportRecurrentController extends Controller
{
    use AllRecordsTrait;

    public $modelClass = 'common\models\DataImportRecurrent';

    protected bool $isCustomerRelated = true;
    protected ?string $handlerName = null;

    public function __construct($id, $module, $config = [])
    {
        $requestParams = array_merge(\Yii::$app->request->get(), \Yii::$app->request->post());
        $this->handlerName = $requestParams['handler_name'] ?? null;

        parent::__construct($id, $module, $config);
    }

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        if ($this->handlerName === SupportedHandlers::HANDLER_PRODUCT_COST_PERIODS ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_FBM_COST ||
            $this->handlerName === SupportedHandlers::HANDLER_ORDER_ITEM_FBM_COST
        ) {
            $behaviors['access'] = Permissions::allowBasicPermissionsWith([
                'GET' => [Permissions::PRODUCT_COST_AUTO_IMPORT_VIEW],
                'POST' => [Permissions::PRODUCT_COST_AUTO_IMPORT_MANAGE],
                'PUT' => [Permissions::PRODUCT_COST_AUTO_IMPORT_MANAGE],
                'DELETE' => [Permissions::PRODUCT_COST_AUTO_IMPORT_MANAGE],
            ]);
        }

        return $behaviors;
    }
}
