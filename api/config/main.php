<?php

use api\components\ApiErrorHandler;
use \common\models\UserToken;
use api\components\BasUser;

$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/params.php',
);

$enableSchemaCache = true;
$schemaCache = 'schemaRedisCache';
$schemaCacheDuration = 60 * 60 * 24;

$enableQueryCache = true;
$queryCache = 'cache';
$queryCacheDuration = 5 * 60;
$applicationName = 'bas_' . YII_ENV . '_';

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? null;
if ($requestMethod === 'GET') {
    $queryCache = 'chainedCache';
    $schemaCache = 'schemaChainedCache';
}

$dbParams = [
    'dbHost' => getenv('APP_DB_HOST'),
    'dbSlaveHost' => getenv('APP_DB_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_PORT'),
    'dbSlavePort' => getenv('APP_DB_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_USERNAME'),
    'dbPassword' => getenv('APP_DB_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];

$db1Params = [
    'dbHost' => getenv('APP_DB_1_HOST'),
    'dbSlaveHost' => getenv('APP_DB_1_SLAVE_HOST'),
    'dbPort' => getenv('APP_DB_1_PORT'),
    'dbSlavePort' => getenv('APP_DB_1_SLAVE_PORT'),
    'dbUser' => getenv('APP_DB_1_USERNAME'),
    'dbPassword' => getenv('APP_DB_1_PASSWORD'),
    'dbUserSlave' => getenv('APP_DB_1_USERNAME_SLAVE'),
    'dbPasswordSlave' => getenv('APP_DB_1_PASSWORD_SLAVE'),
    'profitDashDbName' => getenv('PROFIT_DASH_DB_NAME'),
];


$urlRules = require __DIR__ . '/url-rules.php';

$config = [
    'id' => 'profit-dash-api',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log', 'rollbar'],
    'controllerNamespace' => 'api\controllers',
    'defaultRoute' => 'default/swagger',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'modules' => [
        'v1' => [
            'basePath' => '@api/modules/v1',
            'class' => 'api\modules\v1\Module',
        ],
    ],
    'components' => [
        'request' => [
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ],
            'enableCookieValidation' => false,
        ],
        'user' => [
            'class'=> BasUser::class,
            'identityClass' => UserToken::class,
            'enableAutoLogin' => true,
            'enableSession' => false,
        ],
        'errorHandler' => [
            'class'=> ApiErrorHandler::class,
            'errorAction' => 'default/error',
        ],

        'urlManager' => [
            'enablePrettyUrl' => true,
            'enableStrictParsing' => true,
            'showScriptName' => false,
            'rules' => $urlRules,
        ]
    ],
    'params' => $params,
];

$dbConf = [
    'class' => 'common\components\core\db\Connection',
    'dsn' => "pgsql:host={$dbParams['dbHost']};port={$dbParams['dbPort']};options=--application_name={$applicationName};dbname={$dbParams['profitDashDbName']}",
    'emulatePrepare' => true,
    'username' => $dbParams['dbUser'],
    'password' => $dbParams['dbPassword'],
    'prefix' => $dbParams['profitDashDbName'],
    'charset' => 'utf8',
    'enableSchemaCache' => $enableSchemaCache,
    'schemaCacheDuration' => $schemaCacheDuration,
    'schemaCache' => $schemaCache,
    'enableQueryCache' => $enableQueryCache,
    'queryCache' => $queryCache,
    'queryCacheDuration' => $queryCacheDuration,
    'attributes' => [],
    'on afterOpen' => function($event) {
        $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
    },
    'slaveConfig' => [
        'class' => 'common\components\core\db\Connection',
        'username' => $dbParams['dbUserSlave'],
        'password' => $dbParams['dbPasswordSlave'],
        'prefix' => $dbParams['profitDashDbName'],
        'charset' => 'utf8',
        'enableSchemaCache' => $enableSchemaCache,
        'schemaCacheDuration' => $schemaCacheDuration,
        'schemaCache' => $schemaCache,
        'enableQueryCache' => $enableQueryCache,
        'queryCache' => $queryCache,
        'emulatePrepare' => true,
        'queryCacheDuration' => $queryCacheDuration,
        'attributes' => [],
        'on afterOpen' => function($event) {
            $event->sender->createCommand('SET search_path TO public, "$user";')->execute();
        },
    ],
    'slaves' => [
        ['dsn' => "pgsql:host={$dbParams['dbSlaveHost']};port={$dbParams['dbSlavePort']};options=--application_name={$applicationName};dbname={$dbParams['profitDashDbName']}"],
    ],
];

$db1Conf = [
    'class' => 'common\components\core\db\Connection',
    'dsn' => "pgsql:host={$db1Params['dbHost']};port={$db1Params['dbPort']};options=--application_name={$applicationName};dbname={$db1Params['profitDashDbName']}",
    'emulatePrepare' => true,
    'username' => $db1Params['dbUser'],
    'password' => $db1Params['dbPassword'],
    'prefix' => $db1Params['profitDashDbName'] . '1',
    'charset' => 'utf8',
    'enableSchemaCache' => $enableSchemaCache,
    'schemaCacheDuration' => $schemaCacheDuration,
    'schemaCache' => $schemaCache,
    'enableQueryCache' => $enableQueryCache,
    'queryCache' => $queryCache,
    'queryCacheDuration' => $queryCacheDuration,
    'attributes' => [],
    'slaveConfig' => [
        'class' => 'common\components\core\db\Connection',
        'username' => $db1Params['dbUserSlave'],
        'password' => $db1Params['dbPasswordSlave'],
        'prefix' => $db1Params['profitDashDbName'] . '1',
        'charset' => 'utf8',
        'enableSchemaCache' => $enableSchemaCache,
        'schemaCacheDuration' => $schemaCacheDuration,
        'schemaCache' => $schemaCache,
        'enableQueryCache' => $enableQueryCache,
        'queryCache' => $queryCache,
        'emulatePrepare' => true,
        'queryCacheDuration' => $queryCacheDuration,
        'attributes' => [],
    ],
    'slaves' => [
        ['dsn' => "pgsql:host={$db1Params['dbSlaveHost']};port={$db1Params['dbSlavePort']};options=--application_name={$applicationName};dbname={$db1Params['profitDashDbName']}"],
    ],
];

if ($requestMethod === 'GET') {
    $config['components']['db'] = $dbConf;
    $config['components']['db1'] = $db1Conf;
}

if (YII_ENV === 'local') {
    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        'allowedIPs' => ['*'],
        'generators' => [
            'model' => [
                'class' => 'common\components\generators\model\Generator',
                'templates' => [
                    'oaModel' => '@common/components/generators/model/default',
                ],
            ],
            'rest' => [
                'class' => 'common\components\generators\rest\Generator',
                'templates' => [
                    'oaModel' => '@common/components/generators/rest/default',
                ],
            ],
        ],
    ];
    $config['modules'][] = 'yii\debug\Module';
}

return $config;
