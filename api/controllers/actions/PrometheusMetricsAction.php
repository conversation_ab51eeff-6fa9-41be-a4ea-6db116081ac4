<?php

namespace api\controllers\actions;

use Prometheus\RenderTextFormat;
use yii\base\Action;
use yii\web\Response;

class PrometheusMetricsAction extends Action
{
    /**
     * {@inheritdoc}
     */
    public function run()
    {
        $prometheus = \Yii::$app->prometheus;
        $response = new Response();
        $response->content = (new RenderTextFormat())
            ->render($prometheus->getRegistry()->getMetricFamilySamples());
        $response->headers->set('Content-type', RenderTextFormat::MIME_TYPE);

        return $response;
    }
}
