<?php
declare(strict_types = 1);

namespace api\controllers;

use yii\helpers\Url;
use yii\web\Controller;

class DefaultController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
        ];

        if (YII_ENV !== 'prod') {
            $actions = array_merge($actions, [
                'swagger' => [
                    'class' => 'genxoft\swagger\ViewAction',
                    'apiJsonUrl' => Url::to(['/default/api-json']),
                ],
                'api-json' => [
                    'class' => 'genxoft\swagger\JsonAction',
                    'dirs' => [
                        \Yii::getAlias('@api/modules/v1'),
                        \Yii::getAlias('@common/models'),
                    ],
                ]
            ]);
        }

        return $actions;
    }
}
