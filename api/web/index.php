<?php

require(__DIR__ . '/../../vendor/autoload.php');

$dotenv = new \Symfony\Component\Dotenv\Dotenv(true);
$dotenv->load(__DIR__ . '/../../.env');

defined('YII_ENV') or define('YII_ENV', getenv('YII_ENV') ?: 'prod');
defined('YII_DEBUG') or define('YII_DEBUG', (bool)getenv('YII_DEBUG'));

require(__DIR__ . '/../../vendor/yiisoft/yii2/Yii.php');
require(__DIR__ . '/../../common/config/bootstrap.php');
require(__DIR__ . '/../config/bootstrap.php');
require(__DIR__ . '/../../common/functions.php');

$config = yii\helpers\ArrayHelper::merge(
    require(__DIR__ . '/../../common/config/main.php'),
    require(__DIR__ . '/../../common/config/main-local.php'),
    require(__DIR__ . '/../config/main.php'),
    require(__DIR__ . '/../config/main-local.php')
);

$application = new yii\web\Application($config);
$application->run();
