<?php

namespace api\components;

use common\models\UserToken;
use yii\web\User;

class Bas<PERSON>ser extends User
{
    private array $_access = [];

    public function can($permissionName, $params = [], $allowCaching = true): bool
    {
        /** @var UserToken $identity */
        $identity = $this->getIdentity();

        if (is_null($identity)) {
            return false;
        }

        if ($allowCaching && empty($params) && isset($this->_access[$permissionName])) {
            return $this->_access[$permissionName];
        }

        $permissions = $identity->getAuthUser() ? $identity->getAuthUser()->getPermissions() : [];

        $access = array_key_exists($permissionName, $permissions);

        if ($allowCaching && empty($params)) {
            $this->_access[$permissionName] = $access;
        }

        return $access;
    }
}
