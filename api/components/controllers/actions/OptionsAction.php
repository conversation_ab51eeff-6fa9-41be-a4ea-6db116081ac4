<?php
declare(strict_types = 1);

namespace api\components\controllers\actions;

use \yii\base\Action;
use api\components\CorsTrait;
use Yii;

class OptionsAction extends Action
{
    use CorsTrait;

    /**
     * Responds to the OPTIONS request.
     */
    public function run()
    {
        if (Yii::$app->getRequest()->getMethod() !== 'OPTIONS') {
            Yii::$app->getResponse()->setStatusCode(405);
        }

        $this->setHeaders();
    }
}
