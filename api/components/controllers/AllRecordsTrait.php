<?php

namespace api\components\controllers;

use common\models\MainActiveRecord;
use yii\data\Sort;

/**
 * @mixin Controller
 */
trait AllRecordsTrait
{
    public function prepareDataProvider()
    {
        if (empty(\Yii::$app->request->queryParams['all'])) {
            return parent::prepareDataProvider();
        }

        $params = \Yii::$app->request->queryParams;

        /** @var MainActiveRecord $model */
        $model = new $this->modelClass();

        if (method_exists($model, 'search')) {
            $query = $model->search($params);
        } else {
            $query = $model::find();
        }

        $sort = new Sort([
            'defaultOrder' => $model->getDefaultSort(),
            'params' => $params,
            'attributes' => $model->getSortAttributes(),
        ]);
        $query->orderBy($sort->orders);
        return $query->all();
    }
}
