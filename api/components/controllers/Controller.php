<?php
declare(strict_types = 1);

namespace api\components\controllers;

use api\components\controllers\actions\OptionsAction;
use api\components\CorsTrait;
use common\components\core\db\dbManager\DbManager;
use common\components\core\Exception\ActionRestrictedForNonActiveCustomer;
use common\components\core\Exception\ModuleIsNotEnabledException;
use common\components\CustomerComponent;
use common\components\exception\SellerNotFoundException;
use common\models\Command;
use common\models\Seller;
use common\models\UserToken;
use yii\data\ActiveDataProvider;
use yii\db\ActiveRecord;
use yii\filters\auth\HttpBearerAuth;
use yii\rest\ActiveController;
use yii\web\BadRequestHttpException;

/**
 * Class Controller.
 * @package common\api\components\controllers
 */
class Controller extends ActiveController
{
    use CorsTrait;

    /**
     * List of password time expiration independent routes.
     */
    private const ETERNAL_ROUTES = [
    ];

    /**
     * List of public routes.
     */
    private const PUBLIC_ROUTES = [
        'v1/health/index'
    ];

    protected bool $isCustomerRelated = true;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        \Yii::$app->language = 'en';
        $this->setHeaders();
    }

    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        /**
         * Disable authenticator behavior for public actions without oauth client authorization only!
         */
        if (\in_array($action->getUniqueId(), self::PUBLIC_ROUTES, true)) {
            $this->detachBehavior('authenticator');
            return $action;
        }

        try {
            $result = parent::beforeAction($action);
            if (\Yii::$app->getRequest() && \Yii::$app->getRequest()->isOptions) {
                return $result;
            }
        } catch (SellerNotFoundException $e) {
            throw new ModuleIsNotEnabledException(
                403,
                \Yii::t('admin', 'Your account is being activated. Please wait for SELLERLOGIC to synchronize with Amazon.')
            );
        }

        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;

        if ($this->isCustomerRelated && $dbManager->getCustomerId() === null) {
            throw new BadRequestHttpException('Customer Id is not provided');
        }

        $verb = \Yii::$app->getRequest()->getMethod();

        /** @var UserToken $userToken */
        $userToken = \Yii::$app->user->identity;

        if ($verb !== 'GET' && !$userToken->isInternalClient()) {
            /** @var CustomerComponent $customerComponent */
            $customerComponent = \Yii::$app->customerComponent;
            if (!$customerComponent->isActive()) {
                throw new ActionRestrictedForNonActiveCustomer(
                    403,
                    "Your account is inactive. Only viewing of previously saved information is allowed."
                );
            }
        }

        if (!$userToken->isInternalClient()) {
            $this->rebuildViewsIfNeed();
        }

        return $result;
    }

    protected function rebuildViewsIfNeed()
    {
        /** @var DbManager $dbManager */
        $dbManager = \Yii::$app->dbManager;
        $customerId = $dbManager->getCustomerId();;
        $cache = \Yii::$app->fastPersistentCache;
        $cacheKey = 'customer_' . $customerId . '_rebuild_views_command_sent_time';
        $intervalMinutes = $dbManager->isActive() ? 10 : 30;

        $lastTime = $cache->get($cacheKey);
        $isTimeToRebuild = empty($lastTime) || (time() - $lastTime) > $intervalMinutes * 60;

        if (!$isTimeToRebuild) {
            return;
        }

        $cache->set($cacheKey, time(), $intervalMinutes * 60);

        $cmdCommand = sprintf(
            "seller/rebuild-dynamic-tables %d %d",
            $customerId,
            $customerId + 1
        );

        Command::create($cmdCommand, 1, true, true);
    }

    /**
     * @return $this
     */
    protected function getController()
    {
        return $this;
    }

    /**
     * @return array
     */
    public function getVerbs()
    {
        return $this->verbs();
    }

    /**
     * {@inheritdoc}
     */
    protected function verbs()
    {
        return [
            'index' => ['GET'],
            'view' => ['GET'],
            'create' => ['POST'],
            'update' => ['PUT'],
            'delete' => ['DELETE'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        $actions = parent::actions();

        $actions['index']['prepareDataProvider'] = [$this, 'prepareDataProvider'];

        $actions['options'] = [
            'class' => OptionsAction::class,
        ];

        return $actions;
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        unset($behaviors['authenticator']);

        $behaviors['authenticator'] = [
            'class' =>  HttpBearerAuth::class,
            'except' => ['options'],
        ];

        return $behaviors;
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * <AUTHOR>
     */
    public function prepareDataProvider()
    {
        $params = \Yii::$app->request->queryParams;

        /** @var ActiveRecord $model */
        $model = new $this->modelClass();

        if (method_exists($model, 'search')) {
            $query = $model->search($params);
        } else {
            $query = $model::find();
        }

        $dataProvider = \Yii::createObject([
            'class' => ActiveDataProvider::class,
            'query' => $query,
            'pagination' => [
                'params' => $params,
                'pageSizeLimit' => [1, 100],
                'pageSizeParam' => 'pageSize',
                'defaultPageSize' => 50,
            ],
            'sort' => [
                'defaultOrder' => $model->getDefaultSort(),
                'params' => $params,
                'attributes' => $model->getSortAttributes(),
            ],
        ]);

        $data = $dataProvider->getModels();

        return [
            'totalCount' => $dataProvider->getTotalCount(),
            'pageCount' => $dataProvider->pagination->getPageCount(),
            'currentPage' => $dataProvider->pagination->getPage() + 1,
            'pageSize' => $dataProvider->pagination->getPageSize(),
            'data' => $data,
        ];
    }
}
