<?php
declare(strict_types = 1);

namespace api\components;

use Yii;
use yii\helpers\ArrayHelper;
use yii\web\HeaderCollection;

/**
 * Trait CorsTrait.
 * @package common\api\components
 */
trait CorsTrait
{
    protected function setHeaders()
    {
        $options = [];
        $verbs = $this->controller->getVerbs();
        $actions = $this->controller->actions();

        foreach ($actions as $key=>$action) {
            if (isset($verbs[$key])) {
                $options = ArrayHelper::merge($options, array_combine($verbs[$key], $verbs[$key]));
            }
        }

        $headers = Yii::$app->getResponse()->getHeaders();
        $headers->set('Allow', implode(', ', $options));
        $headers->set('Access-Control-Allow-Methods', implode(', ', $options));
        $headers->set('Access-Control-Allow-Headers', "authorization,content-type");
        $headers->set('Access-Control-Max-Age', "86400");
        $headers->set('Vary', "Origin");

        $this->setAllowOrigin($headers);
    }

    /**
     * <AUTHOR>
     * @param HeaderCollection $headers
     */
    private function setAllowOrigin(HeaderCollection &$headers)
    {
        $baseOrigin = Yii::$app->request->hostInfo;
        $possibleOrigins = [Yii::$app->request->hostInfo];

        if (isset(\Yii::$app->params['appHost']) && !empty(\Yii::$app->params['appHost'])) {
            $possibleOrigins[] = \Yii::$app->params['appHost'];
            $baseOrigin = \Yii::$app->params['appHost'];
        }
        if (!empty(\Yii::$app->params['crmHosts']) && is_array(\Yii::$app->params['crmHosts'])) {
            $possibleOrigins = array_merge($possibleOrigins, \Yii::$app->params['crmHosts']);
        }

        if (!empty(\Yii::$app->params['localHosts']) && is_array(\Yii::$app->params['localHosts'])) {
            $possibleOrigins = array_merge($possibleOrigins, \Yii::$app->params['localHosts']);
        }

        if (in_array(Yii::$app->request->getOrigin(), $possibleOrigins, true)) {
            $origin = Yii::$app->request->getOrigin();
        } else {
            $origin = $baseOrigin;
        }

        $headers->set('Access-Control-Allow-Origin', $origin);
    }
}
