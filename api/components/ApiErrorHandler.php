<?php
declare(strict_types=1);

namespace api\components;

use b<PERSON><PERSON><PERSON>\yii\rollbar\web\ErrorHandler as RollbarWebErrorHandler;
use common\components\core\Exception\InternalCodeAwareInterface;
use Yii;
use yii\base\ErrorException;
use yii\base\Exception;
use yii\base\UserException;
use yii\web\ForbiddenHttpException;
use yii\web\HttpException;
use yii\web\MethodNotAllowedHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * Error handler for API that:
 *  - formats response as JSON
 *  - logs to <PERSON><PERSON> via bai<PERSON><PERSON>/yii2-rollbar
 *  - suppresses some HTTP exceptions from Rollbar
 */
class ApiErrorHandler extends RollbarWebErrorHandler
{
    /**
     * @param \Throwable $exception
     */
    #[\Override]
    protected function renderException($exception)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        parent::renderException($exception);
    }

    /**
     * @param \Throwable $exception
     * @return array
     */
    #[\Override]
    protected function convertExceptionToArray($exception)
    {
        if (!YII_DEBUG && !$exception instanceof UserException && !$exception instanceof HttpException) {
            $exception = new HttpException(500, Yii::t('admin', 'An internal server error occurred.'));
        }

        $internalCode = $exception instanceof InternalCodeAwareInterface
            ? $exception->getInternalCode()
            : 'UNDEFINED';

        $array = [
            'name'          => ($exception instanceof Exception || $exception instanceof ErrorException)
                ? $exception->getName()
                : 'Exception',
            'message'       => $exception->getMessage(),
            'code'          => $exception->getCode(),
            'internal_code' => $internalCode,
        ];

        if ($exception instanceof HttpException) {
            $array['status'] = $exception->statusCode;
        }

        if (YII_DEBUG) {
            $array['type'] = $exception::class;
            if (!$exception instanceof UserException) {
                $array['file']        = $exception->getFile();
                $array['line']        = $exception->getLine();
                $array['stack-trace'] = explode("\n", $exception->getTraceAsString());
                if ($exception instanceof \yii\db\Exception) {
                    $array['error-info'] = $exception->errorInfo;
                }
            }

            if (($prev = $exception->getPrevious()) !== null) {
                $array['previous'] = $this->convertExceptionToArray($prev);
            }
        }

        return $array;
    }

    /**
     * @param \Throwable $exception
     */
    #[\Override]
    public function logException($exception): void
    {
        if (
            $exception instanceof NotFoundHttpException
            || $exception instanceof ForbiddenHttpException
            || $exception instanceof MethodNotAllowedHttpException
        ) {
            return;
        }

        parent::logException($exception);
    }
}
