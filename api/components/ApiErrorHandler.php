<?php
declare(strict_types = 1);

namespace api\components;

use common\components\core\Exception\InternalCodeAwareInterface;
use fl0v\yii2\rollbar\ErrorHandlerTrait;
use Yii;
use yii\base\ErrorException;
use yii\base\Exception;
use yii\base\UserException;
use yii\web\ErrorHandler;
use yii\web\ForbiddenHttpException;
use yii\web\HttpException;
use yii\web\MethodNotAllowedHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class ApiErrorHandler extends ErrorHandler
{
    use ErrorHandlerTrait {
        logException as protected logExceptionRollbar;
    }

    /**
     * @param \Error|\Exception $exception
     */
    protected function renderException($exception)
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        parent::renderException($exception);
    }

    /**
     * @param  \Error|\Exception $exception
     * @return array
     */
    protected function convertExceptionToArray($exception)
    {
        if (!YII_DEBUG && !$exception instanceof UserException && !$exception instanceof HttpException) {
            $exception = new HttpException(500, Yii::t('admin', 'An internal server error occurred.'));
        }

        $internalCode = $exception instanceof InternalCodeAwareInterface
            ? $exception->getInternalCode()
            : 'UNDEFINED';

        $array = [
            'name' => ($exception instanceof Exception || $exception instanceof ErrorException) ? $exception->getName() : 'Exception',
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'internal_code' => $internalCode
        ];
        if ($exception instanceof HttpException) {
            $array['status'] = $exception->statusCode;
        }
        if (YII_DEBUG) {
            $array['type'] = get_class($exception);
            if (!$exception instanceof UserException) {
                $array['file'] = $exception->getFile();
                $array['line'] = $exception->getLine();
                $array['stack-trace'] = explode("\n", $exception->getTraceAsString());
                if ($exception instanceof \yii\db\Exception) {
                    $array['error-info'] = $exception->errorInfo;
                }
            }

            if (($prev = $exception->getPrevious()) !== null) {
                $array['previous'] = $this->convertExceptionToArray($prev);
            }
        }

        return $array;
    }

    public function logException($exception)
    {
        //temp solution in order to make less notifications in rollbar
        if ($exception instanceof NotFoundHttpException) {
            return;
        }
        //temp solution in order to make less notifications in rollbar
        if ($exception instanceof ForbiddenHttpException) {
            return;
        }
        //temp solution in order to make less notifications in rollbar
        if ($exception instanceof MethodNotAllowedHttpException) {
            return;
        }

        $this->logExceptionRollbar($exception);
    }
}
