version: '3.7'
services:
    profit-dash-php-fpm-base: &php-fpm-base
        image: {REGISTRY}/develop/profit-dash/sellerlogic_profit-dash-php-fpm:{IMGTAG}
        restart: always
        volumes:
            - profit-dash-console-runtime:/app/console/runtime
            - profit-dash-api-runtime:/app/api/runtime
        networks:
            - profit-dash-net
        logging:
            options:
                max-size: "50m"
                max-file: "3"

    profit-dash-php-fpm:
        <<: *php-fpm-base
        environment:
            - PROCESS_NAME=php-fpm

    profit-dash-cron:
        <<: *php-fpm-base
        container_name: profit-dash-cron
        command: >
            bash -c "crontab /app/console/cron/crontab
            && cron && tail -f /dev/null"
        privileged: true

    profit-dash-nginx:
        container_name: profit-dash-nginx
        image: {REGISTRY}/develop/profit-dash/sellerlogic_profit-dash-nginx
        restart: always
        depends_on:
            - profit-dash-php-fpm
        ports:
            - 80:80
            - 443:443
            - 9090:9090
        networks:
            - profit-dash-net
    
    newrelic-php-daemon:
        container_name: newrelic-php-daemon
        image: harbor.sl.local/proxy-cache/newrelic/php-daemon:latest
        restart: always
        networks:
            - profit-dash-net
    
    newrelic-infra:
        container_name: newrelic-infra
        image: harbor.sl.local/proxy-cache/newrelic/infrastructure:latest
        restart: always
        environment:
            - NRIA_LICENSE_KEY={NEWRELIC_LICENSE_KEY}
        network_mode: host
        cap_add:
            - SYS_PTRACE
        privileged: true
        pid: host
        volumes:
            - "/:/host:ro"
            - "/var/run/docker.sock:/var/run/docker.sock"

    ### Consumers END ###
volumes:
    profit-dash-console-runtime:
    profit-dash-api-runtime:

networks:
    profit-dash-net:
        driver: bridge
        driver_opts:
            com.docker.network.driver.mtu: 1450
