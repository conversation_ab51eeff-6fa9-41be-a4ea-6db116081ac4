<?php

use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\SetList;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/api',
        __DIR__ . '/common',
        __DIR__ . '/console',
    ])

    ->withSkip([
        __DIR__ . '/vendor/*',
        __DIR__ . '/runtime/*',
    ])

    ->withPhpSets(php83: true)
    ->withPreparedSets(
        deadCode: true,
        codeQuality: true,
        codingStyle: true,
        naming: true,
        privatization: true,
        typeDeclarations: true,
        rectorPreset: true,
    );

    // ->withRules([
    //     \Muse\Rector\CompleteDynamicPropertiesForYii2ActiveRecordRector::class,
    //     \Muse\Rector\CompleteMethodTypingForYii2QueryLinkedWithARRector::class,
    // ]);
