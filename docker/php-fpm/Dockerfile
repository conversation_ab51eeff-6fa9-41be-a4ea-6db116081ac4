FROM harbor.sl.local/develop/base_images/bas-php8-fpm AS composer

ARG GITLAB_COMPOSER_USERNAME
ARG GITLAB_COMPOSER_TOKEN
ARG GITHUB_TOKEN

COPY ./docker/php-fpm/root/.composer/auth.json /root/.composer/auth.json
COPY ./docker/php-fpm/usr/local/etc/php/conf.d/base.ini /usr/local/etc/php/conf.d/base.ini

RUN sed -i "s/{GITLAB_COMPOSER_USERNAME}/$GITLAB_COMPOSER_USERNAME/g" /root/.composer/auth.json \
    && sed -i "s/{GITLAB_COMPOSER_TOKEN}/$GITLAB_COMPOSER_TOKEN/g" /root/.composer/auth.json \
    && sed -i "s/{GITHUB_TOKEN}/$GITHUB_TOKEN/g" /root/.composer/auth.json

COPY ./composer.json ./composer.lock /app/

WORKDIR /app

RUN composer install --no-dev --no-scripts --no-autoloader --prefer-dist
RUN find vendor -type d -name ".git" -exec rm -rf {} +

FROM harbor.sl.local/develop/base_images/bas-php8-fpm AS dev

ARG XDEBUG_VERSION=3.3.2
ENV PHP_IDE_CONFIG="serverName=docker"

RUN \
    apt-get update && \
    pecl channel-update pecl.php.net && \
    pecl install xdebug-${XDEBUG_VERSION} && \
    docker-php-ext-enable xdebug && \
    pecl clear-cache && \
    apt-get purge -y && \
    apt-get autoremove -y

COPY ./docker/php-fpm/php.conf/30-php.dev.base.ini $PHP_INI_DIR/conf.d/30-php.dev.base.ini
COPY ./docker/php-fpm/usr/local/etc/php/conf.d/base.ini $PHP_INI_DIR/conf.d/base.ini

COPY --from=composer /app /app
COPY ./ /app

ARG DEPLOY_ENV
ARG AUTH_TOKEN_URL

RUN composer dump-autoload --optimize \
    && php init --env=$DEPLOY_ENV --overwrite=All \
    && sed -i "s|{AUTH_TOKEN_URL}|$AUTH_TOKEN_URL|g" /app/api/modules/v1/moduleConfig.php

FROM harbor.sl.local/develop/base_images/bas-php8-fpm AS base

ARG NEW_RELIC_AGENT_VERSION
ARG NEW_RELIC_LICENSE_KEY
ARG NEW_RELIC_APPNAME

COPY ./docker/php-fpm/usr/local/etc/php/conf.d/base.ini $PHP_INI_DIR/conf.d/base.ini

COPY --from=composer /app /app
COPY ./ /app

ARG DEPLOY_ENV
ARG AUTH_TOKEN_URL

RUN composer dump-autoload --optimize \
    && php init --env=$DEPLOY_ENV --overwrite=All \
    && sed -i "s|{AUTH_TOKEN_URL}|$AUTH_TOKEN_URL|g" /app/api/modules/v1/moduleConfig.php

RUN curl -L https://download.newrelic.com/php_agent/archive/${NEW_RELIC_AGENT_VERSION}/newrelic-php5-${NEW_RELIC_AGENT_VERSION}-linux.tar.gz | tar -C /tmp -zx \
    && export NR_INSTALL_USE_CP_NOT_LN=1 \
    && export NR_INSTALL_SILENT=1 \
    && /tmp/newrelic-php5-${NEW_RELIC_AGENT_VERSION}-linux/newrelic-install install \
    && rm -rf /tmp/newrelic-php5-* /tmp/nrinstall*

RUN sed -i \
    -e "s/newrelic.license[[:space:]]*=[[:space:]]*.*/newrelic.license = ${NEW_RELIC_LICENSE_KEY}/" \
    -e "s/newrelic.appname[[:space:]]*=[[:space:]]*.*/newrelic.appname = ${NEW_RELIC_APPNAME}/" \
    -e "\$a newrelic.daemon.address=newrelic-php-daemon:31339" \
    /usr/local/etc/php/conf.d/newrelic.ini
