FROM harbor.sl.local/proxy-cache/nginx:1.15-alpine

ARG PROMETHEUS_BASIC_AUTH_USER
ARG PROMETHEUS_BASIC_AUTH_PASSWORD

RUN apk add --no-cache --virtual .build-deps \
    apache2-utils
RUN htpasswd -b -c /etc/nginx/.prometheus_htpasswd ${PROMETHEUS_BASIC_AUTH_USER} ${PROMETHEUS_BASIC_AUTH_PASSWORD}
COPY ./docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY ./docker/nginx/nginx.conf /etc/nginx/nginx.conf

COPY ./ /app

WORKDIR /app