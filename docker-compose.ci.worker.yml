# Docker compose file for prod main worker
version: '3.7'
services:
    profit-dash-php-fpm-base-worker: &php-fpm-worker-base
        image: {REGISTRY}/develop/profit-dash/sellerlogic_profit-dash-php-fpm:{IMGTAG}
        restart: always
        volumes:
            - profit-dash-console-runtime-worker:/app/console/runtime
            - profit-dash-api-runtime-worker:/app/api/runtime
        networks:
            - profit-dash-net-worker
        logging:
            options:
                max-size: "50m"
                max-file: "3"
    ### Consumers START ###
    consumer-financial-event-init_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.INIT.CONSUMER.0
        deploy:
            replicas: 1

    consumer-financial-event-init_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.INIT.CONSUMER.1
        deploy:
            replicas: 1

    consumer-financial-event-refresh_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.REFRESH.CONSUMER.0
        deploy:
            replicas: 5

    consumer-financial-event-refresh_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume FINANCIAL.EVENT.REFRESH.CONSUMER.1
        deploy:
            replicas: 5

    consumer-handle-ready-amazon-reports-init:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume AMAZON_REPORTS.HANDLE_READY_INIT.CONSUMER
        deploy:
            replicas: 2

    consumer-handle-ready-amazon-reports-refresh:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume AMAZON_REPORTS.HANDLE_READY_REFRESH.CONSUMER
        deploy:
            replicas: 10

    consumer-handle-ready-amazon-sync_statuses:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume AMAZON_REPORTS.SYNC_STATUSES.CONSUMER
        deploy:
            replicas: 15

    consumer-amazon-reports-create:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume AMAZON_REPORTS.CREATE.CONSUMER
        deploy:
            replicas: 10

    consumer-cog-sync:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_SYNC.CONSUMER
        deploy:
            replicas: 3

    consumer-product-sync:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume PRODUCT_SYNC.CONSUMER
        deploy:
            replicas: 1

    consumer-data-import-split-inot-parts:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume DATA_IMPORT.SPLIT_INTO_PARTS.CONSUMER
        deploy:
            replicas: 1

    consumer-data-import-process-part:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume DATA_IMPORT.PROCESS_PART.CONSUMER
        deploy:
            replicas: 5

    consumer-data-import-bulk-edit-process-part:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume DATA_IMPORT.BULK_EDIT.PROCESS_PART.CONSUMER
        deploy:
            replicas: 5

    consumer-data-export-process-part:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume DATA_EXPORT.PROCESS_PART.CONSUMER
        deploy:
            replicas: 1

    consumer-data-export-merge-parts:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume DATA_EXPORT.MERGE_PARTS.CONSUMER
        deploy:
            replicas: 1

    consumer-load-order_items_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.0
        deploy:
            replicas: 2

    consumer-load-order_items_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.1
        deploy:
            replicas: 2

    consumer-load-order_items_2:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.2
        deploy:
            replicas: 2

    consumer-load-order_items_3:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.3
        deploy:
            replicas: 2

    consumer-load-order_items_4:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.4
        deploy:
            replicas: 2

    consumer-load-order_items_5:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.5
        deploy:
            replicas: 2

    consumer-load-order_items_6:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.6
        deploy:
            replicas: 2

    consumer-load-order_items_7:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.7
        deploy:
            replicas: 2

    consumer-load-order_items_8:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.8
        deploy:
            replicas: 2

    consumer-load-order_items_9:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.9
        deploy:
            replicas: 1

    consumer-load-order_items_10:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.10
        deploy:
            replicas: 2

    consumer-load-order_items_11:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.11
        deploy:
            replicas: 2

    consumer-load-order_items_12:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.12
        deploy:
            replicas: 2

    consumer-load-order_items_13:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.13
        deploy:
            replicas: 2

    consumer-load-order_items_14:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.14
        deploy:
            replicas: 2

    consumer-load-order_items_15:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.15
        deploy:
            replicas: 2

    consumer-load-order_items_16:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.16
        deploy:
            replicas: 2

    consumer-load-order_items_17:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.17
        deploy:
            replicas: 2

    consumer-load-order_items_18:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.18
        deploy:
            replicas: 2

    consumer-load-order_items_19:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.ITEMS.CONSUMER.19
        deploy:
            replicas: 2

    consumer-load-orders-init_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDERS.INIT.CONSUMER.0
        deploy:
            replicas: 1

    consumer-load-orders-init_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDERS.INIT.CONSUMER.1
        deploy:
            replicas: 1

    consumer-load-orders-refresh_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDERS.REFRESH.CONSUMER.0
        deploy:
            replicas: 3

    consumer-load-orders-refresh_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDERS.REFRESH.CONSUMER.1
        deploy:
            replicas: 3

    consumer-load-order_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.0
        deploy:
            replicas: 4

    consumer-load-order_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.1
        deploy:
            replicas: 4

    consumer-load-order_2:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.2
        deploy:
            replicas: 4

    consumer-load-order_3:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.3
        deploy:
            replicas: 4

    consumer-load-order_4:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume LOAD.ORDER.CONSUMER.4
        deploy:
            replicas: 4

    consumer-ppc-costs-apply:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume PPC_COSTS.APPLY.CONSUMER
        deploy:
            replicas: 1

    consumer-cron:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume CRON.CONSUMER
        deploy:
            replicas: 5

    consumer-customer-process:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume CUSTOMER_PROCESS.CONSUMER
        deploy:
            replicas: 1

    consumer-event-periods-from-cache-to-clickhouse:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume EVENT_PERIODS.FROM_CACHE_TO_CLICKHOUSE.CONSUMER
        deploy:
            replicas: 15

    consumer-cog-chagnes_0:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.0
        deploy:
            replicas: 3

    consumer-cog-chagnes_1:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.1
        deploy:
            replicas: 3

    consumer-cog-chagnes_2:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.2
        deploy:
            replicas: 3

    consumer-cog-chagnes_3:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.3
        deploy:
            replicas: 3

    consumer-cog-chagnes_4:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES.CONSUMER.4
        deploy:
            replicas: 3

    consumer-cog-chagnes_bulk:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHANGES_BULK.CONSUMER
        deploy:
            replicas: 1

    consumer-cog-check-refunds:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume COG_CHECK_REFUNDS.CONSUMER
        deploy:
            replicas: 1

    consumer-indirect-cost-changes:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume INDIRECT_COST_CHANGES.CONSUMER
        deploy:
            replicas: 1

    consumer-order-items-save-to-clickhouse:
        <<: *php-fpm-worker-base
        command: php /app/yii rabbitmq/consume ORDER_ITEM.SAVE_TO_CLICKHOUSE.CONSUMER
        deploy:
            replicas: 60
    ### Consumers END ###

volumes:
    profit-dash-console-runtime-worker:
    profit-dash-api-runtime-worker:

networks:
    profit-dash-net-worker:
        driver: bridge
        driver_opts:
            com.docker.network.driver.mtu: 1500
