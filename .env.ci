# composer
GIT<PERSON><PERSON>_COMPOSER_USERNAME=gitlab-ci-token
GITLAB_COMPOSER_TOKEN={GITLAB_COMPOSER_TOKEN}
GITHUB_TOKEN={GITHUB_TOKEN}

# auth
MAIN_API_URL={MAIN_API_URL}
AUTH_TOKEN_URL={AUTH_TOKEN_URL}

# DB
APP_DB_HOST={APP_DB_HOST}
APP_DB_SLAVE_HOST={APP_DB_SLAVE_HOST}
APP_DB_PORT={APP_DB_PORT}
APP_DB_SLAVE_PORT={APP_DB_SLAVE_PORT}
APP_DB_USERNAME={APP_DB_USERNAME}
APP_DB_PASSWORD={APP_DB_PASSWORD}
APP_DB_USERNAME_SLAVE={APP_DB_USERNAME_SLAVE}
APP_DB_PASSWORD_SLAVE={APP_DB_PASSWORD_SLAVE}
# DB 1
APP_DB_1_HOST={APP_DB_1_HOST}
APP_DB_1_SLAVE_HOST={APP_DB_1_SLAVE_HOST}
APP_DB_1_PORT={APP_DB_1_PORT}
APP_DB_1_SLAVE_PORT={APP_DB_1_SLAVE_PORT}
APP_DB_1_USERNAME={APP_DB_1_USERNAME}
APP_DB_1_PASSWORD={APP_DB_1_PASSWORD}
APP_DB_1_USERNAME_SLAVE={APP_DB_1_USERNAME_SLAVE}
APP_DB_1_PASSWORD_SLAVE={APP_DB_1_PASSWORD_SLAVE}

# Repricer Event DB
REPRICER_EVENT_DB_HOST={REPRICER_EVENT_DB_HOST}
REPRICER_EVENT_DB_PORT={REPRICER_EVENT_DB_PORT}
REPRICER_EVENT_DB_USERNAME={REPRICER_EVENT_DB_USERNAME}
REPRICER_EVENT_DB_PASSWORD={REPRICER_EVENT_DB_PASSWORD}
REPRICER_EVENT_DB_NAME={REPRICER_EVENT_DB_NAME}

# Repricer DB
REPRICER_DB_HOST={REPRICER_DB_HOST}
REPRICER_DB_HOST_1={REPRICER_DB_HOST_1}
REPRICER_DB_USERNAME={REPRICER_DB_USERNAME}
REPRICER_DB_PASSWORD={REPRICER_DB_PASSWORD}

# Customer service DB
CUSTOMER_SERVICE_DB_HOST={CUSTOMER_SERVICE_DB_HOST}
CUSTOMER_SERVICE_DB_PORT={CUSTOMER_SERVICE_DB_PORT}
CUSTOMER_SERVICE_DB_USERNAME={CUSTOMER_SERVICE_DB_USERNAME}
CUSTOMER_SERVICE_DB_PASSWORD={CUSTOMER_SERVICE_DB_PASSWORD}
CUSTOMER_SERVICE_DB_NAME={CUSTOMER_SERVICE_DB_NAME}

# ClickHouse
CLICKHOUSE_HOST={CLICKHOUSE_HOST}
CLICKHOUSE_PORT=8123
CLICKHOUSE_DB_NAME={CLICKHOUSE_DB_NAME}
CLICKHOUSE_USERNAME={CLICKHOUSE_USERNAME}
CLICKHOUSE_PASSWORD={CLICKHOUSE_PASSWORD}
CLICKHOUSE_CLUSTER_NAME={CLICKHOUSE_CLUSTER_NAME}
CLICKHOUSE_MASTER_NODES={CLICKHOUSE_MASTER_NODES}
CLICKHOUSE_MASTER_NODES_MAP={CLICKHOUSE_MASTER_NODES_MAP}

# redis
APP_REDIS_HOST={APP_REDIS_HOST}
APP_REDIS_PORT={APP_REDIS_PORT}
APP_REDIS_STANDARD_CACHE_DB={APP_REDIS_STANDARD_CACHE_DB}
APP_REDIS_SCHEMA_CACHE_DB={APP_REDIS_SCHEMA_CACHE_DB}
APP_REDIS_PROMETHEUS_DB={APP_REDIS_PROMETHEUS_DB}
APP_REDIS_MUTEX_CACHE_DB={APP_REDIS_MUTEX_CACHE_DB}
APP_REDIS_PASSWORD={APP_REDIS_PASSWORD}
APP_REDIS_FAST_PERSISTENT_CACHE_DB={APP_REDIS_FAST_PERSISTENT_CACHE_DB}

# rabbitmq
# 0|1
RABBITMQ_SSL={RABBITMQ_SSL}
RABBITMQ_HOST={RABBITMQ_HOST}
RABBITMQ_PORT=5672
RABBITMQ_VHOST={RABBITMQ_VHOST}
RABBITMQ_DEFAULT_USER={RABBITMQ_DEFAULT_USER}
RABBITMQ_DEFAULT_PASS={RABBITMQ_DEFAULT_PASS}

# Development|Staging|Production|Local
DEPLOY_ENV={DEPLOY_ENV}
# local|dev|rc|prod
YII_ENV={YII_ENV}
# 0|1
YII_DEBUG={YII_DEBUG}

PROFIT_DASH_DB_NAME=profit_dash_db
FINANCE_DB_NAME_PREFIX=finance

ROLLBAR_ACCESS_TOKEN={ROLLBAR_ACCESS_TOKEN}

TOKEN_SERVICE_API_URL={TOKEN_SERVICE_API_URL}

# Selling API credentials
SELLING_API_ACCESS_KEY={SELLING_API_ACCESS_KEY}
SELLING_API_ACCESS_SECRET={SELLING_API_ACCESS_SECRET}
SELLING_API_ROLE_ARN={SELLING_API_ROLE_ARN}

# Selling API credentials
AMAZON_ADS_API_CLIENT_ID={AMAZON_ADS_API_CLIENT_ID}
AMAZON_ADS_API_CLIENT_SECRET={AMAZON_ADS_API_CLIENT_SECRET}

# Internal API
INTERNAL_API_CLIENT_ID={INTERNAL_API_CLIENT_ID}
INTERNAL_API_CLIENT_SECRET={INTERNAL_API_CLIENT_SECRET}
INTERNAL_API_GRANT_TYPE=client_credentials

# BAS API client
BAS_API_URL={BAS_API_URL}
BAS_AUTH_TOKEN_URL={BAS_AUTH_TOKEN_URL}
BAS_INTERNAL_API_CLIENT_ID={BAS_INTERNAL_API_CLIENT_ID}
BAS_INTERNAL_API_CLIENT_SECRET={BAS_INTERNAL_API_CLIENT_SECRET}
BAS_INTERNAL_API_GRANT_TYPE={BAS_INTERNAL_API_GRANT_TYPE}

APP_HOST={APP_HOST}

PROMETHEUS_BASIC_AUTH_USER={PROMETHEUS_BASIC_AUTH_USER}
PROMETHEUS_BASIC_AUTH_PASSWORD={PROMETHEUS_BASIC_AUTH_PASSWORD}
PROMETHEUS_REDIS_HOST={PROMETHEUS_REDIS_HOST}
PROMETHEUS_REDIS_PORT={PROMETHEUS_REDIS_PORT}
PROMETHEUS_REDIS_PASSWORD={PROMETHEUS_REDIS_PASSWORD}

SLACK_WEB_HOOK_METRICS_MONITOR={SLACK_WEB_HOOK_METRICS_MONITOR}
SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR={SLACK_WEB_HOOK_DATA_INCONSISTENCY_MONITOR}

# AWS
AWS_ACCESS_KEY={AWS_ACCESS_KEY}
AWS_SECRET_KEY={AWS_SECRET_KEY}
AWS_REGION={AWS_REGION}
AWS_VERSION={AWS_VERSION}
AWS_BUCKET={AWS_BUCKET}
AWS_PUBLIC_BUCKET={AWS_PUBLIC_BUCKET}
